<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      :before-close="beforeClose"
      :upload-preview="uploadPreview"
      :cell-style="cellStyle"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template #costApplyIdForm="{ size, index }">
        <cost-apply-select
          v-model="form.costApplyId"
          :size="size"
          :change="rowCostApplyChange"
        />
      </template>
      <template #paymentType="{ row, size, index }">
        <el-tag
          v-if="row.paymentType === '1'"
          :size="size"
          type=""
          effect="plain"
        >
          {{ row.$paymentType }}
        </el-tag>
        <el-tag
          v-else-if="row.paymentType === '2'"
          :size="size"
          type="success"
          effect="plain"
        >
          {{ row.$paymentType }}
        </el-tag>
        <el-tag
          v-else-if="row.paymentType === '3'"
          :size="size"
          type="info"
          effect="plain"
        >
          {{ row.$paymentType }}
        </el-tag>
        <el-tag
          v-else-if="row.paymentType === '4'"
          :size="size"
          type="danger"
          effect="plain"
        >
          {{ row.$paymentType }}
        </el-tag>
        <el-tag
          v-else-if="row.paymentType === '5'"
          :size="size"
          type="warning"
          effect="plain"
        >
          {{ row.$paymentType }}
        </el-tag>
        <el-tag
          v-else-if="row.paymentType === '6'"
          :size="size"
          type=""
          effect="dark"
        >
          {{ row.$paymentType }}
        </el-tag>
        <el-tag
          v-else-if="row.paymentType === '7'"
          :size="size"
          type="danger"
          effect="plain"
        >
          {{ row.$paymentType }}
        </el-tag>
        <el-tag
          v-else-if="row.paymentType === '8'"
          :size="size"
          type="warning"
          effect="plain"
        >
          {{ row.$paymentType }}
        </el-tag>
        <el-tag v-else :size="size">
          {{ row.$paymentType }}
        </el-tag>
      </template>
      <template #financeAccountForm="{ size, disabled }">
        <bank-card-input
          v-model="form.financeAccount"
          :size="size"
          :disabled="disabled"
        />
      </template>
      <template #yonyouSync="{ row, size }">
        <span v-for="(item, index) in row.yonyouSync" :key="index">
          {{ yonyouSyncSequenceDictKeyValue[item.sequence] }}:
          <el-tag :size="size">
            {{ yonyouSyncStateDictKeyValue[item.value] }}
          </el-tag>
          ;
          <template v-if="item.id">
            <span style="font-weight: bolder">id:</span>
            {{ item.id }}
          </template>
          <template v-if="item.errMsg"> :{{ item.errMsg }} </template>
          <br />
        </span>
      </template>
      <template #serialNo="{ row, size, index }">
        <flow-timeline-popover
          v-if="row.processInsId"
          :process-ins-id="row.processInsId"
          :form-key="formKey"
          :process-def-key="processDefKey"
          v-model="row.serialNo"
          :flow.sync="row.flow"
          trigger="click"
          lazy
        />
        <span v-else>{{ row.serialNo }}</span>
      </template>
      <template #status="{ row, index }">
        <el-tag v-if="row.status === 0" size="mini" type="info">
          {{ row.$status }}
        </el-tag>
        <el-tag v-else-if="row.status === 1" size="mini" effect="plain">
          {{ row.$status }}
        </el-tag>
        <el-tag v-else-if="row.status === 2" size="mini" effect="plain">
          {{ row.$status }}
        </el-tag>
        <el-tag v-else-if="row.status === 3" size="mini" type="danger">
          {{ row.$status }}
        </el-tag>
        <el-tag
          v-else-if="row.status === 4"
          size="mini"
          type="warning"
          effect="plain"
        >
          {{ row.$status }}
        </el-tag>
        <el-tag
          v-else-if="row.status === 5"
          size="mini"
          type="warning"
          effect="plain"
        >
          {{ row.$status }}
        </el-tag>
        <el-tag
          v-else-if="row.status === 6"
          size="mini"
          type="danger"
          effect="plain"
        >
          {{ row.$status }}
        </el-tag>
        <el-tag
          v-else-if="row.status === 9"
          size="mini"
          type="success"
          effect="plain"
        >
          {{ row.$status }}
        </el-tag>
        <el-tag v-else size="mini" type="warning" effect="dark">
          {{ row.$status }}
        </el-tag>
      </template>
      <template #budgetIdForm="{ size, disabled }">
        <un-finish-budget-select
          v-model="form.budgetId"
          :size="size"
          :params="{ brand: form.brand }"
          placeholder=" "
          @clear="handleBudgetClear"
        />
      </template>
      <template #eaPersonIdForm="{ row, size, disabled, index }">
        <user-select
          v-model="form.eaPersonId"
          :size="size"
          :disabled="disabled"
          userUrl="/api/blade-user/search/user"
        />
      </template>
      <template #financialStaffIdSearch="{ row, size, disabled, index }">
        <user-select
          v-model="form.financialStaffId"
          :size="size"
          :disabled="disabled"
        />
      </template>
      <template #loanIdSearch="{ row, size, disabled, index }">
        <fin-loan-select
          v-model="form.loanId"
          :size="size"
          :disabled="disabled"
        />
      </template>
      <template #loanIdForm="{ row, size, disabled, index }">
        <fin-loan-select
          v-model="form.loanId"
          :size="size"
          :disabled="disabled"
          @confirm="handleLoanSelect"
        />
      </template>
      <template #loanAmount="{ row, index }">
        <span :style="{ color: 'green', fontWeight: 'bold' }">{{
          row.loanAmount
        }}</span>
      </template>
      <template #payState="{ row, index }">
        <el-tag
          v-if="!row.payState || row.payState === '1'"
          size="mini"
          type="info"
        >
          未完成
        </el-tag>
        <el-tag v-else-if="row.payState === '2'" size="mini"> 已完成</el-tag>
      </template>
      <template #payAmount="{ row, index }">
        <template v-if="row.payType === '1'">
          <el-tag size="mini">收款</el-tag>
          <span style="font-weight: bolder"> {{ row.payAmount }}</span>
        </template>
        <template v-else-if="row.payType === '2'">
          <el-tag size="mini" type="danger">退款</el-tag>
          <span style="font-weight: bolder">{{ row.payAmount }}</span>
        </template>
      </template>
      <template #amount="{ row, index }">
        <span
          v-if="row.amount != row.loanAmount"
          :style="{ color: 'red', fontWeight: 'bold' }"
          >{{
            Number(row.amount).toLocaleString("zh-CN", {
              minimumFractionDigits: 2,
            })
          }}</span
        >
        <span v-else :style="{ color: 'green', fontWeight: 'bold' }">{{
          Number(row.amount).toLocaleString("zh-CN", {
            minimumFractionDigits: 2,
          })
        }}</span>
      </template>
      <template #differenceAmount="{ row, index }">
        <span
          v-if="row.differenceAmount != null"
          :style="{ color: 'black', fontWeight: 'bold' }"
          >{{
            Number(row.differenceAmount).toLocaleString("zh-CN", {
              minimumFractionDigits: 2,
            })
          }}</span
        >
      </template>
      <template #itemsForm="{ index, size }">
        <cost-ea-item
          v-model="form.items"
          :budget-id="form.budgetId"
          :cost-apply-id="form.costApplyId"
          @amountChange="handleSumAmount"
          @numChange="handleSumNum"
        />
      </template>
      <template #menuLeft>
        <!--        <el-button-->
        <!--          type="danger"-->
        <!--          :size="option.size"-->
        <!--          icon="el-icon-delete"-->
        <!--          plain-->
        <!--          v-if="permission.finCostEa_delete"-->
        <!--          @click="handleDelete"-->
        <!--          >删 除-->
        <!--        </el-button>-->
        <el-button
          type="warning"
          size="mini"
          icon="el-icon-position"
          plain
          v-if="permission.finCostEa_apply"
          @click="handleApply"
          >报销申请
        </el-button>
        <el-button
          type="primary"
          size="mini"
          icon="el-icon-refresh"
          plain
          disabled
          v-if="permission.finCostEa_yonyou_sync"
          @click="handleYonyouSync"
          >用友同步
        </el-button>
        <el-button
          type="primary"
          size="mini"
          icon="el-icon-download"
          plain
          v-if="permission.finCostEa_export"
          @click="handleExport"
          >导 出
        </el-button>
        <el-button
          type="success"
          size="mini"
          icon="el-icon-printer"
          plain
          @click="handlePrint"
          >打 印
        </el-button>

        <el-divider direction="vertical" />
        <el-radio-group
          v-model="dataType"
          size="mini"
          @input="handleDataTypeChange"
        >
          <el-radio-button label="all">全部</el-radio-button>
          <el-radio-button label="todo">待办</el-radio-button>
        </el-radio-group>
      </template>
      <template #menuRight="{ size }">
        <el-button
          icon="el-icon-time"
          circle
          :size="size"
          @click="handleLog"
        ></el-button>
      </template>
      <template #menu="{ row, size, index }">
        <el-button
          type="text"
          icon="el-icon-s-promotion"
          :size="size"
          v-if="row.status === 0"
          @click="rowApply(row)"
          >提交
        </el-button>
        <el-tooltip
          class="item"
          effect="dark"
          content="审核通过后，财务可以通过收付款按钮来设置是否收付款，以及收付款账号"
          placement="top-start"
        >
          <el-button
            type="text"
            icon="el-icon-coin"
            :size="size"
            v-if="
              permission.finCostEa_pay &&
              [9].includes(row.status) &&
              (!row.payState || row.payState !== '2')
            "
            @click="rowPay(row)"
            >付款登记
          </el-button>
        </el-tooltip>
        <el-divider direction="vertical" />
        <el-dropdown>
          <el-button type="text" :size="size">
            更多<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
              v-if="row.status === 9 && permission.finCostEa_yonyou_sync"
              @click.native="rowYonyouSync(row)"
            >
              <i class="el-icon-refresh"></i>用友同步
            </el-dropdown-item>
            <el-dropdown-item @click.native="rowAttach(row)">
              <i class="el-icon-download"></i>附件管理
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </template>
    </avue-crud>
    <el-drawer
      :visible.sync="detailVisible"
      title="报销单详情"
      custom-class="wf-drawer"
      size="100%"
      append-to-body
    >
      <cost-ea-detail
        v-if="detailVisible"
        :taskId="form.taskId"
        :formKey="formKey"
        :processInstanceId="form.processInsId"
      />
    </el-drawer>
    <log-opt-dialog ref="logOptDialogRef" :module="module" />
    <attach-dialog ref="attachDialogRef" code="private" />
    <cost-ea-pay-dialog
      ref="payDialogRef"
      @submit="() => onLoad(page, query)"
    />
    <el-dialog
      title="用友同步"
      append-to-body
      :visible.sync="yonyou.visible"
      width="355px"
    >
      <avue-form
        v-if="yonyou.visible"
        ref="yonyouSyncRef"
        :option="yonyou.option"
        v-model="yonyou.form"
        @submit="handleYonyouSyncSubmit"
      ></avue-form>
    </el-dialog>
  </basic-container>
</template>

<script>
import {
  add,
  apply,
  getDetail,
  getList,
  getPrintData,
  remove,
  sync,
  syncRange,
  update,
} from "@/api/ni/fin/cost-ea";
import { mapGetters } from "vuex";
import UserSelect from "@/components/user-select";
import FinLoanSelect from "@/views/ni/fin/components/FinLoanSelect";
import AttachDialog from "@/components/attach-dialog";
import LogOptDialog from "@/components/log-opt-dialog";
import { getDetail as getAttachDetail } from "@/api/resource/attach";
import CostEaPayDialog from "@/views/ni/fin/components/CostEaPayDialog";
import CostEaDetail from "@/views/ni/fin/components/costEaDetail";
import exForm from "@/views/plugin/workflow/mixins/ex-form";
import FinAccountingSelect from "@/views/ni/fin/components/AccountingSelect";
import UnFinishBudgetSelect from "@/views/ni/por/components/UnFinishBudgetSelect";
import CostEaItem from "@/views/ni/fin/components/CostEaItem";
import BankCardInput from "@/components/bank-card-input";
import { loadPrintTemplate } from "@/api/system/printTemplate";
import { hiprint } from "vue-plugin-hiprint";
import CostApplySelect from "@/views/ni/fin/components/CostApplySelect";
import { getDetail as getCostApplyDetail } from "@/api/ni/fin/cost-apply";
import FlowTimelinePopover from "@/components/wf-flow-timeline/popover";

export default {
  mixins: [exForm],
  components: {
    CostApplySelect,
    CostEaDetail,
    UserSelect,
    FinLoanSelect,
    AttachDialog,
    LogOptDialog,
    CostEaPayDialog,
    FinAccountingSelect,
    UnFinishBudgetSelect,
    CostEaItem,
    BankCardInput,
    FlowTimelinePopover,
  },
  data() {
    return {
      detailVisible: false,
      module: "ni_fin_cost_ea",
      processDefKey: "process_ni_fin_cost_ea",
      formKey: "wf_ex_fin/CostEa",
      loanTitleCache: new Map(), // 添加贷款标题缓存
      form: {
        items: [],
      },
      query: {},
      loading: true,
      data: [],
      selectionList: [],
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      option: {
        addBtn: false,
        searchEnter: true,
        searchLabelWidth: 110,
        labelWidth: 110,
        align: "center",
        span: 8,
        searchIndex: 3,
        searchIcon: true,
        size: "mini",
        searchSize: "mini",
        height: "auto",
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        selection: true,
        dialogClickModal: false,
        column: [
          {
            label: "状态",
            prop: "status",
            dicData: [],
            search: true,
            searchOrder: 95,
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            dataType: "number",
            type: "select",
          },
          {
            label: "发起人",
            prop: "createUserName",
            display: false,
            search: true,
            searchOrder: 96,
          },
          {
            label: "报销人",
            prop: "eaPersonId",
            hide: true,
            showColumn: false,
          },
          {
            label: "报销人",
            prop: "eaPersonName",
            display: false,
            search: true,
            searchOrder: 98,
          },
          {
            label: "流水号",
            prop: "serialNo",
            placeholder: "系统自动生成",
            searchPlaceholder: " ",
            disabled: true,
            minWidth: 125,
            search: true,
            searchOrder: 99,
          },
          {
            label: "报销类型",
            prop: "type",
            type: "select",
            placeholder: " ",
            dicData: [],
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            search: true,
            searchOrder: 97,
            rules: [
              {
                required: true,
                message: "请选择报销类型",
                trigger: "blur",
              },
            ],
            control: (val) => {
              if (val === "4") {
                return {
                  porOrderIdList: {
                    display: true,
                  },
                };
              } else {
                return {
                  porOrderIdList: {
                    display: false,
                  },
                };
              }
            },
          },
          {
            label: "账套",
            prop: "brand",
            type: "radio",
            minWidth: 90,
            dicData: [],
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            rules: [
              {
                required: true,
                message: "请选择账套",
                trigger: "blur",
              },
            ],
          },
          {
            label: "费用申请",
            prop: "costApplyId",
            hide: true,
            showColumn: false,
            rules: [
              {
                required: true,
                message: "请选择费用申请",
                trigger: "blur",
              },
            ],
          },
          {
            label: "费用申请",
            prop: "costApplySerialNo",
            display: false,
            overHidden: true,
            minWidth: 120,
            search: true,
          },
          {
            label: "关联预算",
            prop: "budgetId",
            hide: true,
            showColumn: false,
            disabled: true,
            rules: [
              {
                required: true,
                message: "请选择关联预算",
                trigger: "blur",
              },
            ],
          },
          {
            label: "关联预算",
            prop: "budgetSerialNo",
            display: false,
            minWidth: 100,
            search: true,
            overHidden: true,
          },
          {
            label: "关联项目",
            prop: "projectSerialNo",
            display: false,
            minWidth: 100,
            search: true,
            overHidden: true,
          },
          {
            label: "冲抵借款",
            prop: "loan",
            type: "switch",
            value: false,
            minWidth: 70,
            dicData: [
              {
                label: "否",
                value: false,
              },
              {
                label: "是",
                value: true,
              },
            ],
            rules: [
              {
                required: true,
                message: "请选择是否冲抵借款",
                trigger: "blur",
              },
            ],
            control: (val) => {
              if (val) {
                return {
                  loanId: {
                    display: true,
                  },
                  loanType: {
                    display: true,
                  },
                  loanAmount: {
                    display: true,
                  },
                };
              } else {
                return {
                  loanId: {
                    display: false,
                  },
                  loanType: {
                    display: false,
                  },
                  loanAmount: {
                    display: false,
                  },
                };
              }
            },
          },
          {
            label: "关联借款单",
            prop: "loanId",
            type: "input",
            display: false,
            hide: true,
            showColumn: false,
            rules: [
              {
                required: true,
                message: "请选择关联借款单",
                trigger: "blur",
              },
            ],
          },
          {
            label: "关联借款单",
            prop: "loanSerialNo",
            type: "input",
            display: false,
            width: 110,
            rules: [
              {
                required: true,
                message: "请选择关联借款单",
                trigger: "blur",
              },
            ],
          },
          {
            label: "借款类型",
            prop: "loanType",
            type: "select",
            placeholder: " ",
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_fin_loan_type",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            display: false,
            hide: true,
            disabled: true,
          },
          {
            label: "借款金额",
            prop: "loanAmount",
            type: "number",
            placeholder: " ",
            precision: 2,
            hide: true,
            display: false,
            disabled: true,
          },
          {
            label: "报销金额",
            prop: "amount",
            placeholder: " ",
            type: "number",
            precision: 2,
            disabled: true,
            minWidth: 100,
          },
          {
            label: "结算差额",
            prop: "differenceAmount",
            placeholder: "借款金额-报销金额",
            type: "number",
            width: 120,
            precision: 2,
            display: false,
          },
          {
            label: "付款方式",
            prop: "paymentType",
            type: "select",
            width: 80,
            span: 8,
            dicData: [],
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            search: true,
            placeholder: " ",
            rules: [
              {
                required: true,
                message: "请选择付款方式",
                trigger: "blur",
              },
            ],
            control: (val) => {
              if (val === "5") {
                return {
                  financeBank: {
                    display: true,
                  },
                  financeAccount: {
                    display: true,
                  },
                };
              } else {
                return {
                  financeBank: {
                    display: false,
                  },
                  financeAccount: {
                    display: false,
                  },
                };
              }
            },
          },
          {
            label: "票据张数",
            prop: "invoiceNum",
            placeholder: " ",
            type: "number",
            precision: 0,
            disabled: true,
            minWidth: 70,
          },
          {
            label: "收款银行",
            prop: "financeBank",
            display: false,
            hide: true,
            showColumn: false,
            span: 8,
            rules: [
              {
                required: true,
                message: "请输入收款银行",
                trigger: "blur",
              },
            ],
          },
          {
            label: "银行账号",
            prop: "financeAccount",
            placeholder: " ",
            span: 8,
            display: false,
            hide: true,
            showColumn: false,
            rules: [
              {
                required: true,
                message: "请输入银行账号",
                trigger: "blur",
              },
            ],
          },
          {
            label: "付款账户",
            prop: "ledgerId",
            placeholder: " ",
            type: "select",
            dicUrl: "/api/ni/fin/ledger/list?status=2",
            props: {
              label: "dictLabel",
              value: "id",
              desc: "currencyName",
            },
            overHidden: true,
            search: true,
            addDisplay: false,
            editDisplay: false,
            rules: [
              {
                required: true,
                message: "请选择",
                trigger: "blur",
              },
            ],
          },
          {
            label: "付款日期",
            prop: "payTime",
            minWidth: 115,
            type: "datetime",
            overHidden: true,
            search: true,
            searchSpan: 6,
            searchRange: true,
            placeholder: " ",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            addDisplay: false,
            editDisplay: false,
          },
          {
            label: "财务人员",
            prop: "financialStaffId",
            addDisplay: false,
            editDisplay: false,
            hide: true,
            search: true,
            showColumn: false,
          },
          {
            label: "财务人员",
            prop: "financialStaffName",
            display: false,
          },
          {
            label: "用途",
            prop: "remark",
            placeholder: " ",
            type: "textarea",
            search: true,
            span: 24,
            minRows: 2,
            overHidden: true,
            minWidth: 110,
          },
          {
            label: "附件",
            type: "upload",
            propsHttp: {
              res: "data",
              url: "attachId",
              name: "originalName",
            },
            action: "/api/blade-resource/oss/endpoint/put-file-attach",
            display: true,
            hide: true,
            showColumn: false,
            span: 24,
            showFileList: true,
            multiple: true,
            limit: 10,
            prop: "attachment",
          },
          {
            label: "报销明细",
            prop: "items",
            labelPosition: "top",
            span: 24,
            hide: true,
            showColumn: false,
          },
          {
            label: "同步",
            prop: "yonyouSync",
            minWidth: 220,
            addDisplay: false,
            editDisplay: false,
            hide: true,
          },
        ],
      },
      statusDict: [],
      statusDictKeyValue: {},
      currencyDict: [],
      currencyDictKeyValue: {},
      brandKeyValue: {},
      typeKeyValue: {},
      paymentTypeKeyValue: {},
      ledgerKeyValue: {},
      yonyouSyncSequenceDict: [],
      yonyouSyncSequenceDictKeyValue: {},
      yonyouSyncStateDict: [],
      yonyouSyncStateDictKeyValue: {},
      payDialogShow: false,
      yonyou: {
        visible: false,
        option: {
          span: 24,
          size: "mini",
          emptyBtn: false,
          column: [
            {
              prop: "date",
              label: "选择日期",
              type: "daterange",
              format: "yyyy-MM-dd",
              valueFormat: "yyyy-MM-dd",
              rules: [
                {
                  required: true,
                  message: "请选择日期范围",
                  trigger: "blur",
                },
              ],
            },
          ],
        },
        form: {},
      },
      dataType: "all",
      costEaPrintTemplate: null,
      exportColumn: [
        {
          label: "状态",
          prop: "status",
          width: 80,
        },
        {
          label: "发起人",
          prop: "createUserName",
          width: 80,
        },
        {
          label: "报销人",
          prop: "eaPersonName",
          width: 80,
        },
        {
          label: "流水号",
          prop: "serialNo",
          width: 125,
        },
        {
          label: "报销类型",
          prop: "type",
          width: 90,
        },
        {
          label: "账套",
          prop: "brand",
          width: 90,
        },
        {
          label: "费用申请",
          prop: "costApplySerialNo",
          width: 120,
        },
        {
          label: "关联预算",
          prop: "budgetSerialNo",
          width: 100,
        },
        {
          label: "关联项目",
          prop: "projectSerialNo",
          width: 100,
        },
        {
          label: "报销金额",
          prop: "amount",
          width: 100,
        },
        {
          label: "付款方式",
          prop: "paymentType",
          width: 80,
        },
        {
          label: "票据张数",
          prop: "invoiceNum",
          width: 70,
        },
        {
          label: "付款账户",
          prop: "ledgerId",
          width: 70,
        },
        {
          label: "付款日期",
          prop: "payTime",
          width: 115,
        },
        {
          label: "财务人员",
          prop: "financialStaffName",
        },
        {
          label: "用途",
          prop: "remark",
          width: 110,
        },
      ],
    };
  },
  computed: {
    ...mapGetters(["userInfo", "permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.finCostEa_add, false),
        viewBtn: this.vaildData(this.permission.finCostEa_view, false),
        delBtn: this.vaildData(this.permission.finCostEa_delete, false),
        editBtn: this.vaildData(this.permission.finCostEa_edit, false),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  mounted() {
    this.dictInit();
  },
  created() {
    loadPrintTemplate(this.module).then((res) => {
      this.costEaPrintTemplate = JSON.parse(res.data.data.content);
    });
  },
  methods: {
    //结算差额单元格样式
    cellStyle({ row, column }) {
      if (
        "differenceAmount" === column.property &&
        row.differenceAmount != null
      ) {
        let backgroundColor;
        if (row.differenceAmount == 0) {
          backgroundColor = "rgba(0, 128, 0, 0.2)"; // 绿色，透明度 20%
        } else if (row.differenceAmount < 0) {
          backgroundColor = "rgba(255, 0, 0, 0.2)"; // 红色，透明度 20%
        } else {
          backgroundColor = "rgba(255, 165, 0, 0.2)"; // 橙色，透明度 20%
        }
        return {
          backgroundColor: backgroundColor,
          color: "black",
          fontWeight: "bold",
          padding: "2px 5px",
        };
      }
    },
    rowCostApplyChange(row) {
      if (row && row.value)
        getCostApplyDetail(row.value).then((res) => {
          const data = res.data.data;
          this.form.budgetId = data.budgetId;
        });
    },
    handleDataTypeChange(val) {
      if (val === "all") {
        this.query = {};
      } else if (val === "todo") {
        this.query.todo = true;
      }
      this.onLoad(this.page);
    },
    async handleExport() {
      let msg =
        '是否导出<span style="color: #F56C6C;font-weight: bold">所有数据</span>?';
      if (this.selectionList.length > 0) {
        msg =
          '是否要导出<span style="color: #F56C6C;font-weight: bold">当前选中的数据</span>？';
      }
      let data = [];
      this.$confirm(msg, {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        dangerouslyUseHTMLString: true,
        type: "warning",
      }).then(async () => {
        if (this.selectionList.length > 0) {
          data = this.selectionList;
        } else {
          const query = { ...this.query };
          if (query.payTime && query.payTime.length === 2) {
            query.startPayTime = query.payTime[0];
            query.endPayTime = query.payTime[1];
            query.payTime = null;
          }
          const res = await getList(1, 100000, query);
          data = res.data.data.records;
        }
        this.$Export.excel({
          title: "报销单",
          columns: this.exportColumn,
          data: data.map((item) => {
            return {
              ...item,
              status: this.statusDictKeyValue[item.status],
              type: this.typeKeyValue[item.type],
              brand: this.brandKeyValue[item.brand],
              paymentType: this.paymentTypeKeyValue[item.paymentType],
              ledgerId: this.ledgerKeyValue[item.ledgerId],
            };
          }),
        });
      });
    },
    handleYonyouSync() {
      this.yonyou.visible = true;
    },
    handleYonyouSyncSubmit(form, done) {
      this.$refs.yonyouSyncRef.validate((valid) => {
        if (valid) {
          syncRange(form.date[0], form.date[1], 0).then(() => {
            this.yonyou.visible = false;
            this.yonyou.form = {};
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!",
            });
            done();
          });
        }
      });
    },
    //打印接口
    async handlePrint() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择要打印的数据");
        return;
      } else if (this.selectionList.length > 1) {
        this.$message.warning("只能选择一条数据");
        return;
      }
      let printData;
      let hiprintTemplate;
      if (!this.costEaPrintTemplate) {
        this.$message.error("打印模板加载失败，请联系管理员");
        return;
      }
      const data = await getPrintData(this.selectionList[0].id);

      const totalAmount = data.data.data.items.reduce(function (prev, cur) {
        return Number(prev) + Number(cur.amount);
      }, 0);
      const totalSheet = data.data.data.items.reduce(function (prev, cur) {
        return Number(prev) + Number(cur.num);
      }, 0);
      (printData = data.data.data),
        (printData.totalAmount = totalAmount.toLocaleString("zh-CN", {
          maximumFractionDigits: 2,
        })),
        (printData.totalSheet = totalSheet.toLocaleString()),
        (printData.returnAmount =
          printData.differenceAmount > 0
            ? Number(printData.differenceAmount).toLocaleString("zh-CN", {
                maximumFractionDigits: 2,
              })
            : "0.00"),
        (printData.addAmount =
          printData.differenceAmount < 0
            ? Math.abs(printData.differenceAmount).toLocaleString("zh-CN", {
                maximumFractionDigits: 2,
              })
            : "0.00"),
        (hiprintTemplate = new hiprint.PrintTemplate({
          template: this.costEaPrintTemplate,
        }));
      if (!printData.loanSerialNo) {
        printData.payAmount = "0.00";
        printData.addAmount = printData.amount.toLocaleString("zh-CN", {
          maximumFractionDigits: 2,
        });
      }
      hiprintTemplate.print(printData);
    },
    handleSumAmount(amount) {
      this.form.amount = amount;
    },
    handleSumNum(num) {
      this.form.invoiceNum = num;
    },
    handleBudgetClear() {
      this.form.budgetId = null;
      this.form.items = [];
    },
    rowDetail(row, index) {
      if (row.processInsId) {
        this.dynamicRoute(
          {
            processInstanceId: row.processInsId,
            formKey: this.formKey,
            processDefKey: this.processDefKey,
          },
          "detail"
        );
      } else {
        this.$refs.crud.rowView(row, index);
      }
    },
    rowYonyouSync(row) {
      let msg = "确定将选择数据同步?";
      if (row.yonyouSync && row.yonyouSync.length > 0) {
        msg = "数据已经同步过，是否继续?";
      }
      this.$confirm(msg, {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        sync(row.id).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
      });
    },
    rowPay(row) {
      //如果报销金额==借款金额，则直接完成

      //如果是报销金额>借款金额，类型是付款1
      //如果是报销金额<借款金额，类型是退款2
      const loanAmount = row.loanAmount ? row.loanAmount : 0;
      let type = 0;
      if (row.amount > loanAmount) {
        type = 2;
      } else if (row.amount < loanAmount) {
        type = 1;
      }
      this.$refs.payDialogRef.init(row, type);
    },
    rowApply(row) {
      this.$confirm("此操作将提交该报销，是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        center: true,
      }).then(() => {
        apply(row.id, this.processDefKey).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
      });
    },
    rowAttach(row) {
      this.$refs.attachDialogRef.init(row.id, this.module);
    },
    handleLog() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择要查看的数据");
        return;
      } else if (this.selectionList.length > 1) {
        this.$message.warning("只能选择一条数据");
        return;
      }
      this.$refs.logOptDialogRef.init(this.selectionList[0].id);
    },
    handleLoanSelect(selectList) {
      const row = selectList[0];
      this.form.type = row.type;
      this.form.loanAmount = row.amount;
      this.form.loanType = row.type;
      this.form.currency = row.currency;
    },
    dictInit() {
      this.$http
        .get("/api/blade-system/dict/dictionary?code=yonyou_sync_sequence")
        .then((res) => {
          this.yonyouSyncSequenceDict = res.data.data;
          this.yonyouSyncSequenceDictKeyValue =
            this.yonyouSyncSequenceDict.reduce((acc, cur) => {
              acc[cur.dictKey] = cur.dictValue;
              return acc;
            }, {});
        });
      this.$http
        .get("/api/blade-system/dict/dictionary?code=yonyou_sync_state")
        .then((res) => {
          this.yonyouSyncStateDict = res.data.data;
          this.yonyouSyncStateDictKeyValue = this.yonyouSyncStateDict.reduce(
            (acc, cur) => {
              acc[cur.dictKey] = cur.dictValue;
              return acc;
            },
            {}
          );
        });

      this.$http
        .get("/api/blade-system/dict/dictionary?code=data_status")
        .then((res) => {
          const column = this.findObject(this.option.column, "status");
          column.dicData = res.data.data;
          this.statusDict = res.data.data;
          this.statusDictKeyValue = this.statusDict.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });
      this.$http
        .get("/api/blade-system/dict-biz/dictionary?code=ni_brand")
        .then((res) => {
          const column = this.findObject(this.option.column, "brand");
          column.dicData = res.data.data;
          this.brandKeyValue = res.data.data.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });
      this.$http
        .get("/api/blade-system/dict-biz/dictionary?code=ni_fin_loan_type")
        .then((res) => {
          const column = this.findObject(this.option.column, "type");
          column.dicData = res.data.data;
          this.typeKeyValue = res.data.data.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });
      this.$http
        .get("/api/blade-system/dict-biz/dictionary?code=ni_fin_payment_type")
        .then((res) => {
          const paymentType = this.findObject(
            this.option.column,
            "paymentType"
          );
          paymentType.dicData = res.data.data;
          this.paymentTypeKeyValue = res.data.data.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });
      this.$http.get("/api/ni/fin/ledger/list?status=2").then((res) => {
        this.ledgerKeyValue = res.data.data.reduce((acc, cur) => {
          acc[cur.id] = cur.dictLabel;
          return acc;
        }, {});
      });
    },
    // 主表模块
    rowSave(row, done, loading) {
      add(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          window.console.log(error);
          loading();
        }
      );
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          window.console.log(error);
          loading();
        }
      );
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    handleApply() {
      this.dynamicRoute(
        {
          processDefKey: this.processDefKey,
          formKey: this.formKey,
        },
        "start"
      );
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen(done, type) {
      if ("add" === type) {
        this.form = {
          brand: "1",
          eaPersonId: this.userInfo.user_id,
        };
      } else if (["edit", "view"].includes(type)) {
        const porOrderListColumn = this.findObject(
          this.option.column,
          "porOrderIdList"
        );
        if ("view" === type) porOrderListColumn.tags = false;
        getDetail(this.form.id).then((res) => {
          this.form = res.data.data;
        });
      }
      if ("view" === type) {
        const column = this.findObject(this.option.column, "loanId");
        column.display = false;
      }
      done();
    },
    beforeClose(done) {
      const column = this.findObject(this.option.column, "loanId");
      column.display = true;
      done();
    },
    uploadPreview(file) {
      getAttachDetail(file.url).then((res) => {
        const { link } = res.data.data;
        window.open(link);
      });
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      const query = Object.assign(params, this.query);
      if (query.payTime && query.payTime.length == 2) {
        query.startPayTime = query.payTime[0];
        query.endPayTime = query.payTime[1];
        query.payTime = null;
      }
      getList(page.currentPage, page.pageSize, query).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
  },
};
</script>

<style></style>

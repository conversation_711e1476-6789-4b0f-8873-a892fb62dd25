<template>
  <el-row>
    <el-col :span="3">
      <div class="box">
        <el-scrollbar>
          <el-card>
            <avue-tree :option="treeOption" :data="treeData" @node-click="nodeClick">
            </avue-tree>
          </el-card>
        </el-scrollbar>
      </div>
    </el-col>
    <el-col :span="21">
      <basic-container>
        <avue-crud :option="option" :table-loading="loading" :data="data" :page.sync="page" :permission="permissionList"
          :before-open="beforeOpen" v-model="form" ref="crud" @row-update="rowUpdate" @row-save="rowSave"
          @row-del="rowDel" @search-change="searchChange" @search-reset="searchReset"
          @selection-change="selectionChange" @current-change="currentChange" @size-change="sizeChange"
          @refresh-change="refreshChange" @on-load="onLoad">
          <!-- 右侧上方按钮 -->
          <template #menuRight="{ size }">
            <el-button icon="el-icon-time" circle :size="size" @click="handleLog"></el-button>
          </template>
          <!-- 左侧自定义按钮 -->
          <template #menuLeft>
            <!-- <el-button
              type="warning"
              size="mini"
              icon="el-icon-s-order"
              @click="handleWriteOff"
            >
              销账
            </el-button> -->
            <el-button type="primary" size="mini" icon="el-icon-s-promotion" @click="handleApply">借款申请
            </el-button>
            <el-button type="warning" size="mini" icon="el-icon-printer" plain @click="handlePrint">打印借款单据</el-button>
            <el-button type="danger" :size="option.size" icon="el-icon-delete" plain v-if="permission.finLoan_delete"
              @click="handleDelete">删 除
            </el-button>
          </template>
          <!-- 操作栏按钮 -->
          <template #menu="{ row, index, size }">
            <!-- <el-button
              type="text"
              icon="el-icon-s-grid"
              :size="size"
              @click="rowItems(row)"
              >明细
            </el-button> -->
            <!-- <el-button
              type="text"
              icon="el-icon-circle-plus"
              :size="size"
              @click="supplementItems(row)"
              >增补费用
            </el-button> -->
            <el-button v-if="row.status === 9 && row.pay === false && permission.ni_fin_loan_pay" type="text"
              icon="el-icon-s-finance" :size="size" @click="rowPay(row)">付款登记
            </el-button>
            <el-button type="text" icon="el-icon-view" :size="size" @click.native="rowFlow(row, index)">详情
            </el-button>
            <!-- <el-divider direction="vertical" /> -->
            <!-- <el-dropdown>
              <el-button type="text" :size="size">
                更多<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item @click.native="rowFlow(row, index)">
                  <i class="el-icon-view"></i>详情
                </el-dropdown-item>
                <el-dropdown-item
                  @click.native="$refs.crud.rowEdit(row, index)"
                >
                  <i class="el-icon-edit"></i>编 辑
                </el-dropdown-item>
                <el-dropdown-item @click.native="$refs.crud.rowDel(row, index)">
                  <i class="el-icon-delete"></i>删 除
                </el-dropdown-item>
                <el-dropdown-item @click.native="rowLog(row)">
                  <i class="el-icon-time"></i>操作日志
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown> -->
          </template>
          <!-- 是否付款 -->
          <template #pay="{ row, index }">
            <el-tag v-if="row.pay" size="mini"> 是</el-tag>
            <el-tag v-else size="mini" type="danger"> 否</el-tag>
          </template>
          <!-- 剩余借款 -->
          <template #remainAmount="{ row }">
            <span v-if="row.payAmount && row.expensedAmount">{{ row.payAmount - row.expensedAmount }} 元</span>
          </template>
          <!-- 是否报销 -->
          <template #costEa="{ row, size, index }">
            <el-tag v-if="row.costEa" :size="size" effect="dark">是</el-tag>
            <el-tag v-else :size="size" type="info" effect="dark">否</el-tag>
          </template>
          <template #receiptStatus="{ row, size }">
            <el-tag v-if="row.receiptStatus" :size="size" effect="dark" type="success">已收款</el-tag>
            <el-tag v-else-if="!row.costEa" :size="size" type="warning" effect="dark">未报销</el-tag>
            <el-tag v-else :size="size" type="info" effect="dark">无需收款</el-tag>
          </template>
          <!-- 格式化显示银行卡号 -->
          <template #bankCardNo="{ row, size }">
            <span v-if="row.bankCardNo">
              {{ setNum(row.bankCardNo) }}
            </span>
            <span v-else-if="row.type == '3'">
              现金
            </span>
          </template>
          <!-- 开户银行 -->
          <template #bankName="{ row }">
            <span v-if="row.type == '3'">
              现金
            </span>
            <span v-else>
              {{ row.bankName }}
            </span>
          </template>
          <!-- 开户人 -->
          <template #bankAccountName="{ row }">
            <span v-if="row.type == '3'">
              现金
            </span>
            <span v-else>
              {{ row.bankAccountName }}
            </span>
          </template>
          <!-- 借款单号 -->
          <template #serialNo="{ row }">
            <flow-timeline-popover v-if="row.processInsId" :process-ins-id="row.processInsId" :form-key="formKey"
              :process-def-key="processDefKey" v-model="row.serialNo" :flow.sync="row.flow" trigger="click" lazy />
            <span v-else>{{ row.serialNo }}</span>
          </template>
          <!-- 报销单号 -->
          <template #costSerialNo="{ row }">
            <flow-timeline-popover v-if="row.costEaProInsId" :process-ins-id="row.costEaProInsId"
              :form-key="costEaFormKey" :process-def-key="costEaProcessDefKey" v-model="row.costEaSerialNo"
              :flow.sync="row.flow" trigger="click" lazy />
            <span v-else>{{ row.costSerialNo }}</span>
          </template>
          <template #createUserSearch="{ disabled, size, index, row }">
            <user-select v-model="form.createUser" :size="size" :disabled="disabled"></user-select>
          </template>
          <template #status="{ row, index }">
            <el-tag v-if="row.status === 0" size="mini" type="info">
              {{ statusDictKeyValue[row.status] }}
            </el-tag>
            <el-tag v-else-if="row.status === 1" size="mini" effect="plain">
              {{ statusDictKeyValue[row.status] }}
            </el-tag>
            <el-tag v-else-if="row.status === 2" size="mini" effect="plain">
              {{ statusDictKeyValue[row.status] }}
            </el-tag>
            <el-tag v-else-if="row.status === 3" size="mini" type="danger">
              {{ statusDictKeyValue[row.status] }}
            </el-tag>
            <el-tag v-else-if="row.status === 4" size="mini" type="warning" effect="plain">
              {{ statusDictKeyValue[row.status] }}
            </el-tag>
            <el-tag v-else-if="row.status === 5" size="mini" type="warning" effect="plain">
              {{ statusDictKeyValue[row.status] }}
            </el-tag>
            <el-tag v-else-if="row.status === 6" size="mini" type="danger" effect="plain">
              {{ statusDictKeyValue[row.status] }}
            </el-tag>
            <el-tag v-else-if="row.status === 9" size="mini" type="success" effect="plain">
              {{ statusDictKeyValue[row.status] }}
            </el-tag>
          </template>
          <template #brand="{ row, disabled, size, index }">
            <el-tag v-if="row.brand" :size="size" :effect="row.brand === '1' ? 'dark ' : 'plain'">
              {{ brandDictKeyValue[row.brand] }}
            </el-tag>
          </template>
          <template #currency="{ row, size }">
            <el-tag v-if="row.currency" :size="size" effect="plain">
              {{ currencyDictKeyValue[row.currency] }}
            </el-tag>
          </template>
          <template #type="{ row, size }">
            <el-tag v-if="row.type" :size="size" effect="info">
              {{ typeDictKeyValue[row.type] }}
            </el-tag>
          </template>
        </avue-crud>
        <!-- 借款开支增补费用弹窗 -->
        <loan-supplement-items-dialog ref="loanSupplementItemsDialogRef" @submit="handleItemSubmit" />
        <!-- 借款开支明细 -->
        <loan-expend-item-drawer ref="loanExpendItemDrawerRef" @submit="handleItemSubmit" />
        <!-- 销账 -->
        <loan-write-off-dialog ref="loanWriteOffDialogRef" @submit="handleWriteOffSubmit"></loan-write-off-dialog>
        <!-- 日志 -->
        <log-opt-dialog ref="logOptDialogRef" :module="module" />
        <el-drawer :visible.sync="detailVisible" :title="form.title" custom-class="wf-drawer" size="100%"
          append-to-body>
          <task-detail v-if="detailVisible" :taskId="form.taskId" :processInstanceId="form.processInsId" />
        </el-drawer>
        <el-dialog title="付款登记" append-to-body :visible.sync="pay.visible" width="555px">
          <avue-form v-if="pay.visible" :option="pay.option" v-model="pay.form" @submit="handlePaySubmit">
            <!-- <template #financialUserId="{ disabled, size, index }">
              <user-select
                v-model="form.financialUserId"
                :size="size"
                :disabled="disabled"
              />
            </template> -->
          </avue-form>
        </el-dialog>
      </basic-container>
    </el-col>
  </el-row>
</template>

<script>
import {
  add,
  getDetail,
  getList,
  remove,
  submit,
  update,
  payLoan,
} from "@/api/ni/fin/loan";
import { mapGetters } from "vuex";
import LogOptDialog from "@/components/log-opt-dialog";
import { getByModule } from "@/api/ni/base/module-flow";
import UserSelect from "@/components/user-select";
import { getDeptLazyTree } from "@/api/system/dept";
import exForm from "@/views/plugin/workflow/mixins/ex-form";
import LoanDetail from "@/views/ni/fin/loan-detail";
import TaskDetail from "@/views/plugin/workflow/ops/detail";
import { dateFormat } from "@/util/date";
import LoanSupplementItemsDialog from "@/views/ni/fin/components/LoanSupplementItemsDialog";
import LoanExpendItemDrawer from "@/views/ni/fin/components/LoanExpendItemDrawer";
import LoanWriteOffDialog from "@/views/ni/fin/components/LoanWriteOffDialog";
import FlowTimelinePopover from "@/components/wf-flow-timeline/popover";
import { loadPrintTemplate } from "@/api/system/printTemplate";
import { hiprint } from "vue-plugin-hiprint";
import { numToCapital } from "@/util/util";

export default {
  mixins: [exForm],
  components: {
    TaskDetail,
    LogOptDialog,
    UserSelect,
    LoanDetail,
    LoanSupplementItemsDialog,
    LoanExpendItemDrawer,
    LoanWriteOffDialog,
    FlowTimelinePopover,
  },
  data() {
    return {
      detailVisible: false,
      bpmnOption: {},
      module: "ni_fin_loan",
      processDefKey: "process_fin_loan",
      costEaProcessDefKey: "process_ni_fin_cost_ea",
      loanPrintTemplate: null,
      formKey: "ni_fin_loan",
      costEaFormKey: "wf_ex_fin/CostEa",
      treeDeptId: "",
      treeData: [],
      treeOption: {
        searchSize: "mini",
        nodeKey: "id",
        lazy: true,
        treeLoad: function (node, resolve) {
          const parentId = node.level === 0 ? 0 : node.data.id;
          getDeptLazyTree(parentId).then((res) => {
            resolve(
              res.data.data.map((item) => {
                return {
                  ...item,
                  leaf: !item.hasChildren,
                };
              })
            );
          });
        },
        addBtn: false,
        menu: false,
        size: "small",
        props: {
          labelText: "标题",
          label: "title",
          value: "value",
          children: "children",
        },
      },
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
        descs: "id",
      },
      selectionList: [],
      option: {
        editBtn: false,
        delBtn: false,
        menuWidth: 180,
        align: "center",
        span: 12,
        searchIndex: 3,
        searchIcon: true,
        size: "mini",
        searchSize: "mini",
        height: "auto",
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        selection: true,
        dialogClickModal: false,
        column: [
          {
            label: "状态",
            prop: "status",
            dicData: [],
            search: true,
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            dataType: "number",
            display: false,
            type: "select",
          },
          {
            label: "申请人",
            prop: "createUser",
            type: "input",
            display: false,
            hide: true,
            showColumn: false,
            search: true,
          },
          {
            label: "申请人",
            prop: "createUserName",
            type: "input",
            display: false,
          },
          {
            label: "申请部门",
            prop: "createDeptName",
            type: "input",
            display: false,
            hide: true,
          },
          {
            label: "申请日期",
            prop: "applyTime",
            type: "date",
            format: "yyyy-MM-dd",
            dataType: "date",
            valueFormat: "yyyy-MM-dd",
            width: 90,
            overHidden: true,
            pickerOptions: {
              disabledDate(time) {
                return time.getTime() > Date.now();
              },
            },
          },
          {
            label: "主题",
            prop: "title",
            overHidden: true,
            rules: [
              {
                required: true,
                message: "请输入标题",
                trigger: "blur",
              },
            ],
          },
          {
            label: "借款单号",
            prop: "serialNo",
            placeholder: "系统自动生成",
            disabled: true,
            search: true,
            width: 120,
          },
          {
            label: "报销单号",
            prop: "costSerialNo",
            width: 120,
          },
          {
            label: "借款事由",
            prop: "reason",
            type: "textarea",
            overHidden: true,
            minRows: 1,
            search: true,
            rules: [
              {
                required: true,
                message: "请输入借款事由",
                trigger: "blur",
              },
            ],
          },
          {
            label: "借款类型",
            prop: "type",
            type: "select",
            placeholder: " ",
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_fin_payment_type",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            search: true,
            rules: [
              {
                required: true,
                message: "请输入类型",
                trigger: "blur",
              },
            ],
          },
          {
            label: "币种",
            prop: "currency",
            type: "select",
            search: true,
            dictData: [],
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            value: "RMB",
            placeholder: " ",
            rules: [
              {
                required: true,
                message: "请选择币种",
                trigger: "blur",
              },
            ],
            control: (val) => {
              if (val === "RMB") {
                return {
                  excRate: {
                    display: false,
                    value: 1,
                  },
                };
              } else {
                return {
                  excRate: {
                    display: true,
                  },
                };
              }
            },
          },
          // {
          //   label: "汇率",
          //   prop: "excRate",
          //   labelTip:
          //     "汇率=本位币/原币.如本位币为人民币，原币为美元: 汇率为:0.1439.",
          //   type: "number",
          //   placeholder: " ",
          //   hide: true,
          //   display: false,
          //   rules: [
          //     {
          //       required: true,
          //       message: "请输入汇率",
          //       trigger: "blur",
          //     },
          //   ],
          // },
          {
            label: "借款金额",
            prop: "amount",
            type: "number",
            overHidden: true,
            precision: 2,
            rules: [
              {
                required: true,
                message: "请输入借款金额",
                trigger: "blur",
              },
            ],
          },
          {
            label: "银行卡号",
            prop: "bankCardNo",
            width: 150,
            rules: [
              {
                required: true,
                message: "请输入银行卡号",
                trigger: "blur",
              },
            ],
          },
          {
            label: "开户行",
            prop: "bankName",
            width: 110,
            overHidden: true,
            rules: [
              {
                required: true,
                message: "请输入开户行",
                trigger: "blur",
              },
            ],
          },
          {
            label: "开户人姓名",
            prop: "bankAccountName",
            rules: [
              {
                required: true,
                message: "请输入开户人姓名",
                trigger: "blur",
              },
            ],
          },
          {
            label: "是否付款",
            prop: "pay",
            minWidth: 69,
            type: "select",
            dicData: [
              {
                label: "是",
                value: true,
              },
              {
                label: "否",
                value: false,
              },
            ],
            addDisplay: false,
            editDisplay: false,
            search: true,
          },
          {
            label: "付款人员",
            prop: "financialUserName",
            addDisplay: false,
            editDisplay: false,
          },
          {
            label: "付款日期",
            prop: "payDate",
            type: "date",
            minWidth: 100,
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            addDisplay: false,
            editDisplay: false,
          },
          {
            label: "付款账户",
            prop: "ledgerId",
            placeholder: " ",
            type: "select",
            minWidth: 110,
            dicUrl: "/api/ni/fin/ledger/list?status=2",
            props: {
              label: "dictLabel",
              value: "id",
              desc: "currencyName",
            },
            overHidden: true,
            search: true,
            addDisplay: false,
            editDisplay: false,
            rules: [
              {
                required: true,
                message: "请选择",
                trigger: "blur",
              },
            ],
          },
          {
            label: "付款金额",
            prop: "payAmount",
            type: "number",
            overHidden: true,
            precision: 2,
            addDisplay: false,
            editDisplay: false,
          },
          {
            label: "账套",
            prop: "brand",
            type: "radio",
            dictData: [],
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            search: true,
            rules: [
              {
                required: true,
                message: "请选择账套",
                trigger: "blur",
              },
            ],
          },
          // {
          //   label: "已开销",
          //   prop: "expensedAmount",
          //   type: "number",
          //   precision: 2,
          //   display: false,
          // },
          // {
          //   label: "剩余借款",
          //   prop: "remainAmount",
          //   type: "number",
          //   precision: 2,
          //   display: false,
          // },
          {
            label: "是否报销",
            prop: "costEa",
            type: "select",
            dicData: [
              {
                label: "是",
                value: true,
              },
              {
                label: "否",
                value: false,
              },
            ],
            addDisplay: false,
            editDisplay: false,
            search: true,
          },
          {
            label: "收款状态",
            prop: "receiptStatus",
          },
          {
            label: "备注",
            prop: "remark",
            type: "textarea",
            hide: true,
            span: 24,
          },
        ],
      },
      data: [],
      statusDict: [],
      statusDictKeyValue: {},
      brandDict: [],
      brandDictKeyValue: {},
      currencyDict: [],
      currencyDictKeyValue: {},
      typeDict: [],
      typeDictKeyValue: {},
      pay: {
        visible: false,
        option: {
          emptyBtn: false,
          span: 24,
          size: "mini",
          searchSize: "mini",
          column: [
            {
              label: "财务人员",
              prop: "financialUserId",
              dicUrl: "/api/blade-user/user-list",
              type: "select",
              disabled: true,
              props: {
                label: "account",
                value: "id",
              },
              rules: [
                {
                  required: true,
                  message: "请选择财务人员",
                  trigger: "blur",
                },
              ],
            },
            {
              label: "付款账户",
              prop: "ledgerId",
              placeholder: " ",
              type: "select",
              dicUrl: "/api/ni/fin/ledger/list?status=2",
              filterable: true,
              props: {
                label: "dictLabel",
                value: "id",
                desc: "currencyName",
              },
              rules: [
                {
                  required: true,
                  message: "请选择付款账户",
                  trigger: "blur",
                },
              ],
            },
            {
              label: "付款日期",
              prop: "payDate",
              type: "date",
              format: "yyyy-MM-dd",
              valueFormat: "yyyy-MM-dd",
              rules: [
                {
                  required: true,
                  message: "请选择付款日期",
                  trigger: "blur",
                },
              ],
            },
            {
              label: "付款金额",
              prop: "payAmount",
              placeholder: " ",
              type: "number",
              min: 0,
              precision: 2,
              overHidden: true,
              search: true,
              addDisplay: false,
              editDisplay: false,
              rules: [
                {
                  required: true,
                  message: "请输入付款金额",
                  trigger: "blur",
                },
              ],
            },
            {
              label: "账套",
              prop: "brand",
              type: "radio",
              dicUrl: "/api/blade-system/dict-biz/dictionary?code=ni_brand",
              props: {
                label: "dictValue",
                value: "dictKey",
              },
              search: true,
              rules: [
                {
                  required: true,
                  message: "请选择账套",
                  trigger: "blur",
                },
              ],
            },
          ],
        },
        form: {},
      },
    };
  },
  computed: {
    ...mapGetters(["userInfo", "permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.finLoan_add, false),
        viewBtn: this.vaildData(this.permission.finLoan_view, false),
        delBtn: this.vaildData(this.permission.finLoan_delete, false),
        editBtn: this.vaildData(this.permission.finLoan_edit, false),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  created() {
    this.dictInit();
    loadPrintTemplate("ni_fin_loan").then((res) => {
      this.loanPrintTemplate = JSON.parse(res.data.data.content);
    });
  },
  methods: {
    //打印借款单据
    handlePrint() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请至少选择一条数据");
        return;
      } else if (this.selectionList.length > 1) {
        this.$message.warning("只能选择单条数据");
        return;
      }
      if (!this.loanPrintTemplate) {
        this.$message.error("打印模板加载失败，请联系管理员");
        return;
      }

      let hiprintTemplate;
      let printDataList;
      printDataList = this.selectionList[0];

      printDataList = {
        ...printDataList,
        bigAmount: this.currencyDictKeyValue[printDataList.currency]
          + " (大写) " + numToCapital(printDataList.amount),
        bankName:  printDataList.type === "3" ? "现金" : printDataList.bankName,
        bankCardNo: printDataList.type === "3" ? "现金" : printDataList.bankCardNo,
        bankAccountName: printDataList.type === "3" ? "现金" : printDataList.bankAccountName,
      }


      hiprintTemplate = new hiprint.PrintTemplate({
        template: this.loanPrintTemplate,
      });
      hiprintTemplate.print(printDataList);
    },
    // 设置银行卡号,每四位添加一个空格
    setNum(data) {
      let res = "";
      res = data
        .replace(/\s/g, "")
        .replace(/[^\d]/g, "")
        .replace(/(\d{4})(?=\d)/g, "$1 ");
      return res;
    },
    //明细提交回调
    // handleItemSubmit(form) {
    //   console.log("回调成功！！！");
    //   this.onLoad(this.page);
    // },
    //增补费用
    supplementItems(row) {
      this.$refs.loanSupplementItemsDialogRef.initDialog(row);
    },
    //借款开支明细
    rowItems(row) {
      this.$refs.loanExpendItemDrawerRef.init(row);
    },
    //销账
    // handleWriteOff(row) {
    //   if (this.selectionList.length === 0) {
    //     this.$message.warning("请选择一条报销数据");
    //     return;
    //   } else if (this.selectionList.length > 1) {
    //     this.$message.warning("只能选择一条数据");
    //     return;
    //   }
    //   this.$refs.loanWriteOffDialogRef.initDialog(this.selectionList[0]);
    // },
    handlePaySubmit(form, done) {
      this.pay.visible = false;
      payLoan(form)
        .then(() => {
          this.refreshChange();
        })
        .finally(() => {
          done();
        });
    },
    rowPay(row) {
      this.pay.form = {
        id: row.id,
        pay: true,
        payDate: dateFormat(new Date(), "yyyy-MM-dd"),
        financialUserId: this.userInfo.user_id,
        payAmount: row.amount,
        ledgerId: row.ledgerId,
        brand: row.brand,
      };
      this.pay.visible = true;
    },
    handleApply() {
      this.dynamicRoute(
        {
          processDefKey: this.processDefKey,
        },
        "start"
      );
    },
    rowFlow(row, index) {
      if (row.processInsId) {
        this.dynamicRoute(
          {
            processInstanceId: row.processInsId,
            id: row.id,
          },
          "detail",
          true
        ).then(() => {
          this.form = { ...row };
          this.detailVisible = true;
        });
      } else {
        this.$refs.crud.rowView(row, index);
      }
    },
    nodeClick(data) {
      this.treeDeptId = data.id;
      this.page.currentPage = 1;
      this.onLoad(this.page);
    },
    handleLog() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择要查看的数据");
        return;
      } else if (this.selectionList.length > 1) {
        this.$message.warning("只能选择一条数据");
        return;
      }
      this.$refs.logOptDialogRef.init(this.selectionList[0].id);
    },
    loadDefaultFlowKey() {
      getByModule(this.module).then((res) => {
        const moduleFlow = res.data.data;
        if (moduleFlow) {
          this.processDefKey = moduleFlow.flowKey;
        }
      });
    },
    handleSubmit(type) {
      this.form.status = 1;
      if (type === "add") {
        this.$refs.crud.rowSave();
      } else if (type === "edit") {
        this.$refs.crud.rowUpdate();
      }
    },
    rowSubmit(row) {
      this.$confirm("此操作将提交该项目，是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        center: true,
      }).then(() => {
        getDetail(row.id).then((res) => {
          const form = res.data.data;
          submit(form, this.processDefKey).then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!",
            });
          });
        });
      });
    },
    dictInit() {
      this.$http
        .get("/api/blade-system/dict/dictionary?code=data_status")
        .then((res) => {
          const column = this.findObject(this.option.column, "status");
          column.dicData = res.data.data;
          this.statusDict = res.data.data;
          this.statusDictKeyValue = this.statusDict.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });
      this.$http
        .get("/api/blade-system/dict-biz/dictionary?code=ni_brand")
        .then((res) => {
          const column = this.findObject(this.option.column, "brand");
          column.dicData = res.data.data;
          this.brandDict = res.data.data;
          this.brandDictKeyValue = this.brandDict.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });
      this.$http
        .get("/api/blade-system/dict/dictionary?code=currency")
        .then((res) => {
          const column = this.findObject(this.option.column, "currency");
          column.dicData = res.data.data;
          this.currencyDict = res.data.data;
          this.currencyDictKeyValue = this.currencyDict.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });
      this.$http
        .get("/api/blade-system/dict-biz/dictionary?code=ni_fin_payment_type")
        .then((res) => {
          const column = this.findObject(this.option.column, "type");
          column.dicData = res.data.data;
          this.typeDict = res.data.data;
          this.typeDictKeyValue = this.typeDict.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });
    },
    rowSave(row, done, loading) {
      if (this.form.status === 1) {
        submit(row, this.processDefKey).then(
          () => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!",
            });
            done();
          },
          (error) => {
            loading();
            window.console.log(error);
          }
        );
      } else {
        add(row).then(
          () => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!",
            });
            done();
          },
          (error) => {
            loading();
            window.console.log(error);
          }
        );
      }
    },
    rowUpdate(row, index, done, loading) {
      if (this.form.status === 1) {
        submit(row, this.processDefKey).then(
          () => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!",
            });
            done();
          },
          (error) => {
            loading();
            window.console.log(error);
          }
        );
      } else {
        update(row).then(
          () => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!",
            });
            done();
          },
          (error) => {
            loading();
            console.log(error);
          }
        );
      }
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen(done, type) {
      if (type === "add") {
        this.form.applyTime = dateFormat(new Date(), "yyyy-MM-dd");
      }
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then((res) => {
          this.form = res.data.data;
        });
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      params.descs = this.page.descs;
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query),
        this.treeDeptId
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
  },
};
</script>

<style></style>

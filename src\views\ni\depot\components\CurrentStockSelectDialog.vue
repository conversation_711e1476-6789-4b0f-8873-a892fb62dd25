<template>
  <el-dialog
    title="库存选择"
    :visible.sync="visible"
    width="86%"
    append-to-body
  >
    <template #title>
      <span>库存选择</span>
      <el-divider direction="vertical" />
      <el-tag>
        当前表格已选择
        <span style="font-weight: bolder">{{ selectionList.length }}</span> 项
        <el-button type="text" size="mini" @click="selectionClear">
          清空
        </el-button>
      </el-tag>
    </template>
    <el-container>
      <avue-crud
        v-if="visible"
        :option="option"
        :table-loading="loading"
        :data="data"
        :page.sync="page"
        v-model="form"
        ref="crud"
        @search-change="searchChange"
        @search-reset="searchReset"
        @selection-change="selectionChange"
        @current-change="currentChange"
        @size-change="sizeChange"
        @refresh-change="refreshChange"
        @on-load="onLoad"
        @row-click="rowClick"
        :cell-style="cellStyle"
      >
        <template #num="{ row, index }">
          <span :style="{ color: colorName, fontWeight: 'bold' }">
            {{ Number(row.num).toLocaleString("ZH") }}
          </span>
        </template>
        <template #pv="{ row, index }">
          <el-tag size="mini" type="danger" effect="dark" v-if="row.pv">
            是
          </el-tag>
          <el-tag size="mini" type="info" effect="plain" v-else> 否</el-tag>
        </template>
        <template #projectSerialNo="{ row, type, size }">
          <span v-if="row.projectId">
            {{ row.projectSerialNo }}
          </span>
          <span v-else-if="row.budgetId">{{ row.budgetSerialNo }}</span>
        </template>
      </avue-crud>
    </el-container>
    <span slot="footer" class="dialog-footer">
      <el-button size="mini" @click="visible = false">取 消</el-button>
      <el-button
        size="mini"
        :disabled="selectionList.length === 0"
        type="primary"
        @click="handleSubmit"
      >
        确 定
      </el-button>
    </span>
  </el-dialog>
</template>

<script>
import { getList } from "@/api/ni/depot/currentStock";
import { mapGetters } from "vuex";

export default {
  props: {
    params: Object,
  },
  computed: {
    ...mapGetters(["permission", "userInfo", "colorName"]),
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  data() {
    return {
      visible: false,
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      exclusions: [],
      option: {
        selectable: (row) => {
          return (
            !this.exclusions ||
            !this.exclusions.some((item) => item.currentStockId === row.id)
          );
        },
        reserveSelection: true,
        searchEnter: true,
        header: false,
        searchSize: "mini",
        highlightCurrentRow: true,
        size: "mini",
        menu: false,
        align: "center",
        calcHeight: 30,
        tip: false,
        searchIcon: true,
        searchIndex: 3,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: false,
        addBtn: false,
        selection: true,
        dialogClickModal: false,
        cancelBtn: false,
        column: [
          {
            label: "仓库",
            prop: "depotId",
            type: "select",
            dicUrl: "/api/ni/base/depot/info/list?type=7,8&status=2",
            props: {
              label: "name",
              value: "id",
            },
            width: 100,
            overHidden: true,
          },
          {
            label: "库位",
            prop: "depotLocation",
            overHidden: true,
            search: true,
            display: false,
            filters: true,
            remote: true,
            width: 100,
            placeholder: " ",
            searchOrder: 99,
          },
          {
            label: "品名",
            prop: "materialName",
            search: true,
            width: 100,
            searchOrder: 99,
            overHidden: true,
          },
          {
            label: "规格型号",
            overHidden: true,
            prop: "specification",
            filters: true,
            search: true,
            searchOrder: 98,
            width: 100,
          },
          {
            label: "材质",
            overHidden: true,
            filters: true,
            prop: "quality",
            width: 95,
          },
          {
            label: "单位",
            prop: "unit",
            overHidden: true,
            type: "select",
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_material_unit",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            width: 60,
            slot: true,
          },
          {
            label: "库存",
            prop: "num",
            type: "number",
            width: 70,
            overHidden: true,
            value: 0,
          },
          {
            label: "用途",
            prop: "purpose",
            display: false,
            search: true,
            width: 100,
            overHidden: true,
          },
          {
            label: "项目/预算",
            prop: "projectSerialNo",
            span: 6,
            search: true,
            overHidden: true,
            disabled: true,
            filterable: true,
            placeholder: " ",
            searchOrder: 94,
            width: 100,
          },
          {
            label: "请购人",
            prop: "personName",
            filters: true,
            width: 80,
          },
          {
            label: "材料批号",
            prop: "sn",
            width: 100,
            overHidden: true,
          },
          {
            label: "编码",
            prop: "materialCode",
            search: true,
            overHidden: true,
            width: 100,
          },
          {
            label: "压力容器",
            prop: "pv",
            width: 70,
          },
          {
            label: "备注",
            prop: "remark",
            type: "textarea",
            width: 110,
            span: 24,
            minRows: 3,
            overHidden: true,
          },
        ],
      },
      data: [],
      currentRow: {},
    };
  },
  methods: {
    onSelect(depotId, exclusions, depotLocation) {
      this.query = {
        depotId,
        depotLocation,
      };
      this.depotId = depotId;
      this.depotLocation = depotLocation;
      this.exclusions = exclusions;
      this.visible = true;
    },
    handleSubmit() {
      this.$emit("submit", this.selectionList);
      this.visible = false;
    },
    searchReset() {
      this.query = {
        depotId: this.depotId,
        depotLocation: this.depotLocation,
      };
      this.page.currentPage = 1;
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      const q = {
        ...params,
        ...this.params,
        depotId: this.depotId,
        depotLocation: this.depotLocation,
        ...this.query,
        unZero: true,

      };
      getList(page.currentPage, page.pageSize, q).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
      });
    },
    rowClick(row) {
      if (
        !this.exclusions ||
        !this.exclusions.some((item) => item.currentStockId === row.id)
      )
        this.$refs.crud.toggleSelection([row]);
    },
    cellStyle({ row, column }) {
      if (
        ["depotId", "depotLocation"].includes(column.columnKey) &&
        row.status < 0
      ) {
        return {
          backgroundColor: "#409EFF",
          color: "#fff",
        };
      }
      if ("materialName" === column.columnKey && row.pv) {
        return {
          backgroundColor: "#F56C6C",
          color: "#fff",
        };
      }
    },
  },
};
</script>

<style scoped></style>

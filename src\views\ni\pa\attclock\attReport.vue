<template>
  <basic-container id="att-report-bsc">
    <avue-crud :key="randomKey" class="att-report-class" :option="option" :table-loading="loading" :data="data"
      :page.sync="page" :permission="permissionList" :before-open="beforeOpen" :search.sync="query" v-model="form"
      ref="crud" @search-change="searchChange" @search-reset="searchReset" @selection-change="selectionChange"
      @current-change="currentChange" @size-change="sizeChange" @refresh-change="refreshChange" @on-load="onLoad"
      :row-class-name="rowClassName" :cell-style="cellStyle" @cell-click="cellMouseClick"
      @cell-mouse-enter="cellMouseEnter">
      <template #menuLeft>
        <el-button size="mini" plain icon="el-icon-s-check" type="primary" v-if="permission.att_report_save"
          @click="storeMonth()">提交
        </el-button>
        <el-button type="warning" size="mini" icon="el-icon-printer" plain v-if="permission.att_report_print"
          @click="handlePrint">打印
        </el-button>
        <el-button size="mini" plain icon="el-icon-download" type="success" v-if="permission.att_report_export"
          @click="handleExport">导出
        </el-button>
        <el-dropdown v-if="!modifyResultShow && !modifyPaiBanShow && !checkTagShow">
          <el-button size="mini" icon="el-icon-edit">批量修改</el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item plain @click.native="handleModifyResultShow()">修改考勤结果
            </el-dropdown-item>
            <el-dropdown-item disabled plain @click.native="handleModifyPaiBanShow()">修改排班
            </el-dropdown-item>
            <!-- <el-dropdown-item plain @click.native="multiCmtForm(month)"
              >修改备注
            </el-dropdown-item> -->
          </el-dropdown-menu>
        </el-dropdown>
        <el-button size="mini" plain @click="modifyCancel" v-if="modifyResultShow || modifyPaiBanShow" type="info">取消
        </el-button>
        <el-button size="mini" plain @click="modifySubmit" v-if="modifyResultShow || modifyPaiBanShow" type="primary">保存
        </el-button>
        <el-dropdown v-if="modifyResultShow">
          <el-button type="text" size="mini" :labelText="modifyLabel" style="color: red">
            {{ modifyLabel ? modifyLabel : "请选择 考勤结果"
            }}<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item v-for="item in modifyOptions" :key="item.dictKey" plain
              @click.native="changeModifyResult(item)">{{ item.dictValue }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-dropdown v-if="modifyPaiBanShow">
          <el-button type="text" size="mini" :labelText="modifyPaiBan" style="color: red">
            {{ modifyPaiBan ? modifyPaiBan : "请选择 排班"
            }}<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item v-for="item in modifyPaiBanList" v-if="modifyPaiBan !== item.label" :key="item.value"
              plain @click.native="changeModifyPaiBan(item)">{{ item.label }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-button size="mini" plain @click="handleCheckTagShow()" v-if="
          permissionList.checkedBtn &&
          !modifyResultShow &&
          !modifyPaiBanShow &&
          !checkTagShow
        " type="info" style="margin-left: 0px">标记
        </el-button>
        <el-button size="mini" plain @click="handleCheckTagHide()" v-if="checkTagShow" type="info">取消
        </el-button>
        <el-button size="mini" plain @click="submitCheckTag()" v-if="checkTagShow" type="primary">保存
        </el-button>
        <el-divider direction="vertical"></el-divider>
        <!--        <el-checkbox v-model="isOri">原始数据</el-checkbox>-->
        <div style="display: inline-block">
          <div style="display: flex; justify-content: center">
            补卡：
            <el-color-picker size="mini" v-model="repairColor"></el-color-picker>
          </div>
        </div>
      </template>
      <template #name="{ row, index }">
        <span v-if="row.level === 255" style="color: #ffffff">
          {{ row.name }}
        </span>
        <span v-else :style="userArr[index].style">
          {{ row.name }}
        </span>
      </template>
      <template #attDaysHeader="{ column }">
        {{ column.label }}
        <div v-if="workDayCount">{{ workDayCount + "天" }}</div>
      </template>
      <template #vacationHeader="{ column }">
        {{ column.label }}
        <div v-if="nonworkDayCount">{{ nonworkDayCount + "天" }}</div>
      </template>
      <template #attDays="{ row, label }">
        <div :style="Number(label) + Number(row.paidVacation) < workDayCount
          ? 'color: red; font-weight: bold'
          : ''
          ">
          {{ label }}
        </div>
      </template>
      <template #leave="{ row, label }">
        <div :style="Number(label) + Number(row.vacation) > nonworkDayCount
          ? 'color: red; font-weight: bold'
          : ''
          ">
          {{ label }}
        </div>
      </template>
      <template #attComment="{ index }">
        <div class="att-comment" :class="{
          'att-comment-checked': attCommentMark[index].focusClass,
          'first-checked': firstCheckedAttComment === index,
          'last-checked': lastCheckedAttComment === index,
        }" @mousedown="commentMousedown(index)" @mouseenter="commentMouseenter(index)" @dblclick="chooseInput(index)">
          <input v-model="attCommentMark[index].attComment" @change="inputCommentMark(attCommentMark[index])"
            @blur="attCommentMark[index].readonly = true" :readonly="attCommentMark[index].readonly" />
        </div>
      </template>
      <template v-for="col in dayCol" :slot="`${col}Header`">
        <div :key="col">
          {{ col }}<br />
          {{ dayOfWeek(col) }}
        </div>
      </template>
      <template v-for="col in dayCol" slot-scope="{ row, size }" :slot="col">
        <div :key="col">
          <el-button :size="size" type="text" @click="
            !modifyResultShow && !modifyPaiBanShow && !checkTagShow
              ? handleItemBox(row, col)
              : ''
            " :style="cellBtnStyle(row[`${col}_statusCode`])">{{
              isOri
                ? row[`${col}_oriResult`]
                  ? row[`${col}_oriResult`]
                  : "&nbsp;"
                : row[`${col}_msgAlias`]
                  ? row[`${col}_msgAlias`]
                  : "&nbsp;"
            }}
            <div v-if="nameMap[`${row.userId}`][`${col}`]">✔️</div>
            <div v-if="
              checkTagShow &&
              nameMap[`${row.userId}`][`${col}_checkable`] &&
              nameMap[`${row.userId}`][`${col}_check`]
            ">
              ✔️
            </div>
          </el-button>
        </div>
      </template>
      <template #queryAttPaySearch="{ search }">
        <el-row>
          <el-col :span="11">
            <el-input-number v-model="search.attPayLeft" :max="maxDayOfMonth" :step="0.01" :precision="2"
              :step-strictly="true" size="mini" style="height: 100%" :controls="false" placeholder="大于等于"
              @keyup.enter.native="onLoad(page)">
            </el-input-number>
          </el-col>
          <el-col :span="2"> 至</el-col>
          <el-col :span="11">
            <el-input-number v-model="search.attPayRight" :max="maxDayOfMonth" :step="0.01" :step-strictly="true"
              :precision="2" size="mini" style="height: 100%" :controls="false" placeholder="小于等于"
              @keyup.enter.native="onLoad(page)">
            </el-input-number>
          </el-col>
        </el-row>
      </template>
    </avue-crud>
    <att-report-item ref="itemForm" @hide="handelHideItem"></att-report-item>

    <el-dialog 
      title="提示" 
      :visible.sync="printDialogVisible" 
      width="20%" 
      :before-close="handleClose" 
      append-to-body>
      <span style="font-size: larger; font-weight: bold;">是否打印当前筛选的所有数据？</span>
      <div class="print-input" v-if="permission.att_report_printWorkDay">
        <p>本月满勤天数:</p>
        <el-input  size="mini" v-model="printWorkDayCount" placeholder="请输入本月满勤天数"></el-input>
      </div>
      
      <span slot="footer" class="dialog-footer">
        <el-button @click="printDialogVisible=false" size="small">取 消</el-button>
        <el-button type="primary" @click="handlePrintData" size="small">确 定</el-button>
      </span>
    </el-dialog>
  </basic-container>
</template>

<script>
import { mapGetters } from "vuex";
import {
  getDetail,
  multiComment,
  multipleCheck,
  multipleModify,
  reportPage,
  store,
} from "@/api/ni/pa/attendance/paAttendance";
import AttReportItem from "@/views/ni/pa/attclock/components/attReportItem";
import NProgress from "nprogress";
import { exportBlob } from "@/api/common";
// import printJS from "print-js";
import { downloadXls } from "@/util/util";
import { dateFormat } from "@/util/date";
import UserSelect from "../../../../components/user-select";
import { hiprint } from "vue-plugin-hiprint";
import { loadPrintTemplate } from "@/api/system/printTemplate";

export default {
  name: "attReport",
  components: {
    AttReportItem,
    UserSelect,
  },
  created() {
    let storeAtt = localStorage.getItem("attReport_pageSize");
    if (storeAtt) {
      let parseAtt = JSON.parse(storeAtt);
      if (parseAtt.pageSize) {
        this.page.pageSize = parseAtt.pageSize;
      }
    }
    this.initialDic();
    for (let i = 1; i < 32; i++) {
      let label = i < 10 ? "0" + i : "" + i;
      this.option.column.push({
        label,
        prop: label,
        hide: false,
        display: false,
        showColumn: false,
        minWidth: "35px",
        overHidden: true,
      });
    }
    this.option.column = this.option.column.concat(this.totalColumn);

    loadPrintTemplate(this.module).then((res) => {
      this.itemPrintTemplate = JSON.parse(res.data.data.content);
    });
  },
  mounted() {
    document.getElementById("att-report-bsc").oncontextmenu = function () {
      return false;
    };
    let yeBan = localStorage.getItem("attReport_yeBan");
    if (yeBan) {
      let parseYeBan = JSON.parse(yeBan);
      if (!parseYeBan.yeBan) {
        const yeBan = this.findObject(this.option.column, "yeBan");
        yeBan.hide = false;
      }
    }
    const column = this.findObject(this.option.column, "yeBan");
    if (column) {
      // 存储 夜班 显隐
      this.$watch(
        () => column.hide,
        (val) => {
          val
            ? localStorage.setItem(
              "attReport_yeBan",
              JSON.stringify({ yeBan: true })
            )
            : localStorage.setItem(
              "attReport_yeBan",
              JSON.stringify({ yeBan: false })
            );
        }
      );
    }

    //必须绑定全局进行监听，因为鼠标可能会在任何地方松开,页面销毁之后会删除该事件
    document.addEventListener("mouseup", this.commentMouseup);

    document.addEventListener("paste", this.commentPaste);
  },
  beforeDestroy() {
    document.removeEventListener("mouseup", this.commentMouseup);
    document.removeEventListener("paste", this.commentPaste);
  },
  data() {
    return {
      module: "ni_pa_attclock_attReport",
      itemPrintTemplate: null,
      exportData: [], //存储打印数据
      company: null, //公司
      title: null, //标题
      attDept: null, //考勤单位
      fullAttDays: null, //本月满勤
      randomKey: Math.random(),
      isOri: true,
      form: {},
      query: {},
      loading: false,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      data: [],
      option: {
        stripe: true,
        searchEnter: true,
        highlightCurrentRow: true,
        searchLabelWidth: 100,
        // 组件的尺寸
        size: "mini",
        // 搜索栏
        searchSize: "mini",
        searchIcon: true, // 搜索栏能否收缩\
        searchMenuSpan: 6,
        searchSpan: 6, // 单个搜索项所占宽度[每行24]
        searchIndex: 3, // 搜索按钮索引,超出的搜索栏收缩
        emptyBtn: true, // 搜索栏清空
        // 表格右上圆按钮
        // columnBtn: false, // 列显隐
        // 表格
        align: "center", // 表格对齐方式:left/center/right
        height: "auto", // 自适应窗口高度,配合calcHeight调节范围
        calcHeight: 30,
        rowKey: "userId",
        border: true, // 表格竖线
        // 操作栏
        menu: false,
        addBtn: false,
        column: [
          {
            label: "用户Id",
            prop: "userId",
            display: false,
            hide: true,
            width: "1px",
            showColumn: false,
          },
          {
            label: "姓名",
            prop: "name",
            type: "input",
            display: false,
            filters: true,
            // fixed: "left",
            width: "59px",
            showColumn: false,
          },
          {
            label: "部门",
            prop: "deptName",
            type: "input",
            display: false,
            width: "50px",
            overHidden: true,
            hide: true,
          },
          {
            label: "月份",
            prop: "month",
            type: "month",
            format: "yyyy-MM",
            valueFormat: "yyyy-MM",
            display: false,
            search: true,
            hide: true,
            searchValue: dateFormat(new Date(), "yyyy-MM"),
          },
          {
            label: "部门",
            prop: "deptId",
            type: "tree",
            dicData: [],
            props: {
              label: "title",
              value: "id",
            },
            multiple: true,
            checkStrictly: true,
            cascader: ["postId"],
            display: false,
            hide: true,
            search: true,
            searchMultiple: true,
            showColumn: false,
          },
          {
            label: "班组",
            prop: "teamId",
            type: "tree",
            dicData: [],
            props: {
              label: "title",
              value: "id",
            },
            multiple: true,
            checkStrictly: true,
            display: false,
            hide: true,
            search: false,
            searchMultiple: true,
            showColumn: false,
          },
          {
            label: "自定义班组",
            prop: "groupId",
            type: "select",
            dicData: [],
            props: {
              label: "name",
              value: "id",
            },
            multiple: true,
            checkStrictly: true,
            display: false,
            hide: true,
            search: true,
            searchMultiple: true,
            showColumn: false,
          },
          {
            label: "人员",
            prop: "personId",
            type: "select",
            dicData: [],
            props: {
              label: "account",
              value: "id",
            },
            filterable: true,
            display: false,
            multiple: true,
            hide: true,
            search: true,
            showColumn: false,
          },
          {
            label: "所属公司",
            prop: "company",
            type: "select",
            dicData: [],
            dataType: "number",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            display: false,
            hide: true,
            search: true,
            showColumn: false,
          },

          {
            label: "岗位",
            prop: "postId",
            type: "tree",
            multiple: true,
            dicUrl: "/api/blade-system/dept/post/select?deptIds={{key}}",
            props: {
              label: "postName",
              value: "id",
            },
            hide: true,
            search: true,
            showColumn: false,
          },
        ],
      },
      month: "",
      workDayCount: "",
      nonworkDayCount: "",
      printWorkDayCount:"",
      maxDayOfMonth: 31,
      weekArray: ["七", "一", "二", "三", "四", "五", "六"],
      nameMap: {},
      userArr: [],
      // 批量修改 结果
      modifyResultShow: false,
      modifyOptions: [],
      modifyLabel: null,
      modifyValue: null,
      // 批量修改 排班
      modifyPaiBanShow: false,
      modifyPaiBanList: [
        { value: 0, label: "长白班" },
        { value: 1, label: "白" },
        { value: 2, label: "夜" },
        { value: 3, label: "白倒夜" },
        { value: 4, label: "夜倒白" },
      ],
      modifyRadio: null,
      modifyPaiBan: null,
      // 假条标记
      checkTagShow: false,
      // 批量备注
      cmtOption: {
        emptyBtn: false,
        column: [
          {
            label: "用户",
            prop: "userIds",
            multiple: true,
            type: "tree",
            dicData: [],
            props: {
              label: "name",
              value: "userId",
            },
            checked: (checkedNodes, checkedKeys) => {
              let keys = checkedKeys.checkedKeys;
              this.userArr.forEach((item) => (item.style = null));
              if (keys && keys.length > 0) {
                keys.forEach((id) => {
                  let user = this.userArr.find((item) => item.userId === id);
                  user.style = "color: red";
                });
              }
            },
            rules: [{ required: true, message: "请选择修改用户" }],
            span: 24,
          },
          {
            label: "备注内容",
            prop: "attComment",
            type: "textarea",
            rules: [{ required: true, message: "请输入备注" }],
            minRows: 2,
            span: 24,
          },
        ],
      },
      monthColumn: [],
      totalColumn: [
        {
          label: "出勤",
          prop: "attDays",
          type: "number",
          controls: false,
          display: false,
          minWidth: "48px",
          overHidden: true,
          showColumn: false,
        },
        {
          label: "夜班",
          prop: "yeBan",
          type: "number",
          controls: false,
          display: false,
          minWidth: "48px",
          overHidden: true,
          hide: true,
        },
        {
          label: "请假",
          prop: "leave",
          type: "number",
          controls: false,
          display: false,
          minWidth: "48px",
          overHidden: true,
          showColumn: false,
        },
        {
          label: "公休",
          prop: "vacation",
          type: "number",
          controls: false,
          display: false,
          minWidth: "45px",
          overHidden: true,
          showColumn: false,
        },
        {
          label: "带薪",
          prop: "paidVacation",
          type: "number",
          controls: false,
          display: false,
          minWidth: "48px",
          overHidden: true,
          showColumn: false,
        },
        {
          label: "迟到/次",
          prop: "lateTimes",
          minWidth: "50px",
          overHidden: true,
          hide: true,
        },
        {
          label: "早退/次",
          prop: "earlyTimes",
          minWidth: "50px",
          overHidden: true,
          hide: true,
        },
        {
          label: "缺失上班卡/次",
          prop: "missStartTimes",
          minWidth: "50px",
          overHidden: true,
          hide: true,
        },
        {
          label: "缺失下班卡/次",
          prop: "missEndTimes",
          minWidth: "50px",
          overHidden: true,
          hide: true,
        },
        {
          label: "缺卡/次",
          prop: "missAllTimes",
          minWidth: "50px",
          overHidden: true,
          hide: true,
        },
        {
          label: "出勤天数",
          prop: "queryAttPay",
          searchLabelTip: "包含带薪和出勤天数",
          display: false,
          hide: true,
          minWidth: "1px",
          overHidden: true,
          showColumn: false,
          search: true,
        },
        {
          label: "备注",
          prop: "attComment",
          type: "input",
          minWidth: "33px",
          overHidden: false,
          search: true,
          hide: false,
          display: false,
        },
      ],
      repairColor: "rgba(255, 69, 0, 0.68)",
      attCommentMark: [],
      clicked: false,
      firstChckedIndex: 0,
      printDialogVisible: false,
    };
  },
  computed: {
    ...mapGetters(["permission", "userInfo", "colorName", "isCollapse"]),
    permissionList() {
      return {
        editBtn: this.vaildData(this.permission.attReport_edit, false),
        checkedBtn: this.vaildData(this.permission.attReport_checked, false),
        scyzzBtn: this.vaildData(this.permission.att_report_scyzz, false),
        yffz1Btn: this.vaildData(this.permission.att_report_yffz1, false),
        yffz2Btn: this.vaildData(this.permission.att_report_yffz2, false),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
    dayCol() {
      let dayCol = [];
      for (let i = 1; i <= this.maxDayOfMonth; i++) {
        dayCol.push(i < 10 ? "0" + i : "" + i);
      }
      return dayCol;
    },
    isAdminOrHR() {
      return (
        this.userInfo.role_name.includes("administrator") ||
        this.userInfo.role_name.includes("HR")
      );
    },
    firstCheckedAttComment() {
      return this.attCommentMark.findIndex((item) => item.focusClass);
    },
    lastCheckedAttComment() {
      return this.attCommentMark.reduce((acc, item, index) => {
        if (item.focusClass) {
          acc = index;
        }
        return acc;
      }, -1);
    },
  },
  watch: {
    "page.pageSize"(newVal, oldVal) {
      if (newVal !== oldVal) {
        localStorage.setItem(
          "attReport_pageSize",
          JSON.stringify({ pageSize: newVal })
        );
      }
    },

    isCollapse: {
      handler(val) {
        let attComment = this.findObject(this.option.column, "attComment");

        if (attComment == -1) {
          attComment = this.findObject(this.totalColumn, "attComment");
        }

        attComment.minWidth = val ? "213px" : "33px";
      },
      immediate: true,
    },
  },
  methods: {
    dayOfWeek(label) {
      return this.weekArray[`${new Date(this.month + "-" + label).getDay()}`];
    },
    cellStyle({ row, column }) {
      const cellStyle = {};
      cellStyle.borderColor = "black";
      if (this.permissionList.checkedBtn) {
        if ([0].includes(row[`${column.label}_check`])) {
          cellStyle.background = "#FEF0F0";
        }
      }
      let date = row.month + "-" + column.label;
      if (Number(column.label) > 0) {
        let dt = new Date(date);
        let number = dt.getDay();
        if (number === 0) {
          cellStyle.background = "#E4E7ED";
        }
      }
      if ("name" === column.columnKey && row.red) {
        cellStyle.color = "red";
      }
      cellStyle.border = "0.5px solid black";

      if ("name" === column.columnKey && row.level == "255") {
        cellStyle.backgroundColor = "#59A07B";
      }
      if (
        !isNaN(parseFloat(column.columnKey)) &&
        isFinite(column.columnKey) &&
        row[column.columnKey + "_repair"]
      ) {
        cellStyle.backgroundColor = this.repairColor;
      }
      if ("attComment" === column.columnKey) {
        cellStyle.padding = "0";
        cellStyle.position = "relative";
      }
      return cellStyle;
    },
    cellBtnStyle(statusCode) {
      let baseStyle = "margin: 0;border: 0;padding: 0;width: 100%;";
      if (0 === statusCode) {
        return baseStyle + "color: #167C46;";
      }
      if (1 === statusCode) {
        return baseStyle + "color: #53A8FF;font-weight: bolder;";
      }
      if (2 === statusCode) {
        return baseStyle + "color: #E6A23C;font-weight: bolder;";
      }
      if (3 === statusCode) {
        return baseStyle + "color: red;font-weight: bolder;";
      }
      if (4 === statusCode) {
        return baseStyle + "color: gray;";
      }
      return baseStyle + "color: #fff;";
    },
    initialDic() {
      const teamIdColumn = this.findObject(this.option.column, "teamId");
      this.$http
        .get("/api/ni/pa/attendance/dept/tree?deptCategory=4")
        .then((res) => {
          if (res.data.data.length > 0) {
            teamIdColumn.search = true;
            teamIdColumn.dicData = res.data.data;
          } else {
            teamIdColumn.search = false;
          }
        });
      this.$http
        .get("/api/ni/pa/attendance/dept/tree?deptCategory=2,3")
        .then((res) => {
          const column = this.findObject(this.option.column, "deptId");
          column.dicData = res.data.data;
        });
      this.$http
        .get(
          "/api/blade-system/dict-biz/dictionary?code=ni_pa_attendance_att_msg"
        )
        .then((res) => {
          const { data } = res.data;
          this.modifyOptions = this.list2Tree(data);
          const attMsg = this.findObject(
            this.modifyResultSelect.option.column,
            "attMsg"
          );
          attMsg.dicData = this.modifyOptions;
          console.log(this.modifyOptions);
        });
      this.$http.get("/api/ni/pa/attendance/select/users").then((res) => {
        const column = this.findObject(this.option.column, "personId");
        column.dicData = res.data.data;
      });
      this.$http
        .get("/api/blade-system/dict/dictionary?code=natergy_company")
        .then((res) => {
          const column = this.findObject(this.option.column, "company");
          column.dicData = res.data.data;
        });
      this.$http.get("/api/ni/pa/group/list?ascs=['sn']").then((res) => {
        const groupId = this.findObject(this.option.column, "groupId");
        if (res.data.data) {
          groupId.search = true;
          groupId.dicData = res.data.data;
        } else {
          groupId.search = false;
        }
      });
    },
    storeMonth() {
      const nowMonth = dateFormat(new Date(), "yyyy-MM");
      let msg = "提交后数据将不能修改，是否继续?";
      if (nowMonth === this.month) {
        msg = "本月的考勤还未结束，保存后将不能对考勤进行修改，是否继续?";
      }
      this.$confirm(msg, {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        const q = { ...this.query };
        // if (this.query.deptId && Array.isArray(this.query.deptId)) {
        //   q.deptId = this.query.deptId.join(",");
        // }
        // if (this.query.personId && Array.isArray(this.query.personId)) {
        //   q.personId = this.query.personId.join(",");
        // }
        // if (this.query.postId && Array.isArray(this.query.postId)) {
        //   q.postId = this.query.postId.join(",");
        // }
        store(q).then(() => {
          this.onLoad(this.page);
          this.$message.success("保存成功");
        });
      });
    },
    // handlePrint() {
    //   NProgress.start();
    //   let url = "/api/ni/pa/attendance/print";
    //   exportBlob(url, {
    //     ...this.query,
    //   }).then((res) => {
    //     if (res.data.type === "application/pdf") {
    //       let data = new Blob([res.data], {
    //         type: "application/pdf;charset=utf-8",
    //       });
    //       let pdfUrl = window.URL.createObjectURL(data);
    //       printJS({ printable: pdfUrl, type: "pdf" });
    //     } else {
    //       this.$message.warning("请保存后, 再继续打印或导出");
    //     }
    //     NProgress.done();
    //   });
    // },
    //获取搜索的打印数据
    async getExportData() {
      //每次获取必须初始化一下，不然会把之前缓存额数据一并带入
      this.exportData = [];
      const promises = [];
      // for (var i = 1; i <= this.page.total / this.page.pageSize + 1; i++) {
      const query = {
        ...this.params,
        ...this.query,
      };
      if (query.teamId) {
        if (!query.deptId) {
          query.deptId = [];
        }
        query.deptId = query.deptId.concat(query.teamId);
      }
      const promise = reportPage(1, 10000, query).then((res) => {
        const data = res.data.data.records;
        this.exportData = this.exportData.concat(data);
        this.company = this.query.company != null ? this.query.$company : "";
        this.title = res.data.data.extendAttribute.finalMonth + "  考勤表";
        this.fullAttDays = "本月满勤" + this.printWorkDayCount + "天";
        // this.fullAttDays =
        //   "本月满勤" + res.data.data.extendAttribute.work + "天";
      });

      promises.push(promise);
      // }

      // 等待所有异步请求完成
      await Promise.all(promises);
      return this.exportData;
    },
    handlePrint() {
      this.printDialogVisible = true;
    },

    // handlePrint() {
    //   this.$confirm("是否打印当前筛选的所有数据？", {
    //     confirmButtonText: "确定",
    //     cancelButtonText: "取消",
    //     type: "warning",
    //   }).then(() => {
    //     this.handlePrintData();
    //   });
    // },
    //数据打印
    async handlePrintData() {
      let printData;
      let hiprintTemplate;

      this.printDialogVisible =false;

      if (!this.itemPrintTemplate) {
        this.$message.error("打印模板加载失败，请联系管理员");
        return;
      }
      //获取打印数据
      const data = await this.getExportData();
      printData = {
        title: this.title,
        company: this.company,
        fullAttDays: this.fullAttDays,
        items: data,
      };

      hiprintTemplate = new hiprint.PrintTemplate({
        template: this.itemPrintTemplate,
      });
      hiprintTemplate.print(printData);

      this.title = null;
      this.company = null;
      this.fullAttDays = null;
    },
    handleExport() {
      NProgress.start();
      let url = "/api/ni/pa/attendance/export";
      const query = {
        ...this.params,
        ...this.query,
      };
      if (query.teamId) {
        if (!query.deptId) {
          query.deptId = [];
        }
        query.deptId = query.deptId.concat(query.teamId);
      }
      exportBlob(url, Object.assign(query)).then((res) => {
        if (res.data.type === "application/vnd.ms-excel") {
          downloadXls(res.data, "考勤报表.xlsx");
        } else {
          this.$message.warning("请保存后, 再继续打印或导出");
        }
        NProgress.done();
      });
    },
    beforeOpen(done, type) {
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then((res) => {
          this.form = res.data.data;
        });
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.reloadReset();
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      if (params.month) {
        const month = params.month.substring(5);
        if ([("01", "03", "05", "07", "08", "10", "12")].includes(month)) {
          this.randomKey = Math.random();
        }
      }
      //this.query = params;
      this.page.currentPage = 1;
      this.reloadReset();
      this.onLoad(this.page, this.query);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      document.body.click();
      this.loading = true;
      const query = {
        ...params,
        ...this.query,
      };
      if (query.teamId) {
        if (!query.deptId) {
          query.deptId = [];
        }
        query.deptId = query.deptId.concat(query.teamId);
      }
      reportPage(page.currentPage, page.pageSize, query).then((res) => {
        const data = res.data.data;
        const extend = data.extendAttribute;
        this.page.total = data.total;
        this.data = data.records;
        this.attCommentMark = this.data.map((item) => {
          return {
            attComment: item.attComment,
            readonly: true,
            focusClass: false,
            userId: item.userId,
          };
        });
        this.workDayCount = Number(extend.work);
        this.nonworkDayCount = Number(extend.nonWork);
        this.printWorkDayCount = this.workDayCount; //默认打印天数是实际出勤天数
        if (this.month !== extend.finalMonth) {
          this.month = extend.finalMonth;
        }
        // 搜索栏 回显 查询月份
        if (
          this.$refs.crud.$refs.headerSearch.searchForm.month !==
          extend.finalMonth
        ) {
          this.$set(
            this.$refs.crud.$refs.headerSearch.searchForm,
            "month",
            extend.finalMonth
          );
        }
        this.renderByChangeMonth(this.maxDayOfMonth, extend.total);
        this.$nextTick(() => {
          this.maxDayOfMonth = extend.total;
        });
        let nameMap = {};
        let userArr = [];
        this.data.forEach((item) => {
          nameMap[`${item.userId}`] = {};
          this.dayCol.forEach((day) => {
            if (day <= this.maxDayOfMonth) {
              nameMap[`${item.userId}`][`${day}`] = false;
              let tag = item[`${day}_check`];
              if (tag) {
                nameMap[`${item.userId}`][`${day}_oriCheck`] = true;
                nameMap[`${item.userId}`][`${day}_check`] = true;
              } else {
                nameMap[`${item.userId}`][`${day}_oriCheck`] = false;
                nameMap[`${item.userId}`][`${day}_check`] = false;
              }
              if ([0, 1].includes(item[`${day}_check`])) {
                nameMap[`${item.userId}`][`${day}_checkable`] = true;
              }
            }
          });
          userArr.push({ name: item.name, userId: item.userId, style: null });
        });
        this.nameMap = nameMap;
        this.userArr = userArr;
        const userIds = this.findObject(this.cmtOption.column, "userIds");
        userIds.dicData = userArr;
        this.loading = false;
        this.selectionClear();
      });
    },
    renderByChangeMonth(oldDayCount, newDayCount) {
      if (oldDayCount > newDayCount) {
        this.dynamicHide(newDayCount, oldDayCount, true);
      } else {
        this.dynamicHide(oldDayCount, newDayCount, false);
      }
    },
    dynamicHide(small, big, hide) {
      let temp = small + 1;
      while (temp <= big) {
        const col = this.findObject(this.option.column, "" + temp);
        col.hide = hide;
        temp++;
      }
      const attComment = this.findObject(this.option.column, "attComment");
      attComment.hide = !attComment.hide;
      const attDays = this.findObject(this.option.column, "attDays");
      attDays.hide = !attDays.hide;
      const yeBan = this.findObject(this.option.column, "yeBan");
      yeBan.hide = !yeBan.hide;
      const leave = this.findObject(this.option.column, "leave");
      leave.hide = !leave.hide;
      const vacation = this.findObject(this.option.column, "vacation");
      vacation.hide = !vacation.hide;
      const paidVacation = this.findObject(this.option.column, "paidVacation");
      paidVacation.hide = !paidVacation.hide;

      this.$nextTick(() => {
        attComment.hide = !attComment.hide;
        attDays.hide = !attDays.hide;
        yeBan.hide = !yeBan.hide;
        leave.hide = !leave.hide;
        vacation.hide = !vacation.hide;
        paidVacation.hide = !paidVacation.hide;
      });
    },
    handleItemBox(row, col) {
      if (col) {
        const day = col;
        let id = row[day + "_id"];
        if (id) {
          this.$refs.itemForm.showItemFrom(
            row.level !== 255 || this.isAdminOrHR,
            this.month + "-" + day + " 00:00:00",
            id
          );
        } else {
          this.$refs.itemForm.showItemFromWithoutId(
            row.level !== 255 || this.isAdminOrHR,
            this.month + "-" + day + " 00:00:00",
            row.userId
          );
        }
      }
    },
    handelHideItem(refresh) {
      if (refresh) {
        this.onLoad(this.page);
      }
    },
    rowClassName() {
      return "att_report_row";
    },
    handleModifyResultShow() {
      this.modifyResultShow = true;
    },
    handleModifyPaiBanShow() {
      this.modifyPaiBanShow = true;
    },
    handleCheckTagShow() {
      this.checkTagShow = true;
    },
    handleModifyShow() {
      this.handleModifyResultShow();
      this.handleModifyPaiBanShow();
    },
    changeModifyResult(item) {
      this.modifyValue = item.dictKey;
      this.modifyLabel = item.dictValue;
    },
    changeModifyPaiBan(item) {
      this.modifyRadio = item.value;
      this.modifyPaiBan = item.label;
    },
    modifyCancel() {
      this.modifyResultShow = false;
      this.modifyPaiBanShow = false;
      this.resetCheck();
    },
    handleCheckTagHide() {
      this.checkTagShow = false;
      let names = Object.keys(this.nameMap);
      names.forEach((name) => {
        this.dayCol.forEach((day) => {
          if (day <= this.maxDayOfMonth) {
            if (this.nameMap[`${name}`][`${day}_checkable`]) {
              this.nameMap[`${name}`][`${day}_check`] =
                this.nameMap[`${name}`][`${day}_oriCheck`];
            }
          }
        });
      });
    },
    modifySubmit() {
      let errorMessage = "";
      if (this.modifyResultShow && !this.modifyValue) {
        errorMessage += " 结果";
      }
      if (
        this.modifyPaiBanShow &&
        ![0, 1, 2, 3, 4].includes(this.modifyRadio)
      ) {
        errorMessage += " 排班";
      }
      if (errorMessage) {
        this.$message({
          type: "error",
          message: "请选择" + errorMessage,
        });
      } else {
        let names = Object.keys(this.nameMap);
        let checkMap = {};
        names.forEach((name) => {
          let labels = Object.keys(this.nameMap[name]);
          checkMap[name] = [];
          labels.forEach((label) => {
            if (this.nameMap[name][label]) {
              checkMap[name].push(label);
            }
          });
        });
        let params = {
          checkMap,
          resultKey: this.modifyValue,
          resultPaiBan: this.modifyRadio,
        };
        multipleModify(params, this.month).then(() => {
          this.$message({
            type: "success",
            message: "修改成功",
          });
          this.onLoad(this.page);
        });
        this.modifyCancel();
      }
    },
    submitCheckTag() {
      this.checkTagShow = false;
      let names = Object.keys(this.nameMap);
      let checkMap = {};
      let uncheckMap = {};
      names.forEach((name) => {
        checkMap[name] = [];
        uncheckMap[name] = [];
        this.dayCol.forEach((day) => {
          if (day <= this.maxDayOfMonth) {
            if (this.nameMap[`${name}`][`${day}_checkable`]) {
              if (
                this.nameMap[`${name}`][`${day}_check`] !==
                this.nameMap[`${name}`][`${day}_oriCheck`]
              ) {
                this.nameMap[`${name}`][`${day}_check`]
                  ? checkMap[name].push(day)
                  : uncheckMap[name].push(day);
              }
            } else {
              if (this.nameMap[`${name}`][`${day}_check`]) {
                uncheckMap[name].push(day);
              }
            }
          }
        });
      });
      let params = {
        checkMap,
        uncheckMap,
      };
      multipleCheck(params, this.month).then(() => {
        this.$message({
          type: "success",
          message: "修改成功",
        });
        this.onLoad(this.page);
      });
      this.modifyCancel();
    },
    resetCheck() {
      let names = Object.keys(this.nameMap);
      names.forEach((name) => {
        let labels = Object.keys(this.nameMap[name]);
        labels.forEach((label) => (this.nameMap[name][label] = false));
      });
      this.modifyValue = null;
      this.modifyLabel = null;
      this.modifyRadio = null;
      this.modifyPaiBan = null;
    },
    cellMouseClick(row, column) {
      if (this.modifyResultShow || this.modifyPaiBanShow) {
        if (row.level === 255 && !this.isAdminOrHR) {
          this.$message.warning("该结果已保存, 请勿直接修改");
        } else {
          if (this.nameMap[`${row.userId}`][`${column.label}`]) {
            this.nameMap[`${row.userId}`][`${column.label}`] = false;
          } else {
            this.nameMap[`${row.userId}`][`${column.label}`] = true;
          }
        }
      }
      if (
        this.checkTagShow &&
        this.nameMap[`${row.userId}`][`${column.label}_checkable`]
      ) {
        if (this.nameMap[`${row.userId}`][`${column.label}_check`]) {
          this.nameMap[`${row.userId}`][`${column.label}_check`] = false;
        } else {
          this.nameMap[`${row.userId}`][`${column.label}_check`] = true;
        }
      }
    },
    cellMouseEnter(row, column, cell, event) {
      if (this.modifyResultShow || this.modifyPaiBanShow) {
        if (Number(column.label) > 0 && event.buttons === 1) {
          if (row.level === 255 && !this.isAdminOrHR) {
            this.$message.warning("该结果已保存, 请勿直接修改");
          } else {
            if (this.nameMap[`${row.userId}`][`${column.label}`]) {
              this.nameMap[`${row.userId}`][`${column.label}`] = false;
            } else {
              this.nameMap[`${row.userId}`][`${column.label}`] = true;
            }
          }
        }
      }
    },
    reloadReset() {
      this.modifyCancel();
    },
    multiCmtForm(month, ids, attComment) {
      this.$DialogForm.show({
        title: "修改备注",
        width: "600px",
        data: { userIds: ids, attComment },
        menuPosition: "right",
        option: this.cmtOption,
        callback: (res) => {
          const data = res.data;
          multiComment(data.userIds.join(","), month, data.attComment).then(
            () => {
              this.$message.success("操作成功");
              data.userIds.forEach((id) => {
                let row = this.data.find((item) => item.userId === id);
                row.attComment = data.attComment;
                let user = this.userArr.find((item) => item.userId === id);
                user.style = null;
              });
              res.done();
              res.close();
            }
          );
        },
      });
    },
    list2Tree(data) {
      const obj = {};
      data.forEach((item) => {
        obj[item.id] = item;
      });
      const parentList = [];
      data.forEach((item) => {
        const parent = obj[item.parentId];
        if (parent) {
          parent.children = parent.children || [];
          parent.children.push(item);
        } else {
          parentList.push(item);
        }
      });
      return parentList;
    },
    /**
     * 双击让输入框可以输入
     */
    chooseInput(index) {
      this.attCommentMark[index].readonly = false;
    },
    /**
     * 鼠标按下时，标记这个cell的索引，并且锁定这cell，取消其他的cell选中
     */
    commentMousedown(index) {
      this.attCommentMark.forEach((item) => (item.focusClass = false));
      this.attCommentMark[index].focusClass = true;
      this.clicked = true;
      this.firstChckedIndex = index;
    },
    /**
     * 鼠标松开时，鼠标划过不再标记
     */
    commentMouseup() {
      this.clicked = false;
    },
    /**
     * 鼠标划过进行标记选中
     */
    commentMouseenter(index) {
      if (!this.clicked) {
        return;
      }
      this.attCommentMark.forEach((item, itemIndex) => {
        //两种情况，一种是firstChckedIndex比index小
        if (index < this.firstChckedIndex) {
          if (itemIndex <= this.firstChckedIndex && itemIndex >= index) {
            item.focusClass = true;
          } else {
            item.focusClass = false;
          }
          //另一种情况是firstChckedIndex比index大
        } else {
          if (itemIndex >= this.firstChckedIndex && itemIndex <= index) {
            item.focusClass = true;
          } else {
            item.focusClass = false;
          }
        }
      });
    },
    /**
     * 对选中行进行粘贴值
     */
    commentPaste(event) {
      event.preventDefault();
      const clipboardData = event.clipboardData || window.clipboardData;
      const pastedText = clipboardData.getData("text");
      if (pastedText.length > 70) {
        this.$message.error("复制内容不能超过70字");
        return;
      }
      this.attCommentMark.forEach((item) => {
        if (item.focusClass) {
          item.attComment = pastedText;
        }
      });

      const userIds = this.attCommentMark
        .filter((item) => item.focusClass)
        .map((item) => item.userId)
        .join();

      setTimeout(() => multiComment(userIds, this.month, pastedText), 0);
    },
    /**
     * 输入框change事件，保存修改内容
     */
    inputCommentMark(attCommentItem) {
      //用异步任务，来保证主线程，切换到其他输入框时不卡顿
      setTimeout(
        () =>
          multiComment(
            attCommentItem.userId,
            this.month,
            attCommentItem.attComment
          ),
        0
      );
    },
  },
};
</script>

<style lang="scss" scoped>

.print-input {
  display: flex;
  flex-direction: row;
  margin-top: 20px;
  .p{
    width: 100%;
  }
  .el-input{
    margin-top: 10px;
    margin-left: 10px;
    width: 30%;
  }
}

.el-table__row.att_report_row .el-table__cell .cell.el-tooltip {
  margin: 0;
  border: 0;
  padding: 0;
  width: 100%;
  min-width: 40px;

  .el-button {
    width: 100%;
  }
}

/deep/ .el-table .cell {
  padding: 0;
}

.att-comment {
  cursor: pointer;
  position: absolute;
  left: -1px;
  top: -1px;
  z-index: 2;
  height: 44px;
  width: calc(100% + 3px);

  input {
    width: 100%;
    height: 100%;
    border: none;
    background-color: transparent;
    cursor: pointer;
  }
}

.att-comment-checked {
  border-left: solid 4px green;
  border-right: solid 4px green;
  background-color: rgba(0, 0, 0, 0.1);
}

.first-checked {
  border-top: solid 4px green;
  border-top-left-radius: 3px;
  border-top-right-radius: 3px;
}

.last-checked {
  border-bottom: solid 4px green;
  border-bottom-left-radius: 3px;
  border-bottom-right-radius: 3px;
}

/deep/ table {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
</style>

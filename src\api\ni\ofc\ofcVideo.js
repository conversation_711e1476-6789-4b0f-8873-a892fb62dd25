import request from "@/router/axios";
// 获取视频列表
export const getOfcVideoPage = (current, size, params) => {
    return request({
      url: '/api/ni/ofc/video/page',
      method: 'GET',
      params:{
        ...params,
        current,
        size,
    },
    });
  };
  
  // 保存视频或文件夹
  export const ofcVideoSave = (data) => {
    return request({
      url: '/api/ni/ofc/video/save',
      method: 'POST',
      data,
    });
  };
  
  // 视频重命名
  export const ofcVideoRename = (id,name) => {
    return request({
      url: '/api/ni/ofc/video/videoRename',
      method: 'POST',
      params:{
         id,
         name,
      },
    });
  };
  
  // 删除
  export const ofcVideoDelete = (id) => {
    return request({
      url: '/api/ni/ofc/video/delete',
      method: 'POST',
      params:{
         id
      },
    });
  };
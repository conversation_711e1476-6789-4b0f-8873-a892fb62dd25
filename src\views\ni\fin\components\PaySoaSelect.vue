<template>
  <div>
    <el-input
      v-model="name"
      :size="size"
      suffix-icon="el-icon-search"
      :disabled="disabled"
      readonly
      @click.native="handleSelect"
    />
    <pay-soa-select-dialog
      ref="paySoaSelectDialogRef"
      :multiple="multiple"
      v-model="value"
      :params="params"
      @confirm="handlePaySoaConfirm"
    />
  </div>
</template>

<script>
import PaySoaSelectDialog from "@/views/ni/fin/components/PaySoaSelectDialog";
import { getDetail } from "@/api/ni/fin/pay-soa";
import Emitter from "element-ui/src/mixins/emitter";

export default {
  name: "PaySoaSelect",
  mixins: [Emitter],
  components: {
    PaySoaSelectDialog,
  },
  props: {
    value: [String, Number],
    multiple: {
      type: Boolean,
      default: false,
    },
    placeholder: String,
    size: {
      type: String,
      default: "mini",
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    readonly: {
      type: <PERSON>olean,
      default: false,
    },
    params: {
      type: Object,
      default: () => {},
    },
    beforeSelect: {
      type: Function,
    },
    lazy: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      name: "",
    };
  },
  watch: {
    value: {
      handler(val) {
        if (val) {
          const name = [];
          const checks = (val + "").split(",");
          const asyncList = [];
          checks.forEach((c) => {
            asyncList.push(getDetail(c));
          });
          Promise.all(asyncList).then((res) => {
            const lst = [];
            res.forEach((r) => {
              const data = r.data.data;
              if (data) {
                name.push(data.serialNo);
                lst.push(data);
              }
            });
            this.$set(this, "name", name.join(","));
            if (!this.lazy) this.$emit("submit", lst);
          });
        } else this.$set(this, "name", "");
      },
      immediate: true,
    },
  },
  methods: {
    handleSelect() {
      console.log("1");
      if (this.readonly || this.disabled) {
        return;
      } else {
        console.log("2");
        if (this.beforeSelect && typeof this.beforeSelect == "function") {
          this.beforeSelect(
            () => (this.$refs.paySoaSelectDialogRef.visible = true)
          );
          return;
        }
        this.$refs.paySoaSelectDialogRef.visible = true;
      }
    },
    handlePaySoaConfirm(selectList) {
      const ids = selectList.map((item) => item.id).join(",");
      this.$emit("input", ids);
      this.$emit("submit", selectList);
      this.$nextTick(() => {
        this.dispatch("ElFormItem", "el.form.blur", [ids]);
      });
    },
  },
};
</script>

<style scoped></style>

import request from "@/router/axios";

export const getList = (current, size, params) => {
  return request({
    url: "/api/ni/chat/info/list",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/ni/chat/info/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const remove = (ids) => {
  return request({
    url: "/api/ni/chat/info/remove",
    method: "post",
    params: {
      ids,
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/ni/chat/info/submit",
    method: "post",
    data: row,
  });
};

export const update = (row) => {
  return request({
    url: "/api/ni/chat/info/submit",
    method: "post",
    data: row,
  });
};

export const publish = (ids) => {
  return request({
    url: "/api/ni/chat/info/publish",
    method: "post",
    params: {
      ids,
    },
  });
};
export const offLine = (ids) => {
  return request({
    url: "/api/ni/chat/info/offLine",
    method: "post",
    params: {
      ids,
    },
  });
};
export const getChatRole = (roleIds) => {
  return request({
    url: "/api/ni/chat/info/role-keys",
    method: "get",
    params: {
      roleIds,
    },
  });
};

export const recordVisits = (id) => {
  return request({
    url: "/api/ni/chat/info/recordVisits",
    method: "post",
    params: {
      id,
    },
  });
};


export const recommend = (id) => {
  return request({
    url: "/api/ni/chat/info/recommend",
    method: "post",
    params: {
      id,
    },
  });
}
export const unRecommend = (id) => {
  return request({
    url: "/api/ni/chat/info/unRecommend",
    method: "post",
    params: {
      id,
    },
  });
}

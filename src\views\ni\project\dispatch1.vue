<template>
  <basic-container>
    <el-tabs v-model="dispatchStatus" @tab-click="handleDispatchStatusChange">
      <el-tab-pane label="全部" name="all"></el-tab-pane>
      <el-tab-pane label="未派工" name="0"></el-tab-pane>
      <el-tab-pane label="已派工" name="1"></el-tab-pane>
      <el-tab-pane label="已完工" name="9"></el-tab-pane>
      <el-tab-pane disabled>
        <template #label> 人员状态:--/--;请假:--;年假:--</template>
      </el-tab-pane>
    </el-tabs>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      v-model="form"
      ref="crud"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
      @cell-click="cellClick"
      :cell-style="cellStyle"
    >
      <template #projectSerialNo="{ row, index }">
        <span>{{ row.projectSerialNo }}</span>
        <span v-if="row.employCount > 0">({{ row.employCount }})</span>
      </template>
      <template #employSerialNo="{ row, index }">
        <flow-timeline-popover
          v-if="row.employProcessInsId"
          :process-ins-id="row.employProcessInsId"
          :process-def-key="processDefKey"
          v-model="row.employSerialNo"
          :flow.sync="row.employFlow"
          trigger="click"
          lazy
        />
        <span v-else>{{ row.employSerialNo }}</span>
      </template>
      <template #employUserName="{ row, index }">
        <span
          v-if="row.employUserName"
          :style="{
            cursor: 'pointer',
            textDecoration: 'underline',
          }"
          @click="rowDispatchDetail(row)"
        >
          {{ row.employUserName }}
        </span>
      </template>
      <template #dispatchedPersonNum="{ row, index }">
        <span v-if="row.dispatchedPersonNum && row.dispatchedPersonNum != 0">
          {{ row.dispatchedPersonNum }}
        </span>
        <span
          :style="{ color: '#F56C6C' }"
          v-if="row.unDispatchPersonNum && row.unDispatchPersonNum != 0"
        >
          <template
            v-if="row.dispatchedPersonNum && row.dispatchedPersonNum != 0"
          >
            +
          </template>
          {{ row.unDispatchPersonNum }}
        </span>
      </template>
      <template #workedHours="{ row, index }">
        <span v-if="row.dispatchedHours && row.dispatchedHours != 0">
          {{ row.dispatchedHours }}
        </span>
        <span
          :style="{ color: '#F56C6C' }"
          v-if="row.workedHours && row.workedHours !== 0"
        >
          <template v-if="row.dispatchedHours && row.dispatchedHours != 0">
            +
          </template>
          {{ row.workedHours }}
        </span>
      </template>
      <template #assignedToName="{ row, index }">
        <template
          v-if="row.id && row.status === 9 && row.items && row.items.length > 0"
        >
          <span v-for="(item, index) in row.items" :key="index"
          >{{ index > 0 ? "," : "" }}{{ item.personName }}(<span
            style="color: #f56c6c"
          >{{ Number(item.finishHours) }}H</span
          >)</span
          >
        </template>
        <span
          v-else
          :style="
            !row.status || row.status !== 9
              ? { cursor: 'pointer', textDecoration: 'underline' }
              : {}
          "
          @click="rowAssignedToChange(row, index)"
        >
          {{ row.assignedToName ? row.assignedToName : "未指派" }}
        </span>
      </template>
      <template #workingHours="{ row, index }">
        <span
          v-if="row.workingHours"
          :style="
            !row.status || row.status !== 9
              ? { cursor: 'pointer', textDecoration: 'underline' }
              : {}
          "
          @click="rowWorkingHoursChange(row, index)"
        >
          {{ row.workingHours }}
        </span>
      </template>
      <template #employWorkDate="{ row, index }">
        <span>{{ row.employStartDate }}</span>
        <span
          v-if="row.employEndDate && row.employEndDate !== row.employStartDate"
        >
          ~{{ row.employEndDate }}
        </span>
      </template>
      <template #communication="{ row, index }">
        <el-tag
          v-if="row.communication"
          size="mini"
          type="danger"
          effect="dark"
        >
          是
        </el-tag>
        <el-tag v-else size="mini" type="info" effect="plain">否</el-tag>
      </template>
      <template #picking="{ row, index }">
        <el-tag v-if="row.picking" size="mini" type="danger" effect="dark">
          是
        </el-tag>
        <el-tag v-else size="mini" type="info" effect="plain">否</el-tag>
      </template>
      <template #drawing="{ row, index }">
        <el-tag v-if="row.drawing" size="mini" type="danger" effect="dark">
          是
        </el-tag>
        <el-tag v-else size="mini" type="info" effect="plain">否</el-tag>
      </template>
      <template #assignedToForm="{ disabled, size, index, row }">
        <user-select
          v-model="form.assignedTo"
          :size="size"
          multiple
          :disabled="disabled"
        ></user-select>
      </template>
      <template #person="{ row, index }">
        <el-tag v-if="row.person" size="mini" type="danger" effect="dark">
          是
        </el-tag>
        <el-tag v-else size="mini" type="info" effect="plain">否</el-tag>
      </template>
      <template #appointUserName="{ row, index }">
        <span v-if="row.person">{{ row.appointUserName }}</span>
      </template>
      <template #opPost="{ row, index }">
        <span v-if="!row.person">{{ row.$opPost }}</span>
      </template>
      <template #employStatus="{ row, index }">
        <el-tag
          v-if="row.employStatus === 23"
          size="mini"
          type="warning"
          effect="plain"
        >
          {{ row.$employStatus }}
        </el-tag>
        <el-tag
          v-else-if="row.employStatus === 24"
          size="mini"
          type="warning"
          effect="dark"
        >
          {{ row.$employStatus }}
        </el-tag>
        <el-tag v-else size="mini" type="success" effect="plain">
          {{ row.$employStatus }}
        </el-tag>
      </template>
      <template #dispatchDate="{ row, size, index }">
        <el-tag v-if="row.dispatchDate" :size="size" type="danger" effect="dark"
        >{{ row.dispatchDate }}
        </el-tag>
      </template>
      <template #applyWorkingHours="{ row, index }">
        <span v-if="row.applyWorkingHours">{{
            row.applyWorkingHours + "H"
          }}</span>
      </template>
      <template #opType="{ row, index }">
        <el-tag v-if="row.opType === '1'" size="mini" type="danger"
        >{{ row.$opType }}
        </el-tag>
        <el-tag v-else-if="row.opType === '2'" size="mini" type="warning"
        >{{ row.$opType }}
        </el-tag>
        <el-tag v-else-if="row.opType === '3'" size="mini"
        >{{ row.$opType }}
        </el-tag>
        <el-tag v-else size="mini" type="info" effect="plain"
        >{{ row.$opType }}
        </el-tag>
      </template>
      <template #menuLeft>
        <div style="display: flex; align-items: center">
          <el-date-picker
            size="mini"
            v-model="dispatchDate"
            type="date"
            placeholder="选择日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            @change="handleDateChange"
            clearable
          >
          </el-date-picker>
          <el-button
            style="margin-left: 5px"
            type="primary"
            size="mini"
            icon="el-icon-s-promotion"
            @click="handleDispatch"
          >开始派工
          </el-button>
          <el-button
            style="margin-left: 5px"
            type="primary"
            size="mini"
            plain
            icon="el-icon-time"
            @click="handleReferenceDispatch"
          >参照上次派工
          </el-button>
          <el-button
            type="warning"
            size="mini"
            icon="el-icon-s-promotion"
            @click="handleCancel"
          >取消派工
          </el-button>
          <el-button
            type="success"
            size="mini"
            icon="el-icon-s-check"
            @click="handleDispatchFinish"
          >完成派工
          </el-button>
          <el-button
            type="danger"
            size="mini"
            icon="el-icon-s-check"
            @click="handleEmployOverrule"
          >驳回申请
          </el-button>
          <!--          <el-button-->
          <!--            type="warning"-->
          <!--            size="mini"-->
          <!--            icon="el-icon-s-check"-->
          <!--            @click="handleExport"-->
          <!--          >导出-->
          <!--          </el-button>-->
          <el-button
            type="danger"
            size="mini"
            icon="el-icon-s-check"
            @click="handleExportPlus"
          >派工导出
          </el-button>

          <!--              <el-checkbox v-model="unDispatch">未派工</el-checkbox>-->
          <el-divider direction="vertical" />
          <!--          <el-checkbox v-model="finish" @change="handleFinishChange">已完工</el-checkbox>-->
        </div>
      </template>
      <template #menuRight>
        <el-radio-group v-model="opType" size="mini">
          <el-radio-button label="all">全部</el-radio-button>
          <el-radio-button
            v-for="(item, index) in opTypeDict"
            :key="index"
            :label="item.dictKey"
          >{{ item.dictValue }}
          </el-radio-button>
        </el-radio-group>
        <el-divider direction="vertical"></el-divider>
        <el-button
          :icon="personShow ? 'el-icon-user-solid' : 'el-icon-user'"
          circle
          size="mini"
          @click="handlePersonShow"
        ></el-button>
      </template>
      <template #menu="{ row, index, size }">
        <el-button
          type="text"
          size="mini"
          icon="el-icon-info"
          @click="rowDispatch(row)"
        >派工明细
        </el-button>
      </template>
    </avue-crud>

    <attach-dialog ref="attachDialogRef" />
    <dispatch-item-drawer ref="dispatchItemDrawerRef" />
    <dispatch-person-select
      ref="dispatchPersonSelectRef"
      @confirm="rowAssignedToChangeSubmit"
    />
    <el-dialog
      :visible.sync="workingHours.visible"
      title="指派工时"
      width="400px"
      append-to-body
    >
      <avue-form
        ref="form"
        :option="workingHours.options"
        v-model="workingHours.form"
        @submit="handleWorkingHoursSubmit"
      />
    </el-dialog>
    <dispatch-finish-dialog
      ref="dispatchFinishDialogRef"
      @submit="handleDispatchFinishSubmit"
    />
    <employ-apply-list-dialog ref="employApplyListDialogRef" />
  </basic-container>
</template>

<script>
import {
  assign,
  assignHours,
  changeRemark,
  getList,
  publish,
  publishCancel,
  referenceDispatch,
} from "@/api/ni/project/dispatch";
import {
  finish as finishEmploy,
  overrule as overruleEmploy,
  getPageWithDispatch as getPage,
} from "@/api/ni/project/employ";
import { mapGetters } from "vuex";
import ProjectSelect from "@/views/ni/project/components/MyProjectSelect";
import UserSelect from "@/components/user-select";
import AttachDialog from "@/components/attach-dialog";
import { dateFormat } from "@/util/date";
import exForm from "@/views/plugin/workflow/mixins/ex-form";
import DispatchPersonSelect from "@/views/ni/project/components/DispatchPersonSelect";
import DispatchItem from "@/views/ni/project/components/DispatchItemDrawer";
import DispatchItemDrawer from "@/views/ni/project/components/DispatchItemDrawer";
import DispatchFinishDialog from "@/views/ni/project/components/DispatchFinishListDialog.vue";
import FlowTimelinePopover from "@/components/wf-flow-timeline/popover.vue";
import EmployApplyListDialog from "@/views/ni/project/components/EmployApplyListDialog.vue";
import NProgress from "nprogress";
import { exportBlob } from "@/api/common";
import { getToken } from "@/util/auth";
import { downloadXls } from "@/util/util";

export default {
  mixins: [exForm],
  components: {
    EmployApplyListDialog,
    FlowTimelinePopover,
    DispatchItem,
    DispatchPersonSelect,
    UserSelect,
    ProjectSelect,
    AttachDialog,
    DispatchItemDrawer,
    DispatchFinishDialog,
  },
  data() {
    return {
      processDefKey: "process_ni_project_employ",
      row: {},
      dispatchShow: false,
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        lazy: true,
        rowKey: "id",
        menu: false,
        searchShow: false,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        searchIndex: 3,
        searchIcon: true,
        labelWidth: 135,
        size: "mini",
        searchSize: "mini",
        align: "center",
        height: "auto",
        calcHeight: 30,
        tip: false,
        searchMenuSpan: 6,
        border: true,
        index: false,
        viewBtn: false,
        selection: true,
        dialogClickModal: false,
        column: [
          {
            label: "派工状态",
            prop: "status",
            type: "select",
            width: 85,
            dicData: [],
            span: 8,
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            dataType: "number",
            addDisplay: false,
            editDisplay: false,
          },
          {
            label: "申请人",
            prop: "employUserId",
            remote: true,
            dicUrl: `/api/blade-user/search/otj/user?name={{key}}`,
            props: {
              label: "realName",
              value: "id",
            },
            dicFormatter: (data) => {
              return data.data.records;
            },
            placeholder: " ",
            filterable: true,
            type: "select",
            hide: true,
            showColumn: false,
            display: false,
            search: true,
          },
          {
            label: "申请人",
            prop: "employUserName",
            display: false,
            disabled: true,
            width: 60,
            overHidden: true,
            span: 8,
          },
          {
            label: "申请部门",
            prop: "employDeptId",
            type: "tree",
            dicUrl: "/api/blade-system/dept/tree",
            props: {
              label: "title",
              value: "id",
            },
            multiple: true,
            checkStrictly: true,
            display: false,
            hide: true,
            search: true,
            searchMultiple: true,
            showColumn: false,
          },
          {
            label: "申请部门",
            prop: "employDeptName",
            display: false,
            width: 70,
            overHidden: true,
            span: 8,
          },

          {
            label: "项目编号",
            prop: "projectId",
            type: "select",
            remote: true,
            dicUrl: "/api/ni/project/page?status=9&keyword={{key}}",
            props: {
              label: "serialNo",
              value: "id",
              desc: "title",
            },
            dicFormatter: (data) => {
              return data.data.records;
            },
            search: true,
            hide: true,
            showColumn: false,
            span: 8,
            rules: [
              {
                required: true,
                message: "请选择项目",
                trigger: "blur",
              },
            ],
          },
          {
            label: "项目编号",
            prop: "projectSerialNo",
            overHidden: true,
            addDisplay: false,
            editDisplay: false,
            width: 105,
            span: 8,
          },
          {
            label: "负责人",
            prop: "chargerName",
            width: 70,
          },
          {
            label: "用工日期",
            prop: "employWorkDate",
            search: true,
            searchRange: true,
            searchOrder: 99,
            overHidden: true,
            span: 8,
            width: 159,
            type: "date",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
          },
          {
            label: "用工内容",
            prop: "content",
            type: "textarea",
            overHidden: true,
            span: 24,
            minRows: 2,
            minWidth: 120,
            rules: [
              {
                required: true,
                message: "请输入派工内容",
                trigger: "blur",
              },
            ],
          },
          {
            label: "每日用工数",
            prop: "personNum",
            type: "number",
            width: 85,
            span: 8,
            rules: [
              {
                required: true,
                message: "请输入用工数",
                trigger: "blur",
              },
            ],
          },
          {
            label: "派工日期",
            prop: "workDate",
            type: "date",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            search: true,
            searchRange: true,
            searchOrder: 98,
            span: 8,
            width: 85,
            rules: [
              {
                required: true,
                message: "请选择派工日期",
                trigger: "blur",
              },
            ],
          },

          {
            label: "操作人",
            prop: "createUserName",
            width: 80,
            overHidden: true,
          },
          {
            label: "指派人员",
            prop: "assignedTo",
            hide: true,
            showColumn: false,
            type: "select",
            remote: true,
            dicUrl: `/api/blade-user/search/otj/user?name={{key}}`,
            props: {
              label: "realName",
              value: "id",
            },
            dicFormatter: (data) => {
              return data.data.records;
            },
            search: true,
          },
          {
            label: "指派人员",
            prop: "assignedToName",
            display: false,
            overHidden: true,
            width: 110,
            span: 8,
          },
          {
            label: "指派工时/人",
            prop: "workingHours",
            display: false,
            overHidden: true,
            width: 90,
            span: 8,
          },
          {
            label: "备注",
            prop: "remark",
            display: false,
            overHidden: true,
            width: 90,
            span: 8,
          },
          {
            label: "累计工数",
            prop: "dispatchedPersonNum",
            overHidden: true,
            span: 8,
            display: false,
            width: 90,
          },
          {
            label: "累计工时",
            prop: "workedHours",
            overHidden: true,
            span: 8,
            display: false,
            width: 90,
          },
          {
            label: "状态",
            prop: "employStatus",
            type: "select",
            hide: true,
            width: 94,
            overHidden: true,
            dicData: [],
            span: 8,
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            dataType: "number",
            addDisplay: false,
            editDisplay: false,
          },

          {
            label: "用工类型",
            prop: "opType",
            type: "select",
            dicData: [],
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            width: 70,
            span: 8,
            rules: [
              {
                required: true,
                message: "请选择派工类型",
                trigger: "blur",
              },
            ],
          },
          {
            label: "派工单号",
            prop: "serialNo",
            width: 110,
            overHidden: true,
          },
          {
            label: "预计工时",
            labelTip: "人/小时",
            prop: "applyWorkingHours",
            type: "number",
            width: 70,
            span: 8,
            rules: [
              {
                required: true,
                message: "请输入 预计工时(小时)",
                trigger: "blur",
              },
            ],
            dataType: "number",
            min: 0,
            row: true,
            minWidth: 90,
          },

          {
            label: "是否指定人员",
            prop: "person",
            value: 0,
            span: 8,
            type: "radio",
            minWidth: 70,
            hide: true,
            row: true,
            dicData: [
              {
                label: "否",
                value: 0,
              },
              {
                label: "是",
                value: 1,
              },
            ],
            placeholder: " ",
            rules: [
              {
                required: true,
                message: "请选择指定人员",
                trigger: "blur",
              },
            ],
            control: (val) => {
              if (val) {
                this.form.opPost = null;
                return {
                  appointUserId: {
                    display: true,
                  },
                  person: {
                    row: false,
                  },
                };
              } else {
                this.form.appointUserId = null;
                return {
                  appointUserId: {
                    display: false,
                  },
                  person: {
                    row: true,
                  },
                };
              }
            },
          },
          {
            label: "指定人员",
            prop: "appointUserId",
            placeholder: " ",
            display: false,
            span: 8,
            row: true,
            hide: true,
            showColumn: false,
            rules: [
              {
                required: true,
                message: "请选择指定人员",
                trigger: "blur",
              },
            ],
          },
          {
            label: "指定人员",
            prop: "appointUserName",
            width: 100,
            display: false,
            overHidden: true,
            span: 8,
            fixed: "right",
          },
          {
            label: "用工单号",
            prop: "employSerialNo",
            placeholder: " ",
            overHidden: true,
            width: 120,
            disabled: true,
            search: true,
            span: 8,
          },
          {
            label: "关联单号",
            prop: "employParentSerialNo",
            width: 110,
            overHidden: true,
          },
          {
            label: "夜班",
            prop: "night",
            span: 8,
            type: "radio",
            minWidth: 70,
            dicData: [
              {
                label: "否",
                value: 0,
              },
              {
                label: "是",
                value: 1,
              },
            ],
            placeholder: " ",
          },
          {
            label: "材料备齐",
            prop: "ready",
            value: true,
            span: 8,
            type: "radio",
            minWidth: 70,
            dicData: [
              {
                label: "否",
                value: 0,
              },
              {
                label: "是",
                value: 1,
              },
            ],
            placeholder: " ",
          },
          {
            label: "是否落实图纸",
            prop: "drawing",
            value: 0,
            span: 8,
            type: "radio",
            minWidth: 70,
            dicData: [
              {
                value: 0,
                label: "不需要",
              },
              {
                label: "否",
                value: -1,
              },
              {
                label: "是",
                value: 1,
              },
            ],
            placeholder: " ",
          },
          {
            type: "radio",
            label: "是否与生产沟通",
            dicData: [
              {
                value: 0,
                label: "不需要",
              },
              {
                label: "是",
                value: 1,
              },
              {
                value: -1,
                label: "否",
              },
            ],
            span: 8,
            display: true,
            props: {
              label: "label",
              value: "value",
            },
            prop: "communication",
            value: 0,
            required: true,
          },
          {
            label: "是否需领料",
            prop: "picking",
            value: false,
            span: 8,
            hide: true,
            type: "radio",
            minWidth: 70,
            dicData: [
              {
                label: "否",
                value: false,
              },
              {
                label: "是",
                value: true,
              },
            ],
            placeholder: " ",
            control: (val) => {
              if (val) {
                return {
                  materials: {
                    display: true,
                  },
                };
              } else {
                return {
                  materials: {
                    display: false,
                  },
                };
              }
            },
          },
          {
            label: "领料明细",
            prop: "materials",
            labelPosition: "top",
            display: false,
            span: 24,
            width: 120,
            overHidden: true,
          },
          {
            label: "评分",
            prop: "rate",
            type: "rate",
            max: 5,
            width: 150,
            texts: ["极差", "失望", "一般", "满意", "惊喜"],
            hide: true,
            fixed: "right",
            colors: ["#99A9BF", "#F7BA2A", "#FF9900"],
          },
        ],
      },
      data: [],
      dispatch: false,
      personShow: false,
      dispatchDate: dateFormat(new Date(), "yyyy-MM-dd"),
      opTypeDict: [],
      opTypeDictKeyValue: {},
      opPostDict: [],
      opPostDictKeyValue: {},
      detailVisible: false,
      unDispatch: false,
      unFinish: true,
      workingHours: {
        visible: false,
        options: {
          enter: true,
          size: "mini",
          span: 24,
          emptyBtn: false,
          column: [
            {
              label: "指派工时",
              prop: "workingHours",
              type: "number",
              remote: true,
              placeholder: " ",
            },
          ],
        },
        form: {},
      },
      opType: "all",
      export: {
        column: [
          {
            label: "派工单（08：00-17：30）",
            prop: "header",
            children: [
              {
                label: "负责人",
                prop: "employUserName",
                width: 100,
              },
              {
                label: "项目号",
                prop: "projectSerialNo",
                width: 150,
              },
              {
                label: "工作内容",
                prop: "content",
                width: 280,
              },
              {
                label: " ",
                prop: "assignedToName",
                width: 200,
              },
              {
                label: "人员",
                prop: "personNum",
                width: 80,
              },
              {
                label: "班组",
                prop: "assignedToTeamLeader",
                width: 80,
              },
            ],
          },
        ],
      },
      finish: false,
      dispatchStatus: "all",
    };
  },
  computed: {
    ...mapGetters(["permission", "userInfo", "colorName"]),
    permissionList() {
      return {};
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  watch: {
    opType: {
      handler(val) {
        this.$nextTick(() => {
          this.page.currentPage = 1;
          this.onLoad(this.page);
        });
      },
    },
  },
  created() {
    this.dictInit();
  },
  methods: {
    handleDispatchStatusChange(val) {
      this.page.currentPage = 1;
      this.onLoad(this.page);
    },
    handleOpTypeChange(val) {
      this.page.currentPage = 1;
      this.onLoad(this.page);
    },
    rowDispatchDetail(row) {
      this.$refs.dispatchItemDrawerRef.onShow(row.employId);
    },
    rowWorkingHoursChange(row, index) {
      if (row.status === 9) {
        return;
      }
      if (!this.dispatchDate) {
        this.$message({
          type: "warning",
          message: "请选择派工日期!",
        });
        return;
      }
      if (!row.id) {
        this.$message({
          type: "warning",
          message: "请先派工!",
        });
        return;
      }
      this.form = row;
      this.form._index = index;
      this.workingHours.form = row;
      this.workingHours.visible = true;
    },
    handleDispatchFinishSubmit(form) {
      this.onLoad(this.page);
    },
    handleWorkingHoursSubmit(form, done) {
      assignHours(form.id, form.workingHours)
        .then(() => {
          this.data[this.form._index].workingHours = form.workingHours;
          this.$message({
            type: "success",
            message: "提交成功!",
          });
          this.workingHours.visible = false;
        })
        .finally(() => {
          done();
        });
    },
    rowAssignedToChange(row, index) {
      if (row.status === 9) {
        return;
      }
      if (!this.dispatchDate) {
        this.$message({
          type: "warning",
          message: "请选择派工日期!",
        });
        return;
      }
      this.form = row;
      this.form._index = index;
      //制作 研发辅助一组
      //电工 研发辅助二组
      let deptCode = row.opType === "1" ? "011004" : "011005";

      this.$refs.dispatchPersonSelectRef.onSelect(
        this.dispatchDate,
        row.assignedTo,
        deptCode
      );
    },
    rowAssignedToChangeSubmit(selectionList, cancel) {
      const personIds = selectionList.map((item) => item.userId);
      assign(
        this.form.employId,
        this.dispatchDate,
        personIds.join(","),
        cancel
      ).then((res) => {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "提交成功!",
        });
      });
    },
    rowDispatch(row) {
      this.$refs.dispatchItemDrawerRef.onShow(row);
    },
    rowAttach(row) {
      this.$refs.attachDialogRef.init(row.id, "ni_project_dispatch");
    },
    projectConfirm(selectionList) {
      if (selectionList) {
        if (
          this.$refs.crud &&
          "[object Function]" === toString.call(this.$refs.crud.clearValidate)
        ) {
          this.$refs.crud.clearValidate(["projectId"]);
        }
      }
    },
    dictInit() {
      this.$http
        .get(
          "/api/blade-system/dict-biz/dictionary?code=ni_project_dispatch_status"
        )
        .then((res) => {
          const column = this.findObject(this.option.column, "status");
          column.dicData = res.data.data;
        });
      this.$http
        .get(
          "/api/blade-system/dict-biz/dictionary?code=ni_project_employ_status"
        )
        .then((res) => {
          const column = this.findObject(this.option.column, "employStatus");
          column.dicData = res.data.data;
        });
      this.$http
        .get(
          "/api/blade-system/dict-biz/dictionary?code=ni_project_dispatch_type"
        )
        .then((res) => {
          const column = this.findObject(this.option.column, "opType");
          column.dicData = res.data.data;
          this.opTypeDict = res.data.data;
          this.opTypeDictKeyValue = this.opTypeDict.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });
    },
    handleCancel() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      const msg = `确认要取消选择的派工?`;
      this.$confirm(msg, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        dangerouslyUseHTMLString: true,
        type: "warning",
        center: true,
      }).then(() => {
        publishCancel(this.ids).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "提交成功!",
          });
        });
      });
    },
    handleReferenceDispatch() {
      // 1. 校验逻辑（复用现有校验）
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      // 3. 构建确认提示信息
      const confirmMessage =
        "确认使用最近的历史派工数据填充今日派工(<span style='color: red;font-weight: bold'>已派工的将跳过</span>)？";
      // 4. 弹出确认框
      this.$confirm(confirmMessage, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        dangerouslyUseHTMLString: true,
      })
        .then(() => {
          // 5. 用户确认后执行填充逻辑
          this.fillWithHistoricalData();
        })
        .catch(() => {
          // 6. 用户取消操作
          this.$message.info("操作已取消");
        });
    },
    handleDispatch() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      if (!this.dispatchDate) {
        this.$message.warning("请选择派工日期");
        return;
      }
      const dispatched = this.selectionList.some((item) => item.status !== 0);
      if (dispatched) {
        this.$message.warning("选择的数据存在已派工的数据，请重新选择");
        return;
      }
      const unDispatch = this.selectionList.some(
        (item) => !item.assignedToName
      );
      if (unDispatch) {
        this.$message.warning("选择的数据未指派人员");
        return;
      }
      const msg = `确认要将选择的<span style="color: red;font-weight: bold">${this.selectionList.length}</span>条数据派工?`;
      this.$confirm(msg, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        dangerouslyUseHTMLString: true,
        type: "warning",
        center: true,
      }).then(() => {
        publish(this.ids).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "提交成功!",
          });
        });
      });
    },
    handleExportPlus() {
      this.$confirm("是否导出当日派工数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        NProgress.start();
        const q = { ...this.query };
        if (q.workDate != null && q.workDate.length > 1) {
          q.workDateStart = q.workDate[0];
          q.workDateEnd = q.workDate[1];
          q.workDate = null;
        }
        if (q.employWorkDate != null && q.employWorkDate.length > 1) {
          q.employWorkDateStart = q.employWorkDate[0];
          q.employWorkDateEnd = q.employWorkDate[1];
          q.employWorkDate = null;
        }
        q.dispatchDate = this.dispatchDate;
        if (!q.queryEmployStatus) q.queryEmployStatus = "2,91,92,9";
        exportBlob(
          `/api/ni/project/dispatch/export-dispatch?${
            this.website.tokenHeader
          }=${getToken()}&dispatchDate=${q.dispatchDate}&queryEmployStatus=${
            q.queryEmployStatus
          }&opType=${this.opType === "all" ? "" : this.opType}`
        ).then((res) => {
          downloadXls(res.data, q.dispatchDate + "派工单.xlsx");
          NProgress.done();
        });
      });
    },
    handleExport() {
      this.$confirm("是否导出当日派工数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        const q = { ...this.query };
        if (q.workDate != null && q.workDate.length > 1) {
          q.workDateStart = q.workDate[0];
          q.workDateEnd = q.workDate[1];
          q.workDate = null;
        }
        if (q.employWorkDate != null && q.employWorkDate.length > 1) {
          q.employWorkDateStart = q.employWorkDate[0];
          q.employWorkDateEnd = q.employWorkDate[1];
          q.employWorkDate = null;
        }
        q.dispatchDate = this.dispatchDate;
        if (!q.queryEmployStatus) q.queryEmployStatus = "2,91,92,9";
        getList(q).then((res) => {
          const data = res.data.data;
          data.forEach((item) => {
            item.personNum = item.assignedTo.split(",").length;
          });
          this.export.column[0].label =
            this.dispatchDate + "派工单（08：00-17：30）";
          this.$Export.excel({
            title: this.dispatchDate + "派工单",
            columns: this.export.column,
            data,
          });
        });
      });
    },
    handleEmployOverrule() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      const dispatch = this.selectionList.some((item) => item.id);
      if (dispatch) {
        this.$message.warning("选择的数据中存在已派工的数据，请重新选择");
        return;
      }
      const employFinish = this.selectionList.some(
        (item) => item.employStatus === 92
      );
      if (employFinish) {
        this.$message.warning("选择的数据中存在已结束的用工申请，请重新选择");
        return;
      }
      this.$confirm("确认要驳回选择的用工申请？", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          const employIds = this.selectionList.map((item) => item.employId);
          return overruleEmploy(employIds.join(","));
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    handleDispatchFinish() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      const unDispatch = this.selectionList.some((item) => !item.id);
      if (unDispatch) {
        this.$message.warning("选择的数据中存在未派工的数据，请重新选择");
        return;
      }
      const employFinish = this.selectionList.some(
        (item) => item.employStatus === 92
      );
      if (employFinish) {
        this.$message.warning("选择的数据中存在已结束的用工申请，请重新选择");
        return;
      }
      let finish = !this.selectionList.some((item) => item.status !== 9);

      if (finish) {
        this.handleFinishEmploy();
        return;
      }
      let status = new Set();
      this.selectionList.forEach((item) => {
        status.add(item.status);
      });
      if (status.size > 1 && status.has(2)) {
        this.$message.warning("选择的数据中存在不同状态的数据，请重新选择");
        return;
      }
      this.$refs.dispatchFinishDialogRef.onShow(this.selectionList);
    },
    handleFinishEmploy() {
      this.$confirm("选择的派工已完工，是否结束该用工申请?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          const employIds = this.selectionList.map((item) => item.employId);
          return finishEmploy(employIds.join(","));
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    handlePersonShow() {
      this.$refs.dispatchPersonSelectRef.onShow(this.dispatchDate);
    },
    searchReset() {
      this.query = {};
      this.opType = "all";
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    handleDateChange() {
      this.page.currentPage = 1;
      this.onLoad(this.page);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      const q = {
        ...params,
        ...this.query,
      };
      if (q.workDate != null && q.workDate.length > 1) {
        q.workDateStart = q.workDate[0];
        q.workDateEnd = q.workDate[1];
        q.workDate = null;
      }
      if (q.employWorkDate != null && q.employWorkDate.length > 1) {
        q.employWorkDateStart = q.employWorkDate[0];
        q.employWorkDateEnd = q.employWorkDate[1];
        q.employWorkDate = null;
      }
      if (q.employDeptId != null) {
        q.queryEmployDeptIds = q.employDeptId.join(",");
      }
      if (this.opType === "all") {
        q.opType = null;
      } else q.opType = this.opType;
      q.dispatchDate = this.dispatchDate;
      if (!q.queryEmployStatus) q.queryEmployStatus = "2,91,92,9";
      // q.status = this.finish ? 9 : q.status;
      q.status = this.dispatchStatus === "all" ? null : this.dispatchStatus;
      getPage(page.currentPage, page.pageSize, q).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
    cellStyle({ row, column }) {
      if ("status" === column.columnKey && row.status === 1) {
        return {
          backgroundColor: this.colorName,
          color: "#fff",
        };
      } else if ("status" === column.columnKey && row.status === 9) {
        return {
          backgroundColor: "#409EFF",
          color: "#fff",
        };
      }
      if ("employUserName" === column.columnKey && row.employStatus === 92) {
        return {
          backgroundColor: "#409EFF",
          color: "#fff",
        };
      }
      if ("employWorkDate" === column.columnKey && row.night) {
        return {
          backgroundColor: "#682593",
          color: "#fff",
        };
      }
      if ("projectSerialNo" === column.columnKey) {
        return {
          cursor: "pointer",
          textDecoration: "underline",
        };
      }
      if ("remark" === column.columnKey && row.id) {
        return {
          cursor: "pointer",
          textDecoration: "underline",
        };
      }
    },
    cellClick(row, column, cell, event) {
      if (column.property === "projectSerialNo") {
        this.$refs.employApplyListDialogRef.onShow({
          projectId: row.projectId,
          statuses: "9,91,92",
        });
      }
      if (column.property === "remark") {
        if (!row.id) return;
        this.$prompt("请填写备注", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
        }).then(({ value }) => {
          changeRemark(row.id, value).then(() => {
            this.$message({
              type: "success",
              message: "操作成功!",
            });
            this.onLoad(this.page);
          });
        });
      }
    },
    fillWithHistoricalData() {
      const employIds = this.selectionList.map((item) => item.employId);
      referenceDispatch(this.dispatchDate, employIds.join(",")).then((res) => {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!",
        });
      });
    },
  },
};
</script>

<style lang="scss" scoped>
/* 当前激活（选中）的行的背景色 */
/deep/ .el-table .el-table__body tr.current-row {
  background-color: inherit;
}

/* 当前激活（选中）的单元格的边框样式 */
/deep/ .el-table .cell.current-row {
  border: 2px solid #409eff; /* 加粗边框，并设置为需要的颜色 */
}

.dispatch-header {
  display: flex;
  justify-content: space-between;
}

/* 使按钮靠右显示 */
/deep/ .el-tabs__nav {
  width: 100%;
}

/deep/ .el-tabs__item.is-top:last-child {
  float: right;
  padding: 0 10px;
}
</style>

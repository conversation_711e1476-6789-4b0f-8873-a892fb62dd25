import request from "@/router/axios";

export const getList = (current, size, params) => {
  return request({
    url: "/api/ni/por/autoApply/page",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/ni/por/autoApply/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const remove = (ids) => {
  return request({
    url: "/api/ni/por/autoApply/remove",
    method: "post",
    params: {
      ids,
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/ni/por/autoApply/save",
    method: "post",
    data: row,
  });
};

export const update = (row) => {
  return request({
    url: "/api/ni/por/autoApply/update",
    method: "post",
    data: row,
  });
};
export const pause = (ids) => {
  return request({
    url: "/api/ni/por/autoApply/pause",
    method: "post",
    params: {
      ids,
    },
  });
};

export const resume = (ids) => {
  return request({
    url: "/api/ni/por/autoApply/resume",
    method: "post",
    params: {
      ids,
    },
  });
};
//自动申购手动触发
export const manuallyTriggered = (ids) => {
  return request({
    url: "/api/ni/por/autoApply/manuallyTriggered",
    method: "post",
    params: {
      ids
    },
  });
};

export const  getApplyNum = (ids) =>{
  return request({
    url: "/api/ni/por/autoApply/getApplyNum",
    method: "post",
    data: {
      ids
    },
  });
}
export const getStockNum=(ids)=>{
  return request({
    url: "/api/ni/por/autoApply/getStockNum",
    method: "post",
    data: {
      ids
    },
  });
}
export const  getApplyCount = (ids) =>{
  return request({
    url: "/api/ni/por/autoApply/getApplyCount",
    method: "post",
    data: {
      ids
    },
  });
}
export const  getApplyTotal = (ids) =>{
  return request({
    url: "/api/ni/por/autoApply/getApplyTotal",
    method: "post",
    data: {
      ids
    },
  });
}

export  const  getStockInTotal=(ids)=>{
  return request({
    url: "/api/ni/por/autoApply/getStockInTotal",
    method: "post",
    data: {
      ids
    },
  });
}
export  const  getStockOutTotal=(ids)=>{
  return request({
    url: "/api/ni/por/autoApply/getStockOutTotal",
    method: "post",
    data: {
      ids
    },
  });
}

export  const  changeBudget=(ids, budgetId)=>{
  return request({
    url: "/api/ni/por/autoApply/changeBudget",
    method: "post",
    params: {
      ids,
      budgetId
    },
  });
}


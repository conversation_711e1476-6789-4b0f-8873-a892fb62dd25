<template>
  <basic-container>
    <avue-skeleton :loading="waiting" avatar :rows="8">
      <avue-title
        style="margin-bottom: 20px"
        :styles="{ fontSize: '20px' }"
        :value="process.name"
      ></avue-title>
      <el-card shadow="never" style="margin-top: 20px">
        <!-- 自定义表单区域 -->
        <avue-form
          v-if="
            option &&
            ((option.column && option.column.length > 0) ||
              (option.group && option.group.length > 0))
          "
          v-model="form"
          ref="form"
          :option="option"
          :defaults.sync="defaults"
          @submit="handleSubmit"
          @error="loading = false"
          :upload-preview="handleUploadPreview"
        >
          <template #title="{ disabled, size }">
            <budget-year-select
              v-model="form.parentId"
              :size="size"
              :disabled="disabled"
              @confirm="handleTitleConfirm"
            />
          </template>
          <template #personIdList="{ disabled, size }">
            <user-select
              v-model="form.personIdList"
              :size="size"
              multiple
              :disabled="disabled"
              @confirm="handlePersonIdListConfirm"
            />
          </template>
          <template #itemsLabel>
            <span style="font-size: 16px; font-weight: 500">预算明细</span>
            <el-divider direction="vertical"></el-divider>
            <el-dropdown @command="itemAdd">
              <el-button type="primary" size="mini" icon="el-icon-plus" plain
                >添加<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="1">实物</el-dropdown-item>
                <el-dropdown-item command="2">费用</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
            <el-button
              type="danger"
              size="mini"
              icon="el-icon-delete"
              plain
              :disabled="item.selectionList.length <= 0"
              @click="itemDelete"
              >删 除
            </el-button>
            <el-button
              type="primary"
              size="mini"
              icon="el-icon-upload2"
              plain
              @click="() => (item.excelBox = true)"
              >导入
            </el-button>
          </template>
          <template #items="{ row, disabled }">
            <avue-crud
              :option="item.option"
              :data="form.items"
              @selection-change="itemSelectionChange"
              ref="itemCrud"
              @cell-click="itemCellClickChange"
            >
              <template #cost="{ row, index }">
                <el-tag size="mini" type="danger" effect="dark" v-if="row.cost">
                  费用
                </el-tag>
                <el-tag size="mini" type="info" effect="plain" v-else>
                  实物
                </el-tag>
              </template>
              <template #materialCodeForm="{ row, disabled, size, index }">
                <material-select
                  v-model="row.materialId"
                  :size="size"
                  :disabled="disabled"
                  @submit="handleMaterialSubmit($event, row)"
                />
              </template>
              <template #numForm="{ row, disabled, size }">
                <el-input-number
                  :size="size"
                  v-model="row.num"
                  :disabled="disabled"
                  :min="0"
                  controls-position="right"
                  style="width: 100%"
                  @change="handleNumChange($event, row)"
                />
              </template>
            </avue-crud>
          </template>
          <template #subsLabel>
            <span style="font-size: 16px; font-weight: 500">预算小项</span>
            <el-divider direction="vertical"></el-divider>
            <el-button-group>
              <el-button
                type="primary"
                size="mini"
                icon="el-icon-plus"
                plain
                @click="subAdd"
              >
                添加
              </el-button>
              <el-button
                type="danger"
                size="mini"
                icon="el-icon-delete"
                plain
                :disabled="item.selectionList.length <= 0"
                @click="subDelete"
                >删 除
              </el-button>
            </el-button-group>
          </template>
          <template #subs="{ row, disabled }">
            <avue-crud
              :option="sub.option"
              :data="form.subs"
              @selection-change="subSelectionChange"
              ref="subCrud"
              @row-update="(form, index, done) => done()"
              @row-save="(form, done) => done()"
            >
              <template #leaderIdForm="{ disabled, size, index, row }">
                <user-select
                  v-model="row.leaderId"
                  :size="size"
                  :disabled="disabled"
                  @confirm="(ids, name) => (row.leaderName = name)"
                ></user-select>
              </template>
              <template #leaderId="{ disabled, size, index, row }">
                <span>{{ row.leaderName }}</span>
              </template>
            </avue-crud>
          </template>
        </avue-form>
        <!-- 自定义表单区域 -->
      </el-card>

      <el-card shadow="never" style="margin-top: 20px" v-if="showExamForm">
        <wf-examine-form
          ref="examineForm"
          :process="process"
          @user-select="handleUserSelect"
        ></wf-examine-form>
      </el-card>
      <div style="height: 120px"></div>
      <el-row
        class="foot-item avue-affix"
        :style="{
          width: isCollapse ? 'calc(100% - 80px)' : 'calc(100% - 260px)',
        }"
        id="avue-view"
      >
        <el-button
          v-no-more-click
          type="primary"
          size="mini"
          v-loading="loading"
          @click="handleSubmit"
          >发起
        </el-button>
        <el-button
          v-if="permission.wf_process_draft"
          type="success"
          size="mini"
          v-loading="loading"
          @click="handleDraftNotClose(process.id, process.formKey, form)"
          >存为草稿
        </el-button>
        <el-button
          v-if="permission.wf_process_draft"
          type="success"
          size="mini"
          v-loading="loading"
          @click="handleDraft(process.id, process.formKey, form)"
          >存为草稿并关闭
        </el-button>
      </el-row>
    </avue-skeleton>

    <!-- 人员选择弹窗 -->
    <wf-user-select
      ref="user-select"
      :check-type="checkType"
      :default-checked="defaultChecked"
      @onConfirm="handleUserSelectConfirm"
    />
    <material-select-dialog
      ref="materialSelectDialogRef"
      multiple
      @submit="handleItemAddSubmit"
    />
    <el-dialog
      title="数据导入"
      append-to-body
      :visible.sync="item.excelBox"
      width="555px"
    >
      <avue-form
        :option="item.excelOption"
        v-model="item.excelForm"
        :upload-after="uploadAfter"
      >
        <template slot="excelTemplate">
          <el-button type="primary" @click="handleTemplate">
            点击下载<i class="el-icon-download el-icon--right"></i>
          </el-button>
        </template>
      </avue-form>
    </el-dialog>
  </basic-container>
</template>

<script>
import WfExamineForm from "@/views/plugin/workflow/process/components/examForm.vue";
import WfUserSelect from "@/views/plugin/workflow/process/components/user-select";

import exForm from "@/views/plugin/workflow/mixins/ex-form";
import draft from "@/views/plugin/workflow/mixins/draft";
import {
  addMultiInstance,
  delegateTask,
  transferTask,
} from "@/api/plugin/workflow/process";
import budget from "./budget";
import ProjectSelect from "@/views/ni/project/components/MyProjectSelect";
import MaterialSelect from "@/views/ni/base/components/MaterialSelect1";
import FinAccountingSelect from "@/views/ni/fin/components/AccountingSelect";
import UserSelect from "@/components/user-select";
import { exportBlob } from "@/api/common";
import { getToken } from "@/util/auth";
import { downloadXls } from "@/util/util";
import MaterialSelectDialog from "@/views/ni/base/components/MaterialSelectDialog";
import BudgetYearSelect from "@/views/ni/por/components/BudgetYearSelect";
import debounce from "@/util/debounce";

export default {
  components: {
    BudgetYearSelect,
    UserSelect,
    WfUserSelect,
    WfExamineForm,
    ProjectSelect,
    MaterialSelect,
    FinAccountingSelect,
    MaterialSelectDialog,
  },
  mixins: [exForm, draft, budget],
  watch: {
    "form.subs": {
      handler(val) {
        if (val && val.length > 0) {
          const sub = this.findObject(this.item.option.column, "subCode");
          sub.dicData = val.map((item) => {
            return {
              label: item.name,
              value: item.code,
            };
          });
        }
      },
      immediate: true,
    },
  },
  activated() {
    let val=this.$route.query.p
    if (val) {
      const param = JSON.parse(Buffer.from(val, "base64").toString());
      const { processId, processDefKey, form } = param;
      if (processId) {
        this.getForm(processId);
      } else if (processDefKey) {
        this.getFormByProcessDefKey(processDefKey);
      }
      if (form) {
        const f = JSON.parse(
            new Buffer(decodeURIComponent(form), "utf8").toString("utf8")
        );
        this.form = Object.assign(this.form, f);
      }
    }
  },
  computed: {
    showExamForm() {
      const { hideComment, hideCopy, hideExamine } = this.process;
      return !hideComment || !hideCopy || !hideExamine;
    },
  },
  data() {
    return {
      defaults: {},
      form: {
        items: [],
        subs: [],
      },
      option: {
        span: 6,
        size: "mini",
        menuBtn: false,
        labelWidth: 130,
        column: [
          {
            type: "input",
            label: "申请人",
            display: true,
            prop: "create_user_name",
            value: "${this.$store.getters.userInfo.nick_name}",
            readonly: true,
          },
          {
            type: "input",
            label: "申请部门",
            display: true,
            prop: "createDeptName",
            value: "${this.$store.getters.userInfo.dept_name}",
            readonly: true,
            row: true,
          },
          {
            label: "预算年度",
            prop: "yearDate",
            type: "year",
            placeholder: " ",
            format: "yyyy",
            valueFormat: "yyyy",
            control: (val) => {
              if (val) {
                return {
                  title: {
                    prepend: val + "年",
                  },
                };
              } else {
                return {
                  title: {
                    prepend: "",
                  },
                };
              }
            },
            rules: [
              {
                required: true,
                message: "请选择预算年度",
                trigger: "blur",
              },
            ],
          },
          {
            label: "预算主题",
            prop: "title",
            prepend: "",
            placeholder: " ",
            rules: [
              {
                required: true,
                message: "请输入预算主题",
                trigger: "blur",
              },
            ],
          },
          {
            label: "预算编号",
            prop: "serialNo",
            placeholder: " ",
            overHidden: true,
            rules: [
              {
                required: true,
                message: "请输入预算编号",
                trigger: "blur",
              },
            ],
          },
          {
            label: "预算类型",
            prop: "type",
            placeholder: " ",
            type: "select",
            dicUrl: "/api/ni/por/type/listWithPermission",
            props: {
              label: "name",
              value: "code",
              desc: "code",
            },
            typeformat(item, label, value) {
              return `${item[label]}(${item[value]})`;
            },
            rules: [
              {
                required: true,
                message: "请选择预算类型",
                trigger: "blur",
              },
            ],
          },
          {
            label: "预算使用人员",
            prop: "personIdList",
            placeholder: " ",
            rules: [
              {
                required: true,
                message: "请选择预算人员",
                trigger: "blur",
              },
            ],
          },
          {
            label: "账套",
            prop: "brand",
            type: "radio",
            value: "1",
            dicUrl: "/api/blade-system/dict-biz/dictionary?code=ni_brand",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            search: true,
            rules: [
              {
                required: true,
                message: "请选择账套",
                trigger: "blur",
              },
            ],
          },
          {
            label: "预算用途",
            prop: "remark",
            type: "textarea",
            span: 24,
            minRows: 3,
            rules: [
              {
                required: true,
                message: "请输入预算用途",
                trigger: "blur",
              },
            ],
          },
          {
            label: "附件",
            type: "upload",
            propsHttp: {
              res: "data",
              url: "link",
              name: "originalName",
            },
            action: "/api/blade-resource/oss/endpoint/put-file",
            display: true,
            span: 24,
            showFileList: true,
            multiple: true,
            limit: 10,
            prop: "attachment",
          },
          {
            label: "预算明细",
            prop: "items",
            labelPosition: "top",
            span: 24,
          },
          {
            label: "预算小项",
            prop: "subs",
            labelPosition: "top",
            span: 24,
          },
        ],
      },
      process: {},
      loading: false,
      item: {
        selectionList: [],
        option: {
          header: false,
          cellBtn: true,
          addBtn: false,
          refreshBtn: false,
          columnBtn: false,
          menu: false,
          saveBtn: false,
          updateBtn: false,
          cancelBtn: false,
          editBtn: false,
          delBtn: false,
          dialogFullscreen: true,
          size: "mini",
          align: "center",
          calcHeight: 30,
          tip: false,
          border: true,
          viewBtn: false,
          dialogClickModal: false,
          selection: true,
          showSummary: true,
          sumColumnList: [
            {
              name: "num",
              type: "sum",
              decimals: 1,
            },
            {
              name: "amount",
              type: "sum",
            },
          ],
          column: [
            {
              label: "小项",
              prop: "subCode",
              type: "select",
              dicData: [],
              placeholder: " ",
              width: 100,
              cell: true,
              rules: [
                {
                  required: false,
                  message: "请选择小项",
                  trigger: "blur",
                },
              ],
            },
            {
              label: "类型",
              prop: "cost",
              placeholder: " ",
              width: 70,
              disabled: true,
            },
            {
              label: "品名",
              prop: "materialName",
              type: "textarea",
              minRows: 1,
              placeholder: " ",
              overHidden: true,
              cell: true,
            },
            {
              label: "用途",
              prop: "purpose",
              type: "textarea",
              minRows: 1,
              placeholder: " ",
              overHidden: true,
              cell: true,
            },
            {
              label: "编码",
              minWidth: 100,
              placeholder: " ",
              prop: "materialCode",
              overHidden: true,
              clearable: false,
              cell: true,
            },
            {
              label: "规格",
              prop: "specification",
              placeholder: " ",
              overHidden: true,
              disabled: true,
            },
            {
              label: "材质",
              prop: "quality",
              placeholder: " ",
              disabled: true,
              overHidden: true,
            },
            {
              label: "单位",
              prop: "unit",
              type: "select",
              dicUrl:
                "/api/blade-system/dict-biz/dictionary?code=ni_material_unit",
              props: {
                label: "dictValue",
                value: "dictKey",
              },
              placeholder: " ",
              slot: true,
              rules: [
                {
                  required: true,
                  message: "请选择单位",
                  trigger: "blur",
                },
              ],
            },
            {
              label: "数量",
              prop: "num",
              type: "number",
              precision: 0,
              placeholder: " ",
              minWidth: 100,
              cell: true,
              rules: [
                {
                  required: true,
                  message: "请输入数量",
                  trigger: "blur",
                },
              ],
            },
            {
              label: "单价",
              prop: "price",
              type: "number",
              controls: false,
              disabled: true,
              precision: 2,
              placeholder: " ",
            },
            {
              label: "金额",
              prop: "amount",
              overHidden: true,
              type: "number",
              cell: true,
              minWidth: 100,
              precision: 2,
              placeholder: " ",
              rules: [
                {
                  required: true,
                  message: "请输入金额",
                  trigger: "blur",
                },
              ],
              change: ({ row, value }) => {
                if (row.num) {
                  row.price = (value / row.num).toFixed(2);
                } else {
                  row.price = 0.0;
                }
              },
            },
            {
              label: "备注",
              prop: "remark",
              type: "textarea",
              minRows: 1,
              placeholder: " ",
              cell: true,
            },
          ],
        },
        excelBox: false,
        excelOption: {
          size: "mini",
          searchSize: "mini",
          submitBtn: false,
          emptyBtn: false,
          column: [
            {
              label: "数据上传",
              prop: "excelFile",
              type: "upload",
              drag: true,
              loadText: "模板上传中，请稍等",
              span: 24,
              data: {},
              propsHttp: {
                res: "data",
              },
              tip: "请上传 .xls,.xlsx 标准格式文件",
              action: "/api/ni/por/budget/item/parse-items",
            },
            {
              label: "模板下载",
              prop: "excelTemplate",
              formslot: true,
              span: 24,
            },
          ],
        },
        excelForm: {},
      },
      sub: {
        selectionList: [],
        option: {
          menuWidth: 150,
          header: false,
          cellBtn: true,
          addBtn: false,
          refreshBtn: false,
          columnBtn: false,
          cancelBtn: false,
          size: "mini",
          align: "center",
          calcHeight: 30,
          tip: false,
          border: true,
          viewBtn: false,
          dialogClickModal: false,
          selection: true,
          column: [
            {
              label: "小项名称",
              prop: "name",
              type: "textarea",
              minRows: 1,
              placeholder: " ",
              overHidden: true,
              cell: true,
              rules: [
                {
                  required: true,
                  message: "请输入数量",
                  trigger: "blur",
                },
              ],
            },
            {
              label: "编码",
              prop: "code",
              type: "textarea",
              minRows: 1,
              placeholder: " ",
              overHidden: true,
              cell: true,
              rules: [
                {
                  required: true,
                  message: "请输入数量",
                  trigger: "blur",
                },
              ],
            },
            {
              label: "负责人",
              prop: "leaderId",
              precision: 0,
              placeholder: " ",
              minWidth: 100,
              cell: true,
              rules: [
                {
                  required: true,
                  message: "请输入数量",
                  trigger: "blur",
                },
              ],
            },
            {
              label: "备注",
              prop: "remark",
              type: "textarea",
              minRows: 1,
              placeholder: " ",
              cell: true,
            },
          ],
        },
        excelBox: false,
        excelOption: {
          size: "mini",
          searchSize: "mini",
          submitBtn: false,
          emptyBtn: false,
          column: [
            {
              label: "数据上传",
              prop: "excelFile",
              type: "upload",
              drag: true,
              loadText: "模板上传中，请稍等",
              span: 24,
              data: {},
              propsHttp: {
                res: "data",
              },
              tip: "请上传 .xls,.xlsx 标准格式文件",
              action: "/api/ni/por/budget/item/parse-items",
            },
            {
              label: "模板下载",
              prop: "excelTemplate",
              formslot: true,
              span: 24,
            },
          ],
        },
        excelForm: {},
      },
    };
  },
  methods: {
    subAdd() {
      this.$refs.subCrud.rowCellAdd({});
    },
    handlePersonIdListConfirm(ids) {
      if (ids) {
        this.form.personIds = ids;
      }
    },
    handleTitleConfirm(selectList) {
      console.log(selectList);
      if (selectList) {
        this.form.title = selectList[0].title;
        this.form.parentId = selectList[0].id;
        this.form.type = selectList[0].type;
        this.form.brand = selectList[0].brand;
        this.form.remark = selectList[0].remark;
      }
    },
    handleTemplate() {
      exportBlob(
        `/api/ni/por/budget/item/export-template?${
          this.website.tokenHeader
        }=${getToken()}`
      ).then((res) => {
        downloadXls(res.data, "明细导入模板.xlsx");
      });
    },
    uploadAfter(res, done) {
      this.item.excelBox = false;
      done();
      if (res) {
        res.forEach((item) => {
          this.form.items.push(item);
        });
      }
    },
    handleNumChange(num, row) {
      if (row.amount) {
        row.amount = (Number(row.amount) / Number(row.num)).toFixed(2);
      }
    },
    itemAdd(cost) {
      const materialCode = this.findObject(
        this.item.option.column,
        "materialCode"
      );
      if (cost === "1") {
        this.$refs.materialSelectDialogRef.visible = true;
        materialCode.rules = [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
        ];
      } else if (cost === "2") {
        this.form.items.forEach((item) => (item.$cellEdit = false));
        this.form.items.push({ cost: true, $cellEdit: true, num: 1 });
        materialCode.rules = [
          {
            required: false,
            message: "请输入",
            trigger: "blur",
          },
        ];
      }
    },
    itemCellClickChange(row) {
      this.form.items.forEach((item) => (item.$cellEdit = false));
      row.$cellEdit = true;
    },
    subSelectionChange(list) {
      this.sub.selectionList = list;
    },
    itemSelectionChange(list) {
      this.item.selectionList = list;
    },
    itemSelectionClear() {
      this.item.selectionList = [];
      this.$refs.itemCrud.toggleSelection();
    },
    subDelete() {
      const indexList = this.sub.selectionList.map((item) => item.$index);
      const [...items] = this.form.subs.filter(
        (item, index) => !indexList.includes(index)
      );
      console.log(items);
      this.form.subs = items;
    },
    itemDelete() {
      const indexList = this.item.selectionList.map((item) => item.$index);
      const [...items] = this.form.items.filter(
        (item, index) => !indexList.includes(index)
      );
      console.log(items);
      this.form.items = items;
    },
    handleItemAddSubmit(selectList) {
      if (selectList) {
        selectList.forEach((item) => {
          const row = {
            typeId: item.typeId,
            materialCode: item.code,
            materialName: item.name,
            materialTypeId: item.typeId,
            materialId: item.id,
            specification: item.specification,
            quality: item.quality,
            gb: item.gb,
            unit: item.unit,
            cost: item.cost,
          };
          this.form.items.push(row);
        });
      }
    },
    getForm(processId) {
      this.getStartForm(processId).then((res) => {
        const _this = this;
        let { process } = res;
        _this.form.processId = process.id;
        const option = _this.option;
        const { column, group } = option;

        column.forEach((col) => {
          if (col.value) col.value = _this.getDefaultValues(col.value);
        });
        if (group && group.length > 0) {
          // 处理group
          group.forEach((gro) => {
            gro.column.forEach((col) => {
              if (col.value) col.value = _this.getDefaultValues(col.value);
            });
          });
        }
        if (_this.permission.wf_process_draft) {
          // 查询是否有草稿箱
          _this.initDraft(process.id).then((data) => {
            _this
              .$confirm("是否恢复之前保存的草稿？", "提示", {})
              .then(() => {
                _this.form = JSON.parse(data);
              })
              .catch(() => {});
          });
        }
        _this.waiting = false;
      });
    },
    getFormByProcessDefKey(processDefKey) {
      this.getStartFormByProcessDefKey(processDefKey).then((res) => {
        const _this = this;
        let { process } = res;
        console.log(process);
        _this.form.processId = process.id;
        const option = _this.option;
        const { column, group } = option;
        column.forEach((col) => {
          if (col.value) col.value = _this.getDefaultValues(col.value);
        });
        if (group && group.length > 0) {
          // 处理group
          group.forEach((gro) => {
            gro.column.forEach((col) => {
              if (col.value) col.value = _this.getDefaultValues(col.value);
            });
          });
        }
        if (_this.permission.wf_process_draft) {
          // 查询是否有草稿箱
          _this.initDraft(process.id).then((data) => {
            _this
              .$confirm("是否恢复之前保存的草稿？", "提示", {})
              .then(() => {
                _this.form = JSON.parse(data);
              })
              .catch(() => {});
          });
        }
        _this.waiting = false;
      });
    },
    projectConfirm(selectionList) {
      if (selectionList) {
        this.$nextTick(() => this.$refs.form.clearValidate());
        console.log(selectionList);
        this.form.projectSerialNo = selectionList[0].serialNo;
      }
    },
    handleMaterialSubmit(selectionList, row1) {
      row1.materialCode = selectionList[0].code;
      row1.materialName = selectionList[0].name;
      row1.materialId = selectionList[0].id;
      row1.specification = selectionList[0].specification;
      row1.quality = selectionList[0].quality;
      row1.gb = selectionList[0].gb;
      row1.unit = selectionList[0].unit;
    },
    validateItems(items) {
      return !items || !items.some((item) => !item.num || !item.amount);
    },
    handleSubmit:debounce(function (){
      this.loading = true;
      const items = this.validateItems(this.form.items);
      if (items) {
        this.handleStartProcess(true)
          .then((done) => {
            this.$message.success("发起成功");
            this.handleCloseTag("/plugin/workflow/process/send");
            done();
          })
          .catch(() => {
            this.loading = false;
          });
      } else {
        this.$message.warning("明细中存在未填写的数量/金额");
        this.loading = false;
      }
    },1000),
    // 人员选择弹窗
    handleUserSelect({ type, checkType }) {
      if (!this.comment && ["transfer", "delegate"].includes(type)) {
        this.$message.error("请填写批复意见");
        return;
      }
      if (type == "assignee")
        this.defaultChecked = this.$refs.examineForm.examineForm.assignee;
      else if (type == "copy")
        this.defaultChecked = this.$refs.examineForm.examineForm.copyUser;

      this.$refs["user-select"].visible = true;
      this.userSelectType = type;
      this.checkType = checkType;
    },
    // 选人回调
    handleUserSelectConfirm(id, name) {
      const { comment, copyUser } = this.$refs.examineForm.examineForm;
      const {
        taskId,
        processInstanceId,
        processDefinitionName,
        processDefinitionId,
      } = this.process;

      const type = this.userSelectType;
      const param = {
        taskId,
        processInstanceId,
        processDefinitionName,
        processDefinitionId,
        assignee: id,
        comment,
        copyUser,
      };
      if (type == "transfer") {
        transferTask(param).then(() => {
          // 转办
          this.$message.success("转办成功");
          this.handleCloseTag("/plugin/workflow/process/todo");
        });
      } else if (type == "delegate") {
        // 委托
        delegateTask(param).then(() => {
          this.$message.success("委托成功");
          this.handleCloseTag("/plugin/workflow/process/todo");
        });
      } else if (type == "addInstance") {
        // 加签
        addMultiInstance(param).then(() => {
          this.$message.success("加签成功");
        });
      } else if (type == "copy") {
        // 抄送
        this.$refs.examineForm.examineForm.copyUser = id;
        this.$refs.examineForm.examineForm.$copyUser = name;
      } else if (type == "assignee") {
        // 指定下一步审批人
        this.$refs.examineForm.examineForm.assignee = id;
        this.$refs.examineForm.examineForm.$assignee = name;
      } else {
        // 外部使用
        this.form[type] = id;
        this.form["$" + type] = name;
      }
      this.$refs["user-select"].visible = false;
    },
  },
};
</script>

<style lang="scss" scoped>
.foot-item {
  position: fixed;
  bottom: 0;
  margin-left: -20px;
  // right: 0;
  z-index: 101;
  height: 66px;
  background-color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  -webkit-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
</style>

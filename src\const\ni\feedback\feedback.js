export default {
  height: "auto",
  calcHeight: 30,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: true,
  selection: true,
  dialogClickModal: false,
  labelWidth: 120,
  column: [
    {
      label: "发起人id",
      prop: "applyUserId",
      type: "input",
      hide: true,
    },
    {
      label: "发起人",
      prop: "applyUserName",
      type: "input",
      search: true,
    },

    {
      label: "重复出现",
      prop: "feedbackCollectionCount",
      type: "input",
      hide: true,
      viewDisplay: true,
    },
    {
      label: "问题负责人",
      prop: "responsibilityName",
      type: "input",
    },
    {
      label: "问题描述",
      prop: "description",
      type: "textarea",
      search: true,
      span: 24,
    },
    {
      label: '相关附件',
      prop: 'fileUrls',
      type: 'upload',
      listType: 'picture-img',
      span: 24,
      hide: true
    },
    {
      label: "AI指导意见",
      prop: "aiGuidance",
      type: "textarea",
      span: 24,
    },

    {
      label: "最终解决日期",
      prop: "finalResolutionTime",
      type: "input",
    },
    {
      label: "最终报告",
      prop: "finalReport",
      type: "input",
      hide: true,
      viewDisplay: false,
    },
    {
      label: "状态",
      prop: "status",
      type: "select",
    },

    {
      label: "状态",
      prop: "feedbackStatuss",
      type: "select",
      search: true,
      dicData: [
        {
          label: "等待负责人认领",
          value: 1,
        },
        {
          label: "认领处理中",
          value: 2,
        },
        {
          label: "待发起人确认",
          value: 3,
        },
        {
          label: "人工分配中",
          value: 4,
        },
        {
          label: "无需处理",
          value: 5,
        },
        {
          label: "人工驳回",
          value: 6,
        }
        ,
        {
          label: "发起人驳回",
          value: 7,
        }
        ,
        {
          label: "问题已处理",
          value: 8,
        }
      ],
      hide: true,
      viewDisplay: false,
    },

    {
      label: "提出时间",
      prop: "createTime",
      type: "input",
    },
    {
      label: "十不放过",
      prop: "isTenNonNeglect",
      type: "select",
      search: true,
      dicData: [
        {
          label: "是",
          value: true,
        },
        {
          label: "不是",
          value: false,
        },
      ],
    },
    {
      label: "同步状态",
      prop: "pointId",
      type: "input",
    },
    {
      label: "问题认领表",
      prop: "solvingRecordList",
      span: 24,
      type: "dynamic",
      hide: true,
      children: {
        align: "center",
        headerAlign: "center",
        column: [
          {
            label: "认领人",
            prop: "responsibilityPersonName",
            placeholder: " ",
            type: "input",
          },
          {
            label: "认领时间",
            prop: "createTime",
            placeholder: " ",
            type: "input",
          },
          {
            label: "预估时间",
            prop: "estimatedTime",
            placeholder: " ",
            type: "input",
          },
          {
            label: "解决时间",
            prop: "resolveDate",
            placeholder: " ",
            type: "input",
          },
          {
            label: "发生原因",
            prop: "problemCause",
            type: "textarea",
          },
          {
            label: "解决方案",
            prop: "solution",
            type: "textarea",
          },
          {
            label: "当前状态",
            prop: "status",
            type: "select",
            dicData: [
              {
                label: "已认领处理中",
                value: 1,
              },
              {
                label: "问题已处理完成",
                value: 2,
              },
            ],
          },
        ],
      },
    },
  ],
};

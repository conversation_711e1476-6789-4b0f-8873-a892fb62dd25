import request from "@/router/axios";

export const getList = (params) => {
  return request({
    url: "/api/ni/base/material/list",
    method: "get",
    params: {
      ...params,
    },
  });
};

export const getPage = (current, size, params) => {
  return request({
    url: "/api/ni/base/material/page",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};
/**
 * 带库存
 * @param current
 * @param size
 * @param params
 * @returns {AxiosPromise}
 */
export const pageWithStock = (current, size, params) => {
  return request({
    url: "/api/ni/base/material/pageWithStock",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/ni/base/material/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const remove = (ids) => {
  return request({
    url: "/api/ni/base/material/remove",
    method: "post",
    params: {
      ids,
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/ni/base/material/save",
    method: "post",
    data: row,
  });
};

export const update = (row) => {
  return request({
    url: "/api/ni/base/material/update",
    method: "post",
    data: row,
  });
};

// export const saveOrUpdateUnitTrans = (row) => {
//   return request({
//     url: '/api/ni/base/material/saveOrUpdateUnitTrans',
//     method: 'post',
//     data: row
//   })
// }
export const unitList = (id) => {
  return request({
    url: "/api/ni/base/material/unit/list",
    method: "post",
    params: {
      id,
    },
  });
};

export const apply = (row, processDefKey) => {
  return request({
    url: "/api/ni/base/material/apply",
    method: "post",
    params: {
      processDefKey,
    },
    data: row,
  });
};
export const syncYonyou = () => {
  return request({
    url: "/api/ni/base/material/syncYonyou",
    method: "post",
  });
};
export const sync = (id, isCovered = false) => {
  return request({
    url: "/api/ni/base/material/sync",
    method: "post",
    params: {
      id,
      isCovered,
    },
  });
};
export const updateDisabledById = (id, disabled) => {
  return request({
    url: "/api/ni/base/material/updateDisabledById",
    method: "post",
    params: {
      id,
      disabled,
    },
  });
};
export const getSyncStatus = () => {
  return request({
    url: "/api/ni/base/material/getSyncStatus",
    method: "get",
  });
};
export const syncFromOld = () => {
  return request({
    url: "/api/ni/base/material/syncFromOld",
    method: "post",
  });
};

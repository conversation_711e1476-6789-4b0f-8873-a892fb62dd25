import request from "@/router/axios";

export const getList = (current, size, params) => {
    return request({
        url: "/api/ni/por/order/page",
        method: "get",
        params: {
            ...params,
            current,
            size,
        },
    });
};
export const getList1 = (params) => {
    return request({
        url: "/api/ni/por/order/list",
        method: "get",
        params,
    });
};

export const getDetail = (id) => {
    return request({
        url: "/api/ni/por/order/detail",
        method: "get",
        params: {
            id,
        },
    });
};

export const remove = (ids) => {
    return request({
        url: "/api/ni/por/order/remove",
        method: "post",
        params: {
            ids,
        },
    });
};

export const add = (row) => {
    return request({
        url: "/api/ni/por/order/save",
        method: "post",
        data: row,
    });
};

export const update = (row) => {
    return request({
        url: "/api/ni/por/order/update",
        method: "post",
        data: row,
    });
};

export const submit = (ids, contractFinish) => {
    return request({
        url: "/api/ni/por/order/submit",
        method: "post",
        params: {
            ids,
            contractFinish,
        },
    });
};

export const register = (row) => {
    return request({
        url: "/api/ni/por/order/register",
        method: "post",
        data: row,
    });
};

export const toVoid = (ids) => {
    return request({
        url: "/api/ni/por/order/toVoid",
        method: "post",
        params: {
            ids,
        },
    });
};
export const itemsUnionCostItemsPage = (current, size, params) => {
    return request({
        url: "/api/ni/por/order/itemsUnionCostItemsPage",
        method: "get",
        params: {
            ...params,
            current,
            size,
        },
    });
};
export const changeSupplier = (ids, supplierId) => {
    return request({
        url: "/api/ni/por/order/changeSupplier",
        method: "post",
        params: {
            ids,
            supplierId,
        },
    });
};
export const changePurchaseUser = (ids, purchaseUserId) => {
    return request({
        url: "/api/ni/por/order/changePurchaseUser",
        method: "post",
        params: {
            ids,
            purchaseUserId,
        },
    });
};

export const linkContract = (ids, contractId) => {
    return request({
        url: "/api/ni/por/order/linkContract",
        method: "post",
        params: {
            ids,
            contractId,
        },
    });
};
export const splitOrder = (row) => {
    return request({
        url: "/api/ni/por/order/splitOrder",
        method: "post",
        data: row,
    });
};
export const cancel = (data) => {
    return request({
        url: "/api/ni/por/order/cancel",
        method: "post",
        data,
    });
};

export const changePayType = (ids, payType) => {
    return request({
        url: "/api/ni/por/order/changePayType",
        method: "post",
        params: {
            ids,
            payType,
        },
    });
};

export const changeBillType = (data) => {
    return request({
        url: "/api/ni/por/order/changeBillType",
        method: "post",
        data,
    });
};

export const buildContract = (data) => {
    return request({
        url: "/api/ni/por/order/buildContract",
        method: "post",
        data,
    });
};
export const splitNum = (id, num) => {
    return request({
        url: "/api/ni/por/order/splitNum",
        method: "post",
        params: {
            id,
            num,
        },
    });
};
export const splitNumAndOrder = (id, num) =>{
    return request({
        url: "/api/ni/por/order/splitNumAndOrder",
        method: "post",
        params: {
            id,
            num,
        },
    });
}
export const changeNum = (id, itemType, num) => {
    return request({
        url: "/api/ni/por/order/changeNum",
        method: "post",
        params: {
            id,
            itemType,
            num,
        },
    });
};
export const changeAmount = (id, itemType, amount) => {
    return request({
        url: "/api/ni/por/order/changeAmount",
        method: "post",
        params: {
            id,
            itemType,
            amount,
        },
    });
};

export const changeRemark = (ids, remark) => {
    return request({
        url: "/api/ni/por/order/changeRemark",
        method: "post",
        params: {
            ids,
            remark,
        },
    });
};
export const unOrderItemList = (params) => {
    return request({
        url: "/api/ni/por/order/unOrderItemList",
        method: "get",
        params,
    });
};

export const unArrivalItemList = (params) => {
    return request({
        url: "/api/ni/por/order/unArrivalItemList",
        method: "get",
        params,
    });
};

export const buildOrder = (data) => {
    return request({
        url: "/api/ni/por/order/buildOrder",
        method: "post",
        data,
    });
};
export const changeType = (ids, type) => {
    return request({
        url: "/api/ni/por/order/changeType",
        method: "post",
        params: {
            ids,
            type,
        },
    });
};
export const getAdvanceAmount = (orderItemIds) => {
    return request({
        url: "/api/ni/por/order/getAdvanceAmount",
        method: "get",
        params: {
            orderItemIds,
        },
    });
};
//生成合同编号
export const generateContractSerialNo = (params) => {
    return request({
        url: "/api/ni/por/order/generateContractSerialNo",
        method: "post",
        params: {
            id: params.id,
        },
    });
};
//合并订单
export const mergePorOrder = (id, data) => {
    return request({
        url: "/api/ni/por/order/mergePorOrder",
        method: "post",
        params: {
            id,
        },
        data: data,
    });
};

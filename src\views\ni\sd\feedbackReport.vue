<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page.sync="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @refresh-change="refreshChange"
               @on-load="onLoad">
      <template slot="menuLeft">
        <el-button type="danger"
                   size="small"
                   icon="el-icon-delete"
                   plain
                   v-if="permission.feedbackReport_delete"
                   @click="handleDelete">删 除
        </el-button>
      </template>
      <template #menu="{ row, index, size }">
        <el-button
          type="text"
          icon="el-icon-tickets"
          :size="size"
          @click="rowAttach(row)"
        >附件
        </el-button>
      </template>
    </avue-crud>
    <attach-dialog ref="attachDialogRef" :detail="attachDetail"/>

  </basic-container>
</template>

<script>
  import {getList, getDetail, add, update, remove} from "@/api/ni/sd/feedbackReport";
  import {mapGetters} from "vuex";
  import AttachDialog from "@/components/attach-dialog/index.vue";

  export default {
    components: {AttachDialog},
    data() {
      return {
        form: {},
        query: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        attachDetail: false,
        selectionList: [],
        option: {
          height:'auto',
          calcHeight: 30,
          tip: false,
          searchShow: true,
          searchMenuSpan: 6,
          border: true,
          index: true,
          viewBtn: true,
          selection: true,
          dialogClickModal: false,
          column: [
            // {
            //   label: "状态",
            //   prop: "status",
            //   type: "input",
            // },
            {
              label: "报告编号",
              prop: "reportNumber",
              type: "input",
            },
            {
              label: "报告日期",
              prop: "reportDate",
              type: "input",
            },
            {
              label: "投诉类型",
                prop: "complaintType",
              type: "input",
            },
            {
              label: "分类",
                prop: "classification",
              type: "input",
            },
            {
              label: "产品批号",
                prop: "prodBatchNum",
              type: "input",
            },
            {
              label: "客户名称",
              prop: "customerName",
              type: "input",
            },
            {
              label: "业务经理",
              prop: "businessManager",
              type: "input",
            },
            {
              label: "投诉内容",
              prop: "complaintContent",
              type: "textarea",
              overHidden: true,
            },
            {
              label: "问题描述",
              prop: "problemDescription",
              type: "textarea",
              overHidden: true,
            },
            {
              label: "临时措施",
              prop: "temporaryMeasures",
              type: "textarea",
              overHidden: true,
            },
            {
              label: "现场验证",
              prop: "onsiteVerification",
              type: "textarea",
              overHidden: true,
            },
            {
              label: "原因判定",
              prop: "causeJudgment",
              type: "textarea",
              overHidden: true,
            },
            {
              label: "永久性纠正措施",
              prop: "permanentCorrectiveMeasures",
              type: "textarea",
              overHidden: true,
            },
            {
              label: "纠正措施验证",
              prop: "verificationOfCorrectiveMeasures",
              type: "textarea",
              overHidden: true,
            },
            {
              label: "技术措施",
              prop: "technicalMeasures",
              type: "textarea",
              overHidden: true,
            },
            {
              label: "创建人",
              prop: "createUser",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              hide: true,
            },
            {
              label: "创建时间",
              prop: "createTime",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              hide: false,
            },
          ]
        },
        data: []
      };
    },
    computed: {
      ...mapGetters(["permission"]),
      permissionList() {
        return {
          addBtn: this.vaildData(this.permission.feedbackReport_add, false),
          viewBtn: this.vaildData(this.permission.feedbackReport_view, false),
          delBtn: this.vaildData(this.permission.feedbackReport_delete, false),
          editBtn: this.vaildData(this.permission.feedbackReport_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    methods: {
      rowAttach(row) {
        if (row.status > 0) {
          this.attachDetail = true;
        } else {
          this.attachDetail = false;
        }
        this.$refs.attachDialogRef.init(row.id, this.module);
      },
      rowSave(row, done, loading) {
        add(row).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          loading();
          window.console.log(error);
        });
      },
      rowUpdate(row, index, done, loading) {
        update(row).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          loading();
          console.log(error);
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1;
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      refreshChange() {
        this.onLoad(this.page, this.query);
      },
      onLoad(page, params = {}) {
        this.loading = true;
        getList(page.currentPage, page.pageSize, Object.assign(params, this.query,{descs:'id'})).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      }
    }
  };
</script>

<style>
</style>

import request from '@/router/axios';

export const getPage = (current, size, params) => {
    return request({
      url: '/api/ni/pa/overtime/apply/recordPage',
      method: 'get',
      params: {
        ...params,
        current,
        size,
      }
    })
  }

export const getDetail = (id) => {
  return request({
    url: '/api/ni/pa/overtime/apply/record/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/ni/pa/overtime/apply/record/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const getDetailListByUserId = (currentPage,pageSize,userId) => {
  return request({
    url: '/api/ni/pa/overtime/apply/getDetailListByUserId',
    method: 'get',
    params: {
      currentPage,
      pageSize,
      userId
    }
  })
}

export const getAttInfo = (current, size, params) => {
  return request({
    url: '/api/ni/pa/overtime/apply/record/getAttInfo',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const renewAttInfo = (params) => {
  return request({
    url: '/api/ni/pa/overtime/apply/record/renewAttInfo',
    method: 'post',
    params: {
      ...params,
      current: 1,
      size: 200,
    }
  })
}

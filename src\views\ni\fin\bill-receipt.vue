<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
      @row-click="rowClick"
    >
      <template #info="{ row, index }">
        <div>
          <span style="color: #303133; font-weight: bolder">销方名称:</span
          >{{ row.salerName }}
        </div>
        <div>
          <span style="color: #303133; font-weight: bolder">销方地址/电话:</span
          >{{ row.salerAddressPhone }}
        </div>
        <div>
          <span style="color: #303133; font-weight: bolder"
            >销方开户行及账号:</span
          >{{ row.salerBankAccount }}
        </div>
      </template>
      <template #taxRate="{ row, index }">
        <span v-if="row.taxRate" style="color: #303133; font-weight: bolder"
          >{{ row.taxRate }}%</span
        >
      </template>
      <template #status="{ row, index }">
        <el-tag v-if="row.status === 1" size="mini" type="info" effect="plain">
          {{ row.$status }}
        </el-tag>
        <el-tag
          v-else-if="row.status === 2"
          size="mini"
          type="success"
          effect="dark"
        >
          {{ row.$status }}
        </el-tag>
        <el-tag
          v-else-if="row.status === 3"
          size="mini"
          type="warning"
          effect="plain"
        >
          {{ row.$status }}
        </el-tag>
        <el-tag
          v-else-if="row.status === 4"
          size="mini"
          type="danger"
          effect="dark"
        >
          {{ row.$status }}
        </el-tag>
        <el-tag v-else size="mini" type="warning" effect="dark">
          {{ row.$status }}
        </el-tag>
      </template>
      <template #brand="{ row, disabled, size, index }">
        <el-tag
          v-if="row.brand"
          :size="size"
          type="success"
          :effect="row.brand === '1' ? 'dark ' : 'plain'"
        >
          {{ brandDictKeyValue[row.brand] }}
        </el-tag>
      </template>
      <template #billType="{ row, size, index }">
        <el-tag
          v-if="row.billType === '1'"
          type="danger"
          :size="size"
          effect="dark"
        >
          {{ billTypeDictKeyValue[row.billType] }}
        </el-tag>
        <el-tag
          v-else-if="row.billType === '2'"
          type="warning"
          :size="size"
          effect="dark"
        >
          {{ billTypeDictKeyValue[row.billType] }}
        </el-tag>
        <el-tag v-else :size="size" effect="dark">
          {{ billTypeDictKeyValue[row.billType] }}
        </el-tag>
      </template>

      <template #cancellationMark="{ row, index }">
        <el-tag v-if="row.cancellationMark === '0'" size="mini" type="success">
          {{ row.$cancellationMark }}
        </el-tag>
        <el-tag v-if="row.cancellationMark === '1'" size="mini" type="warning">
          {{ row.$cancellationMark }}
        </el-tag>
      </template>

      <template #verify="{ row, index }">
        <el-tag v-if="row.verify === '1'" size="mini" type="warning">
          {{ row.$verify }}
        </el-tag>
        <el-tag v-if="row.verify === '2'" size="mini" type="success">
          {{ row.$verify }}
        </el-tag>
        <el-tag v-if="row.verify === '3'" size="mini" type="danger">
          {{ row.$verify }}
        </el-tag>
      </template>

      <template #invoiceType="{ row, disabled, size, index }">
        <el-tag v-if="row.invoiceType === '1'" :size="size" effect="dark">
          蓝字
        </el-tag>
        <el-tag v-else :size="size" type="danger" effect="dark"> 红冲</el-tag>
      </template>
      <template #projectIdForm="{ disabled, size, index, row }">
        <project-select
          v-model="form.projectId"
          :size="size"
          :params="{ status: 9 }"
          :disabled="disabled"
        />
      </template>
      <template #contactIdForm="{ disabled, size, index }">
        <supplier-select
          v-model="form.contactId"
          :size="size"
          :disabled="disabled"
          @submit="handleSupplierSubmit"
        />
      </template>
      <template #invoiceUserIdForm="{ disabled, size, index }">
        <user-select
          v-model="query.invoiceUserId"
          :size="size"
          :disabled="disabled"
        />
      </template>
      <template #invoiceUserIdSearch="{ disabled, size, index }">
        <user-select
          v-model="query.invoiceUserId"
          :size="size"
          :disabled="disabled"
        />
      </template>
      <template #menuLeft>
        <el-button
          type="warning"
          size="mini"
          icon="el-icon-camera"
          plain
          v-if="permission.finInvoice_scan"
          @click="handleInvoiceScan"
          >发票扫描
        </el-button>
        <el-dropdown @command="handleBatch">
          <el-button icon="el-icon-more" type="primary" size="mini">
            批量操作<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="submit" v-if="permission.finInvoice_new"
              >提交
            </el-dropdown-item>
            <el-dropdown-item command="used" v-if="permission.finInvoice_used"
              >已用
            </el-dropdown-item>
            <el-dropdown-item command="del" v-if="permission.finInvoice_delete"
              >删除
            </el-dropdown-item>
            <el-dropdown-item
              command="validate"
              v-if="permission.finInvoice_validate"
              >验证
            </el-dropdown-item>
            <el-dropdown-item
              command="toVoid"
              v-if="permission.finInvoice_toVoid"
              >作废
            </el-dropdown-item>
            <el-dropdown-item v-if="permission.finInvoice_linkSupplier" command="supplier">关联供应商</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-button
          type="warning"
          size="mini"
          icon="el-icon-camera"
          plain
          v-if="permission.finInvoice_export"
          @click="handleInvoiceDetail"
          >导出发票明细
        </el-button>
        <el-divider direction="vertical" />
        <el-tag>
          当前表格已选择
          <span style="font-weight: bolder">{{ selectionList.length }}</span> 项
          <el-button type="text" size="mini" @click="selectionClear">
            清空
          </el-button>
          <template v-if="selectionList.length > 0">
            选中金额:
            <span style="font-weight: bolder; color: #f56c6c">
              {{
                Number(amount).toLocaleString("zh-CN", {
                  minimumFractionDigits: 2,
                })
              }}
            </span>
          </template>
        </el-tag>
      </template>
      <template #menuRight="{ size }">
        <el-button
          icon="el-icon-time"
          circle
          :size="size"
          @click="handleLog"
        ></el-button>
      </template>
      <template #menu="{ row, index, size }" style="width: 200px">
        <el-button
          type="text"
          icon="el-icon-edit"
          :size="size"
          v-if="
            permission.finInvoice_edit &&
            row.status === 1 &&
            (row.createUser === userInfo.user_id ||
              userInfo.role_name.includes('admin'))
          "
          @click="$refs.crud.rowEdit(row, index)"
          >编 辑
        </el-button>
        <el-button
          type="text"
          icon="el-icon-s-promotion"
          :size="size"
          v-if="
            permission.finInvoice_new &&
            row.status === 1 &&
            (row.createUser === userInfo.user_id ||
              userInfo.role_name.includes('admin'))
          "
          @click="rowSubmit(row, index)"
          >提交
        </el-button>
        <el-button
          type="text"
          icon="el-icon-s-promotion"
          :size="size"
          v-if="
            permission.finInvoice_used &&
            row.status === 2 &&
            row.contactId &&
            (row.createUser === userInfo.user_id ||
              userInfo.role_name.includes('admin'))
          "
          @click="rowUse(row, index)"
          >已用
        </el-button>
        <el-button
          type="text"
          icon="el-icon-delete"
          :size="size"
          v-if="
            permission.finInvoice_delete &&
            row.status === 1 &&
            (row.createUser === userInfo.user_id ||
              userInfo.role_name.includes('admin'))
          "
          @click="$refs.crud.rowDel(row, index)"
          >删除
        </el-button>
        <el-button
          type="text"
          icon="el-icon-s-grid"
          size="mini"
          @click.stop="handleDataSub(row)"
          >明细
        </el-button>
        <el-divider direction="vertical" />
        <el-dropdown>
          <el-button type="text" :size="size">
            更多<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item @click.native="rowAttach(row)">
              <i class="el-icon-paperclip"></i>附件管理
            </el-dropdown-item>
            <el-dropdown-item
              type="text"
              icon="el-icon-delete"
              :size="size"
              v-if="permission.finInvoice_delete && [1, 2].includes(row.status)"
              @click.native="rowDel(row)"
            >
              删除
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </template>
      <template #menuForm="{ row, index, type }">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          v-if="type === 'add'"
          @click="$refs.crud.rowSave()"
        >
          新增
        </el-button>
        <el-button
          type="primary"
          icon="el-icon-circle-check"
          size="mini"
          v-if="type === 'edit'"
          @click="$refs.crud.rowUpdate()"
        >
          修改
        </el-button>
        <!--        <el-button-->
        <!--          type="primary"-->
        <!--          icon="el-icon-s-promotion"-->
        <!--          size="mini"-->
        <!--          v-if="['edit', 'add'].includes(type)"-->
        <!--          @click="handleSubmit(type)"-->
        <!--        >-->
        <!--          提交-->
        <!--        </el-button>-->
        <el-button
          icon="el-icon-check"
          size="mini"
          v-if="['edit', 'add'].includes(type)"
          @click="$refs.crud.closeDialog()"
        >
          取消
        </el-button>
      </template>
    </avue-crud>
    <el-drawer
      :title="`[${finInvoiceName}] 详情`"
      :visible.sync="subVisible"
      :direction="direction"
      append-to-body
      :before-close="handleSubClose"
      size="1100px"
    >
      <basic-container>
        <avue-crud
          :option="optionSub"
          :data="dataSub"
          :page.sync="pageSub"
          v-model="formSub"
          :table-loading="loadingSub"
          ref="crudSub"
          @row-del="rowDelSub"
          @row-update="rowUpdateSub"
          @row-save="rowSaveSub"
          :before-open="beforeOpenSub"
          @search-change="searchChangeSub"
          @search-reset="searchResetSub"
          @selection-change="selectionChangeSub"
          @current-change="currentChangeSub"
          @size-change="sizeChangeSub"
          @on-load="onLoadSub"
        >
          <template #taxRate="{ row }">
            <span v-if="row.taxRate">{{ row.taxRate }}%</span>
          </template>
          <template slot="menuLeft">
            <!--optionSub.delBtn-->
            <!--            <el-button-->
            <!--              type="danger"-->
            <!--              size="mini"-->
            <!--              icon="el-icon-delete"-->
            <!--              v-if="optionSub.delBtn"-->
            <!--              plain-->
            <!--              @click="handleDeleteSub"-->
            <!--              >删 除-->
            <!--            </el-button>-->
          </template>
        </avue-crud>
      </basic-container>
    </el-drawer>
    <attach-dialog
      ref="attachDialogRef"
      code="private"
      :detail="attachDetail"
    />
    <log-opt-dialog ref="logOptDialogRef" :module="module" />
    <invoice-scan-dialog ref="invoiceScanDialog" />
    <el-dialog
      title="选择供应商"
      append-to-body
      :visible.sync="supplier.visible"
      width="350px"
    >
      <avue-form
        v-if="supplier.visible"
        :option="supplier.option"
        v-model="supplier.form"
        @submit="handleSupplierLinkSubmit"
      />
    </el-dialog>
  </basic-container>
</template>

<script>
import {
  add,
  back,
  contactLink,
  exportDetails,
  getDetail,
  getPage,
  orderLink,
  remove,
  submit,
  toVoid,
  update,
  used,
  validate,
} from "@/api/ni/fin/invoice";
import {
  add as addSub,
  getDetail as getDetailSub,
  getList as getListSub,
  remove as removeSub,
  update as updateSub,
} from "@/api/ni/fin/invoice-item";
import { mapGetters } from "vuex";
import SupplierSelect from "@/views/ni/base/components/SupplierSelect1";
import PorOrderSelect from "@/views/ni/por/components/OrderSelect";
import ProjectSelect from "@/views/ni/project/components/MyProjectSelect";
import LogOptDialog from "@/components/log-opt-dialog";
import UserSelect from "@/components/user-select";
import AttachDialog from "@/components/attach-dialog";
import InvoiceScanDialog from "@/views/ni/fin/components/InvoiceScanDialog";
import {getList} from "@/api/ni/sd/afterSaleService";

export default {
  components: {
    AttachDialog,
    SupplierSelect,
    PorOrderSelect,
    ProjectSelect,
    LogOptDialog,
    UserSelect,
    InvoiceScanDialog,
  },
  data() {
    return {
      exportData: [], //存储导出数据
      btnDraftDisable: false,
      btnNewDisable: false,
      attachDetail: false,
      isSetup: false,
      module: "ni_fin_bill_receipt",
      form: {},
      query: {},
      loading: true,
      data: [],
      selectionList: [],
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      option: {
        highlightCurrentRow: true,
        saveBtn: false,
        updateBtn: false,
        cancelBtn: false,
        editBtn: false,
        delBtn: false,
        align: "center",
        labelWidth: 120,
        searchIndex: 3,
        searchIcon: true,
        size: "mini",
        searchSize: "mini",
        height: "auto",
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        selection: true,
        selectable: (row) => {
          return row.status !== 4;
        },
        menuWidth: 200,
        column: [
          {
            label: "序号",
            prop: "row",
            width: 55,
            search: false,
            searchRange: true,
            display: false,
          },
          {
            label: "状态",
            prop: "status",
            dicData: [],
            search: true,
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            dataType: "number",
            display: false,
            type: "select",
            minWidth: 70,
            span: 8,
          },
          {
            label: "登记人",
            prop: "createUser",
            type: "select",
            remote: true,
            dicUrl: `/api/blade-user/search/otj/user?name={{key}}`,
            props: {
              label: "realName",
              value: "id",
            },
            dicFormatter: (data) => {
              return data.data.records;
            },
            search: true,
            hide: true,
            showColumn: false,
            display: false,
            span: 8,
          },
          {
            label: "登记人",
            prop: "createUserName",
            overHidden: true,
            display: false,
            minWidth: 80,
            span: 8,
          },
          {
            label: "供应商",
            prop: "contactId",
            type: "select",
            remote: true,
            placeholder: " ",
            dicUrl: `/api/ni/base/supplier/info/page?status=2&keyword={{key}}`,
            props: {
              label: "name",
              value: "id",
              desc: "code",
            },
            dicFormatter: (data) => {
              return data.data.records;
            },
            search: true,
            hide: true,
            showColumn: false,
          },
          {
            label: "供应商名称",
            prop: "contactName",
            minWidth: 120,
            overHidden: true,
          },
          {
            label: "账套",
            prop: "brand",
            type: "radio",
            value: "1",
            dicData: [],
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            minWidth: 70,
            span: 8,
            search: true,
            rules: [
              {
                required: true,
                message: "请选择公司",
                trigger: "blur",
              },
            ],
          },
          {
            label: "发票类型",
            prop: "billType",
            minWidth: 70,
            type: "select",
            span: 8,
            dicUrl: "/api/blade-system/dict-biz/dictionary?code=ni_bill_type",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            placeholder: " ",
            rules: [
              {
                required: true,
                message: "请输入",
                trigger: "blur",
              },
            ],
            change: ({ value }) => {
              if (value === "1") {
                this.form.taxRate = 13;
              } else if (value === "2") {
                this.form.taxRate = 0;
              }
            },
          },
          {
            label: "税率(%)",
            prop: "taxRate",
            placeholder: " ",
            type: "number",
            precision: 2,
            span: 8,
            minWidth: 60,
            controls: false,
          },
          {
            label: "开票日期",
            prop: "date",
            type: "date",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            search: true,
            searchRange: true,
            span: 8,
            minWidth: 85,
            rules: [
              {
                required: true,
                message: "请输入开票日期",
                trigger: "blur",
              },
            ],
          },

          {
            label: "发票号码",
            prop: "serialNo",
            overHidden: true,
            search: true,
            placeholder: " ",
            minWidth: 100,
            span: 8,
            rules: [
              {
                required: true,
                message: "请输入发票号码",
                trigger: "blur",
              },
            ],
          },
          {
            label: "发票代码",
            prop: "code",
            overHidden: true,
            search: false,
            placeholder: " ",
            minWidth: 100,
            span: 8,
          },
          {
            label: "发票抬头",
            prop: "header",
            placeholder: " ",
            hide: true,
            overHidden: true,
            minWidth: 110,
            span: 8,
          },
          {
            label: "发票税号",
            prop: "taxNum",
            placeholder: " ",
            hide: true,
            overHidden: true,
            span: 8,
          },
          {
            label: "金额(含税)",
            prop: "amount",
            type: "number",
            placeholder: " ",
            precision: 2,
            minWidth: 90,
            span: 8,
            hide: true,
            showColumn: false,
            rules: [
              {
                required: true,
                message: "请输入发票金额",
                trigger: "blur",
              },
            ],
          },
          {
            label: "金额(含税)",
            prop: "amountStr",
            placeholder: " ",
            minWidth: 90,
            display: false,
          },
          {
            label: "备注",
            prop: "remark",
            overHidden: true,
            type: "textarea",
            minRows: 2,
            minWidth: 90,
            span: 22,
          },
          {
            label: "作废",
            prop: "cancellationMark",
            type: "select",
            // value: "1",
            dicData: [],
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            minWidth: 70,
            span: 8,
            search: false,
            rules: [
              {
                required: true,
                message: "请选择发票作废标志",
                trigger: "blur",
              },
            ],
          },
          {
            label: "发票验证",
            prop: "verify",
            type: "select",
            // value: "1",
            dicData: [],
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            minWidth: 70,
            span: 8,
            search: false,
            rules: [
              {
                required: true,
                message: "请选择发票验证状态",
                trigger: "blur",
              },
            ],
          },
          {
            label: "发票详情",
            prop: "info",
            width: 200,
            overHidden: true,
            display: false,
          },
          {
            label: "销方名称",
            prop: "salerName",
            overHidden: true,
            display: true,
            search: true,
            minWidth: 110,
            hide: true,
            showColumn: false,
            span: 8,
            rules: [
              {
                required: true,
                message: "请输入销方名称",
                trigger: "blur",
              },
            ],
          },
          {
            label: "销方地址、电话",
            prop: "salerAddressPhone",
            overHidden: true,
            search: false,
            placeholder: " ",
            minWidth: 100,
            hide: true,
            showColumn: false,
            span: 8,
            // rules: [
            //   {
            //     required: true,
            //     message: "请输入销方地址",
            //     trigger: "blur",
            //   },
            // ],
          },
          {
            label: "销方开户行及账号",
            prop: "salerBankAccount",
            overHidden: true,
            search: false,
            placeholder: " ",
            minWidth: 100,
            hide: true,
            showColumn: false,
            span: 8,
            // rules: [
            //   {
            //     required: true,
            //     message: "请输入销方银行账户",
            //     trigger: "blur",
            //   },
            // ],
          },
          {
            label: "创建时间",
            prop: "createTime",
            type: "date",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            hide: true,
            display: false,
            span: 8,
            minWidth: 85,
            placeholder: " ",
          },
          {
            label: "提交日期",
            prop: "applyTime",
            type: "date",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            hide: true,
            display: false,
            span: 8,
            minWidth: 85,
            placeholder: " ",
          },
          {
            label: "已用日期",
            prop: "usedTime",
            type: "date",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            hide: true,
            display: false,
            span: 8,
            minWidth: 85,
            placeholder: " ",
          },
        ],
      },
      subVisible: false,
      direction: "rtl",
      invoice: {},
      finInvoiceName: "发票",
      formSub: {},
      querySub: {},
      loadingSub: true,
      dataSub: [],
      selectionListSub: [],
      pageSub: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      optionSub: {
        showSummary: true,
        sumColumnList: [
          {
            name: "num",
            type: "sum",
            decimals: 1,
          },
          {
            name: "amount",
            type: "sum",
            decimals: 1,
          },
          {
            name: "taxAmount",
            type: "sum",
            decimals: 1,
          },
        ],
        menuWidth: 180,
        editBtn: true,
        delBtn: true,
        menu: true,
        align: "center",
        searchIndex: 3,
        searchIcon: true,
        size: "mini",
        searchSize: "mini",
        height: "auto",
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        selection: true,
        column: [
          {
            label: "名称",
            prop: "materialName",
            cell: true,
            type: "textarea",
            minRows: 1,
            placeholder: " ",
            overHidden: true,
          },
          {
            label: "规格",
            prop: "specification",
            overHidden: true,
            cell: true,
            type: "textarea",
            minRows: 1,
            placeholder: " ",
          },
          {
            label: "单位",
            prop: "unit",
            cell: true,
            placeholder: " ",
            width: 70,
          },
          {
            label: "数量",
            prop: "num",
            type: "number",
            precision: 2,
            cell: true,
            placeholder: " ",
            controls: false,
            change: ({ row, value }) => {
              if (row.amount) {
                row.price = (Number(value) * Number(row.amount)).toFixed(2);
              } else {
                row.price = 0;
              }
            },
          },
          {
            label: "单价",
            prop: "priceStr",
            placeholder: " ",
            controls: false,
            display: false,
          },
          {
            label: "金额",
            prop: "amount",
            type: "number",
            precision: 2,
            hide: true,
            showColumn: false,
            cell: true,
            placeholder: " ",
            controls: false,
            change: ({ row, value }) => {
              if (row.num) {
                row.price = (Number(value) / Number(row.num)).toFixed(2);
                row.taxAmount = (
                  (Number(value) * Number(row.taxRate)) /
                  100
                ).toFixed(2);
              } else {
                row.price = 0;
              }
            },
          },
          {
            label: "金额",
            prop: "amountStr",
            placeholder: " ",
            display: false,
          },
          {
            label: "税率(%)",
            prop: "taxRate",
            type: "number",
            placeholder: " ",
            controls: false,
            change: ({ value }) => {
              if (value != null && value !== 0) {
                this.formSub.taxAmount =
                  ((this.formSub.amount / (100 + value)) * value) / 100;
              } else if (value === 0) {
                this.formSub.taxAmount = 0;
              }
            },
          },
          {
            label: "税额",
            prop: "taxAmount",
            type: "number",
            precision: 2,
            placeholder: " ",
            controls: false,
          },
          {
            label: "备注",
            prop: "remark",
            overHidden: true,
            type: "textarea",
            minRows: 2,
            span: 24,
            cell: true,
            placeholder: " ",
          },
        ],
      },
      brandDict: [],
      brandDictKeyValue: {},
      statusDict: [],
      statusDictKeyValue: {},
      billTypeDict: [],
      billTypeDictKeyValue: {},
      supplier: {
        visible: false,
        option: {
          labelPosition: "top",
          span: 24,
          size: "mini",
          emptyBtn: false,
          column: [
            {
              label: "选择供应商",
              prop: "contactId",
              type: "select",
              remote: true,
              placeholder: " ",
              dicUrl: `/api/ni/base/supplier/info/page?status=2&keyword={{key}}`,
              props: {
                label: "name",
                value: "id",
                desc: "code",
              },
              dicFormatter: (data) => {
                return data.data.records;
              },
              rules: [
                {
                  required: true,
                  message: "请选择供应商",
                  trigger: "blur",
                },
              ],
            },
          ],
        },
        form: {},
      },
    };
  },
  computed: {
    ...mapGetters(["userInfo", "permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.finInvoice_add, false),
        viewBtn: this.vaildData(this.permission.finInvoice_view, false),
        delBtn: this.vaildData(this.permission.finInvoice_delete, false),
        editBtn: this.vaildData(this.permission.finInvoice_edit, false),
      };
    },
    amount() {
      return (
        this.selectionList
          .map((item) => item.amount)
          .reduce((prev, curr) => prev + Number(curr) * 10000, 0) / 10000
      );
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
    subIds() {
      let ids = [];
      this.selectionListSub.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  mounted() {
    this.dicInit();
  },
  methods: {
    handleBatch(command) {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      if (command === "submit") this.handleSubmit();
      else if (command === "del") this.handleDelete();
      else if (command === "used") this.handleUsed();
      else if (command === "toVoid") this.handleToVoid();
      else if (command === "validate") this.handleValidate();
      else if (command === "supplier") this.handleSupplierLink();
    },
    handleInvoiceScan() {
      this.$refs.invoiceScanDialog.visible = true;
    },
    handleOrderLinkSubmit(form, done) {
      orderLink(form)
        .then(() => {
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.orderLinkVisible = false;
          this.onLoad(this.page);
        })
        .finally(() => {
          done();
        });
    },
    handleOrderLink() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择要关联的数据");
        return;
      }
      const suppliers = new Set();
      this.selectionList.forEach((item) => {
        suppliers.add(item.contactId);
      });
      if (suppliers.size > 1) {
        this.$message.warning("请选择同一供应商的发票");
        return;
      }
      this.orderLinkForm = {
        ids: this.selectionList.map((item) => item.id).join(","),
        supplierId: this.selectionList[0].contactId,
        serialNo: this.selectionList.map((item) => item.serialNo).join(","),
      };
      this.orderLinkVisible = true;
    },
    rowAttach(row) {
      //控制上传、删除按钮隐藏,新加的功能----
      if (row.status === 1 && row.createUser === this.userInfo.user_id) {
        this.attachDetail = false;
      } else {
        this.attachDetail = true;
      }
      this.$refs.attachDialogRef.option.editBtn = false;
      this.$refs.attachDialogRef.attachOption.editBtn = false;
      //--------
      this.$refs.attachDialogRef.init(row.id, this.module);
    },
    handleLog() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择要查看的数据");
        return;
      } else if (this.selectionList.length > 1) {
        this.$message.warning("只能选择一条数据");
        return;
      }
      this.$refs.logOptDialogRef.init(this.selectionList[0].id);
    },
    handleOrderSubmit(selectList) {
      this.form.orderId = selectList[0].id;
      this.form.contactId = selectList[0].supplierId;
      this.form.contactName = selectList[0].supplier;
    },
    handleSubmit() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm(
        "此操作将选择数据状态修改为<span style='color:#F56C6C;font-weight: bold'>已提交</span>，是否继续?",
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          dangerouslyUseHTMLString: true,
          type: "warning",
        }
      )
        .then(() => {
          return submit(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    handleUsed() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      const statusErr = this.selectionList.some((item) => item.status !== 2);
      if (statusErr) {
        this.$message.warning("选择的数据中存在非提交的数据，请重新选择");
        return;
      }
      const unContact = this.selectionList.some((item) => !item.contactId);
      if (unContact) {
        this.$message.warning("选择的数据中存在未选择供应商的数据，请重新选择");
        return;
      }
      this.$confirm(
        "此操作将选择数据状态修改为<span style='color:#F56C6C;font-weight: bold'>已用</span>，是否继续?",
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
          dangerouslyUseHTMLString: true,
        }
      )
        .then(() => {
          return used(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    handleValidate() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      const statusErr = this.selectionList.some((item) => item.verify !== 2);
      if (statusErr) {
        this.$message.warning("选择的数据中存在已验证的数据，请重新选择");
        return;
      }
      this.$confirm("此操作将验证选中的发票，是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        dangerouslyUseHTMLString: true,
      })
        .then(() => {
          return validate(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    handleToVoid() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm(
        "此操作将选择数据标记为<span style='color:#F56C6C;font-weight: bold'>作废</span>，是否继续?",
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
          dangerouslyUseHTMLString: true,
        }
      )
        .then(() => {
          return toVoid(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    handleSupplierLink() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.supplier.visible = true;
    },
    handleSupplierLinkSubmit(form, done) {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        this.supplier.visible = false;
        done();
      }
      contactLink(form.contactId, this.ids)
        .then(() => {
          this.supplier.visible = false;
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.onLoad(this.page);
        })
        .finally(() => {
          done();
        });
    },
    rowUse(row) {
      if (row.status !== 2) {
        this.$message.warning("选择的数据中非提交的数据，请重新选择");
        return;
      }
      if (!row.contactId) {
        this.$message.warning("选择的数据未选择供应商，请重新选择");
        return;
      }
      this.$confirm("确认将该数据标记为已用?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return used(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    rowBack(row) {
      this.$confirm("此操作撤回提交的数据，是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return back(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    rowSubmit(row, index) {
      this.$confirm("确认提交该数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        center: true,
      })
        .then(() => {
          return submit(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    handleSupplierSubmit(selectList) {
      this.form.contactId = selectList[0].id;
      this.form.orderId = null;
      this.form.orderTitle = null;
    },
    dicInit() {
      this.$http
        .get("/api/blade-system/dict-biz/dictionary?code=ni_fin_invoice_status")
        .then((res) => {
          const column = this.findObject(this.option.column, "status");
          column.dicData = res.data.data;
          this.statusDict = res.data.data;
          this.statusDictKeyValue = this.statusDict.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });
      this.$http
        .get("/api/blade-system/dict-biz/dictionary?code=ni_brand")
        .then((res) => {
          const column = this.findObject(this.option.column, "brand");
          column.dicData = res.data.data;
          this.brandDict = res.data.data;
          this.brandDictKeyValue = this.brandDict.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });
      this.$http
        .get("/api/blade-system/dict-biz/dictionary?code=ni_bill_type")
        .then((res) => {
          const column = this.findObject(this.option.column, "billType");
          column.dicData = res.data.data;
          this.billTypeDict = res.data.data;
          this.billTypeDictKeyValue = this.billTypeDict.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });
      this.$http
        .get("/api/blade-system/dict-biz/dictionary?code=ni_invoice_verify")
        .then((res) => {
          const column = this.findObject(this.option.column, "verify");
          column.dicData = res.data.data;
        });
      //作废标志
      this.$http
        .get(
          "/api/blade-system/dict-biz/dictionary?code=ni_invoice_cancellation"
        )
        .then((res) => {
          const column = this.findObject(
            this.option.column,
            "cancellationMark"
          );
          column.dicData = res.data.data;
        });
    },
    // 主表模块
    rowSave(row, done, loading) {
      add(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          window.console.log(error);
          loading();
        }
      );
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          window.console.log(error);
          loading();
        }
      );
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    //获取搜索的导出数据
    async getExportData() {
      const promises = [];
      this.exportData = [];
      for (var i = 1; i <= this.page.total / this.page.pageSize + 1; i++) {
        if (this.query.date != null && this.query.date.length === 2) {
          this.query.startDate = this.query.date[0] + " 00:00:00";
          this.query.endDate = this.query.date[1] + " 23:59:59";
          this.query.date = null;
        }
        this.query.type = "2";
        this.query.descs = "id";
        const promise = getPage(i, this.page.pageSize, {
          ...this.params,
          ...this.query,
          descs: "id",
        }).then((res) => {
          const data = res.data.data.records;
          this.exportData = this.exportData.concat(data);
        });
        promises.push(promise);
      }
      // 等待所有异步请求完成
      await Promise.all(promises);
      console.log(this.exportData);
      return this.exportData;
    },

    /**
     * 导出发票明细
     */
    async handleInvoiceDetail() {
      // let data = await this.getExportData();
      // let ids = [];
      // data.forEach((ele) => {
      //   ids.push(ele.id);
      // });
      // exportDetails(ids.join(",")).then((res) => {
      //   // navigator.clipboard.writeText(res.data.data);
      //   this.$Clipboard({
      //     text: res.data.data,
      //   })
      //     .then(() => {
      //       // this.$message.success('拷贝xml成功')
      //       this.$message.success("导出发票明细完成。");
      //     })
      //     .catch(() => {
      //       this.$message({
      //         type: "waning",
      //         message: "该浏览器不支持自动复制",
      //       });
      //     });
      // });
      let msg =
        '是否导出<span style="color: #F56C6C;font-weight: bold">所有筛选的数据</span>?';
      if (this.selectionList.length > 0) {
        msg =
          '是否要导出<span style="color: #F56C6C;font-weight: bold">当前选中的数据</span>？';
      }
      this.$confirm(msg, {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        dangerouslyUseHTMLString: true,
        type: "warning",
      }).then(() => {
        this.handleExportData();
      });
    },
    async handleExportData() {
      let opt = {
        column: [
          {
            label: "发票号码",
            prop: "serialNo",
          },
          {
            label: "发票代码",
            prop: "code",
          },
          {
            label: "开票日期",
            prop: "date",
          },
          {
            label: "发票头",
            prop: "header",
          },
          {
            label: "发票金额",
            prop: "amount",
          },


          {
            label: "货物名称",
            prop: "materialName",
          },
          {
            label: "规格型号",
            prop: "specification",
          },
          {
            label: "单位",
            prop: "unit",
          },
          {
            label: "数量",
            prop: "num",
          },
          {
            label: "单价",
            prop: "price",
          },
          {
            label: "金额",
            prop: "dtAmount",
          },
          {
            label: "税率(%)",
            prop: "taxRate",
          },
          {
            label: "税额",
            prop: "taxAmount",
          },
          {
            label: "备注",
            prop: "remark",
          }
        ],
      };

      if (this.selectionList.length > 0) {
        this.exportData = this.selectionList;
      }else{
        await this.getExportData();
      }

      let ids = [];
      this.exportData.forEach((ele) => {
        ids.push(ele.id);
      });

      await exportDetails(ids.join(",")).then((res) => {
        this.exportData = res.data.data
        // console.log(res.data.data)
      });

      console.log(this.exportData)

      this.$Export.excel({
        title: "发票明细",
        columns: opt.column,
        data: this.exportData.map((item) => {
          return {
            ...item,
            // statusName: this.statusDictKeyValue[item.status],
            // inspectorName: this.expressTypeDictKeyValue[item.inspector],
            // expressCompanyName: this.expressCompanyDictKeyValue[item.expressCompany],
            // paymentTypeDictName: this.paymentTypeDictKeyValue[item.paymentType],
            // expressTypeName: this.expressTypeDictKeyValue[item.expressType],
          };
        }),
      });
      this.exportData = [];
    },

    //获取搜索的打印数据
    // async getExportData() {-
    //   const promises = [];
    //   this.exportData = [];
    //   for (var i = 1; i <= this.page.total / this.page.pageSize + 1; i++) {
    //     const promise = getList(i, this.page.pageSize, {
    //       ...this.params,
    //       ...this.query,
    //     }).then((res) => {
    //       const data = res.data.data.records;
    //       this.exportData = this.exportData.concat(data);
    //     });
    //
    //     promises.push(promise);
    //   }
    //   // 等待所有异步请求完成
    //   await Promise.all(promises);
    //   // console.log(this.exportData)
    //   return this.exportData;
    // },

    /**
     * 多选删除
     */
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      const statusErr = this.selectionList.some((item) => item.status > 1);
      if (statusErr) {
        this.$message.warning("选中的数据存在非草稿的数据，请重新选择");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen(done, type) {
      if ("add" === type) {
        this.form.type = "2";
      }
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then((res) => {
          this.form = res.data.data;
        });
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.form = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    onLoad(page, params = {}) {
      this.loading = true;
      const query = Object.assign(params, this.query);
      if (query.date != null && query.date.length === 2) {
        query.startDate = query.date[0] + " 00:00:00";
        query.endDate = query.date[1] + " 23:59:59";
        query.date = null;
      }
      query.type = "2";
      query.descs = "id";
      getPage(page.currentPage, page.pageSize, query).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        data.records.forEach((item) => {
          if (item.amount) {
            item.amountStr = Number(item.amount).toLocaleString("zh-CN", {
              minimumFractionDigits: 2,
            });
          }
        });
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
    rowClick(row) {
      this.$refs.crud.toggleSelection([row]);
    },
    // 子表模块
    handleDataSub(row) {
      this.subVisible = true;
      this.invoice = row;

      if (row.status === 1 && row.createUser === this.userInfo.user_id) {
        this.optionSub.addBtn1 = true;
        this.optionSub.delBtn = true;
        this.optionSub.editBtn = true;
      } else {
        this.optionSub.addBtn1 = false;
        this.optionSub.delBtn = false;
        this.optionSub.editBtn = false;
      }

      this.onLoadSub(this.pageSub);
    },
    handleSubClose(hide) {
      hide();
    },
    rowSaveSub(row, loading, done) {
      row = {
        ...row,
        invoiceId: this.invoice.id,
      };
      addSub(row).then(
        () => {
          loading();
          this.onLoadSub(this.pageSub);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        },
        (error) => {
          done();
          window.console.log(error);
        }
      );
    },
    rowUpdateSub(row, index, loading, done) {
      row = {
        ...row,
        invoiceId: this.invoice.id,
      };
      updateSub(row).then(
        () => {
          loading();
          this.onLoadSub(this.pageSub);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        },
        (error) => {
          done();
          window.console.log(error);
        }
      );
    },
    rowDelSub(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return removeSub(row.id);
        })
        .then(() => {
          this.onLoadSub(this.pageSub);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    handleAddSub() {
      this.$refs.crudSub.rowCellAdd({
        taxRate: this.invoice.taxRate,
        invoiceId: this.invoice.id,
      });
    },
    handleDeleteSub() {
      if (this.selectionListSub.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return removeSub(this.subIds);
        })
        .then(() => {
          this.onLoadSub(this.pageSub);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crudSub.toggleSelection();
        });
    },
    beforeOpenSub(done, type) {
      if ("add" === type) {
        this.form.taxRate = this.invoice.taxRate;
      } else if (["edit", "view"].includes(type)) {
        getDetailSub(this.formSub.id).then((res) => {
          this.formSub = res.data.data;
        });
      }
      done();
    },
    searchResetSub() {
      this.querySub = {};
      this.onLoadSub(this.pageSub);
    },
    searchChangeSub(params) {
      this.querySub = params;
      this.onLoadSub(this.pageSub, params);
    },
    selectionChangeSub(list) {
      this.selectionListSub = list;
    },
    currentChangeSub(currentPage) {
      this.pageSub.currentPage = currentPage;
    },
    sizeChangeSub(pageSize) {
      this.pageSub.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoadSub(page, params = {}) {
      this.loadingSub = true;
      const values = {
        ...params,
        invoiceId: this.invoice.id,
      };
      getListSub(
        page.currentPage,
        page.pageSize,
        Object.assign(values, this.querySub)
      ).then((res) => {
        const data = res.data.data;
        this.pageSub.total = data.total;
        data.records.forEach((item) => {
          if (item.amount) {
            item.amountStr = Number(item.amount).toLocaleString("zh-CN", {
              minimumFractionDigits: 2,
            });
            item.priceStr = Number(item.price).toLocaleString("zh-CN", {
              minimumFractionDigits: 2,
            });
          }
        });
        this.dataSub = data.records;
        this.selectionListSub = [];
        this.loadingSub = false;
      });
    },
  },
};
</script>

<style></style>

export default {
    labelWidth: 120,
    calcHeight: 30,
    size: "mini",
    menuBtn: true,
    column: [
      {
        type: "input",
        label: "合同id",
        span: 12,
        display: false,
        prop: "contractId",
      },
      {
        type:"input",
        label:"归档编号",
        prop:"archiveNo",
        span:8,
        display:false,
        readonly:true,
        disabled:true,
      },
      {
        type:"input",
        label:"归档编号",
        prop:"archiveSerialNo",
        span:8,
        display:true,
        readonly:true,
        disabled:true,
      },
      {
        type: "input",
        label: "发起人",
        span: 8,
        display: false,
        prop: "createUserName",
        readonly: true,
        disabled:true,
      },
      {
        type: "input",
        label: "部门",
        span: 8,
        display: false,
        prop: "createDeptName",
        readonly: true,
        disabled:true,
      },
      {
        type: "input",
        label: "合同名称",
        span: 8,
        display: true,
        prop: "contractTitle",
        readonly: true,
        disabled:true,
      },
      {
        type: "input",
        label: "合同编号",
        span: 8,
        display: true,
        prop: "contractNo",
        readonly: true,
        disabled:true,
      },
      {
        type: "input",
        label: "合同申请人",
        span: 8,
        display: false,
        prop: "contractUserName",
        readonly: true,
        disabled:true,
      },
      {
        type: "input",
        label: "供应商",
        span: 8,
        display: false,
        prop: "supplierName",
        readonly: true,
        disabled:true,
      },
      {
        type: "input",
        label: "合同金额",
        span: 8,
        display: false,
        prop: "contractAmount",
        readonly: true,
        disabled:true,
      },
      {
        type: "input",
        label: "签订日期",
        span: 8,
        display: false,
        prop: "contractDate",
        readonly: true,
        disabled:true,
      },
      {
        type: "textarea",
        label: "备注",
        span: 24,
        display: false,
        prop: "remark",
      },
      // {
      //   label: "合同归档附件",
      //   type: "upload",
      //   listType: 'picture-card',
      //   propsHttp: {
      //     res: "data",
      //     url: "attachId",
      //     name: "originalName",
      //   },
      //   action: "/api/blade-resource/oss/endpoint/put-file-attach",
      //   display: true,
      //   hide: true,
      //   showColumn: false,
      //   span: 24,
      //   showFileList: true,
      //   dragFile: true,
      //   multiple: true,
      //   limit: 10,
      //   prop: "attachment",
      //   rules: [
      //       {
      //         required: true,
      //         message: "请上传附件",
      //         trigger: "blur",
      //       },
      //     ],
      // },
      {
        label: '附件上传',
        prop: "attachment",
        type: 'upload',
        multiple: true,
        listType: 'picture-card',
        span: 24,
        propsHttp: {
          res: "data",
          url: "attachId",
        },
        action: "/api/blade-resource/oss/endpoint/put-file-attach",
      }
    ],
  }
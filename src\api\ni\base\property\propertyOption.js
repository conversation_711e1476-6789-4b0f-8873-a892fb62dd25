import request from "@/router/axios";

export const getOption = (id) => {
  return request({
    url: "/api/ni/base/propertyOption/config",
    method: "get",
    params: {
      id,
    },
  });
};

export const getList = (current, size, params) => {
  return request({
    url: "/api/ni/base/propertyOption/list",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/ni/base/propertyOption/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const remove = (ids) => {
  return request({
    url: "/api/ni/base/propertyOption/remove",
    method: "post",
    params: {
      ids,
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/ni/base/propertyOption/submit",
    method: "post",
    data: row,
  });
};

export const update = (row) => {
  return request({
    url: "/api/ni/base/propertyOption/submit",
    method: "post",
    data: row,
  });
};

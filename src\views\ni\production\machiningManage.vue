<template>
  <basic-container>
    <avue-crud :option="upn"
               :table-loading="loading"
               :data="data"
               :page.sync="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @refresh-change="refreshChange"
               @on-load="onLoad">
      <template #urgent="{ row, index }">
        <el-tag v-if="row.urgent === '是'" size="mini" type="danger" effect="dark">
          是
        </el-tag>
        <el-tag v-if="row.urgent === '否'" size="mini" type="success" effect="dark">
          否
        </el-tag>
      </template>
      <template #projectIdForm="{ disabled, size, index, row }">
        <project-select
          v-model="form.projectId"
          :size="size"
          :params="{ status: 9 }"
          :disabled="disabled"
          @confirm="projectConfirm"
        />
      </template>
      <template slot="menuLeft">
        <el-button type="danger"
                   size="small"
                   icon="el-icon-delete"
                   plain
                   v-if="permission.machiningManage_delete"
                   @click="handleDelete">删 除
        </el-button>
        <el-button type="danger"
                   size="small"
                   icon="el-icon-delete"
                   plain
                   v-if="permission.machiningManage_apply"
                   @click="showLoadDialog">导 入
        </el-button>
        <el-button
          size="mini"
          icon="el-icon-download"
          type="success"
          v-if="permission.machiningManage_export"
          @click="handleExport"
        >导出
        </el-button>
        <el-button type="success"
                   size="small"
                   plain
                   v-if="permission.machiningManage_apply"
                   @click="handleNew">新
        </el-button>
        <el-button type="success"
                   size="small"
                   plain
                   v-if="permission.machiningManage_examineApprove"
                   @click="handleWaitingProcessing">待加工
        </el-button>
        <el-button type="success"
                   size="small"
                   plain
                   v-if="permission.machiningManage_examineApprove"
                   @click="handleProcessing">正在加工
        </el-button>
        <el-button type="success"
                   size="small"
                   plain
                   v-if="permission.machiningManage_examineApprove"
                   @click="handleOutsideProcessing">正在外协
        </el-button>
        <el-button type="success"
                   size="small"
                   plain
                   v-if="permission.machiningManage_delete && false"
                   @click="handleEntered">入库
        </el-button>
        <el-button type="success"
                   size="small"
                   plain
                   v-if="permission.machiningManage_delete && false"
                   @click="handleOutside">出库
        </el-button>
        <el-button type="danger"
                   size="small"
                   icon="el-icon-delete"
                   plain
                   v-if="permission.machiningManage_delete && false"
                   @click="handleTest">技 术
        </el-button>
        <el-button type="danger"
                   size="small"
                   icon="el-icon-delete"
                   plain
                   v-if="permission.machiningManage_delete && false"
                   @click="handleTest1">工 艺
        </el-button>
        <el-tag>
          当前表格已选择
          <span style="font-weight: bolder">{{ selectionList.length }}</span> 项
          <el-button type="text" size="mini" @click="selectionClear">
            清空
          </el-button>
        </el-tag>

        <el-divider direction="vertical"></el-divider>
        <el-radio-group v-model="tabsNo" size="mini" @input="onLoad(page)">
          <el-radio-button label="own">本人</el-radio-button>
          <el-radio-button label="all">全部</el-radio-button>
        </el-radio-group>
        <el-divider direction="vertical" />

        <el-divider direction="vertical"></el-divider>
        <el-radio-group v-model="tabsNo" size="mini" @input="onLoad(page)">
          <el-radio-button label="in">已入库</el-radio-button>
          <el-radio-button label="out">已出库</el-radio-button>
        </el-radio-group>
        <el-divider direction="vertical" />

      </template>

      <template #menu="{row,index,size}">
        <el-button type="text"
                   icon="el-icon-edit"
                   :size="size"
                   v-if="([1].includes(row.status) && permission.machiningManage_edit
                   && permission.machiningManage_apply
                   && row.createUser === userInfo.user_id)
                   || ([2,6].includes(row.status) //&& permission.machiningManage_edit
                   && permission.machiningManage_examineApprove)"
                   @click="$refs.crud.rowEdit(row,index);undo=false">编 辑
        </el-button>
        <el-button type="text"
                   icon="el-icon-refresh"
                   :size="size"
                   v-if="([6,7].includes(row.status)
                   && permission.machiningManage_apply)"
                   @click="$refs.crud.rowEdit(row,index);undo=true">再 做
        </el-button>
        <el-button type="text"
                   icon="el-icon-refresh"
                   :size="size"
                   v-if="([2].includes(row.status) && row.createUser === userInfo.user_id
                   && permission.machiningManage_apply)"
                   @click="handleDraft(row)">撤 回
        </el-button>
        <el-button type="text"
                   icon="el-icon-time"
                   :size="size"
                   v-if="([6,7].includes(row.status) && permission.machiningManage_examineApprove)"
                   @click="showDialog(row)">工 时
        </el-button>
        <el-divider direction="vertical" />
        <el-dropdown>
          <el-button type="text" :size="size">
            更多<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item type="text"
                              icon="el-icon-time"
                              :size="size"
                              @click.native="rowLog(row,index)">
              操作日志
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </template>
      <template #menuRight="{size}">
        <el-button icon="el-icon-time" circle :size="size" @click="handleLog"></el-button>
      </template>
      <template #menuForm="{row,index,type}">
        <el-button
          type="primary"
          icon="el-icon-circle-check"
          size="mini"
          v-if="type==='edit' && !undo"
          @click="$refs.crud.rowSave()"
        >
          修改
        </el-button>
        <el-button
          type="primary"
          icon="el-icon-refresh"
          size="mini"
          v-if="type==='edit' && undo"
          @click="undof()"
        >
          再做
        </el-button>

      </template>

    </avue-crud>
    <log-opt-dialog ref="logOptDialogRef" :module="module"/>
    <el-dialog
      title="导入验证数据"
      :visible.sync="centerDialogVisible"
      width="30%"
      center
      append-to-body>
      <el-form :model="form">
        <el-form-item >
          <el-input type="textarea"
                    :rows="3"
                    placeholder="请输入内容"
                    v-model="textarea">
          </el-input>
        </el-form-item>
      </el-form>
      导入模板：
      <el-button type="primary" size="small" @click="handleTemplate">
      点击下载<i class="el-icon-download el-icon--right"></i>
      </el-button>
      <span slot="footer" class="dialog-footer">
        <el-button @click="textarea = ''">清 空</el-button>
        <el-button @click="centerDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleImportData">确 定</el-button>
     </span>

    </el-dialog>
    <el-drawer
      title="工时明细"
      :visible.sync="visible"
      :direction="direction"
      size="40%"
      append-to-body
      :before-close="handleClose"
    >
      <template border>
        <el-table class="center-table"
          :data="tableData"
          border
          style="width: 98%;">
          <el-table-column
            prop="process"
            label="工序"
            width="50">
          </el-table-column>
          <el-table-column
            prop="machineType"
            label="机床类型"
            width="120">
          </el-table-column>
          <el-table-column
            prop="processingPersonnel"
            label="加工人"
            width="80">
          </el-table-column>
          <el-table-column
            prop="processingTime"
            label="工时(分钟)"
            width="100">
          </el-table-column>
          <el-table-column
            prop="processingDate"
            label="加工时间"
            show-overflow-tooltip="true"
            width="120">
          </el-table-column>
          <el-table-column
            prop="remark"
            label="备注">
          </el-table-column>
        </el-table>
      </template>
    </el-drawer>
  </basic-container>
</template>

<script>
import {
  getList,
  getDetail,
  add,
  update,
  remove,
  importData,
  changeStatus,
  toNew, getGsList
} from "@/api/ni/production/machiningManage";
  import {mapGetters} from "vuex";
  import ProjectSelect from "@/views/ni/project/components/MyProjectSelect";
import {exportBlob} from "@/api/common";
import {getToken} from "@/util/auth";
import {downloadXls} from "@/util/util";
import LogOptDialog from "@/components/log-opt-dialog/index.vue";

  export default {
    components: {
      LogOptDialog,
      ProjectSelect
    },
    data() {
      return {
        module: 'ni_production_machining_manage',
        statusDictKeyValue: {},

        tableData:null,
        visible: false,
        tabsNo: "own",
        redo: false,
        textarea: "",
        centerDialogVisible: false,
        lx: 0,
        form: {},
        query: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        option1: {//技术
          searchEnter: true,
          delBtn: false,
          editBtn: false,
          updateBtn: false,
          dialogDrag: true,
          align: "center",
          height: 'auto',
          calcHeight: 30,
          searchLabelWidth: 120,
          labelWidth: 120,
          tip: false,
          searchShow: true,
          searchMenuSpan: 6,
          border: true,
          index: false,
          viewBtn: true,
          selection: true,
          searchIndex: 3,
          searchIcon: true,
          dialogClickModal: false,
           column: [
            {
              label: "状态",
              prop: "status",
              type: "select",
              dicUrl: "/api/blade-system/dict-biz/dictionary?code=ni_production_machining_manage_status",
              dataType: "number",
              props: {
                label: "dictValue",
                value: "dictKey"
              },
              search: true,
              addDisplay: false,
              editDisplay: false,
            },
            {
              label: "分类",
              prop: "classify",
              type: "select",
              // multiple: true,//可以多选
              search: false,
              dicData: [
                {
                  label: "项目设计",
                  value: "项目设计",
                },
                {
                  label: "生产维修",
                  value: "生产维修",
                },
                {
                  label: "返修返工",
                  value: "返修返工",
                },
                {
                  label: "其他",
                  value: "其他",
                },
              ],
              rules:[{
                required:true,
                message:"请选择分类",
                trigger:"blur"
              }],
            },
            {
              label: "是否紧急",
              prop: "urgent",
              type: "select",
              search: true,
              value: "否",
              dicData: [
                {
                  label: "是",
                  value: "是",
                },
                {
                  label: "否",
                  value: "否",
                },
              ],
              rules:[{
                required:true,
                message:"请选择是否紧急",
                trigger:"blur"
              }],
            },
            {
              label: "待加工部件名称",
              prop: "componentName",
              type: "input",
              labelWidth: 125,
              search: true,
              // overHidden: true,
              minWidth: 110,
              // order: 10,
              rules:[{
                required:true,
                message:"请选择待加工部件名称",
                trigger:"blur"
              }],
            },
            {
              label: "图纸代号",
              prop: "drawingCode",
              type: "input",
              search: true,
              overHidden: true,
              rules:[{
                required:true,
                message:"请选择图纸代号",
                trigger:"blur"
              }],
            },
            {
              label: "项目负责人",
              prop: "projectLeader",
              type: "input",
              disabled: true,
              search: true,
              // addDisplay: false,
              // editDisplay: false,
            },
            {
              label: "项目号",
              prop: "projectNo",
              type: "input",
              search: true,
              overHidden: true,
              minWidth: 100,

              display: false,

              // rules:[{
              //   required:true,
              //   message:"请选择项目号",
              //   trigger:"blur"
              // }],
            },
             {
               label: "项目号",
               prop: "projectId",
               type: "input",
               placeholder: " ",
               align: "left",
               hide: true,
               showColumn: false,
               overHidden: true,

               rules:[{
                 required:true,
                 message:"请选择项目号",
                 trigger:"blur"
               }],
             },
            {
              label: "规格",
              prop: "specifications",
              type: "input",
              rules:[{
                required:true,
                message:"请选择规格",
                trigger:"blur"
              }],
            },
            {
              label: "材质",
              prop: "materialQuality",
              type: "input",
              rules:[{
                required:true,
                message:"请选择材质",
                trigger:"blur"
              }],
            },
            {
              label: "需求数量",
              prop: "num",
              type: "number",
              min: 1,
              search: false,
              rules:[{
                required:true,
                message:"请选择需求数量",
                trigger:"blur"
              }],
            },
            {
              label: "单位",
              prop: "unit",
              type: "select",
              value: "件",
              minWidth: 50,
              dicData: [
                {
                  label: "件",
                  value: "件",
                },
              ],
              rules:[{
                required:true,
                message:"请选择单位",
                trigger:"blur"
              }],
            },
            {
              label: "需求时间",
              prop: "demandTime",
              type: "datetime",
              format: "yyyy-MM-dd HH:mm:ss",
              valueFormat: "yyyy-MM-dd HH:mm:ss",
              search: false,
              overHidden: true,
              minWidth: 100,
              rules:[{
                required:true,
                message:"请选择需求数量",
                trigger:"blur"
              }],
            },
            {
              label: "加工备注",
              prop: "processingRemarks",
              type: "textarea",
              search: true,
              minWidth: 200,
              overHidden: true,
            },
             {
               label: "入库时间",
               prop: "storageTime",
               type: "datetime",
               format: "yyyy-MM-dd HH:mm:ss",
               valueFormat: "yyyy-MM-dd HH:mm:ss",
               overHidden: true,
               addDisplay: false,
               editDisplay: false,
               search: true,
             },
            {
              label: "加工方式",
              prop: "processingMethod",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              search: false,
              overHidden: true,
            },
            {
              label: "是否外协",
              prop: "outsourced",
              type: "select",
              search: false,
              addDisplay: false,
              editDisplay: false,
              dicData: [
                {
                  label: "是",
                  value: "是",
                },
                {
                  label: "否",
                  value: "否",
                },
              ],
              change: ({ value }) => {
                if (value == ("是")) {
                  this.form.outsourcingClassification = "常规外协";
                }else{
                  this.form.outsourcingClassification = "";
                }
              },
            },
            {
              label: "外协分类",
              prop: "outsourcingClassification",
              type: "select",
              search: false,
              addDisplay: false,
              editDisplay: false,
              dicData: [
                {
                  label: "常规外协",
                  value: "常规外协",
                },
                {
                  label: "特种外协",
                  value: "特种外协",
                },
              ],
            },
            {
              label: "零件工时",
              prop: "workingHours",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              search: false,
            },
            // {
            //   label: "预计完成时间",
            //   prop: "estimatedCompletionTime",
            //   type: "datetime",
            //   format: "yyyy-MM-dd HH:mm:ss",
            //   valueFormat: "yyyy-MM-dd HH:mm:ss",
            //   addDisplay: false,
            //   editDisplay: false,
            //   search: false,
            //   overHidden: true,
            //   minWidth: 100,
            // },
            {
              label: "部件领用日期",
              prop: "collectionDate",
              type: "datetime",
              format: "yyyy-MM-dd HH:mm:ss",
              valueFormat: "yyyy-MM-dd HH:mm:ss",
              addDisplay: false,
              editDisplay: false,
              overHidden: true,
              minWidth: 100,
            },
            {
              label: "已加工数量",
              prop: "processedQuantity",
              type: "input",
              addDisplay: false,
              editDisplay: false,
            },
            {
              label: "领用数量",
              prop: "receivedQuantity",
              type: "input",
              addDisplay: false,
              editDisplay: false,
            },
            {
              label: "退回数量",
              prop: "returnQuantity",
              type: "input",
              addDisplay: false,
              editDisplay: false,
            },
            {
              label: "结余数量",
              prop: "balanceQuantity",
              type: "input",
              addDisplay: false,
              editDisplay: false,
            },
            {
              label: "加工人",
              prop: "processingPerson",
              type: "input",
              addDisplay: false,
              editDisplay: false,
            },
            {
              label: "部件验收人",
              prop: "acceptancePerson",
              type: "input",
              addDisplay: false,
              editDisplay: false,
            },
            {
              label: "领用人",
              prop: "recipient",
              type: "input",
              search: false,
              addDisplay: false,
              editDisplay: false,
            },
            {
              label: "验收结果",
              prop: "acceptanceResults",
              type: "input",
              addDisplay: false,
              editDisplay: false,
            },
            {
              label: "标签编号",
              prop: "tagNumber",
              type: "input",
              search: false,
              addDisplay: false,
              editDisplay: false,
            },
            {
              label: "存放位置",
              prop: "position",
              type: "input",
              search: false,
              addDisplay: false,
              editDisplay: false,
            },
            {
              label: "申请日期",
              prop: "applicationDate",
              type: "datetime",
              format: "yyyy-MM-dd HH:mm:ss",
              valueFormat: "yyyy-MM-dd HH:mm:ss",
              overHidden: true,
              addDisplay: false,
              editDisplay: false,
            },
          ],
        },
        option: {//工艺
          searchEnter: true,
          delBtn: false,
          editBtn: false,
          updateBtn: false,
          dialogDrag: true,
          align: "center",
          height:'auto',
          calcHeight: 30,
          searchLabelWidth: 120,
          labelWidth: 120,
          tip: false,
          searchShow: true,
          searchMenuSpan: 6,
          border: true,
          index: false,
          viewBtn: true,
          selection: true,
          searchIndex: 4,
          // searchSpan: 4,
          // searchLabelPosition: "top",
          // searchLabelWidth: 0,
          searchIcon: true,
          dialogClickModal: false,
          column: [
            {
              label: "状态",
              prop: "status",
              type: "select",
              dicUrl: "/api/blade-system/dict-biz/dictionary?code=ni_production_machining_manage_status",
              dataType: "number",
              props: {
                label: "dictValue",
                value: "dictKey"
              },
              search: true,
              addDisplay: false,
              editDisplay: false,
              searchOrder: 98,
              searchSpan: 3,
              searchLabelWidth: 50,
            },
            {
              label: "分类",
              prop: "classify",
              type: "select",
              search: false,
              dicData: [
                {
                  label: "项目设计",
                  value: "项目设计",
                },
                {
                  label: "生产维修",
                  value: "生产维修",
                },
                {
                  label: "返修返工",
                  value: "返修返工",
                },
                {
                  label: "其他",
                  value: "其他",
                },
              ],
              rules:[{
                required:true,
                message:"请选择分类",
                trigger:"blur"
              }],
            },
            {
              label: "是否紧急",
              prop: "urgent",
              type: "select",
              search: false,
              value: "否",
              dicData: [
                {
                  label: "是",
                  value: "是",
                },
                {
                  label: "否",
                  value: "否",
                },
              ],
              rules:[{
                required:true,
                message:"请选择是否紧急",
                trigger:"blur"
              }],
            },
            {
              label: "待加工部件名称",
              prop: "componentName",
              type: "input",
              labelWidth: 125,
              search: true,
              overHidden: true,
              minWidth: 110,
              // order: 10,
              searchOrder: 95,
              searchSpan: 5,
              rules:[{
                required:true,
                message:"请选择待加工部件名称",
                trigger:"blur"
              }],
            },
            {
              label: "图纸代号",
              prop: "drawingCode",
              type: "input",
              search: true,
              overHidden: true,
              searchOrder: 94,
              searchSpan: 4,
              searchLabelWidth: 80,
              rules:[{
                required:true,
                message:"请选择图纸代号",
                trigger:"blur"
              }],
            },
            {
              label: "项目负责人",
              prop: "projectLeader",
              type: "input",
              search: true,
              addDisplay: false,
              editDisplay: false,
            },
            {
              label: "项目号",
              prop: "projectNo",
              type: "input",
              search: false,
              overHidden: true,
              minWidth: 100,
              rules:[{
                required:true,
                message:"请选择项目号",
                trigger:"blur"
              }],
            },
            {
              label: "规格",
              prop: "specifications",
              type: "input",
              rules:[{
                required:true,
                message:"请选择规格",
                trigger:"blur"
              }],
            },
            {
              label: "材质",
              prop: "materialQuality",
              type: "input",
              rules:[{
                required:true,
                message:"请选择材质",
                trigger:"blur"
              }],
            },
            {
              label: "需求数量",
              prop: "num",
              type: "number",
              min: 1,
              search: false,
              rules:[{
                required:true,
                message:"请选择需求数量",
                trigger:"blur"
              }],
            },
            {
              label: "单位",
              prop: "unit",
              type: "select",
              value: "件",
              minWidth: 50,
              dicData: [
                {
                  label: "件",
                  value: "件",
                },
              ],
              rules:[{
                required:true,
                message:"请选择单位",
                trigger:"blur"
              }],
            },
            {
              label: "需求时间",
              prop: "demandTime",
              type: "datetime",
              format: "yyyy-MM-dd HH:mm:ss",
              valueFormat: "yyyy-MM-dd HH:mm:ss",
              search: false,
              overHidden: true,
              minWidth: 100,
              rules:[{
                required:true,
                message:"请选择需求时间",
                trigger:"blur"
              }],
            },
            {
              label: "加工备注",
              prop: "processingRemarks",
              type: "textarea",
              search: true,
              minWidth: 200,
              minRows: 1,
              overHidden: true,
            },
            {
              label: "入库时间",
              prop: "storageTime",
              type: "datetime",
              format: "yyyy-MM-dd HH:mm:ss",
              valueFormat: "yyyy-MM-dd HH:mm:ss",
              overHidden: true,
              addDisplay: false,
              editDisplay: false,
            },
            {
              label: "入库时间",
              prop: "storageTimeRange",
              type: "datetimerange",
              format:'yyyy-MM-dd HH:mm:ss',
              valueFormat: "yyyy-MM-dd HH:mm:ss",
              // valueFormat:"yyyy-MM-dd",
              startPlaceholder: '日期开始范围',
              endPlaceholder: '日期结束范围',
              search:true,
              searchRange: true,
              hide:true,
              display:false,
              searchOrder: 93,
              searchLabelWidth: 80,
            },
            {
              label: "加工方式",
              prop: "processingMethod",
              type: "select",
              // type: "input",
              // addDisplay: false,
              // editDisplay: false,
              search: false,
              overHidden: true,
              multiple: true,
              dataType: "string",
              dicData: [
                {
                  label: "激光下料",
                  value: "激光下料",
                },
                {
                  label: "锯床下料",
                  value: "锯床下料",
                },
                {
                  label: "等离子下料",
                  value: "等离子下料",
                },
                {
                  label: "铣床",
                  value: "铣床",
                },
                {
                  label: "数控加工中心",
                  value: "数控加工中心",
                },
                {
                  label: "车床",
                  value: "车床",
                },
                {
                  label: "数控车床",
                  value: "数控车床",
                },
                {
                  label: "磨床",
                  value: "磨床",
                },
                {
                  label: "钻床",
                  value: "钻床",
                },
                {
                  label: "线切割加工",
                  value: "线切割加工",
                },
                {
                  label: "焊接组装",
                  value: "焊接组装",
                },
                {
                  label: "外购件加工",
                  value: "外购件加工",
                },
                {
                  label: "其他",
                  value: "其他",
                },
              ],
              rules:[{
                required:true,
                message:"请选择加工方式",
                trigger:"blur"
              }],
            },
            {
              label: "是否外协",
              prop: "outsourced",
              type: "select",
              value: "否",
              search: true,
              dicData: [
                {
                  label: "是",
                  value: "是",
                },
                {
                  label: "否",
                  value: "否",
                },
              ],
              change: ({ value }) => {
                if (value == ("是")) {
                  this.form.outsourcingClassification = "常规外协";
                }else{
                  this.form.outsourcingClassification = "";
                }
              },
            },
            {
              label: "外协分类",
              prop: "outsourcingClassification",
              type: "select",
              search: true,
              dicData: [
                {
                  label: "常规外协",
                  value: "常规外协",
                },
                {
                  label: "特种外协",
                  value: "特种外协",
                },
              ],
            },

            {
              label: "零件工时",
              prop: "workingHours",
              type: "input",
              search: false,
              addDisplay: false,
              editDisplay: false,
            },
            // {
            //   label: "预计完成时间",
            //   prop: "estimatedCompletionTime",
            //   type: "datetime",
            //   format: "yyyy-MM-dd HH:mm:ss",
            //   valueFormat: "yyyy-MM-dd HH:mm:ss",
            //   search: false,
            //   overHidden: true,
            //   minWidth: 100,
            // },
            {
              label: "部件领用日期",
              prop: "collectionDate",
              type: "datetime",
              format: "yyyy-MM-dd HH:mm:ss",
              valueFormat: "yyyy-MM-dd HH:mm:ss",
              addDisplay: false,
              editDisplay: false,
              overHidden: true,
              minWidth: 100,
            },
            {
              label: "已加工数量",
              prop: "processedQuantity",
              type: "input",
              addDisplay: false,
              editDisplay: false,
            },
            {
              label: "领用数量",
              prop: "receivedQuantity",
              type: "input",
              addDisplay: false,
              editDisplay: false,
            },
            {
              label: "退回数量",
              prop: "returnQuantity",
              type: "input",
              addDisplay: false,
              editDisplay: false,
            },
            {
              label: "结余数量",
              prop: "balanceQuantity",
              type: "input",
              addDisplay: false,
              editDisplay: false,
            },
            {
              label: "加工人",
              prop: "processingPerson",
              type: "input",
              addDisplay: false,
              editDisplay: false,
            },
            {
              label: "部件验收人",
              prop: "acceptancePerson",
              type: "input",
              addDisplay: false,
              editDisplay: false,
            },
            {
              label: "领用人",
              prop: "recipient",
              type: "input",
              search: false,
              addDisplay: false,
              editDisplay: false,
            },
            {
              label: "验收结果",
              prop: "acceptanceResults",
              type: "input",
              addDisplay: false,
              editDisplay: false,
            },
            {
              label: "标签编号",
              prop: "tagNumber",
              type: "input",
              search: false,
              addDisplay: false,
              editDisplay: false,
            },
            {
              label: "存放位置",
              prop: "position",
              type: "input",
              search: false,
              addDisplay: false,
              editDisplay: false,
            },
            {
              label: "申请日期",
              prop: "applicationDate",
              type: "datetime",
              format: "yyyy-MM-dd HH:mm:ss",
              valueFormat: "yyyy-MM-dd HH:mm:ss",
              overHidden: true,
              addDisplay: false,
              editDisplay: false,
            },
          ]
        }
        ,
        data: [],
      };
    },
    computed: {
      ...mapGetters(["permission","userInfo"]),
      permissionList() {
        return {
          addBtn: this.vaildData(this.permission.machiningManage_add, false),
          viewBtn: this.vaildData(this.permission.machiningManage_view, false),
          delBtn: this.vaildData(this.permission.machiningManage_delete, false),
          editBtn: this.vaildData(this.permission.machiningManage_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      },
      upn() {
        this.$nextTick(() => {
          this.$refs.crud.doLayout();
        });
        return this.lx?this.option:this.option1
      },

      //检验必填参数异常
      checkNotNull() {
        let status = [];
        this.selectionList.forEach(ele => {
          if(ele.classify === null || (ele['componentName'] === '')){
            status.push(ele.classify);
          }
        });
        return status.length
      },

    },

    mounted(){
      this.dictInit();
    },
    created() {
      console.log("created")
      if(this.permission.hasOwnProperty("machiningManage_examineApprove") && this.permission.machiningManage_examineApprove){
        this.lx = 1
        this.tabsNo="all"
      }
    },
    methods: {
      handleLog() {
        this.$refs.logOptDialogRef.init()
      },
      rowLog(row) {
        this.$refs.logOptDialogRef.init(row.id)
      },
      dictInit() {
        //预算编号
        this.$http
          .get("/api/blade-system/dict-biz/dictionary?code=ni_production_machining_manage_status")
          .then((res) => {
            // console.log(res);
            // const column = this.findObject(this.option.column, "status");
            // column.dicData = res.data.data;
            this.statusDict = res.data.data;
            this.statusDictKeyValue = this.statusDict.reduce((acc, cur) => {
              acc[cur.dictKey] = cur.dictValue;
              return acc;
            }, {});
          });
      },
      handleExport() {
        let msg =
          '是否导出<span style="color: #F56C6C;font-weight: bold">所有数据</span>?';
        if (this.selectionList.length > 0) {
          msg =
            '是否要导出<span style="color: #F56C6C;font-weight: bold">当前选中的数据</span>？';
        }
        this.$confirm(msg, {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          dangerouslyUseHTMLString: true,
          type: "warning",
        }).then(() => {
          this.handleExportData();
        });
      },
      async handleExportData() {
        let opt = {
          column: [
            {
              label: "待加工部件名称",
              prop: "componentName",
            },
            {
              label: "状态",
              prop: "status",
            },
            {
              label: "分类",
              prop: "classify",
            },
            {
              label: "是否紧急",
              prop: "urgent",
            },
            {
              label: "图纸代号",
              prop: "drawingCode",
            },
            {
              label: "项目负责人",
              prop: "projectLeader",
            },
            {
              label: "项目号",
              prop: "projectNo",
            },
            {
              label: "规格",
              prop: "specifications",
            },
            {
              label: "材质",
              prop: "materialQuality",
            },
            {
              label: "需求数量",
              prop: "num",
            },
            {
              label: "单位",
              prop: "unit",
            },
            {
              label: "需求时间",
              prop: "demandTime",
            },
            {
              label: "加工备注",
              prop: "processingRemarks",
            },
            {
              label: "加工方式",
              prop: "processingMethod",
            },
            {
              label: "是否外协",
              prop: "outsourced",
            },
            {
              label: "外协分类",
              prop: "outsourcingClassification",
            },
            {
              label: "已加工数量",
              prop: "processedQuantity",
            },
            {
              label: "入库时间",
              prop: "storageTime",
            },
           ],
        };

        if (this.selectionList.length > 0) {
          this.exportData = this.selectionList;
        }else{
          await this.getExportData();
        }

        this.$Export.excel({
          title: "机加工件管理",
          columns: opt.column,
          data: this.exportData.map((item) => {
            return {
              ...item,
              status: this.statusDictKeyValue[item.status],
              // inspectorName: this.expressTypeDictKeyValue[item.inspector],
              // expressCompanyName: this.expressCompanyDictKeyValue[item.expressCompany],
              // paymentTypeDictName: this.paymentTypeDictKeyValue[item.paymentType],
              // expressTypeName: this.expressTypeDictKeyValue[item.expressType],
            };
          }),
        });
        this.exportData = [];
      },
      //获取搜索的打印数据
      async getExportData() {
        const promises = [];
        this.exportData = [];
        for (var i = 1; i <= this.page.total / this.page.pageSize + 1; i++) {
          const promise = getList(i, this.page.pageSize, {
            ...this.params,
            ...this.query,
          }).then((res) => {
            const data = res.data.data.records;
            this.exportData = this.exportData.concat(data);
          });

          promises.push(promise);
        }
        // 等待所有异步请求完成
        await Promise.all(promises);
        // console.log(this.exportData)
        return this.exportData;
      },

      showDialog(row){
        this.tableData = []
        let parentId = row.id
        getGsList(parentId).then(res => {
          this.tableData = res.data.data;
        });
        this.visible=true
      },

      //检验必填参数异常
      checkNull(colName) {
        // let colName = ['componentName','classify','urgent']
        let status = [];
        this.selectionList.forEach(ele => {
          let err='{'
          colName.forEach(ele1 => {
              if(ele[ele1]===null || ele[ele1]===''){
                err += ele1 + ' , '
              }
            }
          )
          err += '}'
          err = err.replace('{}','')
          if (err.length > 0) {
            status.push(err);
          }
        });
        return status
      },
      undof(){
        this.$refs.crud.rowSave();
      },
      //检验状态不是草稿的数量
      checkStatus(sts) {
        let status = [];
        this.selectionList.forEach(ele => {
          if(ele.status !== sts){
            status.push(ele.status);
          }
        });
        return status.length
      },

      checkStatus2(sts1, sts2) {
        let status = [];
        this.selectionList.forEach(ele => {
          if(ele.status !== sts1 && ele.status !== sts2){
            status.push(ele.status);
          }
        });
        return status.length
      },

      /**
       * 导出excel模版
       */
      handleTemplate() {
        exportBlob(
          `/api/machiningManage/machiningManage/export-template?${
            this.website.tokenHeader
          }=${getToken()}`
        ).then((res) => {
          downloadXls(res.data, "机加工数据导入模板.xlsx");
        });
      },
      projectConfirm(selectionList) {
        //适配选择项目号
        // this.form.projectId1 = selectionList[0].id
        this.form.projectNo = selectionList[0].serialNo
        this.form.projectLeader = this.userInfo.account
        if (selectionList) {
          if (
            this.$refs.crud &&
            "[object Function]" === toString.call(this.$refs.crud.clearValidate)
          ) {
            this.$refs.crud.clearValidate(["projectNo"]);
          }
        }
      },
      rowSave(row, done, loading) {
        // row.projectNo = this.form.projectNo1  //适配选择项目号控件
        // row.projectId = this.form.projectId1  //适配选择项目号控件
        row.projectLeader = this.userInfo.account
        add(row).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          loading();
          window.console.log(error);
        });
      },
      rowUpdate(row, index, done, loading) {
        // row.projectNo = this.form.projectNo1  //适配选择项目号控件
        // row.projectId = this.form.projectId1  //适配选择项目号控件
        if(this.undo){
          row.id = null
          row.status = 1
          row.projectLeader = this.userInfo.account
          let obj = row
          let array = ['id','status','classify','urgent','componentName','drawingCode',
            'projectLeader','projectId','projectNo','specifications','materialQuality',
            'num','unit','demandTime','processingRemarks'];
          //清空不符合条件的列
          for (let key in obj) {
            if (obj.hasOwnProperty(key) && array.indexOf(key) === -1) {
              obj[key] = null
            }
          }
        }
        update(row).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          loading();
          console.log(error);
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      showLoadDialog(){
        this.centerDialogVisible = true
      },
      handleImportData() {
        if(this.textarea.length === 0){
          this.$message.warning("数据不能为空。")
          return;
        }

        this.$confirm("确定将粘贴板数据导入进行验证吗?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(async () => {
          const text = this.textarea
          importData(text).then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          }, error => {
            // this.$message({ type: 'waning', message: error })
            // loading();
            this.onLoad(this.page);
            window.console.log(error);
            error = String(error).replace("Error: ", "")

            this.$Clipboard({
              text: error
            }).then(() => {
              this.$message.success("错误提示导出到剪切板完成。");
            }).catch(() => {
              this.$message({ type: 'waning', message: '该浏览器不支持自动复制' })
            })

          });
        }).catch(() => {
          // 取消操作
          this.$message({
            type: 'info',
            message: '已取消'
          })
        })
        this.centerDialogVisible = false
      },
      /**
       * 状态转已出库
       */
      handleOutside() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }

        let temp;
        temp = this.checkStatus(6)
        if (temp !== 0){
          this.$message.error("有 " + temp + " 条数据的状态不是 <已入库> ，操作失败！！！");
          return;
        }

        // temp = this.checkNotNull
        // if (temp !== 0){
        //   this.$message.error("有 " + temp + " 条数据的必填项为空，操作失败！！！");
        //   return;
        // }

        this.$confirm("确定将选择数据进行状态转成 <已出库> 吗?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return changeStatus(this.ids, 7);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      /**
       * 状态转已入库
       */
      handleEntered() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }

        let temp;
        temp = this.checkStatus2(4, 5)
        if (temp !== 0){
          this.$message.error("有 " + temp + " 条数据的状态不是 <正在加工> 或 <正在外协>，操作失败！！！");
          return;
        }

        // temp = this.checkNotNull
        // if (temp !== 0){
        //   this.$message.error("有 " + temp + " 条数据的必填项为空，操作失败！！！");
        //   return;
        // }

        this.$confirm("确定将选择数据进行状态转成 <已入库> 吗?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return changeStatus(this.ids, 6);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      /**
       * 状态转外协
       */
      handleOutsideProcessing() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }

        let temp;
        temp = this.checkStatus2(2, 3)
        if (temp !== 0){
          this.$message.error("有 " + temp + " 条数据的状态不是 <新> 或 <待加工>，操作失败！！！");
          return;
        }

        let status = [];
        this.selectionList.forEach(ele => {
          if(ele.processingMethod === null){
            status.push(ele.processingMethod);
          }
        });
        temp = status.length
        if (temp !== 0){
          this.$message.error("有 " + temp + " 条数据的必填项为空，操作失败！！！");
          return;
        }

        this.$confirm("确定将选择数据进行状态转成 <正在外协> 吗?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return changeStatus(this.ids, 5);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      /**
       * 状态转正在加工
       */
      handleProcessing() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }

        let temp;
        temp = this.checkStatus2(2, 3)
        if (temp !== 0){
          this.$message.error("有 " + temp + " 条数据的状态不是 <新> 或 <待加工>，操作失败！！！");
          return;
        }

        let status = [];
        this.selectionList.forEach(ele => {
          if(ele.processingMethod === null){
            status.push(ele.processingMethod);
          }
        });
        temp = status.length
        if (temp !== 0){
          this.$message.error("有 " + temp + " 条数据的必填项(加工方式)为空，操作失败！！！");
          return;
        }

        this.$confirm("确定将选择数据进行状态转成 <正在加工> 吗?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return changeStatus(this.ids, 4);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      /**
       * 状态转待加工
       */
      handleWaitingProcessing() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }

        let temp;
        temp = this.checkStatus(2)
        if (temp !== 0){
          this.$message.error("有 " + temp + " 条数据的状态不是 <新> ，操作失败！！！");
          return;
        }

        let status = [];
        this.selectionList.forEach(ele => {
          if(ele.processingMethod === null){
            status.push(ele.processingMethod);
          }
        });
        temp = status.length
        if (temp !== 0){
          this.$message.error("有 " + temp + " 条数据的必填项为空，操作失败！！！");
          return;
        }

        this.$confirm("确定将选择数据进行状态转成 <待加工> 吗?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return changeStatus(this.ids, 3);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      handleDraft(row) {
        this.$confirm("确定将选择数据进行状态转成 草稿 吗?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            console.log(row.id)
            return changeStatus(row.id, 1);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      handleNew() {
        console.log("selectionList:",this.selectionList)
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }

        let temp;
        temp = this.checkStatus(1)
        if (temp !== 0){
          this.$message.error("有 " + temp + " 条数据的状态不是<草稿>，操作失败！！！");
          return;
        }

        // temp = this.checkNotNull
        let colName = ['classify','urgent','componentName','drawingCode','projectNo','num','demandTime']
        let temp1 = this.checkNull(colName)
        if (temp1.length>0){
          console.log(temp1)
          let err=''
          for (let i = 0; i < temp1.length; i++) {
            temp1[i] = temp1[i].replace('classify','分类')
              .replace('urgent','是否紧急')
              .replace('componentName','待加工部件名称')
              .replace('drawingCode','图纸代号')
              .replace('projectNo','项目号')
              .replace('num','需求数量')
              .replace('demandTime','需求时间')
            err+= (i+1) + " -> " +temp1[i] + "<br>"
          }
          // this.$message.error("有 " + temp1.length + ' 条数据的必填项为空，操作失败！！！'+'\n' + err);
          this.$message({
            type: "warning",
            message:"有 " + temp1.length + ' 条数据的必填项为空，操作失败！！！<br><br>' +
              '必填列有： 分类 , 是否紧急 , 待加工部件名称 , 图纸代号 , 项目号 , 需求数量 , 需求时间' +'<br><br>' +
              '空的有： <br>' + err,
            dangerouslyUseHTMLString: true,
          })
          return;
        }

        this.$confirm("确定将选择数据进行状态转成 新 吗?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return toNew(this.ids, 2);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      handleTest() {
        // this.findObject(this.option.column, "status").hide = true;
        // this.$refs.crud.option = this.option1
        // this.$nextTick(() => {
        //   this.$refs.crud.doLayout();
        // });
        // display_permissions
        console.log(this.permission)
        console.log(this.userInfo.dept_name[0])
        this.lx = 0
      },
      handleTest1() {
        // this.findObject(this.option.column, "status").hide = false;
        // this.findObject(this.option.column, "status").order = 1;
        // this.$refs.crud.option = this.option
        // this.$nextTick(() => {
        //   this.$refs.crud.doLayout();
        // });
        this.lx = 1

      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        let icount = 0
        for (let i = 0; i < this.selectionList.length; i++) {
          if(this.selectionList[i].status!==1){
            icount++
          }
        }
        if(icount>0 && this.lx===0){
          this.$message.warning("有 "+icount+" 条状态不是 <草稿>， 删除失败。");
          return;
        }

        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
            // this.form.projectNo = res.data.data.projectId //"1665566421144715265"
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1;
        //前端传值时添加参数
        if (params.storageTimeRange && params.storageTimeRange.length === 2){
          params.storageStartTime = params.storageTimeRange[0]; // 添加 startTime 参数
          params.storageEndTime = params.storageTimeRange[1];
        }
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      refreshChange() {
        this.onLoad(this.page, this.query);
      },
      onLoad(page, params = {}) {
        this.loading = true;

        const query = Object.assign(params, this.query, {descs:'id'});
        query.tabsNo = this.tabsNo

        getList(page.currentPage, page.pageSize, query).then(res => {
          const data = res.data.data;
          console.log(data)
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      }
    }
  };
</script>

<style>
  .center-table {
    margin-left: auto;
    margin-right: auto;
  }
</style>

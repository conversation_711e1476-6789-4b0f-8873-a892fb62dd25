<template>
  <el-container>
    <el-input
      v-model="serialNos"
      :size="size"
      suffix-icon="el-icon-search"
      :disabled="disabled"
      :placeholder="placeholder || ' '"
      clearable
      @clear="handleClear"
      @focus="handleFocus"
    >
      <el-button
        v-if="itemBtn"
        slot="append"
        icon="el-icon-more"
        @click="handleItemShow"
      ></el-button>
    </el-input>
    <por-order-dialog
      v-if="show"
      :show.sync="show"
      :multiple="multiple"
      :params="params"
      :descs="descs"
      :withItem="withItem"
      @submit="handleOrderSubmit"
    />
    <order-form-dialog ref="orderFormDialogRef" detail />
  </el-container>
</template>

<script>
import PorOrderDialog from "./OrderSelectDialog";
import OrderFormDialog from "@/views/ni/por/components/OrderFormDialog";
import Emitter from "element-ui/src/mixins/emitter";

export default {
  name: "PorOrderSelect",
  mixins: [Emitter],
  components: { PorOrderDialog, OrderFormDialog },
  props: {
    value: {
      type: String,
      default: "",
    },
    label: String,
    size: {
      type: String,
      default: "small",
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    itemBtn: {
      type: Boolean,
      default: false,
    },
    params: {
      type: Object,
      default: () => {},
    },
    placeholder: String,
    multiple: {
      type: Boolean,
      default: false,
    },
    beforeSelect: {
      type: Function,
    },
    withItem: {
      type: Boolean,
      default: false,
    },
    descs: {
      type: String,
      default: "id",
    },
  },
  computed: {
    serialNos: {
      get() {
        return this.label;
      },
      set(val) {
        this.$emit("update:label", val);
      },
    },
  },
  data() {
    return {
      show: false,
      ids: "",
    };
  },
  methods: {
    handleClear() {
      this.$emit("update:label", "");
      this.$emit("update:value", "");
      this.$emit("clear");
      this.dispatch("ElFormItem", "el.form.blur", [""]);
    },
    handleFocus() {
      if (!this.disabled) {
        if (this.beforeSelect) {
          this.beforeSelect(() => {
            this.show = true;
          });
        } else {
          this.show = true;
        }
      }
    },
    handleItemShow() {
      if (!this.serialNos) {
        this.$message({
          type: "warning",
          message: "请选择采购订单!",
        });
      }
      this.$refs.orderFormDialogRef.init(this.value);
    },
    handleOrderSubmit(selectList, active) {
      if (selectList) {
        let orderIds;
        if (active === "item") {
          orderIds = selectList.map((item) => item.orderId);
        } else if (active === "order") {
          orderIds = selectList.map((item) => item.id);
        }
        this.ids = Array.from(new Set(orderIds)).join(",");
        const serialNos = selectList.map((item) => item.serialNo);
        this.serialNos = Array.from(new Set(serialNos)).join(",");
        this.$emit("input", this.ids);
        this.$emit("submit", selectList, active);
        this.$nextTick(() => {
          this.dispatch("ElFormItem", "el.form.blur", [this.ids]);
        });
      }
      this.show = false;
    },
  },
};
</script>

<style scoped></style>

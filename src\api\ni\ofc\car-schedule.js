import request from "@/router/axios";

export const getPage = (current, size, params) => {
  return request({
    url: "/api/ni/ofc/car/schedule/page",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/ni/ofc/car/schedule/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const add = (row) => {
    return request({
      url: "/api/ni/ofc/car/schedule/add",
      method: "post",
      data: row,
    });
  };

  export const remove = (ids) => {
    return request({
      url: "/api/ni/ofc/car/schedule/remove",
      method: "post",
      params: {
        ids,
      },
    });
  };
  
  export const update = (row) => {
    return request({
      url: "/api/ni/ofc/car/schedule/update",
      method: "post",
      data: row,
    });
  };

  export const submit = (ids) => {
    return request({
      url: '/api/ni/ofc/car/schedule/submit',
      method: 'post',
      params: {
        ids,
      }
    })
  }
  
  export const back = (ids) => {
    return request({
      url: '/api/ni/ofc/car/schedule/back',
      method: 'post',
      params: {
        ids,
      }
    })
  }

  export const save = (row) => {
    return request({
      url: "/api/ni/ofc/car/schedule/save",
      method: "post",
      data: row,
    });
  };

  //获取当前车辆调度编号
  export const getSerialNo = (id) => {
    return request({
      url: "/api/ni/ofc/car/schedule/getSerialNo",
      method: "get",
      params: {
        id,
      }
    });
  };
//车辆状况重置
  export const resetCar = (id) => {
    return request({
      url: "/api/ni/ofc/car/schedule/resetCar",
      method: "post",
      params: {
        id,
      },
    });
  };
<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @tree-load="treeLoad"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template #personIdsForm="{ row, size, disabled, index }">
        <user-select
          v-model="form.personIds"
          :size="size"
          multiple
          :disabled="disabled"
        />
      </template>
      <template #amountStr="{ row, index }">
        <span
          v-if="row.totalAmount && Number(row.totalAmount) !== 0"
          :style="{ color: 'red' }"
        >
          ({{
            Number(row.totalAmount).toLocaleString("zh-CN", {
              minimumFractionDigits: 2,
            })
          }})
        </span>
        <span>{{ row.amountStr }}</span>
      </template>
      <template #finish="{ row }">
        <el-tag v-if="row.finish" size="mini">是</el-tag>
        <el-tag v-else size="mini" type="info">否</el-tag>
      </template>
      <template #yearDate="{ row }">
        <el-tag v-if="row.yearDate" type="danger" size="mini" effect="plain">
          {{ row.yearDate }}
        </el-tag>
      </template>
      <template #type="{ row, size }">
        <el-tag v-if="row.type === 'SC'" :size="size" effect="dark"
          >{{ row.$type }}
        </el-tag>
        <el-tag
          v-else-if="row.type === 'SZ'"
          :size="size"
          type="success"
          effect="dark"
          >{{ row.$type }}
        </el-tag>
        <el-tag
          v-else-if="row.type === 'GW'"
          :size="size"
          type="info"
          effect="dark"
          >{{ row.$type }}
        </el-tag>
        <el-tag
          v-else-if="row.type === 'SW'"
          :size="size"
          type="warning"
          effect="dark"
          >{{ row.$type }}
        </el-tag>
        <el-tag
          v-else-if="row.type === 'ZL'"
          :size="size"
          type="danger"
          effect="dark"
          >{{ row.$type }}
        </el-tag>
        <el-tag v-else-if="row.type === 'DQ'" :size="size" effect="plain"
          >{{ row.$type }}
        </el-tag>
        <el-tag
          v-else-if="row.type === 'RC'"
          :size="size"
          type="success"
          effect="plain"
          >{{ row.$type }}
        </el-tag>
        <el-tag
          v-else-if="row.type === 'JY'"
          :size="size"
          type="info"
          effect="plain"
          >{{ row.$type }}
        </el-tag>
        <el-tag
          v-else-if="row.type === 'GN'"
          :size="size"
          type="warning"
          effect="plain"
          >{{ row.$type }}
        </el-tag>
        <el-tag
          v-else-if="row.type === 'WL'"
          :size="size"
          type="danger"
          effect="plain"
          >{{ row.$type }}
        </el-tag>
        <el-tag v-else-if="row.type === 'AH'" :size="size"
          >{{ row.$type }}
        </el-tag>
        <el-tag v-else-if="row.type === 'QG'" :size="size" type="info"
          >{{ row.$type }}
        </el-tag>
        <el-tag v-else-if="row.type === 'JM'" :size="size" type="warning"
          >{{ row.$type }}
        </el-tag>
        <el-tag v-else :size="size" type="danger">{{ row.$type }}</el-tag>
      </template>
      <template #brand="{ row, size }">
        <el-tag
          v-if="row.brand"
          :size="size"
          :effect="row.brand === '1' ? 'dark ' : 'plain'"
        >
          {{ row.$brand }}
        </el-tag>
      </template>
      <template #status="{ row }">
        <el-tag v-if="row.status && row.status === 0" size="mini" type="info">
          {{ row.$status }}
        </el-tag>
        <el-tag
          v-else-if="row.status && row.status === 1"
          size="mini"
          effect="plain"
        >
          {{ row.$status }}
        </el-tag>
        <el-tag
          v-else-if="row.status && row.status === 2"
          size="mini"
          effect="plain"
        >
          {{ row.$status }}
        </el-tag>
        <el-tag
          v-else-if="row.status && row.status === 3"
          size="mini"
          type="danger"
        >
          {{ row.$status }}
        </el-tag>
        <el-tag
          v-else-if="row.status && row.status === 4"
          size="mini"
          type="warning"
          effect="plain"
        >
          {{ row.$status }}
        </el-tag>
        <el-tag
          v-else-if="row.status && row.status === 5"
          size="mini"
          type="warning"
          effect="plain"
        >
          {{ row.$status }}
        </el-tag>
        <el-tag
          v-else-if="row.status && row.status === 6"
          size="mini"
          type="danger"
          effect="plain"
        >
          {{ row.$status }}
        </el-tag>
        <el-tag
          v-else-if="row.status && row.status === 9"
          size="mini"
          type="success"
          effect="plain"
        >
          {{ row.$status }}
        </el-tag>
        <el-tag v-else size="mini" type="warning" effect="dark">
          {{ row.$status }}
        </el-tag>
      </template>
      <template #title="{ row, index, size }">
        <span>
          {{ row.title ? row.title : row.budgetTitle }}
        </span>
        <el-tag type="warning" v-if="row.repair" size="mini">补</el-tag>
      </template>
      <template #serialNo="{ row, index }">
        <flow-timeline-popover
          v-if="row.processInsId"
          :process-ins-id="row.processInsId"
          :form-key="formKey"
          :process-def-key="processDefKey"
          v-model="row.serialNo"
          :flow.sync="row.flow"
          trigger="click"
          lazy
        />
        <span v-else>{{ row.serialNo }}</span>
      </template>
      <template #parentIdForm="{ disabled, size }">
        <budget-year-select
          v-model="form.parentId"
          :size="size"
          :disabled="disabled"
          @confirm="handleTitleConfirm"
        />
      </template>
      <template #menuLeft>
        <el-button
          type="primary"
          size="mini"
          icon="el-icon-s-promotion"
          plain
          v-if="permission.ni_por_budget_year_apply"
          @click="handleApply"
          >预算申请
        </el-button>
        <el-button
          type="danger"
          size="mini"
          icon="el-icon-delete"
          plain
          v-if="permission.ni_por_budget_year_delete"
          @click="handleDelete"
          >删 除
        </el-button>
        <el-dropdown
          v-if="permission.ni_por_budget_year_import"
          @command="handleImport"
        >
          <el-button type="warning" size="mini" icon="el-icon-upload2" plain
            >导 入<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="budget">预算导入</el-dropdown-item>
            <el-dropdown-item command="item">明细导入</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-dropdown
          v-if="permission.ni_por_budget_year_export"
          @command="handleExport"
        >
          <el-button type="success" size="mini" icon="el-icon-download"
            >导 出<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="budget">预算导出</el-dropdown-item>
            <el-dropdown-item command="item">明细导出</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-dropdown
          v-if="permission.ni_por_budget_year_printer"
          @command="handlePrint"
        >
          <el-button type="success" size="mini" icon="el-icon-printer"
            >打 印<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="budget">预算打印</el-dropdown-item>
            <el-dropdown-item command="items">明细打印</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </template>
      <template #menuRight="{ size }">
        <el-button
          icon="el-icon-time"
          circle
          :size="size"
          @click="handleLog"
        ></el-button>
      </template>
      <template #menu="{ row, size }">
        <el-button
          v-if="
            (row.repair == null || !row.repair) &&
            ((row.personIdList && row.personIdList.includes(row.createUser)) ||
              userInfo.role_name.includes('admin'))
          "
          type="text"
          :size="size"
          @click="rowPerson(row)"
          >使用人员
        </el-button>
        <el-button
          v-if="row.parentId > 0"
          type="text"
          :size="size"
          icon="el-icon-share"
          @click="rowSub(row)"
          >小项管理
        </el-button>
        <el-button
          type="text"
          icon="el-icon-circle-plus"
          :size="size"
          v-if="row.parentId > 0 && row.status === 9 && !row.repair"
          @click="rowRepair(row)"
          >增补预算
        </el-button>
        <el-button
          v-if="row.parentId > 0"
          type="text"
          :size="size"
          icon="el-icon-s-grid"
          @click="rowDetail(row)"
          >明细
        </el-button>
        <el-button
          icon="el-icon-finished"
          v-if="
            !row.finish &&
            row.parentId > 0 &&
            permission.ni_por_budget_year_finish &&
            ((row.personIdList && row.personIdList.includes(row.createUser)) ||
              userInfo.role_name.includes('admin'))
          "
          type="text"
          :size="size"
          @click="rowFinish(row)"
          >完成
        </el-button>
        <el-button
          icon="el-icon-copy-document"
          v-if="
            !row.finish &&
            row.parentId > 0 &&
            permission.ni_por_budget_year_transfer &&
            ((row.personIdList && row.personIdList.includes(row.createUser)) ||
              userInfo.role_name.includes('admin'))
          "
          type="text"
          :size="size"
          @click="rowTransfer(row)"
          >跨年流转
        </el-button>
      </template>
    </avue-crud>
    <el-dialog
      title="预算数据导入"
      append-to-body
      :visible.sync="excelBox"
      width="555px"
    >
      <avue-form
        :option="excelOption"
        v-model="excelForm"
        :upload-after="uploadAfter"
      >
        <template slot="excelTemplate">
          <el-button type="primary" @click="handleTemplate">
            点击下载<i class="el-icon-download el-icon--right"></i>
          </el-button>
        </template>
      </avue-form>
    </el-dialog>
    <el-dialog
      title="使用人员维护"
      append-to-body
      :visible.sync="personVisible"
      width="555px"
    >
      <avue-form
        :option="personOption"
        v-model="personForm"
        @submit="handlePersonSubmit"
      ></avue-form>
    </el-dialog>
    <log-opt-dialog ref="logOptDialogRef" :module="module" />
    <budget-item-drawer ref="budgetItemDrawerRef" />
    <sub-dialog ref="subDialogRef" />
    <budget-repair-dialog ref="budgetRepairRef" />
  </basic-container>
</template>

<script>
import {
  add,
  finish,
  getDetail,
  getItemsPrintData,
  getList,
  getPrintData,
  remove,
  update,
  updatePerson,
} from "@/api/ni/por/budget-year";
import { getLazyList } from "@/api/ni/por/budget";
import { mapGetters } from "vuex";
import exForm from "@/views/plugin/workflow/mixins/ex-form";
import TaskDetail from "@/views/plugin/workflow/ops/detail";
import MaterialSelect from "@/views/ni/base/components/MaterialSelect";
import ProjectSelect from "@/views/ni/project/components/MyProjectSelect";
import LogOptDialog from "@/components/log-opt-dialog";
import { exportBlob } from "@/api/common";
import { getToken } from "@/util/auth";
import { downloadXls } from "@/util/util";
import BudgetItemDrawer from "@/views/ni/por/components/BudgetItemDrawer";
import { dateFormat } from "@/util/date";
import { hiprint } from "vue-plugin-hiprint";
import { loadPrintTemplate } from "@/api/system/printTemplate";
import SubDialog from "@/views/ni/project/components/SubDialog";
import BudgetRepairDialog from "@/views/ni/por/components/BudgetRepairDialog";
import FlowTimelinePopover from "@/components/wf-flow-timeline/popover";
import BudgetYearSelect from "@/views/ni/por/components/BudgetYearSelect.vue";
import { getPage as getItemPage } from "@/api/ni/por/budget-item";
import UserSelect from "@/components/user-select/index.vue";

export default {
  mixins: [exForm],
  components: {
    UserSelect,
    BudgetYearSelect,
    BudgetItemDrawer,
    BudgetRepairDialog,
    TaskDetail,
    MaterialSelect,
    ProjectSelect,
    LogOptDialog,
    SubDialog,
    FlowTimelinePopover,
  },
  beforeRouteEnter(to, from, next) {
    to.meta.keepAlive = true;
    next();
  },
  data() {
    return {
      excelBox: false,
      excelOption: {
        size: "mini",
        searchSize: "mini",
        submitBtn: false,
        emptyBtn: false,
        column: [
          {
            label: "模板上传",
            prop: "excelFile",
            type: "upload",
            drag: true,
            loadText: "模板上传中，请稍等",
            span: 24,
            data: {},
            propsHttp: {
              res: "data",
            },
            tip: "请上传 .xls,.xlsx 标准格式文件",
            action: "/api/ni/por/budget/year/import",
          },
          {
            label: "模板下载",
            prop: "excelTemplate",
            formslot: true,
            span: 24,
          },
        ],
      },
      excelForm: {},
      parentId: 0,
      module: "ni_por_budget",
      processDefKey: "process_project_budget_year",
      formKey: "wf_ex_por/BudgetYear",
      form: {
        costItems: [],
      },
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        searchLabelWidth: 110,
        lazy: true,
        tabs: true,
        tabsActive: 1,
        span: 8,
        labelWidth: 130,
        menuWidth: 180,
        size: "mini",
        searchSize: "mini",
        align: "center",
        indexLabel: "序号",
        searchIndex: 3,
        searchIcon: true,
        height: "auto",
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: false,
        viewBtn: false,
        selection: true,
        dialogClickModal: false,
        dialogFullscreen: false,
        column: [
          {
            label: "审批状态",
            prop: "status",
            type: "select",
            dicUrl: "/api/blade-system/dict/dictionary?code=data_status",
            dicData: [],
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            searchPlaceholder: " ",
            dataType: "number",
            fixed: "right",
            display: false,
            search: true,
          },
          {
            label: "预算年度",
            prop: "yearDate",
            type: "year",
            placeholder: " ",
            format: "yyyy",
            valueFormat: "yyyy",
            search: true,
            searchOrder: 99,
            clearable: false,
            rules: [
              {
                required: true,
                message: "请选择预算年度",
                trigger: "blur",
              },
            ],
          },
          {
            label: "预算主题",
            prop: "title",
            minWidth: 100,
            placeholder: " ",
            overHidden: true,
            prepend: "",
            search: true,
            searchOrder: 97,
            display: false,
          },
          {
            label: "预算主题",
            prop: "parentId",
            minWidth: 100,
            placeholder: " ",
            overHidden: true,
            prepend: "",
            hide: true,
            showColumn: false,
            rules: [
              {
                required: true,
                message: "请输入预算主题",
                trigger: "blur",
              },
            ],
          },
          {
            label: "预算编号",
            prop: "serialNo",
            placeholder: " ",
            searchPlaceholder: " ",
            overHidden: true,
            search: true,
            searchOrder: 98,
            rules: [
              {
                required: true,
                message: "请输入预算编号",
                trigger: "blur",
              },
            ],
          },
          {
            label: "账套",
            prop: "brand",
            type: "radio",
            dicUrl: "/api/blade-system/dict-biz/dictionary?code=ni_brand",
            value: "1",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            search: true,
            rules: [
              {
                required: true,
                message: "请选择账套",
                trigger: "blur",
              },
            ],
          },
          {
            label: "预算类型",
            prop: "type",
            search: true,
            placeholder: " ",
            type: "select",
            filterable: true,
            minWidth: 90,
            dicUrl: "/api/ni/por/type/list?status=0",
            props: {
              label: "name",
              value: "code",
              desc: "code",
            },
            typeformat(item, label, value) {
              return `${item[label]}(${item[value]})`;
            },
            rules: [
              {
                required: true,
                message: "请选择预算类型",
                trigger: "blur",
              },
            ],
          },
          {
            label: "使用人员",
            prop: "personIds",
            hide: true,
            showColumn: false,
            placeholder: " ",
            overHidden: true,
            rules: [
              {
                required: true,
                message: "请选择预算人员",
                trigger: "blur",
              },
            ],
          },
          {
            label: "使用人员",
            prop: "personNames",
            overHidden: true,
            display: false,
          },
          {
            label: "预算金额",
            prop: "totalAmount",
            labelTip: "如果没有明细，又需要登记金额时输入",
            type: "number",
            placeholder: " ",
            overHidden: true,
            hide: true,
            showColumn: false,
            precision: 2,
          },
          {
            label: "预算金额",
            prop: "amountStr",
            type: "number",
            controls: false,
            placeholder: " ",
            overHidden: true,
            display: false,
            precision: 2,
            minWidth: 100,
          },
          {
            label: "预算用途",
            prop: "remark",
            type: "textarea",
            overHidden: true,
            span: 24,
            minRows: 3,
            rules: [
              {
                required: true,
                message: "请输入预算用途",
                trigger: "blur",
              },
            ],
          },
          {
            label: "是否完成",
            prop: "finish",
            display: false,
            width: 70,
          },
          {
            label: "申请人",
            prop: "createUserName",
            display: false,
          },
          {
            label: "申请部门",
            prop: "createDeptName",
            display: false,
          },
          {
            label: "申请时间",
            prop: "createTime",
            type: "datetime",
            minWidth: 110,
            overHidden: true,
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
            searchRange: true,
            display: false,
            search: true,
          },
        ],
      },
      data: [],
      repairProcessDefKey: "process_project_budget_repair",
      repairFormKey: "wf_ex_por/BudgetRepair",
      personVisible: false,
      personOption: {
        labelWidth: 110,
        size: "mini",
        emptyBtn: false,
        span: 24,
        column: [
          {
            label: "预算使用人员",
            prop: "personIdList",
            type: "select",
            placeholder: " ",
            dicUrl:'/api/blade-user/user-vo-list',
            multiple: true,
            filterable: true,
            props: {
              label: "name",
              value: "id",
              desc: "deptName",
            },
            rules: [
              {
                required: true,
                message: "请选择预算人员",
                trigger: "blur",
              },
            ],
          },
        ],
      },
      personForm: {},
      personList: [],
      budgetPrintTemplate: null,
      budgetItemsPrintTemplate: null,
      export: {
        column: [
          {
            label: "审批状态",
            prop: "status",
          },
          {
            label: "申请人",
            prop: "createUserName",
          },
          {
            label: "申请部门",
            prop: "createDeptName",
          },
          {
            label: "申请时间",
            prop: "createTime",
          },
          {
            label: "预算年度",
            prop: "yearDate",
          },
          {
            label: "预算名称",
            prop: "title",
          },
          {
            label: "预算编号",
            prop: "serialNo",
          },
          {
            label: "预算类型",
            prop: "type",
          },

          {
            label: "金额",
            prop: "amount",
          },
          {
            label: "使用人员",
            prop: "personNames",
          },
          {
            label: "备注",
            prop: "remark",
          },

          {
            label: "预算用途",
            prop: "remark",
          },

          {
            label: "完成",
            prop: "finish",
          },
        ],
        itemColumn: [
          {
            label: "状态",
            prop: "budgetStatus",
          },
          {
            label: "申请人",
            prop: "createUserName",
          },
          {
            label: "申请时间",
            prop: "createTime",
          },
          {
            label: "预算名称",
            prop: "title",
          },
          {
            label: "预算编号",
            prop: "serialNo",
          },
          {
            label: "预算类型",
            prop: "type",
          },
          {
            label: "小项",
            prop: "subCode",
          },
          {
            label: "物/费",
            prop: "cost",
          },
          {
            label: "品名",
            prop: "materialName",
          },
          {
            label: "用途",
            prop: "purpose",
          },
          {
            label: "编码",
            prop: "materialCode",
          },
          {
            label: "规格型号",
            prop: "specification",
          },
          {
            label: "材质",
            prop: "quality",
          },
          {
            label: "国标",
            prop: "gb",
          },
          {
            label: "单位",
            prop: "unit",
          },
          {
            label: "数量",
            prop: "num",
          },
          {
            label: "单价",
            prop: "price",
          },
          {
            label: "金额",
            prop: "amount",
          },
          {
            label: "已采购数量",
            prop: "orderNum",
          },
          {
            label: "已采购金额",
            prop: "orderAmount",
          },
          {
            label: "备注",
            prop: "remark",
          },
          {
            label: "项目名称",
            prop: "projectTitle",
          },
          {
            label: "项目编号",
            prop: "projectSerialNo",
          },
          {
            label: "预算备注",
            prop: "budgetRemark",
          },
          {
            label: "压力容器",
            prop: "pv",
          },
          {
            label: "完成",
            prop: "used",
          },
        ],
      },
      budgetStatusDictKeyValue: {},
      unitDictKeyValue: {},
    };
  },
  created() {
    const year = this.findObject(this.option.column, "yearDate");
    year.searchValue = dateFormat(new Date(), "yyyy");
    this.query.yearDate = year.searchValue;
    this.$nextTick(() => {
      this.onLoad(this.page);
    });
    loadPrintTemplate(this.module).then((res) => {
      this.budgetPrintTemplate = JSON.parse(res.data.data.content);
    });
    loadPrintTemplate("ni_por_budget_items").then((res) => {
      this.budgetItemsPrintTemplate = JSON.parse(res.data.data.content);
    });
    this.dictInit();
  },
  computed: {
    ...mapGetters(["userInfo", "permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.ni_por_budget_year_add, false),
        viewBtn: this.vaildData(this.permission.ni_por_budget_year_view, false),
        delBtn: this.vaildData(
          this.permission.ni_por_budget_year_delete,
          false
        ),
        editBtn: this.vaildData(this.permission.ni_por_budget_year_edit, false),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  methods: {
    handleTitleConfirm(selectList) {
      if (selectList) {
        this.form.title = selectList[0].title;
        this.form.parentId = selectList[0].id;
        this.form.type = selectList[0].type;
        this.form.brand = selectList[0].brand;
        this.form.remark = selectList[0].remark;
      }
    },
    dictInit() {
      this.$http
        .get("/api/blade-system/dict/dictionary?code=data_status")
        .then((res) => {
          this.budgetStatusDictKeyValue = res.data.data.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });
      this.$http
        .get("/api/blade-system/dict-biz/dictionary?code=ni_material_unit")
        .then((res) => {
          this.unitDictKeyValue = res.data.data.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });
    },
    handleApply() {
      const form = { year: 2, items: [], costItems: [] };
      this.dynamicRoute(
        {
          processDefKey: this.processDefKey,
          formKey: this.formKey,
          form: encodeURIComponent(
            Buffer.from(JSON.stringify(form)).toString("utf8")
          ),
        },
        "start"
      );
    },
    handlePersonSubmit(form, done) {
      updatePerson(form)
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        })
        .finally(() => {
          this.personVisible = false;
        });
    },
    rowDetail(row) {
      this.$refs.budgetItemDrawerRef.init(row, row.status !== 0);
    },
    rowFinish(row) {
      this.$confirm("完成后将不能使用该预算,确定将选择预算完成?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return finish(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    rowTransfer(row) {
      this.$confirm("流转后将完成该预算，并创建新的年度预算,是否流转该预算?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return finish(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    async handlePrint(type) {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择要打印的数据");
        return;
      } else if (this.selectionList.length > 1) {
        this.$message.warning("只能选择一条数据");
        return;
      }
      let printData;
      let hiprintTemplate;
      if (type === "budget") {
        if (!this.budgetPrintTemplate) {
          this.$message.error("打印模板加载失败，请联系管理员");
          return;
        }
        const data = await getPrintData(this.selectionList[0].id);
        printData = data.data.data;
        hiprintTemplate = new hiprint.PrintTemplate({
          template: this.budgetPrintTemplate,
        });
      } else if (type === "items") {
        if (!this.budgetItemsPrintTemplate) {
          this.$message.error("打印模板加载失败，请联系管理员");
          return;
        }
        const data = await getItemsPrintData(this.selectionList[0].id);
        printData = {
          items: data.data.data,
        };
        hiprintTemplate = new hiprint.PrintTemplate({
          template: this.budgetItemsPrintTemplate,
        });
      }
      hiprintTemplate.print(printData);
    },
    handleTemplate() {
      exportBlob(
        `/api/ni/por/budget/year/export-template?type=${this.excelForm.type}&${
          this.website.tokenHeader
        }=${getToken()}`
      ).then((res) => {
        let fileName = decodeURI(
          res.headers["content-disposition"].split("=")[1]
        );
        downloadXls(res.data, fileName);
      });
    },
    handleExport(type) {
      if (type === "budget") {
        this.exportBudget();
      } else if (type === "item") {
        this.exportBudgetItem();
      }
    },
    exportBudget() {
      let msg = "确定要导出筛选出的所有预算数据?";
      if (this.selectionList.length > 0) {
        msg = "确定要导出选中的预算数据?";
      }
      this.$confirm(msg, {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        let exportData = [];
        if (this.selectionList.length > 0) {
          exportData = this.selectionList;
        } else if (this.selectionList.length === 0) {
          const query = this.query;
          if (query.createTime && query.createTime.length > 1) {
            query.createTimeStart = query.createTime[0];
            query.createTimeEnd = query.createTime[1];
          }
          if (!query.yearDate) {
            this.$message({
              type: "warning",
              message: "请选择预算年度!",
            });
            return;
          }
          query.descs = "id";
          const res = await getList(1, 99999, query);
          exportData = res.data.data.records;
        }
        exportData = exportData.map((item) => {
          return {
            ...item,
            status: this.budgetStatusDictKeyValue[item.status],
            finish: item.finish ? "是" : "否",
            amount: item.totalAmount ? item.totalAmount : item.amount,
          };
        });
        this.$Export.excel({
          title: "年度预算",
          columns: this.export.column,
          data: exportData,
        });
      });
    },
    exportBudgetItem() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择数据");
        return;
      } else if (this.selectionList.length > 1) {
        this.$message.warning("请选择一条数据");
        return;
      }
      this.$confirm("确定要导出选中的预算的明细数据?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const res = await getItemPage(1, 99999, {
          budgetId: this.selectionList[0].id,
        });
        let data = res.data.data.records;
        data = data.map((item) => {
          return {
            ...item,
            budgetStatus: this.budgetStatusDictKeyValue[item.budgetStatus],
            unit: this.unitDictKeyValue[item.unit],
            cost: item.cost ? "费用" : "实物",
            pv: item.pv ? "是" : "否",
            used: item.used ? "是" : "否",
            subCode: item.subCode ? `${item.subCode}` + item.subName : "",
          };
        });
        this.$Export.excel({
          title: "年度预算明细",
          columns: this.export.itemColumn,
          data,
        });
      });
    },
    handleImport(type) {
      const excelFile = this.findObject(this.excelOption.column, "excelFile");
      excelFile.action = "/api/ni/por/budget/year/import?type=" + type;
      this.$nextTick(() => {
        this.excelBox = true;
        this.excelForm.type = type;
      });
    },
    rowRepair(row) {
      this.$refs.budgetRepairRef.onShow(row);
    },
    handleLog() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择要查看的数据");
        return;
      } else if (this.selectionList.length > 1) {
        this.$message.warning("只能选择一条数据");
        return;
      }
      this.$refs.logOptDialogRef.init(this.selectionList[0].id);
    },
    rowSub(row) {
      this.$refs.subDialogRef.onShow(row);
    },
    rowPerson(row) {
      this.personForm = {
        id: row.id,
        personIdList: row.personIdList,
      };
      this.personVisible = true;
    },
    uploadAfter(res, done, column) {
      this.excelBox = false;
      this.refreshChange();
      done();
      if (res) {
        this.$confirm(res, "提示", {
          confirmButtonText: "确定",
          type: "warning",
          center: true,
        });
      }
    },
    rowSave(row, done, loading) {
      add(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
        }
      );
    },
    rowUpdate(row, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
        }
      );
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen(done, type) {
      const now = new Date();
      this.form.yearDate = now.getFullYear() + "";
      if ("add" === type) {
        this.form.status = 9;
        this.form.year = "2";
      } else if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then((res) => {
          const data = res.data.data;
          data.yearDate = data.yearDate ? data.yearDate + "" : null;
          this.form = data;
        });
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      const query = Object.assign(params, this.query);
      if (query.createTime && query.createTime.length > 1) {
        query.createTimeStart = query.createTime[0];
        query.createTimeEnd = query.createTime[1];
      }
      if (!query.yearDate) {
        this.$message({
          type: "warning",
          message: "请选择预算年度!",
        });
        return;
      }
      this.loading = true;
      query.descs = "id";
      getList(page.currentPage, page.pageSize, query).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        data.records.forEach((item) => {
          item.amountStr = Number(item.amount).toLocaleString("zh-CN", {
            minimumFractionDigits: 2,
          });
        });
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
    treeLoad(tree, treeNode, resolve) {
      const parentId = tree.id;
      getLazyList(parentId).then((res) => {
        resolve(res.data.data);
      });
    },
  },
};
</script>

<style></style>

import request from "@/router/axios";

export const getPage = (current, size, params) => {
  return request({
    url: "/api/ni/fin/cash/transfer/page",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/ni/fin/cash/transfer/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const remove = (ids) => {
  return request({
    url: "/api/ni/fin/cash/transfer/remove",
    method: "post",
    params: {
      ids,
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/ni/fin/cash/transfer/save",
    method: "post",
    data: row,
  });
};

export const update = (row) => {
  return request({
    url: "/api/ni/fin/cash/transfer/update",
    method: "post",
    data: row,
  });
};

export const toVoid = (ids) => {
  return request({
    url: "/api/ni/fin/cash/transfer/toVoid",
    method: "post",
    params: {
      ids,
    },
  });
};
export const red = (id) => {
  return request({
    url: "/api/ni/fin/cash/transfer/red",
    method: "post",
    params: {
      id,
    },
  });
};
export const post = (id) => {
  return request({
    url: "/api/ni/fin/cash/transfer/post",
    method: "post",
    params: {
      id,
    },
  });
};

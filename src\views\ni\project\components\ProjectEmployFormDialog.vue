<script>
import { mapGetters } from "vuex";
import {add, add1, getDetail, update, update1} from "@/api/ni/project/employ";

export default {
  data() {
    return {
      visible: false,
      option: {
        submitBtn: false,
        emptyBtn: false,
        size: "mini",
        span: 8,
        labelWidth: 110,
        column: [
          {
            label: "申请人",
            span: 8,
            display: true,
            prop: "createUserName",
            disabled: true,
            placeholder: " ",
          },
          {
            label: "申请部门",
            span: 8,
            display: true,
            prop: "createDeptName",
            readonly: false,
            placeholder: " ",
            disabled: true,
          },
          {
            label: "负责人",
            component: "wf-user-select",
            span: 8,
            params: {
              checkType: "checkbox",
              userUrl: "/api/blade-user/search/user?status=1",
            },
            prop: "chargerId",
            rules: [
              {
                required: true,
                message: "人员选择必须填写",
              },
            ],
          },
          {
            type: "input",
            label: "用工单号",
            span: 8,
            display: true,
            prop: "serialNo",
            readonly: false,
            placeholder: " 系统自动生成",
            disabled: true,
          },
          {
            type: "select",
            label: "上级单号",
            span: 8,
            display: true,
            props: {
              label: "serialNo",
              value: "serialNo",
              desc: "projectSerialNo",
            },
            prop: "parentSerialNo",
            placeholder: " ",
            remote: true,
            filterable: true,
            clearable: true,
            dicUrl:
              "/api/ni/project/employ/page?statuses=2,9,91&keyword={{key}}",
            dicFormatter: (res) => {
              return res.data.records;
            },
            change: ({ value }) => {
              if (value) {
                this.$http
                  .get("/api/ni/project/employ/detail?serialNo=" + value)
                  .then((res) => {
                    this.form.parentId = res.data.data.id;
                  });
              }
            },
          },
          {
            type: "select",
            label: "项目选择",
            span: 8,
            display: true,
            props: {
              label: "serialNo",
              value: "id",
              desc: "title",
            },
            prop: "projectId",
            required: true,
            rules: [
              {
                required: true,
                message: "项目选择必须填写",
              },
            ],
            placeholder: " ",
            dicUrl:
              "/api/ni/project/unionYearBudget/unFinish/page?status=9&keyword={{key}}",
            remote: true,
            dicMethod: "get",
            dicFormatter: (res) => {
              return res.data.records;
            },
            dicFlag: true,
            filterable: true,
          },
          {
            type: "select",
            label: "用工类型",
            span: 8,
            display: true,
            props: {
              label: "dictValue",
              value: "dictKey",
              desc: "",
            },
            prop: "opType",
            required: true,
            rules: [
              {
                required: true,
                message: "用工类型必须填写",
              },
            ],
            placeholder: " ",
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_project_dispatch_type",
            remote: false,
            dicMethod: "get",
            dicFlag: true,
            dataType: "string",
            dicFormatter: (res) => {
              return res.data;
            },
          },
          {
            type: "textarea",
            label: "用工内容",
            span: 24,
            display: true,
            prop: "content",
            minRows: 2,
            required: true,
            rules: [
              {
                required: true,
                message: "用工内容必须填写",
              },
            ],
            placeholder: " ",
            showWordLimit: true,
          },
          {
            type: "daterange",
            label: "用工日期",
            span: 8,
            display: true,
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            prop: "workDate",
            change: ({ value }) => {
              const d1 = new Date(value[0]);
              const d2 = new Date(value[1]);
              // 计算两个日期的时间差（毫秒）
              const timeDiff = Math.abs(d2.getTime() - d1.getTime());
              // 计算天数
              const days = Math.ceil(timeDiff / (1000 * 3600 * 24)) + 1;
              if (!this.form.personNum)
                this.form.applyWorkingHours = days * 8.5;
              else
                this.form.applyWorkingHours =
                  days * 8.5 * Number(this.form.personNum);
              this.form.workDateStart = value[0];
              this.form.workDateEnd = value[1];
            },
            required: true,
            rules: [
              {
                required: true,
                message: "用工日期必须填写",
              },
            ],
          },
          {
            type: "number",
            label: "预计工时",
            controls: true,
            span: 8,
            display: true,
            prop: "applyWorkingHours",
            required: true,
            rules: [
              {
                required: true,
                message: "预计工时必须填写",
              },
            ],
            placeholder: " ",
          },
          {
            type: "number",
            label: "每日用工数",
            controls: true,
            span: 8,
            display: true,
            prop: "personNum",
            required: true,
            rules: [
              {
                required: true,
                message: "用工数必须填写",
              },
            ],
            placeholder: " ",
            value: "1",
            step: 1,
            change: ({ value }) => {
              const d1 = new Date(this.form.workDateStart);
              const d2 = new Date(this.form.workDateEnd);
              // 计算两个日期的时间差（毫秒）
              const timeDiff = Math.abs(d2.getTime() - d1.getTime());
              // 计算天数
              const days = Math.ceil(timeDiff / (1000 * 3600 * 24)) + 1;
              if (!value) this.form.applyWorkingHours = days * 8.5;
              else this.form.applyWorkingHours = days * 8.5 * Number(value);
            },
          },
          {
            type: "radio",
            label: "是否夜班",
            dicData: [
              {
                label: "否",
                value: "0",
              },
              {
                label: "是",
                value: "1",
              },
            ],
            span: 8,
            props: {
              label: "label",
              value: "value",
            },
            prop: "night",
            required: true,
            rules: [
              {
                required: true,
                message: "是否夜班必须填写",
              },
            ],
            value: "0",
            dataType: "number",
            border: false,
            button: true,
            size: "mini",
          },
          {
            type: "radio",
            label: "材料备齐",
            dicData: [
              {
                label: "否",
                value: "0",
              },
              {
                label: "是",
                value: "1",
              },
            ],
            span: 8,
            display: true,
            prop: "ready",
            required: true,
            rules: [
              {
                required: true,
                message: "材料备齐必须填写",
              },
            ],
            value: "1",
            dataType: "number",
            border: false,
            button: true,
            size: "mini",
          },
          {
            type: "radio",
            label: "指定人员",
            dicData: [
              {
                label: "否",
                value: "0",
              },
              {
                label: "是",
                value: "1",
              },
            ],
            span: 8,
            display: true,
            props: {
              label: "label",
              value: "value",
            },
            prop: "person",
            required: true,
            rules: [
              {
                required: true,
                message: "指定人员必须填写",
              },
            ],
            value: "0",
            change: ({ value }) => {
              const appointUserId = this.findObject(
                this.option.column,
                "appointUserId"
              );
              appointUserId.display = value === 1;
            },
            dataType: "number",
            border: false,
            button: true,
            size: "mini",
          },
          {
            label: "人员选择",
            component: "wf-user-select",
            span: 8,
            params: {
              checkType: "checkbox",
              userUrl:
                "/api/blade-user/search/user?status=1&deptName=研发辅助一组,研发辅助二组",
            },
            display: false,
            prop: "appointUserId",
            required: true,
            rules: [
              {
                required: true,
                message: "人员选择必须填写",
              },
            ],
          },
          {
            type: "radio",
            label: "图纸落实",
            dicData: [
              {
                label: "否",
                value: "-1",
              },
              {
                label: "是",
                value: "1",
              },
              {
                label: "不需要",
                value: "0",
              },
            ],
            span: 8,
            display: true,
            props: {
              label: "label",
              value: "value",
            },
            prop: "drawing",
            required: true,
            rules: [
              {
                required: true,
                message: "指定人员必须填写",
              },
            ],
            value: "0",
            button: true,
            size: "mini",
          },
          {
            type: "radio",
            label: "生产落实",
            dicData: [
              {
                label: "否",
                value: "-1",
              },
              {
                label: "是",
                value: "1",
              },
              {
                label: "不需要",
                value: "0",
              },
            ],
            span: 8,
            display: true,
            props: {
              label: "label",
              value: "value",
            },
            prop: "communication",
            required: true,
            rules: [
              {
                required: true,
                message: "指定人员必须填写",
              },
            ],
            value: "0",
            button: true,
            size: "mini",
          },
          {
            type: "radio",
            label: "是否领料",
            dicData: [
              {
                label: "否",
                value: "0",
              },
              {
                label: "是",
                value: "1",
              },
            ],
            span: 8,
            display: true,
            props: {
              label: "label",
              value: "value",
            },
            prop: "picking",
            required: true,
            rules: [
              {
                required: true,
                message: "指定人员必须填写",
              },
            ],
            value: "0",
            button: true,
            size: "mini",
            change: ({ value }) => {
              const pickingItems = this.findObject(
                this.option.column,
                "pickingItems"
              );
              pickingItems.display = value === 1;
            },
            dataType: "number",
          },
          {
            type: "textarea",
            label: "领料明细",
            span: 24,
            display: false,
            prop: "pickingItems",
            minRows: 2,
            showWordLimit: true,
            required: true,
            rules: [
              {
                required: true,
                message: "领料明细必须填写",
              },
            ],
            placeholder: " ",
          },
        ],
      },
      form: {},
    };
  },
  computed: {
    ...mapGetters(["userInfo"]),
  },
  methods: {
    onApply(form) {
      Object.keys(this.form).forEach((key) => (this.form[key] = null));
      this.form = { ...form };
      this.form.createUserName = this.userInfo.user_name;
      this.form.createDeptName = this.userInfo.dept_name;
      this.form.chargerId = this.userInfo.user_id;
      this.visible = true;
    },
    onEdit(id) {
      Object.keys(this.form).forEach((key) => (this.form[key] = null));
      this.option.detail = false;
      getDetail(id).then((res) => {
        this.form = res.data.data;
        this.visible = true;
      });
    },
    onShow(id) {
      Object.keys(this.form).forEach((key) => (this.form[key] = null));
      this.form.inventories = [];
      this.option.detail = true;
      getDetail(id).then((res) => {
        this.form = res.data.data;
        this.visible = true;
      });
    },
    handleDraft() {
      this.form.status = 1;
      this.$refs.form.submit();
    },
    handlePublish() {
      this.form.status = 2;
      this.$refs.form.submit();
    },
    handleConfirm(form, done) {
      let res;
      if (form.id && form.id > 0) {
        res = update1(form);
      } else {
        res = add1(form);
      }
      res
        .then(() => {
          this.visible = false;
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$emit("confirm", form);
        })
        .finally(() => {
          done();
        });
    },
  },
};
</script>

<template>
  <el-dialog :visible.sync="visible" title="用工申请" fullscreen append-to-body>
    <avue-form
      v-if="visible"
      :option="option"
      v-model="form"
      ref="form"
      @submit="handleConfirm"
    >
      <template #menuForm="{ size }">
        <el-button type="info" :size="size" @click="handleDraft"
          >保存草稿
        </el-button>
        <el-button type="warning" :size="size" @click="handlePublish"
          >发布
        </el-button>
      </template>
    </avue-form>
  </el-dialog>
</template>

<style scoped></style>

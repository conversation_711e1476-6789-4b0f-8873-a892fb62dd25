import request from "@/router/axios";

export const getCoreReport = (area) => {
  return request({
    url: "/api/ni/fg/dashboard/core-report",
    method: "get",
    params: {
      area,
    },
  });
};
export const getDailyInventorySum = (area, startDate, endDate) => {
  return request({
    url: "/api/ni/fg/dashboard/daily-inventory-sum",
    method: "get",
    params: {
      area,
      startDate,
      endDate,
    },
  });
};

export const getInventorySumGroupByDepotId = (area) => {
  return request({
    url: "/api/ni/fg/dashboard/inventory-sum-group-by-depot-id",
    method: "get",
    params: {
      area,
    },
  });
};
export const getDailyInAndOutboundSum = (area, startDate, endDate) => {
  return request({
    url: "/api/ni/fg/dashboard/daily-in-and-outbound-sum",
    method: "get",
    params: {
      area,
      startDate,
      endDate,
    },
  });
};
export const getInventorySumGroupBySpec = (area) => {
  return request({
    url: "/api/ni/fg/dashboard/inventory-sum-group-by-spec",
    method: "get",
    params: { area },
  });
};

export const getInventorySumGroupBySkuId = (area) => {
  return request({
    url: "/api/ni/fg/dashboard/inventory-sum-group-by-skuId",
    method: "get",
    params: { area },
  });
};

<template>
  <div>
    <el-input
      v-model="loanName"
      :size="size"
      :disabled="disabled"
      suffix-icon="el-icon-search"
      @focus="bpFocus"
    />
    <el-dialog
      ref="loan-dialog"
      v-dialogdrag
      custom-class="loan-dialog"
      :visible.sync="visible"
      title="借款单选择"
      width="60%"
      :before-close="handleClose"
      append-to-body
    >
      <avue-crud
        v-if="isInit && visible"
        :option="option"
        :table-loading="loading"
        :data="data"
        :page.sync="page"
        v-model="form"
        ref="crud"
        @search-change="searchChange"
        @search-reset="searchReset"
        @selection-change="selectionList = $event"
        @current-change="page.currentPage = $event"
        @size-change="page.pageSize = $event"
        @row-click="rowClick"
        @on-load="onLoad"
      >
        <template #radio="{ row }">
          <el-radio v-model="form.radio" :label="row.id"><i></i></el-radio>
        </template>
        <template #createUserSearch="{ disabled, size, index, row }">
          <user-select
            v-model="form.createUser"
            :size="size"
            :disabled="disabled"
          ></user-select>
        </template>
        <template #status="{ row, index }">
          <el-tag v-if="row.status === 0" size="mini" type="info">
            {{ statusDictKeyValue[row.status] }}
          </el-tag>
          <el-tag v-else-if="row.status === 1" size="mini" effect="plain">
            {{ statusDictKeyValue[row.status] }}
          </el-tag>
          <el-tag v-else-if="row.status === 2" size="mini" effect="plain">
            {{ statusDictKeyValue[row.status] }}
          </el-tag>
          <el-tag v-else-if="row.status === 3" size="mini" type="danger">
            {{ statusDictKeyValue[row.status] }}
          </el-tag>
          <el-tag
            v-else-if="row.status === 4"
            size="mini"
            type="warning"
            effect="plain"
          >
            {{ statusDictKeyValue[row.status] }}
          </el-tag>
          <el-tag
            v-else-if="row.status === 5"
            size="mini"
            type="warning"
            effect="plain"
          >
            {{ statusDictKeyValue[row.status] }}
          </el-tag>
          <el-tag
            v-else-if="row.status === 6"
            size="mini"
            type="danger"
            effect="plain"
          >
            {{ statusDictKeyValue[row.status] }}
          </el-tag>
          <el-tag
            v-else-if="row.status === 9"
            size="mini"
            type="success"
            effect="plain"
          >
            {{ statusDictKeyValue[row.status] }}
          </el-tag>
        </template>
        <template #brand="{ row, disabled, size, index }">
          <el-tag
            v-if="row.brand"
            :size="size"
            :effect="row.brand === '1' ? 'dark ' : 'plain'"
          >
            {{ brandDictKeyValue[row.brand] }}
          </el-tag>
        </template>
        <template #currency="{ row, disabled, size, index }">
          <el-tag v-if="row.currency" :size="size" effect="plain">
            {{ currencyDictKeyValue[row.currency] }}
          </el-tag>
        </template>
      </avue-crud>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose" size="mini">取 消</el-button>
        <el-button type="primary" @click="handleConfirm" size="mini"
          >确 定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { getDetail, getList as getPage } from "@/api/ni/fin/loan";
import UserSelect from "@/components/user-select";
import { mapGetters } from "vuex";
import Emitter from "element-ui/src/mixins/emitter";

export default {
  mixins: [Emitter],
  name: "FinLoanSelect",
  props: {
    value: {
      type: String,
    },
    userId: String,
    size: {
      type: String,
      default: "mini",
    },
    disabled: Boolean,
  },
  components: {
    UserSelect,
  },
  watch: {
    value: {
      handler(val) {
        if (val && !this.isInit && !this.visible) {
          //如果是逗号分隔的多个 ID，拆分处理
          const ids = val.split(",").map((id) => id.trim());
          if (ids.length > 1) {
            //处理多选情况，逐个获取详情
            Promise.all(ids.map((id) => getDetail(id)))
              .then((responses) => {
                this.selectionList = responses
                  .map((res) => res.data.data)
                  .filter((data) => data);
                this.loanName = this.loanSerialNo;
              })
              .catch((err) => {
                console.error("获取借款单详情失败:", err);
              });
          } else {
            //单选情况
            getDetail(val).then((res) => {
              if (res.data.data) {
                this.selectionList = [res.data.data];
                this.loanName = this.loanSerialNo;
              }
            });
          }
        }
      },
      immediate: true,
    },
  },

  data() {
    return {
      statusDict: [],
      statusDictKeyValue: {},
      brandDict: [],
      brandDictKeyValue: {},
      currencyDict: [],
      currencyDictKeyValue: {},
      payableStatusDict: [],
      payableStatusDictKeyValue: {},
      loanName: "",
      visible: false,
      isInit: false,
      form: {},
      query: {},
      loading: false,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      data: [],
      props: {
        records: "data.data.records",
        total: "data.data.total",
      },
      option: {
        size: "mini",
        searchSize: "mini",
        align: "center",
        menu: false,
        addBtn: false,
        header: false,
        border: true,
        tip: false,
        reserveSelection: true,
        highlightCurrentRow: true,
        gutter: 5,
        searchMenuSpan: 6,
        selection: true,
        column: [
          {
            label: "申请人",
            prop: "createUser",
            type: "input",
            display: false,
            hide: true,
            showColumn: false,
            search: true,
          },
          {
            label: "申请人",
            prop: "createUserName",
            type: "input",
            display: false,
          },
          {
            label: "申请部门",
            prop: "createDeptName",
            type: "input",
            display: false,
            hide: true,
          },
          {
            label: "申请日期",
            prop: "applyTime",
            type: "date",
            format: "yyyy-MM-dd",
            dataType: "date",
            valueFormat: "yyyy-MM-dd",
            minWidth: 100,
            overHidden: true,
            pickerOptions: {
              disabledDate(time) {
                return time.getTime() > Date.now();
              },
            },
          },
          {
            label: "主题",
            prop: "title",
            rules: [
              {
                required: true,
                message: "请输入标题",
                trigger: "blur",
              },
            ],
          },
          {
            label: "借款单号",
            prop: "serialNo",
            placeholder: "系统自动生成",
            disabled: true,
            search: true,
          },
          {
            label: "借款事由",
            prop: "reason",
            type: "textarea",
            overHidden: true,
            minRows: 1,
            search: true,
            rules: [
              {
                required: true,
                message: "请输入借款事由",
                trigger: "blur",
              },
            ],
          },
          {
            label: "币种",
            prop: "currency",
            type: "select",
            search: true,
            dictData: [],
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            value: "RMB",
            placeholder: " ",
            rules: [
              {
                required: true,
                message: "请选择币种",
                trigger: "blur",
              },
            ],
            control: (val) => {
              if (val === "RMB") {
                return {
                  excRate: {
                    display: false,
                    value: 1,
                  },
                };
              } else {
                return {
                  excRate: {
                    display: true,
                  },
                };
              }
            },
          },
          {
            label: "借款金额",
            prop: "amount",
            type: "number",
            overHidden: true,
            precision: 2,
            rules: [
              {
                required: true,
                message: "请输入借款金额",
                trigger: "blur",
              },
            ],
          },
          {
            label: "银行卡号",
            prop: "bankCardNo",
            rules: [
              {
                required: true,
                message: "请输入银行卡号",
                trigger: "blur",
              },
            ],
          },
          {
            label: "开户行",
            prop: "bankName",
            rules: [
              {
                required: true,
                message: "请输入开户行",
                trigger: "blur",
              },
            ],
          },
          {
            label: "开户人姓名",
            prop: "bankAccountName",
            rules: [
              {
                required: true,
                message: "请输入开户人姓名",
                trigger: "blur",
              },
            ],
          },
          {
            label: "是否付款",
            prop: "pay",
            minWidth: 69,
            type: "select",
            dicData: [
              {
                label: "是",
                value: true,
              },
              {
                label: "否",
                value: false,
              },
            ],
            addDisplay: false,
            editDisplay: false,
            search: true,
          },
          {
            label: "付款人员",
            prop: "financialUserName",
            addDisplay: false,
            editDisplay: false,
          },
          {
            label: "付款日期",
            prop: "payDate",
            type: "date",
            minWidth: 100,
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            addDisplay: false,
            editDisplay: false,
          },
          {
            label: "付款金额",
            prop: "payAmount",
            type: "number",
            overHidden: true,
            precision: 2,
            addDisplay: false,
            editDisplay: false,
          },
          // {
          //   label: "已开销",
          //   prop: "expensedAmount",
          //   type: "number",
          //   precision: 2,
          //   display: false,
          // },
          // {
          //   label: "剩余借款",
          //   prop: "remainAmount",
          //   type: "number",
          //   precision: 2,
          //   display: false,
          // },
          {
            label: "是否报销",
            prop: "costEa",
            type: "select",
            dicData: [
              {
                label: "是",
                value: true,
              },
              {
                label: "否",
                value: false,
              },
            ],
            addDisplay: false,
            editDisplay: false,
            search: true,
          },
          {
            label: "备注",
            prop: "remark",
            type: "textarea",
            hide: true,
            span: 24,
          },
        ],
      },
    };
  },
  computed: {
    ...mapGetters(["userInfo", "permission"]),
    ids() {
      let ids = new Set();
      this.selectionList.forEach((ele) => {
        ids.add(ele.id);
      });
      return Array.from(ids).join(",");
    },
    reasons() {
      let reasons = new Set();
      this.selectionList.forEach((ele) => {
        reasons.add(ele.reason);
      });
      return Array.from(reasons).join(",");
    },
    loanSerialNo() {
      let loanSerialNo = new Set();
      this.selectionList.forEach((ele) => {
        loanSerialNo.add(ele.serialNo);
      });
      return Array.from(loanSerialNo).join(",");
    },
    // 实际付款金额汇总计算
    totalAmount() {
      return this.selectionList
        .reduce((sum, item) => {
          return sum + (parseFloat(item.payAmount) || 0);
        }, 0)
        .toFixed(2);
    },
  },
  mounted() {
    this.init();
  },
  methods: {
    bpFocus() {
      this.visible = true;
      this.selectionList = [];
      // this.refreshChange();
    },
    init() {
      if (!this.isInit) {
        this.isInit = true;
      }
      this.dictInit();
    },
    dictInit() {
      this.$http
        .get("/api/blade-system/dict/dictionary?code=data_status")
        .then((res) => {
          const column = this.findObject(this.option.column, "status");
          column.dicData = res.data.data;
          this.statusDict = res.data.data;
          this.statusDictKeyValue = this.statusDict.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });
      this.$http
        .get("/api/blade-system/dict/dictionary?code=currency")
        .then((res) => {
          const column = this.findObject(this.option.column, "currency");
          column.dicData = res.data.data;
          this.currencyDict = res.data.data;
          this.currencyDictKeyValue = this.currencyDict.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });
    },
    handleConfirm() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.loanName = this.loanSerialNo;
      this.$emit("input", this.ids);
      this.$emit("confirm", this.selectionList, this.totalAmount);
      this.$nextTick(() => {
        this.dispatch("ElFormItem", "el.form.blur", [this.ids]);
      });
      this.handleClose();
    },
    handleClose(done) {
      // this.selectionClear()
      this.visible = false;
      if (done && typeof done == "function") done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionClear() {
      this.selectionList = [];
      if (this.$refs.crud) this.$refs.crud.toggleSelection();
    },
    rowClick(row) {
      this.selectionList = [row];
      this.$set(this.form, "radio", row.id);
    },
    async changeDefaultChecked() {
      if (!this.value) {
        this.selectionList = [];
        this.$set(this.form, "radio", "");
        this.loanName = "";
        return;
      }

      const ids = this.value.split(",").map((id) => id.trim()); // 拆分 ID
      let rows = [];

      // 从当前数据中查找已有的数据
      rows = this.data.filter((d) => ids.includes(d.id.toString()));
      const missingIds = ids.filter(
        (id) => !rows.some((row) => row.id.toString() === id)
      );

      // 如果有未找到的id，调用接口获取
      if (missingIds.length > 0) {
        const responses = await Promise.all(
          missingIds.map((id) => getDetail(id))
        );
        const fetchedRows = responses
          .map((res) => res.data.data)
          .filter((data) => data);
        rows = [...rows, ...fetchedRows];
      }

      if (rows.length > 0) {
        this.selectionList = rows;
        this.$set(this.form, "radio", ids[0]);
        this.loanName = this.loanSerialNo;
      } else {
        this.selectionList = [];
        this.$set(this.form, "radio", "");
        this.loanName = "";
      }
    },
    onLoad(page, params = {}) {
      this.loading = true;
      const query = {
        ...params,
        ...this.query,
        all: this.permission.ni_fin_loan_select,
        costEaSelect: true,
        // createUser: this.userInfo.user_id,
        deptId: this.userInfo.dept_id,
        costEa: false,
        pay: true,
      };
      console.log(this.userInfo.dept_id);
      getPage(
        page.currentPage,
        page.pageSize,
        query,
        this.userInfo.dept_id
      ).then((res) => {
        this.page.total = this.getAsVal(res, this.props.total);
        this.data = this.getAsVal(res, this.props.records) || [];
        this.loading = false;
        this.changeDefaultChecked();
      });
    },
    getAsVal(obj, bind = "") {
      let result = this.deepClone(obj);
      if (this.validatenull(bind)) return result;
      bind.split(".").forEach((ele) => {
        if (!this.validatenull(result[ele])) {
          result = result[ele];
        } else {
          result = "";
        }
      });
      return result;
    },
  },
};
</script>
<style lang="scss">
.loan-dialog {
  display: flex;
  flex-direction: column;
  margin: 0 !important;
  position: absolute;
  top: 40%;
  left: 50%;
  transform: translate(-50%, -40%);
  max-height: calc(100% - 30px);
  max-width: calc(100% - 30px);

  .el-dialog__body {
    flex: 1;
    overflow: auto;
  }
}
</style>

export default {
  height: 'auto',
  calcHeight: 30,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: true,
  selection: true,
  dialogClickModal: false,
  column: [
    {
      label: "日期范围",
      prop: "interval",
      type: "date",
      format: "yyyy-MM-dd",
      valueFormat: "yyyy-MM-dd",
      display: false,
      search: true,
      searchRange: true,
      hide: true,
      pickerOptions: {
        shortcuts: [
          {
            text: '上月',
            onClick(picker) {
               // 获取当前日期  
              let today = new Date();  
            
              // 计算上个月的日期  
              let lastMonth = new Date(today);  
              lastMonth.setMonth(lastMonth.getMonth() - 1);  
            
              // 上个月的第一天  
              let firstDayOfMonth = new Date(lastMonth.getFullYear(), lastMonth.getMonth(), 1);  
            
              // 上个月的最后一天，将日期设置为下个月的第0天  
              let lastDayOfMonth = new Date(lastMonth.getFullYear(), lastMonth.getMonth() + 1, 0);  
  

              picker.$emit('pick', [firstDayOfMonth, lastDayOfMonth]);
            }
          },
          {
          text: '本月',
          onClick(picker) {
            // 获取当前日期  
            let today = new Date();  
          
            // 本月的第一天  
            let firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);  
          
            // 本月的最后一天，将日期设置为下个月的第0天  
            let lastDayOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);

            picker.$emit('pick', [firstDayOfMonth, lastDayOfMonth]);
          }
        }
      
      ]
      }
    },
    {
      label: "部门",
      prop: "deptIds",
      type: "tree",
      dicUrl: "/api/blade-system/dept/tree",
      props: {
        label: "title",
        value: "id",
      },
      multiple: true,
      checkStrictly: true,
      display: false,
      hide: true,
      search: true,
      searchMultiple: true,
      showColumn: false,
      cascader: ['userIds']
    },
    {
      label: "人员",
      prop: "userIds",
      type: "select",
      dicUrl: "/api/blade-user/user-list?deptIds={{key}}",
      props: {
        label: "account",
        value: "id",
      },
      filterable: true,
      display: false,
      multiple: true,
      hide: true,
      search: true,
      showColumn: false,
    },
    {
      label: "班组",
      prop: "groupIds",
      type: "select",
      dicUrl: "/api/ni/pa/group/list?ascs=['sn']",
      props: {
        label: "name",
        value: "id",
      },
      multiple: true,
      checkStrictly: true,
      display: false,
      hide: true,
      search: true,
      searchMultiple: true,
      showColumn: false,
    },

    {
      label: "姓名",
      prop: "userName",
      type: "input",
    },
    {
      label: "部门名称",
      prop: "deptName",
      type: "input",
    },
    {
      label: "申请时间",
      prop: "createTime",
      type: "input",
    },
    {
      label: "请假开始时间",
      prop: "startDatetime",
      type: "input",
    },
    {
      label: "请假结束时间",
      prop: "endDatetime",
      type: "input",
    },
    {
      label: "请假总时长",
      prop: "totalDatetime",
      type: "input",
    },
    {
      label: "申请理由",
      prop: "applyReason",
      type: "input",
    },
    {
      label: "状态",
      prop: "status",
      type: "input",
    },
    {
      label: "审核状态",
      prop: "reviewStatus",
      type: "select",
      dicData: [
        {
          label: "已提交",
          value: 1
        },
        {
          label: "已审核",
          value: 2
        },
        {
          label: "已驳回",
          value: 3
        },
        {
          label: "已终止",
          value: 4
        },
      ],
      multiple: false, 
      checkStrictly: false, 
      display: true, 
      hide: false,   
      search: true,  
      showColumn: false, 
    },
    {
      label: "",
      prop: "id",
      type: "input",
      hide: true
    }
  ]
}
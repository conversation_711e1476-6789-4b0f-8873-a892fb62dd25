import request from '@/router/axios';

export const getList = (params) => {
  return request({
    url: '/api/ni/por/inquiry/list',
    method: 'get',
    params: {
      ...params,
    }
  })
}

export const getPage = (current, size, params) => {
  return request({
    url: '/api/ni/por/inquiry/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}


export const getDetail = (id) => {
  return request({
    url: '/api/ni/por/inquiry/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/ni/por/inquiry/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/ni/por/inquiry/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/ni/por/inquiry/submit',
    method: 'post',
    data: row
  })
}

export const saveOrUpdateBatch = (rows) => {
  return request({
    url: '/api/ni/por/inquiry/submitBatch',
    method: 'post',
    data: rows
  })
}




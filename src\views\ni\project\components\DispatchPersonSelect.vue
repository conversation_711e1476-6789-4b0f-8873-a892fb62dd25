<template>
  <el-dialog
    ref="us-dialog"
    v-dialogdrag
    custom-class="us-dialog"
    :visible.sync="visible"
    :title="title"
    width="60%"
    :before-close="handleClose"
    append-to-body
  >
    <template #title>
      {{ title }}
      <template v-if="dispatchPersonNum > 0">
        <el-divider direction="vertical" />
        <span>派工情况：{{ data.length }}/{{ dispatchPersonNum }}</span>
      </template>
      <template v-if="currentTag">
        <el-divider direction="vertical" />
        当前选择的是：
        <el-tag size="small">
          {{ currentTag }}
        </el-tag>

        <el-button
          style="margin-left: 10px"
          type="primary"
          size="mini"
          @click="handlePersonTagChangeSubmit"
          >修改
        </el-button>
        <el-button size="mini" @click="handlePersonTagCancel">取消</el-button>
      </template>
    </template>
    <div style="margin-bottom: 10px">
      <span
        v-for="(item, index) of personTags"
        :key="index"
        style="cursor: pointer"
        @click="handlePersonTagChange(item.dictKey)"
      >
        {{ item.dictValue }}:
        <template v-if="tagsKeyValue[item.dictKey]">
          <el-tag
            size="mini"
            :type="tagsKeyValue[item.dictKey].type"
            :effect="tagsKeyValue[item.dictKey].effect"
          >
            {{ tagsKeyValue[item.dictKey].value }}
          </el-tag>
        </template>
        <el-tag size="mini" effect="plain" v-else>
          {{ item.dictKey }}
        </el-tag>
      </span>
    </div>
    <el-descriptions
      v-loading="loading"
      direction="vertical"
      :column="5"
      border
      size="mini"
    >
      <el-descriptions-item v-for="(item, index) in deptData" :key="index">
        <template slot="label">
          <div class="dept-header-box">
            <span :style="{ color: deptColorKv[item.id] }">{{
              `${item.leaderName}(${item.deptName})`
            }}</span>
            <el-color-picker
              v-model="deptColorKv[item.id]"
              size="mini"
              v-if="detail"
              color-format="hex"
              :predefine="predefineColors"
              @change="handleDeptColorChange($event, item.id)"
            >
            </el-color-picker>
          </div>
        </template>
        <div class="person-box" :style="{ height: '300px' }">
          <el-scrollbar style="height: 100%">
            <div
              style="
                display: flex;
                flex-flow: column nowrap;
                align-items: flex-start;
              "
            >
              <el-checkbox
                v-for="(item, index) in personMap[item.id]"
                :key="index"
                :value="selectionList.includes(item.userId)"
                :label="item.userId"
                :disabled="['年', '假','婚','产','陪产','工伤','病','丧','学'].includes(item.attendance)"
                @change="
                  (checked) => handleCheckboxChange(checked, item.userId)
                "
              >
                <span :style="item.first ? { fontWeight: 'bold' } : {}">
                  {{ item.realName }}
                </span>
                <span
                  v-if="item.attendance && item.attendance !== 'g'"
                  style="color: red"
                >
                  [{{ item.attendance }}]
                </span>
                <template v-if="item.tags && item.tags.length > 0">
                  <template v-for="tag in item.tags">
                    <span>
                      <template v-if="tagsKeyValue[tag]">
                        <el-tag
                          size="mini"
                          :type="tagsKeyValue[tag].type"
                          :effect="tagsKeyValue[tag].effect"
                        >
                          {{ tagsKeyValue[tag].value }}
                        </el-tag>
                      </template>
                      <el-tag size="mini" effect="plain" v-else>
                        {{ item.tag }}
                      </el-tag>
                    </span>
                  </template>
                </template>
                <span
                  v-if="item.workHours && item.workHours > 0"
                  style="color: #003399"
                >
                  ({{ Number(item.workHours) }}H)
                </span>
              </el-checkbox>
            </div>
          </el-scrollbar>
        </div>
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">
          <div class="dept-header-box">
            <span>自定义派工人员</span>
            <div>
              <el-button
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click="handleOtherPersonSelect"
              ></el-button>
              <el-button
                size="mini"
                type="text"
                icon="el-icon-delete"
                @click="handleOtherPersonClear"
              ></el-button>
            </div>
          </div>
        </template>
        <div class="person-box" :style="{ height: '300px' }">
          <el-scrollbar style="height: 100%">
            <div
              style="
                display: flex;
                flex-flow: column nowrap;
                align-items: flex-start;
              "
            >
              <el-checkbox
                v-for="(item, index) in personMap[1]"
                :key="index"
                :value="selectionList.includes(item.userId)"
                :label="item.userId"
                :disabled="
                  ['H', 'P', '假', '休', 'K', 'N', 'B', 'S', 'J','年','婚','产','陪产','工伤','病','丧','学'].includes(
                    item.attendance
                  )
                "
                @change="
                  (checked) => handleCheckboxChange(checked, item.userId)
                "
              >
                <span :style="item.first ? { fontWeight: 'bold' } : {}">
                  {{ item.realName }}
                </span>
                <span
                  v-if="item.attendance && item.attendance !== 'g'"
                  style="color: red"
                >
                  [{{ item.attendance }}]
                </span>
                <template v-if="item.tags && item.tags.length > 0">
                  <template v-for="tag in item.tags">
                    <span>
                      <template v-if="tagsKeyValue[tag]">
                        <el-tag
                          size="mini"
                          :type="tagsKeyValue[tag].type"
                          :effect="tagsKeyValue[tag].effect"
                        >
                          {{ tagsKeyValue[tag].value }}
                        </el-tag>
                      </template>
                      <el-tag size="mini" effect="plain" v-else>
                        {{ item.tag }}
                      </el-tag>
                    </span>
                  </template>
                </template>
                <span
                  v-if="item.workHours && item.workHours > 0"
                  style="color: #003399"
                >
                  ({{ Number(item.workHours) }}H)
                </span>
              </el-checkbox>
            </div>
          </el-scrollbar>
        </div>
      </el-descriptions-item>
      <el-descriptions-item
        v-for="index in 5 - ((deptData.length + 1) % 5)"
        :key="index"
        label=" "
      >
      </el-descriptions-item>
    </el-descriptions>
    <span slot="footer" class="dialog-footer" v-if="!detail">
      <el-button @click="handleClose" size="mini">取 消</el-button>
      <el-button type="primary" @click="handleConfirm" size="mini">
        确 定
      </el-button>
    </span>
    <user-select-dialog
      ref="userSelectDialogRef"
      multiple
      @confirm="handleOtherPersonSelectConfirm"
    />
  </el-dialog>
</template>

<script>
import {
  changeDeptColor,
  changePersonTags,
  dispatchUserList,
  getDeptColors,
  getPersonTags,
} from "@/api/ni/project/employ";
import { getChildrenByCode as getDeptList } from "@/api/system/dept";
import UserSelectDialog from "@/components/user-select-dialog/index.vue";
import { changeOtherPersons } from "@/api/ni/project/dispatch";
import { remove } from "@/api/ni/tracing/boxInfo";

export default {
  components: { UserSelectDialog },
  props: {
    multiple: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      tagsKeyValue: {
        登: {
          type: "warning",
          effect: "dark",
          value: "登",
        },
        限: {
          type: "danger",
          effect: "plain",
          value: "限",
        },
        叉: {
          type: "info",
          effect: "plain",
          value: "叉",
        },
        铝: {
          type: "success",
          effect: "plain",
          value: "铝",
        },
        氩: {
          type: "success",
          effect: "dark",
          value: "氩",
        },
        激: {
          type: "warning",
          effect: "plain",
          value: "激",
        },
      },
      predefineColors: [
        "#ff4500",
        "#ff8c00",
        "#ffd700",
        "#90ee90",
        "#00ced1",
        "#1e90ff",
        "#c71585",
        "rgba(255, 69, 0, 0.68)",
        "rgb(255, 120, 0)",
        "hsv(51, 100, 98)",
        "hsva(120, 40, 94, 0.5)",
        "hsl(181, 100%, 37%)",
        "hsla(209, 100%, 56%, 0.73)",
        "#c7158577",
      ],
      personTags: [],
      deptColorKv: {},
      currentTag: "",
      dispatchDate: null,
      deptCode: null,
      isInit: false,
      detail: true,
      title: "人员选择",
      visible: false,
      deptData: [],
      personMap: {},
      form: {},
      query: {},
      loading: false,
      value: null,
      selectionList: [],
      data: [],
      option: {
        search: false,
        searchEnter: true,
        size: "mini",
        searchSize: "mini",
        align: "center",
        menu: false,
        addBtn: false,
        header: false,
        border: true,
        tip: false,
        reserveSelection: true,
        highlightCurrentRow: true,
        gutter: 5,
        searchMenuSpan: 6,
        selection: true,
        column: [
          {
            label: "姓名",
            prop: "realName",
            filters: true,
            overHidden: true,
          },
          {
            label: "部门",
            prop: "deptName",
            filters: true,
            overHidden: true,
          },
          {
            label: "考勤",
            prop: "attendance",
            overHidden: true,
          },
          {
            label: "已派工时",
            prop: "workHours",
            overHidden: true,
          },
        ],
      },
    };
  },
  computed: {
    ids() {
      let ids = new Set();
      this.selectionList.forEach((ele) => {
        ids.add(ele);
      });
      return Array.from(ids).join(",");
    },
    dispatchPersonNum() {
      return this.data.filter((item) => item.workHours && item.workHours > 0)
        .length;
    },
  },
  created() {
    this.$http
      .get(
        "/api/blade-system/dict-biz/dictionary?code=ni_project_dispatch_person_tag"
      )
      .then((res) => {
        this.personTags = res.data.data;
      });
    getDeptColors().then((res) => {
      this.deptColorKv = res.data.data.reduce((pre, cur) => {
        pre[cur.deptId] = cur.color;
        return pre;
      }, {});
    });
  },
  methods: {
    handleCheckboxChange(checked, userId) {
      if (checked) {
        // 避免重复添加
        if (!this.selectionList.includes(userId)) {
          this.selectionList.push(userId);
        }
      } else {
        const index = this.selectionList.indexOf(userId);
        if (index !== -1) {
          this.selectionList.splice(index, 1);
        }
      }
    },
    handleDeptColorChange(color, deptId) {
      changeDeptColor({ deptId, color }).then(() => {
        this.$message({
          type: "warning",
          message: "操作成功",
        });
      });
    },
    onShow(dispatchDate, deptCode) {
      this.dispatchDate = dispatchDate;
      this.deptCode = deptCode;
      this.selectionList = [];
      this.data = [];
      this.detail = true;
      this.title = "派工人员";
      this.onLoad();
      this.visible = true;
    },
    handlePersonTagChange(key) {
      if (!this.detail) return;
      this.currentTag = key;
      getPersonTags(this.currentTag).then((res) => {
        this.selectionList = res.data.data.map((item) => item.userId);
      });
    },
    handlePersonTagChangeSubmit() {
      if (!this.detail) return;
      changePersonTags({ tag: this.currentTag, userIds: this.ids }).then(
        (res) => {
          this.$message({
            type: "warning",
            message: "操作成功",
          });
          this.currentTag = "";
          this.selectionList = [];
          this.onLoad();
        }
      );
    },
    handlePersonTagCancel() {
      if (!this.detail) return;
      this.currentTag = "";
      this.selectionList = [];
    },
    onSelect(dispatchDate, userIds, deptCode, workHours = "8:00~17:30") {
      this.dispatchDate = dispatchDate;
      this.deptCode = deptCode;
      this.value = userIds;
      this.selectionList = [];
      if (userIds) {
        userIds.split(",").forEach((userId) => this.selectionList.push(userId));
      }
      this.data = [];
      this.detail = false;
      this.title = `人员选择(${workHours})`;
      this.onLoad();
      this.visible = true;
    },
    handleClose(done) {
      this.visible = false;
      if (done && typeof done == "function") done();
    },
    handleOtherPersonSelect() {
      const user = this.personMap["1"];
      let ids;
      if (user) ids = user.map((item) => item.userId).join(",");
      else ids = null;
      this.$refs.userSelectDialogRef.init(ids);
    },
    handleOtherPersonClear() {
      this.$confirm("确定将自定义人员清除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return changeOtherPersons("");
        })
        .then(() => {
          this.onLoad();
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    handleOtherPersonSelectConfirm(ids) {
      changeOtherPersons(ids).then(() => {
        this.$message({
          type: "warning",
          message: "操作成功",
        });
        this.onLoad();
      });
    },
    async handleConfirm() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      // 构建 userId -> user 的 Map，提升查找效率
      const userMap = this.data.reduce((map, user) => {
        map[user.userId] = user;
        return map;
      }, {});

      // 按 selectionList 顺序提取用户
      const users = this.selectionList
        .map((userId) => userMap[userId])
        .filter((user) => user); // 过滤掉无效 userId（可选）
      const isAssigned = users.filter(
        (user) =>
          user.workHours &&
          user.workHours > 0 &&
          (!this.value || !this.value.split(",").includes(user.userId))
      );
      let confirmResult = "confirm";
      if (isAssigned.length > 0) {
        const userName = isAssigned.map((user) => user.realName).join("、");
        try {
          confirmResult = await this.$confirm(
            '<span style="color: #F56C6C;font-weight: bold">' +
              userName +
              "</span>已有其他指派，是否取消原有指派？",
            "提示",
            {
              distinguishCancelAndClose: true,
              dangerouslyUseHTMLString: true,
              confirmButtonText: "是",
              cancelButtonText: "否",
              type: "warning",
            }
          );
        } catch (action) {
          confirmResult = action;
        }
      }
      if (confirmResult === "confirm") {
        this.$emit("confirm", users, true);
        this.handleClose();
      } else if (confirmResult === "cancel") {
        this.$emit("confirm", users, false);
        this.handleClose();
      }
    },
    onLoad() {
      this.loading = true;
      dispatchUserList({
        dispatchDate: this.dispatchDate,
        deptCode: this.deptCode,
      }).then((res) => {
        this.data =
          res.data.data.filter((item) => item.realName !== "马琳燕") || [];
        this.personMap = {};
        const deptData = {};
        this.data.forEach((item) => {
          if (!this.personMap[item.deptId]) {
            this.personMap[item.deptId] = [];
          }
          this.personMap[item.deptId].push(item);
          if (item.deptId !== "1")
            deptData[item.deptId] = {
              id: item.deptId,
              deptName: item.deptName,
              leaderName: item.leaderName,
            };
        });
        this.deptData = Object.values(deptData);
        this.loading = false;
      });
    },
  },
};
</script>

<style lang="scss">
.us-dialog {
  display: flex;
  flex-direction: column;
  margin: 0 !important;
  position: absolute;
  top: 40%;
  left: 50%;
  transform: translate(-50%, -40%);
  max-height: calc(100% - 30px);
  max-width: calc(100% - 30px);

  .el-dialog__body {
    flex: 1;
    overflow: auto;
  }
}

.dept-header-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 10px;

  .el-color-picker {
    margin-left: auto;
  }
}
</style>

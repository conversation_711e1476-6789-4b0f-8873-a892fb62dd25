<template>
  <div class="tree-select-container">
    <el-select
      v-model="selectedValue"
      :placeholder="placeholder"
      :multiple="multiple"
      :clearable="clearable"
      :filterable="filterable"
      :disabled="disabled"
      :filter-method="filterTree"
      popper-class="tree-select-popper"
      @click.native="handleSelectClick"
      @clear="handleClear"
      ref="select"
      class="tree-select"
    >
      <el-option
        v-for="item in displayOptions"
        :key="item.value"
        :label="item.label"
        :value="item.value"
        style="display: none;"
      />
    </el-select>

    <!-- 自定义下拉内容 -->
    <div
      v-show="dropdownVisible"
      class="tree-dropdown"
      :style="dropdownStyle"
    >
      <div class="tree-wrapper">
        <el-tree
          ref="tree"
          :data="treeData"
          :props="treeProps"
          :node-key="nodeKey"
          :show-checkbox="multiple"
          :check-strictly="checkStrictly"
          :default-expand-all="defaultExpandAll"
          :expand-on-click-node="false"
          :filter-node-method="filterNode"
          :highlight-current="!multiple"
          @node-click="handleNodeClick"
          @check="handleNodeCheck"
          @check-change="handleCheckChange"
        />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TreeSelect',
  props: {
    // 树形数据
    data: {
      type: Array,
      default: () => []
    },
    // 树形配置
    props: {
      type: Object,
      default: () => ({
        children: 'children',
        label: 'label',
        value: 'value'
      })
    },
    // 节点唯一标识
    nodeKey: {
      type: String,
      default: 'value'
    },
    // 是否多选
    multiple: {
      type: Boolean,
      default: false
    },
    // 是否可清空
    clearable: {
      type: Boolean,
      default: true
    },
    // 是否可搜索
    filterable: {
      type: Boolean,
      default: false
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    },
    // 占位符
    placeholder: {
      type: String,
      default: '请选择'
    },
    // 是否严格模式（父子节点不关联）
    checkStrictly: {
      type: Boolean,
      default: false
    },
    // 是否默认展开所有节点
    defaultExpandAll: {
      type: Boolean,
      default: false
    },
    // 当前选中值
    value: {
      type: [String, Number, Array],
      default: () => []
    }
  },
  data() {
    return {
      dropdownVisible: false,
      selectedValue: this.multiple ? [] : '',
      selectedNodes: this.multiple ? [] : null,
      dropdownStyle: {
        position: 'absolute',
        top: '100%',
        left: '0',
        width: '100%',
        zIndex: 1000
      }
    }
  },
  computed: {
    treeData() {
      return this.data
    },
    treeProps() {
      return this.props
    },
    displayOptions() {
      if (this.multiple) {
        return this.selectedNodes.map(node => ({
          label: node[this.treeProps.label],
          value: node[this.treeProps.value]
        }))
      } else {
        return this.selectedNodes ? [{
          label: this.selectedNodes[this.treeProps.label],
          value: this.selectedNodes[this.treeProps.value]
        }] : []
      }
    }
  },
  watch: {
    value: {
      handler(newVal) {
        this.selectedValue = newVal
        this.updateSelectedNodes()
      },
      immediate: true
    },
    data: {
      handler() {
        this.updateSelectedNodes()
      },
      deep: true
    }
  },
  mounted() {
    this.initDropdown()
    this.updateSelectedNodes()
  },
  methods: {
    // 初始化下拉框
    initDropdown() {
      // 监听点击事件，点击外部关闭下拉框
      document.addEventListener('click', this.handleDocumentClick)
    },

    // 处理select点击
    handleSelectClick() {
      if (this.disabled) return
      this.toggleDropdown()
    },

    // 切换下拉框显示状态
    toggleDropdown() {
      this.dropdownVisible = !this.dropdownVisible
      if (this.dropdownVisible) {
        this.$nextTick(() => {
          this.updateDropdownPosition()
        })
      }
    },

    // 更新下拉框位置
    updateDropdownPosition() {
      const selectEl = this.$refs.select.$el
      const rect = selectEl.getBoundingClientRect()
      this.dropdownStyle = {
        position: 'absolute',
        top: `${rect.height}px`,
        left: '0',
        width: `${rect.width}px`,
        zIndex: 1000
      }
    },

    // 处理文档点击事件
    handleDocumentClick(event) {
      const selectEl = this.$refs.select.$el
      const dropdownEl = this.$el.querySelector('.tree-dropdown')

      if (!selectEl.contains(event.target) && !dropdownEl.contains(event.target)) {
        this.dropdownVisible = false
      }
    },

    // 处理节点点击（单选模式）
    handleNodeClick(data, node) {
      if (this.multiple) return

      this.selectedNodes = data
      this.selectedValue = data[this.treeProps.value]
      this.dropdownVisible = false

      this.$emit('input', this.selectedValue)
      this.$emit('change', this.selectedValue, data, node)
    },

    // 处理节点选中（多选模式）
    handleNodeCheck(data, checkedInfo) {
      if (!this.multiple) return

      this.selectedNodes = checkedInfo.checkedNodes
      this.selectedValue = this.selectedNodes.map(node => node[this.treeProps.value])

      this.$emit('input', this.selectedValue)
      this.$emit('change', this.selectedValue, this.selectedNodes, checkedInfo)
    },

    // 树节点过滤
    filterTree(query) {
      this.$refs.tree.filter(query)
    },

    // 树节点过滤方法
    filterNode(value, data) {
      if (!value) return true
      return data[this.treeProps.label].indexOf(value) !== -1
    },

    // 处理选中状态变化
    handleCheckChange(data, checked, indeterminate) {
      // 可以在这里处理选中状态变化的逻辑
    },

    // 处理清空
    handleClear() {
      this.selectedNodes = this.multiple ? [] : null
      this.selectedValue = this.multiple ? [] : ''
      this.$emit('input', this.selectedValue)
      this.$emit('change', this.selectedValue, this.selectedNodes)
    },

    // 更新选中的节点
    updateSelectedNodes() {
      if (!this.treeData.length) return

      if (this.multiple) {
        this.selectedNodes = []
        if (Array.isArray(this.selectedValue) && this.selectedValue.length) {
          this.selectedValue.forEach(value => {
            const node = this.findNodeByValue(this.treeData, value)
            if (node) {
              this.selectedNodes.push(node)
            }
          })
        }
      } else {
        if (this.selectedValue) {
          this.selectedNodes = this.findNodeByValue(this.treeData, this.selectedValue)
        } else {
          this.selectedNodes = null
        }
      }
    },

    // 根据值查找节点
    findNodeByValue(data, value) {
      for (let item of data) {
        if (item[this.treeProps.value] === value) {
          return item
        }
        if (item[this.treeProps.children] && item[this.treeProps.children].length) {
          const found = this.findNodeByValue(item[this.treeProps.children], value)
          if (found) return found
        }
      }
      return null
    },

    // 获取选中的节点
    getSelectedNodes() {
      return this.selectedNodes
    },

    // 设置选中值
    setValue(value) {
      this.selectedValue = value
      this.updateSelectedNodes()
    }
  },

  beforeDestroy() {
    document.removeEventListener('click', this.handleDocumentClick)
  }
}
</script>

<style scoped>
.tree-select-container {
  position: relative;
  display: inline-block;
}

.tree-select {
  width: 100%;
}

.tree-dropdown {
  background: white;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  max-height: 300px;
  overflow: auto;
}

.tree-wrapper {
  padding: 8px;
}

.tree-wrapper .el-tree {
  background: transparent;
}

.tree-wrapper .el-tree-node__content {
  height: 32px;
  line-height: 32px;
}

.tree-wrapper .el-tree-node__content:hover {
  background-color: #f5f7fa;
}
</style>

<style>
/* 隐藏el-select自带的下拉框，只显示自定义的树形下拉框 */
.el-select-dropdown.el-popper.tree-select-popper {
  display: none !important;
}
</style>

<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template #red="{ row, index }">
        <el-tag size="mini" type="danger" effect="dark" v-if="row.red">
          是
        </el-tag>
        <el-tag size="mini" type="info" effect="plain" v-else>否</el-tag>
      </template>
      <template #voucher="{ row, size }">
        <el-tag size="mini" type="warning" effect="plain" v-if="row.voucher">
          是
        </el-tag>
        <el-tag size="mini" type="info" effect="dark" v-else>否</el-tag>
      </template>
      <template #yonyouSync="{ row, size }">
        <template v-if="row.yonyouSync">
          <span v-for="(item, index) in row.yonyouSync" :key="index">
            {{ yonyouSyncSequenceDictKeyValue[item.sequence] }}:
            <el-tag :size="size">
              {{ yonyouSyncStateDictKeyValue[item.value] }}
            </el-tag>
            ;
            <template v-if="item.id">
              <span style="font-weight: bolder">id:</span>
              {{ item.id }}
            </template>
            <template v-if="item.errMsg"> :{{ item.errMsg }} </template>
            <br/>
          </span>
        </template>
      </template>
      <template #status="{ row, index }">
        <el-tag v-if="row.status === 1" size="mini" type="info">
          {{ row.$status }}
        </el-tag>
        <el-tag v-else-if="row.status === 2" size="mini">
          {{ row.$status }}
        </el-tag>
        <el-tag v-else-if="row.status === 3" size="mini" type="warning">
          {{ row.$status }}
        </el-tag>
        <el-tag v-else size="mini" type="danger">
          {{ row.$status }}
        </el-tag>
      </template>
      <template #serialNo="{ row, index }">
        <el-button
          style="user-select: all"
          size="mini"
          @click="$refs.crud.rowView(row, index)"
          type="text"
        >{{ row.serialNo }}
        </el-button>
      </template>
      <template #subType="{ row, disabled, size, index }">
        <el-tag
          v-if="row.subType === '1'"
          :size="size"
          type="warning"
          effect="dark"
        >{{ row.$subType }}
        </el-tag>
        <el-tag v-else-if="row.subType === '2'" :size="size" effect="dark"
        >{{ row.$subType }}
        </el-tag>
        <el-tag
          v-else-if="row.subType === '3'"
          type="danger"
          :size="size"
          effect="dark"
        >{{ row.$subType }}
        </el-tag>
        <el-tag v-else-if="row.subType === '4'" :size="size" effect="plain"
        >{{ row.$subType }}
        </el-tag>
        <el-tag
          v-else-if="row.subType === '5'"
          type="danger"
          :size="size"
          effect="plain"
        >{{ row.$subType }}
        </el-tag>
        <el-tag
          v-else-if="row.subType === '9'"
          type="warning"
          :size="size"
          effect="plain"
        >{{ row.$subType }}
        </el-tag>
      </template>
      <template #porOrderSerialNo="{ row, index }">
        <span v-if="row.orderId">{{ row.porOrderSerialNo }}</span>
      </template>
      <template #brand="{ row, disabled, size, index }">
        <el-tag
          v-if="row.brand"
          :size="size"
          type="success"
          :effect="row.brand === '1' ? 'dark ' : 'plain'"
        >
          {{ row.$brand }}
        </el-tag>
      </template>
      <template #currency="{ row, disabled, size, index }">
        <el-tag v-if="row.currency" :size="size" effect="plain">
          {{ row.$currency }}
        </el-tag>
      </template>
      <template #amount="{ row, disabled, size, index }">
        <span
          v-if="row.amount < 0"
          style="color: #f56c6c; font-weight: bolder"
        >{{
            Number(row.amount).toLocaleString("zh-CN", {
              minimumFractionDigits: 2,
            })
          }}</span
        >
        <template v-else>
          <el-badge
            v-if="row.redAmount"
            :value="row.redAmount"
            style="margin-top: 10px; margin-right: 40px"
          >
            <span>
              {{
                Number(row.amount).toLocaleString("zh-CN", {
                  minimumFractionDigits: 2,
                })
              }}
            </span>
          </el-badge>
          <span v-else>
            {{
              Number(row.amount).toLocaleString("zh-CN", {
                minimumFractionDigits: 2,
              })
            }}
          </span>
        </template>
      </template>
      <template #sourceForm="{ disabled, size, index }">
        <fin-payable-apply-select
          v-model="form.source"
          :size="size"
          :multiple="false"
          :disabled="disabled"
          :params="{
            status: 9,
            toVoid: '0',
            payStates: '0,1',
            brand: form.brand,
            supplierId: form.relatedId,
          }"
          :before-select="beforeContractSelect"
          @confirm="handleSourceConfirm"
        />
      </template>
      <template #costItemIdForm="{ disabled, size, index }">
        <por-order-cost-item-select
          v-model="form.costItemId"
          :label.sync="form.costItemName"
          :size="size"
          :disabled="disabled"
          multiple
          :params="{
            payApplyStates: '0,1',
            brand: form.brand,
            supplierId: form.supplierId,
          }"
          :before-select="beforeContractSelect"
          @submit="handleCostItemSubmit"
        />
      </template>
      <template #supplierForm="{ disabled, size, index, type }">
        <span v-if="type === 'view'">{{ form.supplier }}</span>
        <supplier-multiple-select
          v-else
          v-model="form.supplier"
          :size="size"
          :multiple="false"
          :disabled="disabled"
          @clear="handleSupplierClear"
          @submit="handleSupplierSubmit"
        />
      </template>
      <template #financialUserIdForm="{ disabled, size, index }">
        <user-select
          v-model="form.financialUserId"
          :size="size"
          :disabled="disabled"
        />
      </template>
      <template #amountForm="{ size }">
        <el-input-number
          :size="size"
          style="width: 100%"
          v-model="form.amount"
          precision="2"
          controls-position="right"
          :max="form.unPayAmount"
          @change="handleAmount"
        ></el-input-number>
      </template>
      <template #itemsForm="{ row, index, size }">
        <payable-apply-item v-model="form.items"/>
      </template>
      <template #paymentType="{ row, size, index }">
        <el-tag
          v-if="row.paymentType === '1'"
          :size="size"
          type=""
          effect="plain"
        >
          {{ row.$paymentType }}
        </el-tag>
        <el-tag
          v-else-if="row.paymentType === '2'"
          :size="size"
          type="success"
          effect="plain"
        >
          {{ row.$paymentType }}
        </el-tag>
        <el-tag
          v-else-if="row.paymentType === '3'"
          :size="size"
          type="info"
          effect="plain"
        >
          {{ row.$paymentType }}
        </el-tag>
        <el-tag
          v-else-if="row.paymentType === '4'"
          :size="size"
          type="danger"
          effect="plain"
        >
          {{ row.$paymentType }}
        </el-tag>
        <el-tag
          v-else-if="row.paymentType === '5'"
          :size="size"
          type="warning"
          effect="plain"
        >
          {{ row.$paymentType }}
        </el-tag>
        <el-tag
          v-else-if="row.paymentType === '6'"
          :size="size"
          type=""
          effect="dark"
        >
          {{ row.$paymentType }}
        </el-tag>
        <el-tag
          v-else-if="row.paymentType === '7'"
          :size="size"
          type="danger"
          effect="plain"
        >
          {{ row.$paymentType }}
        </el-tag>
        <el-tag
          v-else-if="row.paymentType === '8'"
          :size="size"
          type="warning"
          effect="plain"
        >
          {{ row.$paymentType }}
        </el-tag>
        <el-tag v-else :size="size">
          {{ row.$paymentType }}
        </el-tag>
      </template>
      <template #menuLeft>
        <el-dropdown @command="handleAdd">
          <el-button
            size="mini"
            icon="el-icon-plus"
            type="primary"
            v-if="permission.finPayable_add"
          >
            新增<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="pay">付款</el-dropdown-item>
            <el-dropdown-item command="porRed"
            >采购退货退款(红付)
            </el-dropdown-item>
            <el-dropdown-item command="otherRed">
              其他退款(红付)
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-button
          type="danger"
          size="mini"
          icon="el-icon-delete"
          plain
          v-if="permission.finPayable_delete"
          @click="handleDelete"
        >删 除
        </el-button>
        <el-button
          type="primary"
          size="mini"
          icon="el-icon-download"
          plain
          v-if="permission.finPayable_export"
          @click="handleExport"
        >导 出
        </el-button>
        <el-button
          type="warning"
          size="mini"
          icon="el-icon-refresh"
          plain
          v-if="permission.finPayable_yonyou_sync"
          @click="handleYonyouSync"
        >用友同步
        </el-button>
        <el-button
          icon="el-icon-printer"
          type="info"
          size="mini"
          v-if="permission.finPayable_print"
          @click="handlePrint"
        >
          打 印
        </el-button>
        <!-- 修改付款账户 -->
        <el-button
          type="warning"
          size="mini"
          icon="el-icon-edit"
          plain
          v-if="userInfo.role_name.includes('admin')"
          @click="handleLedger"
        >修改付款账户
        </el-button>
      </template>
      <template #menu="{ row, index, size }">
        <el-button
          type="text"
          icon="el-icon-edit"
          :size="size"
          v-if="permission.finPayable_edit && [1, 3].includes(row.status)"
          @click="rowEdit(row, index)"
        >编 辑
        </el-button>
        <el-button
          type="text"
          icon="el-icon-s-promotion"
          :size="size"
          v-if="[1, 3].includes(row.status)"
          @click="rowSubmit(row)"
        >提交
        </el-button>
        <el-button
          type="text"
          icon="el-icon-delete"
          :size="size"
          v-if="row.status === 2"
          @click="rowVoid(row)"
        >作废
        </el-button>
        <el-button
          type="text"
          icon="el-icon-refresh-left"
          :size="size"
          v-if="
            row.status === 2 &&
            Math.abs(Number(row.amount)) >
              Math.abs(Number(row.redAmount ? row.redAmount : 0))
          "
          @click="rowRed(row)"
        >退款
        </el-button>
        <el-button
          type="text"
          icon="el-icon-refresh-left"
          :size="size"
          v-if="row.status === 2 && row.red"
          @click="rowSource(row)"
        >关联单据
        </el-button>
        <el-button
          type="text"
          icon="el-icon-refresh"
          :size="size"
          v-if="
            row.status === 2 &&
            !row.voucher &&
            row.applyAmount === row.amount &&
            row.voucherMark
          "
          @click="rowVoucher(row)"
        >转凭证
        </el-button>
        <el-button
          type="text"
          icon="el-icon-refresh"
          :size="size"
          v-if="row.status === 2 && row.voucher && !row.red"
          @click="rowPay(row)"
        >转付款单
        </el-button>
        <el-button
          type="text"
          icon="el-icon-refresh"
          :size="size"
          v-if="permission.finPayable_yonyou_sync && row.status !== 4"
          @click="rowYonyouSync(row)"
        >同步用友
        </el-button>
        <el-button
          type="text"
          icon="el-icon-menu"
          :size="size"
          @click="rowItems(row)"
        >明细
        </el-button>
      </template>
    </avue-crud>
    <payable-item-drawer ref="payableItemRef"/>
    <fin-voucher-form
      ref="finVoucherFormRef"
      v-model="voucherForm"
      @submit="onLoad(page)"
    />
    <el-dialog
      title="用友同步"
      append-to-body
      :visible.sync="yonyou.visible"
      width="355px"
    >
      <avue-form
        v-if="yonyou.visible"
        ref="yonyouSyncRef"
        :option="yonyou.option"
        v-model="yonyou.form"
        @submit="handleYonyouSyncSubmit"
      ></avue-form>
    </el-dialog>
    <fin-payable-select-dialog
      ref="payableSelectRef"
      :params="{
        red: false,
        status: 2,
      }"
      @onConfirm="handleSourceChangeConfirm"
    />
    <payable-form-dialog ref="payableFormDialogRef" @submit="onLoad(page)"/>
    <el-dialog
      title="账号选择"
      append-to-body
      :visible.sync="ledger.visible"
      width="355px"
    >
      <avue-form
        v-if="ledger.visible"
        ref="ledgerFormRef"
        :option="ledger.option"
        v-model="ledger.form"
        @submit="handleLedgerSubmit"
      ></avue-form>
    </el-dialog>
  </basic-container>
</template>

<script>
import {
  add,
  getDetail,
  getPage,
  remove,
  submit,
  sync,
  syncRange,
  toVoid,
  trans2Pay,
  update,
  changeSource,
  getList, changeLedger,
} from "@/api/ni/fin/payable";
import {getDetail as getApplyDetail} from "@/api/ni/fin/payable-apply";
import {mapGetters} from "vuex";
import SupplierMultipleSelect from "@/views/ni/base/components/SupplierSelect";
import PorOrderSelect from "@/views/ni/por/components/OrderSelect";
import {dateFormat, dateNow1} from "@/util/date";
import UserSelect from "@/components/user-select";
import FinPayableApplySelect from "@/views/ni/fin/components/FinPayableApplySelect";
import ContractSelect from "@/views/ni/base/components/ContractSelect";
import {numToCapital} from "@/util/util";
import PorOrderCostItemSelect from "@/views/ni/por/components/OrderCostItemSelect";
import PayableApplyItem from "@/views/ni/fin/components/PayableApplyItem";
import FinVoucherForm from "@/views/ni/fin/components/FinVoucherForm";
import PayableItemDrawer from "@/views/ni/fin/components/PayableItemDrawer";
import PayableFormDialog from "@/views/ni/fin/components/PayableFormDialog";
import FinPayableSelectDialog from "@/views/ni/fin/components/FinPayableSelectDialog";
import {hiprint} from "vue-plugin-hiprint";
import {loadPrintTemplate} from "@/api/system/printTemplate";

export default {
  components: {
    FinPayableSelectDialog,
    PayableFormDialog,
    FinVoucherForm,
    PorOrderCostItemSelect,
    FinPayableApplySelect,
    SupplierMultipleSelect,
    PorOrderSelect,
    UserSelect,
    ContractSelect,
    PayableApplyItem,
    PayableItemDrawer,
  },
  data() {
    const PAY_TYPE_PAY = "2";
    return {
      payType: PAY_TYPE_PAY,
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        addBtn: false,
        searchEnter: true,
        saveBtn: false,
        updateBtn: false,
        cancelBtn: false,
        editBtn: false,
        delBtn: false,
        labelWidth: 110,
        span: 8,
        searchIndex: 3,
        searchIcon: true,
        size: "mini",
        searchSize: "mini",
        align: "center",
        height: "auto",
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        selection: true,
        dialogClickModal: false,
        column: [
          {
            label: "状态",
            prop: "status",
            dicData: [],
            search: true,
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            dataType: "number",
            display: false,
            type: "select",
            width: 75,
          },
          {
            label: "编号",
            prop: "serialNo",
            placeholder: "系统自动生成",
            search: true,
            disabled: true,
            overHidden: true,
            minWidth: 100,
          },
          {
            label: "付款类型",
            prop: "subType",
            placeholder: " ",
            search: true,
            disabled: true,
            overHidden: true,
            width: 90,
            type: "select",
            dicData: [],
            props: {
              label: "dictValue",
              value: "dictKey",
            },
          },
          {
            label: "账套",
            prop: "brand",
            placeholder: " ",
            type: "radio",
            overHidden: true,
            dicData: [],
            cascader: ["ledgerId"],
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            search: true,
            width: 70,
            rules: [
              {
                required: true,
                message: "请选择账套",
                trigger: "blur",
              },
            ],
          },
          {
            label: "供应商",
            prop: "supplier",
            type: "input",
            overHidden: true,
            minWidth: 100,
          },
          {
            label: "供应商",
            prop: "relatedId",
            remote: true,
            type: "select",
            dicUrl:
              "/api/ni/base/supplier/info/page?status=2&blacklist=0&keyword={{key}}",
            props: {
              label: "name",
              value: "id",
              desc: "code",
            },
            dicFormatter: (data) => {
              return data.data.records;
            },
            search: true,
            hide: true,
            showColumn: false,
            overHidden: true,
            minWidth: 100,
            display: false,
          },
          {
            label: "开户银行",
            prop: "financeBank",
            minWidth: 110,
            hide: true,
            placeholder: " ",
          },
          {
            label: "收款人全称",
            prop: "financeName",
            placeholder: " ",
            hide: true,
          },
          {
            label: "银行账号",
            prop: "financeAccount",
            placeholder: " ",
            minWidth: 180,
            hide: true,
          },

          {
            label: "付款申请",
            prop: "source",
            type: "select",
            dicUrl: "/api/ni/fin/payableApply/page?serialNo={{key}}",
            remote: true,
            props: {
              label: "serialNo",
              value: "id",
            },
            dicFormatter: (data) => {
              return data.data.records;
            },
            hide: true,
            showColumn: false,
            placeholder: " ",
            search: true,
            minWidth: 100,
          },
          {
            label: "单据来源",
            prop: "sourceSerialNo",
            display: false,
            overHidden: true,
            placeholder: " ",
            minWidth: 100,
          },
          {
            label: "付款订单",
            prop: "porOrderSerialNo",
            display: false,
            type: "input",
            overHidden: true,
            search: true,
            hide: true,
          },
          {
            label: "付款费用",
            prop: "costItemId",
            overHidden: true,
            search: true,
            display: false,
            hide: true,
            showColumn: false,
            placeholder: " ",
          },
          {
            label: "订单金额",
            prop: "orderAmount",
            type: "number",
            display: false,
            precision: 2,
            hide: true,
            showColumn: false,
            disabled: true,
          },
          {
            label: "待付款金额",
            labelTip: "含税",
            prop: "unPayAmount",
            type: "number",
            precision: 2,
            placeholder: " ",
            overHidden: true,
            display: false,
            disabled: true,
            hide: true,
            showColumn: false,
          },
          {
            label: "申请金额",
            prop: "applyAmount",
            type: "number",
            precision: 2,
            hide: true,
            showColumn: false,
            disabled: true,
          },
          {
            label: "已付款金额",
            prop: "payAmount",
            type: "number",
            precision: 2,
            hide: true,
            showColumn: false,
            disabled: true,
          },
          {
            label: "付款金额",
            labelTip: "含税",
            prop: "amount",
            type: "number",
            precision: 2,
            placeholder: " ",
            minWidth: 90,
            rules: [
              {
                required: true,
                message: "请输入付款金额",
                trigger: "blur",
              },
            ],
          },
          {
            label: "金额大写",
            prop: "upperAmount",
            readonly: true,
            placeholder: " ",
            hide: true,
            showColumn: false,
          },
          {
            label: "币种",
            prop: "currency",
            type: "select",
            search: true,
            dicUrl: "/api/blade-system/dict/dictionary?code=currency",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            value: "RMB",
            placeholder: " ",
            width: 75,
            rules: [
              {
                required: true,
                message: "请选择币种",
                trigger: "blur",
              },
            ],
            control: (val) => {
              if (val === "RMB") {
                return {
                  excRate: {
                    display: false,
                    value: 1,
                  },
                };
              } else {
                return {
                  excRate: {
                    display: true,
                  },
                };
              }
            },
          },
          {
            label: "汇率",
            prop: "excRate",
            labelTip:
              "汇率=本位币/原币.如本位币为人民币，原币为美元: 汇率为:0.1439.",
            type: "number",
            placeholder: " ",
            hide: true,
            display: false,
            rules: [
              {
                required: true,
                message: "请输入汇率",
                trigger: "blur",
              },
            ],
          },
          {
            label: "付款账户",
            prop: "ledgerName",
            display: false,
            overHidden: true,
            minWidth: 100,
          },
          {
            label: "付款账户",
            prop: "ledgerId",
            placeholder: " ",
            type: "select",
            dicUrl: "/api/ni/fin/ledger/list?status=2&brand={{key}}",
            overHidden: true,
            props: {
              label: "dictLabel",
              value: "id",
              desc: "currencyName",
            },
            hide: true,
            showColumn: false,
            search: true,
            rules: [
              {
                required: true,
                message: "请选择品牌",
                trigger: "blur",
              },
            ],
          },
          {
            label: "付款方式",
            prop: "paymentType",
            type: "select",
            search: true,
            dicData: [],
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            width: 75,
            rules: [
              {
                required: true,
                message: "请选择付款方式",
                trigger: "blur",
              },
            ],
          },
          {
            label: "付款人员",
            prop: "financialUserId",
            type: "select",
            remote: true,
            dicUrl: `/api/blade-user/search/otj/user?name={{key}}`,
            props: {
              label: "realName",
              value: "id",
            },
            dicFormatter: (data) => {
              return data.data.records;
            },
            search: true,
            hide: true,
            showColumn: false,
            rules: [
              {
                required: true,
                message: "请选择付款人员",
                trigger: "blur",
              },
            ],
          },
          {
            label: "付款人员",
            prop: "financialUserName",
            type: "input",
            display: false,
            width: 75,
          },
          {
            label: "付款日期",
            prop: "date",
            type: "date",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            search: true,
            searchRange: true,
            width: 85,
            rules: [
              {
                required: true,
                message: "请输入付款日期",
                trigger: "blur",
              },
            ],
          },
          {
            label: "红字",
            prop: "red",
            placeholder: " ",
            type: "radio",
            overHidden: true,
            width: 58,
            dicData: [
              {
                label: "是",
                value: true,
              },
              {
                label: "否",
                value: false,
              },
            ],
            change: ({value}) => {
              const source = this.findObject(this.option.column, "source");
              const date = this.findObject(this.option.column, "date");
              const ledgerId = this.findObject(this.option.column, "ledgerId");
              const paymentType = this.findObject(
                this.option.column,
                "paymentType"
              );
              if (value) {
                this.form.source = null;
                source.disabled = true;
                date.label = "退款日期";
                ledgerId.label = "退款账户";
                paymentType.label = "退款方式";
              } else {
                source.disabled = false;
                date.label = "付款日期";
                ledgerId.label = "付款账户";
                paymentType.label = "付款方式";
              }
            },
            rules: [
              {
                required: true,
                message: "请选择是否红字",
                trigger: "blur",
              },
            ],
          },
          {
            label: "凭证",
            prop: "voucher",
            type: "radio",
            search: true,
            width: 45,
            dicData: [
              {
                label: "是",
                value: true,
              },
              {
                label: "否",
                value: false,
              },
            ],
          },
          {
            label: "备注",
            prop: "remark",
            type: "textarea",
            overHidden: true,
            span: 24,
          },
          {
            label: "同步",
            prop: "yonyouSync",
            minWidth: 220,
            addDisplay: false,
            editDisplay: false,
            hide: true,
          },
          {
            label: "付款明细",
            prop: "items",
            labelPosition: "top",
            hide: true,
            span: 24,
            showColumn: false,
          },
        ],
      },
      data: [],
      yonyouSyncSequenceDict: [],
      yonyouSyncSequenceDictKeyValue: {},
      yonyouSyncStateDict: [],
      yonyouSyncStateDictKeyValue: {},
      statusKv: {},
      subTypeKv: {},
      brandKv: {},
      paymentTypeKv: {},
      voucherForm: {},
      yonyou: {
        visible: false,
        option: {
          span: 24,
          size: "mini",
          emptyBtn: false,
          column: [
            {
              prop: "date",
              label: "选择日期",
              type: "daterange",
              format: "yyyy-MM-dd",
              valueFormat: "yyyy-MM-dd",
              rules: [
                {
                  required: true,
                  message: "请选择日期范围",
                  trigger: "blur",
                },
              ],
            },
          ],
        },
        form: {},
      },
      ledger: {
        visible: false,
        option: {
          span: 24,
          size: "mini",
          emptyBtn: false,
          column: [
            {
              label: "账套",
              prop: "brand",
              placeholder: " ",
              type: "radio",
              overHidden: true,
              dicUrl: '/api/blade-system/dict-biz/dictionary?code=ni_brand',
              cascader: ["ledgerId"],
              props: {
                label: "dictValue",
                value: "dictKey",
              },
              disabled: true,
              width: 70,
              rules: [
                {
                  required: true,
                  message: "请选择账套",
                  trigger: "blur",
                },
              ],
            },
            {
              label: "付款账户",
              prop: "ledgerId",
              placeholder: " ",
              type: "select",
              dicUrl: "/api/ni/fin/ledger/list?status=2&brand={{key}}",
              overHidden: true,
              props: {
                label: "dictLabel",
                value: "id",
                desc: "currencyName",
              },
              hide: true,
              showColumn: false,
              search: true,
              rules: [
                {
                  required: true,
                  message: "请选择品牌",
                  trigger: "blur",
                },
              ],
            },
          ],
        },
        form: {},
      },
      row: null,
      exportColumn: [
        {
          label: "状态",
          prop: "status",
          width: 75,
        },
        {
          label: "编号",
          prop: "serialNo",
          width: 100,
        },
        {
          label: "付款类型",
          prop: "subType",
          width: 80,
        },
        {
          label: "账套",
          prop: "brand",
          width: 70,
        },
        {
          label: "供应商",
          prop: "supplier",
          width: 100,
        },
        {
          label: "单据来源",
          prop: "sourceSerialNo",
          width: 100,
        },
        {
          label: "付款金额",
          prop: "amount",
          width: 90,
        },
        {
          label: "币种",
          prop: "currency",
          width: 75,
        },
        {
          label: "付款账户",
          prop: "ledgerName",
          width: 100,
        },
        {
          label: "付款方式",
          prop: "paymentType",
          width: 75,
        },
        {
          label: "付款人员",
          prop: "financialUserName",
          width: 75,
        },
        {
          label: "付款日期",
          prop: "date",
          width: 85,
        },
        {
          label: "红字",
          prop: "red",
          width: 58,
        },
        {
          label: "凭证",
          prop: "voucher",
          width: 45,
        },
        {
          label: "备注",
          prop: "remark",
          width: 150,
        },
      ],
      printTemplate: null,
    };
  },
  computed: {
    ...mapGetters(["permission", "userInfo"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.finPayable_add, false),
        viewBtn: this.vaildData(this.permission.finPayable_view, false),
        delBtn: this.vaildData(this.permission.finPayable_delete, false),
        editBtn: this.vaildData(this.permission.finPayable_edit, false),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  created() {
    this.dictInit();
    loadPrintTemplate("ni_fin_payable").then((res) => {
      this.printTemplate = JSON.parse(res.data.data.content);
    });
  },
  methods: {
    dictInit() {
      this.$http
        .get("/api/blade-system/dict-biz/dictionary?code=ni_por_order_status")
        .then((res) => {
          const status = this.findObject(this.option.column, "status");
          status.dicData = res.data.data;
          this.statusKv = res.data.data.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });
      this.$http
        .get(
          "/api/blade-system/dict-biz/dictionary?code=ni_fin_payable_apply_type"
        )
        .then((res) => {
          const subType = this.findObject(this.option.column, "subType");
          subType.dicData = res.data.data;
          this.subTypeKv = res.data.data.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });
      this.$http
        .get("/api/blade-system/dict-biz/dictionary?code=ni_brand")
        .then((res) => {
          const brand = this.findObject(this.option.column, "brand");
          brand.dicData = res.data.data;
          this.brandKv = res.data.data.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });
      this.$http
        .get("/api/blade-system/dict-biz/dictionary?code=ni_fin_payment_type")
        .then((res) => {
          const paymentType = this.findObject(
            this.option.column,
            "paymentType"
          );
          paymentType.dicData = res.data.data;
          this.paymentTypeKv = res.data.data.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });

      this.$http
        .get("/api/blade-system/dict/dictionary?code=yonyou_sync_sequence")
        .then((res) => {
          this.yonyouSyncSequenceDict = res.data.data;
          this.yonyouSyncSequenceDictKeyValue =
            this.yonyouSyncSequenceDict.reduce((acc, cur) => {
              acc[cur.dictKey] = cur.dictValue;
              return acc;
            }, {});
        });
      this.$http
        .get("/api/blade-system/dict/dictionary?code=yonyou_sync_state")
        .then((res) => {
          this.yonyouSyncStateDict = res.data.data;
          this.yonyouSyncStateDictKeyValue = this.yonyouSyncStateDict.reduce(
            (acc, cur) => {
              acc[cur.dictKey] = cur.dictValue;
              return acc;
            },
            {}
          );
        });
    },
    handleAmount(value) {
      if (value) {
        this.form.upperAmount = numToCapital(value);
        let val = value;
        const items = this.form.items;
        items.forEach((item, index) => {
          const i = {...item};
          const leftAmount = i.orderAmount - i.payAmount;
          if (val > 0) {
            if (val >= leftAmount) {
              i.amount = leftAmount;
              val -= i.amount - i.payAmount;
            } else {
              i.amount = val;
            }
            this.$set(this.form.items, index, i);
          }
        });
        console.log(this.form.items);
      }
    },
    handleSourceConfirm(selectionList) {
      if (selectionList && selectionList.length > 0) {
        getApplyDetail(selectionList[0].id).then((res) => {
          const {data} = res.data;
          this.form.subType = data.type;
          this.form.supplier = data.supplier;
          this.form.relatedId = data.supplierId;
          this.form.porOrderSerialNo = data.porOrderSerialNo;
          this.form.orderId = data.porOrderId;
          this.form.costItemId = data.costItemId;
          this.form.costItemName = data.costItemName;
          this.form.type = data.type;
          this.form.currency = data.currency;
          this.form.remark = data.remark;
          this.form.applyAmount = data.amount;
          this.form.payAmount = data.payAmount;
          this.form.brand = data.brand;
          this.form.unPayAmount = this.form.applyAmount - this.form.payAmount;
          if (data.items) {
            this.form.items = data.items.map((item) => {
              return {
                ...item,
                id: null,
                applyItemId: item.id,
                orderAmount: item.amount,
                amount: 0,
              };
            });
          }
          if (data.costItems) {
            this.form.costItems = data.costItems.map((item) => {
              return {
                ...item,
                id: null,
                costItemId: item.id,
              };
            });
          }
        });
      }
    },
    rowItems(row) {
      this.$refs.payableItemRef.init(row.id);
    },
    rowYonyouSync(row) {
      let msg = "确定将选择数据同步?";
      if (row.yonyouSync && row.yonyouSync.length > 0) {
        msg = "数据已经同步过，是否继续?";
      }
      this.$confirm(msg, {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        sync(row.id).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
      });
    },
    rowPay(row) {
      this.$confirm("此操作将凭证修改为付款单，是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        center: true,
      })
        .then(() => {
          return trans2Pay(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    rowVoucher(row) {
      getDetail(row.id).then((res) => {
        const {data} = res.data;
        const form = {
          ...data,
          voucherItems: data.items.map((item) => ({...item, id: null})),
        };
        this.$refs.finVoucherFormRef.init(form);
      });
    },
    rowSource(row) {
      this.row = row;
      if (row.source) {
        this.$confirm("选择的数据已经存在关联的单据，是否替换?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
          center: true,
        }).then(() => {
          this.$refs.payableSelectRef.onShow({relatedId: row.relatedId});
        });
      } else this.$refs.payableSelectRef.onShow({relatedId: row.relatedId});
    },
    handleSourceChangeConfirm(selectionList) {
      if (selectionList) {
        changeSource(this.row.id, selectionList[0].id).then(() => {
          this.data.forEach((item) => {
            if (item.id === this.row.id) {
              item.source = selectionList[0].id;
              item.sourceSerialNo = selectionList[0].serialNo;
            }
          });
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
      }
    },
    rowRed(row) {
      const msg = "是否将选中的数据<span>退款(红字)</span>?";
      this.$confirm(msg, "提示", {
        dangerouslyUseHTMLString: true,
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        center: true,
      }).then(() => {
        const form = {
          source: row.id,
          type: row.type,
          subType: row.subType,
          relatedId: row.relatedId,
          currency: row.currency,
          amount: -(
            Number(row.amount) + Number(row.redAmount ? row.redAmount : 0)
          ),
          brand: row.brand,
          financialUserId: this.userInfo.user_id,
          date: dateFormat(new Date(), "yyyy-MM-dd"),
          voucher: true,
          red: true,
          items: [],
        };
        this.$refs.payableFormDialogRef.onAdd(form);
      });
    },
    rowVoid(row) {
      this.$confirm("此操作将作废提交的数据，是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        center: true,
      })
        .then(() => {
          return toVoid(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    rowEdit(row) {
      this.$refs.payableFormDialogRef.onEdit(row.id);
    },
    rowSubmit(row) {
      this.$confirm("此操作将提交该数据，是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        center: true,
      })
        .then(() => {
          return submit(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    handleSubmit(type) {
      this.form.status = 2;
      if (type === "add") {
        this.$refs.crud.rowSave();
      } else if (type === "edit") {
        this.$refs.crud.rowUpdate();
      }
    },
    rowPayable() {
      //TODO 查看应付详情
    },
    beforeContractSelect(done) {
      if (!this.form.brand) {
        this.$message({
          type: "warning",
          message: "请选择账套!",
        });
        return;
      }
      if (!this.form.relatedId) {
        this.$message({
          type: "warning",
          message: "请选择供应商!",
        });
        return;
      }
      done();
    },
    handleOrderSubmit(selectList) {
      this.form.orderId = selectList.map((item) => item.id).join(",");
      this.form.currency = selectList[0].currency
        ? selectList[0].currency
        : "RMB";
      this.form.items = selectList.map((item) => {
        return {
          ...item,
          id: null,
          porOrderId: item.id,
          orderAmount: item.amount,
          amount: 0,
        };
      });
      this.form.payAmount = selectList.reduce((acc, cur) => {
        return acc + cur.payApplyAmount;
      }, 0);
      this.form.orderAmount = selectList.reduce((acc, cur) => {
        return acc + cur.amount;
      }, 0);
      this.form.unPayAmount = this.form.orderAmount - this.form.payAmount;
      console.log(this.form.orderAmount);
    },
    handleSupplierClear() {
      this.form.relatedId = null;
    },
    handleCostItemSubmit(selectList) {
      this.form.currency = selectList[0].currency
        ? selectList[0].currency
        : "RMB";
      const items = selectList;
      this.form.costItems = items.map((item) => {
        return {
          ...item,
          id: null,
          costItemId: item.id,
        };
      });
      this.form.unPayAmount = selectList.reduce((acc, cur) => {
        return acc + parseFloat(cur.amount) - parseFloat(cur.payApplyAmount);
      }, 0);
      console.log(this.form.costItems);
    },
    handleSupplierSubmit(selectList) {
      this.form.relatedId = selectList[0].id;
    },
    rowSave(row, done, loading) {
      if (!row.excRate) {
        row.execRate = 1;
      }
      add(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          window.console.log(error);
        }
      );
    },
    rowUpdate(row, index, done, loading) {
      if (!row.excRate) {
        row.execRate = 1;
      }
      update(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          console.log(error);
        }
      );
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    async handleExport() {
      let msg =
        '是否导出<span style="color: #F56C6C;font-weight: bold">所有数据</span>?';
      if (this.selectionList.length > 0) {
        msg =
          '是否要导出<span style="color: #F56C6C;font-weight: bold">当前选中的数据</span>？';
      }
      let data = [];
      this.$confirm(msg, {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        dangerouslyUseHTMLString: true,
        type: "warning",
      }).then(async () => {
        if (this.selectionList.length > 0) {
          data = this.selectionList;
        } else {
          const q = {...this.query};
          if (q.date && q.date.length === 2) {
            q.startDate = q.date[0];
            q.endDate = q.date[1];
            q.date = null;
          }
          const res = await getList(q);
          data = res.data.data;
        }
        this.$Export.excel({
          title: "付款单",
          columns: this.exportColumn,
          data: data.map((item) => {
            return {
              ...item,
              status: this.statusKv[item.status],
              subType: this.subTypeKv[item.subType],
              brand: this.brandKv[item.brand],
              paymentType: this.paymentTypeKv[item.paymentType],
            };
          }),
        });
      });
    },
    handleLedger() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择要修改的数据");
        return;
      } else if (this.selectionList.length > 1) {
        this.$message.warning("只能选择一条数据");
        return;
      }
      this.ledger.form = {
        id: this.selectionList[0].id,
        brand: this.selectionList[0].brand,
        ledgerId: this.selectionList[0].ledgerId,
      }
      this.ledger.visible = true
    },
    handleLedgerSubmit(form, done) {
      changeLedger(form.id, form.ledgerId).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.ledger.visible = false;
        }
      ).finally(() => {
        done();
      })
    },
    async handlePrint() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择要打印的数据");
        return;
      } else if (this.selectionList.length > 1) {
        this.$message.warning("只能选择一条数据");
        return;
      }
      if (!this.printTemplate) {
        this.$message.error("打印模板加载失败，请联系管理员");
        return;
      }
      const res = await getDetail(this.selectionList[0].id);
      let printData = res.data.data;
      printData.title = "付款单";
      if (printData.brand === "2") {
        printData.title = "至简" + printData.title;
      } else if (printData.brand === "4") {
        printData.title = "演绎付款单" + printData.title;
      }
      if (printData.red) {
        printData.title += "(红字)";
      }
      printData.paymentType = this.paymentTypeKv[printData.paymentType];
      printData.upperAmount = numToCapital(printData.amount);
      let hiprintTemplate = new hiprint.PrintTemplate({
        template: this.printTemplate,
      });
      hiprintTemplate.print(printData);
    },
    handleYonyouSync() {
      this.yonyou.visible = true;
    },
    handleYonyouSyncSubmit(form, done) {
      this.$refs.yonyouSyncRef.validate((valid) => {
        if (valid) {
          syncRange(form.date[0], form.date[1], 0).then(() => {
            this.yonyou.visible = false;
            this.yonyou.form = {};
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!",
            });
            done();
          });
        }
      });
    },
    handleAdd(command) {
      const form = {};
      if (command === "pay") {
        form.type = "1";
        form.brand = "1";
        form.voucher = false;
        form.red = false;
      } else if (command === "porRed") {
        form.type = "1";
        form.brand = "1";
        form.subType = "2";
        form.voucher = false;
        form.red = true;
      } else if (command === "otherRed") {
        form.type = "1";
        form.brand = "1";
        form.voucher = false;
        form.red = true;
      }
      this.$refs.payableFormDialogRef.onAdd(form);
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen(done, type) {
      if ("add" === type) {
        this.form.type = 1;
        this.form.subType = "2";
        this.form.financialUserId = this.userInfo.user_id;
        this.form.date = dateNow1();
      } else if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then((res) => {
          this.form = res.data.data;
          this.form.upperAmount = numToCapital(this.form.amount);
        });
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      const q = Object.assign(params, this.query);
      if (q.date && q.date.length === 2) {
        q.startDate = q.date[0];
        q.endDate = q.date[1];
        q.date = null;
      }
      q.descs = "id";
      getPage(page.currentPage, page.pageSize, q).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
  },
};
</script>

<style></style>

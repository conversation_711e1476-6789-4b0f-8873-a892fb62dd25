import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/ni/base/depot/info/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/ni/base/depot/info/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/ni/base/depot/info/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/ni/base/depot/info/save',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/ni/base/depot/info/update',
    method: 'post',
    data: row
  })
}

export const submit = (ids) => {
  return request({
    url: '/api/ni/base/depot/info/submit',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const back = (ids) => {
  return request({
    url: '/api/ni/base/depot/info/back',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const toVoid = (ids) => {
  return request({
    url: '/api/ni/base/depot/info/toVoid',
    method: 'post',
    params: {
      ids,
    }
  })
}
export const getDepotLocationList = (current, size, params) => {
  return request({
    url: '/api/ni/base/depot/info/depotLocation/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

<template>
  <basic-container>

    <FullInfoForm :fullInfo="fullInfo" :autoComplete="true" @info-update="handleInfoUpload" ref="form">
    </FullInfoForm>
    <el-card shadow="never" style="margin-top: 20px"  >
      <wf-examine-form
        ref="examineForm"
        :process="process"
        @user-select="handleUserSelect"
      ></wf-examine-form>
    </el-card>
    <wf-user-select
      ref="user-select"
      :check-type="checkType"
      :default-checked="defaultChecked"
      @onConfirm="handleUserSelectConfirm"
    ></wf-user-select>
<!--    <el-row type="flex" justify="center" align="middle" >-->
<!--      <el-col :span="12">-->
<!--        <el-button type="primary" @click="handleSubmit">发起</el-button>-->
<!--        <el-button type="primary" @click="edit()">保存到员工档案</el-button>-->
<!--        <el-button >取 消</el-button>-->
<!--      </el-col>-->
<!--    </el-row>-->

    <div style="height: 120px"></div>
    <el-row
      class="foot-item avue-affix el-row"
      :style="{
          width: 'calc(100% - 260px)',
        }"
      id="avue-view"
    >
<!--        v-no-more-click-->
      <el-button
        type="primary"
        size="mini"
        v-loading="loading"
        @click="handleSubmit"
      >发起
      </el-button>
<!--      <el-button-->
<!--        v-if="permission.wf_process_draft"-->
<!--        type="success"-->
<!--        size="mini"-->
<!--        v-loading="loading"-->
<!--        @click="handleDraftNotClose(process.id, process.formKey, form)"-->
<!--      >存为草稿-->
<!--      </el-button>-->
<!--      <el-button-->
<!--        v-if="permission.wf_process_draft"-->
<!--        type="success"-->
<!--        size="mini"-->
<!--        v-loading="loading"-->
<!--        @click="handleDraft(process.id, process.formKey, form)"-->
<!--      >存为草稿并关闭-->
<!--      </el-button>-->
    </el-row>
  </basic-container>
</template>

<script>
import WfExamineForm from '@/views/plugin/workflow/process/components/examForm.vue'
import WfUserSelect from '@/views/plugin/workflow/process/components/user-select'

import exForm from '@/views/plugin/workflow/mixins/ex-form'
import draft from '@/views/plugin/workflow/mixins/draft'
import {mapGetters} from "vuex";
import {getAuthorizationHeader} from "@/api/resource/fileManagement";
import {add, fullInfo} from "@/api/ni/emhire/niHrEmployeeBaseInfo";
import FullInfoForm from "@/views/ni/pa/components/employeeFullInfo.vue";
import {startProcess} from "@/api/plugin/workflow/process";

export default {
  components: {
    FullInfoForm,
    WfUserSelect, WfExamineForm
  },
  mixins: [exForm, draft],
  data() {
    return {
      defaults: {},
      form: {},
      carNumVisible:false,
      tagInputVal:'',
      carNumTags:[],
      visible:false,
      loading: false,
      politicalSelect:[],
      eduSelect:[],
      regionSelect:[],
      contractStatusSelect:[
        {label:"未签约",value:1},
        {label:"未与上家解除",value:0},
        {label:"已签约",value:2},
        {label:"已解除",value:3},
      ],
      yesOrNoSelect: [
        {label:'否',value:0},
        {label: '是', value:1},
      ],
      eduTypeSelect: [
        {label:'第一学历',value:3},
        {label:'最高学历',value:2},
        {label:'在读',value:0},
        {label:'毕业',value:1}],
      deptSelect:[],
      postSelect:[],

      postTree:[],
      deptTree:[],
      deptMap:{},
      postMap:{},

      fullInfo:{
        baseInfo:{
          avatar:'',
          avatarFileId:'',
          name:'',
          ethnicity:'',
          idCard:'',
          phone:'',
          address:'',
          enterDate:'',
          fillTime:'',
          otherPhone:'',
          deptId:'',
          positionId:'',
          resumeId:'',
          carNum:'',
          contractStatus:'',
          code:''
        },
        extInfo:{
          bankCard:'',
          bankCardFileId:'',
          householdRegister:'',
          householdRegisterCode:'',
          marriage:'',
          politic:'',
          religion:'',
          currentAddress:'',
          currentAddressCode:'',
          professionalTitle:'',
          driverLicense:'',
          drivingExperienceYears:'',
          hobbies:'',
          englishProficiency:'',
          hasIntellectualProperty:'',
          hasCompetitionRestriction:'',
          researchField:'',
          familyAddress:'',
          familyAddressCode:'',
          hostel:0,
          intern:0,
          idCardBackPicture:'',
          idCardBackPictureFileId:'',
          idCardPicture:'',
          idCardPictureFileId:'',
        },
        familyMembers:[],
        educations:[],
        workExperiences:[],
        files:[],
        dormitory:{
          region:'',
          code:'',
          roomCode:'',
          bedCode:''
        }
      },
      rules: {
        'fullInfo.baseInfo.name': [{ required: true, message: "请输入用户名" }],
        'fullInfo.baseInfo.phone': [{ required: true, message: "请输入手机号" }]
      }
    };
  },
  activated() {
    let val=this.$route.query.p
    if (val) {
      let text = Buffer.from(val, "base64").toString();
      text = text.replace(/[\r|\n|\t]/g, "");
      const param = JSON.parse(text);
      const { processId, processDefKey, form } = param;
      console.info(param)
      if (form) {
        const f = JSON.parse(
          new Buffer(decodeURIComponent(form), "utf8").toString("utf8")
        );
        this.form = Object.assign(this.form, f);
        console.info(this.form)
      }
      if (processId) {
        this.getForm(processId);
      } else if (processDefKey) {
        this.getFormByProcessDefKey(processDefKey);
      }
    }
  },
  computed: {
    ...mapGetters(["permission","userInfo"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.niHrEmployeeBaseInfo_add, false),
        viewBtn: this.vaildData(this.permission.niHrEmployeeBaseInfo_view, false),
        delBtn: this.vaildData(this.permission.niHrEmployeeBaseInfo_delete, false),
        editBtn: this.vaildData(this.permission.niHrEmployeeBaseInfo_edit, false)
      };
    },
  },
  created() {
    console.info("start")
    console.info(this.fullInfo)
  },
  methods: {
    getAuthorizationHeader,
    edit(){
      this.fullInfo.baseInfo.carNum = this.carNumTags.join()
      add(this.fullInfo).then(() => {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
      }, error => {
        window.console.log(error);
      });
    },
    handleInfoUpload(newVal){
      this.fullInfo = newVal
    },
    handleSubmit() {
      this.loading = true;
      console.info('submit before', this.fullInfo);
      let form = this.deepClone(this.form);
      form = {...form,...this.fullInfo}
      form.baseInfo.carNum = this.$refs.form.getCarNum()
      if (this.$refs.examineForm && this.$refs.examineForm.examineForm) {
        const { copyUser, assignee } = this.$refs.examineForm.examineForm;
        form = { ...form, copyUser, assignee };
      }
      console.info('submit',form,this.fullInfo)
      startProcess(form).then(() => {
        this.$message.success("发起成功");
        if (this.fromPath) {
          this.handleCloseTag(this.fromPath);
        } else this.handleCloseTag("/plugin/workflow/process/start");
      }).catch(() => {
        this.loading = false;
      });
    },
    getForm(processId) {
      this.getStartForm(processId).then((res) => {
        const _this = this;
        let { process } = res;
        console.info(process)
        this.form.processId = process.id;
        _this.waiting = false;
      });
    },
    getFormByProcessDefKey(processDefKey) {
      this.getStartFormByProcessDefKey(processDefKey).then((res) => {
        const _this = this;
        let { process } = res;
        console.info(process);
        _this.form.processId = process.id;
        _this.waiting = false;
      });
    },
  }
}
</script>

<style lang="scss" scoped>
.foot-item {
  position: fixed;
  bottom: 0;
  margin-left: -20px;
  // right: 0;
  z-index: 101;
  height: 66px;
  background-color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  -webkit-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
</style>

import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/feedback/feedback/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/feedback/feedback/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/feedback/feedback/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/feedback/feedback/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/feedback/feedback/submit',
    method: 'post',
    data: row
  })
}



export const asyncQdrant = (ids) => {
  return request({
    url: '/api/feedback/feedback/asyncQdrant',
    method: 'post',
    params: {
      ids,
    }
  })
}


<script>
import InventorySelectDialog from "@/views/ni/fg/components/InventorySelectDialog.vue";
import {mapGetters} from "vuex";
import {dateFormat} from "@/util/date";
import {add, getDetail, itemsByOldFaHuoBianHao} from "@/api/ni/fg/fgOutbound";
import GuoWaiFaHuoSelect from "@/views/ni/old/components/GuoWaiFaHuoSelect.vue";
import XiaoShouDingDanSelect from "@/views/ni/old/components/XiaoShouDingDanSelect.vue";
import XiaoShouWaiKuBuHuoSelect from "@/views/ni/old/components/XiaoShouWaiKuBuHuoSelect.vue";

export default {
  name: "OutboundFormDialog",
  components: {XiaoShouWaiKuBuHuoSelect, XiaoShouDingDanSelect, GuoWaiFaHuoSelect, InventorySelectDialog},
  data() {
    return {
      visible: false,
      option: {
        emptyBtn: false,
        size: "mini",
        span: 8,
        labelWidth: 110,
        column: [
          {
            label: "出库编号",
            prop: "serialNo",
            placeholder: "系统自动生成",
            overHeight: true,
          },
          {
            label: "出库主题",
            prop: "title",
            overHeight: true,
            rules: [
              {
                required: true,
                message: "请输入出库主题",
                trigger: "blur",
              },
            ],
          },
          {
            label: "出库类型",
            prop: "type",
            type: "select",
            dicUrl: "/api/blade-system/dict-biz/dictionary?code=ni_fg_outbound_type",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            rules: [
              {
                required: true,
                message: "请选择出库类型",
                trigger: "blur",
              },
            ],
            control: (val) => {
              if (val === '0207') {
                return {
                  backReason: {
                    display: true
                  }
                }
              } else {
                return {
                  backReason: {
                    display: false
                  }
                }
              }
            }
          },
          {
            label: "仓库",
            prop: "depotId",
            type: "select",
            dicUrl: "/api/ni/base/depot/info/list?status=2&type=1",
            props: {
              label: "name",
              value: "id",
              desc: "code",
            },
            rules: [
              {
                required: true,
                message: "请选择仓库",
                trigger: "blur",
              },
            ],
          },
          {
            label: "操作人",
            prop: "opUserId",
            type: "select",
            remote: true,
            dicUrl: `/api/blade-user/search/otj/user?name={{key}}`,
            props: {
              label: "realName",
              value: "id",
            },
            dicFormatter: (data) => {
              return data.data.records;
            },
            placeholder: " ",
            filterable: true,
            overHidden: true,
            rules: [
              {
                required: true,
                message: "请选择操作人",
                trigger: "blur",
              },
            ],
          },
          {
            label: "出库时间",
            prop: "opDate",
            search: true,
            type: "date",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            searchRange: true,
            placeholder: " ",
            clearable: false,
            minWidth: 120,
            rules: [
              {
                required: true,
                message: "请选择操作时间",
                trigger: "blur",
              },
            ],
          },
          {
            label: "关联单据",
            prop: "relatedOrderId",
            type: "input",
          },
          {
            label: "退生产原因",
            prop: "backReason",
            type: "select",
            dicUrl: "/api/blade-system/dict-biz/dictionary?code=ni_fg_outbound_back_reason",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            display: false,
            placeholder: " ",
            rules: [
              {
                required: true,
                message: "请选择退生产原因",
                trigger: "blur",
              },
            ],
          },
          {
            label: "备注",
            prop: "remark",
            type: "textarea",
            span: 24,
            minRows: 2,
            search: true,
          },
          {
            label: "出库明细",
            prop: "items",
            span: 24,
            type: "dynamic",
            hide: true,
            showColumn: false,
            children: {
              rowAdd: () => {
                this.handleInventorySelect()
              },
              size: "mini",
              align: "center",
              headerAlign: "center",
              showSummary: true,
              sumColumnList: [
                {
                  name: 'num',
                  type: 'sum',
                  decimals: 1
                },
              ],
              column: [
                {
                  label: '产品名称',
                  prop: "skuText",
                  placeholder: " ",
                  overHidden: true,
                  cell: false,
                  minWidth: 120,
                },
                {
                  label: '规格',
                  prop: 'specText',
                  placeholder: " ",
                  display: false,
                  overHidden: true,
                  cell: false,
                  width: 120
                },
                {
                  label: '外包装',
                  prop: 'packageText',
                  placeholder: " ",
                  display: false,
                  overHidden: true,
                  cell: false,
                  minWidth: 115
                },
                {
                  label: '内包装',
                  prop: 'innerPackageText',
                  placeholder: " ",
                  display: false,
                  overHidden: true,
                  cell: false,
                  width: 115
                },
                {
                  label: '批号',
                  prop: 'batchNo',
                  placeholder: " ",
                  minWidth: 110,
                  overHidden: true,
                  cell: false,
                },
                {
                  label: '库存箱数',
                  prop: 'currentStock',
                  placeholder: " ",
                  width: 110,
                  overHidden: true,
                  cell: false,
                },
                {
                  label: '数量',
                  prop: 'num',
                  placeholder: " ",
                  type: 'number',
                  minWidth: 80,
                  rules: [
                    {
                      required: true,
                      message: "请输入数量",
                      trigger: "blur",
                    },
                  ],
                },
                {
                  label: "重量",
                  prop: "weight",
                  placeholder: " ",
                  minWidth: 80,
                  type: 'number',
                },
                {
                  label: "单位",
                  prop: "unit",
                  type: "select",
                  filterable: true,
                  width: 80,
                  cell: false,
                  dicUrl: "/api/blade-system/dict-biz/dictionary?code=ni_material_unit",
                  props: {
                    label: "dictValue",
                    value: "dictKey",
                  },
                  rules: [{
                    required: true,
                    message: "请选择单位",
                    trigger: "blur"
                  }],
                  slot: true,
                  placeholder: " ",
                },
                {
                  label: '存货编码',
                  prop: 'materialCode',
                  placeholder: " ",
                  width: 110,
                  cell: false,
                  display: false,
                },
                {
                  label: "备注",
                  prop: "remark",
                  type: "textarea",
                  placeholder: " ",
                  minRows: 1,
                  overHidden: true,
                  minWidth: 120,
                },
              ]
            }
          },
        ]
      },
      form: {}
    }
  },
  computed: {
    ...mapGetters(['userInfo']),
    items() {
      return this.form.items || [];
    },
  },
  methods: {
    onEdit(id) {
      Object.keys(this.form).forEach(key => this.form[key] = null);
      this.form.items = []
      this.option.detail = false
      getDetail(id).then((res) => {
        this.form = res.data.data
        this.visible = true
      })
    },
    onShow(id) {
      Object.keys(this.form).forEach(key => this.form[key] = null);
      this.form.items = []
      this.option.detail = true
      getDetail(id).then((res) => {
        this.form = res.data.data
        this.visible = true
      })
    },
    /**
     *
     * @param depotId 仓库id
     * @param inventories 库存明细
     * @param form 表单数据
     */
    onAdd(depotId, inventories, form) {
      this.option.detail = false
      Object.keys(this.form).forEach(key => this.form[key] = null);
      if (inventories) {
        this.form.items = []
        this.handleInventorySelectConfirm(inventories)
      }
      this.form.depotId = depotId
      this.form.opUserId = this.userInfo.user_id
      this.form.opDate = dateFormat(new Date(), 'yyyy-MM-dd')
      if (form)
        this.form = {
          ...this.form,
          ...form
        }
      this.visible = true
    },
    rowNumChange(value, row) {
      row.weight = Number(value) * Number(row.capacity)
    },
    handleInventorySelect() {
      if (!this.form.depotId) {
        this.$message({
          type: "warning",
          message: "请选择仓库!"
        })
        return
      }
      this.$refs.inventorySelectDialog.onShow()
    },
    handleInventorySelectConfirm(selectionList) {
      // const freeze = selectionList.some((item) => item.status !== 1)
      // if (freeze&&this.form) {
      //   this.$message({
      //     type: "warning",
      //     message: "请选择非冻结的数据进行出库!"
      //   })
      //   return
      // }
      selectionList.forEach((item) => {
        this.items.push({
          skuId: item.skuId,
          skuText: item.skuText,
          specText: item.specText,
          packageText: item.packageText,
          innerPackageText: item.innerPackageText,
          materialId: item.materialId,
          materialCode: item.materialCode,
          currentStock: item.num,
          capacity: item.capacity,
          unit: item.unit,
          batchNo: item.batchNo,
          productionDate: item.productionDate
        })
      })
    },
    handleConfirm(form, done) {
      add(form).then(() => {
        this.visible = false
        this.$message({
          type: "success",
          message: "操作成功!"
        });
        this.$emit('confirm', form)
      }).finally(() => {
        done(0)
      });
    },
    handleXiaoShouDingDanSelectConfirm(selectionList) {
      this.form.relatedOrderText = selectionList[0].fdbh
      this.$confirm("是否导入货物流转明细数据?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          itemsByOldFaHuoBianHao(selectionList[0].fdbh).then((res) => {
            this.form.items = res.data.data
          })
        })
    },
    handleXiaoShouWaiKuBuHuoSelectConfirm(selectionList) {
      this.form.relatedOrderText = selectionList[0].faHuoBianHao
      this.$confirm("是否导入货物流转明细数据?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          itemsByOldFaHuoBianHao(selectionList[0].faHuoBianHao).then((res) => {
            this.form.items = res.data.data
          })
        })
    },
  }
}
</script>

<template>
  <el-dialog
    :visible.sync="visible"
    fullscreen
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    append-to-body
  >
    <avue-form
      v-if="visible"
      :option="option"
      v-model="form"
      @submit="handleConfirm"
    >
      <template #relatedOrderId="{size,disabled}">
        <xiao-shou-ding-dan-select
          v-if="form.type==='0201'"
          v-model="form.relatedOrderId"
          :size="size"
          :disabled="disabled"
          :params="{
            zt: '已发货,已到货'
          }"
          @confirm="handleXiaoShouDingDanSelectConfirm"
        />
        <guo-wai-fa-huo-select
          v-else-if="form.type==='0205'"
          v-model="form.relatedOrderId"
          :size="size"
          :disabled="disabled"
          @confirm="(selectionList)=>form.relatedOrderText = selectionList[0].faHuoBianHao"
        />
        <xiao-shou-wai-ku-bu-huo-select
          v-else-if="form.type==='0206'"
          v-model="form.relatedOrderId"
          :size="size"
          :disabled="disabled"
          :params="{
            zhuangTai: '已发货,已到货'
          }"
          @confirm="handleXiaoShouWaiKuBuHuoSelectConfirm"
        />
        <el-input v-else v-model="form.relatedOrderText" :size="size" :disabled="disabled"/>
      </template>
      <template #num="{row,size,disabled}">
        <el-input-number
          v-model="row.num"
          :min="1"
          :max="!option.detail?row.currentStock:-1"
          :size="size"
          :step="1"
          :controls="false"
          :disabled="disabled"
          @change="rowNumChange($event,row)"
        />
      </template>
    </avue-form>
    <inventory-select-dialog ref="inventorySelectDialog" :params="{depotId:form.depotId,status:1}"
                             multiple
                             @confirm="handleInventorySelectConfirm"/>
  </el-dialog>
</template>

<style scoped>

</style>

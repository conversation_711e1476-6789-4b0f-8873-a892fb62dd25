export default {
  height:'auto',
  calcHeight: 30,
  tip: false,
  searchEnter: true,
  searchIcon: true, // 搜索栏能否收缩
  searchIndex: 2, // 搜索按钮索引,超出的搜索栏收缩
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  addBtn: false,
  viewBtn: false,
  editBtn:false,
  delBtn:false,
  selection: true,
  dialogClickModal: false,
  column: [
    {
      label: "头像",
      prop: "avatar",
      type: "upload",
      listType: 'picture'
    },
    {
      label: "员工编号",
      prop: "code",
      search:true,
      type: "input",
    },
    {
      label: "员工名称",
      prop: "name",
      search: true,
      type: "input",
    },
    {
      label: "民族",
      prop: "ethnicity",
      type: "input",
    },
    {
      label: "身份证号",
      prop: "idCard",
      search: true,
      type: "input",
    },
    {
      label: "联系电话",
      prop: "phone",
      search: true,
      type: "input",
    },
    {
      label: "通讯地址",
      prop: "address",
      type: "input",
    },
    {
      label: "入职时间",
      prop: "enterDate",
      search: true,
      searchRange: true,
      type: "date",
      format: "yyyy-MM-dd",
      valueFormat: "yyyy-MM-dd",
    },
    {
      label: "填表时间",
      prop: "fillTime",
      type: "date",
      format: "yyyy-MM-dd",
      valueFormat: "yyyy-MM-dd",
    },
    {
      label: "其他联系电话",
      prop: "otherPhone",
      type: "input",
    },
    {
      label: "部门",
      prop: "deptId",
      type: "input",
      slot:true
    },
    {
      label: "岗位",
      prop: "positionId",
      type: "input",
      slot:true
    },
    {
      label: "车牌号",
      prop: "carNum",
      search: true,
      type: "input",
    },
    {
      label: "状态",
      prop: "status",
      type: "input"
    },
    {
      label: "合同状态",
      prop: "contractStatus",
      type: "input"
    },
    {
      label: "排序号",
      prop: "customSort",
      hide: true,
      type: "input",
    }
  ]
}

<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page.sync="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @refresh-change="refreshChange"
               @search-change="searchChange"
               @search-reset="searchReset"
               @on-load="onLoad">
      <template slot="menuLeft">
        <el-button type="danger"
                   size="small"
                   icon="el-icon-delete"
                   plain
                   v-if="permission.ofcTaskReminder_delete"
                   @click="handleDelete">删 除
        </el-button>
      </template>

      <!-- 是否完成字段的自定义显示 -->
      <template slot="isComplated" slot-scope="{row}">
        <el-tag
          :type="row.isComplated == 1 ? 'success' : (row.isComplated == 3 ? 'danger' : 'warning')"
          effect="plain"
          size="mini"
        >
          {{ row.isComplated == 1 ? '已完成' : (row.isComplated == 3 ? '已逾期' : '未完成') }}
        </el-tag>
      </template>

      <!-- 自定义操作按钮 -->
      <template slot="menu" slot-scope="scope">
        <el-button v-if="(scope.row.isComplated == 0 || scope.row.isComplated == 3) && scope.row.createUser == userInfo.user_id"
                   type="text"
                   size="small"
                   icon="el-icon-check"
                   @click="handleComplete(scope.row)">确认完成</el-button>
      </template>

      <!-- 待办人搜索插槽 -->
      <template slot="userNameSearch" slot-scope="scope">
        <user-select
          v-model="scope.value"
          size="small"
          @confirm="handleUserSearchConfirm(scope)"
        ></user-select>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
  import {getList, getDetail, add, update, remove} from "@/api/ni/ofc/ofcTaskReminder";
  import {getUser} from "@/api/system/user";
  import option from "@/const/ni/ofc/ofcTaskReminder";
  import {mapGetters} from "vuex";
  import UserSelect from "@/components/user-select";

  export default {
    components: {
      UserSelect
    },
    data() {
      return {
        query: {},
        form: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        option: option,
        data: []
      };
    },
    computed: {
      ...mapGetters(["permission", "userInfo"]),
      permissionList() {
        return {
          addBtn: this.vaildData(this.permission.ofcTaskReminder_add, false),
          viewBtn: this.vaildData(this.permission.ofcTaskReminder_view, false),
          delBtn: this.vaildData(this.permission.ofcTaskReminder_delete, false),
          editBtn: this.vaildData(this.permission.ofcTaskReminder_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    methods: {
      rowSave(row, done, loading) {
        // 如果有用户ID但没有用户名或部门，先获取用户信息
        if (row.createUser && (!row.userName || !row.department)) {
          getUser(row.createUser).then(res => {
            const userData = res.data.data;
            if (userData) {
              row.userName = userData.realName;
              row.department = userData.deptName;
            }
            // 保存
            this.saveData(row, done, loading);
          }).catch(() => {
            // 即使获取用户信息失败也继续保存
            this.saveData(row, done, loading);
          });
        } else {
          // 如果已有用户名和部门或没有用户ID，直接保存
          this.saveData(row, done, loading);
        }
      },

      saveData(row, done, loading) {
        // 确保字段名称与后端匹配
        if (row.userName) {
          row.user_name = row.userName;
        }

        // 确保 department 是字符串类型
        if (row.department) {
          if (Array.isArray(row.department)) {
            row.department = row.department.join(',');
          } else {
            row.department = String(row.department);
          }
        }

        // 确保 createDept 是字符串类型
        if (row.createDept) {
          if (Array.isArray(row.createDept)) {
            row.createDept = row.createDept[0];
          }
        }

        // 确保 isComplated 字段有默认值
        if (row.isComplated === undefined || row.isComplated === null) {
          row.isComplated = 0;
        }

        add(row).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          loading();
          window.console.log(error);
        });
      },
      rowUpdate(row, _index, done, loading) {
        // 如果有用户ID但没有用户名或部门，先获取用户信息
        if (row.createUser && (!row.userName || !row.department)) {
          getUser(row.createUser).then(res => {
            const userData = res.data.data;
            if (userData) {
              row.userName = userData.realName;
              row.department = userData.deptName;
            }
            // 继续更新
            this.updateData(row, done, loading);
          }).catch(() => {
            // 即使获取用户信息失败也继续更新
            this.updateData(row, done, loading);
          });
        } else {
          // 如果已有用户名和部门或没有用户ID，直接更新
          this.updateData(row, done, loading);
        }
      },

      updateData(row, done, loading) {
        // 确保字段名称与后端匹配
        if (row.userName) {
          row.user_name = row.userName;
        }

        // 确保 department 是字符串类型
        if (row.department) {
          if (Array.isArray(row.department)) {
            row.department = row.department.join(',');
          } else {
            row.department = String(row.department);
          }
        }

        // 确保 createDept 是字符串类型
        if (row.createDept) {
          if (Array.isArray(row.createDept)) {
            row.createDept = row.createDept[0];
          }
        }

        // 确保 isComplated 字段有默认值
        if (row.isComplated === undefined || row.isComplated === null) {
          row.isComplated = 0;
        }

        update(row).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          loading();
          console.log(error);
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });
        } else if (type === "add") {
          // 获取当前登录用户信息
          const userInfo = this.$store.getters.userInfo;
          // 设置用户ID、用户名和部门
          this.form.createUser = userInfo.user_id;
          this.form.userName = userInfo.nick_name || userInfo.name;
          this.form.user_name = userInfo.nick_name || userInfo.name;

          // 确保 department 是字符串类型
          if (userInfo.dept_name) {
            // 如果是数组，则转换为字符串
            if (Array.isArray(userInfo.dept_name)) {
              this.form.department = userInfo.dept_name.join(',');
            } else {
              this.form.department = String(userInfo.dept_name);
            }
          } else {
            this.form.department = '';
          }

          // 确保 createDept 是字符串类型
          if (userInfo.dept_id) {
            // 如果是数组，则使用第一个元素
            if (Array.isArray(userInfo.dept_id)) {
              this.form.createDept = userInfo.dept_id[0];
            } else {
              this.form.createDept = userInfo.dept_id;
            }
          } else {
            this.form.createDept = '';
          }

          // 设置是否完成字段的默认值为0
          this.form.isComplated = 0;
        }
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      refreshChange() {
        this.onLoad(this.page, this.query);
      },

      // 搜索重置
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },

      // 搜索变化
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1;
        this.onLoad(this.page, params);
        done();
      },

      // 处理用户搜索选择确认
      handleUserSearchConfirm(scope) {
        // 当用户选择后，将用户ID设置到搜索条件中
        if (scope.value) {
          // 获取用户详细信息
          getUser(scope.value).then(res => {
            const userData = res.data.data;
            if (userData) {
              // 将用户名设置到搜索框中显示
              scope.column.searchValue = userData.realName;
              // 将用户ID设置到搜索条件中
              this.query.createUser = scope.value;
              // 重新加载数据
              this.onLoad(this.page, this.query);
            }
          });
        }
      },


      // 处理确认完成按钮点击
      /**
       * 判断待办事项是否逾期
       * @param {Object} row 待办事项对象
       * @returns {boolean} 是否逾期
       */
      isOverdue(row) {
        // 如果已经完成，不算逾期
        if (row.isComplated === 1) {
          return false;
        }

        // 如果没有截止时间，不算逾期
        if (!row.endTime) {
          return false;
        }

        // 判断当前时间是否超过截止时间
        const endTime = new Date(row.endTime);
        const currentTime = new Date();

        return currentTime > endTime;
      },

      handleComplete(row) {
        // 确认对话框
        this.$confirm('确定将该事项标记为已完成吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          // 创建一个新对象，只包含需要更新的字段
          const updateData = {
            id: row.id,
            isComplated: 1
          };

          // 调用更新API
          update(updateData).then(() => {
            this.$message({
              type: 'success',
              message: '事项已标记为完成!'
            });
            // 重新加载数据
            this.onLoad(this.page);
          }).catch(error => {
            console.error('Error updating task:', error);
            this.$message({
              type: 'error',
              message: '操作失败，请重试!'
            });
          });
        }).catch(() => {
          // 用户取消操作
        });
      },

      onLoad(page, params = {}) {
        this.loading = true;
        getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.page.total = data.total;

          // 处理数据，确保用户名和部门名称正确显示
          const records = data.records;
          const userPromises = [];

          // 对于没有用户名或部门名称的记录，获取用户信息
          records.forEach(record => {
            // 检查是否逾期，如果逾期且未完成，将isComplated设置为3
            if (record.isComplated === 0 && this.isOverdue(record)) {
              record.isComplated = 3; // 已逾期

              // 更新数据库中的值
              const updateData = {
                id: record.id,
                isComplated: 3
              };

              // 调用更新API
              update(updateData).catch(error => {
                console.error('Error updating task status to overdue:', error);
              });
            }

            if (record.createUser && (!record.userName || !record.department)) {
              userPromises.push(
                getUser(record.createUser).then(userRes => {
                  const userData = userRes.data.data;
                  if (userData) {
                    record.userName = userData.realName;
                    record.department = userData.deptName;
                  }
                  return record;
                })
              );
            }
          });

          // 等待所有用户信息获取完成
          Promise.all(userPromises).then(() => {
            this.data = records;
            this.loading = false;
            this.selectionClear();
          }).catch(() => {
            this.data = records;
            this.loading = false;
            this.selectionClear();
          });
        });
      }
    }
  };
</script>

<style>
</style>

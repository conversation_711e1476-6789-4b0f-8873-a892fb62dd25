import request from "@/router/axios";

export const getBudgetTree = (params) => {
  return request({
    url: "/api/ni/por/budget/tree",
    method: "get",
    params,
  });
};

export const getLazyList = (parentId, params) => {
  return request({
    url: "/api/ni/por/budget/lazy-list",
    method: "get",
    params: {
      ...params,
      parentId,
    },
  });
};

export const getList = (current, size, params) => {
  return request({
    url: "/api/ni/por/budget/page",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/ni/por/budget/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const remove = (ids) => {
  return request({
    url: "/api/ni/por/budget/remove",
    method: "post",
    params: {
      ids,
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/ni/por/budget/save",
    method: "post",
    data: row,
  });
};

export const update = (row) => {
  return request({
    url: "/api/ni/por/budget/update",
    method: "post",
    data: row,
  });
};

export const getDetailByProcessInsId = (processInsId) => {
  return request({
    url: "/api/ni/por/budget/detail",
    method: "get",
    params: {
      processInsId,
    },
  });
};

export const getUnFinishPage = (current, size, params, deptId) => {
  return request({
    url: "/api/ni/por/budget/unFinishPage",
    method: "get",
    params: {
      ...params,
      deptId,
      current,
      size,
    },
  });
};

export const getAllUnFinishPage = (current, size, params, deptId) => {
  return request({
    url: "/api/ni/por/budget/allUnFinishPage",
    method: "get",
    params: {
      ...params,
      deptId,
      current,
      size,
    },
  });
};

export const apply = (row, processDefKey) => {
  return request({
    url: "/api/ni/por/budget/apply",
    method: "post",
    params: {
      processDefKey,
    },
    data: row,
  });
};

export const repairSubmit = (row, processDefKey) => {
  return request({
    url: "/api/ni/por/budget/repairSubmit",
    method: "post",
    params: {
      processDefKey,
    },
    data: row,
  });
};

export const getPrintData = (id) => {
  return request({
    url: "/api/ni/por/budget/getPrintData",
    method: "get",
    params: {
      id,
    },
  });
};
export const getItemsPrintData = (budgetId) => {
  return request({
    url: "/api/ni/por/budget/getItemsPrintData",
    method: "get",
    params: {
      budgetId,
    },
  });
};

<script>
import InventorySummarySelectDialog from "@/views/ni/fg/components/InventorySummarySelectDialog.vue";
import { mapGetters } from "vuex";
import { dateFormat } from "@/util/date";
import { add, getDetail, itemsByOldFaHuoBianHao } from "@/api/ni/fg/fgOutbound";
import {
  getBeforeBatchNo,
  getList as getBatchNos,
} from "@/api/ni/fg/fgInventory";
import GuoWaiFaHuoSelect from "@/views/ni/old/components/GuoWaiFaHuoSelect.vue";
import XiaoShouDingDanSelect from "@/views/ni/old/components/XiaoShouDingDanSelect.vue";
import XiaoShouWaiKuBuHuoSelect from "@/views/ni/old/components/XiaoShouWaiKuBuHuoSelect.vue";

export default {
  name: "OutboundFormDialog",
  components: {
    XiaoShouWaiKuBuHuoSelect,
    XiaoShouDingDanSelect,
    GuoWaiFaHuoSelect,
    InventorySummarySelectDialog,
  },
  data() {
    return {
      visible: false,
      option: {
        emptyBtn: false,
        size: "mini",
        span: 8,
        labelWidth: 110,
        column: [
          {
            label: "出库编号",
            prop: "serialNo",
            placeholder: "系统自动生成",
            overHeight: true,
          },
          {
            label: "出库主题",
            prop: "title",
            overHeight: true,
            rules: [
              {
                required: true,
                message: "请输入出库主题",
                trigger: "blur",
              },
            ],
          },
          {
            label: "出库类型",
            prop: "type",
            type: "select",
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_fg_outbound_type",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            rules: [
              {
                required: true,
                message: "请选择出库类型",
                trigger: "blur",
              },
            ],
            control: (val) => {
              if (val === "0207") {
                return {
                  backReason: {
                    display: true,
                  },
                };
              } else {
                return {
                  backReason: {
                    display: false,
                  },
                };
              }
            },
          },
          {
            label: "仓库",
            prop: "depotId",
            type: "select",
            dicUrl: "/api/ni/base/depot/info/list?status=2&type=1",
            props: {
              label: "name",
              value: "id",
              desc: "code",
            },
            rules: [
              {
                required: true,
                message: "请选择仓库",
                trigger: "blur",
              },
            ],
          },
          {
            label: "操作人",
            prop: "opUserId",
            type: "select",
            remote: true,
            dicUrl: `/api/blade-user/search/otj/user?name={{key}}`,
            props: {
              label: "realName",
              value: "id",
            },
            dicFormatter: (data) => {
              return data.data.records;
            },
            placeholder: " ",
            filterable: true,
            overHidden: true,
            rules: [
              {
                required: true,
                message: "请选择操作人",
                trigger: "blur",
              },
            ],
          },
          {
            label: "出库时间",
            prop: "opDate",
            search: true,
            type: "date",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            searchRange: true,
            placeholder: " ",
            clearable: false,
            minWidth: 120,
            rules: [
              {
                required: true,
                message: "请选择操作时间",
                trigger: "blur",
              },
            ],
          },
          {
            label: "关联单据",
            prop: "relatedOrderId",
            type: "input",
          },
          {
            label: "退生产原因",
            prop: "backReason",
            type: "select",
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_fg_outbound_back_reason",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            display: false,
            placeholder: " ",
            rules: [
              {
                required: true,
                message: "请选择退生产原因",
                trigger: "blur",
              },
            ],
          },
          {
            label: "备注",
            prop: "remark",
            type: "textarea",
            span: 24,
            minRows: 2,
            search: true,
          },
          {
            label: "出库明细",
            prop: "items",
            labelPosition: "top",
            span: 24,
            type: "dynamic",
            hide: true,
            showColumn: false,
            children: {
              rowAdd: () => {
                this.handleInventorySelect();
              },
              size: "mini",
              align: "center",
              headerAlign: "center",
              showSummary: true,
              sumColumnList: [
                {
                  name: "num",
                  type: "sum",
                  decimals: 1,
                },
              ],
              column: [
                {
                  label: "存货编码",
                  prop: "materialCode",
                  placeholder: " ",
                  width: 130,
                  cell: false,
                  display: false,
                },
                {
                  label: "规格",
                  prop: "specText",
                  placeholder: " ",
                  display: false,
                  overHidden: true,
                  cell: false,
                  width: 120,
                },
                {
                  label: "外包装",
                  prop: "packageText",
                  placeholder: " ",
                  display: false,
                  overHidden: true,
                  cell: false,
                  width: 115,
                },
                {
                  label: "内包装",
                  prop: "innerPackageText",
                  placeholder: " ",
                  display: false,
                  overHidden: true,
                  cell: false,
                  width: 115,
                },
                {
                  label: "质量",
                  prop: "qualityLevel",
                  type: "select",
                  dicUrl:
                    "/api/blade-system/dict-biz/dictionary?code=ni_product_sku_quality_level",
                  props: {
                    label: "dictValue",
                    value: "dictKey",
                    desc: "dictKey",
                  },
                  placeholder: " ",
                  width: 115,
                  cell: false,
                },
                {
                  label: "批号",
                  prop: "batchNo",
                  placeholder: " ",
                  minWidth: 110,
                  overHidden: true,
                  rules: [
                    {
                      required: true,
                      message: "请选择批号",
                      trigger: "blur",
                    },
                  ],
                },
                {
                  label: "库存箱数",
                  prop: "currentStock",
                  placeholder: " ",
                  width: 110,
                  overHidden: true,
                  cell: false,
                },
                {
                  label: "数量",
                  prop: "num",
                  placeholder: " ",
                  type: "number",
                  minWidth: 80,
                  rules: [
                    {
                      required: true,
                      message: "请输入数量",
                      trigger: "blur",
                    },
                  ],
                },
                {
                  label: "重量",
                  prop: "weight",
                  placeholder: " ",
                  width: 140,
                  type: "number",
                  cell: false,
                },
                {
                  label: "单位",
                  prop: "unit",
                  type: "select",
                  filterable: true,
                  width: 80,
                  cell: false,
                  dicUrl:
                    "/api/blade-system/dict-biz/dictionary?code=ni_material_unit",
                  props: {
                    label: "dictValue",
                    value: "dictKey",
                  },
                  rules: [
                    {
                      required: true,
                      message: "请选择单位",
                      trigger: "blur",
                    },
                  ],
                  slot: true,
                  placeholder: " ",
                },
                {
                  label: "备注",
                  prop: "remark",
                  type: "textarea",
                  placeholder: " ",
                  minRows: 1,
                  overHidden: true,
                  minWidth: 120,
                },
                {
                  label: "#",
                  prop: "action",
                  width: 60,
                  fixed: "right",
                },
              ],
            },
          },
        ],
      },
      form: {},
      excelBox: false,
      excelForm: {},
      excelOption: {
        size: "mini",
        searchSize: "mini",
        submitBtn: false,
        emptyBtn: false,
        column: [
          {
            label: "模板上传",
            prop: "excelFile",
            type: "upload",
            drag: true,
            loadText: "模板上传中，请稍等",
            span: 24,
            propsHttp: {
              res: "data",
            },
            tip: "请上传 .xls,.xlsx 标准格式文件",
            action: "/api/ni/fg/outbound/uploadItems",
          },
          {
            label: "模板下载",
            prop: "excelTemplate",
            formslot: true,
            span: 24,
          },
        ],
      },
      templateColumn: [
        {
          label: "存货编码",
          prop: "materialCode",
        },
        {
          label: "批号",
          prop: "batchNo",
        },
        {
          label: "箱数",
          prop: "num",
        },
        {
          label: "备注",
          prop: "remark",
        },
      ],
    };
  },
  computed: {
    ...mapGetters(["userInfo"]),
    items() {
      return this.form.items || [];
    },
  },
  methods: {
    handleImport() {
      this.excelBox = true;
    },
    handleTemplate() {
      this.$Export.excel({
        title: "出库明细导入",
        columns: this.templateColumn,
        data: [],
      });
    },
    uploadAfter(res, done) {
      this.excelBox = false;
      done();
      if (res && res.length > 0) {
        res.forEach((item) => {
          this.form.items.push(item);
        });
        this.$confirm(res, "提示", {
          confirmButtonText: "确定",
          type: "warning",
          center: true,
        });
      }
    },
    onEdit(id) {
      Object.keys(this.form).forEach((key) => (this.form[key] = null));
      this.form.items = [];
      this.option.detail = false;
      getDetail(id).then((res) => {
        this.form = res.data.data;
        this.visible = true;
      });
    },
    onShow(id) {
      Object.keys(this.form).forEach((key) => (this.form[key] = null));
      this.form.items = [];
      this.option.detail = true;
      getDetail(id).then((res) => {
        this.form = res.data.data;
        this.visible = true;
      });
    },
    /**
     *
     * @param depotId 仓库id
     * @param inventories 库存明细
     * @param form 表单数据
     */
    onAdd(depotId, inventories, form) {
      this.option.detail = false;
      Object.keys(this.form).forEach((key) => (this.form[key] = null));
      this.form.items = [];
      if (inventories) {
        this.handleInventorySelectConfirm(inventories);
      }
      this.form.depotId = depotId;
      this.form.opUserId = this.userInfo.user_id;
      this.form.opDate = dateFormat(new Date(), "yyyy-MM-dd");
      if (form)
        this.form = {
          ...this.form,
          ...form,
        };
      console.log(this.form);
      this.visible = true;
    },
    rowCopy(row, index) {
      const copy = {
        ...row,
        id: null,
        batchNo: null,
      };
      this.form.items.splice(index + 1, 0, copy);
    },
    rowNumChange(value, row) {
      row.weight = Number(value) * Number(row.capacity);
    },
    handleInventorySelect() {
      if (!this.form.depotId) {
        this.$message({
          type: "warning",
          message: "请选择仓库!",
        });
        return;
      }
      this.$refs.inventorySelectDialog.onShow();
    },
    handleInventorySelectConfirm(selectionList) {
      selectionList.forEach((item) => {
        this.items.push({
          skuId: item.skuId,
          skuText: item.skuText,
          spec: item.spec,
          specText: item.specText,
          packageId: item.packageId,
          packageText: item.packageText,
          innerPackageText: item.innerPackageText,
          materialId: item.materialId,
          materialCode: item.materialCode,
          currentStock: item.num,
          capacity: item.capacity,
          unit: item.unit,
          qualityLevel: item.qualityLevel,
          batchNoOptions: [],
          batchNoLoading: false,
        });
      });
    },
    async handleConfirm(form, done) {
      if (form.items && this.hasDuplicateBatchNo(form.items)) {
        this.$message({
          type: "warning",
          message: "请勿重复选择批号!",
        });
        done();
        return;
      }
      const before = await this.checkBefore1(form.depotId, form.items);
      if (before && before.length > 0) {
        this.$confirm("选择的库存中有更早的批号, 是否继续?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            this.handleAdd(form, done);
          })
          .catch(() => {
            done();
          });
      } else {
        this.handleAdd(form, done);
      }
    },
    handleAdd(form, done) {
      if (form.items && form.items.length > 0) {
        const unDepots = [];
        form.items.forEach((item) => {
          if (item.inventoryDepotId !== form.depotId) {
            unDepots.push(item.batchNo);
          }
        });
        if (unDepots.length > 0) {
          this.$confirm(
            `明细中的批号:<span style="color:#F56C6C;">${unDepots}</span>异常(<span style="color:#F56C6C;">批号所属仓库错误</span>), 是否继续?`,
            "提示",
            {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              dangerouslyUseHTMLString: true,
              type: "warning",
            }
          )
            .then(() => {
              this.handleAddNext(form, done);
            })
            .catch(() => {
              done();
            });
        } else {
          this.handleAddNext(form, done);
        }
      }
    },
    handleAddNext(form, done) {
      add(form)
        .then(() => {
          this.visible = false;
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$emit("confirm", form);
        })
        .finally(() => {
          done();
        });
    },
    hasDuplicateBatchNo(items) {
      const skuBatchMap = new Map(); // 存储 { skuId: Set<batchNo> }
      for (const item of items) {
        // 跳过无效数据
        if (!item || !item.skuId || item.batchNo === undefined) continue;

        // 初始化 Set
        if (!skuBatchMap.has(item.skuId)) {
          skuBatchMap.set(item.skuId, new Set());
        }

        const batchSet = skuBatchMap.get(item.skuId);
        if (batchSet.has(item.batchNo)) {
          return true; // 发现相同 skuId 下的批号重复
        }
        batchSet.add(item.batchNo);
      }
      return false; // 无重复
    },
    handleXiaoShouDingDanSelectConfirm(selectionList) {
      this.form.relatedOrderText = selectionList[0].fdbh;
      this.$confirm("是否导入货物流转明细数据?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        itemsByOldFaHuoBianHao(selectionList[0].fdbh).then((res) => {
          this.form.items = res.data.data;
        });
      });
    },
    batchNoRemoteMethod(query, row) {
      console.log(query);
      if (query !== "") {
        row.batchNoLoading = true;
        getBatchNos(1, 20, {
          batchNo: query,
          skuId: row.skuId,
          status: ["0202", "0207", "0203", "0208"].includes(this.form.type)
            ? null
            : 1,
        }).then((res) => {
          row.batchNoLoading = false;
          const data = res.data.data;
          data.records.forEach((item) => {
            item.label = `${item.batchNo}(${item.depotName})`;
          });
          row.batchNoOptions = data.records.filter((item) => {
            return item.batchNo.toLowerCase().indexOf(query.toLowerCase()) > -1;
          });
        });
      } else {
        row.batchNoOptions = [];
      }
    },
    async rowBatchNoChange(val, row) {
      if (row.batchNoOptions && row.batchNoOptions.length > 0) {
        const selectedItem = row.batchNoOptions.find(
          (item) => item.batchNo === val
        );
        if (selectedItem) {
          //校验批次是否存在较早的批次
          row.inventoryId = selectedItem.id;
          row.inventoryDepotId = selectedItem.depotId;
          row.num = selectedItem.num; // 将批号对应数量赋值
          row.productionDate = selectedItem.productionDate;
          this.rowNumChange(row.num, row);
        }
      }
    },
    async checkBefore1(depotId, selectionList) {
      let ids = new Set();
      selectionList.forEach((ele) => {
        ids.add(ele.inventoryId);
      });
      const res = await getBeforeBatchNo(depotId, Array.from(ids).join(","));
      return res.data.data;
    },
    handleXiaoShouWaiKuBuHuoSelectConfirm(selectionList) {
      this.form.relatedOrderText = selectionList[0].faHuoBianHao;
      this.$confirm("是否导入货物流转明细数据?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        itemsByOldFaHuoBianHao(selectionList[0].faHuoBianHao).then((res) => {
          this.form.items = res.data.data;
        });
      });
    },
  },
};
</script>

<template>
  <el-dialog
    :visible.sync="visible"
    fullscreen
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    append-to-body
  >
    <avue-form
      v-if="visible"
      :option="option"
      v-model="form"
      @submit="handleConfirm"
    >
      <template #itemsLabel>
        <div style="display: flex; align-items: center; margin-bottom: 10px">
          出库明细
          <el-divider direction="vertical" />
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-upload2"
            @click="handleImport"
            >导入
          </el-button>
        </div>
      </template>
      <template #batchNo="{ row, size, disabled, index }">
        <el-select
          :size="size"
          :disabled="disabled"
          v-model="row.batchNo"
          filterable
          remote
          allow-create
          placeholder=" "
          :remote-method="
            (query) => {
              batchNoRemoteMethod(query, row);
            }
          "
          :loading="row.batchNoLoading"
          @change="rowBatchNoChange($event, row)"
        >
          <el-option
            v-for="item in row.batchNoOptions"
            :key="item.id"
            :label="item.batchNo"
            :value="item.batchNo"
          >
            <span style="float: left">{{
              `${item.batchNo}(${item.num})`
            }}</span>
            <span
              style="
                position: absolute;
                right: 18px;
                font-size: 12px;
                color: #8492a6;
              "
              >{{ item.depotName }}</span
            >
          </el-option>
        </el-select>
      </template>
      <template #action="{ row, index }">
        <el-button
          type="text"
          icon="el-icon-copy-document"
          @click="rowCopy(row, index)"
        ></el-button>
      </template>
      <template #relatedOrderId="{ size, disabled }">
        <xiao-shou-ding-dan-select
          v-if="form.type === '0201'"
          v-model="form.relatedOrderId"
          :size="size"
          :disabled="disabled"
          @confirm="handleXiaoShouDingDanSelectConfirm"
        />
        <guo-wai-fa-huo-select
          v-else-if="form.type === '0205'"
          v-model="form.relatedOrderId"
          :size="size"
          :disabled="disabled"
          @confirm="
            (selectionList) =>
              (form.relatedOrderText = selectionList[0].faHuoBianHao)
          "
        />
        <xiao-shou-wai-ku-bu-huo-select
          v-else-if="form.type === '0206'"
          v-model="form.relatedOrderId"
          :size="size"
          :disabled="disabled"
          @confirm="handleXiaoShouWaiKuBuHuoSelectConfirm"
        />
        <el-input
          v-else
          v-model="form.relatedOrderText"
          :size="size"
          :disabled="disabled"
        />
      </template>
      <template #num="{ row, size, disabled }">
        <el-input-number
          v-model="row.num"
          :min="1"
          :max="
            !option.detail && row.currentStock != null ? row.currentStock : 'Infinity'
          "
          :size="size"
          :step="1"
          :controls="false"
          :disabled="disabled"
          @change="rowNumChange($event, row)"
        />
      </template>
    </avue-form>
    <inventory-summary-select-dialog
      ref="inventorySelectDialog"
      :params="{ depotId: form.depotId, status: 1 }"
      multiple
      @confirm="handleInventorySelectConfirm"
    />
    <el-dialog
      title="明细导入"
      append-to-body
      :visible.sync="excelBox"
      width="555px"
    >
      <avue-form
        :option="excelOption"
        v-model="excelForm"
        :upload-after="uploadAfter"
      >
        <template slot="excelTemplate">
          <el-button size="mini" type="primary" @click="handleTemplate">
            点击下载<i class="el-icon-download el-icon--right"></i>
          </el-button>
        </template>
      </avue-form>
    </el-dialog>
  </el-dialog>
</template>

<style scoped></style>

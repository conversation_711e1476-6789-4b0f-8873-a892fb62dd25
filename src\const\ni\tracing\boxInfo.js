export default {
  height: "auto",
  calcHeight: 30,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: false,
  viewBtn: false,
  addBtn: false,
  editBtn: false,
  delBtn: false,
  selection: false,
  refreshBtn: true,
  dialogClickModal: false,
  menuWidth: 160,
  column: [
    {
      label: "序号",
      prop: "boxNumber",
      type: "input",
      hide: true,
    },
    {
      label: "箱号",
      prop: "boxCode",
      type: "input",
      width: 125,
      search: true,
    },
    {
      label: "生产日期",
      prop: "productionDate",
      type: "input",
      width: 140,
    },
    {
      label: "品牌",
      prop: "brandName",
      type: "input",
      width: 50,
    },
    {
      label: "内包装",
      prop: "innerPackagingName",
      type: "input",
      width: 90,
    },
    {
      label: "外包装",
      prop: "outerPackagingName",
      type: "input",
      width: 150,
    },
    {
      label: "容量",
      prop: "outerPackagingCapacity",
      type: "input",
      width: 50,
    },
    {
      label: "规格",
      prop: "specificationName",
      type: "input",
      width: 80,
    },
    {
      label: "质量",
      prop: "qualityName",
      type: "input",
      width: 60,
    },
    {
      label: "打标次数",
      prop: "markingCount",
      type: "input",
      width: 80,
      hide: true,
    },
    {
      label: "已倒箱",
      prop: "isInvalid",
      type: "select",
      width: 60,
      dicData: [
        {
          label: "否",
          value: 0,
        },
        {
          label: "是",
          value: 1,
        },
      ],
      html: true,
      formatter: function (val, value, label) {
        if (value === 1) {
          return `<span style="color:red">${label}</span>`;
        } else {
          return `<span style="color:green">${label}</span>`;
        }
      },
    },
    {
      label: "倒箱时间",
      prop: "invalidDate",
      type: "input",
      hide: true,
    },
    {
      label: "原箱编码",
      prop: "sourceBoxCode",
      type: "input",
      hide: true,
    },
    {
      label: "变动",
      prop: "channel",
      type: "select",
      dicData: [
        {
          label: "正常",
          value: 0,
        },
        {
          label: "倒箱",
          value: 1,
        },
        {
          label: "倒垛",
          value: 2,
        },
      ],
    },
  ],
};

import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/ni-ofc-task-reminder/ofcTaskReminder/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/ni-ofc-task-reminder/ofcTaskReminder/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/ni-ofc-task-reminder/ofcTaskReminder/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/ni-ofc-task-reminder/ofcTaskReminder/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/ni-ofc-task-reminder/ofcTaskReminder/submit',
    method: 'post',
    data: row
  })
}


<template>
  <el-drawer
    title="用户列表"
    :visible.sync="visible"
    direction="rtl"
    append-to-body
    size="800px"
  >
    <div class="container">
      <avue-crud
        ref="crud"
        v-if="visible"
        :option="option"
        :data="data"
        v-model="form"
        :page.sync="page"
        :before-open="beforeOpen"
        :before-close="beforeClose"
        @row-save="rowSave"
        @row-del="rowDel"
        @selection-change="selectionChange"
        @current-change="currentChange"
        @size-change="sizeChange"
        @refresh-change="refreshChange"
        @on-load="onLoad"
      >
        <template #menuForm="{ row, index }">
          <el-button
            type="primary"
            icon="el-icon-check"
            size="mini"
            plain
            v-if="type === 'add'"
            @click="handleNext"
            >继续添加
          </el-button>
        </template>
      </avue-crud>
    </div>
  </el-drawer>
</template>

<script>
import { mapGetters } from "vuex";
import { add, remove, getList } from "@/api/ni/meal/takeout-user-group-members";
import { getDetail } from "@/api/ni/meal/takeout-locations";

export default {
  data() {
    return {
      visible: false,
      loading: false,
      selectionList: [],
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      type: "",
      flag: false,
      option: {
        menuWidth: 150,
        editBtn: false,
        span: 12,
        dialogDrag: true,
        size: "mini",
        searchSize: "mini",
        align: "center",
        searchIndex: 2,
        searchIcon: true,
        searchEnter: true,
        calcHeight: 30,
        tip: false,
        searchShow: false,
        border: true,
        selection: true,
        dialogClickModal: false,
        column: [
          {
            label: "选择人员",
            prop: "userId",
            search: true,
            span: 24,
            type: "select",
            remote: true,
            dicUrl: `/api/blade-user/search/otj/user?name={{key}}`,
            props: {
              label: "realName",
              value: "id",
            },
            dicFormatter: (data) => {
              return data.data.records;
            },
            placeholder: " ",
            hide: true,
            showColumn: false,
          },
          {
            label: "人员名称",
            prop: "realName",
            overHidden: true,
            width: 110,
            display: false,
          },
          {
            label: "所属部门",
            prop: "deptName",
            filters: true,
            slot: true,
            display: false,
          },
          {
            label: "岗位",
            prop: "postName",
            slot: true,
            display: false,
          },
        ],
      },
      data: [],
      form: {},
      groupId: null,
    };
  },
  computed: {
    ...mapGetters(["userInfo"]),
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  methods: {
    onShow(groupId) {
      this.groupId = groupId;
      this.visible = true;
    },
    handleNext() {
      this.flag = true;
      this.$refs.crud.rowSave();
    },
    rowSave(row, done, loading) {
      row.groupId = this.groupId;
      add(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          if (this.flag) {
            this.flag = false;
            loading();
            this.form.userId = "";
            return;
          }
          done();
        },
        (error) => {
          loading();
          window.console.log(error);
        }
      );
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    beforeClose(done) {
      this.flag = false;
      done();
    },
    beforeOpen(done, type) {
      this.type = type;
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      getList(page.currentPage, page.pageSize, {
        ...params,
        ...this.query,
        groupId: this.groupId,
      }).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
  },
};
</script>

<style scoped>
.container {
  margin: 15px;
}
</style>

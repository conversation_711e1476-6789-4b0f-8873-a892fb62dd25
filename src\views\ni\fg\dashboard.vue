<template>
  <div>
    <div class="header">
      <el-radio-group v-model="area" size="mini" @input="reload()">
        <el-radio-button label="CN">国内</el-radio-button>
        <el-radio-button label="OS" disabled>国外</el-radio-button>
      </el-radio-group>
    </div>
    <div class="row-container">
      <core-panel v-model="coreData" />
    </div>
    <div class="row-container">
      <trend-analysis-panel
        ref="trendAnalysisRef"
        v-model="trendData"
        :trendPieData="trendPieData"
        @on-load="loadInventoryLine"
      />
    </div>
    <div class="row-container">
      <inbound-and-outbound-analysis-panel
        ref="inboundAndOutboundAnalysisRef"
        v-model="transactionItemData"
        @on-load="loadTransactionItemLine"
      />
    </div>
    <div class="row-container">
      <product-level-analysis-panel
        :inventorySpecData="inventorySpecData"
        :inventorySkuData="inventorySkuData"
      />
    </div>
    <div class="row-container">
      <daily-snapshot-panel ref="dailySnapshotRef" :area="area" />
    </div>
  </div>
</template>

<script>
import CorePanel from "@/views/ni/fg/components/dashboard/CorePanel.vue";
import TrendAnalysisPanel from "@/views/ni/fg/components/dashboard/TrendAnalysisPanel.vue";
import InboundAndOutboundAnalysisPanel from "@/views/ni/fg/components/dashboard/InboundAndOutboundAnalysisPanel.vue";
import ProductLevelAnalysisPanel from "@/views/ni/fg/components/dashboard/ProductLevelAnalysisPanel.vue";
import DailySnapshotPanel from "@/views/ni/fg/components/dashboard/DailySnapshotPanel.vue";
import {
  getCoreReport,
  getDailyInventorySum,
  getInventorySumGroupByDepotId,
  getDailyInAndOutboundSum,
  getInventorySumGroupBySpec,
  getInventorySumGroupBySkuId,
} from "@/api/ni/fg/dashboard";

export default {
  components: {
    ProductLevelAnalysisPanel,
    InboundAndOutboundAnalysisPanel,
    TrendAnalysisPanel,
    CorePanel,
    DailySnapshotPanel,
  },
  data() {
    return {
      area: "CN",
      coreData: {},
      trendData: {
        current: [],
        lastMonth: [],
        samePeriodLastYear: [],
      },
      trendPieData: [],
      transactionItemData: [],
      inventorySpecData: [],
      inventorySkuData: [],
    };
  },
  mounted() {
    this.loadCoreData(this.area);
    this.loadInventoryPie(this.area);
    this.loadProductLevelAnalysisData(this.area);
  },
  methods: {
    reload() {
      this.loadCoreData(this.area);
      this.loadInventoryPie(this.area);
      this.loadProductLevelAnalysisData(this.area);
      this.$refs.trendAnalysisRef.reload();
      this.$refs.inboundAndOutboundAnalysisRef.reload();
      this.$refs.dailySnapshotRef.handleReload();
    },
    loadProductLevelAnalysisData(area) {
      getInventorySumGroupBySpec(area).then((res) => {
        this.inventorySpecData = res.data.data;
      });
      getInventorySumGroupBySkuId(area).then((res) => {
        this.inventorySkuData = res.data.data;
      });
    },
    loadCoreData(area) {
      getCoreReport(area).then((res) => {
        this.coreData = res.data.data;
      });
    },
    loadTransactionItemLine(start, end) {
      getDailyInAndOutboundSum(this.area, start, end).then((res) => {
        this.transactionItemData = res.data.data;
      });
    },
    loadInventoryPie(area) {
      getInventorySumGroupByDepotId(area).then((res) => {
        this.trendPieData = res.data.data;
      });
    },

    loadInventoryLine(start, end) {
      getDailyInventorySum(this.area, start, end).then((res) => {
        this.trendData.current = res.data.data;
      });
      // 上个月数据
      const { start: lastMonthStart, end: lastMonthEnd } =
        this.getPreviousMonthRange(start, end);
      getDailyInventorySum(this.area, lastMonthStart, lastMonthEnd).then(
        (res) => {
          this.trendData.lastMonth = res.data.data;
        }
      );
      // 去年同期数据
      const { start: samePeriodStartStr, end: samePeriodEndStr } =
        this.getSamePeriodMonthRange(start, end);
      getDailyInventorySum(
        this.area,
        samePeriodStartStr,
        samePeriodEndStr
      ).then((res) => {
        this.trendData.samePeriodLastYear = res.data.data;
      });
    },
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      return `${year}-${month}-${day}`;
    },
    getSamePeriodMonthRange(start, end) {
      const startDate = new Date(start);
      const endDate = new Date(end);
      const samePeriodStart = new Date(
        startDate.getFullYear() - 1,
        startDate.getMonth(),
        startDate.getDate()
      );
      const samePeriodEnd = new Date(
        startDate.getFullYear() - 1,
        endDate.getMonth(),
        endDate.getDate()
      );
      return {
        start: this.formatDate(samePeriodStart),
        end: this.formatDate(samePeriodEnd),
      };
    },
    // 计算上个月的起止日期
    getPreviousMonthRange(start, end) {
      const startDate = new Date(start);
      const endDate = new Date(end);
      const lastMonthStart = new Date(
        startDate.getFullYear(),
        startDate.getMonth() - 1,
        startDate.getDate()
      );
      const lastMonthEnd = new Date(
        startDate.getFullYear(),
        endDate.getMonth() - 1,
        endDate.getDate()
      );
      return {
        start: this.formatDate(lastMonthStart),
        end: this.formatDate(lastMonthEnd),
      };
    },
  },
};
</script>
<style lang="scss" scoped>
.header {
  margin-bottom: 0;
  border: 0;
  padding: 0;
  position: sticky;
  top: 0;
  z-index: 1000;
}

.row-container {
  margin-top: 16px;
  margin-bottom: 16px;

  &:last-child {
    margin-bottom: 60px;
  }
}

.row-container:first-of-type {
  margin-top: 10px;
}
</style>

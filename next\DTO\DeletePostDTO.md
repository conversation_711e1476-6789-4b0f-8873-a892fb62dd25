```java
// 定义包路径，属于问题反馈模块的数据传输对象包
package com.natergy.ni.feedback.dto;

// 导入Lombok注解：@Data生成getter/setter等方法，@AllArgsConstructor生成全参构造器，@NoArgsConstructor生成无参构造器
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>  // 作者标识
 */
// @Data：Lombok注解，自动生成类的getter、setter、toString、equals、hashCode等方法
@Data
// @AllArgsConstructor：Lombok注解，自动生成包含所有字段的构造器
@AllArgsConstructor
// @NoArgsConstructor：Lombok注解，自动生成无参构造器
public class DeletePostDTO {
    // 要删除的点的ID（业务上可能对应某个实体的唯一标识，如向量数据库中的点ID）
    private Integer pointId;
}
```

### 类功能说明

该类是一个数据传输对象（DTO，Data Transfer Object），用于在不同层（如控制器与服务层、服务层与外部接口）之间传输 "删除操作" 相关的参数。

- 核心字段`pointId`表示要删除的目标对象的 ID（从命名推测可能与向量数据库中的 "点" 相关）
- 通过 Lombok 注解简化了 JavaBean 的样板代码，自动生成必要的方法和构造器
- 通常通常用于在删除操作的接口调用中作为请求参数载体使用，例如作为 RESTful API 的请求体或参数对象
<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :search.sync="query"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template #menuLeft>
        <el-button
          type="warning"
          icon="el-icon-link"
          size="mini"
          @click="handleLinkGwBh"
        >
          关联备货计划
        </el-button>
      </template>
      <template #menu="{ row, index, size }">
        <el-button
          type="text"
          icon="el-icon-setting"
          size="mini"
          plain
          :disabled="row.publishStatus == 1"
          style="border: 0; background-color: transparent !important"
          v-if="permission.qualityInspection_publish"
          @click.stop="updatePublish(row)"
        >
          发布
        </el-button>
        <el-button
          type="text"
          icon="el-icon-setting"
          size="mini"
          plain
          style="border: 0; background-color: transparent !important"
          @click.stop="handleBatch(row)"
        >
          关联批次
        </el-button>
        <el-button
          :type="row.updateStatus === '1' ? 'warning' : 'text'" 
          icon="el-icon-setting"
          size="mini"
          plain
          :disabled="row.publishStatus == 0"
          style="border: 0; background-color: transparent !important"
          @click.stop="handleModifiedShow(row)"
        >
          修改对外报告
        </el-button>

        <el-button
          type="text"
          icon="el-icon-setting"
          size="mini"
          plain
          style="border: 0; background-color: transparent !important"
          @click.stop="copyQualityInspection(row)"
        >
          复制
        </el-button>

        <el-button
          type="text"
          icon="el-icon-setting"
          size="mini"
          plain
          :disabled="row.synchronousStatus == 1 || row.publishStatus == 0"
          style="border: 0; background-color: transparent !important"
          @click.stop="synchronous(row)"
        >
          同步到旧NI
        </el-button>

      </template>
      <template #menuRight="{ size }">
        <el-button
          icon="el-icon-time"
          circle
          :size="size"
          @click="handleLog"
        ></el-button>
      </template>
    </avue-crud>
    <quality-inspection-production-batch-dialog
      ref="qualityInspectionProductionBatchDialog"
    />
    <el-drawer
      :title="`选择[${scopePropertyName}]要关联的批号`"
      :visible.sync="batchDrawerVisible"
      :direction="direction"
      append-to-body
      :before-close="handleBatchDrawerClose"
      size="70%"
    >
      <div>
        <production-batch-select
          ref="productionBatchSelect"
          v-if="batchDrawerVisible"
          :area="area"
          :shipCode="shipCode"
        />
      </div>

      <div class="demo-drawer__footer" style="margin: 10px 10px">
        <el-button @click="closeBatchDrawer" size="mini">取 消</el-button>
        <el-button type="primary" @click="handleConfirm" size="mini"
          >确 定
        </el-button>
      </div>
    </el-drawer>

    <el-dialog
      title="修改对外展示质检报告"
      :visible.sync="modifiedVisible"
      :modal="false"
      :close-on-click-modal="false"
      width="60%"
    >
      <quality-inspection-modified
        v-if="modifiedVisible"
        :modified-inspection="modifiedInspection"
        @submit-modified="submitModified"
        @close-modified="closeModified"
      />
      <!-- <span slot="footer" class="dialog-footer">
        <el-button @click="closeModified">取 消</el-button>
        <el-button type="primary" @click="closeModified">确 定</el-button>
      </span> -->
    </el-dialog>
    <log-opt-dialog ref="logOptDialogRef" :module="module" />
    <guo-wai-bei-huo-ji-hua-select-dialog
      ref="guoWaiBeiHuoJiHuaSelectRef"
      @confirm="handleLinkGwBhConfirm"
    />
  </basic-container>
</template>

<script>
import {
  getList,
  getDetail,
  add,
  update,
  publish,
  remove,
  associate,
  getBatch,
  linkGuoWaiBeiHuoJiHua,
  copy,
  synchronous,
} from "@/api/ni/tracing/qualityInspection";
import {
  getDetail as getModifedDetail,
  update as updateModified,
} from "@/api/ni/tracing/qualityInspectionModified";
import option from "@/const/ni/tracing/qualityInspectionOS";
import ProductionBatchSelect from "./productionBatchSelect.vue";
import QualityInspectionModified from "./qualityInspectionModifiedOS.vue";
import { mapGetters } from "vuex";
import LogOptDialog from "./components/log-opt-dialog";
import GuoWaiBeiHuoJiHuaSelectDialog from "@/views/ni/old/components/GuoWaiBeiHuoJiHuaSelectDialog.vue";
import QualityInspection from "@/views/ni/tracing/qualityInspection.vue";
import QualityInspectionProductionBatchDialog from "@/views/ni/tracing/components/QualityInspectionProductionBatchDialog.vue";

export default {
  components: {
    QualityInspection,
    GuoWaiBeiHuoJiHuaSelectDialog,
    ProductionBatchSelect,
    QualityInspectionModified,
    LogOptDialog,
    QualityInspectionProductionBatchDialog,
  },
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 50,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: option,
      data: [],
      scopePropertyName: "批次",
      batchDrawerVisible: false,
      direction: "rtl",
      inspectionId: "",
      modifiedVisible: false,
      modifiedInspection: {},
      area: "OS",
      shipCode: null,
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.qualityInspection_add, false),
        viewBtn: this.vaildData(this.permission.qualityInspection_view, false),
        delBtn: this.vaildData(this.permission.qualityInspection_delete, false),
        editBtn: this.vaildData(this.permission.qualityInspection_edit, false),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  methods: {
    showModified() {
      this.modifiedVisible = true;
    },
    closeModified() {
      this.modifiedVisible = false;
      this.onLoad(this.page);
    },
    handleModifiedShow(row) {
      getModifedDetail(row.id).then((res) => {
        this.modifiedInspection = res.data.data;
        this.showModified();
      });
    },
    submitModified(modified) {
      updateModified(modified).then(() => {
        this.$message({
          type: "success",
          message: "修改成功!",
        });
        this.closeModified();
        this.modifiedInspection = {};
      });
    },
    handleBatch(row) {
      // this.scopePropertyName = row.code;
      // this.batchDrawerVisible = true;
      // this.inspectionId = row.id;
      // this.shipCode = row.shipCode;
      this.$refs.qualityInspectionProductionBatchDialog.onShow(row);
    },
    updatePublish(row) {
      this.$confirm("确定发布当前质检报告?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        publish(row).then(
          () => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!",
            });
            done();
          },
          (error) => {
            loading();
            console.log(error);
          }
        );
      });
    },
    handleBatchDrawerClose(hide) {
      hide();
    },
    closeBatchDrawer() {
      this.batchDrawerVisible = false;
    },
    handleConfirm() {
      let ids = this.$refs.productionBatchSelect.ids;
      if (ids.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      const para = {};
      para.inspectionId = this.inspectionId;
      para.batchIds = ids;

      associate(para).then(() => {
        this.$message({
          type: "success",
          message: "操作成功!",
        });
        this.closeBatchDrawer();
      });
    },
    rowSave(row, done, loading) {
      row.area = "OS";
      add(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          window.console.log(error);
        }
      );
    },

    rowUpdate(row, index, done, loading) {
      if(row.adsorptionEN == null){
        row.adsorptionEN = ""
      }
      update(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          console.log(error);
        }
      );
    },

    rowDel(row) {
      //查询当前质检报告是否关联批次
      getBatch(row.id).then((res) => {
        const data = res.data.data;
        if (data && data.length > 0) {
          this.$confirm("当前质检报告已经关联生产批次，是否删除?", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          })
            .then(() => {
              return remove(row.id);
            })
            .then(() => {
              this.onLoad(this.page);
              this.$message({
                type: "success",
                message: "操作成功!",
              });
            });
        } else {
          this.$confirm("确定将选择数据删除?", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          })
            .then(() => {
              return remove(row.id);
            })
            .then(() => {
              this.onLoad(this.page);
              this.$message({
                type: "success",
                message: "操作成功!",
              });
            });
        }
      });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen(done, type) {
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then((res) => {
          this.form = res.data.data;
        });
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      this.query.area = "OS";
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
    handleLinkGwBh() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("该操作将同步生产批次的关联备货id，是否继续?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.$refs.guoWaiBeiHuoJiHuaSelectRef.onSelect();
      });
    },
    handleLinkGwBhConfirm(selectionList) {
      linkGuoWaiBeiHuoJiHua(this.ids, selectionList[0].id).then(() => {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!",
        });
        this.$refs.crud.toggleSelection();
      });
    },
    handleLog() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择要查看的数据");
        return;
      } else if (this.selectionList.length > 1) {
        this.$message.warning("只能选择一条数据");
        return;
      }
      this.$refs.logOptDialogRef.init(this.selectionList[0].id);
    },
    copyQualityInspection(row){
      this.$confirm("确定复制【"+ row.code +"】质检报告?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        copy(row.id).then(
          () => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!",
            });
            done();
          },
          (error) => {
            loading();
            console.log(error);
          }
        );
      });
    },

    synchronous(row){
      this.$confirm("确定将【"+ row.code +"】质检报告同步到旧NI?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        synchronous(row.id).then(
          () => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!",
            });
            done();
          },
          (error) => {
            loading();
            console.log(error);
          }
        );
      });
    },
  },
};
</script>

<style></style>

<template>
  <basic-container>
    <avue-crud :option="option" :table-loading="loading" :data="data" :page.sync="page" :before-open="beforeOpen"
               :search.sync="query" v-model="form" ref="crud" @search-change="searchChange" @search-reset="searchReset"
               @selection-change="selectionChange" @current-change="currentChange" @size-change="sizeChange"
               @refresh-change="refreshChange" @on-load="onLoad" :cell-style="cellStyle" :span-method="arraySpanMethod">
      <template #depotLocation="{ row, index }">
        <el-popover placement="top" width="160" :ref="`depot-popover-${row.id}`" @show="showTempDepotLocation(row)">
          <el-input type="textarea" :autosize="{ minRows: 2 }" placeholder=" " v-model="tempDepotLocation"
                    :ref="`tempDepotLocationRef-${row.id}`"
                    @keyup.enter.native="rowDepotLocationChange(row, tempDepotLocation)">
          </el-input>
          <div style="text-align: right; margin: 10px 0 0 0">
            <el-button size="mini" type="text" @click="rowDepotLocationClose(row)">
              取消
            </el-button>
            <el-button type="primary" size="mini" @click="rowDepotLocationChange(row, tempDepotLocation)">
              确定
            </el-button>
          </div>
          <el-link slot="reference" :type="row.depotLocation ? '' : 'info'" style="font-size: 12px">
            {{ row.depotLocation ? row.depotLocation : "暂无" }}
          </el-link>
        </el-popover>
      </template>
      <template #receiveRemark="{ row, index }">
        <el-popover placement="top" width="160" :ref="`receive-remark-popover-${row.id}`"
                    @show="showTempReceiveRemark(row)">
          <el-input type="textarea" :autosize="{ minRows: 2 }" placeholder=" " v-model="tempReceiveRemark"
                    :ref="`tempReceiveRemarkRef-${row.id}`"
                    @keyup.enter.native="rowReceiveRemarkChange(row, tempReceiveRemark)">
          </el-input>
          <div style="text-align: right; margin: 10px 0 0 0">
            <el-button size="mini" type="text" @click="rowReceiveRemarkClose(row)">
              取消
            </el-button>
            <el-button type="primary" size="mini" @click="rowReceiveRemarkChange(row, tempReceiveRemark)">
              确定
            </el-button>
          </div>
          <el-link slot="reference" :type="row.receiveRemark ? '' : 'info'" style="font-size: 12px">
            {{ row.receiveRemark ? row.receiveRemark : "暂无" }}
          </el-link>
        </el-popover>
      </template>
      <template #staging="{ row, index }">
        <el-tag size="mini" type="danger" effect="dark" v-if="row.staging">
          是
        </el-tag>
        <el-tag size="mini" type="info" effect="plain" v-else>否</el-tag>
      </template>
      <template #budgetTitle="{ row, index }">
        <el-tag size="mini" v-if="row.budgetYear === 2">{{ row.budgetYearDate }}年
        </el-tag>
        <span>{{ row.budgetTitle }}</span>
      </template>
      <template #menuLeft>
        <el-button type="primary" size="mini" icon="el-icon-truck" :disabled="selectionList.length <= 0" plain
                   @click="handleArrival">收货
        </el-button>
        <el-button type="warning" size="mini" icon="el-icon-refresh-left" :disabled="selectionList.length <= 0" plain
                   @click="handleReject">驳回
        </el-button>
        <el-dropdown size="mini" split-button type="primary" @click="handleStockIn" @command="handleStockInWithDepot">
          入 库
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item v-for="item in depotList" :key="item.id" :command="item.id">{{ item.name }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-dropdown @command="handleStockInWithDepotBatch">
          <el-button size="mini" type="warning">
            批量入库<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item v-for="item in depotList" :key="item.id" :command="item.id">{{ item.name }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-button type="warning" size="mini" icon="el-icon-edit" plain @click="handleMaterialChange">修改编码
        </el-button>
        <el-button type="danger" size="mini" icon="el-icon-s-promotion" plain @click="handle2Pv">转入压力容器
        </el-button>
        <el-button type="warning" size="mini" icon="el-icon-s-promotion" plain @click="handleToFa">转入固定资产
        </el-button>
        <el-button type="success" size="mini" icon="el-icon-download" plain @click="handleExport">导出
        </el-button>
        <el-divider direction="vertical"></el-divider>
        <el-tag>
          当前表格已选择
          <span style="font-weight: bolder">{{ selectionList.length }}</span> 项
          <el-button type="text" size="mini" @click="selectionClear">
            清空
          </el-button>
        </el-tag>
      </template>
      <template #unStockInNum="{ row, index, size }">
        <span style="color: #f56c6c; font-weight: bolder">
          {{ row.unStockInNum }}
        </span>
      </template>
      <template #inspection="{ row, disabled, size, index }">
        <el-tag :size="size" v-if="row.inspection">{{ row.$inspection }}
        </el-tag>
        <el-tag :size="size" type="danger" v-else>{{ row.$inspection }}</el-tag>
      </template>
      <template #brand="{ row, disabled, size, index }">
        <el-tag v-if="row.brand" :size="size" :effect="row.brand === '1' ? 'dark ' : 'plain'">
          {{ row.$brand }}
        </el-tag>
      </template>
      <template #payType="{ row, index }">
        <el-tag v-if="row.payType === '1'" size="mini">财务付款</el-tag>
        <el-tag v-else-if="row.payType === '2'" size="mini" type="danger">采购付款
        </el-tag>
      </template>
      <template #contractIdSearch="{ disabled, size, index }">
        <contract-select v-model="form.contractId" :pay-type="payType" :disabled="disabled"
                         :params="{ contractState: '2' }" :size="size"/>
      </template>
      <template #arrivalNum="{ row, index, size }">
        <span v-if="row.arrivalNum === row.num" style="font-weight: bolder; color: green">{{ row.arrivalNum }}</span>
        <span v-else-if="row.arrivalNum > row.num" style="font-weight: bolder; color: #f56c6c">{{
            row.arrivalNum
          }}</span>
        <span v-else style="color: #909399">{{ row.arrivalNum }}</span>
      </template>
      <template #arrival="{ row, index, size }">
        <el-tag v-if="row.arrival === '3'" size="mini" effect="dark">{{ row.$arrival }}
        </el-tag>
        <el-tag v-if="row.arrival === '2'" size="mini" effect="plain">{{ row.$arrival }}
        </el-tag>
      </template>
    </avue-crud>
    <stock-in-form ref="stockInFormRef" @submit="onLoad(page)"/>
    <el-dialog title="收货" append-to-body :visible.sync="receive.visible" width="555px">
      <avue-form :option="receive.option" v-model="receive.form" @submit="handleReceiveSubmit"></avue-form>
    </el-dialog>
    <material-select-dialog ref="materialSelectDialogRef" multiple @submit="handleMaterialChangeSubmit"/>
  </basic-container>
</template>

<script>
import {detail, unStockInPage, reject, changeMaterial} from "@/api/ni/por/order-arrival";
import {mapGetters} from "vuex";
import UserSelect from "@/components/user-select";
import {dateFormat, dateNow1} from "@/util/date";
import StockIn from "@/views/ni/depot/in/stock-in";
import StockInForm from "@/views/ni/depot/components/StockInSNForm";
import SupplierMultipleSelect from "@/views/ni/base/components/SupplierSelect";
import {receive} from "@/api/ni/por/order-item";
import {changeFa, changePv} from "@/api/ni/por/apply-item";
import {stockInByArrivalItems} from "@/api/ni/depot/stockIn";
import MaterialSelectDialog from "@/views/ni/base/components/MaterialSelectDialog";
import {startFixedAssetConfirm} from "@/api/ni/por/order-arrival";

export default {
  components: {
    StockInForm,
    StockIn,
    UserSelect,
    SupplierMultipleSelect,
    MaterialSelectDialog,
  },
  computed: {
    ...mapGetters(["userInfo"]),
    ids() {
      let ids = new Set();
      this.selectionList.forEach((ele) => {
        ids.add(ele.id);
      });
      return Array.from(ids).join(",");
    },
  },
  data() {
    return {
      allData: false,
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        searchEnter: true,
        tip: false,
        menu: false,
        align: "center",
        tabs: true,
        tabsActive: 1,
        span: 6,
        dialogFullscreen: true,
        menuWidth: 150,
        selection: true,
        reserveSelection: true,
        searchLabelWidth: 110,
        editBtn: false,
        delBtn: false,
        searchIndex: 3,
        searchIcon: true,
        addBtn: false,
        size: "mini",
        searchSize: "mini",
        height: "auto",
        calcHeight: 30,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        dialogClickModal: false,
        column: [
          {
            label: "暂存位置",
            prop: "depotLocation",
            overHidden: true,
            minWidth: 100,
            search: true
          },
          {
            label: "收货描述",
            prop: "receiveRemark",
            overHidden: true,
            minWidth: 100,
          },
          {
            label: "采购人",
            prop: "purchaseUserName",
            minWidth: 80,
            overHidden: true,
          },
          {
            label: "到货单",
            prop: "serialNo",
            searchPlaceholder: " ",
            disabled: true,
            search: true,
            minWidth: 129,
            filters: true,
            sortable: true,
            display: false,
          },
          {
            label: "申购人",
            prop: "applyUserName",
            overHidden: true,
            minWidth: 80,
          },
          {
            label: "申购人",
            prop: "applyUserId",
            remote: true,
            dicUrl: `/api/blade-user/search/otj/user?name={{key}}`,
            props: {
              label: "realName",
              value: "id",
            },
            dicFormatter: (data) => {
              return data.data.records;
            },
            hide: true,
            showColumn: false,
            placeholder: " ",
            filterable: true,
            type: "select",
            overHidden: true,
            minWidth: 80,
            search: true,
          },
          {
            label: "采购申请",
            prop: "applySerialNo",
            slot: true,
            placeholder: " ",
            overHidden: true,
            disabled: true,
            search: true,
            filters: true,
            sortable: true,
            minWidth: 120,
            display: false,
          },
          {
            label: "项目名称",
            prop: "projectTitle",
            overHidden: true,
            minWidth: 120,
          },
          {
            label: "预算名称",
            prop: "budgetTitle",
            overHidden: true,
            minWidth: 120,
          },
          {
            label: "供应商",
            prop: "supplier",
            overHidden: true,
            minWidth: 120,
          },
          {
            label: "供应商",
            prop: "supplierId",
            type: "select",
            hide: true,
            showColumn: false,
            remote: true,
            placeholder: " ",
            dicUrl: `/api/ni/base/supplier/info/page?status=2&keyword={{key}}`,
            props: {
              label: "name",
              value: "id",
              desc: "code",
            },
            dicFormatter: (data) => {
              return data.data.records;
            },
            overHidden: true,
            search: true,
            minWidth: 120,
          },
          {
            label: "序号",
            prop: "rowNo",
            overHidden: true,
            width: 60,
            search: true,
            display: false,
          },
          {
            label: "品名",
            prop: "materialName",
            overHidden: true,
            placeholder: " ",
            search: true,
            display: false,
            disabled: true,
            minWidth: 90,
            searchOrder: 100,
          },
          {
            label: "用途",
            prop: "purpose",
            type: "textarea",
            minRows: 1,
            placeholder: " ",
            overHidden: true,
            minWidth: 80,
          },
          {
            label: "规格",
            prop: "specification",
            slot: true,
            overHidden: true,
            placeholder: " ",
            disabled: true,
            filters: true,
            sortable: true,
            minWidth: 90,
            display: false,
            search: true,
          },
          {
            label: "材质",
            prop: "quality",
            slot: true,
            overHidden: true,
            placeholder: " ",
            disabled: true,
            minWidth: 90,
            display: false,
          },

          {
            label: "待入库数",
            prop: "unStockInNum",
            type: "number",
            controls: false,
            placeholder: " ",
            minWidth: 75,
            display: false,
            search: true,
          },
          {
            label: "单位",
            prop: "unit",
            overHidden: true,
            type: "select",
            placeholder: " ",
            value: "1",
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_material_unit",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            slot: true,
            minWidth: 70,
            display: false,
          },
          {
            label: "单价",
            prop: "price",
            type: "number",
            controls: false,
            precision: 2,
            placeholder: " ",
            disabled: true,
            display: false,
            minWidth: 75,
            hide: true,
          },
          {
            label: "金额",
            prop: "qualifiedAmount",
            type: "number",
            precision: 2,
            controls: false,
            placeholder: " ",
            minWidth: 75,
            display: false,
            hide: true,
          },
          {
            label: "编码",
            placeholder: " ",
            prop: "materialCode",
            overHidden: true,
            clearable: false,
            display: false,
            search: true,
            minWidth: 115,
          },
          {
            label: "国标",
            prop: "gb",
            overHidden: true,
            slot: true,
            placeholder: " ",
            disabled: true,
            minWidth: 90,
            display: false,
          },
          {
            label: "项目编号",
            prop: "projectSerialNo",
            overHidden: true,
            minWidth: 120,
          },
          {
            label: "预算编号",
            prop: "budgetSerialNo",
            overHidden: true,
            minWidth: 120,
          },
          {
            label: "采购订单",
            prop: "orderSerialNo",
            slot: true,
            placeholder: " ",
            overHidden: true,
            disabled: true,
            search: true,
            filters: true,
            sortable: true,
            minWidth: 110,
            display: false,
          },
          {
            label: "到货时间",
            minWidth: 95,
            prop: "arrivalDate",
            type: "date",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            disabled: true,
            sortable: true,
            display: false,
            fixed: "right",
          },
          {
            label: "登记时间",
            prop: "createTime",
            type: "datetime",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
            disabled: true,
            filters: true,
            sortable: true,
            display: false,
            minWidth: 137,
          },
          {
            label: "到货情况",
            prop: "arrival",
            type: "radio",
            labelTip:
              "如果是分多次到货的，再最后一次到货后直接选择全部到货，不必选部分到货再扣减到货数",
            dicData: [
              {
                label: "全部到货",
                value: "3",
              },
              {
                label: "部分到货",
                value: "2",
              },
            ],
            hide: true,
            disabled: true,
            filters: true,
            minWidth: 85,
            display: false,
          },
          {
            label: "验收人员",
            prop: "inspectionUserName",
            editDisplay: false,
            overHidden: true,
            filters: true,
            minWidth: 85,
            display: false,
          },
          {
            label: "验收人员",
            prop: "inspectionUserId",
            viewDisplay: false,
            hide: true,
            showColumn: false,
            rules: [
              {
                required: true,
                message: "请输入",
                trigger: "blur",
              },
            ],
          },
          {
            label: "验收时间",
            prop: "inspectionDate",
            type: "date",
            sortable: true,
            minWidth: 95,
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            rules: [
              {
                required: true,
                message: "请选择验货时间",
                trigger: "blur",
              },
            ],
          },
          {
            label: "验收情况",
            prop: "inspectionRemark",
            overHidden: true,
            type: "textarea",
            minWidth: 100,
            span: 24,
            rules: [
              {
                required: true,
                message: "请输入验收情况",
                trigger: "blur",
              },
            ],
          },
          {
            label: "账套",
            prop: "brand",
            type: "radio",
            minWidth: 90,
            searchPlaceholder: " ",
            value: "1",
            dicUrl: "/api/blade-system/dict-biz/dictionary?code=ni_brand",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            search: true,
            filters: true,
            sortable: true,
            disabled: true,
          },
        ],
        group: [
          {
            label: "到货详情",
            arrow: false,
            column: [
              {
                labelWidth: 0,
                label: "",
                prop: "items",
                type: "dynamic",
                span: 24,
                children: {
                  addBtn: false,
                  delBtn: false,
                  size: "mini",
                  align: "center",
                  headerAlign: "center",
                  column: [
                    {
                      label: "到货单",
                      prop: "serialNo",
                      placeholder: " ",
                      disabled: true,
                    },
                    {
                      label: "采购申请",
                      prop: "applySerialNo",
                      placeholder: " ",
                      disabled: true,
                    },
                    {
                      label: "采购订单",
                      prop: "orderSerialNo",
                      placeholder: " ",
                      disabled: true,
                    },
                    {
                      label: "品名",
                      prop: "materialName",
                      placeholder: " ",
                      disabled: true,
                    },
                    {
                      label: "规格",
                      prop: "specification",
                      slot: true,
                      placeholder: " ",
                      disabled: true,
                    },
                    {
                      label: "材质",
                      prop: "quality",
                      overHidden: true,
                    },
                    {
                      label: "编码",
                      placeholder: " ",
                      prop: "materialCode",
                      clearable: false,
                      disabled: true,
                      rules: [
                        {
                          required: true,
                          message: "请输入",
                          trigger: "blur",
                        },
                      ],
                    },
                    {
                      label: "国标",
                      prop: "gb",
                      slot: true,
                      placeholder: " ",
                      disabled: true,
                    },
                    {
                      label: "单位",
                      prop: "unit",
                      type: "select",
                      disabled: true,
                      dicUrl:
                        "/api/blade-system/dict-biz/dictionary?code=ni_material_unit",
                      props: {
                        label: "dictValue",
                        value: "dictKey",
                      },
                      slot: true,
                    },
                    {
                      label: "申购数",
                      prop: "applyNum",
                      disabled: true,
                      type: "number",
                      controls: false,
                      placeholder: " ",
                    },
                    {
                      label: "到货数",
                      prop: "arrivalNum",
                      type: "number",
                      disabled: true,
                      controls: false,
                      placeholder: " ",
                    },
                    {
                      label: "备注",
                      prop: "remark",
                      type: "textarea",
                      minRows: 1,
                      placeholder: " ",
                    },
                  ],
                },
              },
            ],
          },
        ],
      },
      data: [],
      statusDict: [],
      statusDictKeyValue: {},
      arrivalDict: [],
      arrivalDictKeyValue: {},
      brandDict: [],
      brandDictKeyValue: {},
      depotList: [],
      needMergeArr: ["applySerialNo", "serialNo"], // 有合并项的列
      rowMergeArrs: {}, // 包含需要一个或多个合并项信息的对象
      exportData: [], //存储导出数据
      tempDepotLocation: "",
      tempReceiveRemark: "",
      receive: {
        visible: false,
        option: {
          span: 24,
          size: "mini",
          emptyBtn: false,
          column: [
            {
              label: "暂存位置",
              prop: "depotLocation",
              type: "textarea",
              minRows: 1,
              rules: [
                {
                  required: true,
                  message: "请选择暂存位置",
                  trigger: "blur",
                },
              ],
            },
            {
              label: "收货描述",
              prop: "receiveRemark",
              type: "textarea",
              minRows: 1,
            },
          ],
        },
        form: {},
      },
      unRed: true,
      export: {
        column: [
          {
            label: "采购人",
            prop: "purchaseUserName",
            width: 130,
          },
          {
            label: "申购人",
            prop: "applyUserName",
            width: 100,
          },
          {
            label: "采购申请",
            prop: "applySerialNo",
            width: 100,
          },
          {
            label: "项目名称",
            prop: "projectTitle",
          },
          {
            label: "预算名称",
            prop: "budgetTitle",
            width: 100,
          },
          {
            label: "供应商",
            prop: "supplier",
          },
          {
            label: "暂存位置",
            prop: "depotLocation",
            width: 130,
          },
          {
            label: "暂存备注",
            prop: "receiveRemark",
            width: 130,
          },
          {
            label: "品名",
            prop: "materialName",
          },
          {
            label: "编码",
            prop: "materialCode",
            width: 130,
          },
          {
            label: "待入库数",
            prop: "unStockInNum",
          },
          {
            label: "收货描述",
            prop: "receiveRemark",
          },
          {
            label: "单位",
            prop: "unit",
          },
          {
            label: "单价",
            prop: "price",
          },
          {
            label: "金额",
            prop: "qualifiedAmount",
          },
          {
            label: "规格",
            prop: "specification",
          },
          {
            label: "材质",
            prop: "quality",
          },
          {
            label: "国标",
            prop: "gb",
          },
          {
            label: "登记人员",
            prop: "createUserName",
          },
          {
            label: "到货流水号",
            prop: "serialNo",
          },
          {
            label: "采购订单",
            prop: "orderSerialNo",
          },
          {
            label: "暂存",
            prop: "staging",
          },
          {
            label: "到货时间",
            prop: "arrivalDate",
            type: "date",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
          },
          {
            label: "登记时间",
            prop: "createTime",
            type: "datetime",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
          },
          {
            label: "验收人员",
            prop: "inspectionUserName",
          },
          {
            label: "验收时间",
            prop: "inspectionDate",
          },
          {
            label: "验收情况",
            prop: "inspectionRemark",
          },
        ],
      },
    };
  },
  created() {
    this.$http.get("/api/ni/base/depot/info/list?type=7,8&status=2").then((res) => {
      this.depotList = res.data.data;
    });
  },
  methods: {
    handleSupplierClear() {
      this.form.supplierId = null;
    },
    handleSupplierSubmit(selectList) {
      this.query.supplierId = selectList[0].id;
    },
    handleStockInWithDepotBatch(depotId) {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择要入库的数据");
        return;
      }
      const unChecked = this.selectionList.some((item) => !item.inspection);
      if (unChecked) {
        this.$message.warning("选择数据中存在未验收数据，请先验收后再入库");
        return;
      }
      const brands = new Set();
      this.selectionList.forEach((item) => {
        brands.add(item.brand);
      });
      if (brands.size > 1) {
        this.$message.warning("请选择同一账套下的数据");
        return;
      }
      if (this.selectionList.some((item) => item.faStatus === 1)) {
        this.$message.warning("当前不可入库，存在正在审核的固定资产");
        return;
      }
      this.$confirm(
        "此操作会<span style='color: #F56C6C;font-weight: bold'>根据入库单数量生成多条入库记录</span>，是否继续?",
        "提示",
        {
          dangerouslyUseHTMLString: true,
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      ).then(async () => {
        const res = await stockInByArrivalItems(depotId, 0, this.ids);
        const data = res.data.data;
        this.$alert(
          `生成${data.length}条入库单[<span style="color: #F56C6C;font-weight: bold">${data}</span>]`,
          "操作成功",
          {
            type: "success",
            confirmButtonText: "确定",
            dangerouslyUseHTMLString: true,
            callback: () => {
              this.onLoad(this.page);
            },
          }
        );
      });
    },
    handleStockInWithDepot(depotId) {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择要入库的数据");
        return;
      }
      if (this.selectionList.some((item) => item.faStatus === 1)) {
        this.$message.warning("当前不可入库，存在正在审核的固定资产");
        return;
      }
      const unChecked = this.selectionList.some((item) => !item.inspection);
      if (unChecked) {
        this.$message.warning("选择数据中存在未验收数据，请先验收后再入库");
        return;
      }
      const brands = new Set();
      const supplierIds = new Set();
      const purchaseUserIds = new Set();
      const applyIds = new Set();
      const parentIds = new Set();
      this.selectionList.forEach((item) => {
        brands.add(item.brand);
        supplierIds.add(item.supplierId);
        purchaseUserIds.add(item.purchaseUserId);
        applyIds.add(item.applyId);
        parentIds.add(item.parentId);
      });
      if (brands.size > 1) {
        this.$message.warning("请选择同一账套下的数据");
        return;
      }
      if (supplierIds.size > 1) {
        this.$message.warning("请选择同一供应商下的数据");
        return;
      }
      if (purchaseUserIds.size > 1) {
        this.$message.warning("请选择同一采购人采购的数据");
        return;
      }
      const row = {
        title: dateFormat(new Date(), "yyyy-MM-dd") + "入库单",
        type: "in",
        subType: "101",
        supplier: this.selectionList[0].supplier,
        supplierId: this.selectionList[0].supplierId,
        personId: this.selectionList[0].purchaseUserId,
        personName: this.selectionList[0].purchaseUserName,
        applyId: this.selectionList[0].applyId,
        applySerialNo: this.selectionList[0].applySerialNo,
        applyUserId: this.selectionList[0].applyUserId,
        applyUserName: this.selectionList[0].applyUserName,
        brand: this.selectionList[0].brand,
        keeperId: this.userInfo.user_id,
        depotId,
        opTime: dateNow1(),
        red: false,
        pv: 0,
        status: 2,
        items: [],
      };
      this.selectionList.forEach((item) => {
        row.items.push({
          depotId,
          row: item.row,
          sn: item.row, //批号
          depotLocation: item.depotLocation,
          porApplyUserId: item.applyUserId,
          porApplyId: item.applyId,
          porApplyItemId: item.applyItemId,
          orderId: item.orderId,
          orderItemId: item.orderItemId,
          porOrderArrivalId: item.id,
          projectSerialNo: item.projectSerialNo,
          projectId: item.projectId,
          personId: item.applyUserId,
          personName: item.applyUserName,
          materialId: item.materialId,
          materialCode: item.materialCode,
          materialName: item.materialName,
          materialTypeId: item.materialTypeId,
          specification: item.specification,
          budgetId: item.budgetYearId,
          budgetSerialNo: item.budgetSerialNo,
          budgetYear: item.budgetYear,
          gb: item.gb,
          unit: item.unit,
          num: item.unStockInNum,
          price: (item.qualifiedAmount / item.qualifiedNum).toFixed(2),
          amount: item.qualifiedAmount - item.stockInAmount - item.backAmount,
          remark: item.receiveRemark,
          pv: 0,
          yzdyzb: item.applyType === '21' ? 1 : 0
        });
      });
      this.$refs.stockInFormRef.init(row);
    },
    handleStockIn() {
      this.handleStockInWithDepot(null);
    },
    beforeOpen(done, type) {
      if (["edit", "view"].includes(type)) {
        detail(this.form.id).then((res) => {
          this.form = res.data.data;
          this.form.inspection = true;
          this.form.inspectionUserId = this.userInfo.user_id;
          this.form.inspectionDate = dateFormat(new Date(), "yyyy-MM-dd");
        });
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      const q = {
        ...params,
        ...this.query,
        inspection: true,
        qualified: "1,2,4",
        cost: false,
        pv: 0,
        red: 0
      };
      if (q.purchaseTime != null && q.purchaseTime.length === 2) {
        q.startPurchaseTime = q.purchaseTime[0];
        q.endPurchaseTime = q.purchaseTime[1];
        q.purchaseTime = null;
      }
      unStockInPage(page.currentPage, page.pageSize, q).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.data.forEach((item) => {
          item.rowNo = item.row;
          if (item.no) {
            item.rowNo += "-" + item.no;
          }
          if (item.arrivalNum == null || item.arrivalNum <= 0) {
            item.arrivalState = "3";
          } else if (item.arrivalNum && item.arrivalNum >= item.num) {
            item.arrivalState = "1";
          } else if (item.arrivalNum && item.arrivalNum < item.num) {
            item.arrivalState = "2";
          }
        });
        this.rowMergeArrs = this.rowMergeHandle(this.needMergeArr, this.data);
        this.loading = false;
        this.selectionClear();
      });
    },
    cellStyle({row, column}) {
      if ("purchaseUserName" === column.columnKey && row.crash) {
        return "backgroundColor:#F56C6C";
      }
      if ("materialName" === column.columnKey && row.applyType === '21') {
        return {
          background: 'url(/img/yzdyzb.png) no-repeat left center / contain',
          backgroundColor: "#F56C6C",
          color: "#fff",
        };
      }
    },
    arraySpanMethod({column, rowIndex}) {
      if ("applySerialNo" === column.property) {
        return this.mergeAction("applySerialNo", rowIndex, column);
        // return this.mergeAction("applyUserName", rowIndex, column);
      }
      if ("serialNo" === column.property)
        return this.mergeAction("serialNo", rowIndex, column);
    },
    mergeAction(val, rowIndex) {
      let _row = this.rowMergeArrs[val].rowArr[rowIndex];
      let _col = _row > 0 ? 1 : 0;
      return {
        rowspan: _row,
        colspan: _col,
      };
    },
    rowMergeHandle(arr, data) {
      if (!Array.isArray(arr) && !arr.length) return false;
      if (!Array.isArray(data) && !data.length) return false;
      let needMerge = {};
      arr.forEach((i) => {
        needMerge[i] = {
          rowArr: [],
          rowMergeNum: 0,
        };
        data.forEach((item, index) => {
          if (index === 0) {
            needMerge[i].rowArr.push(1);
            needMerge[i].rowMergeNum = 0;
          } else {
            if (item[i] === data[index - 1][i]) {
              needMerge[i].rowArr[needMerge[i].rowMergeNum] += 1;
              needMerge[i].rowArr.push(0);
            } else {
              needMerge[i].rowArr.push(1);
              needMerge[i].rowMergeNum = index;
            }
          }
        });
      });
      return needMerge;
    },
    //数据导出
    async handleExportData() {
      const q = {
        ...this.param,
        ...this.query,
        inspection: true,
        qualified: "1,2,4",
        cost: false,
        pv: 0,
      };
      if (q.purchaseTime != null && q.purchaseTime.length === 2) {
        q.startPurchaseTime = q.purchaseTime[0];
        q.endPurchaseTime = q.purchaseTime[1];
        q.purchaseTime = null;
      }
      unStockInPage(1, 100000, q).then((res) => {
        const data = res.data.data.records;
        this.$Export.excel({
          title: "待入库明细",
          columns: this.export.column,
          data: data.map((item) => {
            return {
              ...item,
              brand: this.brandDictKeyValue[item.brand],
              staging: item.staging ? "是" : "否",
            };
          }),
        });
      });
    },
    handleMaterialChange() {
      if (this.selectionList.length === 0) {
        this.$message.error("请至少选择一条数据");
        return;
      }
      this.$refs.materialSelectDialogRef.visible = true;
    },
    handleMaterialChangeSubmit(selectList) {
      const material = selectList[0];
      changeMaterial(this.ids, material.id).then(() => {
        this.onLoad(this.page)
        this.$message({
          type: "success",
          message: "操作成功!",
        });
        this.$refs.crud.toggleSelection();
      })
    },
    // 转入固定资产
    handleToFa() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请至少选择一条数据");
        return;
      }
      //部分入库不可发起转入固定资产确认流程
      if (this.selectionList.some((item) => item.qualified === "2")) {
        this.$message.warning("部分合格的数据不可转入固定资产");
        return;
      }
      if (this.selectionList.some((item) => item.faStatus === 1)) {
        this.$message.warning("存在正在审核的固定资产");
        return;
      }
      this.$confirm(
        "确认将选择的数据转入固定资产[<span style='font-weight:bold;color:red'>将会发起固定资产确认流程</span>]？",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          dangerouslyUseHTMLString: true,
          type: "warning",
        }
      )
        .then(() => {
          //先把物品标记为固定资产
          const fa = true;
          const ids = this.selectionList
            .map((item) => item.applyItemId)
            .join(",");
          changeFa(ids, fa)
            .then(() => {
              //手动更新selectionList中的fa字段
              this.selectionList.forEach((item) => {
                item.fa = true;
              });
              //判断是否有固定资产
              const faItems = this.selectionList.filter(item => item.fa === true && (!item.inFa || item.inFa == null));
              if (faItems.length > 0) {
                // 如果存在固定资产，只针对固定资产发起流程
                this.handleStartProcess(faItems);
              }
            })
        });
    },
    handleStartProcess(faItems) {
      const items = faItems.map((item => {
        let price;
        if (item.price != null && !isNaN(Number.parseFloat(item.price))) {
          price = Number.parseFloat(item.price).toFixed(2);
        } else if (item.qualifiedAmount != null && Number(item.qualifiedNum) > 0) {
          price = (Number.parseFloat(item.qualifiedAmount) / Number.parseFloat(item.qualifiedNum)).toFixed(2);
        } else {
          price = "0.00";
        }
        return {
          ...item,
          brandName: item.$brand,
          pvName: item.pv ? "是" : "否",
          qualifiedNum: Number.parseFloat(item.unStockInNum),
          num: Number.parseFloat(item.unStockInNum),
          arrivalNum: Number.parseFloat(item.unStockInNum),
          arrivalAmount: Number.parseFloat(item.unStockInNum) * Number.parseFloat(price),
          amount: Number.parseFloat(item.unStockInNum) * Number.parseFloat(item.unStockInNum),
          price: Number(price),
          projectSerialNo: item.projectSerialNo != null ? item.projectSerialNo : item.budgetSerialNo,
        }
      }))
      startFixedAssetConfirm(items).then(() => {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "发起流程成功",
        });
      });
    },
    handle2Pv() {
      if (this.selectionList.length === 0) {
        this.$message.error("请至少选择一条数据");
        return;
      }
      this.$confirm(
        "确认将选择的数据转入压力容器[<span style='font-weight:bold;color:red'>将会修改数据的状态为压力容器</span>]？",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          dangerouslyUseHTMLString: true,
          type: "warning",
        }
      )
        .then(() => {
          const ids = this.selectionList
            .map((item) => item.applyItemId)
            .join(",");
          return changePv(ids, 1);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    handleExport() {
      this.$confirm("是否导出当前筛选的所有数据？", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.handleExportData();
      });
    },
    showTempDepotLocation(row) {
      this.$nextTick(() => {
        this.tempDepotLocation = row.depotLocation;
        this.$refs[`tempDepotLocationRef-${row.id}`].focus();
      });
    },
    showTempReceiveRemark(row) {
      this.$nextTick(() => {
        this.tempReceiveRemark = row.receiveRemark;
        this.$refs[`tempReceiveRemarkRef-${row.id}`].focus();
      });
    },
    rowReceiveRemarkClose(row) {
      document.body.click();
      this.$refs[`receive-remark-popover-${row.id}`].doClose();
    },
    rowReceiveRemarkChange(row, receiveRemark) {
      document.body.click();
      this.$refs[`receive-remark-popover-${row.id}`].doClose();
      const data = [
        {
          type: "orderArrival",
          id: row.id,
          receiveRemark: receiveRemark,
        },
      ];
      receive(data).then(() => {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!",
        });
      });
    },
    rowDepotLocationClose(row) {
      document.body.click();
      this.$refs[`depot-popover-${row.id}`].doClose();
    },
    rowDepotLocationChange(row, depotLocation) {
      document.body.click();
      this.$refs[`depot-popover-${row.id}`].doClose();
      const data = [
        {
          type: "orderArrival",
          id: row.id,
          depotLocation: depotLocation,
        },
      ];
      receive(data).then(() => {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!",
        });
      });
    },
    handleReject() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$prompt("确定要要选择的数据驳回?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        inputPlaceholder: "请输入驳回原因",
        inputErrorMessage: "驳回原因",
        inputValidator: (value) => {
          if (!value.trim()) {
            return "驳回原因不能为空";
          }
        },
      })
        .then((value) => {
          return reject(this.ids, value);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    handleArrival() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.receive.visible = true;
    },
    handleReceiveSubmit(form, done) {
      const data = this.selectionList.map((item) => {
        return {
          type: "orderArrival",
          id: item.id,
          depotLocation: form.depotLocation,
          receiveRemark: form.receiveRemark,
        };
      });
      receive(data)
        .then(() => {
          this.receive.visible = false;
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        })
        .finally(() => {
          done();
        });
    },
  },
};
</script>

<style></style>

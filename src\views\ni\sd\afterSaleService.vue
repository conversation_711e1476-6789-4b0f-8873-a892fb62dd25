<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page.sync="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @refresh-change="refreshChange"
               @on-load="onLoad">
      <template slot="menuLeft">
        <el-button type="danger"
                   size="small"
                   icon="el-icon-delete"
                   plain
                   v-if="permission.afterSaleService_delete"
                   @click="handleDelete">删 除
        </el-button>
        <el-button
          size="mini"
          icon="el-icon-download"
          type="success"
          v-if="permission.customerComplaint_export"
          @click="handleExport"
        >导出
        </el-button>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
  import {getList, getDetail, add, update, remove} from "@/api/ni/sd/afterSaleService";
  import {mapGetters} from "vuex";

  export default {
    data() {
      return {
        form: {},
        query: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        option: {
          align: 'center',
          dialogFullscreen: true,
          searchEnter: true,
          size: 'mini',
          labelWidth: 190,
          menuWidth: 200,
          height:'auto',
          calcHeight: 30,
          tip: false,
          searchShow: true,
          searchMenuSpan: 6,
          border: true,
          index: false,
          viewBtn: true,
          selection: true,
          dialogClickModal: false,
          searchSize: 'mini',
          span: 6,
          searchIndex: 3,
          searchIcon: true,
          column: [
            // {
            //   label: "状态",
            //   prop: "status",
            //   type: "input",
            // },
            {
              label: "客户名称",
              prop: "customerName",
              type: "input",
              search: true,
            },
            {
              label: "客户地址",
              prop: "customerAddress",
              type: "input",
            },
            {
              label: "业务经理",
              prop: "businessManager",
              type: "input",
              search: true,
            },
            {
              label: "到达时间",
              prop: "arrivalDate",
              type: "input",
            },
            {
              label: "客户联系人",
              prop: "customerContact",
              type: "input",
              search: true,
            },
            {
              label: "职务",
              prop: "position",
              type: "input",
            },
            {
              label: "电话",
              prop: "phone",
              type: "input",
            },
            {
              label: "现场问题",
              prop: "onSiteProblem",
              type: "input",
            },
            {
              label: "问题产品出现的频次",
              prop: "problemProductFrequency",
              type: "input",
            },
            {
              label: "问题产品尺寸",
              prop: "problemProductSize",
              type: "input",
            },
            {
              label: "问题产品尺寸附图",
              prop: "problemProductSizePic",
              type: "upload",
            },
            {
              label: "产品用途",
              prop: "productUsage",
              type: "input",
            },
            {
              label: "是否充氩气",
              prop: "isArgonFilled",
              type: "input",
            },
            {
              label: "充氩气方式",
              prop: "argonFillingMethod",
              type: "input",
            },
            {
              label: "充氩气位置",
              prop: "argonFillingPosition",
              type: "input",
            },
            {
              label: "排气孔位置",
              prop: "exhaustHolePosition",
              type: "input",
            },
            {
              label: "充气压力",
              prop: "inflationPressure",
              type: "input",
            },
            {
              label: "漏粉位置",
              prop: "powderLeakPosition",
              type: "input",
            },
            {
              label: "插件处插件样式",
              prop: "powderLeakPositionPluginType",
              type: "input",
            },
            {
              label: "插件处插件间隙",
              prop: "powderLeakPositionPluginGap",
              type: "input",
            },
            {
              label: "插件处附图",
              prop: "powderLeakPositionPluginPic",
              type: "upload",
            },
            {
              label: "折弯处透气孔是否拉大",
              prop: "powderLeakPositionBendVentEnlarged",
              type: "input",
            },
            {
              label: "折弯处拉大程度",
              prop: "powderLeakPositionBendVentEnlargedDegree",
              type: "input",
            },
            {
              label: "折弯处附图",
              prop: "powderLeakPositionBendPic",
              type: "upload",
            },
            {
              label: "间隔条透气孔尺寸",
              prop: "powderLeakPositionIntervalVentSize",
              type: "input",
            },
            {
              label: "间隔条透气孔附图",
              prop: "powderLeakPositionIntervalVentPic",
              type: "upload",
            },
            {
              label: "冲氩气处附图",
              prop: "powderLeakPositionArgonPic",
              type: "upload",
            },
            {
              label: "其它附图",
              prop: "powderLeakPositionOtherPic",
              type: "upload",
            },
            {
              label: "漏粉的形状",
              prop: "powderLeakShape",
              type: "input",
            },
            {
              label: "漏粉形状附图",
              prop: "powderLeakShapePic",
              type: "upload",
            },
            {
              label: "产品批号",
              prop: "productBatchNumber",
              type: "input",
              search: true,
            },
            {
              label: "产品规格",
              prop: "productSpecification",
              type: "input",
            },
            {
              label: "产品规格附图",
              prop: "productSpecificationPic",
              type: "upload",
            },
            {
              label: "新开封产品袋子或桶底部是否有渣子",
              prop: "bottomSlag",
              type: "input",
            },
            {
              label: "新开封产品袋子或桶底部渣子数量",
              prop: "bottomSlagQuantity",
              type: "input",
            },
            {
              label: "新开封产品袋子或桶底部渣子附图",
              prop: "bottomSlagPic",
              type: "upload",
            },
            {
              label: "新开封产品手攥是否有渣子",
              prop: "handGraspSlag",
              type: "input",
            },
            {
              label: "新开封产品手攥渣子数量",
              prop: "handGraspSlagQuantity",
              type: "input",
            },
            {
              label: "新开封产品手攥渣子附图",
              prop: "handGraspSlagPic",
              type: "upload",
            },
            {
              label: "新开封产品标准筛是否有渣子",
              prop: "standardSieveSlag",
              type: "input",
            },
            {
              label: "新开封产品标准筛渣子数量",
              prop: "standardSieveSlagQuantity",
              type: "input",
            },
            {
              label: "新开封产品标准筛渣子附图",
              prop: "standardSieveSlagPic",
              type: "upload",
            },
            {
              label: "灌装机品牌",
              prop: "fillingMachineBrand",
              type: "input",
            },
            {
              label: "出厂时间",
              prop: "manufactureDate",
              type: "input",
            },
            {
              label: "上料方式",
              prop: "feedingMethod",
              type: "input",
            },
            {
              label: "真空泵空滤情况",
              prop: "vacuumPumpFilter",
              type: "input",
            },
            {
              label: "真空泵空滤附图",
              prop: "vacuumPumpFilterPic",
              type: "upload",
            },
            {
              label: "输送器是否完好",
              prop: "conveyorCondition",
              type: "input",
            },
            {
              label: "输送器附图",
              prop: "conveyorPic",
              type: "upload",
            },
            {
              label: "存料方式",
              prop: "storageMethod",
              type: "input",
            },
            {
              label: "铁桶/储料罐底部是否有渣子或粉尘",
              prop: "bottomSlagOrDust",
              type: "input",
            },
            {
              label: "铁桶/储料罐底部是否已清理",
              prop: "cleaned",
              type: "input",
            },
            {
              label: "铁桶/储料罐底部清理周期",
              prop: "cleaningCycle",
              type: "input",
            },
            {
              label: "铁桶/储料罐底部附图",
              prop: "storageBottomPic",
              type: "upload",
            },
            {
              label: "缓存罐下料方式",
              prop: "dischargingMethod",
              type: "input",
            },
            {
              label: "是否将直吹改为非直吹",
              prop: "dischargingMethodChanged",
              type: "input",
            },
            {
              label: "缓存罐下料附图",
              prop: "dischargingPic",
              type: "upload",
            },
            {
              label: "缓存罐底部是否有渣子或粉尘",
              prop: "bufferBottomSlagOrDust",
              type: "input",
            },
            {
              label: "缓存罐是否已清理",
              prop: "bufferCleaned",
              type: "input",
            },
            {
              label: "缓存罐清理周期",
              prop: "bufferCleaningCycle",
              type: "input",
            },
            {
              label: "缓存罐相关附图",
              prop: "bufferPic",
              type: "upload",
            },
            {
              label: "创建人",
              prop: "createUser",
              type: "tree",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              dicUrl:`/api/blade-user/user-list`,
              props: {
                label: "name",
                value: "id",
              },
            },
            {
              label: "创建部门",
              prop: "createDept",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              type: "tree",
              dicUrl:`/api/blade-system/dept/list`,
              props: {
                label: "deptName",
                value: "id"
              },
            },
            {
              label: "创建时间",
              prop: "createTime",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
            },
          ]
        }
        ,
        data: []
      };
    },
    computed: {
      ...mapGetters(["permission"]),
      permissionList() {
        return {
          addBtn: this.vaildData(this.permission.afterSaleService_add, false),
          viewBtn: this.vaildData(this.permission.afterSaleService_view, false),
          delBtn: this.vaildData(this.permission.afterSaleService_delete, false),
          editBtn: this.vaildData(this.permission.afterSaleService_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    methods: {
      handleExport() {
        let msg =
          '是否导出<span style="color: #F56C6C;font-weight: bold">所有数据</span>?';
        if (this.selectionList.length > 0) {
          msg =
            '是否要导出<span style="color: #F56C6C;font-weight: bold">当前选中的数据</span>？';
        }
        this.$confirm(msg, {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          dangerouslyUseHTMLString: true,
          type: "warning",
        }).then(() => {
          this.handleExportData();
        });
      },
      // 数据导出
      async handleExportData() {
        let opt = {
          column: [
            {
              label: "客户名称",
              prop: "customerName",
            },
            {
              label: "客户地址",
              prop: "customerAddress",
            },
            {
              label: "业务经理",
              prop: "businessManager",
            },
            {
              label: "到达日期",
              prop: "arrivalDate",
            },
            {
              label: "客户联系人",
              prop: "customerContact",
            },
            {
              label: "职务",
              prop: "position",
            },
            {
              label: "电话",
              prop: "phone",
            },
            {
              label: "现场问题",
              prop: "onSiteProblem",
            },
            {
              label: "问题产品出现频率",
              prop: "problemProductFrequency",
            },
            {
              label: "问题产品尺寸",
              prop: "problemProductSize",
            },
            {
              label: "问题产品尺寸附图",
              prop: "problemProductSizePic",
            },
            {
              label: "产品用途",
              prop: "productUsage",
            },
            {
              label: "是否充氩气",
              prop: "isArgonFilled",
            },
            {
              label: "氩气填充方法",
              prop: "argonFillingMethod",
            },
            {
              label: "氩气填充位置",
              prop: "argonFillingPosition",
            },
            {
              label: "排气孔位置",
              prop: "exhaustHolePosition",
            },
            {
              label: "充气压力",
              prop: "inflationPressure",
            },
            {
              label: "粉末泄漏位置",
              prop: "powderLeakPosition",
            },
            {
              label: "插件类型",
              prop: "powderLeakPositionPluginType",
            },
            {
              label: "插件间隙",
              prop: "powderLeakPositionPluginGap",
            },
            {
              label: "插件的附图",
              prop: "powderLeakPositionPluginPic",
            },
            {
              label: "折弯处通风口是否扩大",
              prop: "powderLeakPositionBendVentEnlarged",
            },
            {
              label: "折弯处通风口扩大程度",
              prop: "powderLeakPositionBendVentEnlargedDegree",
            },
            {
              label: "折弯处的附图",
              prop: "powderLeakPositionBendPic",
            },
            {
              label: "间隔条通气孔尺寸",
              prop: "powderLeakPositionIntervalVentSize",
            },
            {
              label: "间隔条通气孔附图",
              prop: "problemProductSize",
            },
            {
              label: "充氩气处附图",
              prop: "powderLeakPositionArgonPic",
            },
            {
              label: "其他附图",
              prop: "powderLeakPositionOtherPic",
            },
            {
              label: "漏粉形状",
              prop: "powderLeakShape",
            },
            {
              label: "漏粉形状附图",
              prop: "powderLeakShapePic",
            },
            {
              label: "产品批号",
              prop: "productBatchNumber",
            },
            {
              label: "产品规格",
              prop: "productSpecification",
            },
            {
              label: "产品规格附图",
              prop: "productSpecificationPic",
            },
            {
              label: "新开封产品袋子或桶底部是否有渣子",
              prop: "bottomSlag",
            },
            {
              label: "新开封产品袋子或桶底部渣子数量",
              prop: "bottomSlagQuantity",
            },
            {
              label: "新开封产品袋子或桶底部附图",
              prop: "bottomSlagPic",
            },
            {
              label: "新开封产品手攥是否有渣子",
              prop: "handGraspSlag",
            },
            {
              label: "新开封产品手攥渣子数量",
              prop: "handGraspSlagQuantity",
            },
            {
              label: "新开封产品手攥附图",
              prop: "handGraspSlagPic",
            },
            {
              label: "新开封产品标准筛是否有渣子",
              prop: "standardSieveSlag",
            },
            {
              label: "新开封产品标准筛渣子数量",
              prop: "standardSieveSlagQuantity",
            },
            {
              label: "新开封产品标准筛附图",
              prop: "standardSieveSlagQuantity",
            },
            {
              label: "灌装机品牌",
              prop: "fillingMachineBrand",
            },
            {
              label: "出厂日期",
              prop: "manufactureDate",
            },
            {
              label: "上料方式",
              prop: "feedingMethod",
            },
            {
              label: "真空泵空滤",
              prop: "vacuumPumpFilter",
            },
            {
              label: "真空泵空滤附图",
              prop: "vacuumPumpFilterPic",
            },
            {
              label: "输送器是否完好",
              prop: "conveyorCondition",
            },
            {
              label: "输送器附图",
              prop: "conveyorPic",
            },            {
              label: "存料方式",
              prop: "storageMethod",
            },            {
              label: "底部是否有渣子或粉尘",
              prop: "bottomSlagOrDust",
            },            {
              label: "是否已清理",
              prop: "cleaned",
            },
            {
              label: "清理周期",
              prop: "cleaningCycle",
            },
            {
              label: "底部附图",
              prop: "storageBottomPic",
            },
            {
              label: "缓存罐下料方式",
              prop: "dischargingMethod",
            },
            {
              label: "是否将直吹改为非直吹",
              prop: "dischargingMethodChanged",
            },            {
              label: "缓存罐下料方式附图",
              prop: "dischargingPic",
            },
            {
              label: "缓存罐底部是否有渣子或粉尘",
              prop: "bufferBottomSlagOrDust",
            },
            {
              label: "缓存罐底部是否已清理",
              prop: "bufferCleaned",
            },
            {
              label: "缓存罐底部清理周期",
              prop: "bufferCleaningCycle",
            },
            {
              label: "缓存罐底部附图",
              prop: "bufferPic",
            },
          ],
        };

        if (this.selectionList.length > 0) {
          this.exportData = this.selectionList;
        }else{
          await this.getExportData();
        }

        this.$Export.excel({
          title: "售后服务记录",
          columns: opt.column,
          data: this.exportData.map((item) => {
            return {
              ...item,
              // statusName: this.statusDictKeyValue[item.status],
              // inspectorName: this.expressTypeDictKeyValue[item.inspector],
              // expressCompanyName: this.expressCompanyDictKeyValue[item.expressCompany],
              // paymentTypeDictName: this.paymentTypeDictKeyValue[item.paymentType],
              // expressTypeName: this.expressTypeDictKeyValue[item.expressType],
            };
          }),
        });
        this.exportData = [];
      },
      //获取搜索的打印数据
      async getExportData() {
        const promises = [];
        this.exportData = [];
        for (var i = 1; i <= this.page.total / this.page.pageSize + 1; i++) {
          const promise = getList(i, this.page.pageSize, {
            ...this.params,
            ...this.query,
          }).then((res) => {
            const data = res.data.data.records;
            this.exportData = this.exportData.concat(data);
          });

          promises.push(promise);
        }
        // 等待所有异步请求完成
        await Promise.all(promises);
        // console.log(this.exportData)
        return this.exportData;
      },

      rowSave(row, done, loading) {
        add(row).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          loading();
          window.console.log(error);
        });
      },
      rowUpdate(row, index, done, loading) {
        update(row).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          loading();
          console.log(error);
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1;
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      refreshChange() {
        this.onLoad(this.page, this.query);
      },
      onLoad(page, params = {}) {
        this.loading = true;
        getList(page.currentPage, page.pageSize, Object.assign(params, this.query,{descs:'id'})).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      }
    }
  };
</script>

<style>
</style>

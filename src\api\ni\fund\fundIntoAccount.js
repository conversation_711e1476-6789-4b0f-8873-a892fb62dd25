import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/fund/fundIntoAccount/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/fund/fundIntoAccount/info',
    method: 'get',
    params: {
      id
    }
  })
}

export const confirmImport = (data) => {
  return request({
    url: '/api/fund/fundIntoAccount/confirmImport',
    method: 'post',
    data
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/fundIntoAccount/niFundIntoAccount/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/fundIntoAccount/niFundIntoAccount/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/fundIntoAccount/niFundIntoAccount/submit',
    method: 'post',
    data: row
  })
}




export const stopCalculatingInterest = (intoAccountId) => {
  return request({
    url: '/api/fund/fundIntoAccount/stopCalculatingInterest',
    method: 'post',
    params: {
      intoAccountId
    }
  })
}



export const getTotalSumActualIncomeAmount = () => {
  return request({
    url: '/api/fund/fundIntoAccount/getTotalSumActualIncomeAmount',
    method: 'get'
  })
}





export const getAccountBaseCurrentBalance = (params) => {
  return request({
    url: '/api/fund/fundIntoAccount/getAccountBaseCurrentBalance',
    method: 'get',
    params
  })
}

export const batchWithdrawAllInterest = () => {
  return request({
    url: '/api/fund/fundIntoAccount/batchWithdrawAllInterest',
    method: 'post'
  })
}

export const previewWithdrawInterest = (params) => {
  return request({
    url: '/api/fund/fundIntoAccount/previewWithdrawInterest',
    method: 'get',
    params
  })
}
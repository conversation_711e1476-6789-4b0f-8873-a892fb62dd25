<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :search.sync="query"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
      :row-style="rowStyle"
    >
      <template slot-scope="{ row, label, size }" slot="status">
        <el-tag :size="size" :type="rowTagType(row.status)">{{ label }}</el-tag>
      </template>
      <template #overtimeTotal="{ row }">
        <span v-if="row.overtimeTotal">{{ row.overtimeTotal }} 小时</span>
        <span v-else> 无</span>
      </template>
      <template #isNewEmployee="{ row }">
        <span>{{ row.isNewEmployee ? '是' : '否' }}</span>
      </template>
      <template #entryTime="{ row }">
        <span>{{ row.entryTime ? new Date(row.entryTime).toLocaleDateString() : '无' }}</span>
      </template>
      <template #menuLeft>
        <el-button
          type="success"
          size="mini"
          icon="el-icon-download"
          plain
          @click="handleExport"
        >导出
        </el-button>
        <el-button
          type="warning"
          size="mini"
          icon="el-icon-refresh"
          plain
          @click="handleRenewAttInfo"
        >更新数据
        </el-button>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import {
  getAttInfo,
  renewAttInfo,
} from "@/api/ni/pa/attendance/paOvertimeApplyRecord";
import { getPage, changeDeduction } from "@/api/ni/pa/attendance/paWorkSummary";
import { mapGetters } from "vuex";
import { dateFormat } from "@/util/date";

export default {
  data() {
    return {
      form: {},
      query: {},
      loading: false,
      page: {
        pageSize: 200,
        pageSizes: [200, 300],
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        excelBtn: true,
        size: "mini",
        searchSize: "mini",
        searchShow: true,
        searchEnter: true,
        searchIcon: true,
        searchSpan: 6,
        searchBtn: true,
        searchMenuSpan: 6,
        searchIndex: 3,
        emptyBtn: true,
        columnBtn: true,
        searchShowBtn: true,
        filterBtn: false,
        refreshBtn: true,
        align: "center",
        height: "auto",
        calcHeight: 30,
        rowKey: "id",
        border: true,
        tip: false,
        selection: true,
        selectionFixed: false,
        index: true,
        indexFixed: false,
        indexLabel: "序号",
        menu: true,
        menuFixed: false,
        menuAlign: "center",
        addBtn: false,
        editBtn: false,
        viewBtn: true,
        delBtn: false,
        dialogClickModal: false,
        dialogModal: true,
        dialogType: "dialog",
        saveBtn: true,
        cancelBtn: true,
        showSummary: true,
        sumColumnList: [
          { name: "totalAmount", type: "sum" },
          { name: "totalDays", type: "sum" },
          { name: "deduction", type: "sum" },
        ],
        column: [
          {
            label: "月份",
            prop: "month",
            type: "month",
            format: "yyyy-MM",
            valueFormat: "yyyy-MM",
            display: false,
            search: true,
            hide: true,
            searchValue: dateFormat(new Date(), "yyyy-MM"),
          },
          {
            label: "用户",
            prop: "userIds",
            type: "select",
            remote: true,
            dicUrl: `/api/blade-user/search/otj/user?name={{key}}`,
            multiple: true,
            collapseTags: true,
            display: false,
            props: {
              label: "realName",
              value: "id",
            },
            dicFormatter: (data) => {
              return data.data.records;
            },
            placeholder: " ",
            filterable: true,
            hide: true,
            showColumn: false,
            search: true,
          },
          {
            label: "班组",
            prop: "deptIds",
            type: "tree",
            dicUrl: `/api/ni/pa/work/summary/dept/tree`,
            props: {
              label: "deptName",
              value: "id",
            },
            hide: true,
            display: false,
            showColumn: false,
            search: true,
            multiple: true,
            collapseTags: true,
          },
          {
            label: "姓名",
            prop: "userName",
          },
          {
            label: "部门",
            prop: "deptName",
          },
          {
            label: "加班时长",
            prop: "overtimeTotal",
          },
          {
            label: "加班工时",
            prop: "overTimeDays",
          },
          {
            label: "考勤工时",
            prop: "attDays",
          },
          {
            label: "考核工时",
            prop: "totalDays",
          },
          {
            label: "是否新员工",
            prop: "isNewEmployee",
            dictData:[
              {
                label: '是',
                value: true
              }, {
                label: '否',
                value: false
              }
            ],
            hide: true,
          },
          {
            label: "入职时间",
            prop: "entryTime",
            hide: true,
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyy-MM-dd',
          },
        ],
      },
      data: [],
      exportData: [],
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.attRecord_add, false),
        viewBtn: this.vaildData(this.permission.attRecord_view, false),
        delBtn: this.vaildData(this.permission.attRecord_delete, false),
        editBtn: this.vaildData(this.permission.attRecord_edit, false),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  methods: {
    rowStyle({ row }) {
      if (row.isNewEmployee) {
        return {
          backgroundColor: "#ffff66",
        };
      }
    },
    deductionChange(row) {
      changeDeduction(row.id, row.deduction).then(() => {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "修改成功!",
        });
      });
    },
    handleRenewAttInfo() {
      this.$confirm("是否要更新" + `${this.query.month}` + "月份的数据？", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        const params = {};
        const query = Object.assign(params, this.query);
        if (query.month == null) {
          query.month = dateFormat(new Date(), "yyyy-MM");
        }
        renewAttInfo(query).then(() => {
          this.onLoad(this.page);
        });
      });
    },
    //数据导出
    handleExport() {
      this.$confirm("是否导出当前筛选的所有数据？", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.handleExportData();
      });
    },
    // 导出数据处理
    async handleExportData() {
      let opt = {
        column: [
          {
            label: `${this.query.month}` + "考核汇总",
            prop: "headerTitle",
            showSummary: true,
            sumColumnList: [
              {
                name: "totalDays",
                type: "sum",
              },
              {
                name: "totalAmount",
                type: "sum",
              },
            ],
            align: "center",
            headerAlign: "center",
            children: [
              {
                label: "姓名",
                prop: "userName",
                headerStyle: {
                  font: { bold: true },
                  alignment: { horizontal: "center" },
                },
              },
              {
                label: "考核工时",
                prop: "totalDays",
              },
              {
                label: "考核金额",
                prop: "totalAmount",
              },
              {
                label: "扣款",
                prop: "deduction",
              },
              {
                label: "备注",
                prop: "remark",
              },
            ],
          },
        ],
      };
      await this.getExportData().then(() => {
        this.$Export.excel({
          title: `${this.query.month}` + "考核汇总",
          columns: opt.column,
          data: this.exportData.map((item) => {
            return {
              ...item,
            };
          }),
          titleStyle: {
            font: { size: 16, bold: true },
            alignment: { horizontal: "center" },
          },
        });
        this.exportData = [];
      });
    },
    //获取搜索的打印数据
    async getExportData() {
      //每次获取必须初始化一下，不然会把之前缓存额数据一并带入
      this.exportData = [];
      const query = { ...this.params, ...this.query };
      await getPage(1, 10000, query).then((res) => {
        this.exportData = res.data.data;
      });
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      const query = Object.assign(params, this.query);
      if (query.month == null) {
        query.month = dateFormat(new Date(), "yyyy-MM");
      }
      getPage(page.currentPage, page.pageSize, query).then((res) => {
        const data = res.data.data;
        this.page.total = data.total || data.length;
        this.data = data.records || data;
        this.loading = false;
        this.selectionClear();
      });
    },
  },
};
</script>

<style></style>

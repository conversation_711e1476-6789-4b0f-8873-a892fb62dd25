import request from "@/router/axios";

export const getPage = (current, size, params) => {
  return request({
    url: "/api/ni/fin/payable/page",
    method: "get",
    params: {
      ...params,
      type: "1",
      current,
      size,
    },
  });
};
export const getList = (params) => {
  return request({
    url: "/api/ni/fin/payable/list",
    method: "get",
    params
  });
};
export const getDetail = (id) => {
  return request({
    url: "/api/ni/fin/payable/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const remove = (ids) => {
  return request({
    url: "/api/ni/fin/payable/remove",
    method: "post",
    params: {
      ids,
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/ni/fin/payable/save",
    method: "post",
    data: row,
  });
};

export const update = (row) => {
  return request({
    url: "/api/ni/fin/payable/update",
    method: "post",
    data: row,
  });
};

export const getSupplierPayable = (supplierId) => {
  return request({
    url: "/api/ni/fin/payable/supplier",
    method: "get",
    params: {
      supplierId,
    },
  });
};

export const submit = (ids) => {
  return request({
    url: "/api/ni/fin/payable/submit",
    method: "post",
    params: {
      ids,
    },
  });
};

export const toVoid = (ids) => {
  return request({
    url: "/api/ni/fin/payable/toVoid",
    method: "post",
    params: {
      ids,
    },
  });
};

export const back = (params) => {
  return request({
    url: "/api/ni/fin/payable/back",
    method: "post",
    params,
  });
};

export const sync = (id, isCovered = false) => {
  return request({
    url: "/api/ni/fin/payable/sync",
    method: "post",
    params: {
      id,
      isCovered,
    },
  });
};
export const getAliPayPage = (current, size, params) => {
  return request({
    url: "/api/ni/fin/payable/alipayPage",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};
export const trans2Voucher = (row) => {
  return request({
    url: "/api/ni/fin/payable/trans2Voucher",
    method: "post",
    data: row,
  });
};

export const trans2Pay = (id) => {
  return request({
    url: "/api/ni/fin/payable/trans2Pay",
    method: "post",
    params: {
      id,
    },
  });
};
export const syncRange = (startDate, endDate, voucher) => {
  return request({
    url: "/api/ni/fin/payable/syncRange",
    method: "post",
    params: {
      voucher,
      startDate,
      endDate,
    },
  });
};
export const syncOughtPay = (id, itemIds) => {
  return request({
    url: "/api/ni/fin/payable/syncOughtPay",
    method: "post",
    params: {
      id,
      itemIds,
    },
  });
};
export const changeSource = (id, source) => {
  return request({
    url: "/api/ni/fin/payable/changeSource",
    method: "post",
    params: {
      id,
      source,
    },
  });
};

export const getWithApplyPage = (current, size, params) => {
  return request({
    url: "/api/ni/fin/payable/pageWithApply",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const changeLedger = (id,ledgerId)=>{
  return request({
    url: "/api/ni/fin/payable/changeLedger",
    method: "post",
    params: {
      id,
      ledgerId,
    },
  });
}

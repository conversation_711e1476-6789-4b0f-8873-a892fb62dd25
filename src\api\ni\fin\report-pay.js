import request from "@/router/axios";

export const getPage = (current, size, params) => {
  return request({
    url: "/api/ni/fin/report/pay/page",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getPayableAmount = (params) => {
  return request({
    url: "/api/ni/fin/report/getPayableAmount",
    method: "get",
    params,
  });
};

export const getPayableSupplierTotal = (params) => {
  return request({
    url: "/api/ni/fin/report/getPayableSupplierTotal",
    method: "get",
    params,
  });
};

export const getSoaAmount = (params) => {
  return request({
    url: "/api/ni/fin/report/getSoaAmount",
    method: "get",
    params,
  });
};

export const getPaymentApplyAmount = (params) => {
  return request({
    url: "/api/ni/fin/report/getPaymentApplyAmount",
    method: "get",
    params,
  });
};

export const getPaymentAmount = (params) => {
  return request({
    url: "/api/ni/fin/report/getPaymentAmount",
    method: "get",
    params,
  });
};
export const getPaymentSupplierTotal = (params) => {
  return request({
    url: "/api/ni/fin/report/getPaymentSupplierTotal",
    method: "get",
    params,
  });
};

export const getPayableLineData = (params) => {
  return request({
    url: "/api/ni/fin/report/payableLineData",
    method: "get",
    params,
  });
};
export const getPaymentLineData = (params) => {
  return request({
    url: "/api/ni/fin/report/paymentLineData",
    method: "get",
    params,
  });
};

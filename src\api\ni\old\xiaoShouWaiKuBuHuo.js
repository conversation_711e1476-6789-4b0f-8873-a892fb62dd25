import request from '@/router/axios';


export const getDetail = (id) => {
  return request({
    url: '/api/ni/old/xiaoShouWaiKuBuHuo/detail',
    method: 'get',
    params: {
      id
    }
  })
}
export const getShippingList = (current, size, params) => {
  return request({
    url: '/api/ni/old/xiaoShouWaiKuBuHuo/shippingList',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const list = (current, size, params) => {
  return request({
    url: '/api/ni/old/xiaoShouWaiKuBuHuo/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}


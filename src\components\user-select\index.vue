<template>
  <div>
    <el-input
      :type="multiple ? 'textarea' : ''"
      v-model="personName"
      autosize
      :size="size"
      :disabled="disabled"
      suffix-icon="el-icon-search"
      clearable
      @focus="bpFocus"
      @clear="handleClear"
    />
    <el-dialog
      ref="us-dialog"
      v-dialogdrag
      custom-class="us-dialog"
      :visible.sync="visible"
      title="人员选择"
      width="60%"
      :before-close="handleClose"
      append-to-body
    >
      <template #title>
        人员选择
        <el-divider direction="vertical"/>
        <el-tag>
          当前表格已选择
          <span style="font-weight: bolder">{{ selectionList.length }}</span> 项
          <el-button type="text" size="mini" @click="selectionClear">
            清空
          </el-button>
        </el-tag>
      </template>
      <avue-crud
        v-if="isInit && visible"
        :option="option"
        :table-loading="loading"
        :search.sync="query"
        :data="data"
        :page.sync="page"
        v-model="form"
        ref="crud"
        @search-change="searchChange"
        @search-reset="searchReset"
        @selection-change="selectionList = $event"
        @current-change="page.currentPage = $event"
        @size-change="page.pageSize = $event"
        @row-click="rowClick"
        @on-load="onLoad"
      >
        <template v-if="!multiple" #radio="{ row }">
          <el-radio v-model="form.radio" :label="row.id"><i></i></el-radio>
        </template>
        <template #name="{ row, index }">
          <el-tag
            size="mini"
            type="danger"
            effect="plain"
            v-if="row.status === 2"
          >
            离职
          </el-tag>
          <span>{{ row.name }}</span>
        </template>
      </avue-crud>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose" size="mini">取 消</el-button>
        <el-button type="primary" @click="handleConfirm" size="mini"
          >确 定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { getUser } from "@/api/system/user";
import { getToken } from "@/util/auth";
import Emitter from 'element-ui/src/mixins/emitter';

export default {
  mixins: [Emitter],
  props: {
    value: {
      type: String,
    },
    params: {
      type: Object,
      default: () => {},
    },
    size: {
      type: String,
      default: "mini",
    },
    userUrl: {
      type: String,
      default: () => {
        return "/api/blade-user/search/user?status=1";
      },
    },
    disabled: Boolean,
    customOption: Object,
    multiple: {
      type: Boolean,
      default: false,
    },
  },
  watch: {
    value: {
      handler(val) {
        if (val && !this.visible) {
            const name = [];
            const checks = (val + "").split(",");
            const asyncList = [];
            checks.forEach((c) => {
              if (c) asyncList.push(getUser(c));
            });
            Promise.all(asyncList).then((res) => {
              res.forEach((r) => {
                const data = r.data.data;
                if (data) name.push(data.realName);
              });
              this.$set(this, "personName", name.join(","));
            });
          } else this.$set(this, "personName", "");
        if (!val) {
          this.handleClear();
        }
      },
      immediate: true,
    },
    multiple: {
      handler(val) {
        if (!val) {
          this.$set(this.option, "selection", false);
          this.findObject(this.option.column, "radio").hide = false;
        } else {
          this.$set(this.option, "selection", true);
          this.findObject(this.option.column, "radio").hide = true;
        }
      },
      immediate: true,
    },
    visible: {
      handler(val) {
        if (val) {
          if (this.params) {
            if (
              this.params.deptName !== this.query.deptName ||
              this.params.postName !== this.query.postName
            ) {
              this.mergeQuery();
            }
          }
        }
      },
    },
  },
  computed: {
    ids() {
      let ids = new Set();
      this.selectionList.forEach((ele) => {
        ids.add(ele.id);
      });
      return Array.from(ids).join(",");
    },
    names() {
      let names = new Set();
      this.selectionList.forEach((ele) => {
        names.add(ele.realName);
      });
      return Array.from(names).join(",");
    },
  },
  data() {
    return {
      personName: "",
      visible: false,
      isInit: false,
      form: {},
      query: {},
      loading: false,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      data: [],
      props: {
        id: "id",
        name: "realName",
        records: "data.data.records",
        total: "data.data.total",
      },
      option: {
        searchEnter: true,
        size: "mini",
        searchSize: "mini",
        align: "center",
        menu: false,
        addBtn: false,
        header: false,
        border: true,
        tip: false,
        reserveSelection: true,
        highlightCurrentRow: true,
        gutter: 5,
        searchMenuSpan: 6,
        selection: true,
        column: [
          {
            label: "",
            prop: "radio",
            type: "radio",
            width: 55,
            hide: true,
          },
          {
            label: "头像",
            prop: "avatar",
            type: "upload",
            propsHttp: {
              home: `/api/blade-resource/attach/download?${
                this.website.tokenHeader
              }=${getToken()}&id=`,
            },
            width: 90,
          },
          {
            label: "姓名",
            prop: "name",
            overHidden: true,
            search: true,
          },
          {
            label: "部门",
            prop: "deptName",
            overHidden: true,
            search: true,
          },
          {
            label: "职位",
            prop: "postName",
            overHidden: true,
            search: true,
          },
        ],
      },
    };
  },
  mounted() {
    this.init();
  },
  methods: {
    handleClear() {
      this.personName = "";
      if (!this.multiple) {
        this.selectionList = [];
        this.$set(this.form, "radio", null);
      } else this.selectionClear();
      this.$emit("input", this.ids);
      this.$emit("clear");
    },
    bpFocus() {
      this.selectionList = [];
      this.visible = true;
    },
    init() {
      if (!this.isInit) {
        if (this.customOption) {
          const { column, userProps } = this.customOption;
          if (column) this.$set(this.option, "column", column);
          if (userProps) this.$set(this, "props", userProps);
        }
        this.mergeQuery();
        this.isInit = true;
      }
    },
    mergeQuery() {
      this.query = Object.assign(this.query, this.params);
    },
    handleConfirm() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.personName = this.names;
      this.$emit("input", this.ids);
      this.$emit("confirm", this.ids, this.names);
      this.$nextTick(() => {
        this.dispatch('ElFormItem', 'el.form.blur', [this.ids]);
      });
      this.handleClose();
    },
    handleClose(done) {
      // this.selectionClear()
      this.visible = false;
      if (done && typeof done == "function") done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionClear() {
      this.selectionList = [];
      if (this.$refs.crud) this.$refs.crud.toggleSelection();
    },
    rowClick(row) {
      if (!this.multiple) {
        this.selectionList = [row];
        this.$set(this.form, "radio", row.id);
      } else this.$refs.crud.toggleSelection([row]);
    },
    async getAPromise(userId) {
      let row = this.data.find((d) => d.id === userId); // 当前页查找
      if (!row) {
        row = this.selectionList.find((d) => d.id === userId); // 勾选列表查找
        if (!row) {
          let res = await getUser(userId); // 接口查找
          if (res.data.data) row = res.data.data;
        }
      }
      return row;
    },
    async changeDefaultChecked() {
      if (!this.value || this.value.length < 1) return;
      let defaultChecked = this.value;

      if (this.multiple) {
        // this.selectionClear()
        const checks = defaultChecked.split(",");
        if (checks.length > 0) {
          setTimeout(() => {
            let promiseArr = [];
            checks.forEach((c) => {
              promiseArr.push(this.getAPromise(c));
            });
            Promise.all(promiseArr)
              .then((results) => {
                let names = [];
                results.forEach((row) => {
                  if (row && this.$refs.crud) {
                    names.push(row.realName);
                    this.$refs.crud.toggleRowSelection(row, true);
                  }
                });
                this.personName = Array.from(names).join(",");
              })
              .catch((error) => this.$message.warning("初始化失败"));
          }, 500);
        }
      } else {
        let row = this.data.find((d) => d.id == defaultChecked);
        if (!row) {
          let res = await getUser(defaultChecked);
          if (res.data.data) row = res.data.data;
        }

        if (row) {
          this.selectionList = [row];
          this.$set(this.form, "radio", defaultChecked);
        } else {
          this.selectionList = [];
          this.$set(this.form, "radio", "");
        }
      }
      this.personName = this.names;
    },
    onLoad(page, params = {}) {
      this.loading = true;
      const param = {
        current: page.currentPage,
        size: page.pageSize,
        ...Object.assign(params, this.query),
      };
      this.$axios.get(this.userUrl, { params: param }).then((res) => {
        this.page.total = this.getAsVal(res, this.props.total);
        this.data = this.getAsVal(res, this.props.records) || [];
        this.loading = false;

        this.changeDefaultChecked();
      });
    },
    getAsVal(obj, bind = "") {
      let result = this.deepClone(obj);
      if (this.validatenull(bind)) return result;
      bind.split(".").forEach((ele) => {
        if (!this.validatenull(result[ele])) {
          result = result[ele];
        } else {
          result = "";
        }
      });
      return result;
    },
  },
};
</script>
<style lang="scss">
.us-dialog {
  display: flex;
  flex-direction: column;
  margin: 0 !important;
  position: absolute;
  top: 40%;
  left: 50%;
  transform: translate(-50%, -40%);
  max-height: calc(100% - 30px);
  max-width: calc(100% - 30px);

  .el-dialog__body {
    flex: 1;
    overflow: auto;
  }
}
</style>

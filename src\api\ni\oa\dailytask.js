import request from "@/router/axios";

export const getList = (current, size, params) => {
  return request({
    url: "/api/ni/oa/dailytask/page",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/ni/oa/dailytask/detail",
    method: "get",
    params: {
      id,
    },
  });
};

// //////////////////////////////////////////////

export const remove = (ids) => {
  return request({
    url: "/api/ni/oa/dailytask/remove",
    method: "post",
    params: {
      ids,
    },
  });
};
//////////////////////////////保存按钮接口
export const add = (row) => {
  return request({
    url: "/api/ni/oa/dailytask/add",
    method: "post",
    data: row,
  });
};

export const update = (row) => {
  return request({
    url: "/api/ni/oa/dailytask/update",
    method: "post",
    data: row,
  });
};

export const start = (row) => {
  return request({
    url: "/api/ni/oa/dailytask/start",
    method: "post",
    data: row,
  });
};

export const finish = (row) => {
  return request({
    url: "/api/ni/oa/dailytask/finish",
    method: "post",
    data: row,
  });
};

export const getRecordList = (current, size, params) => {
  return request({
    url: "/api/ni/oa/dailytask/record/page",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const submit = (ids) => {
  return request({
    url: "/api/ni/oa/dailytask/submit",
    method: "post",
    params: {
      ids,
    },
  });
};

export const back = (ids) => {
  return request({
    url: "/api/ni/oa/dailytask/back",
    method: "post",
    params: {
      ids,
    },
  });
};

/*
 *
 *
 */
export const reportPage = (current, size, params) => {
  return request({
    url: "/api/ni/oa/dayTaskReport/report-page",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

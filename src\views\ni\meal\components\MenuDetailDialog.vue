<template>
  <el-dialog :title="title" :visible.sync="visible" width="85%" append-to-body>
    <avue-crud
      v-if="visible"
      :option="option"
      :table-loading="loading"
      :data="data"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @refresh-change="refreshChange"
    >
      <template #menuLeft>
        <el-button
          type="primary"
          size="mini"
          icon="el-icon-plus"
          @click="handleAdd"
        >新 增
        </el-button>
      </template>
      <template #status="{ row, index }">
        <el-tag v-if="row.status === 1" size="mini" type="info">
          {{ row.$status }}
        </el-tag>
        <el-tag v-else-if="row.status === 2" size="mini">
          {{ row.$status }}
        </el-tag>
        <el-tag v-else-if="row.status === 3" size="mini" type="warning">
          {{ row.$status }}
        </el-tag>
        <el-tag v-else size="mini" type="danger">
          {{ row.$status }}
        </el-tag>
      </template>
      <template #type="{ row, index }">
        <el-tag size="mini" v-if="row.type == 'breakfast'" effect="plain">
          {{ row.$type }}
        </el-tag>
        <el-tag
          size="mini"
          v-else-if="row.type == 'lunch'"
          type="danger"
          effect="plain"
        >
          {{ row.$type }}
        </el-tag>
        <el-tag
          size="mini"
          v-else-if="row.type == 'dinner'"
          type="warning"
          effect="plain"
        >
          {{ row.$type }}
        </el-tag>
      </template>
      <template #foodId="{ row, index }">
        <el-tag v-if="row.peppery" size="mini" type="danger" effect="dark">
          辣
        </el-tag>
        <span>{{ row.$foodId }}</span>
      </template>
      <template #peppery="{ row, index }">
        <el-tag v-if="row.peppery" size="mini" type="danger">
          {{ row.$peppery }}
        </el-tag>
        <el-tag v-else size="mini">
          {{ row.$peppery }}
        </el-tag>
      </template>
      <template #menuRight="{ size }">
        <el-button
          icon="el-icon-time"
          circle
          :size="size"
          @click="handleLog"
        ></el-button>
      </template>
    </avue-crud>
  </el-dialog>
</template>

<script>
import {add, back, getDetail, getList, remove, submit, toVoid, update,} from "@/api/ni/meal/menu";
import {getDetail as getFoodDetail} from "@/api/ni/meal/food";
import {mapGetters} from "vuex";

export default {
  name: "menuDetailDialog",
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.omMenu_add, false),
        viewBtn: this.vaildData(this.permission.omMenu_view, false),
        delBtn: this.vaildData(this.permission.omMenu_delete, false),
        editBtn: this.vaildData(this.permission.omMenu_edit, false),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  data() {
    return {
      title: "",
      day: null,
      type: null,
      visible: false,
      form: {},
      query: {},
      loading: true,
      selectionList: [],
      option: {
        addBtn: false,
        cellBtn: true,
        align: "center",
        searchIndex: 3,
        searchIcon: true,
        size: "mini",
        searchSize: "mini",
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        dialogClickModal: false,
        column: [
          {
            label: "类型",
            prop: "type",
            row: true,
            cell: true,
            type: "select",
            placeholder: " ",
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_meal_menu_type",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            rules: [
              {
                required: true,
                message: "请选择类型",
                trigger: "blur",
              },
            ],
          },
          {
            label: "菜名",
            prop: "foodId",
            type: "select",
            dicUrl: "/api/ni/meal/food/list",
            props: {
              label: "name",
              value: "id",
            },
            typeformat(item) {
              return `${item.peppery ? "[辣]" : ""}${item.name}`;
            },
            filterable: true,
            cell: true,
            placeholder: " ",
            rules: [
              {
                required: true,
                message: "请选择菜名",
                trigger: "blur",
              },
            ],
            change: ({value, row}) => {
              if (value) {
                console.log(row);
                getFoodDetail(value).then((res) => {
                  const {data} = res.data;
                  row.price = data.price;
                  row.step = data.step;
                  this.$set(row, "max", data.max);
                });
              }
            },
          },
          {
            label: "单价",
            prop: "price",
            type: "number",
            controls: false,
            precision: 2,
            placeholder: " ",
            cell: true,
            rules: [
              {
                required: true,
                message: "请输入单价",
                trigger: "blur",
              },
            ],
          },
          {
            label: "步长",
            prop: "step",
            type: "number",
            placeholder: " ",
            precision: 1,
            min: 0.5,
            cell: true,
          },
          {
            label: "最大订单数",
            prop: "max",
            type: "number",
            placeholder: " ",
            precision: 1,
            min: 0.5,
            cell: true,
          },
          {
            label: "备注",
            prop: "remark",
            row: true,
            type: "textarea",
            minRows: 1,
            placeholder: " ",
            span: 24,
            cell: true,
          },
        ],
      },
      data: [],
    };
  },
  methods: {
    init(day, type) {
      this.day = day;
      this.type = type;
      this.title = this.day + "-菜单明细";
      this.onLoad();
      this.visible = true;
    },
    handleAdd() {
      this.data.splice(0, 0, {
        $cellEdit: true,
        date: this.day,
        type: this.type,
      });
    },
    handleSubmit(type) {
      this.form.status = 2;
      if (type === "add") {
        this.$refs.crud.rowSave();
      } else if (type === "edit") {
        this.$refs.crud.rowUpdate();
      }
    },
    rowToVoid(row) {
      this.$confirm("此操作作废提交的数据，是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        center: true,
      })
        .then(() => {
          return toVoid(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    rowBack(row) {
      this.$confirm("此操作撤回提交的数据，是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        center: true,
      })
        .then(() => {
          return back(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    rowSubmit(row) {
      this.$confirm("此操作将提交该数据，是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        center: true,
      })
        .then(() => {
          return submit(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    handleLog() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择要查看的数据");
        return;
      } else if (this.selectionList.length > 1) {
        this.$message.warning("只能选择一条数据");
        return;
      }
      this.$refs.logOptDialogRef.init(this.selectionList[0].id);
    },
    rowSave(row, done, loading) {
      add(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          window.console.log(error);
        }
      );
    },
    rowUpdate(row, index, done, loading) {
      this.$confirm("是否同步修改员工订餐的金额?", {
        distinguishCancelAndClose: true,
        confirmButtonText: "是",
        cancelButtonText: "否",
        type: "warning",
      })
        .then(() => {
          update(row, true).then(
            () => {
              this.onLoad(this.page);
              this.$message({
                type: "success",
                message: "操作成功!",
              });
            },
            (error) => {
              loading();
              console.log(error);
            }
          );
        })
        .catch((action) => {
          if (action === "cancel")
            update(row).then(
              () => {
                this.onLoad(this.page);
                this.$message({
                  type: "success",
                  message: "操作成功!",
                });
              },
              (error) => {
                loading();
                console.log(error);
              }
            );
        });
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.$confirm("是否同步删除该菜单员工的订餐?", {
          distinguishCancelAndClose: true,
          confirmButtonText: "是",
          cancelButtonText: "否",
          type: "warning",
        })
          .then(() => {
            remove(row.id, true).then(() => {
              this.onLoad(this.page);
              this.$message({
                type: "success",
                message: "操作成功!",
              });
            });
          })
          .catch((action) => {
            if (action === "cancel") {
              remove(row.id).then(() => {
                this.onLoad(this.page);
                this.$message({
                  type: "success",
                  message: "操作成功!",
                });
              });
            }
          });
      });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.$confirm("是否同步删除该菜单员工的订餐?", {
          distinguishCancelAndClose: true,
          confirmButtonText: "是",
          cancelButtonText: "否",
          type: "warning",
        })
          .then(() => {
            remove(this.ids, true).then(() => {
              this.onLoad(this.page);
              this.$message({
                type: "success",
                message: "操作成功!",
              });
              this.$refs.crud.toggleSelection();
            });
          })
          .catch((action) => {
            if (action === "cancel") {
              remove(this.ids).then(() => {
                this.onLoad(this.page);
                this.$message({
                  type: "success",
                  message: "操作成功!",
                });
                this.$refs.crud.toggleSelection();
              });
            }
          });
      });
    },
    beforeOpen(done, type) {
      if ("add" === type) {
        this.form.date = this.day;
        this.form.type = this.type;
        this.form.status = 2;
      } else if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then((res) => {
          this.form = res.data.data;
        });
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad();
    },
    searchChange(params, done) {
      this.query = params;
      this.onLoad(params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    refreshChange() {
      this.onLoad(this.query);
    },
    onLoad(params = {}) {
      this.loading = true;
      const q = {
        ...params,
        ...this.query,
        date: this.day,
        type: this.type,
      };
      getList(q).then((res) => {
        const data = res.data.data;
        this.data = data;
        this.loading = false;
        this.selectionClear();
      });
    },
  },
};
</script>

<style scoped></style>

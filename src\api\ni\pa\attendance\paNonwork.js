import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/ni/pa/paNonwork/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/ni/pa/paNonwork/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/ni/pa/paNonwork/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/ni/pa/paNonwork/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/ni/pa/paNonwork/submit',
    method: 'post',
    data: row
  })
}

export const getCountByMonth = (workMonth) => {
  return request({
    url: '/api/ni/pa/paNonwork/detail',
    method: 'get',
    params: {
      workMonth
    }
  })
}

export const getHoliday = (year) => {
  return request({
    url: '/api/ni/pa/paNonwork/getHoliday',
    method: 'get',
    params: {
      year
    }
  })
}

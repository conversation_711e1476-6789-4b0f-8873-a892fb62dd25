<script>
import { getDetail as getPackageDetail } from "@/api/ni/product/packaging";
import { getList } from "@/api/ni/product/sku";

export default {
  name: "SkuSelectDialog",
  props: {
    params: {
      type: Object,
      default: () => {},
    },
    multiple: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      visible: false,
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        menu: false,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        size: "mini",
        searchSize: "mini",
        align: "center",
        searchIndex: 3,
        searchIcon: true,
        searchEnter: true,
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        selection: true,
        dialogClickModal: false,
        column: [
          {
            label: "",
            prop: "radio",
            type: "radio",
            width: 55,
            hide: true,
          },

          {
            label: "名称",
            prop: "name",
            placeholder: " ",
            overHidden: true,
            minWidth: 155,
            hide: true,
          },
          {
            label: "SKU",
            prop: "sku",
            labelTip: "推荐使用标准化的命名方式来定义",
            placeholder: " ",
            overHidden: true,
            minWidth: 155,
            hide: true,
          },
          {
            label: "产品类型",
            prop: "productId",
            type: "select",
            dicUrl: "/api/ni/product/info/list?current=1&size=10000&status=1",
            props: {
              label: "name",
              value: "id",
              desc: "code",
            },
            minWidth: 80,
            remote: true,
            hide: true,
            showColumn: false,
            dicFormatter: (data) => {
              return data.data.records;
            },
            placeholder: " ",
            filterable: true,
            search: true,
          },
          {
            label: "产品类型",
            prop: "productText",
            display: false,
            width: 100,
          },
          {
            label: "存货编码",
            prop: "materialCode",
            search: true,
            width: 110,
            display: false,
          },
          {
            label: "规格",
            prop: "specification",
            type: "select",
            dicUrl: "/api/ni/product/spec/list",
            props: {
              label: "name",
              value: "code",
              desc: "code",
            },
            hide: true,
            showColumn: false,
            allowCreate: true,
            filterable: true,
            overHidden: true,
            minWidth: 120,
            search: true,
            searchOrder: 99,
            rules: [
              {
                required: true,
                message: "请选择规格",
                trigger: "blur",
              },
            ],
          },
          {
            label: "规格",
            prop: "specText",
            display: false,
            overHidden: true,
            width: 150,
          },
          {
            label: "质量",
            prop: "qualityLevel",
            type: "select",
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_product_sku_quality_level",
            props: {
              label: "dictValue",
              value: "dictKey",
              desc: "dictKey",
            },
            placeholder: " ",
            width: 100,
          },
          {
            label: "外包装",
            prop: "packageId",
            type: "select",
            dicUrl: "/api/ni/product/packaging/list?current=1&size=20&status=1",
            props: {
              label: "name",
              value: "id",
              desc: "type",
            },
            cascader: ["innerPackageId"],
            remote: true,
            dicFormatter: (data) => {
              return data.data.records;
            },
            filterable: true,
            change: ({ value }) => {
              if (value) {
                getPackageDetail(value).then((res) => {
                  this.form.capacity = res.data.data.capacity;
                  this.form.unit = res.data.data.unit;
                  this.form.currentMarkId = res.data.data.defaultMarkId;
                });
              }
            },
            hide: true,
            showColumn: false,
          },
          {
            label: "外包装",
            prop: "packageText",
            display: false,
            overHidden: true,
            searchOrder: 98,
            search: true,
            width: 115,
          },
          {
            label: "唛头",
            prop: "currentMarkId",
            type: "select",
            dicUrl: "",
            minWidth: 120,
            hide: true,
            showColumn: false,
          },
          {
            label: "唛头",
            prop: "currentMarkText",
            hide: true,
            display: false,
            overHidden: true,
            width: 100,
          },
          {
            label: "内包装",
            prop: "innerPackageId",
            type: "select",
            dicUrl:
              "/api/ni/product/packaging/innerTree?outerPackageId={{packageId}}&status=1",
            props: {
              label: "name",
              value: "id",
              desc: "type",
            },
            hide: true,
            showColumn: false,
            filterable: true,
          },
          {
            label: "内包装",
            prop: "innerPackageText",
            display: false,
            overHidden: true,
            width: 115,
          },
          {
            label: "单包容量",
            prop: "capacity",
            placeholder: " ",
            minWidth: 80,
          },
          {
            label: "单位",
            prop: "unit",
            type: "select",
            filterable: true,
            width: 70,
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_material_unit",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            slot: true,
            placeholder: " ",
          },
          {
            label: "关联编码",
            prop: "materialId",
            hide: true,
            showColumn: false,
          },

          {
            label: "备注",
            prop: "remark",
            type: "textarea",
            minRows: 2,
            overHidden: true,
            minWidth: 140,
            span: 24,
          },
          {
            label: "状态",
            prop: "status",
            type: "radio",
            minWidth: 80,
            dicData: [
              {
                label: "启用",
                value: "1",
              },
              {
                label: "停用",
                value: "2",
              },
            ],
            dataType: "number",
            rules: [
              {
                required: true,
                message: "请选择状态",
                trigger: "blur",
              },
            ],
          },
        ],
      },
      data: [],
      form: {},
    };
  },
  methods: {
    onShow() {
      if (!this.multiple) {
        this.$set(this.option, "selection", false);
        this.findObject(this.option.column, "radio").hide = false;
      } else {
        this.$set(this.option, "selection", true);
        this.findObject(this.option.column, "radio").hide = true;
      }
      this.visible = true;
    },
    rowClick(row) {
      if (!this.multiple) {
        this.selectionList = [row];
        this.$set(this.form, "radio", row.id);
      } else this.$refs.crud.toggleSelection([row]);
    },
    handleClose(done) {
      this.visible = false;
      if (done && typeof done == "function") done();
    },
    handleConfirm() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$emit("confirm", this.selectionList);
      this.handleClose();
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    searchReset() {
      this.query = {};
      this.page.currentPage = 1;
      this.onLoad(this.page);
    },
    selectionClear() {
      this.selectionList = [];
      if (this.$refs.crud) this.$refs.crud.toggleSelection();
    },
    onLoad(page, params = {}) {
      this.loading = true;
      const q = {
        ...params,
        ...this.query,
        ...this.params,
        status: 1,
      };
      getList(page.currentPage, page.pageSize, q).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
  },
};
</script>

<template>
  <el-dialog v-dialogDrag :visible.sync="visible" append-to-body width="1100px">
    <avue-crud
      ref="crud"
      v-if="visible"
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      v-model="form"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionList = $event"
      @current-change="page.currentPage = $event"
      @size-change="page.pageSize = $event"
      @row-click="rowClick"
      @on-load="onLoad"
    >
      <template v-if="!multiple" #radio="{ row }">
        <el-radio v-model="form.radio" :label="row.id"><i></i></el-radio>
      </template>
    </avue-crud>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose" size="mini">取 消</el-button>
      <el-button type="primary" @click="handleConfirm" size="mini"
        >确 定</el-button
      >
    </span>
  </el-dialog>
</template>

<style scoped></style>

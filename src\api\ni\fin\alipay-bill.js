import request from "@/router/axios";

export const getPage = (current, size, params) => {
  return request({
    url: "/api/ni/fin/alipay-bill/page",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getList = (params) => {
  return request({
    url: "/api/ni/fin/alipay-bill/list",
    method: "get",
    params,
  });
};
export const loadBill = (time) => {
  return request({
    url: "/api/ni/fin/alipay-bill/loadBill",
    method: "get",
    params: {
      time,
    },
  });
};
export const compareBill = (ids) => {
  return request({
    url: "/api/ni/fin/alipay-bill/compareBill",
    method: "post",
    params: {
      ids,
    },
  });
};

export const manualCompareBill = (params) => {
  return request({
    url: "/api/ni/fin/alipay-bill/manualCompareBill",
    method: "post",
    params,
  });
};
export const updateBillStatusBatch = (rows) => {
  return request({
    url: "/api/ni/fin/alipay-bill/updateBillStatusBatch",
    method: "post",
    data: rows,
  });
};
export const getPageJoinOrderItem = (current, size, params) => {
  return request({
    url: "/api/ni/fin/alipay-bill/pageJoinOrderItem",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};
/**
 * 支付宝对账单针对业务流水号相同的数据进行了合并
 * @param current
 * @param size
 * @param params
 * @returns {*}
 */
export const getPageJoinOrderItem1 = (current, size, params) => {
  return request({
    url: "/api/ni/fin/alipay-bill/v1/pageJoinOrderItem",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

/**
 * 支付宝对账单针对业务流水号相同的数据进行了合并
 * @param current
 * @param size
 * @param params
 * @returns {*}
 */
export const getPageJoinOrderItem2 = (current, size, params) => {
  return request({
    url: "/api/ni/fin/alipay-bill/v2/pageJoinOrderItem",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};
export const adjustmentBill = (data) => {
  return request({
    url: "/api/ni/fin/alipay-bill/adjustmentBill",
    method: "post",
    data,
  });
};

export const manualAdjustmentBill = (data) => {
  return request({
    url: "/api/ni/fin/alipay-bill/manualAdjustmentBill",
    method: "post",
    data,
  });
};
export const auditing = (ids) => {
  return request({
    url: "/api/ni/fin/alipay-bill/auditing",
    method: "post",
    params: {
      ids,
    },
  });
};
export const unAuditing = (ids) => {
  return request({
    url: "/api/ni/fin/alipay-bill/unAuditing",
    method: "post",
    params: {
      ids,
    },
  });
};
export const getListJoinOrderItem = (params) => {
  return request({
    url: "/api/ni/fin/alipay-bill/listJoinOrderItem",
    method: "get",
    params,
  });
};

export const changeNote = (ids, businessSerialNos, note) => {
  return request({
    url: "/api/ni/fin/alipay-bill/changeNote",
    method: "post",
    params: {
      ids,
      businessSerialNos,
      note,
    },
  });
};
export const getPageDayReport = (current, size, params) => {
  return request({
    url: "/api/ni/fin/alipay-bill/day/report/page",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};
export const listByPayableApplyIdJoinOrderItem = (payableApplySerialNo) => {
  return request({
    url: "/api/ni/fin/alipay-bill/listByPayableApplyIdJoinOrderItem",
    method: "get",
    params: {
      payableApplySerialNo,
    },
  });
};

export const getSumData = (startTime, endTime) => {
  return request({
    url: "/api/ni/fin/alipay-bill/getSumData",
    method: "get",
    params: {
      startTime,
      endTime,
    },
  });
};
/**
 * 订单核销数据
 * @param month
 * @returns {AxiosPromise}
 */
export const getOrderWriteOff = (month) => {
  return request({
    url: "/api/ni/fin/alipay-bill/getOrderWriteOff",
    method: "get",
    params: {
      month,
    },
  });
};
/**
 *  支付宝核销数据
 * @param month
 * @returns {AxiosPromise}
 */
export const getAlipayWriteOff = (month) => {
  return request({
    url: "/api/ni/fin/alipay-bill/getAlipayWriteOff",
    method: "get",
    params: {
      month,
    },
  });
};

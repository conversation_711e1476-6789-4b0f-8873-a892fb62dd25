<template>
  <basic-container>
    <avue-skeleton :loading="waiting" avatar :rows="8">
      <div style="display: flex">
        <avue-title
          style="margin-bottom: 20px"
          :styles="{ fontSize: '20px' }"
          :value="process.name"
        ></avue-title>
        <el-badge
          v-if="permission.wf_process_draft && draftCount > 0"
          :value="draftCount"
          style="margin-top: 5px; margin-right: 40px"
          type="warning"
        >
          <el-button size="mini" v-loading="loading" @click="handleDraftBox"
            >草稿箱
          </el-button>
        </el-badge>
      </div>
      <el-card shadow="never" style="margin-top: 20px">
        <!-- 自定义表单区域 -->
        <avue-form
          v-if="
            option &&
            ((option.column && option.column.length > 0) ||
              (option.group && option.group.length > 0))
          "
          v-model="form"
          ref="form"
          :option="option"
          :defaults.sync="defaults"
          @submit="handleSubmit"
          @error="loading = false"
          :upload-preview="handleUploadPreview"
        >
          <template #adjustId="{ size, index }">
            <fin-adjust-select
              v-model="form.adjustId"
              :size="size"
              :params="{
                status: 9,
                supplierId: form.supplierId,
                brand: form.brand,
                used: 0,
              }"
              :before-select="beforeAdjustSelect"
              @change="handleAdjustChange"
            />
          </template>
          <template #costApplyId="{ size, index }">
            <cost-apply-select
              v-model="form.costApplyId"
              :size="size"
              :change="handleCostApplyChange"
            />
          </template>
          <template #financeBank="{ size, disabled }">
            <supplier-finance-select
              v-model="form.financeBank"
              :supplier-id="form.supplierId"
              :size="size"
              :disabled="disabled"
              @submit="handleFinanceSelect"
            />
          </template>
          <template #financeAccount="{ size, disabled }">
            <bank-card-input
              v-model="form.financeAccount"
              :size="size"
              :disabled="disabled"
            />
          </template>
          <template #budgetId="{ size, disabled }">
            <un-finish-budget-select
              v-model="form.budgetId"
              :size="size"
              :disabled="disabled"
              placeholder=" "
              @clear="handleBudgetClear"
              @confirm="handleBudgetConfirm"
            />
          </template>
          <template #contractId="{ disabled, size, index }">
            <contract-select
              v-model="form.contractId"
              :pay-type="payType"
              :disabled="disabled"
              :size="size"
              :params="{
                b: form.supplierId,
                status: 9,
                payApplyStates: '0,1',
              }"
              :before-select="beforeContractSelect"
              @confirm="handleContractIdConfirm"
            />
          </template>
          <template #amount="{ size, disabled }">
            <el-input-number
              v-if="!['3', '9'].includes(form.type)"
              :size="size"
              style="width: 100%"
              v-model="form.amount"
              precision="2"
              controls-position="right"
              @change="handleAmount"
            ></el-input-number>
            <el-input-number
              v-else-if="['9'].includes(form.type)"
              :size="size"
              style="width: 100%"
              v-model="form.amount"
              precision="2"
              controls-position="right"
              @change="handleAmount"
            ></el-input-number>
            <el-input-number
              v-else
              :size="size"
              style="width: 100%"
              v-model="form.amount"
              precision="2"
              disabled
              controls-position="right"
              @change="handleAmount"
            ></el-input-number>
          </template>
          <template #supplierId="{ disabled, size, index }">
            <supplier-select
              v-model="form.supplierId"
              :size="size"
              :multiple="false"
              :disabled="disabled"
              @submit="handleSupplierSubmit"
            />
          </template>
          <template #paySoaId="{ disabled, size, index }">
            <pay-soa-select
              v-model="form.paySoaId"
              :size="size"
              :disabled="disabled"
              :before-select="beforeContractSelect"
              :params="{
                brand: form.brand,
                supplierId: form.supplierId,
                confirm: true,
                payApplyStates: '0,1',
              }"
              @submit="handlePaySoaSubmit"
            />
          </template>
          <template #porOrderId="{ disabled, size, index }">
            <por-order-select
              v-model="form.porOrderId"
              :label.sync="form.porOrderSerialNo"
              :size="size"
              :disabled="disabled"
              :params="{
                brand: form.brand,
                supplierId: form.supplierId,
                payType: '2',
                payApplyStates: '0,1',
                payState: 0,
              }"
              :before-select="beforeContractSelect"
              @submit="handleOrderSubmit"
              @clear="handleOrderClear"
            />
          </template>
          <template #itemsLabel>
            <span style="font-size: 16px; font-weight: 500">{{
              form.soaType === "2" ? "支付宝明细" : "付款明细"
            }}</span>
            <el-divider
              v-if="form.type && ['1', '3'].includes(form.type)"
              direction="vertical"
            />
            <el-button-group
              v-if="
                ['3'].includes(form.type) ||
                (form.type === '1' && ['3', '9'].includes(form.contractType))
              "
            >
              <el-button
                type="primary"
                size="mini"
                icon="el-icon-plus"
                @click="costItemAdd"
                >添加
              </el-button>
              <el-button
                type="danger"
                size="mini"
                icon="el-icon-delete"
                @click="costItemDelete"
                >删除
              </el-button>
            </el-button-group>
          </template>
          <template #items="{ row, index, size }">
            <payable-apply-order-alipay
              v-if="form.soaType && form.soaType === '2'"
              v-model="form.items"
            />
            <payable-apply-order-item
              v-else
              v-model="form.items"
              :selectionList.sync="item.selectionList"
              :cost="form.type === '3'"
              @sumAmount="handleSumAmount"
            />
          </template>
        </avue-form>
        <!-- 自定义表单区域 -->
      </el-card>

      <el-card shadow="never" style="margin-top: 20px" v-if="showExamForm">
        <wf-examine-form
          ref="examineForm"
          :process="process"
          @user-select="handleUserSelect"
        ></wf-examine-form>
      </el-card>
      <div style="height: 120px"></div>
      <el-row
        class="foot-item avue-affix"
        :style="{
          width: isCollapse ? 'calc(100% - 80px)' : 'calc(100% - 260px)',
        }"
        id="avue-view"
      >
        <el-button
          v-no-more-click
          type="primary"
          size="mini"
          v-loading="loading"
          @click="handleSubmit"
          >发起
        </el-button>
        <el-button
          v-if="permission.wf_process_draft"
          type="success"
          size="mini"
          v-loading="loading"
          @click="
            handleDraftNotClose(process.id, process.formKey, form, process.key)
          "
          >存为草稿
        </el-button>
        <el-button
          v-if="permission.wf_process_draft"
          type="success"
          size="mini"
          v-loading="loading"
          @click="handleDraft(process.id, process.formKey, form, process.key)"
          >存为草稿并关闭
        </el-button>
      </el-row>
    </avue-skeleton>
    <el-dialog
      title="财务信息选择"
      append-to-body
      :visible.sync="financeVisible"
      width="800px"
    >
      <avue-crud
        ref="financeCrud"
        :option="financeOption"
        v-model="financeForm"
        :data="financeData"
        @row-click="rowClick"
      >
        <template #radio="{ row, index }">
          <el-radio v-model="form.radio" :label="index"><i></i></el-radio>
        </template>
      </avue-crud>
      <span slot="footer" class="dialog-footer">
        <el-button @click="() => (financeVisible = false)" size="mini"
          >取 消</el-button
        >
        <el-button type="primary" @click="handleFinanceConfirm" size="mini"
          >确 定</el-button
        >
      </span>
    </el-dialog>
    <budget-item-dialog
      ref="porBudgetCostItemRef"
      multiple
      :params="{ used: false, cost: true }"
      @confirm="handleCostItemSelect"
    />
    <!-- 人员选择弹窗 -->
    <wf-user-select
      ref="user-select"
      :check-type="checkType"
      :default-checked="defaultChecked"
      @onConfirm="handleUserSelectConfirm"
    ></wf-user-select>
    <!-- 草稿弹窗 -->
    <draft-popup
      :visible.sync="isDraftPopupVisible"
      :draftList="draftList"
      @select="handleDraftSelect"
      @delete="handleDraftDelete"
    ></draft-popup>
    <cost-apply-item-dialog
      ref="costApplyItemRef"
      :apply-id="form.costApplyId"
      multiple
      @onConfirm="handleCostApplySelectConfirm"
    />
  </basic-container>
</template>

<script>
import WfExamineForm from "@/views/plugin/workflow/process/components/examForm.vue";
import WfUserSelect from "@/views/plugin/workflow/process/components/user-select";

import exForm from "@/views/plugin/workflow/mixins/ex-form";
import draft from "@/views/plugin/workflow/mixins/draft";
import PayableApplyOrderItem from "@/views/ni/fin/components/PayableApplyItem";
import { getDetail as getSupplierDetail } from "@/api/ni/base/supplier/supplierinfo";
import SupplierSelect from "@/views/ni/base/components/SupplierSelect1";
import PorOrderSelect from "@/views/ni/por/components/OrderSelect";
import ContractSelect from "@/views/ni/base/components/ContractSelect";
import PayableApplyOrderAlipay from "@/views/ni/fin/components/PayableApplyOrderAlipay";
import {
  getList as getOrderItemList,
  getList as getItemList,
} from "@/api/ni/por/order-item";
import { numToCapital } from "@/util/util";
import { getDetail as getPaySoaDetail } from "@/api/ni/fin/pay-soa";
import PaySoaSelect from "@/views/ni/fin/components/PaySoaSelect";
import BankCardInput from "@/components/bank-card-input";
import AlipayBillList from "@/views/ni/por/components/AlipayBillList";
import AlipayBillSelect from "@/views/ni/por/components/AlipayBillSelect";
import FinPayableApplyForm from "@/views/ni/fin/components/FinPayableApplyForm";
import PayableApplyDetail from "@/views/ni/fin/payable-apply-detail";
import unFinishBudgetSelect from "@/views/ni/por/components/UnFinishBudgetSelect";
import BudgetItemDialog from "@/views/ni/por/components/BudgetItemDialog";
import CostApplySelect from "@/views/ni/fin/components/CostApplySelect";
import { getDetail as getCostApplyDetail } from "@/api/ni/fin/cost-apply";
import CostApplyItemDialog from "@/views/ni/fin/components/CostApplyItemDialog";
import SupplierFinanceSelect from "@/views/ni/base/components/SupplierFinanceSelect";
import FinAdjustSelect from "@/views/ni/fin/components/FinAdjustSelect";
import DraftPopup from "@/views/plugin/workflow/process/components/draftPopup.vue";
import debounce from "@/util/debounce";

export default {
  components: {
    FinAdjustSelect,
    CostApplyItemDialog,
    FinPayableApplyForm,
    PayableApplyDetail,
    AlipayBillList,
    AlipayBillSelect,
    BankCardInput,
    PaySoaSelect,
    ContractSelect,
    WfUserSelect,
    WfExamineForm,
    PayableApplyOrderItem,
    PayableApplyOrderAlipay,
    SupplierSelect,
    PorOrderSelect,
    unFinishBudgetSelect,
    BudgetItemDialog,
    CostApplySelect,
    SupplierFinanceSelect,
    DraftPopup,
  },
  mixins: [exForm, draft],
  activated() {
    let val = this.$route.query.p;
    if (val) {
      let text = Buffer.from(val, "base64").toString();
      text = text.replace(/[\r|\n|\t]/g, "");
      const param = JSON.parse(text);
      const { processId, processDefKey, form } = param;
      if (form) {
        const f = JSON.parse(
          new Buffer(decodeURIComponent(form), "utf8").toString("utf8")
        );
        this.form = Object.assign(this.form, f);
      }
      if (processId) {
        this.getForm(processId);
      } else if (processDefKey) {
        this.getFormByProcessDefKey(processDefKey);
      }
    }
  },
  computed: {
    showExamForm() {
      const { hideComment, hideCopy, hideExamine } = this.process;
      return !hideComment || !hideCopy || !hideExamine;
    },
    itemIds() {
      let items = new Set();
      this.form.items.forEach((ele) => {
        items.add(ele.budgetItemId);
      });
      return Array.from(items).join(",");
    },
  },
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      //  这里的vm指的就是vue实例，可以用来当做this使用
      vm.fromPath = from.path; //获取上一级路由的路径
    });
  },
  created() {
    this.dictInit();
  },
  data() {
    const PAY_TYPE_PAY = "2";
    return {
      payType: PAY_TYPE_PAY,
      fromPath: "",
      defaults: {},
      form: {
        items: [],
      },
      option: {
        labelWidth: 150,
        calcHeight: 30,
        size: "mini",
        menuBtn: false,
        column: [
          {
            type: "input",
            label: "申请人",
            display: true,
            prop: "createUserName",
            value: "${this.$store.getters.userInfo.nick_name}",
            readonly: true,
            span: 6,
          },
          {
            type: "input",
            label: "申请部门",
            display: true,
            prop: "createDeptName",
            value: "${this.$store.getters.userInfo.dept_name}",
            readonly: true,
            span: 6,
          },
          {
            label: "账套",
            prop: "brand",
            placeholder: " ",
            type: "radio",
            span: 6,
            dicData: [],
            row: true,
            value: "1",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            rules: [
              {
                required: true,
                message: "请选择账套",
                trigger: "blur",
              },
            ],
          },
          {
            label: "主题",
            prop: "title",
            overHidden: true,
            placeholder: " ",
            search: true,
            minWidth: 120,
            span: 6,
            display: false,
            rules: [
              {
                required: true,
                message: "请输入主题",
                trigger: "blur",
              },
            ],
          },
          {
            label: "用途",
            prop: "useTo",
            type: "textarea",
            placeholder: " ",
            minRows: 1,
            span: 6,
            rules: [
              {
                required: true,
                message: "请输入用途",
                trigger: "blur",
              },
            ],
          },
          {
            label: "编号",
            prop: "serialNo",
            placeholder: "系统自动生成",
            disabled: true,
            span: 6,
            row: true,
          },
          {
            label: "供应商",
            prop: "supplierId",
            minWidth: 100,
            overHidden: true,
            span: 6,
            search: true,
            rules: [
              {
                required: true,
                message: "请选择供应商",
                trigger: "blur",
              },
            ],
          },
          {
            label: "开户银行",
            prop: "financeBank",
            placeholder: " ",
            span: 6,
            hide: true,
            rules: [
              {
                required: true,
                message: "请输入开户银行",
                trigger: "blur",
              },
            ],
          },
          {
            label: "收款人全称",
            prop: "financeName",
            placeholder: " ",
            span: 6,
            hide: true,
            rules: [
              {
                required: true,
                message: "请输入收款人全称",
                trigger: "blur",
              },
            ],
          },
          {
            label: "银行账号",
            prop: "financeAccount",
            placeholder: " ",
            span: 6,
            row: true,
            hide: true,
            rules: [
              {
                required: true,
                message: "请输入银行账号",
                trigger: "blur",
              },
            ],
          },
          {
            label: "申请类型",
            prop: "type",
            type: "select",
            placeholder: " ",
            dicData: [],
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            row: false,
            minWidth: 100,
            span: 6,
            rules: [
              {
                required: true,
                message: "请选择申请类型",
                trigger: "blur",
              },
            ],
            control: (val) => {
              if (val === "1") {
                this.form.items = [];
                //合同
                return {
                  type: {
                    row: false,
                  },
                  paySoaId: {
                    display: false,
                  },
                  paySoaAmount: {
                    display: false,
                  },
                  porOrderId: {
                    display: false,
                  },
                  porOrderAmount: {
                    display: false,
                  },
                  contractId: {
                    display: true,
                  },
                  contractAmount: {
                    display: true,
                  },
                  unPayAmount: {
                    display: true,
                  },
                  costApplyId: {
                    display: false,
                  },
                  budgetId: {
                    disabled: false,
                    display: true,
                    row: true,
                    rules: [
                      {
                        required: false,
                      },
                    ],
                  },
                  ofcSoaId: {
                    display: false,
                  },
                  items: {
                    display: true,
                  },
                  amount: {
                    disabled: false,
                  },
                };
              } else if (val === "2") {
                //采购
                this.form.items = [];
                return {
                  type: {
                    row: false,
                  },
                  paySoaId: {
                    display: true,
                  },
                  paySoaAmount: {
                    display: true,
                  },
                  porOrderId: {
                    display: false,
                  },
                  porOrderAmount: {
                    display: false,
                  },
                  contractId: {
                    display: false,
                  },
                  contractAmount: {
                    display: false,
                  },
                  unPayAmount: {
                    display: true,
                  },
                  costApplyId: {
                    display: false,
                  },
                  budgetId: {
                    disabled: false,
                    display: false,
                    row: true,
                  },
                  ofcSoaId: {
                    display: false,
                  },
                  items: {
                    display: true,
                  },
                  amount: {
                    disabled: false,
                  },
                };
              } else if (val === "3") {
                //费用
                return {
                  type: {
                    row: false,
                  },
                  paySoaId: {
                    display: false,
                  },
                  paySoaAmount: {
                    display: false,
                  },
                  porOrderId: {
                    display: false,
                  },
                  porOrderAmount: {
                    display: false,
                  },
                  contractId: {
                    display: false,
                  },
                  contractAmount: {
                    display: false,
                  },
                  unPayAmount: {
                    display: false,
                  },
                  costApplyId: {
                    display: true,
                  },
                  budgetId: {
                    display: true,
                    row: true,
                    rules: [
                      {
                        required: true,
                      },
                    ],
                  },
                  ofcSoaId: {
                    display: false,
                  },
                  items: {
                    display: true,
                  },
                  amount: {
                    disabled: true,
                  },
                };
              } else if (val === "5") {
                this.form.items = [];
                //预付款
                return {
                  type: {
                    row: false,
                  },
                  paySoaId: {
                    display: false,
                  },
                  paySoaAmount: {
                    display: false,
                  },
                  porOrderId: {
                    display: true,
                  },
                  porOrderAmount: {
                    display: true,
                  },
                  contractId: {
                    display: false,
                  },
                  contractAmount: {
                    display: false,
                  },
                  unPayAmount: {
                    display: true,
                  },
                  costApplyId: {
                    display: false,
                  },
                  budgetId: {
                    disabled: false,
                    display: false,
                    row: true,
                  },
                  ofcSoaId: {
                    display: false,
                  },
                  items: {
                    display: true,
                  },
                  amount: {
                    disabled: false,
                  },
                };
              } else if (val === "6") {
                this.form.items = [];
                //快递付款
                return {
                  type: {
                    row: false,
                  },
                  paySoaId: {
                    display: false,
                  },
                  paySoaAmount: {
                    display: false,
                  },
                  porOrderId: {
                    display: false,
                  },
                  porOrderAmount: {
                    display: false,
                  },
                  contractId: {
                    display: false,
                  },
                  contractAmount: {
                    display: false,
                  },
                  unPayAmount: {
                    display: true,
                  },
                  costApplyId: {
                    display: false,
                  },
                  budgetId: {
                    disabled: true,
                    display: true,
                    row: false,
                  },
                  ofcSoaId: {
                    display: true,
                  },
                  items: {
                    display: true,
                  },
                  amount: {
                    disabled: false,
                  },
                };
              } else {
                this.form.items = [];
                //其他
                return {
                  type: {
                    row: true,
                  },
                  paySoaId: {
                    display: false,
                  },
                  paySoaAmount: {
                    display: false,
                  },
                  porOrderId: {
                    display: false,
                  },
                  porOrderAmount: {
                    display: false,
                  },
                  contractId: {
                    display: false,
                  },
                  contractAmount: {
                    display: false,
                  },
                  unPayAmount: {
                    display: false,
                  },
                  costApplyId: {
                    display: false,
                  },
                  budgetId: {
                    disabled: false,
                    display: false,
                    row: true,
                  },
                  ofcSoaId: {
                    display: false,
                  },
                  items: {
                    display: false,
                  },
                  amount: {
                    disabled: false,
                  },
                };
              }
            },
          },
          {
            label: "采购对账单",
            prop: "paySoaId",
            span: 6,
            display: false,
            hide: true,
            rules: [
              {
                required: true,
                message: "请选择采购对账单",
                trigger: "blur",
              },
            ],
          },
          {
            label: "采购订单",
            prop: "porOrderId",
            span: 6,
            display: false,
            hide: true,
            rules: [
              {
                required: true,
                message: "请选择采购订单",
                trigger: "blur",
              },
            ],
          },
          {
            label: "关联合同",
            prop: "contractId",
            span: 6,
            display: false,
            hide: true,
            rules: [
              {
                required: true,
                message: "请选择关联合同",
                trigger: "blur",
              },
            ],
          },
          {
            label: "费用申请",
            prop: "costApplyId",
            display: false,
            span: 6,
            rules: [
              {
                required: false,
                message: "请选择费用申请",
                trigger: "blur",
              },
            ],
          },
          {
            label: "关联预算",
            prop: "budgetId",
            placeholder: " ",
            span: 6,
            row: true,
            display: false,
            hide: true,
            rules: [
              {
                required: true,
                message: "请选择关联预算",
                trigger: "blur",
              },
            ],
          },
          {
            label: "快递对账单",
            prop: "ofcSoaId",
            placeholder: " ",
            span: 6,
            row: true,
            display: false,
            hide: true,
            rules: [
              {
                required: true,
                message: "请选择快递对账单",
                trigger: "blur",
              },
            ],
          },
          {
            label: "对账金额",
            prop: "paySoaAmount",
            type: "number",
            placeholder: " ",
            disabled: true,
            display: false,
            hide: true,
            showColumn: false,
            precision: 2,
            span: 6,
            row: true,
          },
          {
            label: "订单金额",
            prop: "porOrderAmount",
            type: "number",
            placeholder: " ",
            disabled: true,
            display: false,
            hide: true,
            showColumn: false,
            precision: 2,
            span: 6,
            row: true,
          },
          {
            label: "合同金额",
            prop: "contractAmount",
            type: "number",
            precision: 2,
            placeholder: " ",
            disabled: true,
            display: false,
            hide: true,
            showColumn: false,
            span: 6,
            row: true,
          },
          {
            span: 6,
            label: "是否调账",
            prop: "adjust",
            type: "radio",
            value: 0,
            row: false,
            dicData: [
              {
                label: "是",
                value: 1,
              },
              {
                label: "否",
                value: 0,
              },
            ],
            change: ({ column, value }) => {
              const adjustId = this.findObject(this.option.column, "adjustId");
              const adjustAmount = this.findObject(
                this.option.column,
                "adjustAmount"
              );
              if (value === 1) {
                adjustId.display = true;
                adjustAmount.display = true;
                column.row = false;
              } else {
                adjustId.display = false;
                adjustAmount.display = false;
                column.row = true;
                if (this.form.adjustId && this.form.adjustAmount) {
                  this.form.unPayAmount += this.form.adjustAmount;
                  this.form.amount = null;
                  this.form.upperAmount = null;
                }
                this.form.adjustId = null;
                this.form.adjustAmount = 0;
              }
            },
          },
          {
            label: "调账申请",
            prop: "adjustId",
            labelTip: "调增是加上调账金额，调减是减去调账金额",
            display: false,
            span: 6,
          },
          {
            label: "调账金额",
            prop: "adjustAmount",
            type: "number",
            placeholder: " ",
            disabled: true,
            precision: 2,
            span: 6,
            row: true,
            display: false,
          },
          {
            label: "待付款金额",
            prop: "unPayAmount",
            type: "number",
            placeholder: " ",
            disabled: true,
            display: true,
            hide: true,
            showColumn: false,
            precision: 2,
            span: 6,
          },
          {
            label: "本次付款金额",
            labelTip: "含税",
            prop: "amount",
            type: "number",
            placeholder: " ",
            minWidth: 100,
            precision: 2,
            disabled: false,
            span: 6,
            rules: [
              {
                required: true,
                message: "请输入本次付款金额(含税)",
                trigger: "blur",
              },
            ],
          },
          {
            label: "金额大写",
            prop: "upperAmount",
            readonly: true,
            placeholder: " ",
            hide: true,
            showColumn: false,
            span: 6,
          },
          {
            label: "币种",
            prop: "currency",
            type: "select",
            search: true,
            minWidth: 70,
            dicUrl: "/api/blade-system/dict/dictionary?code=currency",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            value: "RMB",
            placeholder: " ",
            span: 6,
            row: true,
            rules: [
              {
                required: true,
                message: "请选择币种",
                trigger: "blur",
              },
            ],
            control: (val) => {
              if (val === "RMB") {
                return {
                  excRate: {
                    display: false,
                    row: false,
                    value: 1,
                  },
                  currency: {
                    row: true,
                  },
                };
              } else {
                return {
                  excRate: {
                    display: true,
                    row: true,
                  },
                  currency: {
                    row: false,
                  },
                };
              }
            },
          },
          {
            label: "汇率",
            prop: "excRate",
            labelTip:
              "汇率=本位币/原币.如本位币为人民币，原币为美元: 汇率为:0.1439.",
            type: "number",
            placeholder: " ",
            hide: true,
            display: false,
            span: 6,
            row: true,
            rules: [
              {
                required: true,
                message: "请输入汇率",
                trigger: "blur",
              },
            ],
          },
          {
            label: "付款日期",
            prop: "applyPayableDate",
            type: "date",
            minWidth: 95,
            placeholder: " ",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            span: 6,
            rules: [
              {
                required: true,
                message: "请选择申请付款日期",
                trigger: "blur",
              },
            ],
          },
          {
            label: "付款方式",
            prop: "paymentType",
            type: "select",
            minWidth: 85,
            span: 6,
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_fin_payment_type",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            placeholder: " ",
            rules: [
              {
                required: true,
                message: "请选择付款方式",
                trigger: "blur",
              },
            ],
          },
          {
            label: "发票类型",
            prop: "billType",
            minWidth: 70,
            type: "select",
            dicUrl: "/api/blade-system/dict-biz/dictionary?code=ni_bill_type",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            placeholder: " ",
            span: 6,
            rules: [
              {
                required: true,
                message: "请输入",
                trigger: "blur",
              },
            ],
            change: ({ value }) => {
              if (value === "1") {
                this.form.taxRate = 13;
              } else if (value === "2") {
                this.form.taxRate = 0;
              }
            },
          },
          {
            label: "税率(%)",
            prop: "taxRate",
            minWidth: 65,
            placeholder: " ",
            type: "number",
            precision: 2,
            controls: false,
            hide: true,
            span: 6,
          },
          {
            label: "备注",
            prop: "remark",
            type: "textarea",
            minRows: 2,
            span: 24,
            overHidden: true,
            minWidth: 110,
          },
          {
            label: "附件",
            type: "upload",
            propsHttp: {
              res: "data",
              url: "attachId",
              name: "originalName",
            },
            action:
              "/api/blade-resource/oss/endpoint/put-file-attach?code=public",
            display: true,
            span: 24,
            showFileList: true,
            multiple: true,
            limit: 10,
            prop: "attachment",
          },
          {
            label: "实物信息",
            prop: "items",
            labelPosition: "top",
            display: false,
            hide: true,
            span: 24,
            showColumn: false,
          },
        ],
      },
      item: {
        selectionList: [],
        option: {
          cellBtn: true,
          addBtn: false,
          refreshBtn: false,
          columnBtn: false,
          menu: false,
          saveBtn: false,
          updateBtn: false,
          cancelBtn: false,
          editBtn: false,
          delBtn: false,
          dialogFullscreen: true,
          size: "mini",
          align: "center",
          calcHeight: 30,
          tip: false,
          border: true,
          viewBtn: false,
          dialogClickModal: false,
          selection: true,
          showSummary: true,
          sumColumnList: [
            {
              name: "num",
              type: "sum",
              decimals: 1,
            },
            {
              name: "amount",
              type: "sum",
            },
          ],
          column: [
            {
              label: "编码",
              minWidth: 100,
              placeholder: " ",
              prop: "materialCode",
              overHidden: true,
              clearable: false,
              rules: [
                {
                  required: true,
                  message: "请输入",
                  trigger: "blur",
                },
              ],
            },
            {
              label: "品名",
              prop: "materialName",
              placeholder: " ",
              disabled: true,
              overHidden: true,
            },
            {
              label: "规格",
              prop: "specification",
              placeholder: " ",
              overHidden: true,
              disabled: true,
            },
            {
              label: "材质",
              prop: "quality",
              placeholder: " ",
              disabled: true,
              overHidden: true,
            },
            {
              label: "国标",
              prop: "gb",
              placeholder: " ",
              disabled: true,
              overHidden: true,
            },
            {
              label: "单位",
              prop: "unit",
              type: "select",
              dicUrl:
                "/api/blade-system/dict-biz/dictionary?code=ni_material_unit",
              props: {
                label: "dictValue",
                value: "dictKey",
              },
              placeholder: " ",
              slot: true,
              rules: [
                {
                  required: true,
                  message: "请选择单位",
                  trigger: "blur",
                },
              ],
            },
            {
              label: "数量",
              prop: "num",
              type: "number",
              precision: 0,
              placeholder: " ",
              minWidth: 100,
              cell: true,
              rules: [
                {
                  required: true,
                  message: "请输入数量",
                  trigger: "blur",
                },
              ],
            },
            {
              label: "单价",
              prop: "price",
              type: "number",
              controls: false,
              disabled: true,
              precision: 2,
              placeholder: " ",
            },
            {
              label: "金额",
              prop: "amount",
              overHidden: true,
              type: "number",
              cell: true,
              minWidth: 100,
              precision: 2,
              placeholder: " ",
              rules: [
                {
                  required: true,
                  message: "请输入金额",
                  trigger: "blur",
                },
              ],
            },
            {
              label: "备注",
              prop: "remark",
              type: "textarea",
              minRows: 1,
              placeholder: " ",
              cell: true,
            },
          ],
        },
      },
      process: {},
      loading: false,
      payment: 0,
      financeSearch: false,
      financeVisible: false,
      financeOption: {
        refreshBtn: false,
        columnBtn: false,
        menu: false,
        highlightCurrentRow: true,
        size: "mini",
        searchSize: "mini",
        align: "center",
        searchIndex: 3,
        searchIcon: true,
        calcHeight: 30,
        tip: false,
        border: true,
        viewBtn: true,
        reserveSelection: true,
        labelWidth: 120,
        dialogClickModal: false,
        addBtn: false,
        delBtn: false,
        editBtn: false,
        column: [
          {
            label: "",
            prop: "radio",
            type: "radio",
            width: 55,
          },
          {
            label: "开户银行",
            prop: "financeBank",
            minWidth: 110,
            placeholder: " ",
          },
          {
            label: "收款人全称",
            prop: "financeName",
            placeholder: " ",
          },
          {
            label: "银行账号",
            prop: "financeAccount",
            placeholder: " ",
            minWidth: 180,
          },
          {
            label: "付款方式",
            prop: "payType",
            placeholder: " ",
            minWidth: 120,
          },
          {
            label: "发票类型",
            prop: "bill",
            placeholder: " ",
            minWidth: 120,
          },
          {
            label: "备注",
            prop: "remark",
            placeholder: " ",
            minWidth: 120,
          },
        ],
      },
      financeSelectionList: [],
      financeForm: {},
      financeData: [],
      itemDialogShow: false,
      isDraftPopupVisible: false,
      draftList: [],
      draftCount: 0,
      draftId: null,
    };
  },
  methods: {
    handleBudgetConfirm(selectionList) {
      if (selectionList) {
        this.form.budgetSerialNo = selectionList[0].serialNo;
        this.form.budgetType = selectionList[0].type;
      }
    },
    handleFinanceSelect(selectionList) {
      this.form.financeName = selectionList[0].financeName;
      this.form.financeAccount = selectionList[0].financeAccount;
      this.form.bill = selectionList[0].bill;
    },
    beforeAdjustSelect(done) {
      if (!this.form.brand) {
        this.$message({
          type: "warning",
          message: "请选择账套!",
        });
        return;
      }
      if (!this.form.supplierId) {
        this.$message({
          type: "warning",
          message: "请选择供应商!",
        });
        return;
      }
      done();
    },
    handleAdjustChange(selection) {
      if (selection) {
        const adjust = selection[0];
        this.form.adjustAmount = adjust.amount;
        this.form.adjustType = adjust.subType;
        this.form.unPayAmount = this.getUnPayAmountWithoutAdjust(this.form);
        //调增
        if (adjust.subType === "1") {
          this.form.unPayAmount += Number(adjust.amount);
          //调减
        } else if (adjust.subType === "2") {
          this.form.unPayAmount -= Number(adjust.amount);
        }
        this.form.amount = this.form.unPayAmount;
        this.form.upperAmount = numToCapital(this.form.amount);
      }
    },
    getUnPayAmountWithoutAdjust(form) {
      let amount = 0;
      if (this.form.type === "1") {
        amount = Number(form.contractAmount);
      } else if (this.form.type === "2") {
        amount = Number(form.paySoaAmount);
      } else if (this.form.type === "3") {
        if (form.costAmount) amount = Number(form.costAmount);
        else {
          form.items.forEach((item) => {
            amount += Number(item.amount);
          });
        }
      } else if (this.form.type === "5") {
        amount = Number(form.porOrderAmount);
      }
      return amount;
    },
    handleCostApplyChange(row) {
      if (row && row.value)
        getCostApplyDetail(row.value).then((res) => {
          const data = res.data.data;
          this.form.budgetId = data.budgetId;
          this.form.supplierId = data.supplierId;
          this.form.brand = data.brand;
          this.form.amount = data.amount;
          this.form.upperAmount = numToCapital(this.form.amount);
          this.form.items = data.items.map((item) => {
            return {
              ...item,
              id: null,
              applyId: null,
              cost: true,
              type: "9",
            };
          });
        });
    },
    handleSumAmount(amount) {
      if (["1", "3"].includes(this.form.type)) {
        this.form.costAmount = amount;
        if (
          this.form.adjust &&
          this.form.adjustId &&
          this.form.adjustType === "1" &&
          this.form.adjustAmount
        ) {
          this.form.amount = amount + Number(this.form.adjustAmount);
        } else if (
          this.form.adjust &&
          this.form.adjustId &&
          this.form.adjustType === "2" &&
          this.form.adjustAmount
        ) {
          this.form.amount = amount - Number(this.form.adjustAmount);
        } else this.form.amount = amount;
        this.form.upperAmount = numToCapital(this.form.amount);
      }
    },
    sumAmount() {
      this.$nextTick(() => {
        const itemAmount = this.form.items.reduce((acc, cur) => {
          return Number(acc) + Number(cur.amount);
        }, 0);
        this.form.amount = Number(itemAmount.toFixed(2));
        if (
          this.form.adjust &&
          this.form.adjustId &&
          this.form.adjustType === "1" &&
          this.form.adjustAmount
        ) {
          this.form.amount = itemAmount + Number(this.form.adjustAmount);
        } else if (
          this.form.adjust &&
          this.form.adjustId &&
          this.form.adjustType === "2" &&
          this.form.adjustAmount
        ) {
          this.form.amount = itemAmount - Number(this.form.adjustAmount);
        } else this.form.amount = itemAmount;
        this.form.upperAmount = numToCapital(this.form.amount);
      });
    },
    costItemDelete() {
      if (!this.item.selectionList || this.item.selectionList.length <= 0) {
        this.$message.warning("请选择要删除的数据");
        return;
      }
      const indexList = this.item.selectionList.map((item) => item.$index);
      const [...items] = this.form.items.filter(
        (item, index) => !indexList.includes(index)
      );
      this.form.items = items;
      this.sumAmount();
    },
    costItemAdd() {
      if (
        this.form.type === "3" &&
        !this.form.costApplyId &&
        !this.form.budgetId
      ) {
        this.$message.warning("请选择费用/预算");
        return;
      }
      if (
        this.form.type === "1" &&
        this.form.contractType === "9" &&
        !this.form.budgetId
      ) {
        this.form.items.push({
          cost: 1,
          $cellEdit: true,
        });
      } else if (this.form.type === "3" && this.form.costApplyId) {
        this.$refs.costApplyItemRef.visible = true;
      } else this.$refs.porBudgetCostItemRef.init(this.form.budgetId);
    },
    handleCostApplySelectConfirm(selectionList) {
      selectionList.forEach((item) => {
        this.form.items.push({
          budgetId: item.budgetId,
          budgetItemId: item.budgetItemId,
          costApplyItemId: item.id,
          materialName: item.materialName,
          materialCode: item.materialCode,
          specification: item.specification,
          quality: item.quality,
          unit: item.unit,
          num: item.num,
          amount: item.amount,
          remark: item.remark,
          cost: true,
          budgetSerialNo: item.serialNo,
          gb: item.gb,
          price: item.price,
          $cellEdit: false,
        });
      });
      this.sumAmount();
    },
    handlePaySoaSubmit(selectList) {
      if (selectList && selectList.length > 0) {
        getPaySoaDetail(selectList[0].id).then((res) => {
          const data = res.data.data;
          //对账类型
          this.form.soaType = data.type;
          this.form.paySoaAmount = data.amount;
          this.form.unPayAmount =
            Number(data.amount) - Number(data.payApplyAmount);
          if (this.form.adjust === 1)
            this.form.unPayAmount -= Number(
              this.form.adjustAmount ? this.form.adjustAmount : 0
            );
          this.form.amount = this.form.unPayAmount;
          this.form.upperAmount = numToCapital(this.form.unPayAmount);
          this.form.billType = data.billType;
          this.form.taxRate = data.taxRate;
          const items = [];
          if (data.items) {
            data.items.forEach((item) => {
              const i = {
                ...item,
                type: data.type === "1" ? "1" : item.type,
                supplierName: item.supplier,
              };
              if (data.type === "1" || (data.type == "2" && item.type == "1"))
                i.porOrderItemId = item.businessId;
              if (data.type === "2" && item.type === "2")
                i.purchaseBackItemId = item.businessId;
              if (data.type === "2" && item.type === "3")
                i.alipayAdjustmentId = item.businessId;
              i.id = null;
              items.push(i);
            });
          }
          if (data.backItems) {
            data.backItems.forEach((item) => {
              items.push({
                ...item,
                type: "2",
                num: -Math.abs(Number(item.num)),
                amount: -Math.abs(Number(item.amount)),
                supplierName: item.supplier,
                purchaseBackItemId: item.id,
                id: null,
              });
            });
          }
          this.form.items = items;
        });
      }
    },
    handleAmount(value) {
      if (value) {
        this.form.upperAmount = numToCapital(value);
      }
    },
    handleBudgetClear() {
      this.form.budgetId = null;
      this.form.items = [];
    },
    handleCostItemSelect(selectionList) {
      selectionList.forEach((item) => {
        this.form.items.push({
          budgetSerialNo: item.serialNo,
          budgetId: item.budgetId,
          budgetItemId: item.id,
          materialName: item.materialName,
          materialCode: item.materialCode,
          specification: item.specification,
          quality: item.quality,
          gb: item.gb,
          unit: item.unit,
          usedNum: item.applyNum ? item.applyNum : 0,
          budgetNum: item.num,
          num: item.cost ? 1 : null,
          remark: item.remark,
          cost: item.cost,
        });
      });
    },
    dictInit() {
      const typeColumn = this.findObject(this.option.column, "type");
      const brandColumn = this.findObject(this.option.column, "brand");
      this.$http
        .get(
          "/api/blade-system/dict-biz/dictionary?code=ni_fin_payable_apply_type"
        )
        .then((res) => {
          const data = res.data.data;
          data.forEach((item) => {
            if (item.dictKey === "1" || item.dictKey === "9") {
              item.disabled = true;
            }
          });
          typeColumn.dicData = data;
        });
      this.$http
        .get("/api/blade-system/dict-biz/dictionary?code=ni_brand")
        .then((res) => {
          brandColumn.dicData = res.data.data;
        });
    },
    async handleContractIdConfirm(selectList) {
      if (selectList != null && selectList.length > 0) {
        const contract = selectList[0];
        this.form.contractType = contract.type;
        this.form.contractAmount = contract.orderAmount
          ? contract.orderAmount
          : contract.amount;
        this.form.unPayAmount =
          Number(
            contract.unPayAmount ? contract.unPayAmount : contract.amount
          ) - Number(contract.payApplyAmount);
        if (this.form.adjust === 1)
          this.form.unPayAmount -= Number(
            this.form.adjustAmount ? this.form.adjustAmount : 0
          );
        //只有合同付款才会加载
        if (this.form.contractId && this.form.type === "1") {
          const itemRes = await getItemList({
            contractId: this.form.contractId,
          });
          this.form.items = itemRes.data.data
            .filter((item) => item.payType === "4")
            .map((item) => {
              return {
                ...item,
                price: (item.amount / item.num).toFixed(2),
                porOrderItemId: item.id,
                id: null,
              };
            });
        }
      }
    },
    handleFinanceConfirm() {
      this.form.financeBank = this.financeSelectionList[0].financeBank;
      this.form.financeName = this.financeSelectionList[0].financeName;
      this.form.financeAccount = this.financeSelectionList[0].financeAccount;
      this.form.payType = this.financeSelectionList[0].payType;
      this.form.bill = this.financeSelectionList[0].bill;
      this.financeVisible = false;
    },
    rowClick(row) {
      this.financeSelectionList = [row];
      this.$set(this.form, "radio", row.$index);
    },
    handleSupplierSubmit(selectList) {
      this.form.supplier = selectList[0].name;
      this.form.financeBank = null;
      this.form.financeName = null;
      this.form.financeAccount = null;
      //选择付款账户
      this.buildFinance(selectList[0].id);
      if (this.form.type !== "3") {
        this.form.porOrderId = null;
        this.form.porOrderSerialNo = null;
        this.form.porOrderAmount = null;
        this.form.contractId = null;
        this.form.contractAmount = null;
        this.form.unPayAmount = null;
        this.form.budgetId = null;
        this.form.paySoaAmount = null;
        this.form.paySoaId = null;
        this.form.amount = null;
        this.form.upperAmount = null;
        this.form.items = [];
      }
    },
    buildFinance(supplierId) {
      getSupplierDetail(supplierId).then((res) => {
        const data = res.data.data;
        const finance = data.finance;
        if (finance && finance.length === 1) {
          this.form.financeBank = finance[0].financeBank;
          this.form.financeName = finance[0].financeName;
          this.form.payType = finance[0].payType;
          this.form.bill = finance[0].bill;
          this.form.financeAccount = finance[0].financeAccount;
        } else if (finance && finance.length > 1) {
          this.financeData = finance;
          this.financeVisible = true;
        }
      });
    },
    beforeContractSelect(done) {
      if (!this.form.brand) {
        this.$message({
          type: "warning",
          message: "请选择账套!",
        });
        return;
      }
      if (!this.form.supplierId) {
        this.$message({
          type: "warning",
          message: "请选择供应商!",
        });
        return;
      }
      done();
    },
    handleOrderClear() {
      this.form.porOrderSerialNo = null;
      this.form.amount = null;
      this.form.porOrderAmount = null;
      this.form.items = [];
    },
    async handleOrderSubmit(selectList) {
      this.form.currency = selectList[0].currency
        ? selectList[0].currency
        : "RMB";
      this.form.porOrderAmount = selectList.reduce((acc, cur) => {
        return acc + Number(cur.amount);
      }, 0);
      const itemRes = await getOrderItemList({
        orderIds: selectList.map((item) => item.id).join(","),
      });
      const items = itemRes.data.data;
      items.forEach(
        (item) =>
          (item.price = (Number(item.amount) / Number(item.num)).toFixed(2))
      );
      this.form.items = items.map((item) => {
        return {
          ...item,
          porOrderItemId: item.id,
          id: null,
        };
      });
      this.form.unPayAmount = selectList.reduce((acc, cur) => {
        return acc + Number(cur.amount) - Number(cur.payApplyAmount);
      }, 0);
      if (this.form.adjust === 1)
        this.form.unPayAmount -= Number(
          this.form.adjustAmount ? this.form.adjustAmount : 0
        );
    },
    getForm(processId) {
      this.getStartForm(processId).then((res) => {
        const _this = this;
        let { process } = res;
        this.form.processId = process.id;
        const option = _this.option;
        const { column, group } = option;
        column.forEach((col) => {
          if (col.value) col.value = _this.getDefaultValues(col.value);
        });
        if (group && group.length > 0) {
          // 处理group
          group.forEach((gro) => {
            gro.column.forEach((col) => {
              if (col.value) col.value = _this.getDefaultValues(col.value);
            });
          });
        }
        if (_this.permission.wf_process_draft) {
          // 查询草稿箱
          this.initDraft(process.id, process.key).then((data) => {
            this.draftCount = data.length;
            this.draftList = data;
            if (data && Array.isArray(data) && data.length > 0) {
              _this
                .$confirm("是否引用之前保存的草稿？", "提示", {})
                .then(() => {
                  this.isDraftPopupVisible = true; // 打开草稿弹窗
                });
            }
          });
        }
        _this.waiting = false;
      });
    },
    getFormByProcessDefKey(processDefKey) {
      this.getStartFormByProcessDefKey(processDefKey).then((res) => {
        const _this = this;
        let { process } = res;
        _this.form.processId = process.id;
        const option = _this.option;
        const { column, group } = option;
        column.forEach((col) => {
          if (col.value) col.value = _this.getDefaultValues(col.value);
        });
        if (group && group.length > 0) {
          // 处理group
          group.forEach((gro) => {
            gro.column.forEach((col) => {
              if (col.value) col.value = _this.getDefaultValues(col.value);
            });
          });
        }
        if (_this.permission.wf_process_draft) {
          // 查询草稿箱
          _this.initDraft(process.id, process.key).then((data) => {
            _this.draftCount = data.length;
            _this.draftList = data;
            if (data && Array.isArray(data) && data.length > 0) {
              _this
                .$confirm("是否引用之前保存的草稿？", "提示", {})
                .then(() => {
                  _this.isDraftPopupVisible = true; // 打开草稿弹窗
                });
            }
          });
        }
        console.log(_this.form);
        _this.waiting = false;
      });
    },
    validateItems(items) {
      return (
        !items ||
        !items.some((item) => (!item.num || !item.amount) && item.materialName)
      );
    },
    handleSubmit: debounce(function () {
      this.loading = true;
      if (!this.form.items || this.form.items.length === 0) {
        this.$message.warning("未填写明细，请填写明细后再提交");
        this.loading = false;
        return;
      }
      if (this.form.type === "3") {
        //校验
        const items = this.validateItems(this.form.items);
        if (!items) {
          this.$message.warning("明细中存在未填写的数量/金额");
          this.loading = false;
          return;
        }
      }
      //保存再提交
      this.form.draftId = this.draftId;
      this.handleStartProcess(true)
        .then((done) => {
          this.$message.success("发起成功");
          if (this.draftId != null) {
            this.draftCount = this.draftCount - 1;
            this.draftList = this.draftList.filter(
              (item) => item.id !== this.draftId
            );
          }
          if (this.fromPath) {
            this.handleCloseTag(this.fromPath);
          } else this.handleCloseTag("/ni/fin/payable-apply");
          done();
        })
        .catch(() => {
          this.loading = false;
        });
    }, 1000),

    //选择草稿
    handleDraftSelect(selectedDraft) {
      //草稿版本与流程版本不一致
      if (!selectedDraft.sameVersion) {
        this.$confirm(
          "选中的草稿与当前流程版本不一致，是否继续引用？",
          "提示",
          {}
        ).then(() => {
          this.draftId = selectedDraft.id;
          this.form = JSON.parse(selectedDraft.variables);
          this.isDraftPopupVisible = false;
        });
      } else {
        this.draftId = selectedDraft.id;
        this.form = JSON.parse(selectedDraft.variables);
        this.form.draftId = selectedDraft.id;
      }
    },
    //删除草稿
    handleDraftDelete(draftId) {
      this.$confirm("是否删除选中的草稿箱数据？", "提示", {}).then(() => {
        this.$axios
          .post(`/api/blade-workflow/process/draft/delete/${draftId}`)
          .then((response) => {
            this.$message.success("草稿删除成功");
            this.draftCount = this.draftCount - 1;
            this.draftList = this.draftList.filter(
              (item) => item.id !== draftId
            );
          })
          .catch((error) => {
            this.$message.error("草稿删除失败，请重试");
          });
      });
    },
    handleDraftBox() {
      if (this.draftList.length > 0) {
        this.isDraftPopupVisible = true;
      } else {
        // 重新获取草稿数据
        this.initDraft(this.form.processId).then((data) => {
          this.draftCount = data.length;
          this.draftList = data;
          if (data && Array.isArray(data) && data.length > 0) {
            this.isDraftPopupVisible = true;
          }
        });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.foot-item {
  position: fixed;
  bottom: 0;
  margin-left: -20px;
  // right: 0;
  z-index: 101;
  height: 66px;
  background-color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  -webkit-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
</style>

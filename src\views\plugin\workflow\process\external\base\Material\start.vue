<template>
  <basic-container>
    <avue-skeleton :loading="waiting" avatar :rows="8">
      <avue-title
        style="margin-bottom: 20px"
        :styles="{ fontSize: '20px' }"
        :value="process.name"
      ></avue-title>
      <el-card shadow="never" style="margin-top: 20px">
        <!-- 自定义表单区域 -->
        <avue-form
          v-if="
            option &&
            ((option.column && option.column.length > 0) ||
              (option.group && option.group.length > 0))
          "
          v-model="form"
          ref="form"
          :option="option"
          :defaults.sync="defaults"
          @submit="handleSubmit"
          @error="loading = false"
          :upload-preview="handleUploadPreview"
        >
        </avue-form>
        <!-- 自定义表单区域 -->
      </el-card>

      <el-card shadow="never" style="margin-top: 20px" v-if="showExamForm">
        <wf-examine-form
          ref="examineForm"
          :process="process"
          @user-select="handleUserSelect"
        ></wf-examine-form>
      </el-card>
      <div style="height: 120px"></div>
      <el-row
        class="foot-item avue-affix"
        :style="{
          width: isCollapse ? 'calc(100% - 80px)' : 'calc(100% - 260px)',
        }"
        id="avue-view"
      >
        <el-button
          type="primary"
          size="mini"
          v-loading="loading"
          @click="handleSubmit"
          >发起
        </el-button>
        <el-button
            v-if="permission.wf_process_draft"
            type="success"
            size="mini"
            v-loading="loading"
            @click="handleDraftNotClose(process.id, process.formKey, form)"
        >存为草稿
        </el-button>
        <el-button
            v-if="permission.wf_process_draft"
            type="success"
            size="mini"
            v-loading="loading"
            @click="handleDraft(process.id, process.formKey, form)"
        >存为草稿并关闭
        </el-button>
      </el-row>
    </avue-skeleton>

    <!-- 人员选择弹窗 -->
    <wf-user-select
      ref="user-select"
      :check-type="checkType"
      :default-checked="defaultChecked"
      @onConfirm="handleUserSelectConfirm"
    ></wf-user-select>
  </basic-container>
</template>

<script>
import WfExamineForm from "@/views/plugin/workflow/process/components/examForm.vue";
import WfUserSelect from "@/views/plugin/workflow/process/components/user-select";

import exForm from "@/views/plugin/workflow/mixins/ex-form";
import draft from "@/views/plugin/workflow/mixins/draft";
import debounce from "@/util/debounce";

export default {
  components: {
    WfUserSelect,
    WfExamineForm,
  },
  mixins: [exForm, draft],
  watch: {
    "$route.query.p": {
      handler(val) {
        if (val) {
          const param = JSON.parse(Buffer.from(val, "base64").toString());
          const { processId, processDefKey, form } = param;
          if (processId) {
            this.getForm(processId);
          } else if (processDefKey) {
            this.getFormByProcessDefKey(processDefKey);
          }
          if (form) {
            this.form = { ...form };
          }
        }
      },
      immediate: true,
    },
  },
  computed: {
    showExamForm() {
      const { hideComment, hideCopy, hideExamine } = this.process;
      return !hideComment || !hideCopy || !hideExamine;
    },
  },
  data() {
    return {
      defaults: {},
      form: {},
      option: {
        labelWidth: 120,
        calcHeight: 30,
        size: "mini",
        span: 8,
        menuBtn: false,
        column: [
          {
            type: "input",
            label: "创建人",
            display: true,
            prop: "createUserName",
            value: "${this.$store.getters.userInfo.nick_name}",
            readonly: true,
          },
          {
            type: "input",
            label: "创建部门",
            display: true,
            prop: "createDeptName",
            value: "${this.$store.getters.userInfo.dept_name}",
            readonly: true,
          },
          {
            label: "类目",
            prop: "typeId",
            type: "tree",
            overHidden: true,
            dicUrl: "/api/ni/base/material/type/tree",
            parent: false,
            props: {
              label: "name",
              value: "id",
              desc: "code",
            },
            width: 120,
            slot: true,
            rules: [
              {
                required: true,
                message: "请选择物料分类",
                trigger: "blur",
              },
            ],
          },
          {
            label: "品名",
            prop: "name",
            overHidden: true,
            search: true,
            searchOrder: 99,
            rules: [
              {
                required: true,
                message: "请输入",
                trigger: "blur",
              },
            ],
          },
          {
            label: "编码",
            prop: "code",
            overHidden: true,
            placeholder: "系统自动生成",
            disabled: true,
          },
          {
            label: "规格",
            prop: "specification",
            overHidden: true,
            search: true,
          },
          {
            label: "材质",
            prop: "quality",
            overHidden: true,
            search: true,
          },
          {
            label: "国标",
            prop: "gb",
            search: true,
            overHidden: true,
          },
          {
            label: "单位",
            prop: "unit",
            overHidden: true,
            type: "select",
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_material_unit",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            width: 120,
            slot: true,
            rules: [
              {
                required: true,
                message: "请输入单位",
                trigger: "blur",
              },
            ],
          },
          {
            label: "是否同步用友",
            prop: "sync",
            type: "radio",
            dicData: [
              {
                label: "是",
                value: true,
              },
              {
                label: "否",
                value: false,
              },
            ],
            rules: [
              {
                required: true,
                message: "请选择是否同步用友",
                trigger: "blur",
              },
            ],
          },
          {
            label: "备注",
            prop: "remark",
            overHidden: true,
            type: "textarea",
            span: 24,
            minRows: 3,
          },
        ],
      },
      process: {},
      loading: false,
    };
  },
  methods: {
    getForm(processId) {
      this.getStartForm(processId).then((res) => {
        const _this = this;
        let { process } = res;
        this.form.processId = process.id;
        const option = _this.option;
        const { column, group } = option;
        column.forEach((col) => {
          if (col.value) col.value = _this.getDefaultValues(col.value);
        });
        if (group && group.length > 0) {
          // 处理group
          group.forEach((gro) => {
            gro.column.forEach((col) => {
              if (col.value) col.value = _this.getDefaultValues(col.value);
            });
          });
        }
        //
        // const groupArr = []
        // const columnArr = this.filterAvueColumn(column, startForm, true).column
        // if (group && group.length > 0) { // 处理group
        //   group.forEach(gro => {
        //     gro.column = this.filterAvueColumn(gro.column, startForm, true).column
        //     if (gro.column.length > 0) groupArr.push(gro)
        //   })
        // }
        //
        // option.column = columnArr
        // option.group = groupArr
        // this.option = option

        if (_this.permission.wf_process_draft) {
          // 查询是否有草稿箱
          _this.initDraft(process.id).then((data) => {
            _this
              .$confirm("是否恢复之前保存的草稿？", "提示", {})
              .then(() => {
                _this.form = JSON.parse(data);
              })
              .catch(() => {});
          });
        }
        _this.waiting = false;
      });
    },
    getFormByProcessDefKey(processDefKey) {
      this.getStartFormByProcessDefKey(processDefKey).then((res) => {
        const _this = this;
        let { process } = res;
        _this.form.processId = process.id;
        const option = _this.option;
        const { column, group } = option;
        column.forEach((col) => {
          if (col.value) col.value = _this.getDefaultValues(col.value);
        });
        if (group && group.length > 0) {
          // 处理group
          group.forEach((gro) => {
            gro.column.forEach((col) => {
              if (col.value) col.value = _this.getDefaultValues(col.value);
            });
          });
        }
        if (_this.permission.wf_process_draft) {
          // 查询是否有草稿箱
          _this.initDraft(process.id).then((data) => {
            _this
              .$confirm("是否恢复之前保存的草稿？", "提示", {})
              .then(() => {
                _this.form = JSON.parse(data);
              })
              .catch(() => {});
          });
        }
        _this.waiting = false;
      });
    },
    handleSubmit:debounce(function (){
      console.log(this.form);
      console.log(this.form.tag == null);
      console.log(this.form.tag == []);
      //保存再提交
      if (this.form.tag == null || this.form.tag == []) {
        console.log(1);
        this.form.tag = "";
      } else {
        console.log(12);
        this.form.tag = this.form.tag.join(",");
      }
      this.handleStartProcess(true)
        .then((done) => {
          this.$message.success("发起成功");
          this.handleCloseTag("/plugin/workflow/process/send");
          done();
        })
        .catch(() => {
          this.loading = false;
        });
    },1000)
  },
};
</script>

<style lang="scss" scoped>
.foot-item {
  position: fixed;
  bottom: 0;
  margin-left: -20px;
  // right: 0;
  z-index: 101;
  height: 66px;
  background-color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  -webkit-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
</style>

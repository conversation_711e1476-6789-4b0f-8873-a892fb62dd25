import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/ni/pa/paAttendanceGroup/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/ni/pa/paAttendanceGroup/detailVO',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/ni/pa/paAttendanceGroup/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/ni/pa/paAttendanceGroup/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/ni/pa/paAttendanceGroup/submit',
    method: 'post',
    data: row
  })
}

export const getUserList = (id) => {
  return request({
    url: '/api/ni/pa/paAttendanceGroup/transferUserList',
    method: 'get',
    params: {
      id
    }
  })
}

export const changeGroupUser = (userGroupVO) => {
  return request({
    url: '/api/ni/pa/paAttendanceGroup/changeGroupUser',
    method: 'post',
    data: userGroupVO
  })
}


```java
package com.natergy.ni.feedback.params;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 问题反馈搜索参数类
 * （封装问题反馈列表查询时的各种筛选条件和分页、排序参数）
 *
 * <AUTHOR>
 * @since 2025-03-03
 */
// @Data：Lombok注解，自动生成getter、setter、toString等方法，简化代码
@Data
public class FeedbackParams {

	/**
	 * 创建日期区间
	 * （存储开始日期和结束日期，格式通常为"yyyy-MM-dd"，用于筛选特定时间段内创建的问题）
	 */
	@ApiModelProperty(value = "创建日期区间")
	private List<String> createDate;

	/**
	 * 问题状态列表
	 * （存储多个状态值，对应FeedbackStatusEnum枚举，用于筛选符合指定状态的问题，如待处理、已解决等）
	 */
	@ApiModelProperty(value = "问题状态")
	private List<Integer> feedbackStatuss;

	/**
	 * 是否属于"十不放过"
	 * （true：是"十不放过"问题；false：不是"十不放过"问题，用于专项筛选）
	 */
	@ApiModelProperty(value = "是否 十不放过 true代表是  false代表不是")
	private Boolean isTenNonNeglect;

	/**
	 * 排序方式
	 * （1：按创建时间降序排序（最新创建优先）；2：按创建时间升序排序（最早创建优先））
	 */
	@ApiModelProperty(value = "排序方式，1：最新创建，2：最晚创建")
	private Integer sortBy;

	/**
	 * 数据权限筛选标识
	 * （1：筛选当前用户负责的问题；2：筛选当前用户创建的问题，用于数据权限控制）
	 */
	@ApiModelProperty(value = "1：我负责的 2：我创建的")
	private Integer responsibilityOrOwer;

	/**
	 * 当前用户ID
	 * （结合responsibilityOrOwer使用，用于定位当前用户的负责或创建的问题）
	 */
	@ApiModelProperty(value = "当前用户id")
	private Long currentUserId;

	/**
	 * 是否可以查看所有用户的反馈
	 * （true：管理员权限，可查看所有问题；false：普通用户权限，仅查看有权限的问题）
	 */
	@ApiModelProperty(value = "是否可以查看所有用户反馈")
	private Boolean canAllUser;

	/**
	 * 问题ID
	 * （用于精确筛选某个特定问题，通常在查询单个问题的详情或关联记录时使用）
	 */
	@ApiModelProperty(value = "问题的id")
	private Long feedbackId;

	/**
	 * 向量点ID列表
	 * （关联向量数据库中的点数据，用于筛选与特定向量点相关的问题，支持批量筛选）
	 */
	@ApiModelProperty(value = "pointIds")
	private List<Integer> pointIds;

}
```

#### 技术特点

- **参数聚合**：将分散的查询条件聚合到一个类中，避免接口方法参数过多，提高代码可读性。
- **类型安全**：通过明确的字段类型（如`List<String>`、`Boolean`）约束参数格式，减少类型转换错误。
- **文档化**：使用`@ApiModelProperty`注解说明每个字段的含义，结合 Swagger 可自动生成 API 文档，方便前后端协作。
- **扩展性**：新增查询条件时，只需在该类中添加字段，无需修改接口方法定义，符合开闭原则。

#### 应用场景

该类主要在问题反馈列表查询接口（如`IFeedbackService.page(FeedbackParams params)`）中使用，服务层通过解析这些参数，构建对应的 MyBatis 查询条件（如动态 SQL 中的`WHERE`子句），最终返回符合条件的问题列表。

例如：

- 普通用户查询 “自己创建的、状态为待处理的问题” 时，`responsibilityOrOwer=2`、`feedbackStatuss=[1]`、`currentUserId=xxx`。
- 管理员查询 “2025 年 1 月内的所有十不放过问题” 时，`createDate=["2025-01-01", "2025-01-31"]`、`isTenNonNeglect=true`、`canAllUser=true`。

通过该类的参数传递，实现了灵活、安全、可扩展的问题反馈查询功能。
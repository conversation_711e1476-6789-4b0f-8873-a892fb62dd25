<template>
  <el-container>
    <el-aside class="aside_main" :class="{ aside_main_show: !asideStatus }">
      <div class="box">
        <el-scrollbar style="height: 100%">
          <basic-container>
            <!-- 左侧搜索框 -->
            <el-input
              placeholder="请输入内容"
              v-model="materialName"
              class="tree-search"
              @keyup.enter.native="handleMaterialSearch"
            >
              <el-button
                slot="append"
                icon="el-icon-search"
                @click="handleMaterialSearch"
              ></el-button>
            </el-input>
            <el-divider></el-divider>
            <avue-tree
              ref="typeTree"
              :option="treeOption"
              :data="treeData"
              @node-click="typeClick"
            />
          </basic-container>
        </el-scrollbar>
        <!-- 下方继续搜索 -->
        <div v-if="goSearchVisible">
          <span
            >总共找到{{ this.materialInfoList.length }}条数据， 当前第{{
              this.searchIndex + 1
            }}条</span
          >
          <el-button
            size="medium"
            type="success"
            plain
            icon="el-icon-d-arrow-right"
            @click="goSearch"
            >继续搜索
          </el-button>
        </div>
      </div>
    </el-aside>
    <el-container>
      <el-main class="main_cont">
        <!-- 左侧按钮 -->
        <div class="aside_open_close" @click="asidechange">
          <i class="el-icon-arrow-left" v-if="aside_open_close"></i>
          <i class="el-icon-arrow-right" v-else></i>
        </div>
        <basic-container>
          <avue-crud
            :option="option"
            :table-loading="loading"
            :data="data"
            :search.sync="query"
            :page.sync="page"
            :permission="permissionList"
            :before-open="beforeOpen"
            v-model="form"
            ref="crud"
            @row-update="rowUpdate"
            @row-save="rowSave"
            @row-del="rowDel"
            @current-row-change="handleCurrentRowChange"
            @search-change="searchChange"
            @search-reset="searchReset"
            @selection-change="selectionChange"
            @current-change="currentChange"
            @size-change="sizeChange"
            @refresh-change="refreshChange"
            @on-load="onLoad"
            @row-click="rowClick"
          >
            <template #cost="{ row, index }">
              <el-tag size="mini" type="danger" effect="dark" v-if="row.cost">
                费用
              </el-tag>
              <el-tag size="mini" type="info" effect="plain" v-else>
                实物
              </el-tag>
            </template>
            <template #typeName="{ row, index }">
              <span>{{ row.typeName }}-{{ row.typeCode }}</span>
            </template>
            <template #status="{ row, index }">
              <el-tag v-if="row.status === 0" size="mini" type="info">
                {{ row.$status }}
              </el-tag>
              <el-tag v-else-if="row.status === 1" size="mini" effect="plain">
                {{ row.$status }}
              </el-tag>
              <el-tag v-else-if="row.status === 2" size="mini" effect="plain">
                {{ row.$status }}
              </el-tag>
              <el-tag v-else-if="row.status === 3" size="mini" type="danger">
                {{ row.$status }}
              </el-tag>
              <el-tag
                v-else-if="row.status === 4"
                size="mini"
                type="warning"
                effect="plain"
              >
                {{ row.$status }}
              </el-tag>
              <el-tag
                v-else-if="row.status === 5"
                size="mini"
                type="warning"
                effect="plain"
              >
                {{ row.$status }}
              </el-tag>
              <el-tag
                v-else-if="row.status === 6"
                size="mini"
                type="danger"
                effect="plain"
              >
                {{ row.$status }}
              </el-tag>
              <el-tag
                v-else-if="row.status === 9"
                size="mini"
                type="success"
                effect="plain"
              >
                {{ row.$status }}
              </el-tag>
              <el-tag v-else size="mini" type="warning" effect="dark">
                {{ row.$status }}
              </el-tag>
            </template>
            <template #tag="{ row, size }">
              <template v-if="row.tag">
                <el-tag
                  v-for="item in row.tag.split(',')"
                  :key="item"
                  :size="size"
                  >{{ item }}
                </el-tag>
              </template>
            </template>
            <template #disabled="{ row, size, index }">
              <el-switch
                v-if="permission.materialinfo_disabled"
                size="mini"
                v-model="row.disabled"
                active-color="red"
                @change="rowDisabledChange($event, row)"
              />
              <el-tag
                v-else-if="row.disabled"
                :size="size"
                effect="plain"
                type="danger"
              >
                是
              </el-tag>
              <el-tag v-else :size="size" effect="dark" type="info"> 否</el-tag>
            </template>
            <template #markIdForm="{ disabled, size, index }"> </template>
            <template #yonyouSync="{ row, size }">
              <span v-for="(item, index) in row.yonyouSync" :key="index">
                {{ yonyouSyncSequenceDictKeyValue[item.sequence] }}:
                <el-tag :size="size">
                  {{ yonyouSyncStateDictKeyValue[item.value] }}
                </el-tag>
                <template v-if="item.errMsg"> :{{ item.errMsg }} </template>
                <br />
              </span>
            </template>
            <template #menuLeft>
              <el-button
                type="primary"
                size="mini"
                icon="el-icon-s-promotion"
                plain
                v-if="permission.materialinfo_apply"
                @click="handleAdd"
                >编码申请
              </el-button>
              <el-button
                type="danger"
                size="mini"
                icon="el-icon-delete"
                plain
                v-if="permission.materialinfo_delete"
                @click="handleDelete"
                >删 除
              </el-button>
              <el-button
                type="warning"
                size="mini"
                icon="el-icon-upload"
                plain
                v-if="
                  permission.materialinfo_import ||
                  userInfo.role_name.includes('admin')
                "
                @click="() => (excelBox = true)"
                >导 入
              </el-button>
              <el-button
                type="danger"
                size="mini"
                icon="el-icon-refresh"
                v-if="userInfo.role_name.includes('admin')"
                plain
                @click="handleOldSync"
                >同步旧ni数据
              </el-button>
              <el-button
                type="warning"
                size="mini"
                icon="el-icon-refresh"
                plain
                v-if="userInfo.role_name.includes('admin')"
                @click="handleYonyouSync"
                >用友同步
              </el-button>
              <el-button
                type="warning"
                size="mini"
                icon="el-icon-refresh"
                plain
                v-if="userInfo.role_name.includes('admin')"
                @click="handleComparison"
                >编码匹配
              </el-button>
              <el-divider direction="vertical" />
              <el-radio-group
                v-model="dataType"
                size="mini"
                @input="handleDataTypeChange"
              >
                <el-radio-button label="all">全部</el-radio-button>
                <el-radio-button label="todo">待审核</el-radio-button>
              </el-radio-group>
            </template>
            <template #menuRight="{ size }">
              <el-button
                icon="el-icon-time"
                circle
                :size="size"
                @click="handleLog"
              ></el-button>
            </template>
            <template #menu="{ row, index, size }">
              <el-button
                v-if="
                  !row.$cellEdit &&
                  (permission.materialinfo_edit || row.status === 0)
                "
                type="text"
                size="mini"
                icon="el-icon-edit"
                @click="$refs.crud.rowEdit(row, index)"
                >编辑
              </el-button>
              <el-button
                v-if="
                  !row.$cellEdit &&
                  (permission.materialinfo_delete || row.status === 0)
                "
                type="text"
                size="mini"
                icon="el-icon-delete"
                @click="$refs.crud.rowDel(row, index)"
                >删除
              </el-button>
              <el-button
                v-if="
                  !row.$cellEdit &&
                  row.status === 0 &&
                  (userInfo.user_id === row.createUser ||
                    userInfo.role_name.includes('admin'))
                "
                type="text"
                icon="el-icon-s-promotion"
                :size="size"
                @click="rowApply(row)"
                >提交
              </el-button>
              <el-button
                v-if="!row.$cellEdit"
                type="text"
                icon="el-icon-help"
                :size="size"
                @click="rowFlow(row, index)"
                >详情
              </el-button>
              <el-divider v-if="!row.$cellEdit" direction="vertical" />
              <el-dropdown v-if="!row.$cellEdit">
                <el-button type="text" :size="size">
                  更多<i class="el-icon-arrow-down el-icon--right"></i>
                </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item @click.native="rowAttach(row)">
                    <i class="el-icon-download"></i>附件管理
                  </el-dropdown-item>
                  <el-dropdown-item
                    v-if="
                      row.status === 9 && userInfo.role_name.includes('admin')
                    "
                    @click.native="rowYonyouSync(row)"
                  >
                    <i class="el-icon-refresh"></i>同步用友
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
          </avue-crud>
        </basic-container>
        <attach-dialog ref="attachDialogRef" code="private" />
        <multiple-unit-dialog
          ref="multipleUnitRef"
          @submit="handleMultipleUnitSubmit"
        />
        <log-opt-dialog ref="logOptDialogRef" :module="module" />
        <el-drawer
          :visible.sync="detailVisible"
          :title="form.title"
          custom-class="wf-drawer"
          size="100%"
          append-to-body
        >
          <task-detail
            v-if="detailVisible"
            :taskId="form.taskId"
            :formKey="formKey"
            :option="option"
            :processInstanceId="form.processInsId"
          ></task-detail>
        </el-drawer>
        <el-dialog
          title="存货编码数据导入"
          append-to-body
          :visible.sync="excelBox"
          width="555px"
        >
          <avue-form
            :option="excelOption"
            v-model="excelForm"
            :upload-after="uploadAfter"
          >
            <template slot="excelTemplate">
              <el-button type="primary" @click="handleTemplate">
                点击下载<i class="el-icon-download el-icon--right"></i>
              </el-button>
            </template>
          </avue-form>
        </el-dialog>
        <el-dialog
          title="编码匹配"
          append-to-body
          :visible.sync="comparison.visible"
          width="555px"
        >
          <avue-form
            :option="comparison.option"
            v-model="comparison.form"
            :upload-after="comparisonUploadAfter"
          >
            <template slot="excelTemplate">
              <el-button type="primary" @click="handleComparisonTemplate">
                点击下载<i class="el-icon-download el-icon--right"></i>
              </el-button>
            </template>
          </avue-form>
        </el-dialog>
        <el-dialog
          title="同步数据"
          append-to-body
          :visible.sync="oldSync.visible"
          width="555px"
        >
          <el-descriptions :column="2" size="mini" border>
            <el-descriptions-item label="旧分类数量">
              {{ oldSync.form.oldMaterialTypeNum }}
            </el-descriptions-item>
            <el-descriptions-item label="当前分类数量">
              {{ oldSync.form.materialTypeNum }}
            </el-descriptions-item>
            <el-descriptions-item label="旧编码数量">
              {{ oldSync.form.oldMaterialNum }}
            </el-descriptions-item>
            <el-descriptions-item label="当前编码数量">
              {{ oldSync.form.materialNum }}
            </el-descriptions-item>
          </el-descriptions>
          <span slot="footer" class="dialog-footer">
            <el-button @click="() => (oldSync.visible = false)" size="mini">
              取 消
            </el-button>
            <el-button type="primary" @click="handleOldDataSync" size="mini">
              同 步
            </el-button>
          </span>
        </el-dialog>
      </el-main>
    </el-container>
    <el-dialog
      v-dialogDrag
      custom-class="my-dialog"
      class="avue-dialog avue-dialog--top my-dialog--wrapper"
      :title="type.title"
      :lock-scroll="false"
      :close-on-click-modal="false"
      :modal="false"
      :visible.sync="type.visible"
      width="300px"
    >
      <el-descriptions
        v-if="type.visible"
        class="margin-top"
        :column="2"
        size="mini"
        direction="vertical"
        border
      >
        <el-descriptions-item>
          <template slot="label"> 描述</template>
          <span style="color: #f56c6c; font-weight: bolder">
            {{ type.remark }}
          </span>
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label"> 规格举例</template>
          <span style="color: #f56c6c; font-weight: bolder">
            {{ type.specification }}
          </span>
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label"> 默认单位</template>
          <span style="color: #f56c6c; font-weight: bolder">
            {{ unitDictKv[type.unit] }}
          </span>
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label"> 分类国标</template>
          <span style="color: #f56c6c; font-weight: bolder">
            {{ type.gb }}
          </span>
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </el-container>
</template>

<script>
import {
  add,
  apply,
  getDetail,
  getPage,
  getSyncStatus,
  remove,
  sync,
  syncFromOld,
  syncYonyou,
  update,
  updateDisabledById,
} from "@/api/ni/base/material/materialinfo";
import {
  getDetail as getTypeDetail,
  getLazyList,
  searchMaterialByName,
} from "@/api/ni/base/material/type";
import { mapGetters } from "vuex";
import exForm from "@/views/plugin/workflow/mixins/ex-form";
import MultipleUnitDialog from "@/views/ni/base/components/MultipleUnitDialog";
import FlowSetDialog from "@/components/flow-set-dialog";
import { startProcess } from "@/api/plugin/workflow/process";
import AttachDialog from "@/components/attach-dialog";
import LogOptDialog from "@/components/log-opt-dialog";
import { exportBlob } from "@/api/common";
import { getToken } from "@/util/auth";
import { downloadXls } from "@/util/util";
import TaskDetail from "@/views/plugin/workflow/ops/detail";

export default {
  mixins: [exForm],
  components: {
    LogOptDialog,
    MultipleUnitDialog,
    FlowSetDialog,
    AttachDialog,
    TaskDetail,
  },
  data() {
    return {
      module: "ni_base_material",
      detailVisible: false,
      processDefKey: "process_base_material_apply",
      formKey: "wf_ex_base/Material",
      asideStatus: true, //控制左侧菜单栏显隐
      aside_open_close: true, //控制左侧菜单栏显隐
      materialName: "", //左侧搜索栏绑定值
      materialInfoList: [], //存储查询的结果
      searchIndex: 0, //存储查询下标
      goSearchVisible: false,
      treeOption: {
        filter: false,
        size: "mini",
        searchSize: "mini",
        addBtn: false,
        editBtn: false,
        delBtn: false,
        lazy: true,
        treeLoad: function (node, resolve) {
          const parentId = node.level === 0 ? 0 : node.data.id;
          getLazyList(parentId).then((res) => {
            resolve(
              res.data.data.map((item) => {
                return {
                  ...item,
                  label: `${item.name}-${item.code}`,
                  value: item.id,
                  leaf: !item.hasChildren,
                };
              })
            );
          });
        },
        defaultExpandedKeys: [],
      },
      treeData: [],
      type: {
        visible: false,
        title: "",
        id: null,
        code: null,
        leaf: false,
        unit: null,
        gb: null,
        specification: null,
      },
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        menuWidth: 150,
        editBtn: false,
        delBtn: false,
        cellBtn: true,
        highlightCurrentRow: true,
        searchEnter: true,
        dialogDrag: true,
        size: "mini",
        searchSize: "mini",
        align: "center",
        indexLabel: "序号",
        labelWidth: 120,
        calcHeight: 30,
        span: 8,
        tip: false,
        searchIcon: true,
        searchIndex: 3,
        searchShow: true,
        searchSpan: 6,
        border: true,
        index: false,
        selection: true,
        dialogClickModal: false,
        cancelBtn: false,
        column: [
          {
            label: "关键字",
            prop: "searchInfo",
            search: true,
            hide: true,
            showColumn: false,
            display: false,
            searchOrder: 200,
          },
          {
            label: "状态",
            prop: "status",
            search: true,
            type: "select",
            dicUrl: "/api/blade-system/dict/dictionary?code=data_status",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            dataType: "number",
            minWidth: 70,
            slot: true,
          },
          {
            label: "分类",
            prop: "typeId",
            type: "select",
            overHidden: true,
            addDisabled: true,
            editDisabled: true,
            filterable: true,
            hide: true,
            showColumn: false,
            dicUrl: "/api/ni/base/material/type/list",
            props: {
              label: "name",
              value: "id",
              desc: "code",
            },
            width: 120,
            slot: true,
            rules: [
              {
                required: true,
                message: "请选择物料分类",
                trigger: "blur",
              },
            ],
          },
          {
            label: "分类",
            prop: "typeName",
            overHidden: true,
            display: false,
            minWidth: 100,
          },
          {
            label: "类型",
            prop: "cost",
            type: "radio",
            value: false,
            dicData: [
              {
                label: "费用",
                value: true,
              },
              {
                label: "实物",
                value: false,
              },
            ],
            control: (val) => {
              if (!val) {
                return {
                  quality: {
                    rules: [
                      {
                        required: true,
                        message: "请输入材质",
                        trigger: "blur",
                      },
                    ],
                  },
                };
              } else {
                return {
                  quality: {
                    rules: [
                      {
                        required: false,
                      },
                    ],
                  },
                };
              }
            },
            rules: [
              {
                required: true,
                message: "请选择是否费用",
                trigger: "blur",
              },
            ],
          },
          {
            label: "品名",
            prop: "name",
            type: "textarea",
            minRows: 1,
            overHidden: true,
            search: true,
            searchOrder: 99,
            minWidth: 110,
            cell: true,
            placeholder: " ",
            rules: [
              {
                required: true,
                message: "请输入品名",
                trigger: "blur",
              },
            ],
          },
          {
            label: "编码",
            prop: "code",
            overHidden: true,
            search: true,
            placeholder: "系统自动生成",
            searchOrder: 97,
            minWidth: 120,
          },
          {
            label: "规格",
            prop: "specification",
            type: "textarea",
            placeholder: " ",
            minRows: 1,
            overHidden: true,
            search: true,
            minWidth: 110,
            searchOrder: 98,
            cell: true,
            rules: [
              {
                required: true,
                message: "请输入规格",
                trigger: "blur",
              },
            ],
          },
          {
            label: "材质",
            prop: "quality",
            type: "textarea",
            placeholder: " ",
            minRows: 1,
            overHidden: true,
            search: true,
            cell: true,
            minWidth: 110,
          },
          {
            label: "国标",
            prop: "gb",
            type: "textarea",
            placeholder: " ",
            minRows: 1,
            search: true,
            overHidden: true,
            cell: true,
            minWidth: 100,
          },
          {
            label: "单位",
            prop: "unit",
            overHidden: true,
            search: true,
            type: "select",
            filterable: true,
            cell: true,
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_material_unit",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            placeholder: " ",
            minWidth: 85,
            slot: true,
            rules: [
              {
                required: true,
                message: "请输入单位",
                trigger: "blur",
              },
            ],
          },
          {
            label: "是否禁用",
            prop: "disabled",
            search: true,
            type: "radio",
            value: false,
            addDisplay: false,
            dicData: [
              {
                label: "是",
                value: true,
              },
              {
                label: "否",
                value: false,
              },
            ],
            minWidth: 70,
            rules: [
              {
                required: true,
                message: "请选择是否禁用",
                trigger: "blur",
              },
            ],
          },
          {
            label: "关联唛头",
            prop: "markId",
            display: false,
            hide: true,
            showColumn: false,
          },
          {
            label: "是否同步用友",
            prop: "sync",
            type: "radio",
            value: true,
            cell: true,
            minWidth: 120,
            dicData: [
              {
                label: "是",
                value: true,
              },
              {
                label: "否",
                value: false,
              },
            ],
            rules: [
              {
                required: true,
                message: "请选择是否同步用友",
                trigger: "blur",
              },
            ],
          },
          {
            label: "备注",
            prop: "remark",
            overHidden: true,
            type: "textarea",
            cell: true,
            span: 24,
            minRows: 1,
            minWidth: 120,
            placeholder: " ",
          },
          {
            label: "申请人",
            prop: "createUserName",
            display: false,
            overHidden: true,
            minWidth: 65,
          },
          {
            label: "用友同步",
            prop: "yonyouSync",
            minWidth: 220,
            addDisplay: false,
            editDisplay: false,
            hide: true,
          },
        ],
        group: [
          {
            label: "包装明细",
            arrow: false,
            display: false,
            column: [
              {
                label: "内包装",
                prop: "innerPackaging",
                search: true,
                overHidden: true,
                rules: [
                  {
                    required: false,
                    message: "请输入内包装",
                    trigger: "blur",
                  },
                ],
              },
              {
                label: "外包装",
                prop: "outerPackaging",
                search: true,
                overHidden: true,
                rules: [
                  {
                    required: false,
                    message: "请输入外包装",
                    trigger: "blur",
                  },
                ],
              },
              {
                label: "特殊指标要求",
                prop: "special",
                overHidden: true,
              },
              {
                label: "质量级别",
                prop: "qualityLevel",
                type: "select",
                dicUrl:
                  "/api/blade-system/dict-biz/dictionary?code=ni_base_material_quality_level",
                props: {
                  label: "dictValue",
                  value: "dictKey",
                },
                overHidden: true,
              },
            ],
          },
          {
            label: "库存预警",
            arrow: false,
            addDisplay: false,
            editDisplay: false,
            column: [
              {
                label: "安全存量上限",
                prop: "stockUpper",
                overHidden: true,
                type: "number",
                value: 0,
              },
              {
                label: "安全存量下限",
                prop: "stockLower",
                overHidden: true,
                type: "number",
                value: 0,
              },
            ],
          },
        ],
      },
      data: [],
      statusDict: [],
      statusDictKeyValue: {},
      unitDict: [],
      unitDictKv: {},
      yonyouSyncSequenceDict: [],
      yonyouSyncSequenceDictKeyValue: {},
      yonyouSyncStateDict: [],
      yonyouSyncStateDictKeyValue: {},
      excelBox: false,
      excelOption: {
        size: "mini",
        searchSize: "mini",
        submitBtn: false,
        emptyBtn: false,
        column: [
          {
            label: "数据上传",
            prop: "excelFile",
            type: "upload",
            drag: true,
            loadText: "模板上传中，请稍等",
            span: 24,
            data: {},
            propsHttp: {
              res: "data",
            },
            tip: "请上传 .xls,.xlsx 标准格式文件",
            action: "/api/ni/base/material/import",
          },
          {
            label: "数据覆盖",
            prop: "isCovered",
            type: "switch",
            align: "center",
            width: 80,
            dicData: [
              {
                label: "否",
                value: 0,
              },
              {
                label: "是",
                value: 1,
              },
            ],
            value: 0,
            slot: true,
            rules: [
              {
                required: true,
                message: "请选择是否覆盖",
                trigger: "blur",
              },
            ],
          },
          {
            label: "模板下载",
            prop: "excelTemplate",
            formslot: true,
            span: 24,
          },
        ],
      },
      excelForm: {},
      oldSync: {
        visible: false,
        form: {},
      },
      comparison: {
        visible: false,
        option: {
          size: "mini",
          searchSize: "mini",
          submitBtn: false,
          emptyBtn: false,
          column: [
            {
              label: "数据上传",
              prop: "excelFile",
              type: "upload",
              drag: true,
              loadText: "模板上传中，请稍等",
              span: 24,
              data: {},
              propsHttp: {
                res: "data",
              },
              tip: "请上传 .xls,.xlsx 标准格式文件",
              action: "/api/ni/base/material/materialComparison",
            },
            {
              label: "模板下载",
              prop: "excelTemplate",
              formslot: true,
              span: 24,
            },
          ],
        },
        form: {},
        templateColumn: [
          {
            label: "品名",
            prop: "materialName",
          },
          {
            label: "规格",
            prop: "specification",
          },
          {
            label: "材质",
            prop: "quality",
          },
          {
            label: "分类",
            prop: "typeCode",
          },
        ],
      },
      dataType: "all",
      currentRow: null,
    };
  },
  computed: {
    ...mapGetters(["userInfo", "permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.materialinfo_add, false),
        viewBtn: this.vaildData(this.permission.materialinfo_view, false),
        delBtn: this.vaildData(this.permission.materialinfo_delete, false),
        editBtn: this.vaildData(this.permission.materialinfo_edit, false),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
    params() {
      return this.form.params | [];
    },
  },
  created() {
    this.dictInit();
    this.query.status = 9;
    this.query.disabled = false;
  },
  watch: {
    "excelForm.isCovered"() {
      if (this.excelForm.isCovered !== "") {
        const column = this.findObject(this.excelOption.column, "excelFile");
        column.action = `/api/ni/base/material/import?isCovered=${this.excelForm.isCovered}`;
      }
    },
  },
  methods: {
    // 侧边栏收缩与展开
    asidechange() {
      this.asideStatus = !this.asideStatus;

      if (this.asideStatus) {
        setTimeout(() => {
          this.aside_open_close = true;
        }, 500);
      } else {
        setTimeout(() => {
          this.aside_open_close = false;
        }, 500);
      }
    },

    handleOldDataSync() {
      syncFromOld().then(() => {
        this.oldSync.visible = false;
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "同步成功!",
        });
        this.$refs.crud.toggleSelection();
      });
    },
    handleDataTypeChange(val) {
      if (val === "all") {
        this.query = {};
      } else if (val === "todo") {
        this.query.todo = true;
      }
      this.onLoad(this.page);
    },
    handleOldSync() {
      getSyncStatus().then((res) => {
        this.oldSync.form = res.data.data;
        this.oldSync.visible = true;
      });
    },
    handleComparison() {
      this.comparison.visible = true;
    },
    handleYonyouSync() {
      this.$confirm("确定要同步用友?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        syncYonyou().then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
      });
    },
    handleAdd() {
      if (!this.type.id || !this.type.leaf) {
        this.$message({
          type: "warning",
          message: "请选择分类末级!",
        });
        return;
      }
      const isEdit = this.data.some((item) => item.$cellEdit);
      if (isEdit) {
        this.$message({
          type: "warning",
          message: "有正在编辑的草稿，请保存/取消后再操作!",
        });
        return;
      }
      this.data.splice(0, 0, {
        typeId: this.type.id,
        typeName: this.type.title,
        createUserName: this.userInfo.nick_name,
        sync: true,
        status: 0,
        $cellEdit: true,
      });
    },
    async handleApply() {
      let form = {};
      if (this.currentRow) {
        const res = await this.$http.get(
          "/api/ni/base/material/type/detail?id=" + this.currentRow.typeId
        );
        const data = res.data.data;
        form = {
          typeId: data.id,
          // materialTypeName: `${data.name}[${data.code}]`,
        };
      }
      this.dynamicRoute(
        {
          processDefKey: this.processDefKey,
          form,
        },
        "start"
      );
    },
    goSearch() {
      if (this.materialInfoList.length > 1) {
        this.treeOption.defaultExpandedKeys = [];
        if (this.searchIndex < this.materialInfoList.length - 1) {
          this.searchIndex += 1;
          const data = this.materialInfoList[this.searchIndex];
          const keys = data.ancestors.split(",");
          keys.push(data.id);
          this.treeOption.defaultExpandedKeys = keys;
          this.typeClick(data); //触发类型单击
          this.$refs.typeTree.setCurrentKey(data.id); //定位到当前条目
        } else {
          this.$message({
            type: "warning",
            message: "搜索完成!",
          });
        }
      }
    },
    handleMaterialSearch() {
      console.log("handleMaterialSearch");
      console.log(this.materialName);
      if (this.materialName == "") {
        this.treeOption.defaultExpandedKeys = [];
        this.materialInfoList = [];
        this.searchIndex = 0;
        this.goSearchVisible = false;
        this.$message({
          type: "warning",
          message: "请输入搜索内容!",
        });
        return;
      }
      this.treeOption.defaultExpandedKeys = [];
      this.materialInfoList = [];
      this.searchIndex = 0;
      searchMaterialByName(this.materialName).then((res) => {
        console.log(res);
        if (res.data.data.length == 0) {
          this.$message({
            type: "error",
            message: "无搜索记录!",
          });
          return;
        }
        const data = res.data.data[0]; //首先搜索第一个
        this.materialInfoList = res.data.data;
        console.log(data);
        this.typeClick(data); //触发类型单击
        this.$refs.typeTree.setCurrentKey(data.id); //定位到当前条目
        const keys = data.ancestors.split(",");
        keys.push(data.id);
        this.treeOption.defaultExpandedKeys = keys;
        if (this.materialInfoList.length > 1) {
          this.goSearchVisible = true; //多条数据情况下显示继续搜索按钮
        } else {
          this.goSearchVisible = false;
        }
      });
    },
    rowClick(row) {
      if (row.$cellEdit) {
        return;
      }
      this.goSearchVisible = false; //隐藏
      this.materialInfoList = []; //置空
      this.searchIndex = 0; //置零
      this.treeOption.defaultExpandedKeys = [];
      if (!row.typeId) return;
      getTypeDetail(row.typeId).then((res) => {
        const { data } = res.data;
        const keys = data.ancestors.split(",");
        keys.push(row.typeId);
        this.treeOption.defaultExpandedKeys = keys;
        this.type.visible = true;
        this.$nextTick(() => {
          this.type.id = data.id;
          this.type.title = row.typeName;
          this.type.code = data.code;
          this.type.leaf = data.type === "2";
          this.type.remark = data.remark;
          this.type.specification = data.specification;
          this.type.unit = data.unit;
          this.type.gb = data.gb;
          this.$refs.typeTree.setCurrentKey(row.typeId);
        });
      });
    },
    rowDisabledChange(val, row) {
      this.$confirm(
        `此操作将更改物料=>${row.name}的禁用状态，是否继续?`,
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
          center: true,
        }
      )
        .then(() => {
          updateDisabledById(row.id, val).then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!",
            });
          });
        })
        .catch(() => {
          row.blacklist = !val;
        });
    },
    handleComparisonTemplate() {
      this.$Export.excel({
        title: "编码匹配导入模板",
        columns: this.comparison.templateColumn,
        data: [],
      });
    },
    handleTemplate() {
      exportBlob(
        `/api/ni/base/material/export-template?${
          this.website.tokenHeader
        }=${getToken()}`
      ).then((res) => {
        downloadXls(res.data, "存货编码导入模板.xlsx");
      });
    },
    comparisonUploadAfter(res, done, loading, column) {
      this.comparison.visible = false;
      done();
      if (res) {
        this.$confirm(res, "提示", {
          confirmButtonText: "确定",
          type: "warning",
          center: true,
        });
      }
    },
    uploadAfter(res, done, loading, column) {
      this.excelBox = false;
      this.refreshChange();
      done();
      if (res) {
        this.$confirm(res, "提示", {
          confirmButtonText: "确定",
          type: "warning",
          center: true,
        });
      }
    },
    handleSubmit(type) {
      this.form.status = 1;
      //TODO 提交
      if (type === "add") {
        this.$refs.crud.rowSave();
      } else if (type === "edit") {
        this.$refs.crud.rowUpdate();
      }
    },
    rowYonyouSync(row) {
      let msg = "确定将选择数据同步?";
      if (row.yonyouSync && row.yonyouSync.length > 0) {
        msg = "数据已经同步过，是否继续?";
      }
      this.$confirm(msg, {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        sync(row.id).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
      });
    },
    rowFlow(row, index) {
      if (row.processInsId) {
        this.dynamicRoute(
          {
            processInstanceId: row.processInsId,
            processDefKey: this.processDefKey,
          },
          "detail"
        );
      } else {
        this.$refs.crud.rowView(row, index);
      }
    },
    rowEnabled(row) {
      this.$confirm("此操作将启用该数据，是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        center: true,
      }).then(() => {
        updateDisabledById(row.id, false).then(() => {
          row.disabled = false;
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
      });
    },
    rowDisabled(row) {
      this.$confirm("此操作将禁用该数据，是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        center: true,
      }).then(() => {
        updateDisabledById(row.id, true).then(() => {
          row.disabled = true;
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
      });
    },
    rowAttach(row) {
      this.$refs.attachDialogRef.init(row.id, this.module);
    },
    rowApply(row) {
      this.$confirm("此操作将提交该数据，是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        center: true,
      }).then(() => {
        getDetail(row.id).then((res) => {
          const form = res.data.data;
          this.getStartFormByProcessDefKey(this.processDefKey).then((res) => {
            let { process } = res;
            form.processId = process.id;
            form.status = 1;
            console.log(form);
            startProcess(form).then(() => {
              this.onLoad(this.page);
              this.$message({
                type: "success",
                message: "操作成功!",
              });
            });
          });
        });
      });
    },
    handleLog() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择要查看的数据");
        return;
      } else if (this.selectionList.length > 1) {
        this.$message.warning("只能选择一条数据");
        return;
      }
      this.$refs.logOptDialogRef.init(this.selectionList[0].id);
    },
    handleMultipleUnitSubmit(unitItems) {
      this.form.units = unitItems;
    },
    handleMultipleUnit(row) {
      if (!row.unit) {
        this.$message.warning("请先选择单位");
        return;
      }
      console.log(this.form.units);
      this.$refs.multipleUnitRef.init(row.id, row.unit, this.form.units);
    },
    dictInit() {
      this.$http
        .get("/api/blade-system/dict-biz/dictionary?code=ni_material_unit")
        .then((res) => {
          this.unitDict = res.data.data;
          this.unitDictKv = this.unitDict.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });
      this.$http
        .get("/api/blade-system/dict/dictionary?code=yonyou_sync_sequence")
        .then((res) => {
          this.yonyouSyncSequenceDict = res.data.data;
          this.yonyouSyncSequenceDictKeyValue =
            this.yonyouSyncSequenceDict.reduce((acc, cur) => {
              acc[cur.dictKey] = cur.dictValue;
              return acc;
            }, {});
        });
      this.$http
        .get("/api/blade-system/dict/dictionary?code=yonyou_sync_state")
        .then((res) => {
          this.yonyouSyncStateDict = res.data.data;
          this.yonyouSyncStateDictKeyValue = this.yonyouSyncStateDict.reduce(
            (acc, cur) => {
              acc[cur.dictKey] = cur.dictValue;
              return acc;
            },
            {}
          );
        });
    },
    resetClicked() {
      this.$refs.typeTree.setCurrentKey(null);
      this.type.id = null;
      this.type.code = null;
      this.type.leaf = false;
    },
    typeClick(data) {
      getTypeDetail(data.id).then((res) => {
        const { data } = res.data;
        this.type.visible = true;
        this.$nextTick(() => {
          this.type.id = data.id;
          this.type.title = data.name;
          this.type.code = data.code;
          this.type.leaf = data.type === "2";
          this.type.remark = data.remark;
          this.type.specification = data.specification;
          this.type.unit = data.unit;
          this.type.gb = data.gb;
          this.page.currentPage = 1;
          this.onLoad(this.page);
        });
      });
    },
    rowSave(row, done, loading) {
      const f = { ...row };
      if (row.tag) {
        f.tag = row.tag.join(",");
      }
      f.sell = false;
      f.purchase = false;
      f.selfMake = false;
      f.productConsume = false;
      if (f.attrs != null && f.attrs.length > 0) {
        for (const attr of f.attrs) {
          if ("sell" === attr) {
            f.sell = true;
          } else if ("purchase" === attr) {
            f.purchase = true;
          } else if ("selfMake" === attr) {
            f.selfMake = true;
          } else if ("productConsume" === attr) {
            f.productConsume = true;
          }
        }
      }
      if (f.status === 1) {
        apply(f, this.processDefKey).then(
          () => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!",
            });
            done();
          },
          (error) => {
            loading();
            window.console.log(error);
          }
        );
      } else {
        add(f).then(
          () => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!",
            });
            done();
          },
          (error) => {
            loading();
            window.console.log(error);
          }
        );
      }
    },
    rowUpdate(row, index, done, loading) {
      const f = { ...row };
      if (row.tag) {
        console.log(row.tag);
        f.tag = row.tag.join(",");
      }
      f.sell = false;
      f.purchase = false;
      f.selfMake = false;
      f.productConsume = false;
      if (f.attrs != null && f.attrs.length > 0) {
        for (const attr of f.attrs) {
          if ("sell" === attr) {
            f.sell = true;
          } else if ("purchase" === attr) {
            f.purchase = true;
          } else if ("selfMake" === attr) {
            f.selfMake = true;
          } else if ("productConsume" === attr) {
            f.productConsume = true;
          }
        }
      }
      if (f.status === 1) {
        apply(f, this.processDefKey).then(
          () => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!",
            });
            done();
          },
          (error) => {
            loading();
            window.console.log(error);
          }
        );
      } else {
        update(f).then(
          () => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!",
            });
            done();
          },
          (error) => {
            loading();
            console.log(error);
          }
        );
      }
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen(done, type) {
      if (type === "add") {
        if (!this.type.id) {
          this.$message({
            type: "error",
            message: "请选择分类!",
          });
          return;
        }
        console.log(this.type.leaf);
        if (!this.type.leaf) {
          this.$message({
            type: "error",
            message: "请选择叶子节点添加编码!",
          });
          return;
        }
        this.form.typeId = this.type.id;
      }
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then((res) => {
          this.form = res.data.data;
          this.form.attrs = [];
          if (this.form.sell) {
            this.form.attrs.push("sell");
          }
          if (this.form.purchase) {
            this.form.attrs.push("purchase");
          }
          if (this.form.selfMake) {
            this.form.attrs.push("selfMake");
          }
          if (this.form.productConsume) {
            this.form.attrs.push("productConsume");
          }
          if (this.form.tag) {
            this.form.tag = this.form.tag.split(",");
          }
        });
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.resetClicked();
      this.onLoad(this.page);
    },
    handleCurrentRowChange(val) {
      this.currentRow = val;
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      console.log(this.query);
      console.log(this.form);
      this.query.typeId = this.type.id;
      const q = Object.assign(params, this.query);
      if (this.query.tag) {
        q.tag = this.query.tag.join(",");
      }
      if (q.attrs != null && q.attrs.length > 0) {
        for (const attr of q.attrs) {
          if ("sell" === attr) {
            q.sell = true;
          } else if ("purchase" === attr) {
            q.purchase = true;
          } else if ("selfMake" === attr) {
            q.selfMake = true;
          } else if ("productConsume" === attr) {
            q.productConsume = true;
          }
        }
        q.attrs = null;
      }
      getPage(page.currentPage, page.pageSize, q).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.unit-select-plus {
  display: inline-block;
}

// .box{
//   width: auto;
//   height: 800px;
// }

/* 侧边栏样式 */
.aside_main {
  width: 350px !important;
  transition: width 0.5s;
}

.aside_main_show {
  width: 0px !important;
}

.main_cont {
  position: relative;
  margin: 0;
  padding: 0;
  background-color: #e9eef3;
}

/* 侧边栏按钮样式 */
.aside_open_close {
  position: absolute;
  left: 0;
  top: 30%;
  width: 16px;
  height: 60px;
  line-height: 60px;
  color: #fff;
  background-color: #167c46;
  border-radius: 0 6px 6px 0;
  font-size: 20px;
  z-index: 1000;
  cursor: pointer;
}

.aside_open_close:hover {
  background-color: #45966b;
  color: #fff;
}

.tree-search {
  background-color: #fff;
}

/deep/ .my-dialog--wrapper {
  pointer-events: none;
}

/deep/ .my-dialog {
  pointer-events: auto;
  position: absolute;
  left: 220px;
  top: -30px;
}
</style>

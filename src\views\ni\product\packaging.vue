<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      :cell-style="cellStyle"
      @on-load="onLoad"
    >
      <template #name="{ row, index }">
        <span
          :style="{
            color: colorName,
            cursor: 'pointer',
            textDecoration: 'underline',
          }"
          @click="$refs.crud.rowView(row, index)"
        >
          {{ row.name }}
        </span>
      </template>

      <template #materialIdForm="{ size, disabled }">
        <material-select
          v-model="form.materialId"
          :size="size"
          :disabled="disabled"
        />
      </template>
      <template #menuLeft>
        <el-button
          type="danger"
          size="mini"
          icon="el-icon-delete"
          plain
          v-if="permission.productPackaging_delete"
          @click="handleDelete"
        >
          删 除
        </el-button>
        <el-button
          type="info"
          size="mini"
          icon="el-icon-upload"
          plain
          @click="handleImport"
          >导 入
        </el-button>
        <el-divider direction="vertical"></el-divider>
        <el-radio-group v-model="innerPark" size="mini">
          <el-radio-button label="all">全部</el-radio-button>
          <el-radio-button label="0">外包装</el-radio-button>
          <el-radio-button label="1">内包装</el-radio-button>
        </el-radio-group>
      </template>
      <template #menu="{ row, size, index }">
        <el-button
          type="text"
          icon="el-icon-video-pause"
          :size="size"
          v-if="row.status === 1"
          @click="rowPause(row, index)"
          >停用
        </el-button>
        <el-button
          type="text"
          icon="el-icon-video-play"
          :size="size"
          v-else
          @click="rowPlay(row, index)"
          >启用
        </el-button>
        <el-button
          type="text"
          icon="el-icon-download"
          :size="size"
          @click="rowAttach(row)"
          >附件
        </el-button>
      </template>
    </avue-crud>
    <inner-packaging-dialog ref="innerPackagingDialogRef" />
    <dict-dialog ref="dictRef" />
    <el-dialog
      title="包装数据导入"
      append-to-body
      :visible.sync="excelBox"
      width="555px"
    >
      <avue-form
        :option="excelOption"
        v-model="excelForm"
        :upload-after="uploadAfter"
      >
        <template #excelTemplate>
          <el-button type="primary" @click="handleTemplate">
            点击下载<i class="el-icon-download el-icon--right"></i>
          </el-button>
        </template>
      </avue-form>
    </el-dialog>
    <attach-dialog ref="attachDialogRef" code="public" />
  </basic-container>
</template>

<script>
import {
  add,
  getDetail,
  getList,
  remove,
  update,
  pause,
  play,
} from "@/api/ni/product/packaging";
import { mapGetters } from "vuex";
import MaterialSelect from "@/views/ni/base/components/MaterialSelect1.vue";
import InnerPackagingDialog from "@/views/ni/product/components/InnerPackagingDialog.vue";
import DictDialog from "@/components/dict-dialog/index.vue";
import { exportBlob } from "@/api/common";
import { getToken } from "@/util/auth";
import { downloadXls } from "@/util/util";
import AttachDialog from "@/components/attach-dialog/index.vue";

export default {
  components: {
    AttachDialog,
    DictDialog,
    InnerPackagingDialog,
    MaterialSelect,
  },
  data() {
    return {
      module: "ni_product_packaging",
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        menuWidth: 270,
        labelWidth: 110,
        size: "mini",
        searchSize: "mini",
        align: "center",
        searchIndex: 3,
        searchIcon: true,
        searchEnter: true,
        height: "auto",
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        selection: true,
        dialogClickModal: false,
        column: [
          {
            label: "包装名",
            prop: "name",
            type: "input",
            search: true,
            minWidth: 110,
            overHidden: true,
            rules: [
              {
                required: true,
                message: "请输入包装名",
                trigger: "blur",
              },
            ],
          },
          {
            label: "类型",
            prop: "type",
            type: "select",
            minWidth: 80,
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_product_package_material",
            props: {
              label: "dictValue",
              value: "dictKey",
              desc: "dictKey",
            },
            search: true,
            width: 100,
            rules: [
              {
                required: true,
                message: "请选择类型",
                trigger: "blur",
              },
            ],
          },
          {
            label: "编码",
            prop: "boxType",
            search: true,
            rules: [
              {
                required: true,
                message: "请选择编码",
                trigger: "blur",
              },
            ],
          },
          {
            label: "品牌",
            prop: "brand",
            type: "select",
            filterable: true,
            overHidden: true,
            width: 75,
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_product_brand",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            search: true,
          },
          {
            label: "材质",
            prop: "quality",
            minWidth: 100,
            overHidden: true,
          },
          {
            label: "容量",
            prop: "capacity",
            type: "number",
            minWidth: 90,
            overHidden: true,
            rules: [
              {
                required: true,
                message: "请输入容量",
                trigger: "blur",
              },
            ],
          },
          {
            label: "单位",
            prop: "unit",
            type: "select",
            filterable: true,
            width: 70,
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_material_unit",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            rules: [
              {
                required: true,
                message: "请选择单位",
                trigger: "blur",
              },
            ],
            slot: true,
            placeholder: " ",
          },
          {
            label: "内/外包装",
            prop: "innerPark",
            type: "radio",
            width: 75,
            dicData: [
              {
                label: "外包装",
                value: 0,
              },
              {
                label: "内包装",
                value: 1,
              },
            ],
            rules: [
              {
                required: true,
                message: "请选择是否内包装",
                trigger: "blur",
              },
            ],
          },
          {
            label: "默认唛头",
            prop: "defaultMarkId",
            type: "select",
            minWidth: 110,
            overHidden: true,
            dicUrl: "",
          },
          {
            label: "存货编码",
            prop: "materialId",
            hide: true,
            showColumn: false,
          },
          {
            label: "存货编码",
            prop: "materialCode",
            minWidth: 110,
            overHidden: true,
            display: false,
          },
          {
            label: "备注",
            prop: "remark",
            span: 24,
            type: "textarea",
            minRows: 2,
            minWidth: 110,
            overHidden: true,
          },
          {
            label: "状态",
            prop: "status",
            type: "radio",
            width: 80,
            value: 1,
            dicData: [
              {
                label: "启用",
                value: 1,
              },
              {
                label: "停用",
                value: 2,
              },
            ],
            dataType: "number",
            rules: [
              {
                required: true,
                message: "请选择状态",
                trigger: "blur",
              },
            ],
          },
        ],
      },
      data: [],
      /*
       *数据导入
       */
      excelBox: false,
      excelOption: {
        size: "mini",
        searchSize: "mini",
        submitBtn: false,
        emptyBtn: false,

        column: [
          {
            label: "数据上传",
            prop: "excelFile",
            type: "upload",
            drag: true,
            loadText: "数据上传中，请稍等",
            span: 24,
            data: {},
            propsHttp: {
              res: "data",
            },
            tip: "请上传 .xls,.xlsx 标准格式文件",
            action: `/api/ni/product/packaging/import`,
          },
          {
            label: "模板下载",
            prop: "excelTemplate",
            formslot: true,
            span: 24,
          },
        ],
      },
      excelForm: {},
      innerPark: "all",
    };
  },
  computed: {
    ...mapGetters(["permission", "colorName"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.productPackaging_add, false),
        viewBtn: this.vaildData(this.permission.productPackaging_view, false),
        delBtn: this.vaildData(this.permission.productPackaging_delete, false),
        editBtn: this.vaildData(this.permission.productPackaging_edit, false),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  watch: {
    innerPark: function (val) {
      this.page.currentPage = 1;
      this.onLoad(this.page);
    },
  },
  methods: {
    handleTemplate() {
      exportBlob(
        `/api/ni/product/packaging/export-template?${
          this.website.tokenHeader
        }=${getToken()}`
      ).then((res) => {
        downloadXls(res.data, "包装导入模板.xlsx");
      });
    },
    uploadAfter(res, done, loading, column) {
      console.log(res);
      window.console.log(column);
      this.excelBox = false;
      this.refreshChange();
      done();
      if (res) {
        this.$confirm(res, "提示", {
          confirmButtonText: "确定",
          type: "warning",
          center: true,
        });
      }
    },
    rowAttach(row) {
      this.$refs.attachDialogRef.init(row.id, this.module);
    },
    rowPlay(row, index) {
      play(row.id).then(() => {
        this.data[index].status = 1;
        this.$message({
          type: "success",
          message: "操作成功!",
        });
      });
    },
    rowPause(row, index) {
      pause(row.id).then(() => {
        this.data[index].status = 2;
        this.$message({
          type: "success",
          message: "操作成功!",
        });
      });
    },
    rowSave(row, done, loading) {
      add(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          window.console.log(error);
        }
      );
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          console.log(error);
        }
      );
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    handleImport() {
      this.excelBox = true;
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen(done, type) {
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then((res) => {
          this.form = res.data.data;
        });
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.innerPark = "all";
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      const queryParams = { ...this.query, ...params };
      if (this.innerPark !== "all") {
        queryParams.innerPark = this.innerPark;
      }
      getList(page.currentPage, page.pageSize, queryParams).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
    cellStyle({ row, column }) {
      if ("status" === column.columnKey && row.status === 2) {
        return {
          backgroundColor: "#F56C6C",
          color: "#fff",
        };
      }
    },
  },
};
</script>

<style></style>

<template>
  <el-row :gutter="20">
    <el-col
      :xs="24"
      :sm="12"
      :md="6"
      :lg="6"
      :xl="6"
      v-for="(item, index) in panelList"
      :key="index"
    >
      <el-card
        shadow="hover"
        :body-style="{ padding: '10px 20px' }"
        :class="{
          'board-item': true,
          'board-item--main-color': index === 0,
        }"
      >
        <div slot="header" class="clearfix">
          <span>{{ item.title }}</span>
        </div>
        <div class="board-item-top">
          <el-statistic
            :value-style="{ fontSize: `${resizeTime * 36}px`, color: item.value<0?'#F56C6C':'' }"
            :style="{ fontSize: `${resizeTime * 36}px` }"
            group-separator=","
            :precision="item.precision"
            :value="item.value"
          >
            <template v-if="item.prefix" slot="prefix">
              {{ item.prefix }}
            </template>
          </el-statistic>
        </div>
        <div class="board-item-left">
          <span>
            <i :class="item.icon" />
          </span>
        </div>

        <div class="board-item-bottom">
          <div class="board-item-placeholder" v-if="item.placeholder">
            <i class="el-icon-warning-outline" />{{ item.placeholder }}
          </div>
          <div class="board-item-block" v-if="item.mom">
            环比
            <span
              class="board-item-trend"
              :style="{
                color: item.mom
                  ? item.mom >= 0
                    ? '#167c46'
                    : '#f56c6c'
                  : '#909399',
              }"
            >
              <i class="el-icon-caret-top" v-if="item.mom >= 0"></i>
              <i class="el-icon-caret-bottom" v-else-if="item.mom < 0"></i>
              <i class="el-icon-d-caret" v-else />
              {{ item.mom ? item.mom : "-.--" }}%
            </span>
          </div>
          <i
            class="el-icon-arrow-right"
            v-if="item.path"
            @click="handlePath(item.path)"
          />
        </div>
      </el-card>
    </el-col>
  </el-row>
</template>
<script>
export default {
  props: {
    value: {
      type: Object,
      default: () => {},
    },
  },
  mounted() {
    this.$nextTick(() => {
      this.updateContainer();
      this.updatePanelValues(); // 新增方法
    });
    window.addEventListener("resize", this.updateContainer, false);
  },
  data() {
    return {
      resizeTime: 1,
      panelList: [
        {
          title: "总库存(件)",
          prop: "total",
          value: 0,
          icon: "el-icon-s-grid",
          mom: 0.0,
          path: "/ni/fg/inventorySummary",
        },
        {
          title: "总重量(kg)",
          prop: "weight",
          value: 0,
          icon: "el-icon-files",
          mom: 0.0,
          path: "/ni/fg/inventorySummary",
        },
        {
          title: "库存天数",
          prop: "days",
          value: 0,
          icon: "el-icon-time",
          placeholder: "当前总库存数量/过去30天的日均出库数量",
        },
        {
          title: "当日库存变化(kg)",
          prop: "inventoryChanges",
          value: 0,
          icon: "el-icon-guide",
          placeholder: " ",
          path: "/ni/fg/transaction-item",
        },
      ],
    };
  },
  watch: {
    value: {
      deep: true,
      immediate: true,
      handler(newVal) {
        if (newVal && Object.keys(newVal).length > 0) {
          this.updatePanelValues();
        }
      },
    },
  },
  methods: {
    handlePath(path) {
      if (path) {
        this.$router.push({ path });
      }
    },
    updatePanelValues() {
      if (this.value) {
        this.panelList = this.panelList.map((panel) => {
          if (panel.prop === "inventoryChanges") {
            const inboundTotal = Number(this.value.inboundTotal) || 0;
            const outboundTotal = Number(this.value.outboundTotal) || 0;
            return {
              ...panel,
              value:(inboundTotal - outboundTotal),
              placeholder: `当日入库:${inboundTotal.toLocaleString()}/当日出库:${outboundTotal.toLocaleString()}`,
            };
          }
          const propValue = this.value[panel.prop];
          return {
            ...panel,
            value: propValue !== undefined ? propValue : panel.value,
          };
        });
      }
    },
    updateContainer() {
      if (
        document.documentElement.clientWidth >= 1400 &&
        document.documentElement.clientWidth < 1920
      ) {
        this.resizeTime = (document.documentElement.clientWidth / 2080).toFixed(
          2
        );
      } else if (document.documentElement.clientWidth < 1080) {
        this.resizeTime = (document.documentElement.clientWidth / 1080).toFixed(
          2
        );
      } else {
        this.resizeTime = 1;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.board-item {
  padding: 8px;

  /deep/ .el-card__header {
    color: #303133;
    font-size: 15px;
    font-weight: 500;
    border-bottom: 0;
    padding: 16px 18px !important;
  }

  /deep/ .el-card__body {
    height: 98px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    flex: 1;
    position: relative;
  }

  &:hover {
    cursor: pointer;
  }

  .board-item-top .el-statistic {
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    line-height: 44px;
    font-size: 36px;
  }

  .board-item-bottom {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding-bottom: 6px;

    .board-item-block {
      color: #909399;
    }

    .board-item-placeholder {
      color: #909399;
      font-size: 14px;
    }

    .board-item-trend {
      color: #167c46;
      font-weight: bolder;

      i {
        cursor: pointer;
      }
    }

    .el-icon-arrow-right {
      margin-left: auto;
    }

    i:hover {
      color: #167c46;
    }
  }

  &-block {
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 22px;
    color: var(--td-text-color-placeholder);
  }

  &-trend {
    margin-left: 8px;
  }

  .board-item-left {
    position: absolute;
    top: 0;
    right: 32px;

    > span {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      width: 56px;
      height: 56px;
      background: #d7fbe7;
      border-radius: 50%;

      i {
        font-size: 24px;
        color: #167c46;
      }
    }
  }
}
</style>

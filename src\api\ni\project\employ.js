import request from "@/router/axios";

export const getList = (params) => {
  return request({
    url: "/api/ni/project/employ/list",
    method: "get",
    params: {
      ...params,
    },
  });
};

export const getPage = (current, size, params) => {
  return request({
    url: "/api/ni/project/employ/page",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/ni/project/employ/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const remove = (ids) => {
  return request({
    url: "/api/ni/project/employ/remove",
    method: "post",
    params: {
      ids,
    },
  });
};
export const add = (row) => {
  return request({
    url: "/api/ni/project/employ/add",
    method: "post",
    data: row,
  });
};
export const add1 = (row) => {
  return request({
    url: "/api/ni/project/employ/v1/add",
    method: "post",
    data: row,
  });
};
export const update = (row) => {
  return request({
    url: "/api/ni/project/employ/update",
    method: "post",
    data: row,
  });
};
export const update1 = (row) => {
  return request({
    url: "/api/ni/project/employ/v1/update",
    method: "post",
    data: row,
  });
};

export const publish = (ids) => {
  return request({
    url: "/api/ni/project/employ/publish",
    method: "post",
    params: {
      ids,
    },
  });
};
export const publish1 = (ids) => {
  return request({
    url: "/api/ni/project/employ/v1/publish",
    method: "post",
    params: {
      ids,
    },
  });
};
export const revokePublish = (id) => {
  return request({
    url: "/api/ni/project/employ/revokePublish",
    method: "post",
    params: {
      id,
    },
  });
};
export const revokePublish1 = (id) => {
  return request({
    url: "/api/ni/project/employ/v1/revokePublish",
    method: "post",
    params: {
      id,
    },
  });
};
export const dispatch = (params) => {
  return request({
    url: "/api/ni/project/employ/dispatch",
    method: "post",
    params,
  });
};

export const backDispatch = (id) => {
  return request({
    url: "/api/ni/project/employ/backDispatch",
    method: "post",
    params: {
      id,
    },
  });
};

export const finishWork = (row) => {
  return request({
    url: "/api/ni/project/employ/finishWork",
    method: "post",
    data: row,
  });
};
export const submit = (ids) => {
  return request({
    url: "/api/ni/project/employ/submit",
    method: "post",
    params: {
      ids,
    },
  });
};

export const back = (ids) => {
  return request({
    url: "/api/ni/project/employ/back",
    method: "post",
    params: {
      ids,
    },
  });
};

export const toVoid = (ids) => {
  return request({
    url: "/api/ni/project/employ/toVoid",
    method: "post",
    params: {
      ids,
    },
  });
};

export const confirm = (ids) => {
  return request({
    url: "/api/ni/project/employ/confirm",
    method: "post",
    params: {
      ids,
    },
  });
};

export const dispatchUserList = (params) => {
  return request({
    url: "/api/ni/project/employ/dispatchUserList",
    method: "get",
    params,
  });
};
export const getPageWithDispatch = (current, size, params) => {
  return request({
    url: "/api/ni/project/employ/pageWithDispatch",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};
export const finish = (ids) => {
  return request({
    url: "/api/ni/project/employ/finish",
    method: "post",
    params: {
      ids,
    },
  });
};

export const overrule = (ids) => {
  return request({
    url: "/api/ni/project/employ/overrule",
    method: "post",
    params: {
      ids,
    },
  });
};
export const changePersonTags = (params) => {
  return request({
    url: "/api/ni/project/employ/changePersonTags",
    method: "post",
    params
  });
}
export const getPersonTags = (tag) => {
  return request({
    url: "/api/ni/project/employ/getPersonTags",
    method: "get",
    params: {
      tag
    }
  });
}
export const changeDeptColor = (params) => {
  return request({
    url: "/api/ni/project/employ/changeDeptColor",
    method: "post",
    params
  });
}
export const getDeptColors = () => {
  return request({
    url: "/api/ni/project/employ/getDeptColors",
    method: "get",
  });
}
export const changeOpType = (params) => {
  return request({
    url: "/api/ni/project/employ/changeOpType",
    method: "post",
    params
  });
}

import request from "@/router/axios";

export const getPage = (current, size, params) => {
  return request({
    url: "/api/ni/fin/payableApply/page",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};
export const getList = (params) => {
  return request({
    url: "/api/ni/fin/payableApply/list",
    method: "get",
    params: {
      ...params,
    },
  });
};
export const unAuditAndPaylist = (params) => {
  return request({
    url: "/api/ni/fin/payableApply/unAuditAndPaylist",
    method: "get",
    params: {
      ...params,
    },
  });
};

export const unAuditAndUnPaylist = (params) => {
  return request({
    url: "/api/ni/fin/payableApply/unAuditAndUnPaylist",
    method: "get",
    params: {
      ...params,
    },
  });
};
export const getDetail = (id) => {
  return request({
    url: "/api/ni/fin/payableApply/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const getDetailByProcessInsId = (processInsId) => {
  return request({
    url: "/api/ni/fin/payableApply/detail",
    method: "get",
    params: {
      processInsId,
    },
  });
};
export const remove = (ids) => {
  return request({
    url: "/api/ni/fin/payableApply/remove",
    method: "post",
    params: {
      ids,
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/ni/fin/payableApply/save",
    method: "post",
    data: row,
  });
};

export const update = (row) => {
  return request({
    url: "/api/ni/fin/payableApply/update",
    method: "post",
    data: row,
  });
};

export const submit = (id, processDefKey) => {
  return request({
    url: "/api/ni/fin/payableApply/submit",
    method: "post",
    params: {
      id,
      processDefKey,
    },
  });
};

export const back = (ids) => {
  return request({
    url: "/api/ni/fin/payableApply/back",
    method: "post",
    params: {
      ids,
    },
  });
};

export const toVoid = (ids) => {
  return request({
    url: "/api/ni/fin/payableApply/toVoid",
    method: "post",
    params: {
      ids,
    },
  });
};
export const changeDocStatus = (ids, docStatus) => {
  return request({
    url: "/api/ni/fin/payableApply/changeDocStatus",
    method: "post",
    params: {
      ids,
      docStatus,
    },
  });
};
export const createApply = (row) => {
  return request({
    url: "/api/ni/fin/payableApply/createApply",
    method: "post",
    data: row,
  });
};
export const getPrintData = (id) => {
  return request({
    url: "/api/ni/fin/payableApply/getPrintData",
    method: "get",
    params: {
      id,
    },
  });
};
export const getItemsPrintData = (budgetId) => {
  return request({
    url: "/api/ni/fin/payableApply/getItemsPrintData",
    method: "get",
    params: {
      budgetId,
    },
  });
};

export const changeBillSerialNo = (id, billSerialNo) => {
  return request({
    url: "/api/ni/fin/payableApply/changeBillSerialNo",
    method: "post",
    params: {
      id,
      billSerialNo,
    },
  });
};

export const updateCostItemMaterialId = (id, materialId) => {
  return request({
    url: "/api/ni/fin/payableApply/updateCostItemMaterialId",
    method: "post",
    params: {
      id,
      materialId,
    },
  });
};
export const itemPage = (current, size, applyId) => {
  return request({
    url: "/api/ni/fin/payableApply/item/page",
    method: "get",
    params: {
      applyId,
      current,
      size,
    },
  });
};
export const listWithPay = (params) => {
    return request({
        url: "/api/ni/fin/payableApply/listWithPay",
        method: "get",
        params,
    });
};

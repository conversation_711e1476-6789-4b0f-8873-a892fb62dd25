<template>
  <basic-container>
    <avue-form
      :option="option"
      v-model="form"
      ref="form"
      @submit="handleSubmit"
      @reset-change="handleReset"
    >
    </avue-form>
  </basic-container>
</template>

<script>
import { add, update } from "@/api/ni/tracing/qualityInspectionModified";

export default {
  props: {
    modifiedInspection: {
      require: true,
    },
  },
  data() {
    return {
      form: this.modifiedInspection,
      loading: true,
      option: {
        labelWidth: 150,
        emptyBtn: false,
        column: [
          {
            label: "是否合格",
            prop: "result",
            type: "select",
            width: 80,
            value: true,
            labelSuffix: "<span style='color: red;'>*</span>",
            dicData: [
              {
                label: "是",
                value: true,
              },
              {
                label: "否",
                value: false,
              },
            ],
            rules: [
              {
                required: true,
                message: "是否合格为必填项",
                trigger: ["blur", "change"]
              }
            ],
            html: true,
            formatter: function (val, value, label) {
              if (value === true) {
                return `<span style="color:green">${label}</span>`;
              } else {
                return `<span style="color:red">${label}</span>`;
              }
            },
          },
          {
            label: "质检日期",
            prop: "date",
            type: "date",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            width: 100,
            labelSuffix: "<span style='color: red;'>*</span>",
            rules: [
              {
                required: true,
                message: "质检日期为必填项",
                trigger: ["blur", "change"]
              }
            ],
            html: true
          },
          {
            label: "含水",
            prop: "waterContent",
            type: "input",
            width: 60,
            labelSuffix: "<span style='color: red;'>*</span>",
            rules: [
              {
                required: true,
                message: "含水为必填项",
                trigger: ["blur", "change"]
              }
            ],
            html: true 
          },
          {
            label: "烧后NaCl",
            prop: "afterBurningNaCl",
            type: "input",
            width: 80,
          },
          {
            label: "烧后LiCl",
            prop: "afterBurningLiCl",
            type: "input",
            width: 80,
            labelSuffix: "<span style='color: red;'>*</span>",
            rules: [
              {
                required: true,
                message: "烧后LiCl为必填项",
                trigger: ["blur", "change"]
              }
            ],
            html: true
          },
          {
            label: "有效水吸附",
            prop: "effectiveWaterAdsorption",
            type: "input",
            width: 90,
          },
          {
            label: "强度",
            prop: "strength",
            type: "input",
            width: 60,
          },
          {
            label: "变异系数",
            prop: "coefficientOfVariation",
            type: "input",
            width: 80,
          },
          {
            label: "堆积密度",
            prop: "bulkDensity",
            type: "input",
            width: 80,
            labelSuffix: "<span style='color: red;'>*</span>",
            rules: [
              {
                required: true,
                message: "堆积密度为必填项",
                trigger: ["blur", "change"]
              }
            ],
            html: true
          },
          {
            label: "粒度",
            prop: "particleSize",
            type: "input",
            width: 60,
          },
          {
            label: "粒度合格率(%)",
            prop: "particleSizeQualifiedRate",
            type: "input",
            width: 110,
            labelSuffix: "<span style='color: red;'>*</span>",
            rules: [
              {
                required: true,
                message: "粒度合格率(%)",
                trigger: ["blur", "change"]
              }
            ],
            html: true
          },
          {
            label: "温升10g",
            prop: "temperatureRise10",
            type: "input",
            width: 70,
          },
          {
            label: "温升20g",
            prop: "temperatureRise20",
            type: "input",
            width: 70,
            labelSuffix: "<span style='color: red;'>*</span>",
            rules: [
              {
                required: true,
                message: "温升20g为必填项",
                trigger: ["blur", "change"]
              }
            ],
            html: true
          },
          {
            label: "温升30g",
            prop: "temperatureRise30",
            type: "input",
            width: 70,
          },
          {
            label: "磨前落粉",
            prop: "fallingPowderBeforeGrinding",
            type: "input",
            width: 80,
            labelSuffix: "<span style='color: red;'>*</span>",
            rules: [
              {
                required: true,
                message: "磨前落粉为必填项",
                trigger: ["blur", "change"]
              }
            ],
            html: true
          },
          {
            label: "磨后落粉",
            prop: "fallingPowderAfterGrinding",
            type: "input",
            width: 80,
          },
          {
            label: "渣子",
            prop: "waste",
            type: "input",
            width: 60,
          },
          {
            label: "170度吸气量",
            prop: "inspiratoryVolume170t",
            type: "input",
            width: 90,
          },
          {
            label: "70度吸气量",
            prop: "inspiratoryVolume70t",
            type: "input",
            width: 90,
          },
          {
            label: "96H吸气量",
            prop: "inspiratoryVolume96h",
            type: "input",
            width: 90,
          },
          {
            label: "气体解吸量(ml/g)",
            prop: "gasDesorption",
            type: "input",
            width: 120,
            labelSuffix: "<span style='color: red;'>*</span>",
            rules: [
              {
                required: true,
                message: "气体解吸量(ml/g)为必填项",
                trigger: ["blur", "change"]
              }
            ],
            html: true
          },
        ],
      },
    };
  },
  methods: {
    handleSubmit(form, done) {
      this.$emit("submit-modified", form);
      done();
    },
    handleReset() {
      this.$emit("close-modified");
    },
    rowSave(row, done, loading) {
      row.attach = JSON.stringify(row.attach);
      add(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          window.console.log(error);
        }
      );
    },
    rowUpdate(row, index, done, loading) {
      row.attach = JSON.stringify(row.attach);
      update(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          console.log(error);
        }
      );
    },
    onLoad(page, params = {}) {},
  },
};
</script>

<style></style>

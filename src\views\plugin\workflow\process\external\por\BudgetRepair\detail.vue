<template>
  <basic-container>
    <avue-skeleton :loading="waiting" avatar :rows="8">
      <avue-affix id="avue-view" :offset-top="114">
        <div class="header">
          <avue-title :value="process.processDefinitionName"></avue-title>
          <div v-if="process.status != 'todo'">
            主题：
            <avue-select v-model="theme" size="mini" :clearable="false" :dic="themeList"></avue-select>
          </div>
        </div>
      </avue-affix>
      <el-tabs v-model="activeName">
        <el-tab-pane label="申请信息" name="first">
          <el-card shadow="never">
            <div id="printBody" :class="process.status != 'todo' ? `wf-theme-${theme}` : ''">
              <avue-form v-if="
                option &&
                ((option.column && option.column.length > 0) ||
                  (option.group && option.group.length > 0))
              " v-model="form" ref="form" :defaults.sync="defaults" :option="option"
                :upload-preview="handleUploadPreview">
                <template #projectId="{ disabled, size, index, row }">
                  <project-select v-model="form.projectId" :size="size" :params="{ status: 9 }" :disabled="disabled"
                    @confirm="projectConfirm" />
                </template>
                <template #itemsLabel>
                  <span style="font-size: 16px; font-weight: 500">预算明细</span>
                  <el-divider direction="vertical"></el-divider>
                  <el-dropdown @command="itemAdd">
                    <el-button type="primary" size="mini" icon="el-icon-plus" plain>添加<i
                        class="el-icon-arrow-down el-icon--right"></i>
                    </el-button>
                    <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item command="1">实物</el-dropdown-item>
                      <el-dropdown-item command="2">费用</el-dropdown-item>
                    </el-dropdown-menu>
                  </el-dropdown>
                  <el-button type="danger" size="mini" icon="el-icon-delete" plain
                    :disabled="item.selectionList.length <= 0" @click="itemDelete">删 除
                  </el-button>
                  <el-button type="primary" size="mini" icon="el-icon-upload2" plain
                    @click="() => (item.excelBox = true)">导入
                  </el-button>
                </template>
                <template #items="{ row, disabled }">
                  <avue-crud :option="item.option" :data="form.items" @selection-change="itemSelectionChange"
                    ref="itemCrud" @cell-click="itemCellClickChange" :upload-preview="handleUploadPreview">
                    <template #cost="{ row, index }">
                      <el-tag size="mini" type="danger" effect="dark" v-if="row.cost">
                        费用
                      </el-tag>
                      <el-tag size="mini" type="info" effect="plain" v-else>
                        实物
                      </el-tag>
                    </template>
                    <template #pv="{ row, index }">
                      <el-tag size="mini" type="danger" effect="dark" v-if="row.pv">
                        是
                      </el-tag>
                      <el-tag size="mini" type="info" effect="plain" v-else>
                        否
                      </el-tag>
                    </template>
                    <template #materialCodeForm="{ row, disabled, size, index }">
                      <material-select v-model="row.materialId" :size="size" :disabled="disabled"
                        @submit="handleMaterialSubmit($event, row)" />
                    </template>
                    <template #numForm="{ row, disabled, size }">
                      <el-input-number :size="size" v-model="row.num" :disabled="disabled" :min="0"
                        controls-position="right" style="width: 100%" @change="handleNumChange($event, row)" />
                    </template>
                    <template #priceForm="{ row, disabled, size }">
                      <el-input-number :size="size" v-model="row.price" :disabled="disabled" :min="0" :controls="false"
                        style="width: 100%" @change="handlePriceChange($event, row)" />
                    </template>
                    <template #attach="{ row, index }">
                      <el-link v-if="row.attach" type="primary" target="_blank" @click="rowAttach(row, index)">
                        <i class="el-icon-circle-plus-outline" />
                        附件({{ row.attach ? row.attach.length : 0 }})
                      </el-link>
                    </template>
                  </avue-crud>
                </template>
              </avue-form>
            </div>
          </el-card>
          <el-card shadow="never" style="margin-top: 20px" v-if="process.status == 'todo'">
            <wf-examine-form ref="examineForm" :comment.sync="comment" :process="process"
              @user-select="handleUserSelect"></wf-examine-form>
          </el-card>
        </el-tab-pane>
        <el-tab-pane label="流转信息" name="second">
          <el-card shadow="never" style="margin-top: 5px">
            <wf-flow :flow="flow"></wf-flow>
          </el-card>
        </el-tab-pane>
        <el-tab-pane label="流程跟踪" name="third">
          <template v-if="activeName == 'third'">
            <el-card shadow="never" style="margin-top: 5px">
              <wf-design ref="bpmn" style="height: 500px" :options="bpmnOption"></wf-design>
            </el-card>
          </template>
        </el-tab-pane>
      </el-tabs>
    </avue-skeleton>

    <!-- 底部按钮 -->
    <wf-button :loading="submitLoading" :button-list="buttonList" :process="process" :comment="comment"
      @examine="handleExamine" @user-select="handleUserSelect" @print="handlePrint" @rollback="handleRollbackTask"
      @terminate="handleTerminateProcess" @withdraw="handleWithdrawTask"></wf-button>
    <!-- 人员选择弹窗 -->
    <user-select ref="user-select" :check-type="checkType" :default-checked="defaultChecked"
      @onConfirm="handleUserSelectConfirm"></user-select>
    <el-dialog title="数据导入" append-to-body :visible.sync="item.excelBox" width="555px">
      <avue-form :option="item.excelOption" v-model="item.excelForm" :upload-after="uploadAfter">
        <template slot="excelTemplate">
          <el-button type="primary" @click="handleTemplate">
            点击下载<i class="el-icon-download el-icon--right"></i>
          </el-button>
        </template>
      </avue-form>
    </el-dialog>
    <material-select-dialog ref="materialSelectDialogRef" multiple @submit="handleItemAddSubmit" />
    <attach-dialog ref="attachRef" code="public" :delBtn="false" @close="handleRefreshAttach" />
  </basic-container>
</template>

<script>
import WfExamineForm from "@/views/plugin/workflow/process/components/examForm.vue";
import WfButton from "@/views/plugin/workflow/process/components/button.vue";
import WfFlow from "@/views/plugin/workflow/process/components/flow.vue";
import userSelect from "@/views/plugin/workflow/process/components/user-select";

import exForm from "@/views/plugin/workflow/mixins/ex-form";
import theme from "@/views/plugin/workflow/mixins/theme";
import { getDetail as getBudgetDetail } from "@/api/ni/por/budget-year";
import { exportBlob } from "@/api/common";
import { getToken } from "@/util/auth";
import { downloadXls } from "@/util/util";
import MaterialSelectDialog from "@/views/ni/base/components/MaterialSelectDialog";
import BudgetSelect from "@/views/ni/por/components/BudgetSelect";
import ProjectSelect from "@/views/ni/project/components/MyProjectSelect";
import MaterialSelect from "@/views/ni/base/components/MaterialSelect1";
import FinAccountingSelect from "@/views/ni/fin/components/AccountingSelect";
import AttachDialog from "@/components/attach-dialog";
import { getList as getAttachList } from "@/api/resource/attach";

export default {
  mixins: [exForm, theme],
  components: {
    BudgetSelect,
    userSelect,
    WfExamineForm,
    WfButton,
    WfFlow,
    MaterialSelectDialog,
    ProjectSelect,
    MaterialSelect,
    FinAccountingSelect,
    AttachDialog,
  },
  watch: {
    '$route.query.p': {
      immediate: true,
      handler(val) {
        if (val) {
          this.submitLoading = true;
          Object.keys(this.form).forEach((key) => (this.form[key] = ""));
          const param = JSON.parse(Buffer.from(val, "base64").toString());
          const { taskId, processInsId } = param;
          if (processInsId) {
            this.getDetail(taskId, processInsId);
          }
        }
      },
    },
    "form.parentId": {
      async handler(val) {
        console.log(this.form.projectId);
        const subCode1 = this.findObject(this.option.column, "subCode");
        const subCode = this.findObject(this.item.option.column, "subCode");
        if (val && val > 0) {
          let projectId;
          if (this.form.year === "2") projectId = val;
          else projectId = this.form.projectId;
          const r = await this.$http.get(
            "/api/ni/project/sub/select?projectId=" + projectId
          );
          subCode.dicData = r.data.data;
          subCode1.dicData = r.data.data;
        }
      },
      immediate: true,
    },
  },
  data() {
    return {
      fromPath:null,
      activeName: "first",
      defaults: {},
      form: {},
      vars: [], // 需要提交的字段
      submitLoading: false, // 提交时按钮loading
      option: {
        tabs: true,
        tabsActive: 1,
        span: 8,
        size: "mini",
        menuBtn: false,
        labelWidth: 130,
        column: [
          {
            type: "input",
            label: "申请人",
            span: 8,
            display: true,
            prop: "createUserName",
            value: "${this.$store.getters.userInfo.nick_name}",
            readonly: true,
          },
          {
            type: "input",
            label: "申请部门",
            span: 8,
            display: true,
            prop: "createDeptName",
            value: "${this.$store.getters.userInfo.dept_name}",
            readonly: true,
          },
          {
            label: "预算编号",
            prop: "budgetSerialNo",
            placeholder: " ",
            overHidden: true,
            disabled: true,
            rules: [
              {
                required: true,
                message: "请选择预算",
                trigger: "blur",
              },
            ],
          },
          {
            label: "预算名称",
            prop: "budgetTitle",
            placeholder: " ",
            overHidden: true,
            disabled: true,
          },
          {
            label: "增补编号",
            prop: "serialNo",
            placeholder: "系统自动生成",
            overHidden: true,
            search: true,
            disabled: true,
          },
          {
            label: "年度预算",
            prop: "year",
            labelTip: "年度预算可以使用多次",
            type: "radio",
            dicUrl: "/api/blade-system/dict/dictionary?code=yes_no",
            value: "2",
            disabled: true,
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            control: (val) => {
              if (val === "2") {
                return {
                  budgetTitle: {
                    display: true,
                  },
                  projectSerialNo: {
                    display: false,
                  },
                };
              } else {
                return {
                  budgetTitle: {
                    display: false,
                  },
                  projectSerialNo: {
                    display: true,
                  },
                };
              }
            },
          },
          {
            label: "小项",
            prop: "subCode",
            type: "select",
            dicData: [],
            props: {
              label: "name",
              value: "code",
            },
            placeholder: " ",
            width: 100,
            cell: true,
          },
          {
            label: "预算类型",
            prop: "type",
            placeholder: " ",
            type: "select",
            filterable: true,
            minWidth: 90,
            disabled: true,
            dicUrl: "/api/ni/por/type/list?status=0",
            props: {
              label: "name",
              value: "code",
              desc: "code",
            },
            typeformat(item, label, value) {
              return `${item[label]}(${item[value]})`;
            },
          },
          {
            label: "项目编号",
            prop: "projectSerialNo",
            placeholder: " ",
            display: true,
            disabled: true,
            align: "left",
            overHidden: true,
            search: true,
          },
          {
            label: "项目名称",
            prop: "projectTitle",
            placeholder: " ",
            display: true,
            overHidden: true,
            disabled: true,
          },
          {
            label: "项目剩余额度(w)",
            prop: "surplusBudget",
            placeholder: " ",
            disabled: true,
            display: false,
            hide: true,
            showColumn: false,
          },
          {
            label: "账套",
            prop: "brand",
            type: "radio",
            value: "1",
            dicUrl: "/api/blade-system/dict-biz/dictionary?code=ni_brand",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            disabled: true,
            rules: [
              {
                required: true,
                message: "用友账套",
                trigger: "blur",
              },
            ],
          },
          {
            label: "增补原由",
            prop: "remark",
            type: "textarea",
            placeholder: " ",
            overHidden: true,
            span: 24,
            minRows: 3,
            rules: [
              {
                required: true,
                message: "请输入增补原由",
                trigger: "blur",
              },
            ],
          },
          {
            label: "附件",
            type: "upload",
            propsHttp: {
              res: "data",
              url: "link",
              name: "originalName",
            },
            action: "/api/blade-resource/oss/endpoint/put-file",
            display: true,
            span: 24,
            showFileList: true,
            multiple: true,
            limit: 10,
            prop: "attachment",
          },
          {
            label: "预算明细",
            prop: "items",
            labelPosition: "top",
            span: 24,
          },
        ],
      },
      item: {
        selectionList: [],
        option: {
          cellBtn: true,
          addBtn: false,
          refreshBtn: false,
          columnBtn: false,
          menu: false,
          saveBtn: false,
          updateBtn: false,
          cancelBtn: false,
          editBtn: false,
          delBtn: false,
          dialogFullscreen: true,
          size: "mini",
          align: "center",
          calcHeight: 30,
          tip: false,
          border: true,
          viewBtn: false,
          dialogClickModal: false,
          selection: true,
          showSummary: true,
          sumColumnList: [
            {
              name: "num",
              type: "sum",
              decimals: 1,
            },
            {
              name: "amount",
              type: "sum",
            },
          ],
          column: [
            {
              label: "小项",
              prop: "subCode",
              type: "select",
              dicData: [],
              props: {
                label: "name",
                value: "code",
              },
              placeholder: " ",
              width: 100,
              cell: true,
            },
            {
              label: "类型",
              prop: "cost",
              placeholder: " ",
              width: 70,
              disabled: true,
            },
            {
              label: "品名",
              prop: "materialName",
              type: "textarea",
              minRows: 1,
              placeholder: " ",
              overHidden: true,
              cell: true,
            },
            {
              label: "用途",
              prop: "purpose",
              type: "textarea",
              minRows: 1,
              placeholder: " ",
              overHidden: true,
              cell: true,
            },
            {
              label: "编码",
              minWidth: 100,
              placeholder: " ",
              prop: "materialCode",
              overHidden: true,
              clearable: false,
              cell: true,
            },
            {
              label: "规格",
              prop: "specification",
              placeholder: " ",
              overHidden: true,
              disabled: true,
            },
            {
              label: "材质",
              prop: "quality",
              placeholder: " ",
              disabled: true,
              overHidden: true,
            },
            {
              label: "国标",
              prop: "gb",
              placeholder: " ",
              disabled: true,
              overHidden: true,
            },
            {
              label: "单位",
              prop: "unit",
              type: "select",
              dicUrl:
                "/api/blade-system/dict-biz/dictionary?code=ni_material_unit",
              props: {
                label: "dictValue",
                value: "dictKey",
              },
              placeholder: " ",
              slot: true,
              rules: [
                {
                  required: true,
                  message: "请选择单位",
                  trigger: "blur",
                },
              ],
            },
            {
              label: "数量",
              prop: "num",
              type: "number",
              precision: 0,
              placeholder: " ",
              minWidth: 100,
              cell: true,
              rules: [
                {
                  required: true,
                  message: "请输入数量",
                  trigger: "blur",
                },
              ],
            },
            {
              label: "单价",
              prop: "price",
              type: "number",
              controls: false,
              disabled: true,
              precision: 2,
              cell: true,
              placeholder: " ",
            },
            {
              label: "金额",
              prop: "amount",
              overHidden: true,
              type: "number",
              cell: true,
              minWidth: 100,
              precision: 2,
              placeholder: " ",
              rules: [
                {
                  required: true,
                  message: "请输入金额",
                  trigger: "blur",
                },
              ],
              change: ({ row, value }) => {
                if (row.num) {
                  row.price = (value / row.num).toFixed(2);
                } else {
                  row.price = 0.0;
                }
              },
            },
            {
              label: "压力容器",
              prop: "pv",
              cell: true,
              type: "radio",
              width: 110,
              dicData: [
                {
                  label: "是",
                  value: 1,
                },
                {
                  label: "否",
                  value: 0,
                },
              ],
            },
            {
              label: "备注",
              prop: "remark",
              type: "textarea",
              minRows: 1,
              placeholder: " ",
              cell: true,
            },
            {
              label: "附件",
              type: "upload",
              width: 94,
              propsHttp: {
                res: "data",
                url: "attachId",
                name: "originalName",
              },
              cell: true,
              action:
                "/api/blade-resource/oss/endpoint/put-file-attach?code=public",
              display: true,
              showFileList: true,
              multiple: true,
              limit: 10,
              prop: "attach",
            },
          ],
        },
        excelBox: false,
        excelOption: {
          size: "mini",
          searchSize: "mini",
          submitBtn: false,
          emptyBtn: false,
          column: [
            {
              label: "数据上传",
              prop: "excelFile",
              type: "upload",
              drag: true,
              loadText: "模板上传中，请稍等",
              span: 24,
              data: {},
              propsHttp: {
                res: "data",
              },
              tip: "请上传 .xls,.xlsx 标准格式文件",
              action: "/api/ni/por/budget/item/parse-items",
            },
            {
              label: "模板下载",
              prop: "excelTemplate",
              formslot: true,
              span: 24,
            },
          ],
        },
        excelForm: {},
        actionItem: null,
        actionItemIndex: null,
      },
    };
  },
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      //  这里的vm指的就是vue实例，可以用来当做this使用
      vm.fromPath = from.path; //获取上一级路由的路径
    });
  },
  methods: {
    rowAttach(row, index) {
      if (!this.option.detail) {
        return;
      }
      this.actionItem = row;
      this.actionItemIndex = index;
      this.$refs.attachRef.init(row.id, "ni_por_budget_item");
    },
    handleRefreshAttach() {
      getAttachList({
        businessName: "ni_por_budget_item",
        businessKey: this.actionItem.id,
      }).then((res) => {
        const data = res.data.data;
        this.actionItem.attach = data.map((item) => {
          return {
            label: item.originalName,
            value: item.id,
          };
        });
        this.form.items.splice(this.actionItemIndex, 1, { ...this.actionItem });
      });
    },
    handleMaterialSubmit(selectionList, row1) {
      row1.materialCode = selectionList[0].code;
      row1.materialName = selectionList[0].name;
      row1.materialId = selectionList[0].id;
      row1.specification = selectionList[0].specification;
      row1.quality = selectionList[0].quality;
      row1.gb = selectionList[0].gb;
      row1.unit = selectionList[0].unit;
    },
    projectConfirm(selectionList) {
      if (selectionList) {
        this.$nextTick(() => this.$refs.form.clearValidate());
        console.log(selectionList);
        this.form.projectSerialNo = selectionList[0].serialNo;
        this.form.projectTitle = selectionList[0].title;
      }
    },
    handleBudgetConfirm(selectList, row) {
      if (selectList && selectList.length > 0) {
        row.projectId = selectList[0].projectId;
        row.projectTitle = selectList[0].projectTitle;
        row.projectSerialNo = selectList[0].projectSerialNo;
        getBudgetDetail(row.budgetId).then((res) => {
          this.form = res.data.data;
          this.form.budgetSerialNo = this.form.serialNo;
          this.form.id = null;
          this.form.parentId = row.budgetId;
          this.form.title = null;
          this.form.serialNo = null;
          this.form.remark = null;
          this.form.items = [];
          this.form.status = null;
        });
      }
    },
    handleTemplate() {
      exportBlob(
        `/api/ni/por/budget/item/export-template?${this.website.tokenHeader
        }=${getToken()}`
      ).then((res) => {
        downloadXls(res.data, "明细导入模板.xlsx");
      });
    },
    uploadAfter(res, done) {
      this.item.excelBox = false;
      done();
      if (res) {
        res.forEach((item) => {
          this.form.items.push(item);
        });
      }
    },
    handlePriceChange(price, row) {
      if (row.num) {
        row.amount = (Number(row.num) * Number(price)).toFixed(2);
      }
    },
    handleNumChange(num, row) {
      if (row.amount) {
        row.amount = (Number(row.amount) / Number(row.num)).toFixed(2);
      }
    },
    itemImport() { },
    itemAdd(cost) {
      const materialCode = this.findObject(
        this.item.option.column,
        "materialCode"
      );
      if (cost === "1") {
        this.$refs.materialSelectDialogRef.visible = true;
        materialCode.rules = [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
        ];
      } else if (cost === "2") {
        this.form.items.forEach((item) => (item.$cellEdit = false));
        this.form.items.push({ cost: true, $cellEdit: true, num: 1, pv: 0 });
        materialCode.rules = [
          {
            required: false,
            message: "请输入",
            trigger: "blur",
          },
        ];
      }
    },
    itemCellClickChange(row) {
      this.form.items.forEach((item) => (item.$cellEdit = false));
      if (!this.option.detail) row.$cellEdit = true;
    },
    itemSelectionChange(list) {
      this.item.selectionList = list;
    },
    itemSelectionClear() {
      this.item.selectionList = [];
      this.$refs.itemCrud.toggleSelection();
    },
    itemDelete() {
      const indexList = this.item.selectionList.map((item) => item.$index);
      const [...items] = this.form.items.filter(
        (item, index) => !indexList.includes(index)
      );
      this.form.items = items;
    },
    handleItemAddSubmit(selectList) {
      if (selectList) {
        selectList.forEach((item) => {
          const row = {
            subCode: this.form.subCode,
            typeId: item.typeId,
            materialCode: item.code,
            materialName: item.name,
            materialTypeId: item.typeId,
            materialId: item.id,
            specification: item.specification,
            quality: item.quality,
            gb: item.gb,
            unit: item.unit,
            cost: item.cost,
            pv: 0,
          };
          this.form.items.push(row);
        });
      }
    },
    // 获取任务详情
    getDetail(taskId, processInsId) {
      this.getTaskDetail(taskId, processInsId).then((res) => {
        const { process } = res;
        const { variables, status, assignee, processIsFinished } = process;
        const option = this.option;
        option.menuBtn = false;
        // 增加对status值的基本校验，确保其不是未定义或者空值的情况
        option.detail =
          !["recall", "reject"].includes(processIsFinished) &&
          status !== "todo";
        if (
          assignee === this.userInfo.user_id &&
          (["recall", "reject"].includes(processIsFinished) ||
            process.taskName === "发起人")
        ) {
          option.detail = false;
        } else {
          option.detail = true;
        }
        const { column, group } = option;
        column.forEach((col) => {
          if (col.value) col.value = this.getDefaultValues(col.value);
        });
        if (group && group.length > 0) {
          // 处理group
          group.forEach((gro) => {
            gro.column.forEach((col) => {
              if (col.value) col.value = this.getDefaultValues(col.value);
            });
          });
        }
        for (let key in variables) {
          if (!variables[key]) delete variables[key];
        }
        const serialNumber = this.findObject(
          this.option.column,
          "serialNumber"
        );
        if (
          option.column &&
          process.variables &&
          process.variables.serialNumber &&
          (!serialNumber || serialNumber === -1)
        ) {
          option.column.unshift({
            label: "流水号",
            prop: "serialNumber",
            span: 24,
            detail: true,
          });
        }

        this.option = option;
        this.form = variables;
        this.waiting = false;
        this.submitLoading = false;
      });
    },
    // 审核
    handleExamine(pass) {
      this.submitLoading = true;
      this.$refs.form.validate((valid, done) => {
        if (valid) {
          const variables = {};
          this.option.column.forEach((v) => {
            if (this.form[v.prop]) variables[v.prop] = this.form[v.prop];
          });
          console.log(variables);

          this.handleCompleteTask(pass, variables)
            .then(() => {
              this.$message.success("处理成功");
              if (this.fromPath) {
                this.handleCloseTag(this.fromPath);
              } else
              this.handleCloseTag("/plugin/workflow/process/todo");
            })
            .catch(() => {
              done();
              this.submitLoading = false;
            });
        } else {
          done();
          this.submitLoading = false;
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
/deep/ .el-link--inner {
  font-size: 12px;
}

/deep/ .el-upload-list__item-name {
  font-size: 12px;
}

.header {
  width: 100%;
  height: 50px;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 10px 10px 0;
}
</style>

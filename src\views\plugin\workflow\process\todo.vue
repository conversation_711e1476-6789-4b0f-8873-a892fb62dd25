<template>
  <basic-container class="wf-container">
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :search.sync="query"
      :permission="permissionList"
      v-model="form"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="onLoad(page, query)"
      @on-load="onLoad"
    >
      <template #taskName="{ row, index }">
        <flow-timeline-popover
          :process-ins-id="row.processInstanceId"
          :form-key="row.formKey"
          :process-def-key="row.processDefinitionKey"
          v-model="row.taskName"
          trigger="click"
          lazy
        />
      </template>
      <template #menuLeft>
        <el-radio-group
          size="mini"
          v-model="processDefName"
          @input="handleProcessDefNameChange"
        >
          <el-radio-button label="预算"></el-radio-button>
          <el-radio-button label="付款申请(未付款)"></el-radio-button>
          <el-radio-button label="付款申请(已付款)"></el-radio-button>
          <el-radio-button label="报销申请"></el-radio-button>
          <el-radio-button label="合同"></el-radio-button>
        </el-radio-group>
      </template>
      <template slot="menu" slot-scope="{ row }">
        <el-button
          v-if="permission.wf_process_todo_detail"
          type="text"
          size="small"
          icon="el-icon-info"
          @click="dynamicRoute(row, 'detail')"
        >详情
        </el-button>
        <el-button
          v-if="permission.wf_process_todo_follow"
          type="text"
          size="small"
          icon="el-icon-search"
          @click="handleFlow(row)"
        >流程图
        </el-button>
      </template>
      <template #searchMenu v-if="permission.wf_process_todo_search">
        <el-badge
          :value="query.formSearch ? query.formSearch.split(',').length : 0"
          :hidden="!query.formSearch || query.formSearch.split(',').length == 0"
        >
          <el-button
            icon="el-icon-search"
            @click="$refs['wf-search'].visible = true"
          >表单
          </el-button>
        </el-badge>
      </template>
    </avue-crud>

    <el-dialog
      :visible.sync="bpmnVisible"
      append-to-body
      destroy-on-close
      title="流程图"
      width="70%"
      custom-class="wf-dialog"
    >
      <wf-design
        ref="bpmn"
        style="height: 60vh"
        :options="bpmnOption"
      ></wf-design>
    </el-dialog>
    <wf-search ref="wf-search" v-model="query.formSearch"></wf-search>

    <el-dialog
      title="请扫描"
      :visible.sync="scanVisible"
      width="30%"
      append-to-body
    >
      <el-input
        v-model="processInsIdInput"
        @keyup.enter.native="handleSubmit"
        placeholder="流程编号"
        ref="processInsInput"
      ></el-input>
      <span slot="footer" class="dialog-footer">
        <el-button @click="scanVisible = false">取 消</el-button>
        <el-button
          type="primary"
          @click="scanVisible = false"
          @keyup.enter.native="handleSubmit"
        >确 定</el-button
        >
      </span>
    </el-dialog>
  </basic-container>
</template>

<script>
import {todoList as getList, detail} from "@/api/plugin/workflow/process";
import {
  unAuditAndPaylist,
  unAuditAndUnPaylist,
} from "@/api/ni/fin/payable-apply";
import {mapGetters} from "vuex";

import exForm from "../mixins/ex-form";

import WfSearch from "./components/search.vue";
import FlowTimelinePopover from "@/components/wf-flow-timeline/popover";

export default {
  mixins: [exForm],
  components: {WfSearch, FlowTimelinePopover},
  data() {
    return {
      processInsIdInput: "",
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        searchEnter: true,
        size: "mini",
        height: "auto",
        calcHeight: 30,
        tip: false,
        border: true,
        selection: true,
        dialogType: "drawer",
        addBtn: false,
        editBtn: false,
        delBtn: false,
        align: "center",
        searchMenuSpan: 6,
        searchSize: "mini",
        searchIndex: 3,
        searchIcon: true,
        column: [
          {
            label: "流程名称",
            prop: "processDefinitionName",
            overHidden: true,
          },
          {
            label: "流程名称",
            prop: "processDefinitionKey1",
            type: 'select',
            dicUrl: "/api/blade-workflow/design/deployment/list?current=1&size=30&name={{key}}",
            remote: true,
            props: {
              label: "name",
              value: "key",
              desc: 'version'
            },
            dicFormatter: (data) => {
              return data.data.records;
            },
            placeholder: " ",
            filterable: true,
            hide: true,
            showColumn: false,
            search: true,
            searchOrder: 99,
            overHidden: true,
          },
          {
            label: "流程标识",
            prop: "processDefinitionKey",
            overHidden: true,
          },
          {
            label: "流水号",
            prop: "serialNumber",
            bind: "variables.serialNumber",
            search: true,
            searchOrder: 98,
            overHidden: true,
          },
          {
            label: "业务编号",
            prop: "serialNo",
            bind: "variables.serialNo",
            search: true,
            searchOrder: 97,
            overHidden: true,
          },
          {
            label: "流程分类",
            row: true,
            type: "tree",
            dicUrl: "/api/blade-workflow/design/category/tree",
            props: {
              label: "name",
              value: "id",
            },
            prop: "category",
            search: true,
          },
          {
            label: "当前节点",
            prop: "taskName",
          },
          {
            label: "申请人",
            prop: "startUsername",
            search: true,
          },
          {
            label: "申请时间",
            prop: "createTime",
            type: "datetime",
            format: "yyyy-MM-dd HH:mm",
            width: 165,
          },
        ],
      },
      data: [],
      bpmnVisible: false,
      bpmnOption: {},
      processDefName: "",
    };
  },
  computed: {
    ...mapGetters(["userInfo", "permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.deployment_add, false),
        viewBtn: this.vaildData(this.permission.deployment_view, false),
        delBtn: this.vaildData(this.permission.deployment_delete, false),
        editBtn: this.vaildData(this.permission.deployment_edit, false),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  mounted() {
    this.$bus.$on("todo_new_message_event", () => {
      this.searchReset();
    });
  },
  beforeDestroy() {
    this.$bus.$off("todo_new_message_event");
  },
  methods: {
    async handleProcessDefNameChange(val) {
      if (["付款申请(未付款)", "付款申请(已付款)"].includes(val)) {
        this.query.processDefinitionName = "付款申请";
        //获取付款申请数据
        let res;
        if ("付款申请(已付款)" === val) {
          res = await unAuditAndPaylist();
        } else {
          res = await unAuditAndUnPaylist();
        }
        const {data} = res.data;
        const processInsIds = new Set();
        data.forEach((item) => {
          if (item.processInsId) processInsIds.add(item.processInsId);
        });
        if (processInsIds.size > 0)
          this.query.processInstanceId = Array.from(processInsIds).join(",");
      } else this.query.processDefinitionName = val;
      this.page.currentPage = 1;
      this.onLoad(this.page, this.query);
    },
    handleDetail(row) {
      const param = {
        taskId: row.taskId,
        processInsId: row.processInstanceId,
      };
      this.$router.push(
        "/workflow/process/detail/" +
        Buffer.from(JSON.stringify(param)).toString("base64")
      );
    },
    handleFlow(row) {
      const {taskId, processInstanceId} = row;
      detail({taskId, processInsId: processInstanceId}).then((res) => {
        const {process, flow} = res.data.data;

        this.bpmnOption = {
          mode: "view",
          xml: process.xml,
          flows: this.handleResolveFlows(flow),
        };

        console.log(this.bpmnOption);

        this.bpmnVisible = true;
      });
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    onLoad(page, params = {}) {
      this.loading = true;
      const q = {
        ...params,
        ...this.query
      }
      if (q.processDefinitionKey1) {
        q.processDefinitionKey = q.processDefinitionKey1
      }
      getList(
        page.currentPage,
        page.pageSize,
        q
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
      });
    },
  },
};
</script>

<style lang="scss">
.wf-container {
  .el-collapse-item__content {
    padding-top: 8px;
  }

  .el-badge__content.is-fixed {
    right: 16px;
  }
}

.wf-dialog {
  display: flex;
  flex-direction: column;
  margin: 0 !important;
  position: absolute;
  top: 40%;
  left: 50%;
  transform: translate(-50%, -40%);
  max-height: calc(100% - 30px);
  max-width: calc(100% - 30px);

  .el-dialog__body {
    flex: 1;
    overflow: auto;
  }
}
</style>

import request from "@/router/axios";

export const getList = (current, size, params) => {
    return request({
        url:"/api/ni/ofc/expressManage/page",
        method:"get",
        params:{
            ...params,
            current,
            size,
        },
    });
};

export const getDetail = (id) => {
    return request({
      url: "/api/ni/ofc/expressManage/detail",
      method: "get",
      params: {
        id,
      },
    });
  };

  export const add = (row) => {
    return request({
        url:"/api/ni/ofc/expressManage/add",
        method:"post",
        params:{
            data:row,
        },
    });
  };

  export const remove = (ids) => {
    return request({
        url:"/api/ni/ofc/expressManage/remove",
        method:"post",
        params:{
            ids,
        },
    });
  };

  export const update = (row , pos) => {
    return request({
        url:"/api/ni/ofc/expressManage/update",
        method:"post",
        data: row,
        params:pos
    });
  };

  export const submit = (ids) => {
    return request({
      url: '/api/ni/ofc/expressManage/submit',
      method: 'post',
      params: {
        ids,
      }
    })
  }

  export const back = (ids) => {
    return request({
      url: '/api/ni/ofc/expressManage/back',
      method: 'post',
      params: {
        ids,
      }
    })
  }

  export const save = (row , pos) => {
    return request({
      url: "/api/ni/ofc/expressManage/save",
      method: "post",
      data:row,
      params:pos
    });
  };

/**
 * 驳回
 */
export const reject = (ids) => {
  return request({
    url: '/api/ni/ofc/expressManage/reject',
    method: 'post',
    params: {
      ids,
    }
  })
}

/**
 * 草稿
 */
export const draft = (ids) => {
  return request({
    url: '/api/ni/ofc/expressManage/draft',
    method: 'post',
    params: {
      ids,
    }
  })
}

/**
 *结算
 */
export const settlement = (ids) => {
  return request({
    url: '/api/ni/ofc/expressManage/settlement',
    method: 'post',
    params: {
      ids,
    }
  })
}

/**
 * 批量对账
 */
export const verify = (ids) => {
  return request({
    url:"/api/ni/ofc/expressManage/verify",
    method:"post",
    params:{
      ids,
    },
  });
};

// export const getItemsExpressPrintData = (Id) => {
//   return request({
//     url: "/api/ni/por/budget/getItemsPrintData",
//     method: "get",
//     params: {
//       budgetId,
//     },
//   });
// };

export const getItemsExpressPrintData = (params) => {
  return request({
    url: "/api/ni/ofc/expressManage/getItemsExpressPrintData",
    method: "get",
    ...params,
  });
};

// export const expressManageExport = (params) => {
//   return request({
//     url: "/api/ni/ofc/expressManage/expressManageExport",
//     method: "get",
//       params,
//   });
// };

/**
 * 校验快递
 */
export const verifyExpress = (data) => {
  return request({
    url:"/api/ni/ofc/expressManage/verifyExpress",
    method:"post",
    params:{
      data,
    },
  });
};

/**
 * 批量导入快递
 */
export const batchLoad = (row) => {
  return request({
    url:"/api/ni/ofc/expressManage/batchLoad",
    method:"post",
    params:{
      data: row,
    },
  });
};

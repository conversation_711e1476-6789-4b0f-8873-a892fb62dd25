<template>
  <basic-container>
    <avue-skeleton :loading="waiting" avatar :rows="8">
      <div style="display: flex;">
      <avue-title
        style="margin-bottom: 20px"
        :styles="{ fontSize: '20px' }"
        :value="process.name"
      ></avue-title>
        <el-badge v-if="permission.wf_process_draft&&draftCount > 0" :value="draftCount"
                  style="margin-top: 5px;  margin-right: 40px;" type="warning">
          <el-button
            size="mini"
            v-loading="loading"
            @click="handleDraftBox"
          >草稿箱
          </el-button>
        </el-badge>
      </div>
      <el-card shadow="never" style="margin-top: 20px">
        <!-- 自定义表单区域 -->
        <avue-form
          v-if="
            option &&
            ((option.column && option.column.length > 0) ||
              (option.group && option.group.length > 0))
          "
          v-model="form"
          ref="form"
          :option="option"
          :defaults.sync="defaults"
          @submit="handleSubmit"
          @error="loading = false"
          :upload-preview="handleUploadPreview"
        >
          <template #projectId="{ disabled, size, index, row }">
            <project-select
              v-model="form.projectId"
              :size="size"
              :params="{ status: 9 }"
              :disabled="disabled"
              @confirm="projectConfirm"
            />
          </template>
          <template #itemsLabel>
            <span style="font-size: 16px; font-weight: 500">预算明细</span>
            <el-divider direction="vertical"></el-divider>
            <el-dropdown @command="itemAdd">
              <el-button type="primary" size="mini" icon="el-icon-plus" plain
              >添加<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="1">实物</el-dropdown-item>
                <el-dropdown-item command="2">费用</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
            <el-button
              type="danger"
              size="mini"
              icon="el-icon-delete"
              plain
              :disabled="item.selectionList.length <= 0"
              @click="itemDelete"
            >删 除
            </el-button>
            <el-button
              type="primary"
              size="mini"
              icon="el-icon-upload2"
              plain
              @click="() => (item.excelBox = true)"
            >导入
            </el-button>
          </template>
          <template #items="{ row, disabled }">
            <avue-crud
              :option="item.option"
              :data="form.items"
              @selection-change="itemSelectionChange"
              ref="itemCrud"
              @cell-click="itemCellClickChange"
            >
              <template #cost="{ row, index }">
                <el-tag size="mini" type="danger" effect="dark" v-if="row.cost">
                  费用
                </el-tag>
                <el-tag size="mini" type="info" effect="plain" v-else>
                  实物
                </el-tag>
              </template>
              <template #pv="{ row, index }">
                <el-tag size="mini" type="danger" effect="dark" v-if="row.pv">
                  是
                </el-tag>
                <el-tag size="mini" type="info" effect="plain" v-else>
                  否
                </el-tag>
              </template>
              <template #materialCodeForm="{ row, disabled, size, index }">
                <material-select
                  v-model="row.materialId"
                  :size="size"
                  :disabled="disabled"
                  @submit="handleMaterialSubmit($event, row)"
                />
              </template>
              <template #numForm="{ row, disabled, size }">
                <el-input-number
                  :size="size"
                  v-model="row.num"
                  :disabled="disabled"
                  :min="0"
                  :controls="false"
                  style="width: 100%"
                  @change="handleNumChange($event, row)"
                />
              </template>
              <template #priceForm="{ row, disabled, size }">
                <el-input-number
                  :size="size"
                  v-model="row.price"
                  :disabled="disabled"
                  :min="0"
                  :controls="false"
                  style="width: 100%"
                  @change="handlePriceChange($event, row)"
                />
              </template>
              <template #attach="{ row, index }">
                <el-link v-if="row.attach" type="primary" target="_blank">
                  <i class="el-icon-circle-plus-outline"/>
                  附件({{ row.attach ? row.attach.length : 0 }})
                </el-link>
              </template>
            </avue-crud>
          </template>
        </avue-form>
        <!-- 自定义表单区域 -->
      </el-card>

      <el-card shadow="never" style="margin-top: 20px" v-if="showExamForm">
        <wf-examine-form
          ref="examineForm"
          :process="process"
          @user-select="handleUserSelect"
        ></wf-examine-form>
      </el-card>
      <div style="height: 120px"></div>
      <el-row
        class="foot-item avue-affix"
        :style="{
          width: isCollapse ? 'calc(100% - 80px)' : 'calc(100% - 260px)',
        }"
        id="avue-view"
      >
        <el-button
          v-no-more-click
          type="primary"
          size="mini"
          v-loading="loading"
          @click="handleSubmit"
        >发起
        </el-button>
        <el-button
          v-if="permission.wf_process_draft"
          type="success"
          size="mini"
          v-loading="loading"
          @click="handleDraftNotClose(process.id, process.formKey, form, process.key)"
        >存为草稿
        </el-button>
        <el-button
          v-if="permission.wf_process_draft"
          type="success"
          size="mini"
          v-loading="loading"
          @click="handleDraft(process.id, process.formKey, form, process.key)"
        >存为草稿并关闭
        </el-button>
      </el-row>
    </avue-skeleton>
    <material-select-dialog
      ref="materialSelectDialogRef"
      multiple
      @submit="handleItemAddSubmit"
    />
    <!-- 人员选择弹窗 -->
    <wf-user-select
      ref="user-select"
      :check-type="checkType"
      :default-checked="defaultChecked"
      @onConfirm="handleUserSelectConfirm"
    />
    <el-dialog
      title="数据导入"
      append-to-body
      :visible.sync="item.excelBox"
      width="555px"
    >
      <avue-form
        :option="item.excelOption"
        v-model="item.excelForm"
        :upload-after="uploadAfter"
      >
        <template slot="excelTemplate">
          <el-button type="primary" @click="handleTemplate">
            点击下载<i class="el-icon-download el-icon--right"></i>
          </el-button>
        </template>
      </avue-form>
    </el-dialog>
    <!-- 草稿弹窗 -->
    <draft-popup
      :visible.sync="isDraftPopupVisible"
      :draftList="draftList"
      @select="handleDraftSelect"
      @delete="handleDraftDelete"
    ></draft-popup>
  </basic-container>
</template>

<script>
import WfExamineForm from "@/views/plugin/workflow/process/components/examForm.vue";
import WfUserSelect from "@/views/plugin/workflow/process/components/user-select";

import exForm from "@/views/plugin/workflow/mixins/ex-form";
import draft from "@/views/plugin/workflow/mixins/draft";
import {
  addMultiInstance,
  delegateTask,
  transferTask,
} from "@/api/plugin/workflow/process";
import ProjectSelect from "@/views/ni/project/components/MyProjectSelect";
import MaterialSelect from "@/views/ni/base/components/MaterialSelect1";
import FinAccountingSelect from "@/views/ni/fin/components/AccountingSelect";
import MaterialSelectDialog from "@/views/ni/base/components/MaterialSelectDialog";
import {exportBlob} from "@/api/common";
import {getToken} from "@/util/auth";
import {downloadXls} from "@/util/util";
import AttachDialog from "@/components/attach-dialog";
import DraftPopup from "@/views/plugin/workflow/process/components/draftPopup.vue";
import debounce from "@/util/debounce";

export default {
  components: {
    AttachDialog,
    WfUserSelect,
    WfExamineForm,
    ProjectSelect,
    MaterialSelect,
    FinAccountingSelect,
    MaterialSelectDialog,
    DraftPopup
  },
  mixins: [exForm, draft],
  activated() {
    let val = this.$route.query.p;
    if (val) {
      let text = Buffer.from(val, "base64").toString();
      text = text.replace(/[\r|\n|\t]/g, "");
      const param = JSON.parse(text);
      const {processId, processDefKey, form} = param;
      if (processId) {
        this.getForm(processId);
      } else if (processDefKey) {
        this.getFormByProcessDefKey(processDefKey);
      }
      if (form) {
        const f = JSON.parse(
          new Buffer(decodeURIComponent(form), "utf8").toString("utf8")
        );
        this.form = Object.assign(this.form, f);
      }
    }
  },
  watch: {
    "form.projectId": {
      handler(val) {
        const subCode1 = this.findObject(this.option.column, "subCode");
        const subCode = this.findObject(this.item.option.column, "subCode");
        if (val && val > 0)
          this.$http
            .get("/api/ni/project/sub/select?projectId=" + val)
            .then((res) => {
              subCode.dicData = res.data.data;
              subCode1.dicData = res.data.data;
            });
      },
      immediate: true,
    },
  },
  computed: {
    showExamForm() {
      const {hideComment, hideCopy, hideExamine} = this.process;
      return !hideComment || !hideCopy || !hideExamine;
    },
  },
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      console.log(from, "from");
      //  这里的vm指的就是vue实例，可以用来当做this使用
      vm.fromPath = from.path; //获取上一级路由的路径
    });
  },
  data() {
    return {
      defaults: {},
      form: {
        items: [],
      },
      process: {},
      loading: false,
      option: {
        tabs: true,
        tabsActive: 1,
        span: 8,
        size: "mini",
        menuBtn: false,
        labelWidth: 150,
        column: [
          {
            type: "input",
            label: "申请人",
            span: 8,
            display: true,
            prop: "createUserName",
            value: "${this.$store.getters.userInfo.nick_name}",
            readonly: true,
          },
          {
            type: "input",
            label: "申请部门",
            span: 8,
            display: true,
            prop: "createDeptName",
            value: "${this.$store.getters.userInfo.dept_name}",
            readonly: true,
          },

          {
            label: "预算编号",
            prop: "serialNo",
            placeholder: "系统自动生成",
            overHidden: true,
            disabled: true,
          },
          {
            label: "预算主题",
            prop: "title",
            placeholder: " ",
            rules: [
              {
                required: true,
                message: "请输入预算主题",
                trigger: "blur",
              },
            ],
          },
          {
            label: "预算类型",
            prop: "type",
            placeholder: " ",
            type: "select",
            dicUrl: "/api/ni/por/type/listWithPermission",
            props: {
              label: "name",
              value: "code",
              desc: "code",
            },
            rules: [
              {
                required: true,
                message: "请选择预算类型",
                trigger: "blur",
              },
            ],
          },
          {
            label: "项目名称",
            prop: "projectId",
            placeholder: " ",
            rules: [
              {
                required: true,
                message: "请选择项目",
                trigger: "blur",
              },
            ],
          },
          {
            label: "小项",
            prop: "subCode",
            type: "select",
            dicData: [],
            props: {
              label: "name",
              value: "code",
              desc: "code",
            },
            placeholder: " ",
            width: 100,
            rules: [
              {
                required: false,
                message: "请选择小项",
                trigger: "blur",
              },
            ],
            change: ({value}) => {
              if (value && this.form.items && this.form.items.length > 0) {
                const subItems = new Set();
                this.form.items.forEach((item) => {
                  if (item.subCode && item.subCode !== value) {
                    subItems.add(item.subCode);
                  }
                });
                if (subItems.size > 0) {
                  this.$confirm("是否修改已设置小项的明细?", "提示", {
                    confirmButtonText: "是",
                    cancelButtonText: "否",
                    type: "warning",
                  })
                    .then(() => {
                      this.form.items.forEach((item) => {
                        item.subCode = value;
                      });
                    })
                    .catch(() => {
                      this.form.items.forEach((item) => {
                        if (!item.subCode) {
                          item.subCode = value;
                        }
                      });
                    });
                } else {
                  this.form.items.forEach((item) => {
                    item.subCode = value;
                  });
                }
                console.log(this.form.items);
              }
              console.log(
                value && this.form.items && this.form.items.length > 0
              );
              console.log(this.form.items);
            },
          },
          {
            label: "费用浮动比例%(±)",
            prop: "intervalRate",
            type: "number",
            value: 15,
            hide: true,
          },
          {
            label: "费用核算依据",
            prop: "hsyj",
            hide: true,
          },
          {
            label: "账套",
            prop: "brand",
            type: "radio",
            value: "1",
            dicUrl: "/api/blade-system/dict-biz/dictionary?code=ni_brand",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            search: true,
            rules: [
              {
                required: true,
                message: "账套",
                trigger: "blur",
              },
            ],
          },
          {
            label: "有效期(月)",
            prop: "validity",
            type: "number",
            min: 0,
            precision: 0,
            hide: true,
          },
          {
            label: "备注",
            prop: "remark",
            type: "textarea",
            span: 24,
            minRows: 3,
          },
          {
            label: "附件",
            type: "upload",
            propsHttp: {
              res: "data",
              url: "attachId",
              name: "originalName",
            },
            action:
              "/api/blade-resource/oss/endpoint/put-file-attach?code=public",
            display: true,
            span: 24,
            showFileList: true,
            multiple: true,
            limit: 10,
            prop: "attachment",
          },
          {
            label: "预算明细",
            prop: "items",
            labelPosition: "top",
            span: 24,
          },
        ],
      },
      item: {
        selectionList: [],
        option: {
          header: false,
          menu: false,
          cellBtn: true,
          addBtn: false,
          refreshBtn: false,
          columnBtn: false,
          saveBtn: false,
          updateBtn: false,
          cancelBtn: false,
          editBtn: false,
          delBtn: false,
          dialogFullscreen: true,
          size: "mini",
          align: "center",
          calcHeight: 30,
          tip: false,
          border: true,
          viewBtn: false,
          dialogClickModal: false,
          selection: true,
          showSummary: true,
          sumColumnList: [
            {
              name: "num",
              type: "sum",
              decimals: 1,
            },
            {
              name: "amount",
              type: "sum",
            },
          ],
          column: [
            {
              label: "小项",
              prop: "subCode",
              type: "select",
              dicData: [],
              props: {
                label: "name",
                value: "code",
                desc: "code",
              },
              placeholder: " ",
              width: 100,
              cell: true,
              rules: [
                {
                  required: false,
                  message: "请选择小项",
                  trigger: "blur",
                },
              ],
            },
            {
              label: "类型",
              prop: "cost",
              placeholder: " ",
              width: 70,
              disabled: true,
            },
            {
              label: "品名",
              prop: "materialName",
              type: "textarea",
              minRows: 1,
              placeholder: " ",
              overHidden: true,
              cell: true,
            },
            {
              label: "用途",
              prop: "purpose",
              type: "textarea",
              minRows: 1,
              placeholder: " ",
              overHidden: true,
              cell: true,
            },
            {
              label: "编码",
              minWidth: 100,
              placeholder: " ",
              prop: "materialCode",
              clearable: false,
              cell: true,
              rules: [
                {
                  required: true,
                  message: "请输入",
                  trigger: "blur",
                },
              ],
            },
            {
              label: "规格",
              prop: "specification",
              placeholder: " ",
              overHidden: true,
              disabled: true,
            },
            {
              label: "材质",
              prop: "quality",
              placeholder: " ",
              disabled: true,
              overHidden: true,
            },
            {
              label: "国标",
              prop: "gb",
              placeholder: " ",
              disabled: true,
              overHidden: true,
            },
            {
              label: "单位",
              prop: "unit",
              type: "select",
              dicUrl:
                "/api/blade-system/dict-biz/dictionary?code=ni_material_unit",
              props: {
                label: "dictValue",
                value: "dictKey",
              },
              placeholder: " ",
              slot: true,
              rules: [
                {
                  required: true,
                  message: "请选择单位",
                  trigger: "blur",
                },
              ],
            },
            {
              label: "数量",
              prop: "num",
              type: "number",
              precision: 0,
              placeholder: " ",
              minWidth: 100,
              cell: true,
              rules: [
                {
                  required: true,
                  message: "请输入数量",
                  trigger: "blur",
                },
              ],
            },
            {
              label: "单价",
              prop: "price",
              type: "number",
              controls: false,
              disabled: true,
              precision: 2,
              cell: true,
              placeholder: " ",
            },
            {
              label: "金额",
              prop: "amount",
              overHidden: true,
              type: "number",
              cell: true,
              minWidth: 100,
              precision: 2,
              controls: false,
              placeholder: " ",
              rules: [
                {
                  required: true,
                  message: "请输入金额",
                  trigger: "blur",
                },
              ],
              change: ({row, value}) => {
                if (row.num) {
                  row.price = (value / row.num).toFixed(2);
                } else {
                  row.price = 0.0;
                }
              },
            },
            {
              label: "压力容器",
              prop: "pv",
              cell: true,
              type: "radio",
              width: 110,
              dicData: [
                {
                  label: "是",
                  value: 1,
                },
                {
                  label: "否",
                  value: 0,
                },
              ],
            },
            {
              label: "备注",
              prop: "remark",
              type: "textarea",
              minRows: 1,
              placeholder: " ",
              overHidden: true,
              cell: true,
            },
            {
              label: "附件",
              type: "upload",
              width: 94,
              propsHttp: {
                res: "data",
                url: "attachId",
                name: "originalName",
              },
              cell: true,
              action:
                "/api/blade-resource/oss/endpoint/put-file-attach?code=public",
              display: true,
              showFileList: true,
              multiple: true,
              limit: 10,
              prop: "attach",
            },
          ],
        },
        excelBox: false,
        excelOption: {
          size: "mini",
          searchSize: "mini",
          submitBtn: false,
          emptyBtn: false,
          column: [
            {
              label: "数据上传",
              prop: "excelFile",
              type: "upload",
              drag: true,
              loadText: "模板上传中，请稍等",
              span: 24,
              data: {},
              // propsHttp: {
              //   res: "data",
              // },
              tip: "请上传 .xls,.xlsx 标准格式文件",
              action: "/api/ni/por/budget/item/parse-items",
            },
            {
              label: "模板下载",
              prop: "excelTemplate",
              formslot: true,
              span: 24,
            },
          ],
        },
        excelForm: {},
      },
      fromPath: "",
      isDraftPopupVisible: false,
      draftList: [],
      draftCount: 0,
      draftId: null
    };
  },
  methods: {
    handleTemplate() {
      exportBlob(
        `/api/ni/por/budget/item/export-template?${
          this.website.tokenHeader
        }=${getToken()}`
      ).then((res) => {
        downloadXls(res.data, "明细导入模板.xlsx");
      });
    },
    // uploadAfter(res, done) {
    //   this.item.excelBox = false;
    //   done();
    //   if (res) {
    //     res.forEach((item) => {
    //       this.form.items.push(item);
    //     });
    //   }
    // },
    uploadAfter(res, done) {
      this.item.excelBox = false;
      done();
      if (res) {
        if (res.msg) {
          // alert弹窗显示未导入进去的数据
          this.$alert(res.msg, "以下数据未导入成功，请手动添加或核对后重新提交", {
            dangerouslyUseHTMLString: true,
            callback: () => {
              // 弹窗关闭后的回调
              res.data.forEach((item) => {
                this.form.items.push(item);
              });
            },
          });
        } else {
          // 如果没有msg，直接添加 items
          res.data.forEach((item) => {
            this.form.items.push(item);
          });
        }
      }
    },
    handleNumChange(num, row) {
      if (row.price) {
        row.amount = (Number(row.num) * Number(row.price)).toFixed(2);
      }
    },
    handlePriceChange(price, row) {
      if (row.num) {
        row.amount = (Number(row.num) * Number(price)).toFixed(2);
      }
    },
    itemImport() {
    },
    itemAdd(cost) {
      const materialCode = this.findObject(
        this.item.option.column,
        "materialCode"
      );
      if (cost === "1") {
        this.$refs.materialSelectDialogRef.visible = true;
        materialCode.rules = [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
        ];
      } else if (cost === "2") {
        this.form.items.forEach((item) => (item.$cellEdit = false));
        this.form.items.push({
          subCode: this.form.subCode ? this.form.subCode : "",
          cost: true,
          $cellEdit: true,
          num: 1,
          amount: 0,
          pv: 0,
        });
        materialCode.rules = [
          {
            required: false,
            message: "请输入",
            trigger: "blur",
          },
        ];
      }
    },
    itemCellClickChange(row) {
      this.form.items.forEach((item) => (item.$cellEdit = false));
      row.$cellEdit = true;
    },
    itemSelectionChange(list) {
      this.item.selectionList = list;
    },
    itemSelectionClear() {
      this.item.selectionList = [];
      this.$refs.itemCrud.toggleSelection();
    },
    itemDelete() {
      const indexList = this.item.selectionList.map((item) => item.$index);
      const [...items] = this.form.items.filter(
        (item, index) => !indexList.includes(index)
      );
      this.form.items = items;
    },
    handleItemAddSubmit(selectList) {
      if (selectList) {
        selectList.forEach((item) => {
          const row = {
            subCode: this.form.subCode ? this.form.subCode : "",
            typeId: item.typeId,
            materialCode: item.code,
            materialName: item.name,
            materialTypeId: item.typeId,
            materialId: item.id,
            specification: item.specification,
            quality: item.quality,
            gb: item.gb,
            unit: item.unit,
            cost: item.cost,
            num: 1,
            amount: 0,
            pv: 0,
          };
          this.form.items.push(row);
        });
      }
    },
    getForm(processId) {
      this.getStartForm(processId).then((res) => {
        const _this = this;
        let {process} = res;
        _this.form.processId = process.id;
        const option = _this.option;
        const {column, group} = option;

        column.forEach((col) => {
          if (col.value) col.value = _this.getDefaultValues(col.value);
        });
        if (group && group.length > 0) {
          // 处理group
          group.forEach((gro) => {
            gro.column.forEach((col) => {
              if (col.value) col.value = _this.getDefaultValues(col.value);
            });
          });
        }
        if (this.permission.wf_process_draft) {
            // 查询草稿箱
              this.initDraft(process.id,process.key).then((data) => {
              this.draftCount = data.length;
              this.draftList = data;
              if (data && Array.isArray(data) && data.length > 0) {
                this.$confirm("是否引用之前保存的草稿？", "提示", {})
                .then(() => {
                  this.isDraftPopupVisible = true; // 打开草稿弹窗
                })
              }
            });
        }
        _this.waiting = false;
      });
    },
    getFormByProcessDefKey(processDefKey) {
      this.getStartFormByProcessDefKey(processDefKey).then((res) => {
        const _this = this;
        let {process} = res;
        _this.form.processId = process.id;
        const option = _this.option;
        const {column, group} = option;
        column.forEach((col) => {
          if (col.value) {
            col.value = _this.getDefaultValues(col.value);
          }
        });
        if (group && group.length > 0) {
          // 处理group
          group.forEach((gro) => {
            gro.column.forEach((col) => {
              if (col.value) col.value = _this.getDefaultValues(col.value);
            });
          });
        }
        if (this.permission.wf_process_draft) {
            // 查询草稿箱
              this.initDraft(process.id,process.key).then((data) => {
              this.draftCount = data.length;
              this.draftList = data;
              if (data && Array.isArray(data) && data.length > 0) {
                this.$confirm("是否引用之前保存的草稿？", "提示", {})
                .then(() => {
                  this.isDraftPopupVisible = true; // 打开草稿弹窗
                })
              }
            });
        }
        _this.waiting = false;
      });
    },
    projectConfirm(selectionList) {
      if (selectionList) {
        this.$nextTick(() => this.$refs.form.clearValidate());
        this.form.projectSerialNo = selectionList[0].serialNo;
        this.form.projectTitle = selectionList[0].title;
        this.form.brand = selectionList[0].brand;
      }
    },
    handleMaterialSubmit(selectionList, row1) {
      row1.materialCode = selectionList[0].code;
      row1.materialName = selectionList[0].name;
      row1.materialId = selectionList[0].id;
      row1.specification = selectionList[0].specification;
      row1.quality = selectionList[0].quality;
      row1.gb = selectionList[0].gb;
      row1.unit = selectionList[0].unit;
    },
    validateItems(items) {
      return !items || !items.some((item) => !item.num || !item.amount);
    },
    handleSubmit:debounce(function (){
      this.loading = true;
      const items = this.validateItems(this.form.items);
      if (items) {
        this.form.draftId = this.draftId;
        this.handleStartProcess(true)
          .then((done) => {
            this.$message.success("发起成功");
            if(this.draftId != null){
              this.draftCount = this.draftCount-1;
              this.draftList = this.draftList.filter(item => item.id !== this.draftId);
            }
            if (this.fromPath) {
              this.handleCloseTag(this.fromPath);
            } else this.handleCloseTag("/ni/por/budget");
            done();
          })
          .catch(() => {
            this.loading = false;
          });
      } else {
        this.$message.warning("明细中存在未填写的数量/金额");
        this.loading = false;
      }
    },1000),
    // 人员选择弹窗
    handleUserSelect({type, checkType}) {
      if (!this.comment && ["transfer", "delegate"].includes(type)) {
        this.$message.error("请填写批复意见");
        return;
      }
      if (type == "assignee")
        this.defaultChecked = this.$refs.examineForm.examineForm.assignee;
      else if (type == "copy")
        this.defaultChecked = this.$refs.examineForm.examineForm.copyUser;

      this.$refs["user-select"].visible = true;
      this.userSelectType = type;
      this.checkType = checkType;
    },
    // 选人回调
    handleUserSelectConfirm(id, name) {
      const {comment, copyUser} = this.$refs.examineForm.examineForm;
      const {
        taskId,
        processInstanceId,
        processDefinitionName,
        processDefinitionId,
      } = this.process;

      const type = this.userSelectType;
      const param = {
        taskId,
        processInstanceId,
        processDefinitionName,
        processDefinitionId,
        assignee: id,
        comment,
        copyUser,
      };
      if (type == "transfer") {
        transferTask(param).then(() => {
          // 转办
          this.$message.success("转办成功");
          this.handleCloseTag("/plugin/workflow/process/todo");
        });
      } else if (type == "delegate") {
        // 委托
        delegateTask(param).then(() => {
          this.$message.success("委托成功");
          this.handleCloseTag("/plugin/workflow/process/todo");
        });
      } else if (type == "addInstance") {
        // 加签
        addMultiInstance(param).then(() => {
          this.$message.success("加签成功");
        });
      } else if (type == "copy") {
        // 抄送
        this.$refs.examineForm.examineForm.copyUser = id;
        this.$refs.examineForm.examineForm.$copyUser = name;
      } else if (type == "assignee") {
        // 指定下一步审批人
        this.$refs.examineForm.examineForm.assignee = id;
        this.$refs.examineForm.examineForm.$assignee = name;
      } else {
        // 外部使用
        this.form[type] = id;
        this.form["$" + type] = name;
      }
      this.$refs["user-select"].visible = false;
    },
    //选择草稿
    handleDraftSelect(selectedDraft) {
      //草稿版本与流程版本不一致
      if(!selectedDraft.sameVersion){
        this.$confirm("选中的草稿与当前流程版本不一致，是否继续引用？", "提示", {})
        .then(() => {
          this.draftId = selectedDraft.id;
          this.form = JSON.parse(selectedDraft.variables);
          this.isDraftPopupVisible = false;
        });
      } else {
        this.draftId = selectedDraft.id;
        this.form = JSON.parse(selectedDraft.variables);
      }
    },
    //删除草稿
    handleDraftDelete(draftId) {
      this.$confirm("是否删除选中的草稿箱数据？", "提示", {})
        .then(() => {
          this.$axios.post(`/api/blade-workflow/process/draft/delete/${draftId}`)
          .then(response => {
            this.$message.success('草稿删除成功');
            this.draftCount = this.draftCount-1;
            this.draftList = this.draftList.filter(item => item.id !== draftId);
          })
          .catch(error => {
            this.$message.error('草稿删除失败，请重试');
          });
      })
    },
    handleDraftBox() {
      if (this.draftList.length > 0) {
        this.isDraftPopupVisible = true;
      } else {
        // 重新获取草稿数据
        this.initDraft(this.form.processId).then((data) => {
          this.draftCount = data.length;
          this.draftList = data;
          if (data && Array.isArray(data) && data.length > 0) {
            this.isDraftPopupVisible = true;
          }
        });
      }
    },

  },
};
</script>

<style lang="scss" scoped>
/deep/ .el-link--inner {
  font-size: 12px;
}

/deep/ .el-upload-list__item-name {
  font-size: 12px;
}

.foot-item {
  position: fixed;
  bottom: 0;
  margin-left: -20px;
  // right: 0;
  z-index: 101;
  height: 66px;
  background-color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  -webkit-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
</style>

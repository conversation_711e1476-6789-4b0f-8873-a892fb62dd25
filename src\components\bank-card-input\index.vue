<template>
  <el-input
    type="text"
    :size="size"
    :maxlength="maxLength"
    v-model="account"
    :disabled="disabled"
    clearable
    :placeholder="placeholder"
    @keyup.native="validateNum(account)"
  ></el-input>
</template>

<script>
export default {
  name: "bank-card-input",
  props: {
    value: Number,
    placeholder: {
      type: String,
      default: "请输入银行账号",
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    size: {
      type: String,
      default: "mini",
    },
    maxLength: {
      type: Number,
    },
  },
  watch: {
    value: {
      handler(val) {
        if (val) this.validateNum(val);
      },
      immediate: true,
    },
  },
  data() {
    return {};
  },
  computed: {
    account: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit("input", val);
      },
    },
  },
  methods: {
    validateNum(data) {
      this.setNum(data);
      this.$emit("change", this.account);
    },
    setNum(data) {
      let res = data
        .replace(/\s/g, "")
        .replace(/[^\d-]/g, "")
        .replace(/(\d{4})(?=\d)/g, "$1 ");
      this.account = res;
    },
  },
};
</script>

<style scoped></style>

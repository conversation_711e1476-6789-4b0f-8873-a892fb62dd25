<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
      :cell-style="cellStyle"
    >
      <template #serialNo="{ row, index }">
        <flow-timeline-popover
          v-if="row.processInsId"
          :process-ins-id="row.processInsId"
          :form-key="formKey"
          :process-def-key="processDefKey"
          v-model="row.serialNo"
          :flow.sync="row.flow"
          trigger="click"
          lazy
        />
        <span v-else>{{ row.serialNo }}</span>
      </template>
      <template #type="{ row, size }">
        <el-tag :size="size" effect="dark">{{ row.$type }}</el-tag>
      </template>
      <template #amount="{ row, index, size }">
        <span>{{
          Number(row.amount).toLocaleString("zh-CN", {
            minimumFractionDigits: 2,
          })
        }}</span>
      </template>
      <template #projectIdForm="{ row, disabled, size, index }">
        <project-select
          v-model="form.projectId"
          :size="size"
          :params="{ status: 9 }"
        />
      </template>
      <template #budgetIdForm="{ row, disabled, size, index }">
        <un-finish-budget-select
          v-model="form.budgetId"
          :size="size"
          @confirm="handleBudgetConfirm($event, form)"
          @clear="handleBudgetClear"
        />
      </template>
      <template #materialCodeForm="{ row, disabled, size, index, type }">
        <span>{{ row.materialCode }}</span>
      </template>
      <template #numForm="{ row, disabled, size, index }">
        <el-input-number
          :size="size"
          v-model="row.num"
          :disabled="disabled"
          :min="0"
          controls-position="right"
          style="width: 100%"
          @change="handleNumChange($event, row)"
        />
      </template>
      <template #menuLeft>
        <el-button
          v-if="permission.costApply_apply"
          type="primary"
          size="mini"
          icon="el-icon-plus"
          plain
          @click="handleApply"
          >费用申请
        </el-button>
        <el-button
          type="warning"
          size="mini"
          icon="el-icon-circle-plus-outline"
          plain
          v-if="permission.costApply_role_set"
          @click="handleRole"
          >权限设置
        </el-button>
      </template>
      <template #menuRight="{ size }">
        <el-button
          icon="el-icon-time"
          circle
          :size="size"
          @click="handleLog"
        ></el-button>
      </template>
      <template #menu="{ row, index, size }">
        <el-button
          type="text"
          :size="size"
          icon="el-icon-position"
          style="color: #e6a23c"
          v-if="
            permission.payableApply_apply &&
            row.status === 9 &&
            row.paymentStatus === 0
          "
          @click="handlePayableApply(row)"
          >付款申请
        </el-button>
        <el-button
          icon="el-icon-download"
          type="text"
          :size="size"
          @click="rowAttach(row)"
          >附件
        </el-button>
      </template>
    </avue-crud>
    <inquiry-dialog
      v-if="inquiryShow"
      :show.sync="inquiryShow"
      :title="inquiryTitle"
      :data="form"
      @submit="searchReset"
    />
    <el-drawer
      :visible.sync="detailVisible"
      :title="form.title"
      custom-class="wf-drawer"
      size="100%"
      append-to-body
    >
      <cost-apply-detail
        v-if="detailVisible"
        :taskId="form.taskId"
        :businessKey="form.id"
        :processInstanceId="form.processInsId"
      />
    </el-drawer>
    <attach-dialog ref="attachDialogRef" />
    <log-opt-dialog ref="logOptDialogRef" :module="module" />
    <budget-item-dialog
      ref="porBudgetItemRef"
      multiple
      :params="{ used: false, cost: true }"
      @confirm="handleCostItemSelect"
    />
    <el-dialog
      title="权限设置"
      append-to-body
      :visible.sync="role.visible"
      width="345px"
    >
      <avue-form
        :option="role.option"
        v-model="role.form"
        @submit="handleRoleSubmit"
      ></avue-form>
    </el-dialog>
  </basic-container>
</template>

<script>
import {
  add,
  getDetail,
  getPage,
  getTypeByRoleId,
  remove,
  roleRelation,
  update,
} from "@/api/ni/fin/cost-apply";
import { mapGetters } from "vuex";
import ApplyItemForm from "@/views/ni/por/components/ApplyItemForm";
import InquiryDialog from "@/views/ni/por/components/InquiryDialog";
import exForm from "@/views/plugin/workflow/mixins/ex-form";
import TaskDetail from "@/views/plugin/workflow/ops/detail";
import AttachDialog from "@/components/attach-dialog";
import BudgetSelect from "@/views/ni/por/components/BudgetSelect";
import ProjectSelect from "@/views/ni/project/components/MyProjectSelect";
import ApplyDetail from "@/views/ni/por/apply-detail";
import FlowSetDialog from "@/components/flow-set-dialog";
import LogOptDialog from "@/components/log-opt-dialog";
import OrderArrivalListDialog from "@/views/ni/por/components/OrderArrivalListDialog";
import FinAccountingSelect from "@/views/ni/fin/components/AccountingSelect";
import UnFinishBudgetSelect from "@/views/ni/por/components/UnFinishBudgetSelect";
import BudgetItemDialog from "@/views/ni/por/components/BudgetItemDialog";
import BudgetCostItemDialog from "@/views/ni/por/components/BudgetCostItemDialog";
import CostItemDialog from "@/views/ni/por/components/CostItemDialog";
import CostApplyDetail from "@/views/ni/por/cost-apply-detail";
import SupplierSelect from "@/views/ni/base/components/SupplierSelect1";
import MaterialSelect from "@/views/ni/base/components/MaterialSelect1";
import FlowTimelinePopover from "@/components/wf-flow-timeline/popover";
import { numToCapital } from "@/util/util";
import { dateFormat, dateNow1 } from "@/util/date";

export default {
  mixins: [exForm],
  components: {
    SupplierSelect,
    BudgetCostItemDialog,
    BudgetItemDialog,
    UnFinishBudgetSelect,
    ApplyItemForm,
    InquiryDialog,
    TaskDetail,
    AttachDialog,
    MaterialSelect,
    BudgetSelect,
    ProjectSelect,
    ApplyDetail,
    FlowSetDialog,
    LogOptDialog,
    OrderArrivalListDialog,
    FinAccountingSelect,
    CostItemDialog,
    CostApplyDetail,
    FlowTimelinePopover,
  },
  data() {
    return {
      itemDialogShow: false,
      activeName: "items",
      detailVisible: false,
      module: "ni_fin_cost_apply",
      processDefKey: "process_fin_cost_apply",
      formKey: "wf_ex_fin/CostApply",
      inquiryTitle: "",
      inquiryShow: false,
      inquiriesAmount: -1,
      form: {
        items: [],
        costItems: [],
      },
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      needInquiry: true,
      selectionList: [],
      option: {
        addBtn: false,
        editBtn: false,
        delBtn: false,
        span: 6,
        dialogFullscreen: true,
        searchLabelWidth: 110,
        labelWidth: 110,
        size: "mini",
        searchSize: "mini",
        align: "center",
        searchIndex: 3,
        searchIcon: true,
        height: "auto",
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: false,
        viewBtn: false,
        selection: true,
        dialogClickModal: false,
        column: [
          {
            label: "审批状态",
            prop: "status",
            dicUrl: "/api/blade-system/dict/dictionary?code=data_status",
            search: true,
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            dataType: "number",
            type: "select",
          },
          {
            label: "申请人",
            prop: "createUserName",
            addDisplay: false,
            editDisplay: false,
            overHidden: true,
          },
          {
            label: "申请部门",
            prop: "createDeptName",
            addDisplay: false,
            editDisplay: false,
            overHidden: true,
            hide: true,
          },
          {
            label: "申请时间",
            prop: "createTime",
            type: "datetime",
            minWidth: 127,
            overHidden: true,
            display: false,
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
          },
          {
            label: "流水号",
            prop: "serialNo",
            overHidden: true,
            minWidth: 135,
            search: true,
            disabled: true,
            placeholder: "系统自动生成",
            searchPlaceholder: " ",
          },
          {
            label: "主题",
            prop: "title",
            placeholder: " ",
            overHidden: true,
            minWidth: 135,
            search: true,
            rules: [
              {
                required: true,
                message: "请输入申请主题",
                trigger: "blur",
              },
            ],
          },
          {
            label: "供应商",
            prop: "supplierId",
            placeholder: " ",
            hide: true,
            showColumn: false,
            search: true,
            type: "select",
            remote: true,
            dicUrl: `/api/ni/base/supplier/info/page?status=2&blacklist=0&keyword={{key}}`,
            props: {
              label: "name",
              value: "id",
              desc: "code",
            },
            dicFormatter: (data) => {
              return data.data.records;
            },
          },
          {
            label: "供应商",
            prop: "supplierName",
            placeholder: " ",
            minWidth: 120,
            overHidden: true,
          },
          {
            label: "关联项目",
            prop: "projectSerialNo",
            overHidden: true,
            placeholder: " ",
            minWidth: 117,
            disabled: true,
          },
          {
            label: "关联预算",
            prop: "budgetSerialNo",
            display: false,
            minWidth: 117,
            overHidden: true,
          },
          {
            label: "关联预算",
            prop: "budgetId",
            overHidden: true,
            search: true,
            hide: true,
            showColumn: false,
            rules: [
              {
                required: true,
                message: "请选择关联预算",
                trigger: "blur",
              },
            ],
          },
          {
            label: "申请类型",
            prop: "type",
            placeholder: " ",
            type: "select",
            dicUrl: "/api/ni/por/type/listWithPermission",
            props: {
              label: "name",
              value: "code",
              desc: "code",
            },
            minWidth: 117,
            overHidden: true,
            search: true,
            rules: [
              {
                required: true,
                message: "请选择申请类型",
                trigger: "blur",
              },
            ],
          },
          {
            label: "总金额",
            prop: "amount",
            type: "number",
            minWidth: 90,
            placeholder: " ",
            disabled: true,
            overHidden: true,
            controls: false,
          },
          {
            label: "账套",
            prop: "brand",
            type: "radio",
            minWidth: 90,
            value: "1",
            dicUrl: "/api/blade-system/dict-biz/dictionary?code=ni_brand",
            disabled: true,
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            search: true,
          },
          {
            label: "付款状态",
            prop: "paymentStatus",
            type: "select",
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_fin_payment_status",
            display: false,
            search: true,
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            dataType: "number",
            width: 84,
            overHidden: true,
          },
        ],
        group: [
          {
            label: "费用明细",
            arrow: false,
            column: [
              {
                labelWidth: 0,
                label: "",
                prop: "items",
                span: 24,
                type: "dynamic",
                children: {
                  size: "mini",
                  showSummary: true,
                  sumColumnList: [
                    {
                      name: "num",
                      type: "sum",
                      decimals: 1,
                    },
                    {
                      name: "amount",
                      type: "sum",
                    },
                  ],
                  rowAdd: (done) => {
                    if (!this.form.budgetId) {
                      this.$message.warning("请选择预算");
                      return;
                    }
                    if (!this.form.budgetId) {
                      done();
                    } else {
                      this.$refs.porBudgetItemRef.init(this.form.budgetId);
                    }
                  },
                  align: "center",
                  headerAlign: "center",
                  column: [
                    {
                      label: "费用名称",
                      prop: "materialName",
                      type: "textarea",
                      minRows: 1,
                      placeholder: " ",
                    },
                    {
                      label: "用途",
                      prop: "purpose",
                      type: "textarea",
                      minRows: 1,
                      placeholder: " ",
                      overHidden: true,
                      cell: true,
                    },
                    {
                      label: "编码",
                      prop: "materialId",
                      placeholder: " ",
                    },
                    {
                      label: "规格型号",
                      placeholder: " ",
                      prop: "specification",
                      disabled: true,
                    },
                    {
                      label: "数量",
                      prop: "num",
                      type: "number",
                      placeholder: " ",
                      minWidth: 100,
                      rules: [
                        {
                          required: true,
                          message: "请输入数量",
                          trigger: "blur",
                        },
                      ],
                    },
                    {
                      label: "单价",
                      prop: "price",
                      type: "number",
                      controls: false,
                      precision: 2,
                      placeholder: " ",
                      cell: true,
                      change: ({ row, value }) => {
                        const that = this;
                        if (row.num) {
                          row.amount = Number(value) * Number(row.num);
                        } else {
                          row.amount = 0;
                        }
                        let amount = 0;
                        that.form.items.forEach((item) => {
                          amount += Number(item.amount);
                        });
                        that.form.amount = amount;
                      },
                    },
                    {
                      label: "金额",
                      prop: "amount",
                      overHidden: true,
                      controls: false,
                      type: "number",
                      minWidth: 100,
                      precision: 2,
                      placeholder: " ",
                      rules: [
                        {
                          required: true,
                          message: "请输入金额",
                          trigger: "blur",
                        },
                      ],
                      change: ({ row, value }) => {
                        const that = this;
                        if (row.num) {
                          row.price = value / row.num;
                        } else {
                          row.price = 0;
                        }
                        let amount = 0;
                        that.form.items.forEach((item) => {
                          amount += Number(item.amount);
                        });
                        that.form.amount = amount;
                      },
                    },
                    {
                      label: "备注",
                      prop: "remark",
                      type: "textarea",
                      minRows: 1,
                      placeholder: " ",
                    },
                  ],
                },
              },
            ],
          },
        ],
      },
      data: [],
      typeDict: [],
      typeDictKeyValue: {},
      role: {
        visible: false,
        option: {
          span: 24,
          size: "mini",
          searchSize: "mini",
          emptyBtn: false,
          column: [
            {
              prop: "roleId",
              label: "角色选择",
              type: "tree",
              dicUrl: "/api/blade-system/role/tree",
              props: {
                label: "title",
                value: "key",
              },
              placeholder: " ",
              rules: [
                {
                  required: true,
                  message: "请选择角色",
                  trigger: "blur",
                },
              ],
              change: ({ value }) => {
                if (value) {
                  console.log(value);
                  getTypeByRoleId(value).then((res) => {
                    this.$set(this.role.form, "type", res.data.data);
                  });
                }
              },
            },
            {
              prop: "type",
              label: "类型选择",
              type: "select",
              dicUrl: "/api/ni/por/type/listWithPermission",
              props: {
                label: "name",
                value: "code",
                desc: "code",
              },
              placeholder: " ",
              multiple: true,
              rules: [
                {
                  required: true,
                  message: "请选择类型",
                  trigger: "blur",
                },
              ],
            },
          ],
        },
        form: {},
      },
    };
  },
  computed: {
    ...mapGetters(["permission", "userInfo"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.costApply_add, false),
        viewBtn: this.vaildData(this.permission.costApply_view, false),
        delBtn: this.vaildData(this.permission.costApply_delete, false),
        editBtn: this.vaildData(this.permission.costApply_edit, false),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
    itemData() {
      return this.form.items || [];
    },
  },
  created() {
    this.$http.get("/api/ni/por/type/list?status=0").then((res) => {
      const column = this.findObject(this.option.column, "type");
      column.dicData = res.data.data;
      this.typeDict = res.data.data;
      this.typeDictKeyValue = this.typeDict.reduce((acc, cur) => {
        acc[cur.code] = cur.name;
        return acc;
      }, {});
    });
  },
  methods: {
    handleRoleSubmit(form, done) {
      const types = form.type.join(",");
      roleRelation({ roleId: form.roleId, types })
        .then(() => {
          this.role.visible = false;
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        })
        .finally(() => {
          done();
        });
    },
    rowDetail(row, index) {
      if (row.processInsId) {
        this.dynamicRoute(
          {
            processInstanceId: row.processInsId,
            formKey: this.formKey,
            processDefKey: this.processDefKey,
          },
          "detail"
        );
      } else {
        this.$refs.crud.rowView(row, index);
      }
    },
    handleApply() {
      const form = {};
      this.dynamicRoute(
        {
          processDefKey: this.processDefKey,
          formKey: this.formKey,
          form: encodeURIComponent(
            Buffer.from(JSON.stringify(form)).toString("utf8")
          ),
        },
        "start"
      );
    },
    handleSupplierSubmit(selectList) {
      this.form.supplier = selectList[0].name;
    },
    handleMaterialSubmit(selectList, row1) {
      if (selectList.length > 0) {
        const row = selectList[0];
        this.$nextTick(() => {
          this.$set(row1, "materialName", row.name);
          this.$set(row1, "materialCode", row.code);
          this.$set(row1, "specification", row.specification);
          this.$set(row1, "quality", row.quality);
          this.$set(row1, "gb", row.gb);
          this.$set(row1, "unit", row.unit);
          this.$set(row1, "cost", row.cost);
        });
      }
    },
    handleCostItemSelect(selectionList) {
      this.form.items = selectionList.map((item) => {
        return {
          cost: item.cost,
          materialName: item.materialName,
          budgetItemId: item.id,
          purpose: item.purpose,
          materialId: item.materialId,
          materialCode: item.materialCode,
          specification: item.specification,
          quality: item.quality,
          num: item.num,
          price: item.price,
          usedNum: item.applyNum ? item.applyNum : 0,
          budgetNum: item.num,
          amount: item.amount,
          remark: item.remark,
        };
      });
    },
    handleNumChange(num, row) {
      const that = this;
      if (row.price) {
        row.amount = Number(row.price) * Number(row.num);
      }
      let amount = 0;
      that.form.items.forEach((item) => {
        amount += Number(item.amount ? item.amount : 0);
      });
      that.form.amount = amount;
    },
    handleAccountingSubmit(selectList, row) {
      if (selectList.length > 0) {
        row.accountingCode = selectList[0].code;
        row.currency = selectList[0].currency;
      }
    },
    handleLog() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择要查看的数据");
        return;
      } else if (this.selectionList.length > 1) {
        this.$message.warning("只能选择一条数据");
        return;
      }
      this.$refs.logOptDialogRef.init(this.selectionList[0].id);
    },
    handleBudgetClear() {
      this.form.budgetId = null;
      this.form.items = [];
      this.form.costItems = [];
      const amountColumn = this.findObject(
        this.option.group[0].column[0].children.column,
        "amount"
      );
      const costItemAmount = this.findObject(
        this.option.group[1].column[0].children.column,
        "amount"
      );
      if (this.form.crash) {
        amountColumn.disabled = false;
        costItemAmount.disabled = false;
      } else {
        amountColumn.disabled = true;
        costItemAmount.disabled = true;
      }
    },
    handleBudgetConfirm(selectList, row) {
      if (selectList && selectList.length > 0) {
        row.projectId = selectList[0].projectId;
        row.projectTitle = selectList[0].projectTitle;
        row.projectSerialNo = selectList[0].projectSerialNo;
        row.brand = selectList[0].brand;
        row.type = selectList[0].type;
      }
    },
    handleSubmit(type) {
      //TODO 提交
      this.form.status = 1;
      if (type === "add") {
        this.$refs.crud.rowSave();
      } else if (type === "edit") {
        this.$refs.crud.rowUpdate();
      }
    },
    rowFlow(row, index) {
      if (row.processInsId) {
        this.dynamicRoute(
          {
            processInstanceId: row.processInsId,
          },
          "detail",
          true
        ).then(() => {
          this.form = { ...row };
          this.detailVisible = true;
        });
      } else {
        this.$refs.crud.rowView(row, index);
      }
    },
    handlePayableApply(row) {
      getDetail(row.id).then((res) => {
        const data = res.data.data;
        const form = {
          title: data.title,
          useTo: data.title,
          brand: data.brand,
          supplierId: data.supplierId,
          type: "3",
          costApplyId: data.id,
          budgetId: data.budgetId,
          unPayAmount: data.amount,
          amount: data.amount,
          upperAmount: numToCapital(data.amount),
          currency: data.currency,
          applyPayableDate: dateFormat(new Date(), "yyyy-MM-dd"),
          items: data.items.map((item) => {
            return {
              ...item,
              id: null,
              applyId: null,
              cost: true,
              type: "9",
            };
          }),
        };
        this.dynamicRoute(
          {
            processDefKey: "process_fin_payable_apply",
            formKey: "wf_ex_fin/PayableApply",
            form: encodeURIComponent(
              Buffer.from(JSON.stringify(form)).toString("utf8")
            ),
          },
          "start"
        );
      });
    },
    rowAttach(row) {
      this.$refs.attachDialogRef.init(row.id, this.module);
    },
    handleDetail(row, index) {
      if (row.processInsId) {
        this.dynamicRoute(
          {
            processInstanceId: row.processInsId,
          },
          "detail",
          true
        ).then(() => {
          this.form = { ...row };
          this.detailVisible = true;
        });
      } else {
        this.$refs.crud.rowView(row, index);
      }
    },
    rowSave(row, done, loading) {
      //TODO 如果row的status==1 则走提交申请
      //如果采购金额超过2000，则需要比价
      add(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          window.console.log(error);
        }
      );
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          window.console.log(error);
        }
      );
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    handleRole() {
      this.role.visible = true;
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen(done, type) {
      if ("add" === type) {
        this.form.crash = false;
        this.form.inquiry = true;
        this.form.status = 0;
        this.form.type = "6";
      }
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then((res) => {
          this.form = res.data.data;
        });
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      const query = Object.assign(params, this.query);
      getPage(page.currentPage, page.pageSize, query).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
    cellStyle({ row, column }) {
      if ("brand" === column.columnKey) {
        return this.brandStyle(row.brand);
      }
      if ("status" === column.columnKey) {
        return this.statusStyle(row.status);
      }
      if ("paymentStatus" === column.columnKey) {
        return this.paymentStatusStyle(row.paymentStatus);
      }
    },
    brandStyle(brand) {
      if (brand === "1") {
        return {
          background: "#167C46",
          color: "#fff",
        };
      } else if (brand === "2") {
        return {
          background: "#06c15c",
          color: "#fff",
        };
      } else if (brand === "4") {
        return {
          background: "#056487",
          color: "#fff",
        };
      } else if (brand === "5") {
        return {
          background: "#ae800c",
          color: "#fff",
        };
      }
      return {};
    },
    statusStyle(status) {
      if (status === 0) {
        return {
          backgroundColor: "#909399",
          color: "#fff",
        };
      } else if (status === 1) {
        return {
          backgroundColor: "#409EFF",
        };
      } else if (status === 2) {
        return {
          backgroundColor: " #E6A23C",
          color: "#fff",
        };
      } else if (status === 3) {
        return {
          backgroundColor: " #F56C6C",
        };
      } else if (status === 6) {
        return {
          backgroundColor: " #F56C6C",
          color: "#fff",
        };
      } else if ([4, 5].includes(status)) {
        return {
          backgroundColor: " #E6A23C",
        };
      } else if (status === 9) {
        return {
          backgroundColor: " #67C23A",
          color: "#fff",
        };
      } else {
        return {
          backgroundColor: " #E6A23C",
        };
      }
    },
    paymentStatusStyle(paymentStatus) {
      if (paymentStatus === 0) {
        return {
          backgroundColor: "#FAAD14",
          color: "#fff",
        };
      } else if (paymentStatus === 1) {
        return {
          backgroundColor: "#1890FF",
          color: "#fff",
        };
      } else if (paymentStatus === 2) {
        return {
          backgroundColor: "#52C41A",
          color: "#fff",
        };
      } else if (paymentStatus === 3) {
        return {
          backgroundColor: "#2F5AAC",
          color: "#fff",
        };
      }
      return {};
    },
  },
};
</script>

<style scoped></style>

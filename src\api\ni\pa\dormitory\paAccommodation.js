import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/ni/pa/paAccommodation/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/ni/pa/paAccommodation/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/ni/pa/paAccommodation/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/ni/pa/paAccommodation/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/ni/pa/paAccommodation/submit',
    method: 'post',
    data: row
  })
}

export const getDetailByProcessInsId = (processInsId) => {
  return request({
    url: '/api/ni/pa/paAccommodation/detail',
    method: 'get',
    params: {
      processInsId
    }
  })
}

export const start = (row) => {
  return request({
    url: '/api/ni/pa/paAccommodation/start',
    method: 'post',
    data: row
  })
}

export const saveAndStart = (processDefKey, row) => {
  return request({
    url: '/api/ni/pa/paAccommodation/saveAndStart',
    method: 'post',
    params: {
      processDefKey
    },
    data: row
  })
}


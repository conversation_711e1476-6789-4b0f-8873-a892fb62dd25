<template>
    <el-dialog
      title="发票扫描"
    ref="invoice-scan-dialog"
    :visible.sync = "visible"
    append-to-body
      @opened="opened"
    width="80%"
    style="top: -10vh;"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
    <el-container id="container" style="height: 700px;">
      <el-aside width="195px" class="aside">
        <div class="header">
          <i class="el-icon-document"></i>
          &nbsp;图像预览
        </div>
        <div class="scroll" style="overflow: auto;height: calc(100% - 65px);">
          <ul style="margin:auto;padding:0;margin-top:5px">
            <draggable v-model="urls" :move="onMove" chosenClass="chosen" forceFallback="true" animation="1000" @end="onEndDrag">
              <li v-for="(image,index) in urls" :key="image" style="padding:10px;padding-right:25px">
                <div style="display:inline;position: relative;">
                  <span style="font-size:12px;position: absolute;left:0;width:25px;text-align:right">{{index+1}}.</span>
                  <el-image :class="{'imageListSelect': selectManyImages.indexOf(index)>=0}" ref="imageList" :src="image.src" fit="contain" alt="image.imageName" :initial-index="index"
                            lazy style="height:80px;margin-left:35px;width:80%;border:2px solid #dedede" @click="!$event.ctrlKey && selectImage(image,index)" @click.ctrl="selectManyImage(image,index)">
                  </el-image>
                </div>
              </li>
            </draggable>
          </ul>
        </div>
        <div style="background-color: #eaeaea;width:194px;height:25px;
					line-height: 25px;padding-left:20px;color:#666;box-sizing:border-box">
          总张数&nbsp;{{urls.length}}
        </div>
      </el-aside>
      <el-container>
        <el-header height="70px">
          <div class="menuContainer">
            <ul>
              <li v-if="!btnLinkScan" @click="pdfLoad()">
                <div class="iconContainer">
                  <img src="../../../../../public/img/invoice-scan/pdf.png" />
                </div>
                <div class="menu">发票导入</div>
              </li>

              <li v-if="btnLinkScan" @click="scan()" >
                <div class="iconContainer" disabled="false">
                  <img src="../../../../../public/img/invoice-scan/scan.png" />
                </div>
                <div class="menu">发票扫描</div>
              </li>
              <!--              <li v-if="!btnLinkScan" @click="!btnLinkScan?null: scan()">-->
              <!--                <div class="iconContainer">-->
              <!--                  <img src="../../../../../public/img/invoice-scan/icons/scan.png" class="imgGray"/>-->
              <!--                </div>-->
              <!--                <div class="menu">正常扫描</div>-->
              <!--              </li>-->
              <li @click="ocr()">
                <div class="iconContainer">
                  <img src="../../../../../public/img/invoice-scan/recognize.png" />
                </div>
                <div class="menu">发票识别</div>
              </li>
              <li style="border-right:1px solid #dfdfdf;height:26px;margin-top:10px">
              </li>
              <li  v-if="!btnLinkScan" @click="init()">
                <div class="iconContainer">
                  <img src="../../../../../public/img/invoice-scan/pageCover.png" />
                </div>
                <div class="menu">连接扫描</div>
              </li>
              <li  v-if="btnLinkScan" @click="getLastBatch()" >
                <div class="iconContainer">
                  <img src="../../../../../public/img/invoice-scan/batch.png" />
                </div>
                <div class="menu">最后批次</div>
              </li>
              <li v-if="btnLinkScan" @click="setting()">
                <div class="iconContainer">
                  <img src="../../../../../public/img/invoice-scan/setup.png" />
                </div>
                <div class="menu">扫描设置</div>
              </li>

              <li style="border-right:1px solid #dfdfdf;height:26px;margin-top:10px">
              </li>

              <li @click="deleteImage()">
                <div class="iconContainer">
                  <img src="../../../../../public/img/invoice-scan/delImage.png" />
                </div>
                <div class="menu">删除选定</div>
              </li>
              <li @click="deleteAllImage()">
                <div class="iconContainer">
                  <img src="../../../../../public/img/invoice-scan/delAll.png" />
                </div>
                <div class="menu">清空列表</div>
              </li>
              </ul>
          </div>
          </el-header>
        <el-main id="main">
          <div style="height:100%;float:left;position: relative;" class="maincanvas_width" id="optCanvas">
            <!--蒙板-->
            <div class="mask" v-if="isDisableOpt"></div>
            <div class="opt">
              <ul style="margin-left:-40px;line-height:24px">
                <li @click="scaleMax($event)">
                  <img src="../../../../../public/img/invoice-scan/amplify.png" title="放大"/>
                </li>
                <li @click="scaleMin($event)">
                  <img src="../../../../../public/img/invoice-scan/narrow.png" title="缩小"/>
                </li>
                <li @click="ajustDimensions($event)">
                  <img src="../../../../../public/img/invoice-scan/auto.png" title="自适应"/>
                </li>
                <li @click="realDimensions($event)">
                  <img src="../../../../../public/img/invoice-scan/real.png" title="实际大小"/>
                </li>
                <li @click="addListenerToDiv($event)">
                  <img src="../../../../../public/img/invoice-scan/drag.png" title="拖拽"/>
                </li>
                <li @click="rotate(180,$event)">
                  <img src="../../../../../public/img/invoice-scan/roate.png" title="旋转180度"/>
                </li>
                <li @click="innerErase($event)">
                  <img src="../../../../../public/img/invoice-scan/erase-inner.png" title="内擦除"/>
                </li>

                <li @click="undo($event)">
                  <img src="../../../../../public/img/invoice-scan/undo.png" title="撤销" v-if="undoStack.length>0"/>
                  <img src="../../../../../public/img/invoice-scan/undogray.png" title="撤销" v-if="undoStack.length<=0"/>
                </li>
                <li @click="redo($event)">
                  <img src="../../../../../public/img/invoice-scan/redo.png" title="前进" v-if="redoStack.length>0"/>
                  <img src="../../../../../public/img/invoice-scan/redogray.png" title="前进" v-if="redoStack.length<=0"/>
                </li>

                <li @click="save()">
                  <img src="../../../../../public/img/invoice-scan/save.png" title="保存"/>
                </li>
              </ul>
            </div>

            <div class="image-container scroll" id="canvas-container" ref="canvasContainer" @contextmenu.prevent
                 style="margin-left:58px;margin:0 auto;height:90%;">
              <canvas id="imageCanvas" style=" width: 100%; height: 100%;"></canvas>
<!--              <div class="pageBtn" v-if="image!=null">-->
<!--                <el-button class="info_button" type="info" icon="el-icon-arrow-left" circle style="float:left;margin-left:10px" @click="pre()"></el-button>-->
<!--              </div>-->
<!--              &lt;!&ndash;定制右侧按钮，下一张按钮向左移动&ndash;&gt;-->
<!--              <div class="pageBtn" style="right:20px" id="next_image" v-if="permissions.indexOf('oprate-right')<0 && image!=null">-->
<!--                <el-button class="info_button" type="info" icon="el-icon-arrow-right" circle style="float:right;" @click="next()"></el-button>-->
<!--              </div>-->
<!--              <div class="pageBtn" style="right:200px" id="next_image" v-if="permissions.indexOf('oprate-right')>=0 && image!=null">-->
<!--                <el-button class="info_button" type="info" icon="el-icon-arrow-right" circle style="float:right;" @click="next()"></el-button>-->
<!--              </div>-->
            </div>

            <div class="image-container" style="border: 1px solid #e9e9eb">
              <el-row>
                <el-col :span="6"><div><b>文件名：</b>{{this.arrFpxx.length===0?"":this.arrFpxx[this.index].imageName}}</div></el-col>
                <el-col :span="6"><div><b>发票代码：</b>{{this.arrFpxx.length===0?"":this.arrFpxx[this.index].fpdm}}</div></el-col>
                <el-col :span="6"><div><b>发票号码：</b>{{this.arrFpxx.length===0?"":this.arrFpxx[this.index].fphm}}</div></el-col>
                <el-col :span="6"><div><b>开票日期：</b>{{this.arrFpxx.length===0?"":this.arrFpxx[this.index].kprq}}</div></el-col>
                <el-col :span="4">
                  <div v-if="this.arrFpxx.length>0 && this.arrFpxx[this.index].jym!=''">校验码：{{this.arrFpxx[this.index].jym}}</div>
                  <div v-if="this.arrFpxx.length>0 && this.arrFpxx[this.index].je!=''">金额：{{this.arrFpxx[this.index].je}}</div>
                </el-col>
                <input type="file" multiple ref="hiddenfile" accept="application/pdf" @change="handlePdf1" class="hiddenInput" style="display: none;" />

              </el-row>

            </div>

          </div>

        </el-main>

      </el-container>

      <div class="mask-defined" v-if="isProcess">
        <el-progress class="mask-defined-process" :stroke-width="20" :percentage="process" :text-inside="true"></el-progress>
      </div>

    </el-container>


      <template>
        <el-dialog
          title="发票识别、验证"
          ref="invoice-ocr-dialog"
          :visible.sync = "ocrVisible"
          append-to-body
          width="80%"
          :close-on-click-modal="false"
          :close-on-press-escape="false"
        >
          <el-container  >

            <el-table
              ref="multipleTable"
              :header-cell-style="{ background: '#F5F7FA', color: '#000' }"
              :data="this.arrFpxx"
              border
              :highlight-current-row=true
              @selection-change="handleSelectionChange"
              stripe="true"
              height="500px">
              <el-table-column
                type="selection"
                width="55"
              >
              </el-table-column>
              <el-table-column property="zt" label="状态" width="200"></el-table-column>
              <el-table-column property="imageName" label="文件名" width="200" show-overflow-tooltip="true"></el-table-column>
              <el-table-column property="fpdm" label="发票代码" width="120">
                <template slot-scope="scope">
                  <div v-if="!scope.row.isEdit">{{ scope.row.fpdm }}</div>
                  <div v-else>
                    <el-input v-model="scope.row.fpdm"></el-input>
                  </div>
                </template>
              </el-table-column>
              <el-table-column property="fphm" label="发票号码" width="200"  show-overflow-tooltip="true">
                <template slot-scope="scope">
                  <div v-if="!scope.row.isEdit">{{ scope.row.fphm }}</div>
                  <div v-else>
                    <el-input v-model="scope.row.fphm"></el-input>
                  </div>
                </template>
              </el-table-column>
              <el-table-column property="kprq" label="开票日期" width="100">
                <template slot-scope="scope">
                  <div v-if="!scope.row.isEdit">{{ scope.row.kprq }}</div>
                  <div v-else>
                    <el-input v-model="scope.row.kprq"></el-input>
                  </div>
                </template>
              </el-table-column>
              <el-table-column property="xym" label="校验码" width="100">
                <template slot-scope="scope">
                  <div v-if="!scope.row.isEdit">{{ scope.row.xym }}</div>
                  <div v-else>
                    <el-input v-model="scope.row.xym"></el-input>
                  </div>
                </template>
              </el-table-column>
              <el-table-column property="bhsje" label="金额" width="110">
                <template slot-scope="scope">
                  <div v-if="!scope.row.isEdit">{{ scope.row.bhsje }}</div>
                  <div v-else>
                    <el-input v-model="scope.row.bhsje"></el-input>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="">
                <template slot-scope="scope">
                  <el-button
                    type="text"
                    @click="handleClick(scope.row)">{{ scope.row.isEdit ? '保存' : '编辑' }}</el-button>
                  <el-button
                    type="text"
                    @click="handleQkClick(scope.row)">清空</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-container>
          <el-container>
            <el-main>
                  <el-row type="flex" >
                    <el-col :span="1"></el-col><el-col :span="23" style="font-size: 16px;">{{ this.tipMessage }}</el-col>
                  </el-row>
                  <el-row type="flex" >
                    <el-col :span="24" :offset="1">
                      <el-button  type="primary" plain style="font-size: 18px;" :loading="this.btnLoading" @click="readd()">开始识别</el-button>
                      <el-button  type="primary" plain style="font-size: 18px;" @click="zhongZhi">中止</el-button>
                    </el-col>
                  </el-row>
            </el-main>
          </el-container>
        </el-dialog>
      </template>

  </el-dialog>
</template>

<style src="../../../../../public/cdn/invoice-scan/scan.css" scoped/>

<script>

import "../../../../../public/cdn/invoice-scan/protocolcheck.js"

import {extractData, scanForm, uploadForm} from "../../../../../public/cdn/invoice-scan/data"
import "../../../../../public/cdn/invoice-scan/WebScan";

import $ from 'jquery'
import {fabric} from "fabric";

import {
  addSub,
  formatInvoiceApi,
  invoiceOcr,
  invoiceQRCode,
  invoiceUrl, pdfToPng,
  upload, verifyInvoice
} from "@/api/ni/fin/invoice-ocr";

export default{
  data() {
    return {
      btnLinkScan: false,  //根据连接扫描状态，控制按钮禁用

      visible: false,
      ocrVisible:false,
      imgSrc:'',
      imgYzm:'',
      imgYzmys:'请输入验证码',
      tipMessage:'...',  //识别提示信息
      btnLoading:false,
      btnOKDisabled:true,

      arrFpxx: [],
      multipleSelection: [],

      index: 0,

      loopBreak: false, //开始循环中的等待退出控制变量
      keyOk: false,//确认验证码


      // form: {
        // data: data,
      scanSystem: scanForm.scanSystem,
      // },

      ///------data.js----start
      urls:[],
      canvas:null,
      image:{
        width:0,
        height:0,
      },
      WebScan:null,
      isSetup:false,
      isDoubleOfd:0,
      isSelectDoubleOfd:false,
      isSelectDoublePdf:false,
      isSelectSortMode:false,
      sortMode:0,
      isDoublePdf:0,
      isProcess:false,
      process:0,
      form:scanForm,
      treeOptions:[],
      treePlaceHolder:'请选择图像路径',
      isDisableOpt:false,
      treeProps: {
        multiple: false,
        lazy: true,
        emitPath:false,
        checkStrictly:true,
        value:'id',
        label:'label',
        leaf:'isEnd'
      },
      devices:[],
      pid:"",
      imageName:'',
      loading:null,
      loadingPageShow:false,
      rectificationDialog:false,
      repSliderValue:0,
      isWindows:false,
      assistLine:null,
      assistText:null,
      rules:{
        device:[
          { required: true, message: '请选择扫描仪', trigger: 'blur' }
        ],

      },

      activeName:'1',
      isInsertScan:false,
      selectImageObj:{
        index:-1
      },
      selectManyImages:[],
      oprationImageObj:{},
      redoStack:[],
      undoStack:[],
      startP:null,
      isUploadAllImage:false,
      uploadAllForm:uploadForm,
      uploadAllRules:{

      },

      permissions:[],
      isDownLoad:false,
      downLoadMode:0,
      isPageCover:false,
      isReal:false,
      logLevel:'DEBUG',
      split:'0',
      autoRotate:'0',
      autoRotateLinux:'None',
      splitLinux:'None',

      canvasJson:null,
      minCheek:0,
      maxCheek:20,
      extract:extractData,
      modelList:new Array(),
      isManagerModel:false,
      isaddModel:false,
      modelAddInputValue:'',
      isEditModel:false,
      modelEditInputValue:'',
      modelEditId:'',
      flipSideRotationLinux:'Book',
      flipSideRotationWindows :'0',
      uploadLicenceUrl:'',
      preLicenceDialog:false,
      ///------data.js ---end

      invoiceApiDict:[],
      invoiceApiDictKeyValue: {},
    }

  },
  created(){
    //Vue的方法给原生调用，则需要把方法挂在Window下面
    window.eleLoadding = this.eleLoadding;
    window.eleAlert=this.eleMessage;
    window.scanCallBack = this.scanCallBack;
    window.compressProcessCallback = this.compressProcessCallback;
    window.majorOFDProcessCallback = this.majorOFDProcessCallback;
    document.addEventListener('keydown', this.handleKeyDown)
    console.log("created");
  },

  components:{

  },

  computed: {

  },

  mounted() {
    console.log("mounted");
    this.dicInit()
    // var xx = {
    //   "imageName": 'fp20250619001.png',
    //   "imageSrc": 'xx',
    //   "fpdm": "",
    //   "fphm": "24332000000153867274",
    //   "kprq": "20240524",
    //   "jym": "",
    //   "je": "39.2",
    //   "zt": "",
    //   "bhsje": "39.2",
    //
    //   // "file": file,
    // }
    // var xx = {
    //   "imageName": 'fp20250619001.png',
    //   "imageSrc": 'xx',
    //   "fpdm": "",
    //   "fphm": "25322000000266142414",
    //   "kprq": "20250611",
    //   "jym": "",
    //   "je": "5.9",
    //   "zt": "",
    //   "bhsje": "5.9",
    //
    //   // "file": file,
    // }
    //
    // this.arrFpxx.push(xx);
  },
  methods:{
    handleClick(row) {
      // 动态设置数据并通过这个数据判断显示方式
      if (row.isEdit) {
        // 其实我们想要取消编辑状态的话不需要用下面这步
        // 直接使用 row.isEdit = false 就行
        // 因为 this.$set 后 isEdit 字段已经被 Vue 加上了响应式
        this.$delete(row, 'isEdit')
      } else {
        this.$set(row, 'isEdit', true);
      }
    },
    handleQkClick(row){
      this.$confirm("确定将选择数据清空吗?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          // return remove(row.id);
          row.fpdm = ""
          row.fphm = ""
          row.kprq = ""
          row.bhsje = ""
          row.xym = ""
          row.zt = ""
        })
    },
    /**
     * 选择pdf导入
     */
    pdfLoad() {
      let that = this;
      that.$refs.hiddenfile.click();// .$el.click();
    },

    /**
     * 导入pdf，将pdf 转换成图片 base54
     */
    async handlePdf1(evt) {
      // console.log('files:', evt.target.files)
      var loading=this.eleLoadding();
      for (const file of evt.target.files) {
        // console.log('file:', file)
      // let file = evt.target.files[0];
      // if (file === undefined) {
      //   return;
      // }

        let pdfName = file.name.substring(0, file.name.lastIndexOf("."));

        let formData = new FormData();
        formData.append('file', file)

        let ret = false
        ret = await pdfToPng(formData).then((res) => {
          let obj = new Object();
          obj.src = 'data:image/png;base64,' + res.data.data['image']
          obj.imageName = pdfName + '.png'
          this.urls.push(obj)
          var xx = {
            "imageName": obj.imageName,
            "imageSrc": obj.src,
            "fpdm": "",
            "fphm": "",
            "kprq": "",
            "jym": "",
            "je": "",
            "zt": "",
            "file": file,
          }

          this.arrFpxx.push(xx);
          // console.log('this.arrFpxx:', this.arrFpxx)
          return res.data.success
        });
        // console.log(ret)

        if (!ret) {
          this.$message({
            type: "error",
            message: "文件 " + pdfName + ' 导入失败！！！',
          });
        }

      }
      loading.close()
      // this.$message({
      //   type: "success",
      //   message: "文件导入完成！！！！！!",
      // });
    },

    /**
     * 识别的发票信息存入数据库
     */
    async rowSaveSub(row, rowItem) {
      let ret = await addSub(row, rowItem).then(
        (res) => {
          // this.$message({
          //   type: "success",
          //   message: "操作成功!",
          // });
          return res.data.data.id
        },
        (error) => {
          window.console.log("error:", error);
          return null
        }
      );
      return ret
    },

    /**
     * 复选框
     */
    toggleSelection(rows) {
      if (rows) {
        rows.forEach(row => {
          this.$refs.multipleTable.toggleRowSelection(row);
        });
      } else {
        this.$refs.multipleTable.clearSelection();
      }
    },

    handleSelectionChange(val) {
      this.multipleSelection = val;
      console.log('多选：', this.multipleSelection)
    },

    dicInit() {
      this.$http
        .get("/api/blade-system/dict-biz/dictionary?code=ni_fin_invoice_ocr_path")
        .then((res) => {
          // console.log(res.data.data)
          this.invoiceApiDict = res.data.data;
          this.invoiceApiDictKeyValue = this.invoiceApiDict.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});

          // console.log(this.invoiceApiDictKeyValue)
        });
    },
    /**
     *
     */
    async readd() {
      console.log("开始识别")
      if(this.multipleSelection.length === 0){
        this.eleMessage("请选择要识别的发票！！！","warning");
        return
      }
      this.btnLoading = true
      this.tipMessage = '开始识别...'
      this.loopBreak = false

      let runIndex = 0

      try {
        //进入循环
        for (let i = 0; i < this.multipleSelection.length; i++) {
          runIndex = i
          this.imgSrc = ""
          this.imgYzmys = ''

          // 选择行
          this.$refs.multipleTable.setCurrentRow(this.multipleSelection[i])
          this.tipMessage = '开始识别...' + this.multipleSelection[i].imageName

          let fpdm = '', fphm = '', kprq = '', xym = '', bhsje = ''

          if (this.multipleSelection[i].fphm !== '') {
            //当前行的发票参数不为空，不执行二维码识别和发票识别
            fpdm = this.multipleSelection[i].fpdm
            fphm = this.multipleSelection[i].fphm
            kprq = this.multipleSelection[i].kprq
            bhsje = this.multipleSelection[i].bhsje
            xym = this.multipleSelection[i].xym
          } else {
            //当前行的发票参数为空，执行二维码识别和发票识别
            //先对发票图片进行识别
            this.tipMessage = '正在识别，' + this.multipleSelection[i].imageName + '  正在进行二维码识别'
            var qcxx = await this.invoiceQRCode(this.multipleSelection[i].imageSrc)
            this.tipMessage = '正在识别，' + this.multipleSelection[i].imageName + '，  二维码识别完成'
            console.log('二维码：', qcxx)
            if (qcxx.length >= 8) {
              // fplx = qcxx[1].trim()
              fpdm = qcxx[2].trim()
              fphm = qcxx[3].trim()
              kprq = qcxx[5].trim()
              xym = qcxx[6].trim();
              bhsje = qcxx[4].trim();

              this.tipMessage = '正在识别，' + this.multipleSelection[i].imageName + '，  二维码识别成功'
              this.tipMessage = '正在识别，' + this.multipleSelection[i].imageName + '，  正在从国税读取发票信息...'
            } else {
              this.tipMessage = '正在识别，' + this.multipleSelection[i].imageName + '  正在进行OCR识别'
              const t1 = window.performance.now()
              let fpxx = await this.getInvoiceOCR(this.multipleSelection[i].imageSrc)
              const t2 = window.performance.now()
              console.log('图片OCR时间（ms）：', t2 - t1);//

              if (fpxx === null || fpxx.length < 6) {
                this.multipleSelection[i].zt = '发票识别失败'
                this.tipMessage = '正在识别，' + this.multipleSelection[i].imageName + '  发票识别失败'
                continue
              }

              this.tipMessage = '正在识别，' + this.multipleSelection[i].imageName + '  正在进行格式化'
              var fpxx1 = await this.formatInvoice(fpxx)

              if (fpxx1 === null || "" === fpxx1["fphm"]) {
                this.multipleSelection[i].zt = '发票识别格式化失败'
                this.tipMessage = '正在识别，' + this.multipleSelection[i].imageName + '  发票信息格式化失败'
                continue
              }

              this.tipMessage = '正在识别，' + this.multipleSelection[i].imageName + '  发票信息格式化完成'
              this.tipMessage = '正在识别，' + this.multipleSelection[i].imageName + '，  正在从国税读取发票信息...'
              fpdm = fpxx1['fpdm']
              fphm = fpxx1['fphm']
              kprq = fpxx1['kprq']
              bhsje = fpxx1['kpje']
              xym = fpxx1['jym']

            }
          }
          this.multipleSelection[i].fpdm = fpdm
          this.multipleSelection[i].fphm = fphm
          this.multipleSelection[i].kprq = kprq
          this.multipleSelection[i].bhsje = bhsje
          this.multipleSelection[i].xym = xym

          this.tipMessage = '正在识别，' + this.multipleSelection[i].imageName + '，  正在从国税读取发票信息...'

          let id = ""
          //判断是否存在，并写入数据库
          let isSucess = await verifyInvoice(fpdm, fphm, kprq, xym, bhsje).then((res)=> {
            // return res.data.data
            console.log('res:==', res)
            if (res.data.data['message'] === '成功') {
              id = res.data.data['id']
              console.log('id:', id)
              return true
            }else{
              this.multipleSelection[i].zt = res.data.data['message']
              this.tipMessage = '正在识别，' + this.multipleSelection[i].imageName + '，  ' + res.data.data['message']
              return false
            }

          })

          if (!isSucess) {
            continue
          }

          if(this.loopBreak){
            this.tipMessage = '用户已中止'
            break
          }
          this.tipMessage = '正在识别，' + this.multipleSelection[i].imageName + '，  正在上传附件...'
          //上传附件
          let base64 = this.multipleSelection[i].imageSrc
          let imageName = this.multipleSelection[i].imageName.replace(".png", "").replace(".jpg", "")
          let businessKey= id

          let ret = false
          if (this.multipleSelection[i].hasOwnProperty('file')) {
            console.log('是pdf')
            let file = this.multipleSelection[i].file
            ret = await this.uploadPdfFile(file, businessKey, imageName)
          }else {
            ret = await this.uploadImgFile(base64, businessKey, imageName)
          }

          if(ret){
            this.multipleSelection[i].zt = "成功"
            this.tipMessage = '正在识别，' + this.multipleSelection[i].imageName + '，  发票识别成功'
            //取消选中
            // this.$refs.multipleTable.toggleRowSelection(this.arrFpxx[i]);
          }else{
            this.multipleSelection[i].zt = "上传附件失败"
            this.tipMessage = '正在识别，' + this.multipleSelection[i].imageName + '，  上传附件失败'
          }
          // this.tipMessage = '发票识别完成'
        }
        this.tipMessage = '发票识别完成。'

      }catch (e){
        console.log(e)
        this.multipleSelection[runIndex].zt = e
        this.imgYzm = ""
        this.btnLoading = false
      }

      this.imgYzm = ""
      this.btnLoading = false

    },

    /**
     * 延时函数
     */
    sleep(ms) {
      return new Promise(resolve => setTimeout(resolve, ms));
    },

    zhongZhi() {
      this.loopBreak = true
      console.log('this.multipleSelection:', this.multipleSelection)
    },

    /**
     * 上传图片附件
     */
    async uploadImgFile(base64, businessKey, fileName){
      const toFile = this.base64ToFile(base64, fileName)
      // console.log("file:", toFile)
      const formData = new FormData();
      formData.append('businessKey', businessKey);
      formData.append('businessName', 'ni_fin_bill_receipt');
      formData.append('file', toFile);
      let ret = false
      ret = await upload(formData).then((res) => {
        console.log(res.data)
        return res.data.success
      });

      // console.log(ret)
      return ret
    },
    /**
     * 上传pdf附件
     */
    async uploadPdfFile(file, businessKey, fileName){
      console.log("fileName:", fileName)
      const formData = new FormData();
      formData.append('businessKey', businessKey);
      formData.append('businessName', 'ni_fin_bill_receipt');
      formData.append('file', file);
      let ret = false
      ret = await upload(formData).then((res) => {
        console.log(res.data)
        return res.data.success
      });

      // console.log(ret)
      return ret
    },

    /**
     * base64ToFile
     */
    base64ToFile(base64, fileName = 'myimg'){
      // 将base64按照 , 进行分割 将前缀  与后续内容分隔开
      let data = base64.split(','),
        // 利用正则表达式 从前缀中获取图片的类型信息（image/png、image/jpeg、image/webp等）
        type = data[0].match(/:(.*?);/)[1],
        // 从图片的类型信息中 获取具体的文件格式后缀（png、jpeg、webp）
        suffix = type.split('/')[1],
        // 使用atob()对base64数据进行解码  结果是一个文件数据流 以字符串的格式输出
        bstr = window.atob(data[1]),
        // 获取解码结果字符串的长度
        n = bstr.length,
        // 根据解码结果字符串的长度创建一个等长的整形数字数组
        // 但在创建时 所有元素初始值都为 0
        u8arr = new Uint8Array(n)

      // 将整形数组的每个元素填充为解码结果字符串对应位置字符的UTF-16 编码单元
      while (n--) {
        // charCodeAt()：获取给定索引处字符对应的 UTF-16 代码单元
        u8arr[n] = bstr.charCodeAt(n)
      }
      // 利用构造函数创建File文件对象
      // new File(bits, name, options)
      const file = new File([u8arr], `${fileName}.${suffix}`, {
        type: type
      })

      // 返回file
      return file
    },

    /**
     * 发票json格式化
     */
    async formatInvoice(jsn){
      if(jsn.length === 0){
        alert('没有发票json')
        return;
      }

      // var url = "http://localhost:1889/e4";
      let url = this.invoiceApiDictKeyValue[3]
      console.log("formatInvoice_url:", url)

      var data = await formatInvoiceApi(url, jsn).then((res) => {
        // console.log('格式化：', res.data)
        return res.data;
      });
      return data

    },

    /**
     * 发票识别
     */
    async getInvoiceOCR(imgc){
      if(imgc===undefined){
        alert('没有图片')
        return null;
      }
      imgc = imgc.replace('data:image/jpeg;base64,','').replace('data:image/png;base64,','')
      imgc = "{\"images\":[\"" + imgc + "\"]}"

      // var url = "http://localhost:1889/predict/ch_pp-ocrv3";
      let url = this.invoiceApiDictKeyValue[2]
      console.log("ocr_url:", url)

      var data = await invoiceOcr(url, imgc).then((res) => {
        // console.log('识别：', res.data.results[0].data)
        console.log('识别：', res.data)
        return res.data.results[0].data;
      });
      return data
    },

    /**
     * 国*税*网读取1
     */
    async getInvoiceUrl1(fpdm, fphm, kprq, jym, fplx) {
      // var url = `http://localhost:1889/e1/${fpdm}/${fphm}/${kprq}/${jym}`;
      let url = this.invoiceApiDictKeyValue[4] + `/${fpdm}/${fphm}/${kprq}/${jym}/${fplx}`
      console.log("e1_url:", url)

      await invoiceUrl(url).then((res) => {
        var imgSrc = res.data['image_data']
        this.imgSrc = "data:image/png;base64," + imgSrc
        this.imgYzmys = res.data['image_message']
        console.log('已刷新验证码')
      });

    },

    /**
     * 国*税*网读取2
     */
    async getInvoiceUrl2(fpdm, fphm, yzm) {
      // var url = `http://localhost:1889/e2/${fpdm}/${fphm}/${yzm}`;
      let url = this.invoiceApiDictKeyValue[5] + `/${fpdm}/${fphm}/${yzm}`
      console.log("e2_url:", url)

      var xx = await invoiceUrl(url).then((res) => {
        // console.log('res2:', res)
        // var fpxx = res.data;
        return res.data
      });
      return xx
    },

    /**
     * 发票二维码识别
     */
    async invoiceQRCode(imgc){
      if(imgc===undefined){
        return;
      }
      imgc = imgc.replace('data:image/jpeg;base64,','').replace('data:image/png;base64,','')

      // var url = "http://localhost:1889/e3";
      let url = this.invoiceApiDictKeyValue[1]
      console.log("url:", url)

       var ret = await invoiceQRCode(url, imgc).then((res) => {
         // console.log('二维码识别：', res)
         var fpxx = "";
         for (var ia in res.data) {
           fpxx = res.data[ia];
           break;
         }
         var aFpxx = fpxx.split(',')
         return aFpxx
       });
       return ret
    },

    /**
     * 关闭拖动图片
     * @returns {boolean}
     */
    onMove(){
      return false;
    },

    /**
     * 调整图像位置
     * @param {Object} evt
     */
    onEndDrag:function(evt){
      console.log(evt)
      return;
      // var oldIndex = evt.oldIndex;
      // var newIndex = evt.newIndex;
      // var that = this;
      // this.WebScan.changeIndex(oldIndex,newIndex,function(result){
      //   if(result.code!=200){
      //     that.$message.warning(result.msg);
      //     return;
      //   }
      //   that.eleMessage("调整成功","success");
      // });
    },

    /**
     * 撤销
     * @param {Object} $event
     */
    undo:function($event){
      if(this.undoStack.length<=0){
        return false;
      }
      this.addOptActive($event);
      this.clearDivListener();
      var undo=this.undoStack.pop();
      var zoom = undo['zoom']||this.canvas.getZoom();
      if(undo==undefined){
        return false;
      }
      //确定图像
      var redo = this.canvas.toJSON(['selection','hasControls','selectable']);
      redo['zoom']=this.canvas.getZoom();
      redo['angle']=Math.trunc(this.image.angle);
      this.redoStack.push(redo);
      this.canvas.loadFromJSON(undo,this.canvas.renderAll.bind(this.canvas));
      this.canvas.renderAll();
      var objects = undo.objects;
      var image = null;
      if(objects!=null && objects.length>0){
        for(var i = 0;i<objects.length;i++){
          var type=objects[i].type;
          if(type=='image'){
            image = objects[i];
            this.image=image;
            break;
          }
        }
      }
      if(image!=null){
        var angle = undo['angle']||Math.trunc(image.angle);
        var orignalWidth=image.width;
        var orignalHeight=image.height;
        var oldEven=(angle/90)%2;
        if(oldEven!=0&&angle%90==0){
          orignalWidth = this.image.height;
          orignalHeight = this.image.width;
        }
        this.canvas.setDimensions({
          width:orignalWidth*zoom,
          height:orignalHeight*zoom
        });
        this.canvas.setZoom(zoom);
        this.canvas.setBackgroundColor("#ffffff");
        this.canvas.renderAll.bind(this.canvas);
      }
      this.canvas.renderAll();
    },
    /**
     * 重做
     * @param {Object} $event
     */
    redo:function($event){
      if(this.redoStack.length<=0){
        return false;
      }
      this.addOptActive($event);
      this.clearDivListener();
      var redo=this.redoStack.pop();
      var zoom = redo['zoom']||this.canvas.getZoom();
      if(redo==undefined){
        return;
      }
      this.addUndoStack();
      this.canvas.loadFromJSON(redo,this.canvas.renderAll.bind(this.canvas));
      this.canvas.renderAll();
      var objects = redo.objects;
      var image = null;
      if(objects!=null && objects.length>0){
        for(var i = 0;i<objects.length;i++){
          var type=objects[i].type;
          if(type=='image'){
            image = objects[i];
            this.image=image;
            break;
          }
        }
      }
      if(image!=null){
        var angle = redo['angle']||Math.trunc(image.angle);
        var orignalWidth=image.width;
        var orignalHeight=image.height;
        var oldEven=(angle/90)%2;
        if(oldEven!=0&&angle%90==0){
          orignalWidth = this.image.height;
          orignalHeight = this.image.width;
        }
        this.canvas.setDimensions({
          width:orignalWidth*zoom,
          height:orignalHeight*zoom
        });
        this.canvas.setZoom(zoom);
      }
      this.canvas.renderAll();
    },

    /**
     * 删除选择的图像
     */
    deleteImage:function(){
      var that=this;
      if(this.selectManyImages==null || this.selectManyImages == undefined || this.selectManyImages.length<=0){
        this.eleMessage("请选择要删除的图像","warning");
        return ;
      }
      var sortList=that.selectManyImages.sort();
      this.$confirm('此操作将永久删除文件, 是否继续?', '删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        callback:function(value){
          if(value=='confirm'){
            //兼容pdf导入删除----------
            if (!that.btnLinkScan) {
              that.arrFpxx.splice(that.selectImageObj.index, 1)
              var index=sortList[sortList.length-1];
              if(index==that.urls.length-1){
                index=0;
              }else{
                index+=1
              }
              var nextSelectImage=that.urls[index];
              for(var i=sortList.length-1;i>=0;i--){
                that.urls.splice(sortList[i],1);
              }
              var nextSelectIndex=that.urls.indexOf(nextSelectImage);
              if(that.urls.length>nextSelectIndex){
                //选中下一个元素
                that.selectImage(that.urls[nextSelectIndex],nextSelectIndex);
              }else{
                that.clearDivListener();
                that.canvas.clear();
                that.canvas.setZoom(1);
                // that.totalAngle=0;
                that.canvas.discardActiveObject();
                $(".canvas-container").css("border","none");
              }
              return ;
            }
            //-------------------------

            var loading=that.eleLoadding();
            that.WebScan.deleteByIndexes(sortList,function(result){
              loading.close();
              if(result.code==200){
                var index=sortList[sortList.length-1];
                if(index==that.urls.length-1){
                  index=0;
                }else{
                  index+=1
                }
                var nextSelectImage=that.urls[index];
                for(var i=sortList.length-1;i>=0;i--){
                  that.urls.splice(sortList[i],1);
                }
                var nextSelectIndex=that.urls.indexOf(nextSelectImage);
                if(that.urls.length>nextSelectIndex){
                  //选中下一个元素
                  that.selectImage(that.urls[nextSelectIndex],nextSelectIndex);
                }else{
                  that.clearDivListener();
                  that.canvas.clear();
                  that.canvas.setZoom(1);
                  // that.totalAngle=0;
                  that.canvas.discardActiveObject();
                  $(".canvas-container").css("border","none");
                }
                //新加，删除选择图片记录 arrFpxx
                that.arrFpxx.splice(that.selectImageObj.index, 1)

                that.eleMessage("删除成功","success");
              }else{
                that.eleMessage(result.msg,"warning");
              }
            });
          }
        }
      });
    },

    /**
     * 保存图像
     */
    save:function(){
      if(!this.btnLinkScan){
        this.eleMessage("导入的pdf图像不支持修改保存！！！","warning");
        return;
      }
      if(this.imageName==null||this.imageName==''){
        this.eleMessage("请选择图像","warning");
        // this.$message.warning("请选择图像");
        return;
      }
      this.clearDivListener();
      var zoom=this.canvas.getZoom();
      zoom=1/zoom;
      var dataUrl=this.canvas.toDataURL({
        format: 'jpeg',//不能修改
        multiplier: zoom,
        quality:1
      });
      var that=this;
      var loading=this.eleLoadding();
      this.WebScan.saveImage(this.imageName,dataUrl,function(result){
        console.log('result:', result)
        console.log('that.multipleSelection:', that.multipleSelection)

        loading.close();
        if(result.code==200) {
          that.eleMessage("保存成功", "success");
          that.undoStack.length = 0;
          that.redoStack.length = 0;
          //获取选中的一张重新赋值
          var index = that.selectImageObj.index;
          var image = that.urls[index];
          image.src = dataUrl;
          that.urls.splice(index, 1, image);
          that.canvasJson = '';
          that.loadImage(image);
          // that.selectImageObj.image.src=dataUrl;

          //新加,点击保存后，更新arrFpxx
          that.arrFpxx[index].imageSrc = dataUrl
          // console.log(dataUrl)
          that.multipleSelection = []
          if (that.$refs.multipleTable != undefined) {
            that.$refs.multipleTable.clearSelection();
          }

        }else{
          that.eleMessage(result.msg,"warning");
        }
      });
    },

    /**
     * 内擦除
     * @param {Object} $event
     */
    innerErase:function($event){
      this.addOptActive($event);
      this.clearDivListener();
      var that=this;
      this.canvas.hoverCursor = 'crosshair';
      this.canvas.defaultCursor = 'crosshair';
      this.canvas.selection=true;
      this.canvas.on("mouse:down",function(e){
        that.startP=that.canvas.getPointer(e);
      })
      that.canvas.on("mouse:up",function(e){
        let endPointer=that.canvas.getPointer(e);
        if(endPointer.x==that.startP.x && endPointer.y == that.startP.y){
          return;
        }
        //计算开始结束点和范围
        var width=endPointer.x-that.startP.x;
        var height=endPointer.y-that.startP.y;
        if(width<0){
          width=-width;
        }
        if(height<0){
          height=-height;
        }
        var startLeft=that.startP;
        if(endPointer.x<startLeft.x){
          startLeft.x=endPointer.x;
        }
        if(endPointer.y<startLeft.y){
          startLeft.y=endPointer.y;
        }
        //添加矩形区域
        var rect=new fabric.Rect({
          fill:'rgb(255,255,255)',
          hasBorders:false,
          hasControls:false,
          left:startLeft.x,
          top:startLeft.y,
          width:width,
          height:height,
          preserveObjectStacking:true,
          selectable:false
        })
        that.addUndoStack();
        that.canvas.add(rect);
      });
    },

    addUndoStack:function(){
      var canvasJSON=this.canvas.toJSON(['selection','hasControls','selectable']);
      canvasJSON['zoom']=this.canvas.getZoom();
      canvasJSON['angle']=Math.trunc(this.image.angle);
      this.undoStack.push(canvasJSON);
    },

    /**
     * 旋转图像
     * @param {Object} rotate 旋转角度
     */
    rotateAngle:function(rotate){
      var that=this;
      var zoom=this.canvas.getZoom();
      var group=new fabric.ActiveSelection(this.canvas.getObjects(), {
        selectable:false,
        hasBorders:false,
        hasControls:false,
        centeredRotation:true,
        originX:"center",
        originY:"center"
      });
      // this.totalAngle+=rotate;
      group.rotate(rotate);
      var orignalWidth=this.image.width;
      var orignalHeight=this.image.height;
      var angle = Math.trunc(this.image.angle);
      angle+=rotate;
      // var oldEven=(this.totalAngle/90)%2;
      // if(oldEven!=0&&this.totalAngle%90==0){
      var oldEven=(angle/90)%2;
      if(oldEven!=0&&angle%90==0){
        orignalWidth = this.image.height;
        orignalHeight = this.image.width;
      }
      group.set({
        left:orignalWidth/2,
        top:orignalHeight/2
      });
      this.canvas.setDimensions({
        width:orignalWidth*zoom,
        height:orignalHeight*zoom
      });
      that.canvas.setBackgroundColor("#ffffff");
      that.canvas.renderAll.bind(this.canvas);
      group.destroy();
    },

    /**
     * 旋转
     * @param {Object} rotate 旋转角度
     * @param {Object} $event
     */
    rotate:function(rotate,$event){
      if($event!=undefined){
        this.addOptActive($event);
      }
      this.clearDivListener();
      this.addUndoStack();
      this.rotateAngle(rotate);
    },

    /**
     * 为canvas外层添加鼠标按下和监听
     */
    addListenerToDiv:function($event){
      this.addOptActive($event);
      this.clearDivListener();
      var that=this;
      var startX=0;
      var startY=0;
      this.canvas.defaultCursor = 'pointer';
      this.canvas.hoverCursor = 'pointer';
      $("#canvas-container").bind("mousedown",function(e){
        startX=e.offsetX;
        startY=e.offsetY;
        that.canvas.selection=false;
        $("#canvas-container").bind("mousemove",function(e){
          var endX=e.offsetX;
          var endY=e.offsetY;
          var offsetX=startX-endX;
          var offsetY=startY-endY;
          //获取现在X的滚动长度
          var scrollY=$(this).scrollTop();
          //获取当前Y的滚动长度
          var scrollX=$(this).scrollLeft();
          $(this).scrollLeft(scrollX+offsetX);
          $(this).scrollTop(scrollY+offsetY);
        });
      });
      $("#canvas-container").bind("mouseup",function(e){
        console.log(e)
        $(this).unbind("mousemove");
      });
    },

    /**
     * 放大
     */
    scaleMax:function($event){
      this.addOptActive($event);
      this.clearDivListener();
      // var height=this.$refs.canvasContainer.offsetHeight;
      // var width=this.$refs.canvasContainer.offsetWidth;
      if(this.image==null){
        this.$message.warning('请选择图像进行操作');
        return;
      }
      var offset=0.1;
      var scale = this.canvas.getZoom();
      scale+=offset;
      var canvasWidth=this.image.width*scale;
      var canvasHeight=this.image.height*scale;
      var angle = Math.trunc(this.image.angle);
      // var oldEven=(this.totalAngle/90)%2;
      // if(oldEven!=0&&this.totalAngle%90==0){
      var oldEven=(angle/90)%2;
      if(oldEven!=0&&angle%90==0){
        this.canvas.setDimensions({
          width:canvasHeight,
          height:canvasWidth
        })
      }else{
        this.canvas.setDimensions({
          width:canvasWidth,
          height:canvasHeight
        })
      }
      this.canvas.setZoom(scale);
    },
    /**
     * 缩小
     * @param {Object} $event
     */
    scaleMin:function($event){
      this.addOptActive($event);
      this.clearDivListener();
      // var height=this.$refs.canvasContainer.offsetHeight;
      // var width=this.$refs.canvasContainer.offsetWidth;
      if(this.image==null){
        this.$message.warning("请选择图像进行操作");
        return;
      }
      var offset=0.1;
      var scale = this.canvas.getZoom();
      scale-=offset;
      if(scale<0.1){
        scale=0.1;
      }
      var canvasWidth=this.image.width*scale;
      var canvasHeight=this.image.height*scale;
      var angle = Math.trunc(this.image.angle);
      // var oldEven=(this.totalAngle/90)%2;
      // if(oldEven!=0&&this.totalAngle%90==0){
      var oldEven=(angle/90)%2;
      if(oldEven!=0&&angle%90==0){
        this.canvas.setDimensions({
          width:canvasHeight,
          height:canvasWidth
        })
      }else{
        this.canvas.setDimensions({
          width:canvasWidth,
          height:canvasHeight
        })
      }
      this.canvas.setZoom(scale);
    },

    /**
     * 删除批次下的所有图像
     */
    deleteAllImage(){
      var that=this;
      this.$confirm('此操作将永久清除该批次文件, 是否继续?', '清空列表', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        callback:function(value){
          if(value=='confirm'){
            //兼容pdf删除---
            if (!that.btnLinkScan) {
              that.urls=new Array();
              that.canvas.clear()
              that.canvasJson = ''
              that.canvas.setZoom(1);

              that.arrFpxx = []
              return ;
            }
            //-------------

            var loading=that.eleLoadding();
            var result=that.WebScan.deleteAllImage(function(result){
              loading.close();
              if(result.code==200){
                that.urls=new Array();
                that.eleMessage("清空成功","success");
                // window.location.reload();

                that.canvas.clear()
                that.canvasJson = ''
                that.canvas.setZoom(1);

                that.arrFpxx = []
                console.log("that.arrFpxx11:", that.arrFpxx)

              }else{
                that.eleMessage(result.msg,"warning");
              }
            });
            console.log(result)
          }
        }
      })
    },

    /**
     * 界面显示后执行
     */
    opened(){
      console.log("Opened");
      this.initRun();

      // var xx = {
      //   "imageName":"1111",
      //   "imageSrc":"2222",
      //   "fpdm":"",
      //   "fphm":"25372000000005889905",
      //   "kprq":"20250107",
      //   "xym":"",
      //   "bhsje":"1020.00",
      //   "zt":"",
      // }
      // this.arrFpxx.push(xx);
      // var xx1 = {
      //   "imageName":"1111",
      //   "imageSrc":"2222",
      //   "fpdm":"",
      //   "fphm":"25372000000013497053",
      //   "kprq":"20250114",
      //   "xym":"",
      //   "bhsje":"20790.00",
      //   "zt":"",
      // }
      // this.arrFpxx.push(xx1);

    },

    /**
     * 初始化
     */
    initRun(){
      // this.arrFpxx = []
      // console.log("this.arrFpxx:", this.arrFpxx)
      if(this.canvas!=undefined){
        return;
      }
      // console.log(this.canvas)
      var width=$("#canvas-container").width();
      var height=$("#canvas-container").height();
      // console.log(width)
      // console.log(height)

      this.canvas =new fabric.Canvas("imageCanvas",
        {
          allowTouchScrolling:true,
          centeredRotation:true,
          centeredScaling:true,
          defaultCursor:'default',
          hoverCursor:'default',
          backgroundColor:"#ffffff",
          backgroundVpt:true,
          width:width,
          height:height,
        })

      this.addDbClick();
      // console.log(this.form.netWorkScanParam)
      this.getSystem();
      // this.init();
      this.userId=this.getQueryVariable("id");
      // console.log("初始化")
    },

    /**
     * 多选图像
     * @param {Object} image 图像对象包含src和imageName两个属性
     * @param {Object} index 图像在列表中的排序，从0开始
     */
    selectManyImage:function(image,index){
      var indexof=this.selectManyImages.indexOf(index);
      if(indexof>=0){
        var nextIndex=indexof+1;
        if(nextIndex<this.selectManyImages.length){
          let index=this.selectManyImages[nextIndex];
          this.checkImageModify(this.urls[index],index);
        }else{
          var preIndex=indexof-1;
          if(preIndex>=0){
            let index=this.selectManyImages[preIndex];
            this.checkImageModify(this.urls[index],index);
          }else{
            // this.loadImage(null);
            this.checkImageModify(null,0);
            $(".canvas-container").css("border","none");
          }
        }
        this.selectManyImages.splice(indexof,1);
      }else{
        this.selectManyImages.push(index);
        this.checkImageModify(this.urls[index],index);
      }
    },

    /**
     * 保存扫描设定参数
     */
    saveSetup(){
      var that=this;
      this.form.params=new Array();
            var form={
        device:"GL2030A",
        autofeeder:true,
        pixel:2,
        white:0,
        single:true,
        format:"png",
        resolution:300,
        isActual:true,
        isUI:false,
        cropAdjustTop:0,
        cropAdjustLeft:0,
        cropAdjustBottom:0,
        cropAdjustRight:0,
        compress:0,
        mode:0,
        upload:{
          uploadMode:2,
          httpUrl:'',
          fileName:'',
          httpMethod:'',
          header:'',
          param:'',
          ftpUrl:'',
          ftpPath:'/invoice-scan',
          ftpUser:'',
          ftpPassword:'',
          ftpPort:21,
          ftpMode:2
        },
        scanSystem:{
          licence:'0eXmL/7gpINSOJ7MICMhrQ==',
          imagePath:'c:/image',
          imagePreName:'fp',
          isDate:true,
          isTime:false,
          random:0,
          randomLength:3,
          randomCover:true,
          datePattern:'yyyyMMdd'
        },
        params:[
          {
          "code":"1153",
          "value":"95",
          "type":"1"
          },
          {
            "code":"9266",
            "value":"0",
            "type":"4"
          },
          {
            "code":"1152",
            "value":"0",
            "type":"6"
          },
          {
            "code":"1136",
            "value":"0",
            "type":"4"
          },
          {
            "code":"9197",
            "value":"2",
            "type":"4"
          }
        ]
      };
      this.WebScan.setParams(form,function(result){
        if(result.code==200){
          that.eleMessage(result.msg,"success");
          that.isSetup=false;
        }else{
          that.eleMessage(result.msg,"warning");
        }
      })
    },

    /**
     * 授权
     */
    approveLicence(){
      this.form.scanSystem.licence='0eXmL/7gpINSOJ7MICMhrQ=='
      if(this.form.scanSystem.licence==''||this.form.scanSystem.licence==null){
        this.eleMessage("请输入授权码","warning");
        return;
      }
      var that=this;
      this.WebScan.licence=this.form.scanSystem.licence;
      this.WebScan.saveLicence(this.form.scanSystem.licence,function(result){
        if(result.code!=200){
          that.eleMessage(result.msg,"warning");
          return;
        }
        that.eleMessage("授权成功","success")
        that.preLicenceDialog=false
      })
    },

    getQueryVariable(variable)
    {
      var query = window.location.search.substring(1);
      var vars = query.split("&");
      for (var i=0;i<vars.length;i++) {
        var pair = vars[i].split("=");
        if(pair[0] == variable)
        {
          return pair[1];
        }
      }
      return null;
    },

    getSystem(){
      var isWin = (navigator.platform == "Win32") || (navigator.platform == "Windows");
      this.form.isWindow=isWin;
    },

    checkImageModify(image,index){
      if(image==null){
        return;
      }
      var that = this;
      if(this.canvasJson!=null&& this.canvasJson!=''){
        var dataUrl = this.canvas.toDatalessJSON();
        //发生更改，做更新提醒
        if(JSON.stringify(dataUrl)!=JSON.stringify(this.canvasJson)){
          this.$confirm('文件发生更改是否需要保存?', '保存', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
            callback: function (value) {
              if(value=="confirm"){
                that.clearDivListener();
                var zoom=that.canvas.getZoom();
                zoom=1/zoom;
                var dataUrl=that.canvas.toDataURL({
                  format: 'jpeg',
                  multiplier: zoom,
                  quality:1
                });
                var loading=that.eleLoadding();
                that.WebScan.saveImage(that.imageName,dataUrl,function(result){
                  loading.close();
                  if(result.code==200){
                    that.eleMessage("保存成功","success");
                    that.undoStack.length=0;
                    that.redoStack.length=0;
                    //获取选中的一张重新赋值
                    that.canvasJson='';
                    //更改图像列表中的base64
                    var pre_index = that.selectImageObj.index;
                    that.urls[pre_index].src=dataUrl;
                    that.selectImageObj={
                      "image":that.urls[index],
                      "index":index
                    }
                    that.loadImage(that.urls[index]);

                  }else{
                    that.eleMessage(result.msg,"warning");
                  }
                  that.selectImageObj={
                    "image":that.urls[index],
                    "index":index
                  }
                  that.loadImage(that.urls[index]);
                });
              }else{
                that.selectImageObj={
                  "image":that.urls[index],
                  "index":index
                }
                that.loadImage(that.urls[index]);
              }
            }
          })
        }else{
          this.selectImageObj={
            "image":this.urls[index],
            "index":index
          }
          this.loadImage(this.urls[index]);
        }
      }else{
        this.selectImageObj={
          "image":this.urls[index],
          "index":index
        }
        this.loadImage(this.urls[index]);
      }
    },

    /**
     * 清除canvas上所有的监听
     */
    clearDivListener(){
      $("#canvas-container").unbind("mouseup");
      $("#canvas-container").unbind("mousedown");
      this.canvas.defaultCursor = 'default';
      this.canvas.hoverCursor = 'default';
      this.canvas.off("mouse:down");
      this.canvas.off("mouse:up");
      this.canvas.off("mouse:move");
      this.canvas.off("mouse:dblclick");
      this.rectificationDialog=false;
      if(this.assistLine!=null && this.assistLine!=undefined){
        this.canvas.remove(this.assistLine);
      }
      if(this.assistText!=null && this.assistText!=undefined){
        this.canvas.remove(this.assistText);
      }
      this.addDbClick();
    },

    /**
     * 加载大图
     * @param {Object} imageObj 图像对象包含src和imageName两个属性
     */
    loadImage(imageObj){
      this.clearDivListener();
      this.canvas.clear();
      this.canvas.setZoom(1);
      // this.totalAngle=0;
      this.canvas.discardActiveObject();
      var that=this;
      if(imageObj==null || imageObj==undefined){
        return;
      }
      var src=imageObj.src;
      new fabric.Image.fromURL(src,function(image){
        that.image=image;
        that.imageName=imageObj.imageName;
        that.canvas.add(image);
        that.canvas.renderAll();
        that.ajustDimensions();
        var dataUrl = that.canvas.toDatalessJSON();
        that.canvasJson=dataUrl;
      },{
        selectable:false
      });
      $(".canvas-container").css("border","2px solid #e1e1e1");
    },

    /**
     * 选择图片
     * @param {Object} image 图像对象包含src和imageName两个属性
     * @param {Object} index 图像在列表中的排序，从0开始
     */
    selectImage(image,index){
      this.checkImageModify(image,index);
      this.selectManyImages=new Array();
      this.selectManyImages.push(index);

      this.index = index;
    },

    /**
     * 获取所有的图像
     */
    getAllImages(){
      console.log("获取所有图像，初始化，清空数组")
      var that=this;
      var loading=this.eleLoadding();
      this.WebScan.getAllImages(function(result){
        loading.close();
        if(result.code==200){
          if(result.data!=null && result.data.length>0){
            that.urls=result.data;
            // if(that.selectImageObj.image!=undefined){
            // 	that.selectImage(that.selectImageObj.image,that.selectImageObj.index);
            // }

            //2024.3.12新加----
            console.log("获取所有图像，初始化，清空数组")
            that.arrFpxx = [];//清空数组
            for (let i = 0; i < result.data.length; i++) {
              var xx = {
                "imageName":result.data[i].imageName,
                "imageSrc":result.data[i].src,
                "fpdm":"",
                "fphm":"",
                "kprq":"",
                "xym":"",
                "bhsje":"",
                "zt":"",
              }
              that.arrFpxx.push(xx);
            }
            console.log("fpxx:", that.arrFpxx)
            //----------------
            that.selectImage(that.urls[0],0);
          }
        }else{
          that.eleMessage(result.msg,"warning");
        }
      })
    },

    loadUploadUrl(){
      var that = this;
      if(this.permissions.indexOf('ocr-extract-uploadOfd')>=0){
        this.WebScan.getSystemValue("uploadUrl",function(result){
          if(result.code==200){
            that.extract.uploadUrl=result.data;
          }
        })
      }
    },
    /**
     * 获取最后一个批次
     */
    getLastBatch(){
      let that = this;
      let loading=this.eleLoadding();
      this.WebScan.getLastBatch(function(data){
          loading.close();
        // var pid=sessionStorage.getItem("webscan_pid");
        if(data.code==200){
          let pid = data.data
          that.pid = pid
          if(data.data!=null && data.data!=that.pid) {
            sessionStorage.setItem('webscan_pid', pid)
            that.WebScan.setClientId(pid);
            that.getAllImages();
          }else{
            // that.getAllImages();
            that.eleMessage("当前批次已是最新批次","warning");
            // return;
          }
        }else{
          that.eleMessage(data.msg,"warning");
          return;
        }

      })

    },

    /**
     * 初始化方法
     */
    init(){
      // this.initRun()
      this.WebScan=new window.WebScan({
        url:'http://127.0.0.1:18989/WebScan',
        wsUrl:'http://127.0.0.1:28989/',
        licence:this.licence
      });

      this.form.scanSystem.licence = "0eXmL/7gpINSOJ7MICMhrQ==";
      var pid=sessionStorage.getItem("webscan_pid");
      this.pid = pid;
      console.log('pid:', pid)
      var _that=this;
      var loading=this.eleLoadding();
      this.WebScan.licence=this.form.scanSystem.licence;
      this.WebScan.initSef(pid,function(result){
        loading.close();
        if(result.code!=200){
          //检查WebScan是否运行
          if(_that.form.isWindow){
            window.protocolCheck("WebScan:1",
              function(){
                window.confirm("请安装WebScan后使用!");
              }
            )
          }
          else{
            _that.eleMessage(result.msg,"插件未安装或未启动")
          }
          return;
        }
        // console.log("授权：", result)
        if(pid != null && pid != '' && pid != undefined){
          _that.pid=pid;
          sessionStorage.setItem("webscan_pid",_that.pid);
          _that.WebScan.setClientId(pid);
          //获取上次的扫描结果
        }else{
          _that.pid=_that.WebScan.getClientId();
          sessionStorage.setItem("webscan_pid",_that.pid);
        }
        _that.getAllImages();
        _that.getUploadParam();
        // var that = this;
        _that.WebScan.getPermission(function(result){
          if(result.code==200){
            var auth = result.data;
            _that.permissions = auth.permissions;
            _that.loadUploadUrl();
          }
        });
        // _that.getLevel();
        // //加载模板
        // _that.loadModels();
        _that.btnLinkScan = true
        _that.eleMessage("连接扫描仪成功", "success")
      });
    },

    loadModels:function(){
      var that = this;
      var loading = this.eleLoadding("加载模板列表");
      this.WebScan.loadModels(function(result){
        loading.close();
        if(result.code==200){
          var model={
            "id":null,
            "name":"无"
          }
          that.modelList = result.data;
          if(that.modelList!=null && that.modelList.length>0){
            that.modelList.unshift(model);
          }
        }else{
          this.eleMessage(result.msg,"warning")
        }
      })
    },

    /**
     * 获取当前系统的日志级别
     */
    getLevel:function(){
      var that = this;
      var loading = this.eleLoadding();
      this.WebScan.getLogLevel(function (result) {
        loading.close();
        if (result.code == 200) {
          that.logLevel=result.data;
        } else {
          that.eleMessage(result.msg, "warning");
        }
      });
    },

    /**
     * 弹出提示框
     * @param {Object} msg 提示框内容
     * @param {Object} sign 样式，warn、success、info
     */
    eleMessage:function(msg,sign){
      if(sign=='warning' || sign=='error'){
        this.$alert(msg,"警告！");
        return;
      }
      this.$message({
        message : msg,
        type: sign,
        duration:2000
      });
    },

    /**
     * 加载动画
     */
    eleLoadding(text){
      if(text==''||text==undefined){
        text="Loading...";
      }
      return this.$loading({
        lock: true,
        text: text,
        spinner: 'el-icon-loading',
        target:'#container',
        background: 'rgba(0,0,0,0.7)'
      });
    },

    /**
     * 获取设备列表
     */
    getDevices(){
      var that=this;
      var loading=this.eleLoadding();
      this.WebScan.getDevices(function(result){
        loading.close();
        if(result.code!=200){
          that.eleMessage(result.msg,"warning");
          return;
        }else{
          that.devices=result.data;
          console.log(that.devices)
          //获取浏览器中的内存
          var localDevice=localStorage.getItem('webscan_device');
          if(localDevice!=null && window.devices.contains(localDevice)){
            that.form.device=localDevice;
          }
          if(that.devices!=null && that.devices.length>0){
            if(that.form.device==null || that.form.device==undefined){
              that.form.device=that.devices[0];
            }
          }
        }
      });
    },

    /**
     * 获取保存的参数
     */
    getUploadParam(){
      var that=this;
      this.WebScan.getParams(function(result){
        if(result.code!=200){
          that.eleMessage(result.msg,"warning");
          return;
        }else{
          if(result.data!=null){
            console.log(result)
            that.form=result.data;
            // that.dealParams(result)
            that.isWindows=result.data.isWindow;
            if(that.form.scanSystem.licence!='' && that.form.scanSystem.licence!=undefined){
              that.getDevices();
            }else{
              that.preLicenceDialog=true;
            }
          }
        }
      });
    },

    /**
     * 图像大图真实大小显示
     */
    realDimensions:function($event){
      this.addOptActive($event);
      this.clearDivListener();
      if(this.image==null || this.image==undefined){
        return;
      }
      var that=this;
      //判断是否需要横放
      var angle = Math.trunc(this.image.angle);
      // var oldEven=(this.totalAngle/90)%2;
      // if(oldEven!=0&&this.totalAngle%90==0){
      var oldEven=(angle/90)%2;
      if(oldEven!=0&&angle%90==0){
        this.canvas.setDimensions({
          width:that.image.height,
          height:that.image.width
        })
      }else{
        this.canvas.setDimensions({
          width:that.image.width,
          height:that.image.height
        })
      }
      this.canvas.setZoom(1);
      this.canvas.renderAll.bind(this.canvas);
      // this.isReal=true;
    },

    /**
     * 为左边列表中单选图像添加红框，其他图像去除红框
     * @param {Object} e
     */
    addOptActive:function(e){
      console.log(e)
      if(e==null || e==undefined){
        return;
      }
      $(e.currentTarget).addClass("active");
      $(e.currentTarget).siblings().removeClass("active");
    },

    /**
     * 图像大图自适应显示
     */
    ajustDimensions:function($event){
      this.addOptActive($event);
      this.clearDivListener();
      if(this.image==null || this.image==undefined){
        return;
      }
      var orignalWidth=this.image.width;
      var orignalHeight=this.image.height;
      var angle = Math.trunc(this.image.angle);
      // var oldEven=(this.totalAngle/90)%2;
      // if(oldEven!=0&&this.totalAngle%90==0){
      var oldEven=(angle/90)%2;
      if(oldEven!=0&&angle%90==0){
        orignalWidth = this.image.height;
        orignalHeight = this.image.width;
      }
      var width=$("#canvas-container").width()-4;
      var height=$("#canvas-container").height()-4;
      var zoom = Math.min(width/orignalWidth, height/orignalHeight);

      //图像压缩
      if(width>this.image.width&&height>orignalHeight){
        orignalWidth=this.image.width;
        orignalHeight=this.image.height
      }
      else if(zoom<1){
        orignalWidth=orignalWidth*zoom;
        orignalHeight=orignalHeight*zoom;
        this.canvas.setZoom(zoom);
      }else{
        this.canvas.setZoom(zoom);
      }
      this.canvas.setDimensions({
        'width':orignalWidth,
        'height':orignalHeight
      })
      this.canvas.renderAll.bind(this.canvas);
      this.clearDivListener();
      // this.isReal=false;
    },
    /**
     * 添加双击监听事件
     */
    addDbClick:function(){
      var that = this;
      this.canvas.on("mouse:dblclick",function(ev){
        console.log(ev)
        // console.log("双击")
        if(that.canvas.getZoom()==1){
          that.ajustDimensions();
        }else{
          that.realDimensions();
        }
      });
    },


    orderByRiding(){
      var that = this;
      var loading = this.eleLoadding('骑订排序中');
      this.WebScan.getParams(function(result){
        loading.close();
        if(result.code==200){
          var params = result.data;
          var sortType = params.sort;
          if(sortType!=undefined&&sortType!=null){
            that.sortMode=sortType;
            that.sortImageHttp();
          }else{
            console.log(that.selectImageObj)
            if(that.selectImageObj==null|| that.selectImageObj.index==-1){
              if(that.urls!=null && that.urls.length>0){
                that.selectImage(that.urls[0],0);
              }
            }
          }
        }else{
          that.eleMessage(result.msg,"warning")
        }
      })
    },

    /**
     * 发票识别
     */
    ocr(){
      console.log('this.arrFpxx:', this.arrFpxx)
      this.ocrVisible=true;



    },

    /**
     * 设置打印机
     */
    setting(){

      this.saveSetup();

    },


    /**
     * 正常扫描
     */
    scan(){
      // console.log("t:", this.WebScan.licence)
      var result=this.WebScan.startScan(window.scanCallBack,null);
      console.log("result:", result)
      if(result.code!=200){
        this.eleMessage(result.msg,"warning");
        return;
      }
      this.isInsertScan=false;
      this.isCoverScan=false;
      this.loading=this.eleLoadding();
    },

    eleUnloadding(){
      this.loading.close();
    },

    /**
     * 扫描回调函数
     * @param {Object} data socketIO返回的图像信息
     */
    scanCallBack:function(data){
      this.eleUnloadding();
      var code=data.code;
      console.log("返回",data)
      if(code ==201){
        if(this.permissions.indexOf('oprate-disable')>=0){
          this.isDisableOpt=true;
        }
        this.addImage(data);

        var xx = {
          "imageName":data.imageName,
          "imageSrc":data.image,
          "fpdm":"",
          "fphm":"",
          "kprq":"",
          "xym":"",
          "bhsje":"",
          "zt":"",
        }

        this.arrFpxx.push(xx);

      }
      if(code==203){
        this.isDisableOpt=false;
        this.isInsertScan=false;
        this.isCoverScan=false;
        this.oprationImageObj={};
        //根据后台参数判断是否需要骑订排序
        this.orderByRiding();
      }
      if(data.code==500){
        this.isDisableOpt=false;
        this.eleMessage(data.msg,"warning");
      }
    },

    addImage(data){
      var image={
        "src":data.image,
        "imageName":data.imageName,
      }
      if(this.isInsertScan){
        //找到文件位置，插入元素
        if(this.oprationImageObj==null||this.oprationImageObj.index<0||this.oprationImageObj.index==undefined){
          this.urls.push(image);
        }else{
          let index=this.oprationImageObj.index;
          this.urls.splice(index,0,image);
          this.oprationImageObj.index+=1;
        }
      }else if(this.isCoverScan){
        if(this.oprationImageObj==null||this.oprationImageObj.index<0||this.oprationImageObj.index==undefined){
          this.urls.push(image);
        }else{
          let index=this.oprationImageObj.index;
          //替换当前元素
          this.urls.splice(index,1,image);
          //找下一个元素
          if(index==this.urls.length-1){
            this.oprationImageObj= {};
          }else{
            this.oprationImageObj={
              image:this.urls[index+1],
              index:index+1
            };
          }
        }
      }else if(this.isPageCover){
        var isFirst = data.first;
        if(isFirst!=null &&isFirst){
          this.urls.unshift(image);
        }else{
          this.urls.push(image);
        }
      }
      else{
        // this.urls.unshift(image);
        this.urls.push(image);
      }
      if(this.permissions.indexOf('oprate-disable')>=0){
        this.selectImage(this.urls[this.urls.length-1],this.urls.length-1);
      }
    },




  }


}




</script>





<style >
  .imgGray
  {
    -webkit-filter: grayscale(100%);
    /* Chrome, Safari, Opera */
    filter: grayscale(100%);
  }
</style>

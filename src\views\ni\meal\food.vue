<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      :upload-after="uploadAfter"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template #status="{ row, index }">
        <el-tag v-if="row.status === 1" size="mini" type="info">
          {{ row.$status }}
        </el-tag>
        <el-tag v-else-if="row.status === 2" size="mini">
          {{ row.$status }}
        </el-tag>
        <el-tag v-else-if="row.status === 3" size="mini" type="warning">
          {{ row.$status }}
        </el-tag>
        <el-tag v-else size="mini" type="danger">
          {{ row.$status }}
        </el-tag>
      </template>
      <template #name="{ row, index }">
        <el-button
          size="mini"
          type="text"
          @click="$refs.crud.rowView(row, index)"
        >{{ row.name }}
        </el-button>
      </template>
      <template #type="{ row, index }">
        <el-tag v-if="row.type === '1'" size="mini">
          {{ row.$type }}
        </el-tag>
        <el-tag
          v-else-if="('2', '3', '4').includes(row.type)"
          size="mini"
          type="success"
        >
          {{ row.$type }}
        </el-tag>
        <el-tag v-else-if="row.type === '5'" size="mini" type="warning">
          {{ row.$type }}
        </el-tag>
        <el-tag v-else size="mini" type="danger">
          {{ row.$type }}
        </el-tag>
      </template>
      <template slot="menuLeft">
        <el-button
          type="danger"
          size="mini"
          icon="el-icon-delete"
          plain
          v-if="permission.omFood_delete"
          @click="handleDelete"
        >删 除
        </el-button>
        <el-button
          type="success"
          size="mini"
          plain
          v-if="permission.omFood_import"
          icon="el-icon-upload2"
          @click="handleImport"
        >导入
        </el-button>
        <el-button
          type="warning"
          size="mini"
          plain
          v-if="permission.omFood_import"
          icon="el-icon-download"
          @click="handleExport"
        >导出
        </el-button>
      </template>
      <template #peppery="{ row, index }">
        <el-tag v-if="row.peppery" size="mini" type="danger">
          {{ row.$peppery }}
        </el-tag>
        <el-tag v-else size="mini">
          {{ row.$peppery }}
        </el-tag>
      </template>
      <template #menuRight="{ size }">
        <el-button
          icon="el-icon-time"
          circle
          :size="size"
          @click="handleLog"
        ></el-button>
      </template>
      <template #rate="{ row, index }">
        <el-rate
          v-if="row.rate > 0"
          v-model="row.rate"
          disabled
          show-score
          text-color="#ff9900"
          score-template="{value}"
        >
        </el-rate>
        <span v-else style="color: #909399">暂无评分</span>
      </template>
      <template #menu="{ row, index, size }">
        <el-button
          type="text"
          icon="el-icon-edit"
          :size="size"
          v-if="permission.omFood_edit && [1, 2, 3].includes(row.status)"
          @click="$refs.crud.rowEdit(row, index)"
        >编 辑
        </el-button>
        <el-button
          type="text"
          icon="el-icon-s-promotion"
          :size="size"
          v-if="[1, 3].includes(row.status)"
          @click="rowSubmit(row)"
        >提交
        </el-button>
        <el-button
          type="text"
          icon="el-icon-delete"
          :size="size"
          v-if="row.status === 2"
          @click="rowToVoid(row)"
        >作废
        </el-button>
        <el-button
          type="text"
          icon="el-icon-chat-line-square"
          :size="size"
          @click="rowEvaluate(row)"
        >评价({{ row.evaluateNum ? row.evaluateNum : 0 }})
        </el-button>
      </template>
      <template #menuForm="{ row, index, type }">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          v-if="type === 'add'"
          @click="$refs.crud.rowSave()"
        >
          新增
        </el-button>
        <el-button
          type="primary"
          icon="el-icon-circle-check"
          size="mini"
          v-if="type === 'edit'"
          @click="$refs.crud.rowUpdate()"
        >
          修改
        </el-button>
        <el-button
          type="primary"
          icon="el-icon-s-promotion"
          size="mini"
          v-if="['edit', 'add'].includes(type)"
          @click="handleSubmit(type)"
        >
          提交
        </el-button>
        <el-button
          icon="el-icon-check"
          size="mini"
          v-if="['edit', 'add'].includes(type)"
          @click="$refs.crud.closeDialog()"
        >
          取消
        </el-button>
      </template>
    </avue-crud>
    <log-opt-dialog ref="logOptDialogRef" :module="module"/>
    <evaluate-dialog ref="evaluateDialogRef"/>
    <el-dialog
      title="菜品数据导入"
      append-to-body
      :visible.sync="excelBox"
      width="555px"
    >
      <avue-form
        :option="excelOption"
        v-model="excelForm"
        :upload-after="uploadAfter"
      >
        <template slot="excelTemplate">
          <el-button size="mini" type="primary" @click="handleTemplate">
            点击下载<i class="el-icon-download el-icon--right"></i>
          </el-button>
        </template>
      </avue-form>
    </el-dialog>
  </basic-container>
</template>

<script>
import {add, back, getDetail, getPage, remove, submit, toVoid, update,} from "@/api/ni/meal/food";
import {mapGetters} from "vuex";
import LogOptDialog from "@/components/log-opt-dialog";
import EvaluateDialog from "@/views/ni/meal/components/EvaluateDialog";
import {exportBlob} from "@/api/common";
import {getToken} from "@/util/auth";
import {downloadXls} from "@/util/util";

export default {
  components: {
    EvaluateDialog,
    LogOptDialog,
  },
  data() {
    return {
      module: "ni_meal_food",
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        searchEnter: true,
        labelWidth: 110,
        saveBtn: false,
        updateBtn: false,
        cancelBtn: false,
        editBtn: false,
        delBtn: false,
        align: "center",
        searchIndex: 3,
        searchIcon: true,
        size: "mini",
        searchSize: "mini",
        height: "auto",
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        selection: true,
        dialogClickModal: false,
        column: [
          {
            label: "菜名",
            prop: "name",
            row: true,
            search: true,
            type: "input",
            rules: [
              {
                required: true,
                message: "请输入菜名",
                trigger: "blur",
              },
            ],
          },
          {
            label: "类型",
            prop: "type",
            row: true,
            hide: true,
            type: "select",
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_meal_food_type",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
          },
          {
            label: "图片",
            type: "upload",
            listType: "picture-img",
            propsHttp: {
              res: "data",
              url: "link",
            },
            action:
              "/api/blade-resource/oss/endpoint/put-file-attach?code=public",
            tip: "只能上传jpg/png，且不超过500kb",
            fileSize: 512,
            span: 12,
            row: true,
            prop: "imgUrl",
          },
          {
            label: "是否辣菜",
            prop: "peppery",
            type: "radio",
            value: false,
            row: true,
            dicData: [
              {
                label: "是",
                value: true,
              },
              {
                label: "否",
                value: false,
              },
            ],
            rules: [
              {
                required: true,
                message: "请选择是否辣菜",
                trigger: "blur",
              },
            ],
          },
          {
            label: "评分",
            prop: "rate",
            minWidth: 120,
            addDisplay: false,
            editDisplay: false,
          },
          {
            label: "默认单价",
            prop: "price",
            type: "number",
            controls: false,
            precision: 2,
            placeholder: " ",
            row: true,
            search: true,
          },
          {
            label: "默认步长",
            prop: "step",
            type: "number",
            placeholder: " ",
            precision: 1,
            value: 1,
            min: 0.5,
            step: 0.5,
            hide: true,
            row: true,
          },
          {
            label: "最大订单数",
            prop: "max",
            type: "number",
            placeholder: " ",
            precision: 1,
            value: 1,
            min: 0.5,
            step: 0.5,
            hide: true,
            row: true,
          },
          {
            label: "备注",
            row: true,
            prop: "remark",
            type: "textarea",
            span: 24,
          },
          {
            label: "状态",
            prop: "status",
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_por_order_status",
            search: true,
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            addDisplay: false,
            editDisplay: false,
            dataType: "number",
            fixed: "left",
            type: "radio",
          },

          {
            label: "创建人",
            prop: "createUserName",
            addDisplay: false,
            editDisplay: false,
          },
          {
            label: "创建部门",
            prop: "createDeptName",
            addDisplay: false,
            editDisplay: false,
            hide: true,
          },
          {
            label: "提交时间",
            prop: "applyTime",
            type: "datetime",
            addDisplay: false,
            editDisplay: false,
            format: "yyyy-MM-dd hh:mm:ss",
            valueFormat: "yyyy-MM-dd hh:mm:ss",
          },
        ],
      },
      data: [],
      excelBox: false,
      excelForm: {},
      excelOption: {
        size: "mini",
        searchSize: "mini",
        submitBtn: false,
        emptyBtn: false,
        column: [
          {
            label: "模板上传",
            prop: "excelFile",
            type: "upload",
            drag: true,
            loadText: "模板上传中，请稍等",
            span: 24,
            propsHttp: {
              res: "data",
            },
            tip: "请上传 .xls,.xlsx 标准格式文件",
            action: "/api/ni/meal/food/import-food",
          },
          {
            label: "模板下载",
            prop: "excelTemplate",
            formslot: true,
            span: 24,
          },
        ],
      },
      export: {
        column: [
          {
            label: "id",
            prop: "id",
            width: 130,
          },
          {
            label: "菜名",
            prop: "name",
            width: 130,
          },
          {
            label: "类型",
            prop: "type",
            width: 130,
          },
          {
            label: "是否辣菜",
            prop: "peppery",
          },
          {
            label: "默认单价",
            prop: "price",
          },
          {
            label: "默认步长",
            prop: "step",
          },
          {
            label: "最大订单数",
            prop: "max",
          },
          {
            label: "备注",
            prop: "remark",
          },
        ]
      }
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.omFood_add, false),
        viewBtn: this.vaildData(this.permission.omFood_view, false),
        delBtn: this.vaildData(this.permission.omFood_delete, false),
        editBtn: this.vaildData(this.permission.omFood_edit, false),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  methods: {
    handleTemplate() {
      exportBlob(
        `/api/ni/meal/food/export-template?${
          this.website.tokenHeader
        }=${getToken()}`
      ).then((res) => {
        downloadXls(res.data, "菜品导入模板.xlsx");
      });
    },
    handleSubmit(type) {
      this.form.status = 2;
      if (type === "add") {
        this.$refs.crud.rowSave();
      } else if (type === "edit") {
        this.$refs.crud.rowUpdate();
      }
    },
    rowEvaluate(row) {
      this.$refs.evaluateDialogRef.init(row.id);
    },
    rowToVoid(row) {
      this.$confirm("此操作作废提交的数据，是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        center: true,
      })
        .then(() => {
          return toVoid(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    rowBack(row) {
      this.$confirm("此操作撤回提交的数据，是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        center: true,
      })
        .then(() => {
          return back(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    rowSubmit(row) {
      this.$confirm("此操作将提交该数据，是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        center: true,
      })
        .then(() => {
          return submit(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    handleLog() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择要查看的数据");
        return;
      } else if (this.selectionList.length > 1) {
        this.$message.warning("只能选择一条数据");
        return;
      }
      this.$refs.logOptDialogRef.init(this.selectionList[0].id);
    },
    rowSave(row, done, loading) {
      add(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          window.console.log(error);
        }
      );
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          console.log(error);
        }
      );
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    handleExport() {
      this.$confirm("是否导出当前筛选的所有数据？", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const res = await getPage(1, 100000, this.query);
        this.$Export.excel({
          title: "菜品明细",
          columns: this.export.column,
          data: res.data.data.records.map((item) => {
            return {
              ...item
            }
          })
        });
      });
    },
    handleImport() {
      this.excelBox = true;
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen(done, type) {
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then((res) => {
          this.form = res.data.data;
        });
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      getPage(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
    uploadAfter(res, done, loading, column) {
      console.log(res, column);
      this.form.img = res.attachId;
      done();
    },
  },
};
</script>

<style></style>

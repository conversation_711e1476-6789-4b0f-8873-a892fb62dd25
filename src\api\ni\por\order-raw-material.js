import request from "@/router/axios";

export const getDetailByProcessInsId = (processInsId) => {
  return request({
    url: "/api/ni/por/order/raw-material/detailByProcessInsId",
    method: "get",
    params: {
      processInsId,
    },
  });
};

export const apply = (row, processDefKey) => {
  return request({
    url: "/api/ni/por/order/raw-material/apply",
    method: "post",
    params: {
      processDefKey,
    },
    data: row,
  });
};

export const getList = (current, size, params) => {
  return request({
    url: "/api/ni/por/order/raw-material/page",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const remove = (ids) => {
  return request({
    url: "/api/ni/por/order/raw-material/remove",
    method: "post",
    params: {
      ids,
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/ni/por/order/raw-material/save",
    method: "post",
    data: row,
  });
};

export const update = (row) => {
  return request({
    url: "/api/ni/por/order/raw-material/update",
    method: "post",
    data: row,
  });
};

export const submit = (ids) => {
  return request({
    url: "/api/ni/por/order/raw-material/submit",
    method: "post",
    params: {
      ids,
    },
  });
};

export const toVoid = (ids) => {
  return request({
    url: "/api/ni/por/order/raw-material/toVoid",
    method: "post",
    params: {
      ids,
    },
  });
};

<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
      :cell-style="cellStyle"
    >
      <template #img="{ row, index }">
        <div v-if="row.img">
          <el-image
            v-for="d of row.img"
            style="width: 70px; margin-right: 20px"
            :src="d"
            :preview-src-list="[d]"
            fit="scale-down"
          ></el-image>
        </div>
      </template>
      <template #menuLeft>
        <el-button
          type="danger"
          size="mini"
          icon="el-icon-delete"
          plain
          v-if="permission.productPallet_delete"
          @click="handleDelete"
          >删 除
        </el-button>
        <el-button
          type="primary"
          size="mini"
          icon="el-icon-download"
          @click="handleExport"
        >
          导出
        </el-button>
      </template>
      <template #menu="{ row, size, index }">
        <el-button
          type="text"
          icon="el-icon-download"
          :size="size"
          @click="rowAttach(row)"
          >附件
        </el-button>
      </template>
    </avue-crud>
    <attach-dialog ref="attachDialogRef" code="public" />
  </basic-container>
</template>

<script>
import {
  getList,
  getDetail,
  add,
  update,
  remove,
} from "@/api/ni/product/pallet";
import { mapGetters } from "vuex";
import AttachDialog from "@/components/attach-dialog/index.vue";
import { fileLinkByBusinessKeys } from "@/api/resource/attach";

export default {
  components: { AttachDialog },
  data() {
    return {
      module: "ni_product_pallet",
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        menuWidth: 300,
        size: "mini",
        searchSize: "mini",
        align: "center",
        searchIndex: 3,
        searchIcon: true,
        searchEnter: true,
        height: "auto",
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        selection: true,
        dialogClickModal: false,
        column: [
          {
            label: "托盘名称",
            prop: "name",
            type: "input",
            search: true,
            overHidden: true,
            row: true,
            minWidth: 110,
            rules: [
              {
                required: true,
                message: "请输入",
                trigger: "blur",
              },
            ],
          },
          {
            label: "托盘编码",
            prop: "code",
            type: "input",
            search: true,
            overHidden: true,
            row: true,
            minWidth: 110,
            rules: [
              {
                required: true,
                message: "请输入",
                trigger: "blur",
              },
            ],
          },
          {
            label: "尺寸(mm)",
            minWidth: 200,
            display: false,
            children: [
              {
                label: "长度",
                prop: "baseLength",
                type: "input",
                span: 8,
              },
              {
                label: "宽度",
                prop: "baseWidth",
                type: "input",
                span: 8,
              },
              {
                label: "高度",
                prop: "baseHeight",
                type: "input",
                span: 8,
              },
            ],
          },
          {
            label: "图片",
            prop: "img",
            display: false,
            minWidth: 150,
          },
          {
            label: "适用纸箱",
            prop: "box",
            overHidden: true,
            span: 24,
            type: "textarea",
            minWidth: 120,
          },
          {
            label: "备注",
            prop: "remark",
            overHidden: true,
            span: 24,
            type: "textarea",
            minWidth: 120,
          },
          {
            label: "状态",
            prop: "status",
            type: "radio",
            value: 1,
            minWidth: 70,
            dicData: [
              {
                label: "启用",
                value: 1,
              },
              {
                label: "停用",
                value: 0,
              },
            ],
          },
        ],
      },
      data: [],
    };
  },
  computed: {
    ...mapGetters(["permission", "colorName"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.productPallet_add, false),
        viewBtn: this.vaildData(this.permission.productPallet_view, false),
        delBtn: this.vaildData(this.permission.productPallet_delete, false),
        editBtn: this.vaildData(this.permission.productPallet_edit, false),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  methods: {
    rowAttach(row) {
      this.$refs.attachDialogRef.init(row.id, this.module);
    },
    rowSave(row, done, loading) {
      add(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          window.console.log(error);
        }
      );
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          console.log(error);
        }
      );
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    async handleExport() {
      let msg =
        "确定将<span style='font-weight: bolder;color: #F56C6C'>选择数据</span>导出?";
      if (this.selectionList.length === 0) {
        msg =
          "确定要将<span style='font-weight: bolder;color: #F56C6C'>全部数据</span>导出?";
      }
      try {
        await this.$alert(msg, {
          dangerouslyUseHTMLString: true,
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        });
        const data = [];
        if (this.selectionList.length > 0) {
          this.selectionList.forEach((item) => {
            data.push({
              ...item,
              status: item.status === 0 ? "停用" : "启用",
            });
          });
        } else {
          const res = await getList(1, 10000, {
            ...this.params,
            ...this.query,
          });
          const data1 = res.data.data.records;
          data1.forEach((item) => {
            data.push({
              ...item,
              status: item.status === 0 ? "停用" : "启用",
            });
          });
        }
        await this.$Export.excel({
          title: "托盘信息导出",
          columns: [
            {
              label: "托盘名称",
              prop: "name",
              width: 130,
            },
            {
              label: "托盘编码",
              prop: "code",
              width: 130,
            },
            {
              label: "长度",
              prop: "baseLength",
            },
            {
              label: "宽度",
              prop: "baseWidth",
            },
            {
              label: "高度",
              prop: "baseHeight",
            },
            {
              label: "适用纸箱",
              prop: "box",
            },
            {
              label: "备注",
              prop: "remark",
            },
            {
              label: "状态",
              prop: "status",
            },
          ],
          data,
        });
      } catch (e) {
        console.log(e);
      }
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen(done, type) {
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then((res) => {
          this.form = res.data.data;
        });
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records.map((item) => {
          return {
            ...item,
            img: [],
          };
        });
        if (this.data.length > 0) {
          const ids = this.data.map((item) => item.id);
          if (ids) this.loadImg(ids.join(","));
        }
        this.loading = false;
        this.selectionClear();
      });
    },
    loadImg(ids) {
      if (!ids) return;
      fileLinkByBusinessKeys(ids, this.module).then((res) => {
        const data = res.data.data;
        const map = data.reduce((acc, cur) => {
          if (!acc[cur.businessKey]) acc[cur.businessKey] = cur.link;
          else acc[cur.businessKey] += "," + cur.link;
          return acc;
        }, {});
        this.data.forEach((item) => {
          item.img = map[item.id].split(",");
        });
        console.log(this.data);
      });
    },
    cellStyle({ row, column }) {
      if ("status" === column.columnKey) {
        if (row.status === 0) {
          return {
            backgroundColor: "#F56C6C",
            color: "#fff",
          };
        } else if (row.status === 1) {
          return {
            backgroundColor: this.colorName,
            color: "#fff",
          };
        }
      }
    },
    openPreview(img, index = 0) {
      this.$ImagePreview(img, index, {
        interval: 3000,
        closeOnClickModal: true,
      });
    },
  },
};
</script>

<style></style>

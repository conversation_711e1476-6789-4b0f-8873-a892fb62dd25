<template>
  <basic-container>
    <vxe-form
      v-if="searchShow"
      :size="size"
      :data="form"
      title-align="right"
      prevent-submit
      custom-layout
      @keydown.enter.native="handleSearch"
      @submit.native.prevent
    >
      <vxe-form-item title="" field="rowNo">
        <template #default="{ data }">
          <vxe-input
            size="mini"
            v-model="form.rowNo"
            placeholder="序号"
            clearable
          ></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="" field="purchaseUserName">
        <template #default="{ data }">
          <vxe-input
            size="mini"
            v-model="form.purchaseUserName"
            placeholder="采购人"
            clearable
          ></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="" field="purchaseTimeM">
        <template #default="{ data }">
          <vxe-input
            size="mini"
            type="date"
            v-model="form.purchaseTimeM"
            placeholder="采购日期"
            clearable
            multiple
          ></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="" field="materialName">
        <template #default="{ data }">
          <vxe-input
            size="mini"
            v-model="form.materialName"
            placeholder="品名"
            clearable
          ></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="" field="specification">
        <template #default="{ data }">
          <vxe-input
            size="mini"
            v-model="form.specification"
            placeholder="规格"
            clearable
          ></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="" field="applyUserName">
        <template #default="{ data }">
          <vxe-input
            size="mini"
            v-model="form.applyUserName"
            placeholder="申购人"
            clearable
          ></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="" field="supplier">
        <template #default="{ data }">
          <vxe-input
            size="mini"
            v-model="form.supplier"
            placeholder="供应商"
            clearable
          ></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="" field="serialNo" folding>
        <template #default="{ data }">
          <vxe-input
            size="mini"
            v-model="form.serialNo"
            placeholder="订单编号"
            clearable
          ></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="" field="arrivalSerialNo" folding>
        <template #default="{ data }">
          <vxe-input
            size="mini"
            v-model="form.arrivalSerialNo"
            placeholder="到货单号"
            clearable
          ></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="" field="remark" folding>
        <template #default="{ data }">
          <vxe-input
            size="mini"
            v-model="form.remark"
            placeholder="备注"
            clearable
          ></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="" field="contractSerialNo" folding>
        <template #default="{ data }">
          <vxe-input
            size="mini"
            v-model="form.contractSerialNo"
            placeholder="合同编号"
            clearable
          ></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="" field="arrivalState" folding>
        <template #default="{ data }">
          <vxe-select
            size="mini"
            v-model="form.arrivalStates"
            placeholder="到货状态"
            clearable
          >
            <vxe-option
              v-for="(item, index) in arrivalStateDict"
              :key="index"
              :value="item.value"
              :label="item.label"
            ></vxe-option>
          </vxe-select>
        </template>
      </vxe-form-item>
      <vxe-form-item title="" field="paymentStatus" folding>
        <template #default="{ data }">
          <vxe-select
            size="mini"
            v-model="form.paymentStatus"
            placeholder="付款状态"
            clearable
          >
            <vxe-option
              v-for="(item, index) in paymentStatusDict"
              :key="index"
              :value="item.value"
              :label="item.label"
            ></vxe-option>
          </vxe-select>
        </template>
      </vxe-form-item>
      <vxe-form-item title="" field="depotState" folding>
        <template #default="{ data }">
          <vxe-select
            size="mini"
            v-model="form.depotState"
            placeholder="入库状态"
            clearable
          >
            <vxe-option
              v-for="(item, index) in depotStateDict"
              :key="index"
              :value="item.value"
              :label="item.label"
            ></vxe-option>
          </vxe-select>
        </template>
      </vxe-form-item>
      <vxe-form-item title="" field="projectSerialNo" folding>
        <template #default="{ data }">
          <vxe-input
            size="mini"
            v-model="form.projectSerialNo"
            placeholder="项目编号"
            clearable
          ></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="" field="budgetSerialNo" folding>
        <template #default="{ data }">
          <vxe-input
            size="mini"
            v-model="form.budgetSerialNo"
            placeholder="预算编号"
            clearable
          ></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="" field="applySerialNo" folding>
        <template #default="{ data }">
          <vxe-input
            size="mini"
            v-model="form.applySerialNo"
            placeholder="采购申请"
            clearable
          ></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="" field="isBill" folding>
        <template #default="{ data }">
          <vxe-select
            size="mini"
            v-model="form.isBill"
            placeholder="是否开票"
            clearable
          >
            <vxe-option
              v-for="(item, index) in isBillDict"
              :key="index"
              :value="item.value"
              :label="item.label"
            ></vxe-option>
          </vxe-select>
        </template>
      </vxe-form-item>
      <vxe-form-item title="" field="billType" folding>
        <template #default="{ data }">
          <vxe-select
            size="mini"
            v-model="form.billType"
            placeholder="发票类型"
            clearable
          >
            <vxe-option
              v-for="(item, index) in billTypeDict"
              :key="index"
              :value="item.value"
              :label="item.label"
            ></vxe-option>
          </vxe-select>
        </template>
      </vxe-form-item>
      <vxe-form-item title="" field="billSerialNo" folding>
        <template #default="{ data }">
          <vxe-input
            size="mini"
            v-model="form.billSerialNo"
            placeholder="关联发票"
            clearable
          ></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="" field="alipayBusinessNo" folding>
        <template #default="{ data }">
          <vxe-input
            size="mini"
            v-model="form.alipayBusinessNo"
            placeholder="支付宝订单号"
            clearable
          ></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="" field="soaSerialNo" folding>
        <template #default="{ data }">
          <vxe-input
            size="mini"
            v-model="form.soaSerialNo"
            placeholder="对账单号"
            clearable
          ></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="" field="receive" folding>
        <template #default="{ data }">
          <vxe-select
            size="mini"
            v-model="form.receive"
            placeholder="收货"
            clearable
          >
            <vxe-option
              v-for="(item, index) in receiveDict"
              :key="index"
              :value="item.value"
              :label="item.label"
            ></vxe-option>
          </vxe-select>
        </template>
      </vxe-form-item>
      <vxe-form-item title="" field="tags" folding>
        <template #default="{ data }">
          <vxe-input
            size="mini"
            v-model="form.tags"
            placeholder="标签"
            clearable
          ></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="" field="num" folding>
        <template #default="{ data }">
          <vxe-input
            size="mini"
            type="number"
            v-model="form.num"
            placeholder="数量："
            clearable
            :controls="false"
          ></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="" field="price" folding>
        <template #default="{ data }">
          <vxe-input
            size="mini"
            type="number"
            v-model="form.price$1"
            placeholder="单价："
            clearable
            :controls="false"
            style="width: 23%"
          ></vxe-input>
          -
          <vxe-input
            size="mini"
            type="number"
            v-model="form.price$2"
            placeholder=" "
            clearable
            :controls="false"
            style="width: 23%"
          ></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="" field="amount" folding>
        <template #default="{ data }">
          <vxe-input
            size="mini"
            type="number"
            v-model="form.amount$1"
            placeholder="金额："
            clearable
            :controls="false"
            style="width: 23%"
          ></vxe-input>
          -
          <vxe-input
            size="mini"
            type="number"
            v-model="form.amount$2"
            placeholder=" "
            clearable
            :controls="false"
            style="width: 23%"
          ></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item align="center" collapse-node>
        <template #default>
          <vxe-button
            size="mini"
            status="primary"
            content="查询"
            :loading="loading"
            @click="handleSearch"
          ></vxe-button>
          <vxe-button
            size="mini"
            content="清空"
            @click="handleClear"
          ></vxe-button>
        </template>
      </vxe-form-item>
    </vxe-form>

    <vxe-toolbar ref="toolbarRef" :size="size" custom>
      <template #buttons>
        <el-dropdown>
          <el-button
            type="success"
            plain
            size="mini"
            @click="handleExport('export')"
          >
            导出<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item @click.native="handleExport('copy')">
              <i class="el-icon-s-ticket" /> 复制
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-dropdown style="margin-left: 5px">
          <el-button type="primary" size="mini">
            批量操作<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item @click.native="handleMerge">
              <i class="el-icon-s-ticket" /> 合并订单
            </el-dropdown-item>
            <el-dropdown-item @click.native="handleBillSerialNo">
              <i class="el-icon-s-ticket" /> 关联发票
            </el-dropdown-item>
            <el-dropdown-item @click.native="handleRemark">
              <i class="el-icon-edit-outline" />修改备注
            </el-dropdown-item>
            <el-dropdown-item @click.native="handleAlipayBusinessNo">
              <i class="el-icon-edit-outline" />关联支付宝订单号
            </el-dropdown-item>
            <el-dropdown-item @click.native="handleManufacturer">
              <i class="el-icon-edit-outline" />修改制造商
            </el-dropdown-item>
            <el-dropdown-item @click.native="handleCancel">
              <i class="el-icon-delete" />批量取消
            </el-dropdown-item>
            <el-dropdown-item @click.native="handleArrival">
              <i class="el-icon-s-ticket" /> 生成到货单
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-button
          type="danger"
          size="mini"
          plain
          @click="handleSplit"
          style="margin-left: 5px"
        >
          拆分
        </el-button>
        <el-button
          type="primary"
          v-if="dataType === '3'"
          icon="el-icon-money"
          size="mini"
          @click="handleAlipay"
        >
          支付宝登记
        </el-button>
        <el-dropdown
          v-if="dataType === '3'"
          @command="handleAlipaySoa"
          style="margin-left: 5px"
        >
          <el-button type="primary" size="mini">
            生成支付宝对账单<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="add">新增</el-dropdown-item>
            <el-dropdown-item command="edit">补充</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-dropdown
          v-if="dataType === '1'"
          @command="handlePorSoa"
          style="margin-left: 5px"
        >
          <el-button type="primary" size="mini">
            采购对账单<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="add">新增</el-dropdown-item>
            <el-dropdown-item command="edit">补充</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-divider direction="vertical" />
        <el-checkbox v-model="pv">压力容器</el-checkbox>
        <el-divider direction="vertical" />
        <el-tag style="margin-bottom: 5px">
          已选择
          <span style="font-weight: bolder">{{ selectionList.length }}</span> 项
          <el-button type="text" size="mini" @click="handleSelectionClear">
            清空
          </el-button>
          <template v-if="selectionList.length > 0">
            选中金额:
            <span style="font-weight: bolder; color: #f56c6c">
              {{
                amount.toLocaleString("zh-CN", {
                  minimumFractionDigits: 2,
                })
              }}
            </span>
          </template>
        </el-tag>
      </template>
      <template #tools>
        <vxe-button
          type="text"
          icon="vxe-icon-search"
          class="tool-btn"
          @click="searchShow = !searchShow"
        ></vxe-button>
        <vxe-button transfer round @dropdown-click="handleSizeChange">
          <template #default>{{ size }}</template>
          <template #dropdowns>
            <vxe-button mode="text" name="mini" content="mini"></vxe-button>
            <vxe-button mode="text" name="small" content="small"></vxe-button>
            <vxe-button mode="text" name="medium" content="medium"></vxe-button>
          </template>
        </vxe-button>
        <vxe-button
          type="text"
          icon="vxe-icon-refresh"
          class="tool-btn"
          @click="onLoad(page)"
        ></vxe-button>
      </template>
    </vxe-toolbar>
    <div style="margin-bottom: 5px">
      <el-radio-group v-model="dataType" size="mini" @input="handleDataType">
        <el-radio-button label="all">全部</el-radio-button>
        <el-radio-button
          v-for="(item, index) in payTypeDict"
          :key="index"
          :label="item.value"
          >{{ item.label }}
        </el-radio-button>
      </el-radio-group>
      <el-divider direction="vertical"></el-divider>
      <el-radio-group
        v-model="dataArrival"
        size="mini"
        @input="handleDataArrival"
      >
        <el-radio-button label="all">全部</el-radio-button>
        <el-radio-button label="unArrival">未到货</el-radio-button>
        <el-radio-button label="unSoa">未对账</el-radio-button>
        <el-radio-button label="soa">已对账</el-radio-button>
        <el-radio-button label="unBill">未关联发票</el-radio-button>
      </el-radio-group>
      <el-divider direction="vertical" />
      <el-radio-group v-model="dataBrand" size="mini" @input="handleDataBrand">
        <el-radio-button label="all">全部</el-radio-button>
        <el-radio-button
          :label="item.dictKey"
          :key="index"
          v-for="(item, index) in brandDict"
          >{{ item.dictValue }}
        </el-radio-button>
      </el-radio-group>
    </div>
    <vxe-table
      :size="size"
      round
      border
      resizable
      stripe
      show-overflow
      ref="crud"
      :loading="loading"
      :data="data"
      :height="tableHeight"
      :mouse-config="{ selected: true }"
      :row-config="{ isCurrent: true, isHover: true, keyField: 'id' }"
      :sort-config="{ trigger: 'cell' }"
      :column-config="{ resizable: true }"
      :checkbox-config="{ checkField: 'checked', trigger: 'row' }"
      :edit-config="editConfig"
      :keyboard-config="{
        isArrow: true,
        isDel: true,
        isEnter: true,
        isTab: true,
        isEdit: true,
      }"
      :scroll-x="{ enabled: true }"
      :scroll-y="{ enabled: true }"
      @checkbox-change="handleSelectionChange"
      @checkbox-all="handleSelectionChange"
      :cell-class-name="cellStyle"
    >
      <vxe-column type="checkbox" width="55" fixed="left"></vxe-column>
      <vxe-column
        title="序号"
        field="rowNo"
        min-width="70"
        :filters="[{ data: '' }]"
        :filter-method="customStringFilterMethod"
      >
        <template #filter="{ $panel, column }">
          <vxe-input
            type="text"
            v-for="(option, index) in column.filters"
            :key="index"
            v-model="option.data"
            @keyup.enter="$panel.confirmFilter()"
            @input="$panel.changeOption($event, !!option.data, option)"
            placeholder=" "
            clearable
          ></vxe-input>
        </template>
        <template #default="{ row, rowIndex }">
          <span
            :style="
              row.backNum && row.backNum > 0
                ? {
                    color: colorName,
                    cursor: 'pointer',
                    textDecoration: 'underline',
                  }
                : {}
            "
            @click="rowBackShow(row)"
          >
            {{ row.rowNo }}
          </span>
          <span v-if="row.receivingAddress && row.receivingAddress !== '1'"
            >({{ receivingAddressDictKeyValue[row.receivingAddress] }})</span
          >
        </template>
      </vxe-column>
      <vxe-column
        title="采购日期"
        field="purchaseTimeStr"
        :filters="[{ data: '' }]"
        :filter-method="customDateFilterMethod"
        width="100"
      >
        <template #filter="{ $panel, column }">
          <vxe-input
            type="date"
            v-for="(option, index) in column.filters"
            :key="index"
            v-model="option.data"
            @keyup.enter="$panel.confirmFilter()"
            @change="$panel.changeOption($event, !!option.data, option)"
            placeholder="请选择"
            clearable
          ></vxe-input>
        </template>
      </vxe-column>
      <vxe-column
        title="采购人"
        field="purchaseUserName"
        min-width="85"
        :filters="[{ data: '' }]"
        :filter-method="customStringFilterMethod"
      >
        <template #filter="{ $panel, column }">
          <vxe-input
            type="text"
            v-for="(option, index) in column.filters"
            :key="index"
            v-model="option.data"
            @keyup.enter="$panel.confirmFilter()"
            @input="$panel.changeOption($event, !!option.data, option)"
            placeholder=" "
            clearable
          ></vxe-input>
        </template>
      </vxe-column>
      <vxe-column
        title="需用日期"
        field="needDate"
        min-width="100"
        :filters="[{ data: '' }]"
        :filter-method="customDateFilterMethod"
      >
        <template #filter="{ $panel, column }">
          <vxe-input
            type="date"
            v-for="(option, index) in column.filters"
            :key="index"
            v-model="option.data"
            @keyup.enter="$panel.confirmFilter()"
            @change="$panel.changeOption($event, !!option.data, option)"
            placeholder="请选择"
            clearable
          ></vxe-input>
        </template>
      </vxe-column>
      <vxe-column
        title="品名"
        field="materialName"
        min-width="125"
        :filters="[{ data: '' }]"
        :filter-method="customStringFilterMethod"
      >
        <template #default="{ row, rowIndex }">
          <i
            style="cursor: pointer"
            class="el-icon-copy-document"
            @click="copyData(row)"
          />
          <span @click="showHistoryDialog(row)">{{ row.materialName }}</span>
        </template>
        <template #filter="{ $panel, column }">
          <vxe-textarea
            :autosize="{ minRows: 2, maxRows: 4 }"
            v-for="(option, index) in column.filters"
            :key="index"
            v-model="option.data"
            @keyup.enter="$panel.confirmFilter()"
            @change="$panel.changeOption($event, !!option.data, option)"
            placeholder=" "
            clearable
          ></vxe-textarea>
        </template>
      </vxe-column>
      <vxe-column
        title="规格"
        field="specification"
        min-width="115"
        :filters="[{ data: '' }]"
        :filter-method="customStringFilterMethod"
      >
        <template #filter="{ $panel, column }">
          <vxe-textarea
            :autosize="{ minRows: 2, maxRows: 4 }"
            v-for="(option, index) in column.filters"
            :key="index"
            v-model="option.data"
            @keyup.enter="$panel.confirmFilter()"
            @change="$panel.changeOption($event, !!option.data, option)"
            placeholder=" "
            clearable
          ></vxe-textarea>
        </template>
      </vxe-column>
      <vxe-column
        title="材质"
        field="quality"
        min-width="110"
        :filters="[{ data: '' }]"
        :filter-method="customStringFilterMethod"
      >
        <template #filter="{ $panel, column }">
          <vxe-textarea
            :autosize="{ minRows: 2, maxRows: 4 }"
            v-for="(option, index) in column.filters"
            :key="index"
            v-model="option.data"
            @keyup.enter="$panel.confirmFilter()"
            @change="$panel.changeOption($event, !!option.data, option)"
            placeholder=" "
            clearable
          ></vxe-textarea>
        </template>
      </vxe-column>
      <vxe-column field="unitStr" title="单位" min-width="65"></vxe-column>
      <vxe-column
        title="申请备注"
        field="applyRemark"
        min-width="105"
        :filters="[{ data: '' }]"
        :filter-method="customStringFilterMethod"
      >
        <template #filter="{ $panel, column }">
          <vxe-textarea
            :autosize="{ minRows: 2, maxRows: 4 }"
            v-for="(option, index) in column.filters"
            :key="index"
            v-model="option.data"
            @keyup.enter="$panel.confirmFilter()"
            @change="$panel.changeOption($event, !!option.data, option)"
            placeholder=" "
            clearable
          ></vxe-textarea>
        </template>
      </vxe-column>
      <vxe-column
        field="applyAttachNum"
        title="申请附件"
        show-overflow
        width="80"
      >
        <template #default="{ row, rowIndex }">
          <span
            v-if="row.applyAttachNum && row.applyAttachNum > 0"
            :style="{ cursor: 'pointer', color: colorName }"
            @click="rowAttach(row)"
          >
            附件({{ row.applyAttachNum }})
          </span>
        </template>
      </vxe-column>
      <vxe-column
        title="数量"
        size="mini"
        field="num"
        min-width="95"
        :filters="[{ data: '' }]"
        :filter-method="customStringFilterMethod"
        :edit-render="{ autofocus: '.vxe-input--inner' }"
      >
        <template #edit="scope">
          <vxe-input
            v-model="scope.row.num"
            type="number"
            size="mini"
            :controls="false"
            placeholder=" "
            :disabled="
              (['2', '4'].includes(scope.row.payType) &&
                !(
                  //预付款和合同预付允许采购主管修改
                  (
                    userInfo.role_name.includes('manager') &&
                    userInfo.role_name.includes('pa')
                  )
                )) ||
              (['1'].includes(scope.row.payType) &&
                scope.row.paymentStatus > 0) ||
              (['1', '3'].includes(scope.row.payType) && scope.row.soa === 1)
            "
            @blur="rowNumChangeSubmit($event, scope.row)"
          ></vxe-input>
        </template>
        <template #filter="{ $panel, column }">
          <vxe-input
            size="small"
            type="number"
            :controls="false"
            v-for="(option, index) in column.filters"
            :key="index"
            v-model="option.data"
            @keyup.enter="$panel.confirmFilter()"
            @change="$panel.changeOption($event, !!option.data, option)"
            placeholder=" "
          ></vxe-input>
        </template>
        <template #default="{ row }">
          <span
            :style="
              !(
                (['1', '4'].includes(row.payType) && row.paymentStatus > 0) ||
                (['1', '3'].includes(row.payType) && row.soa === 1)
              )
                ? {
                    fontWeight: 'bold',
                    color: colorName,
                  }
                : {}
            "
          >
            {{ row.numStr }}
          </span>
          <span
            v-if="row.backNum && Number(row.backNum) > 0"
            style="color: red"
          >
            ({{ "-" + row.backNumStr }})
          </span>
        </template>
      </vxe-column>

      <vxe-column
        field="price"
        title="单价"
        min-width="95"
        :filters="[
          { label: '不是0元', value: 1, checked: false },
          { label: '0元或未填', value: 2, checked: false },
        ]"
        :filter-method="
          ({ option, row }) => {
            if (option.value === 1) {
              return Number(row.price) !== 0;
            } else if (option.value === 2) {
              return Number(row.price) === 0 || !row.price;
            }
          }
        "
        :edit-render="{ autofocus: '.vxe-input--inner' }"
      >
        <template #edit="scope">
          <vxe-input
            v-model="scope.row.price"
            type="number"
            size="mini"
            placeholder=" "
            :controls="false"
            :disabled="
              (['2', '4'].includes(scope.row.payType) &&
                !(
                  //预付款和合同预付允许采购主管修改
                  (
                    userInfo.role_name.includes('manager') &&
                    userInfo.role_name.includes('pa')
                  )
                )) ||
              (['1'].includes(scope.row.payType) &&
                scope.row.paymentStatus > 0) ||
              (['1', '3'].includes(scope.row.payType) && scope.row.soa === 1)
            "
            @blur="rowPriceChangeSubmit($event, scope.row)"
          ></vxe-input>
        </template>
        <template #default="{ row }">
          <span
            :style="
              !(
                (['1', '4'].includes(row.payType) &&
                  row.paymentStatus > 0) ||
                (['1', '3'].includes(row.payType) && row.soa === 1)
              )
                ? {
                    fontWeight: 'bold',
                    color: colorName,
                  }
                : {}
            "
          >
            {{ row.priceStr }}
          </span>
        </template>
      </vxe-column>
      <vxe-column
        field="amount"
        title="金额"
        min-width="95"
        :filters="[{ data: '' }]"
        :filter-method="customStringFilterMethod"
        :edit-render="{ autofocus: '.vxe-input--inner' }"
      >
        <template #edit="scope">
          <vxe-input
            v-model="scope.row.amount"
            type="number"
            size="mini"
            className="amount-input"
            placeholder=" "
            :controls="false"
            :disabled="
              (['2', '4'].includes(scope.row.payType) &&
                !(
                  //预付款和合同预付允许采购主管修改
                  (
                    userInfo.role_name.includes('manager') &&
                    userInfo.role_name.includes('pa')
                  )
                )) ||
              (['1'].includes(scope.row.payType) &&
                scope.row.paymentStatus > 0) ||
              (['1', '3'].includes(scope.row.payType) && scope.row.soa === 1)
            "
            @blur="rowAmountChangeSubmit($event, scope.row)"
          />
        </template>
        <template #filter="{ $panel, column }">
          <vxe-input
            type="text"
            v-for="(option, index) in column.filters"
            :key="index"
            v-model="option.data"
            @keyup.enter="$panel.confirmFilter()"
            @input="$panel.changeOption($event, !!option.data, option)"
            placeholder=" "
            clearable
          ></vxe-input>
        </template>
        <template #default="{ row }">
          <span
            :style="
              !(
                (['1', '4'].includes(row.payType) && row.paymentStatus > 0) ||
                (['1', '3'].includes(row.payType) && row.soa === 1)
              )
                ? {
                    fontWeight: 'bold',
                    color: colorName,
                  }
                : {}
            "
          >
            {{ row.amountStr }}
          </span>
          <span
            v-if="row.backAmount && Number(row.backAmount) > 0"
            style="color: red"
          >
            ({{ "-" + row.backAmountStr }})
          </span>
        </template>
      </vxe-column>
      <vxe-column
        field="remark"
        title="备注"
        min-width="120"
        :filters="[{ data: '' }]"
        :filter-method="customStringFilterMethod"
        :edit-render="{ autofocus: '.vxe-textarea--inner' }"
      >
        <template #edit="scope">
          <vxe-textarea
            v-model="scope.row.remark"
            placeholder=" "
            :autosize="{ minRows: 1 }"
            @blur="rowRemarkChangeSubmit($event, scope.row)"
          />
        </template>
        <template #filter="{ $panel, column }">
          <vxe-input
            type="text"
            v-for="(option, index) in column.filters"
            :key="index"
            v-model="option.data"
            @keyup.enter="$panel.confirmFilter()"
            @input="$panel.changeOption($event, !!option.data, option)"
            placeholder=" "
            clearable
          ></vxe-input>
        </template>
      </vxe-column>
      <vxe-column field="inquiryLength" title="比价" show-overflow width="109">
        <template #default="{ row, rowIndex }">
          <span
            v-if="row.inquiryLength"
            style="color: red; cursor: pointer"
            @click="rowInquiryShow(row, rowIndex)"
            >比价【{{ row.inquiryLength }}】</span
          >
          <span v-else>无比价</span>
        </template>
      </vxe-column>
      <vxe-column
        field="arrivalSerialNo"
        title="到货单号"
        min-width="150"
        :filters="[{ data: '' }]"
        :filter-method="customStringFilterMethod"
        sortable
        :fixed="arrivalSerialNoFixed"
      >
        <template #filter="{ $panel, column }">
          <vxe-input
            type="text"
            v-for="(option, index) in column.filters"
            :key="index"
            v-model="option.data"
            @keyup.enter="$panel.confirmFilter()"
            @input="$panel.changeOption($event, !!option.data, option)"
            placeholder=" "
            clearable
          ></vxe-input>
        </template>
      </vxe-column>
      <vxe-column
        field="supplier"
        title="供应商"
        min-width="145"
        :filters="[{ data: '' }]"
        :filter-method="customStringFilterMethod"
      >
        <template #default="{ row, rowIndex }">
          <span
            v-if="
              row.paymentStatus > 0 ||
              (row.depotState && ['1', '2'].includes(row.depotState)) ||
              (row.arrivalState && ['1', '2'].includes(row.arrivalState))
            "
            >{{ row.supplier || "-" }}
          </span>
          <vxe-button
            v-else
            type="text"
            size="medium"
            @click="rowSupplierChange(row)"
          >
            {{ row.supplier }}
          </vxe-button>
        </template>
        <template #filter="{ $panel, column }">
          <vxe-input
            type="text"
            v-for="(option, index) in column.filters"
            :key="index"
            v-model="option.data"
            @keyup.enter="$panel.confirmFilter()"
            @input="$panel.changeOption($event, !!option.data, option)"
            placeholder=" "
            clearable
          ></vxe-input>
        </template>
      </vxe-column>
      <vxe-column
        field="orderAmountStr"
        title="合计金额"
        min-width="85"
      ></vxe-column>
      <vxe-column
        field="payType"
        title="付款方式"
        min-width="115"
        :formatter="({ cellValue }) => payTypeDictKeyValue[cellValue]"
        :filters="payTypeDict"
        :edit-render="{}"
      >
        <template #edit="{ row }">
          <vxe-select
            size="mini"
            v-model="row.payType"
            @change="rowPayType($event, row)"
            :disabled="
              (row.soa != null && row.soa > 0) ||
              (row.paymentStatus > 0)
            "
          >
            <vxe-option
              v-for="(item, index) in payTypeDict"
              :key="index"
              :value="item.value"
              :label="item.label"
            ></vxe-option>
          </vxe-select>
        </template>
      </vxe-column>
      <vxe-column
        field="contractSerialNo"
        title="合同编号"
        min-width="105"
        :filters="[{ data: '' }]"
        :filter-method="customStringFilterMethod"
      >
        <template #filter="{ $panel, column }">
          <vxe-input
            type="text"
            v-for="(option, index) in column.filters"
            :key="index"
            v-model="option.data"
            @keyup.enter="$panel.confirmFilter()"
            @input="$panel.changeOption($event, !!option.data, option)"
            placeholder=" "
            clearable
          ></vxe-input>
        </template>
        <template #default="{ row }">
          <span
            v-if="!row.contractSerialNo"
            class="el-dropdown-link"
            style="font-size: 11px; color: #909399"
            @click="handleGenerateContractSerialNo(row)"
          >
            生成编号
          </span>
          <span
            v-else-if="row.contractId"
            style="color: #49bb7f; font-weight: bolder; font-size: 11px"
            >{{ row.contractSerialNo }}</span
          >
          <el-popconfirm
            v-else
            confirm-button-text="好的"
            cancel-button-text="不用了"
            icon="el-icon-info"
            icon-color="red"
            title="是否生成合同？"
            @confirm="rowBuildContract(row.orderId)"
          >
            <el-link style="font-size: 11px" target="_blank" slot="reference"
              >{{ row.contractSerialNo }}
            </el-link>
          </el-popconfirm>
        </template>
      </vxe-column>
      <vxe-column
        field="arrivalState"
        title="到货状态"
        min-width="105"
        :formatter="({ cellValue }) => arrivalStateDictKeyValue[cellValue]"
        :filters="arrivalStateDict"
      ></vxe-column>
      <vxe-column
        field="paymentStatus"
        title="付款状态"
        min-width="105"
        :formatter="({ cellValue }) => paymentStatusDictKeyValue[cellValue]"
        :filters="paymentStatusDict"
      ></vxe-column>
      <vxe-column
        field="depotState"
        title="入库状态"
        min-width="105"
        :formatter="({ cellValue }) => depotStateDictKeyValue[cellValue]"
        :filters="depotStateDict"
      ></vxe-column>
      <vxe-column
        field="applySerialNo"
        title="采购申请"
        min-width="110"
        :filters="[{ data: '' }]"
        :filter-method="customStringFilterMethod"
      >
        <template #filter="{ $panel, column }">
          <vxe-input
            type="text"
            v-for="(option, index) in column.filters"
            :key="index"
            v-model="option.data"
            @keyup.enter="$panel.confirmFilter()"
            @input="$panel.changeOption($event, !!option.data, option)"
            placeholder=" "
            clearable
          ></vxe-input>
        </template>
      </vxe-column>
      <vxe-column field="arrivalNum" title="到货数" min-width="95"></vxe-column>
      <vxe-column
        field="qualifiedNum"
        title="合格数"
        min-width="95"
      ></vxe-column>
      <vxe-column field="backNum" title="退货数" min-width="95"></vxe-column>
      <vxe-column
        field="depotNum"
        title="入库数量"
        min-width="105"
      ></vxe-column>
      <vxe-column
        field="applyUserName"
        title="申购人"
        min-width="95"
        :filters="[{ data: '' }]"
        :filter-method="customStringFilterMethod"
      >
        <template #filter="{ $panel, column }">
          <vxe-input
            type="text"
            v-for="(option, index) in column.filters"
            :key="index"
            v-model="option.data"
            @keyup.enter="$panel.confirmFilter()"
            @input="$panel.changeOption($event, !!option.data, option)"
            placeholder=" "
            clearable
          ></vxe-input>
        </template>
      </vxe-column>
      <vxe-column
        field="receivingAddress"
        title="到货地"
        min-width="90"
        :formatter="({ cellValue }) => receivingAddressDictKeyValue[cellValue]"
        :filters="receivingAddressDict"
        :edit-render="{ autoFocus: true }"
      >
        <template #edit="{ row }">
          <vxe-select
            size="mini"
            clearable
            transfer
            v-model="row.receivingAddress"
            @change="rowReceivingAddress($event, row)"
          >
            <vxe-option
              v-for="(item, index) in receivingAddressDict"
              :key="index"
              :value="item.value"
              :label="item.label"
            ></vxe-option>
          </vxe-select>
        </template>
      </vxe-column>
      <vxe-column
        field="projectSerialNo"
        title="项目号"
        min-width="95"
        :filters="[{ data: '' }]"
        :filter-method="customStringFilterMethod"
      >
        <template #filter="{ $panel, column }">
          <vxe-input
            type="text"
            v-for="(option, index) in column.filters"
            :key="index"
            v-model="option.data"
            @keyup.enter="$panel.confirmFilter()"
            @input="$panel.changeOption($event, !!option.data, option)"
            placeholder=" "
            clearable
          ></vxe-input>
        </template>
      </vxe-column>
      <vxe-column
        field="budgetSerialNo"
        title="预算编号"
        min-width="97"
        :filters="[{ data: '' }]"
        :filter-method="customStringFilterMethod"
      >
        <template #filter="{ $panel, column }">
          <vxe-input
            type="text"
            v-for="(option, index) in column.filters"
            :key="index"
            v-model="option.data"
            @keyup.enter="$panel.confirmFilter()"
            @input="$panel.changeOption($event, !!option.data, option)"
            placeholder=" "
            clearable
          ></vxe-input>
        </template>
      </vxe-column>
      <vxe-column
        field="serialNo"
        title="订单编号"
        min-width="110"
        :filters="[{ data: '' }]"
        :filter-method="customStringFilterMethod"
      >
        <template #filter="{ $panel, column }">
          <vxe-input
            type="text"
            v-for="(option, index) in column.filters"
            :key="index"
            v-model="option.data"
            @keyup.enter="$panel.confirmFilter()"
            @input="$panel.changeOption($event, !!option.data, option)"
            placeholder=" "
            clearable
          ></vxe-input>
        </template>
      </vxe-column>
      <vxe-column
        field="pv"
        title="压力容器"
        show-overflow="title"
        width="97"
        :formatter="({ cellValue }) => (cellValue ? '是' : '否')"
        :edit-render="{}"
      >
        <template #edit="{ row }">
          <vxe-select size="mini" v-model="row.pv" @change="rowPv($event, row)">
            <vxe-option :value="1" label="是"></vxe-option>
            <vxe-option :value="0" label="否"></vxe-option>
          </vxe-select>
        </template>
      </vxe-column>
      <vxe-column
        field="fa"
        title="固定资产"
        show-overflow="title"
        width="97"
        :formatter="({ cellValue }) => (cellValue ? '是' : '否')"
        :edit-render="{}"
      >
        <template #edit="{ row }">
          <vxe-select size="mini" v-model="row.fa" @change="rowFa($event, row)">
            <vxe-option :value="1" label="是"></vxe-option>
            <vxe-option :value="0" label="否"></vxe-option>
          </vxe-select>
        </template>
      </vxe-column>
      <vxe-column
        field="manufacturer"
        title="制造商"
        show-overflow="title"
        width="120"
        :filters="[{ data: '' }]"
        :filter-method="customStringFilterMethod"
        :edit-render="{ autofocus: '.vxe-textarea--inner' }"
      >
        <template #edit="scope">
          <vxe-textarea
            v-model="scope.row.manufacturer"
            placeholder=" "
            :autosize="{ minRows: 1 }"
            @blur="rowManufacturerChangeSubmit($event, scope.row)"
          />
        </template>
        <template #filter="{ $panel, column }">
          <vxe-input
            type="text"
            v-for="(option, index) in column.filters"
            :key="index"
            v-model="option.data"
            @keyup.enter="$panel.confirmFilter()"
            @input="$panel.changeOption($event, !!option.data, option)"
            placeholder=" "
            clearable
          ></vxe-input>
        </template>
      </vxe-column>
      <vxe-column
        field="isBill"
        title="是否开票"
        min-width="115"
        :formatter="({ cellValue }) => isBillDictKeyValue[cellValue]"
        :filters="isBillDict"
        :fixed="billTypeNoFixed"
        :edit-render="{}"
      >
        <template #edit="{ row }">
          <vxe-select
            size="mini"
            v-model="row.isBill"
            @change="rowIsBill($event, row)"
          >
            <vxe-option
              v-for="(item, index) in isBillDict"
              :key="index"
              :value="item.value"
              :label="item.label"
            ></vxe-option>
          </vxe-select>
        </template>
      </vxe-column>
      <vxe-column
        field="billType"
        title="发票类型"
        min-width="115"
        :formatter="
          ({ cellValue }) => {
            const item = this.billTypeDict.find(
              (item) => item.value === cellValue
            );
            return item ? item.label : cellValue;
          }
        "
        :filters="billTypeDict"
        :fixed="billTypeNoFixed"
        :edit-render="{}"
      >
        <template #edit="{ row }">
          <vxe-select
            size="mini"
            v-model="row.billType"
            @change="rowBillType($event, row)"
          >
            <vxe-option
              v-for="(item, index) in billTypeDict"
              :key="index"
              :value="item.value"
              :label="item.label"
            ></vxe-option>
          </vxe-select>
        </template>
      </vxe-column>
      <vxe-column
        field="billSerialNo"
        title="关联发票"
        min-width="115"
        :filters="[{ data: '' }]"
        :filter-method="customStringFilterMethod"
        :edit-render="{ autofocus: '.vxe-textarea--inner' }"
        :fixed="billSerialNoFixed"
      >
        <template #edit="scope">
          <vxe-textarea
            v-model="scope.row.billSerialNo"
            placeholder=" "
            :autosize="{ minRows: 1 }"
            @blur="rowBillSerialNoChangeSubmit($event, scope.row)"
          />
        </template>
        <template #filter="{ $panel, column }">
          <vxe-input
            type="text"
            v-for="(option, index) in column.filters"
            :key="index"
            v-model="option.data"
            @keyup.enter="$panel.confirmFilter()"
            @input="$panel.changeOption($event, !!option.data, option)"
            placeholder=" "
            clearable
          ></vxe-input>
        </template>
      </vxe-column>
      <vxe-column
        field="alipayBusinessNo"
        title="支付宝订单号"
        width="144"
        :filters="[{ data: '' }]"
        :filter-method="customStringFilterMethod"
        :edit-render="{ autofocus: '.vxe-textarea--inner' }"
        :fixed="alipayBusinessNoFixed"
      >
        <template #edit="scope">
          <vxe-textarea
            v-model="scope.row.alipayBusinessNo"
            placeholder=" "
            :autosize="{ minRows: 1 }"
            :disabled="
              (scope.row.paymentStatus > 0) ||
              (scope.row.soa != null &&
                scope.row.soa > 0 &&
                scope.row.soaConfirm)
            "
            @blur="rowAlipayBusinessNoChangeSubmit($event, scope.row)"
          />
        </template>
        <template #filter="{ $panel, column }">
          <vxe-input
            type="text"
            v-for="(option, index) in column.filters"
            :key="index"
            v-model="option.data"
            @keyup.enter="$panel.confirmFilter()"
            @input="$panel.changeOption($event, !!option.data, option)"
            placeholder=" "
            clearable
          ></vxe-input>
        </template>
      </vxe-column>
      <vxe-column
        field="soa"
        title="对账"
        width="85"
        :formatter="({ cellValue }) => soaDictKeyValue[cellValue]"
        :filters="soaDict"
        :fixed="soaFixed"
      ></vxe-column>
      <vxe-column
        field="soaSerialNo"
        title="对账单号"
        width="110"
        :filters="[{ data: '' }]"
        :filter-method="customStringFilterMethod"
      >
        <template #filter="{ $panel, column }">
          <vxe-input
            type="text"
            v-for="(option, index) in column.filters"
            :key="index"
            v-model="option.data"
            @keyup.enter="$panel.confirmFilter()"
            @input="$panel.changeOption($event, !!option.data, option)"
            placeholder=" "
            clearable
          ></vxe-input>
        </template>
      </vxe-column>
      <vxe-column
        field="tags"
        title="标签"
        width="185"
        :filters="[{ data: '' }]"
        :filter-method="customStringFilterMethod"
      >
        <template #filter="{ $panel, column }">
          <vxe-input
            type="text"
            v-for="(option, index) in column.filters"
            :key="index"
            v-model="option.data"
            @keyup.enter="$panel.confirmFilter()"
            @input="$panel.changeOption($event, !!option.data, option)"
            placeholder=" "
            clearable
          ></vxe-input>
        </template>
        <template #default="{ row, rowIndex }">
          <span
            v-if="row.tags"
            style="cursor: pointer"
            @click="rowTagsChange(row)"
            >{{ row.tags }}</span
          >
          <vxe-button v-else mode="text" @click="rowTagsChange(row)">
            设置
          </vxe-button>
        </template>
      </vxe-column>
      <vxe-column
        field="purpose"
        title="用途"
        width="105"
        :filters="[{ data: '' }]"
        :filter-method="customStringFilterMethod"
      >
        <template #filter="{ $panel, column }">
          <vxe-input
            type="text"
            v-for="(option, index) in column.filters"
            :key="index"
            v-model="option.data"
            @keyup.enter="$panel.confirmFilter()"
            @input="$panel.changeOption($event, !!option.data, option)"
            placeholder=" "
            clearable
          ></vxe-input>
        </template>
      </vxe-column>
      <vxe-column
        field="brand"
        title="账套"
        show-overflow
        width="70"
        :formatter="({ cellValue }) => brandDictKeyValue[cellValue]"
        :filters="brandDict"
        :filter-method="
          ({ option, row }) => (row.brand ? row.brand : '1') === option.value
        "
      >
      </vxe-column>
      <vxe-column
        field="receive"
        title="收货"
        width="85"
        :formatter="({ cellValue }) => receiveDictKeyValue[cellValue]"
        :filters="receiveDict"
      ></vxe-column>
      <vxe-column
        field="depotLocation"
        title="暂存位置"
        width="115"
        :filters="[{ data: '' }]"
        :filter-method="customStringFilterMethod"
      >
        <template #filter="{ $panel, column }">
          <vxe-input
            type="text"
            v-for="(option, index) in column.filters"
            :key="index"
            v-model="option.data"
            @keyup.enter="$panel.confirmFilter()"
            @input="$panel.changeOption($event, !!option.data, option)"
            placeholder=" "
            clearable
          ></vxe-input>
        </template>
      </vxe-column>
      <vxe-column
        field="receiveRemark"
        title="收货描述"
        width="115"
        :filters="[{ data: '' }]"
        :filter-method="customStringFilterMethod"
      >
        <template #filter="{ $panel, column }">
          <vxe-input
            type="text"
            v-for="(option, index) in column.filters"
            :key="index"
            v-model="option.data"
            @keyup.enter="$panel.confirmFilter()"
            @input="$panel.changeOption($event, !!option.data, option)"
            placeholder=" "
            clearable
          ></vxe-input>
        </template>
      </vxe-column>
    </vxe-table>
    <vxe-pager
      size="mini"
      :loading="loading"
      :current-page="page.currentPage"
      :page-size="page.pageSize"
      :page-sizes="page.pageSizes"
      :total="page.total"
      :layouts="[
        'PrevPage',
        'JumpNumber',
        'NextPage',
        'FullJump',
        'Sizes',
        'Total',
      ]"
      @page-change="currentChange"
    >
    </vxe-pager>
    <vxe-modal v-model="split.visible" :mask="false">
      <template #title>
        <span style="color: red">拆分明细</span>
      </template>
      <template #default>
        <avue-form
          v-if="split.visible"
          :option="split.option"
          v-model="split.form"
          @submit="handleSplitSubmit"
        >
        </avue-form>
      </template>
    </vxe-modal>
    <change-tags-dialog ref="changeTagRef" @submit="handleTagsSubmit" />
    <contract-select-dialog
      :params="{ contractState: '2', bbType: 'supplier' }"
      ref="contractSelectRef"
      @submit="handleContractSubmit"
    />
    <change-supplier-dialog
      ref="changeSupplierDialogRef"
      @submit="searchReset()"
    />
    <order-arrival-dialog ref="orderArrivalDialogRef" @submit="searchReset()" />
    <order-item-history-dialog ref="orderItemHistoryRef" />
    <fin-pay-soa-form
      ref="paySoaRef"
      v-model="soaForm"
      type="2"
      @submit="refreshChange"
    />
    <pay-soa-select-dialog
      ref="paySoaSelectRef"
      :params="{ type: '1', confirm: false, supplierId: supplierId }"
      @confirm="handlePorSoaEditConfirm"
    />
    <pay-soa-select-dialog
      ref="alipaySoaSelectRef"
      :params="{ type: '2', confirm: false }"
      @confirm="handlePorSoaEditConfirm"
    />
    <bill-receipt-select-dialog
      ref="billReceiptRef"
      @confirm="handleLinkBillConfirm"
      :title="linkTitle"
    />
    <order-form-dialog ref="orderFormDialogRef" />
    <order-merge-dialog ref="mergeDialogRef" @merge="mergeOrderSubmit" />
    <change-bill-dialog ref="changeBillDialogRef" @submit="changeBillSubmit" />
    <change-alipay-business-no-dialog
      ref="changeAlipayBusinessNoDialogRef"
      @submit="changeAlipayBusinessNoSubmit"
    />
    <attach-dialog ref="attachRef" code="public" detail :delBtn="false" />
    <purchase-back-item-select-dialog
      ref="purchaseBackItemSelectRef"
      hide-btn
      hide-search
      edit
    />
    <inquiry-show-card ref="inquiryPopRef" />
  </basic-container>
</template>

<script>
import {
  changeAmount,
  changeNum,
  changeRemark,
  getDetail,
  linkContract,
  splitNum,
  splitNumAndOrder,
} from "@/api/ni/por/order";
import {
  cancel,
  changeAlipayBusinessNo,
  changeBillSerialNo1,
  changeBillType,
  changeIsBill,
  changeManufacturer,
  changePayType,
  changePv,
  changeFa,
  getList,
  getPage,
  linkBill,
  mergeOrderItem,
  changeReceivingAddress,
} from "@/api/ni/por/order-item";
import SupplierSelectDialog from "@/views/ni/base/components/SupplierSelectDialog";
import ContractSelect from "@/views/ni/base/components/ContractSelect";
import SupplierMultipleSelect from "@/views/ni/base/components/SupplierSelect";
import ContractPorPayDialog from "@/views/ni/base/components/ContractPorPayDialog";
import ContractSelectDialog from "@/views/ni/base/components/ContractSelectDialog";
import { mapGetters } from "vuex";
import OrderArrivalDialog from "@/views/ni/por/components/OrderArrivalDialog1";
import OrderItemHistoryDialog from "@/views/ni/por/components/OrderItemHistoryDialog";
import NProgress from "nprogress";
import { exportBlob } from "@/api/common";
import printJS from "print-js";
import { numToCapital } from "@/util/util";
import exForm from "@/views/plugin/workflow/mixins/ex-form";
import { dateFormat, dateNow1 } from "@/util/date";
import FinAlipayForm from "@/views/ni/fin/components/FinAlipayForm";
import FinPaySoaForm from "@/views/ni/fin/components/FinPaySoaForm";
import BillReceiptSelectDialog from "@/views/ni/fin/components/BillReceiptSelectDialog";
import PaySoaSelectDialog from "@/views/ni/fin/components/PaySoaSelectDialog";
import { addTo } from "@/api/ni/fin/pay-soa";
import OrderFormDialog from "@/views/ni/por/components/OrderFormDialog";
import PriceChangeCard from "@/views/ni/por/components/price-change-card";
import RemarkChangeCard from "@/views/ni/por/components/remark-change-card";
import InquiryShowCard from "@/views/ni/por/components/inquiry-show-card";
import ArrivalShowCard from "@/views/ni/por/components/arrival-show-card";
import NumChangeCard from "@/views/ni/por/components/num-change-card";
import { getDetail1 } from "@/api/ni/base/supplier/supplierinfo";
import XEUtils from "xe-utils";
import ChangeSupplierDialog from "@/views/ni/por/components/ChangeSupplierDialog";
import ChangeBillDialog from "@/views/ni/por/components/ChangeBillDialog";
import ChangeTagsDialog from "@/views/ni/por/components/ChangeTagsDialog";
import ChangeAlipayBusinessNoDialog from "@/views/ni/por/components/ChangeAlipayBusinessNoDialog";
import AttachDialog from "@/components/attach-dialog";
import { buildContractByOrder } from "@/api/ni/base/contract";
import OrderMergeDialog from "@/views/ni/por/components/OrderMergeDialog";
import PurchaseBackItemSelectDialog from "@/views/ni/por/components/PurchaseBackItemSelectDialog";

export default {
  name: "PorOrderItem",
  mixins: [exForm],
  components: {
    OrderMergeDialog,
    AttachDialog,
    ChangeAlipayBusinessNoDialog,
    ChangeSupplierDialog,
    NumChangeCard,
    PriceChangeCard,
    OrderFormDialog,
    PaySoaSelectDialog,
    BillReceiptSelectDialog,
    FinPaySoaForm,
    FinAlipayForm,
    ContractSelectDialog,
    ContractPorPayDialog,
    SupplierSelectDialog,
    ContractSelect,
    OrderArrivalDialog,
    SupplierMultipleSelect,
    OrderItemHistoryDialog,
    RemarkChangeCard,
    InquiryShowCard,
    ArrivalShowCard,
    ChangeBillDialog,
    ChangeTagsDialog,
    PurchaseBackItemSelectDialog,
  },
  computed: {
    ...mapGetters(["permission", "userInfo", "colorName"]),
    amount() {
      let amount = 0;
      if (this.selectionList && this.selectionList.length > 0) {
        amount = this.selectionList.reduce((acc, cur) => {
          return Number(acc) + Number(cur.amount);
        }, 0);
      }
      return amount;
    },
    ids() {
      let ids = new Set();
      this.selectionList.forEach((ele) => {
        ids.add(ele.id);
      });
      return Array.from(ids).join(",");
    },
  },
  beforeRouteEnter(to, from, next) {
    to.meta.keepAlive = true;
    next();
  },
  data() {
    return {
      size: "mini",
      tableHeight: this.calculateTableHeight(), // 初始化表格高度
      editConfig: {
        trigger: "dblclick",
        mode: "cell",
      },
      form: {
        payType: null,
        arrivalState: null,
        paymentStatus: null,
        depotState: null,
        billType: null,
        receive: null,
      },
      query: {},
      loading: true,
      page: {
        pageSizes: [20, 50, 100, 200, 500, 1000],
        pageSize: 20,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      data: [],
      payTypeDict: [],
      payTypeDictKeyValue: {},
      unitDict: [],
      unitDictKeyValue: {},
      brandDict: [],
      brandDictKeyValue: {},
      billTypeDict: [],
      billTypeDictKeyValue: {},
      isBillDict: [],
      isBillDictKeyValue: {},
      soaDict: [],
      soaDictKeyValue: {},
      receiveDict: [],
      receiveDictKeyValue: {},
      arrivalStateDict: [],
      arrivalStateDictKeyValue: {},
      depotStateDict: [],
      depotStateDictKeyValue: {},
      receivingAddressDict: [],
      receivingAddressDictKeyValue: {},
      paymentStatusDict: [],
      paymentStatusDictKeyValue: {},
      dataType: "all",
      dataBrand: "all",
      dataArrival: "all",
      soaForm: {},
      linkTitle: null,
      exportList: "",
      export: {
        column: [
          {
            label: "采购日期",
            prop: "purchaseTime",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
          },
          {
            label: "采购人",
            prop: "purchaseUserName",
          },
          {
            label: "供应商",
            prop: "supplier",
          },
          {
            label: "需用日期",
            prop: "needDate",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
          },
          {
            label: "序号",
            prop: "row",
          },
          {
            label: "品名",
            prop: "materialName",
          },

          {
            label: "规格",
            prop: "specification",
          },
          {
            label: "材质",
            prop: "quality",
          },
          {
            label: "单位",
            prop: "unit",
          },
          {
            label: "数量",
            prop: "num",
          },
          {
            label: "未到数量",
            prop: "unArrivalNum",
          },
          {
            label: "单价",
            prop: "price",
          },
          {
            label: "金额",
            prop: "amount",
          },
          {
            label: "到货状态",
            prop: "arrivalState",
          },
          {
            label: "付款状态",
            prop: "paymentStatus",
          },
          {
            label: "入库状态",
            prop: "depotState",
          },
          {
            label: "采购申请",
            prop: "applySerialNo",
          },
          {
            label: "到货单号",
            prop: "arrivalSerialNo",
          },
          {
            label: "订单编号",
            prop: "serialNo",
          },
          {
            label: "付款方式",
            prop: "payType",
          },
          {
            label: "发票类型",
            prop: "billType",
          },
          {
            label: "税率(%)",
            prop: "taxRate",
          },
          {
            label: "到货数量",
            prop: "arrivalNum",
          },
          {
            label: "到货金额",
            prop: "arrivalAmount",
          },
          {
            label: "申购人",
            prop: "applyUserName",
          },
          {
            label: "所属项目",
            prop: "projectSerialNo",
          },
          {
            label: "预算编号",
            prop: "budgetSerialNo",
          },
          {
            label: "账套",
            prop: "brand",
          },
          {
            label: "支付宝付款单号",
            prop: "alipaySerialNo",
          },
          {
            label: "支付宝订单号",
            prop: "alipayBusinessNo",
          },
          {
            label: "对账",
            prop: "soa",
          },
          {
            label: "发票号",
            prop: "billSerialNo",
          },
          {
            label: "收货地",
            prop: "receivingAddress",
          },
        ],
      },
      arrivalSerialNoFixed: false,
      soaFixed: false,
      alipayBusinessNoFixed: false,
      billSerialNoFixed: false,
      billTypeNoFixed: false,
      split: {
        visible: false,
        form: {},
        option: {
          span: 24,
          labelWidth: 130,
          size: "mini",
          emptyBtn: false,
          column: [
            {
              label: "拆分数量",
              prop: "num",
              type: "number",
              placeholder: " ",
              rules: [
                {
                  required: true,
                  message: "请输入拆分数量",
                  trigger: "blur",
                },
              ],
            },
            {
              label: "生成新订单",
              prop: "order",
              type: "radio",
              dicData: [
                {
                  label: "是",
                  value: 1,
                },
                {
                  label: "否",
                  value: 0,
                },
              ],
              placeholder: " ",
              rules: [
                {
                  required: true,
                  message: "请选择是否生成新订单",
                  trigger: "blur",
                },
              ],
            },
          ],
        },
      },
      bill: {
        visible: false,
        form: {},
        option: {
          span: 24,
          labelWidth: 130,
          size: "mini",
          emptyBtn: false,
          column: [
            {
              label: "发票类型",
              prop: "billType",
              type: "radio",
              dicData: [],
            },
            {
              label: "发票号码",
              prop: "billSerialNo",
              type: "textarea",
              minRows: 1,
            },
          ],
        },
      },
      pv: false,
      searchShow: true,
    };
  },
  watch: {
    pv: {
      handler(val) {
        if (val) this.form.pv = 1;
        else this.form.pv = null;
        this.page.currentPage = 1;
        this.onLoad(this.page);
      },
      immediate: false,
    },
  },
  created() {
    this.$nextTick(() => {
      // 将表格和工具栏进行关联
      const $table = this.$refs.crud;
      const $toolbar = this.$refs.toolbarRef;
      if ($table && $toolbar) {
        $table.connect($toolbar);
      }
    });
    this.dictInit();
    this.onLoad(this.page);
  },
  mounted() {
    window.addEventListener("resize", this.handleResize);
  },
  destroyed() {
    window.removeEventListener("resize", this.handleResize);
  },
  methods: {
    handleSizeChange(val) {
      this.size = val.name;
    },
    rowBackShow(row) {
      if (row.backNum && row.backNum > 0)
        this.$refs.purchaseBackItemSelectRef.onShow({ orderItemId: row.id });
    },
    rowAttach(row) {
      this.$refs.attachRef.init(row.applyItemId, "ni_por_apply_item");
    },
    dictInit() {
      this.$http
        .get("/api/blade-system/dict-biz/dictionary?code=ni_fin_payment_status")
        .then((res) => {
          this.paymentStatusDict = res.data.data.map((item) => {
            return {
              label: item.dictValue,
              value: item.dictKey,
            };
          });
          this.paymentStatusDictKeyValue = res.data.data.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });
      this.$http
        .get(
          "/api/blade-system/dict-biz/dictionary?code=ni_por_receiving_address"
        )
        .then((res) => {
          this.receivingAddressDict = res.data.data.map((item) => {
            return {
              label: item.dictValue,
              value: item.dictKey,
            };
          });
          this.receivingAddressDictKeyValue = res.data.data.reduce(
            (acc, cur) => {
              acc[cur.dictKey] = cur.dictValue;
              return acc;
            },
            {}
          );
        });
      this.receiveDict = [
        {
          label: "是",
          value: true,
        },
        {
          label: "否",
          value: false,
        },
      ];
      this.receiveDictKeyValue = this.receiveDict.reduce((acc, cur) => {
        acc[cur.value] = cur.label;
        return acc;
      }, {});
      this.soaDict = [
        {
          label: "是",
          value: 1,
        },
        {
          label: "否",
          value: 0,
        },
      ];
      this.soaDictKeyValue = this.soaDict.reduce((acc, cur) => {
        acc[cur.value] = cur.label;
        return acc;
      }, {});
      this.depotStateDict = [
        {
          label: "未入库",
          value: "0",
        },
        {
          label: "部分入库",
          value: "1",
        },
        {
          label: "已入库",
          value: "2",
        },
      ];
      this.depotStateDictKeyValue = this.depotStateDict.reduce((acc, cur) => {
        acc[cur.value] = cur.label;
        return acc;
      }, {});
      this.arrivalStateDict = [
        {
          label: "未到货",
          value: "0",
        },
        {
          label: "部分到货",
          value: "1",
        },
        {
          label: "已到货",
          value: "2",
        },
      ];
      this.arrivalStateDictKeyValue = this.arrivalStateDict.reduce(
        (acc, cur) => {
          acc[cur.value] = cur.label;
          return acc;
        },
        {}
      );
      this.$http
        .get("/api/blade-system/dict-biz/dictionary?code=ni_por_pay_type")
        .then((res) => {
          this.payTypeDict = res.data.data.map((item) => {
            return {
              label: item.dictValue,
              value: item.dictKey,
            };
          });
          this.payTypeDictKeyValue = this.payTypeDict.reduce((acc, cur) => {
            acc[cur.value] = cur.label;
            return acc;
          }, {});
        });
      this.$http
        .get("/api/blade-system/dict-biz/dictionary?code=ni_por_order_is_bill")
        .then((res) => {
          this.isBillDict = res.data.data.map((item) => {
            return {
              label: item.dictValue,
              value: item.dictKey,
            };
          });
          this.isBillDictKeyValue = this.isBillDict.reduce((acc, cur) => {
            acc[cur.value] = cur.label;
            return acc;
          }, {});
          this.isBillDict.push({
            label: "空白",
            value: "0",
          });
        });
      this.$http
        .get("/api/blade-system/dict-biz/dictionary?code=ni_bill_type")
        .then((res) => {
          const list = res.data.data.map((item) => {
            return {
              label: item.dictValue,
              value: item.dictKey,
            };
          });
          list.push({ label: "空白", value: "-1" });
          this.billTypeDict = list;
          this.billTypeDictKeyValue = res.data.data.reduce((acc, cur) => {
            acc[cur.value] = cur.label;
            return acc;
          }, {});
          const billType = this.findObject(this.bill.option.column, "billType");
          billType.dicData = this.billTypeDict;
        });
      this.$http
        .get("/api/blade-system/dict-biz/dictionary?code=ni_material_unit")
        .then((res) => {
          this.unitDict = res.data.data;
          this.unitDictKeyValue = this.unitDict.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });
      this.$http
        .get("/api/blade-system/dict-biz/dictionary?code=ni_brand")
        .then((res) => {
          this.brandDict = res.data.data;
          this.brandDictKeyValue = this.brandDict.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });
    },
    rowReceivingAddress($event, row) {
      changeReceivingAddress(row.id, $event.value).then(() => {
        row.receivingAddress = $event.value;
        this.$message({
          type: "success",
          message: "操作成功!",
        });
      });
    },
    copyData(row) {
      const header = [
        "采购日期",
        String.fromCharCode(9),
        "采购人",
        String.fromCharCode(9),
        "供应商",
        String.fromCharCode(9),
        "需用日期",
        String.fromCharCode(9),
        "序号",
        String.fromCharCode(9),
        "品名",
        String.fromCharCode(9),
        "规格",
        String.fromCharCode(9),
        "材质",
        String.fromCharCode(9),
        "单位",
        String.fromCharCode(9),
        "数量",
        String.fromCharCode(9),
        "未到数量",
        String.fromCharCode(9),
        "单价",
        String.fromCharCode(9),
        "金额",
        String.fromCharCode(9),
        "到货状态",
        String.fromCharCode(9),
        "付款状态",
        String.fromCharCode(9),
        "入库状态",
        String.fromCharCode(9),
        "采购申请",
        String.fromCharCode(9),
        "到货单号",
        String.fromCharCode(9),
        "订单编号",
        String.fromCharCode(9),
        "付款方式",
        String.fromCharCode(9),
        "发票类型",
        String.fromCharCode(9),
        "到货数量",
        String.fromCharCode(9),
        "到货金额",
        String.fromCharCode(9),
        "申购人",
        String.fromCharCode(9),
        "所属项目",
        String.fromCharCode(9),
        "预算编号",
        String.fromCharCode(9),
        "支付宝订单号",
        String.fromCharCode(9),
        "发票号",
        String.fromCharCode(9),
        "对账单号",
        String.fromCharCode(10),
      ].join(" ");
      const data = [
        row.purchaseTime,
        String.fromCharCode(9),
        row.purchaseUserName,
        String.fromCharCode(9),
        row.supplier,
        String.fromCharCode(9),
        row.needDate,
        String.fromCharCode(9),
        row.row,
        String.fromCharCode(9),
        row.materialName,
        String.fromCharCode(9),
        row.specification,
        String.fromCharCode(9),
        row.quality,
        String.fromCharCode(9),
        this.unitDictKeyValue[row.unit],
        String.fromCharCode(9),
        row.num,
        String.fromCharCode(9),
        row.unArrivalNum,
        String.fromCharCode(9),
        row.price,
        String.fromCharCode(9),
        row.amount,
        String.fromCharCode(9),
        this.arrivalStateDictKeyValue[row.arrivalState],
        String.fromCharCode(9),
        this.paymentStatusDictKeyValue[row.paymentStatus],
        String.fromCharCode(9),
        this.depotStateDictKeyValue[row.depotState],
        String.fromCharCode(9),
        row.applySerialNo,
        String.fromCharCode(9),
        row.arrivalSerialNo,
        String.fromCharCode(9),
        row.serialNo,
        String.fromCharCode(9),
        this.payTypeDictKeyValue[row.payType],
        String.fromCharCode(9),
        this.billTypeDictKeyValue[row.billType],
        String.fromCharCode(9),
        row.arrivalNum,
        String.fromCharCode(9),
        row.arrivalAmount,
        String.fromCharCode(9),
        row.applyUserName,
        String.fromCharCode(9),
        row.projectSerialNo,
        String.fromCharCode(9),
        row.budgetSerialNo,
        String.fromCharCode(9),
        row.alipayBusinessNo,
        String.fromCharCode(9),
        row.billSerialNo,
        String.fromCharCode(9),
        row.soaSerialNo,
        String.fromCharCode(10),
      ].join(" ");
      if (window.clipboardData) {
        window.clipboardData.setData("text", header + data);
      } else {
        (function () {
          document.oncopy = function (e) {
            e.clipboardData.setData("text", header + data);
            e.preventDefault();
            document.oncopy = null;
          };
        })(header + data);
        document.execCommand("Copy");
      }
      this.$message({
        type: "success",
        message: "复制成功!",
      });
    },
    rowInquiryShow(row) {
      this.$refs.inquiryPopRef.onShowByApplyItemId(row.applyItemId);
    },
    customStringFilterMethod({ option, row, column }) {
      if (option.data) {
        return (
          XEUtils.toValueString(row[column.field])
            .toLowerCase()
            .indexOf(XEUtils.toValueString(option.data).toLowerCase()) > -1
        );
      }
      return true;
    },
    customDateFilterMethod({ option, row, column }) {
      if (option.data) {
        return XEUtils.isDateSame(row[column.field], option.data, "yyyy-MM-dd");
      }
      return true;
    },
    handleResize() {
      // 窗口大小变化时重新计算表格高度
      this.tableHeight = this.calculateTableHeight();
    },
    calculateTableHeight() {
      // 根据实际需求计算表格高度
      return window.innerHeight - 316;
    },
    rowPv($event, row) {
      changePv(row.id, $event.value).then(() => {
        this.$message({
          type: "success",
          message: "操作成功!",
        });
      });
    },
    rowFa($event, row) {
      changeFa(row.id, $event.value).then(() => {
        this.$message({
          type: "success",
          message: "操作成功!",
        });
      });
    },
    rowManufacturerChangeSubmit($event, row) {
      if ($event.value)
        changeManufacturer(row.id, $event.value).then(() => {
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    rowBillSerialNoChangeSubmit($event, row) {
      changeBillSerialNo1(row.id, $event.value).then(() => {
        row.billSerialNo = $event.value;
        if (row.billSerialNo) row.isBill = "3";
        this.$message({
          type: "success",
          message: "操作成功!",
        });
      });
    },
    rowAlipayBusinessNoChangeSubmit($event, row) {
      changeAlipayBusinessNo(row.id, $event.value).then(() => {
        this.$message({
          type: "success",
          message: "操作成功!",
        });
      });
    },
    rowNumChangeSubmit($event, row) {
      const amount = (row.price * $event.value).toFixed(2);
      changeNum(row.id, "", $event.value).then(() => {
        row.amount = amount;
        row.numStr = Number($event.value).toLocaleString("zh-CN", {
          minimumFractionDigits: 2,
        });
        row.amountStr = Number(amount).toLocaleString("zh-CN", {
          minimumFractionDigits: 2,
        });
        this.$message({
          type: "success",
          message: "操作成功!",
        });
      });
    },
    async rowPriceChangeSubmit($event, row) {
      try {
        if (["1", "2"].includes(row.depotState)) {
          await this.$confirm(
            "该数据已入库,修改后需手动修改同步用友的数据,是否继续修改?",
            "提示",
            {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
            }
          );
        }
        const amount = (row.num * $event.value).toFixed(2);
        await changeAmount(row.id, "", amount);
        row.price = $event.value;
        row.priceStr = Number($event.value).toLocaleString("zh-CN", {
          minimumFractionDigits: 2,
        });
        row.amount = amount;
        row.amountStr = Number(amount).toLocaleString("zh-CN", {
          minimumFractionDigits: 2,
        });
        this.$message({
          type: "success",
          message: "操作成功!",
        });
      } catch (action) {
        row.price = Number(row.priceStr.replace(/,/g, ""));
      }
    },
    rowRemarkChangeSubmit($event, row) {
      changeRemark(row.id, $event.value).then(() => {
        row.remark = $event.value;
        this.$message({
          type: "success",
          message: "操作成功!",
        });
      });
    },
    async rowAmountChangeSubmit($event, row) {
      try {
        if (["1", "2"].includes(row.depotState)) {
          await this.$confirm(
            "该数据已入库,修改后需手动修改同步用友的数据,是否继续修改?",
            "提示",
            {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
            }
          );
        }
        await changeAmount(row.id, "", $event.value);
        row.price = ($event.value / row.num).toFixed(2);
        row.priceStr = Number(row.price).toLocaleString("zh-CN", {
          minimumFractionDigits: 2,
        });
        row.amount = $event.value;
        row.amountStr = Number(row.amount).toLocaleString("zh-CN", {
          minimumFractionDigits: 2,
        });
        this.$message({
          type: "success",
          message: "操作成功!",
        });
      } catch (action) {
        row.amount = Number(row.amountStr.replace(/,/g, ""));
      }
    },
    async rowBuildContract(orderId) {
      const res = await getDetail(orderId);
      const data = res.data.data;
      const porApplyUsers = data.items
        .filter((item) => item.applyUserId != null)
        .map((item) => item.applyUserId);
      const porApplyUser = Array.from(new Set(porApplyUsers)).join(",");
      const form = {
        name:
          dateFormat(new Date(), "yyyy-MM-dd") +
          "[供应商]" +
          this.data.supplier +
          "采购合同",
        brand: data.brand,
        type: ["1", "12"],
        a: 0,
        b: data.supplierId,
        bName: data.supplier,
        bbPic: data.supplierLinkman,
        bbType: "supplier",
        payType: "2",
        amount: data.amount,
        porOrderAmount: data.amount,
        upperAmount: numToCapital(data.amount),
        orderId: orderId,
        order: data.serialNo,
        contractDate: dateFormat(new Date(), "yyyy-MM-dd"),
        contractState: "2",
        aaPic: this.userInfo.user_id,
        porApplyUser,
        serialNo: this.selectionList[0].contractSerialNo,
        items: data.items,
      };
      //TODO ********** 这个采购订单会报错，在start的json反序列化报错
      this.dynamicRoute(
        {
          processDefKey: "process_base_contract_por",
          formKey: "wf_ex_base/Contract/Por",
          form: encodeURIComponent(
            Buffer.from(JSON.stringify(form)).toString("utf8")
          ),
        },
        "start"
      );
    },
    handleManufacturer() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择要修改的数据");
        return;
      }
      this.$prompt("制造商", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
      }).then(({ value }) => {
        changeManufacturer(this.ids, value).then(() => {
          this.data.forEach((item) => {
            if (this.ids.split(",").includes(item.id)) {
              item.manufacturer = value;
            }
          });
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
      });
    },
    handleRemark() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择要修改的数据");
        return;
      }
      this.$prompt("备注", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
      }).then(({ value }) => {
        changeRemark(this.ids, value).then(() => {
          this.data.forEach((item) => {
            if (this.ids.split(",").includes(item.id)) {
              item.remark = value;
            }
          });
          // this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
      });
    },
    handleAlipay() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择登记的订单");
        return;
      }
      const payType = this.selectionList.some((item) => item.payType !== "3");
      if (payType) {
        this.$message.warning("数据中存在非支付宝订单的数据,请重新选择");
        return;
      }
      this.$prompt("请输入支付宝流水号", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
      }).then(({ value }) => {
        changeAlipayBusinessNo(this.ids, value).then(() => {
          this.data.forEach((item) => {
            if (this.ids.split(",").includes(item.id)) {
              item.alipayBusinessNo = value;
            }
          });
          // this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
      });
    },
    handlePorSoa(value) {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择明细");
        return;
      }
      const payType = this.selectionList.some((item) => item.payType === "3");
      if (payType) {
        this.$message.warning("数据中存在支付宝订单的数据,请重新选择");
        return;
      }
      const supplierIds = new Set();
      this.selectionList.forEach((item) => {
        supplierIds.add(item.supplierId);
      });
      if (supplierIds.size > 1) {
        this.$message.warning("数据中存在不同供应商的数据,请重新选择");
        return;
      }
      const contractPay = this.selectionList.some(
        (item) => item.payType && item.payType === "4"
      );
      if (contractPay) {
        this.$message.warning("数据中存已关联合同的数据,请重新选择");
        return;
      }
      const soa = this.selectionList.some((item) => item.soa);
      if (soa) {
        this.$message.warning("数据中存在已对账的数据,请重新选择");
        return;
      }
      this.soaForm = {
        type: "1",
        brand: this.selectionList[0].brand || "1",
        amount: this.amount,
        supplierId: this.selectionList[0].supplierId,
        backItems: [],
        items: this.selectionList.map((item) => {
          return {
            ...item,
            num: Number(item.num) - Number(item.backNum),
            supplierName: item.supplier,
            orderNum: Number(item.num) - Number(item.backNum),
            cell: false,
          };
        }),
      };
      if (value === "add") this.$refs.paySoaRef.show();
      else if (value === "edit") {
        this.$refs.paySoaSelectRef.visible = true;
      }
    },
    handlePorSoaEditConfirm(selectionList) {
      if (selectionList && selectionList.length === 1) {
        const data = {
          items: this.selectionList,
        };
        addTo(selectionList[0].id, data).then((res) => {
          const data = res.data;
          this.onLoad(this.page);
          this.$message({
            type: data.code === 200 ? "success" : "danger",
            message: data.msg,
          });
        });
      }
    },
    async handleAlipaySoa(value) {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择明细");
        return;
      }
      const payType = this.selectionList.some((item) => item.payType !== "3");
      if (payType) {
        this.$message.warning("数据中存在非支付宝订单的数据,请重新选择");
        return;
      }
      const alipayBusinessNoN = this.selectionList.some(
        (item) => !item.alipayBusinessNo
      );
      if (alipayBusinessNoN) {
        this.$message.warning("数据中存在未添加支付宝业务号的数据,请重新选择");
        return;
      }
      const soa = this.selectionList.some((item) => item.soa);
      if (soa) {
        this.$message.warning("数据中存在已对账的数据,请重新选择");
        return;
      }
      const res = await getDetail1({ code: "050337" });
      const supplierId = res.data.data.id;
      this.soaForm = {
        type: "2",
        brand: this.selectionList[0].brand || "1",
        amount: this.amount,
        supplierId,
        backItems: [],
        items: this.selectionList.map((item) => {
          return {
            ...item,
            supplierName: item.supplier,
            num: Number(item.num) - Number(item.backNum),
            orderNum: Number(item.num) - Number(item.backNum),
            cell: false,
          };
        }),
      };
      if (value === "add") this.$refs.paySoaRef.show();
      else if (value === "edit") {
        this.$refs.alipaySoaSelectRef.visible = true;
      }
    },
    rowPayType($event, row) {
      changePayType(row.id, $event.value).then(() => {
        row.payType = $event.value;
        this.$message({
          type: "success",
          message: "操作成功!",
        });
      });
    },
    rowIsBill($event, row) {
      changeIsBill(row.id, $event.value).then(() => {
        row.isBill = $event.value;
        this.$message({
          type: "success",
          message: "操作成功!",
        });
      });
    },
    rowBillType($event, row) {
      changeBillType(row.id, $event.value).then(() => {
        row.billType = $event.value;
        this.$message({
          type: "success",
          message: "操作成功!",
        });
      });
    },
    cellStyle({ row, column }) {
      if ("purchaseUserName" === column.property && row.receive) {
        return "col-orange";
      }
      if ("rowNo" === column.property && row.yearsAgo) {
        return "col-red";
      }
      if ("applyUserName" === column.property && row.crash) {
        return "col-red";
      }
      if ("alipayBusinessNo" === column.property && row.alipayBillState) {
        return "col-green";
      }
      if ("materialName" === column.property && row.applyType === "21") {
        return "col-yzdyzb";
      }
      return null;
    },
    handleSelectionClear() {
      this.$refs.crud.clearCheckboxRow();
      this.selectionList = [];
    },
    handleDataBrand() {
      this.page.currentPage = 1;
      this.onLoad(this.page);
    },
    handleDataArrival(value) {
      if ("unBill" === value) {
        this.billTypeNoFixed = "right";
        this.billSerialNoFixed = "right";
      } else {
        this.billTypeNoFixed = false;
        this.billSerialNoFixed = false;
      }
      this.page.currentPage = 1;
      this.onLoad(this.page);
    },
    handleDataType(value) {
      if (["1", "3"].includes(value)) {
        this.soaFixed = "right";
        this.arrivalSerialNoFixed = "right";
      } else {
        this.soaFixed = false;
        this.arrivalSerialNoFixed = false;
      }
      if (value === "3") {
        this.alipayBusinessNoFixed = "right";
      } else {
        this.alipayBusinessNoFixed = false;
      }
      this.page.currentPage = 1;
      this.onLoad(this.page);
    },
    handleCancel() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择要取消的数据");
        return;
      }
      this.$confirm("确定将选择数据取消?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return cancel(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    handlePrint() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择要打印的数据");
        return;
      }
      NProgress.start();
      let url = "/api/ni/por/budget/export2Pdf";
      exportBlob(url, { id: this.selectionList[0].id }).then((res) => {
        let data = new Blob([res.data], {
          type: "application/pdf;charset=utf-8",
        });
        let pdfUrl = window.URL.createObjectURL(data);
        printJS({ printable: pdfUrl, type: "pdf" });
        NProgress.done();
      });
    },
    showHistoryDialog(row) {
      this.$refs.orderItemHistoryRef.initByMaterialId(row);
    },
    handleSplitSubmit(form, done) {
      let res;
      if (form.order === 1) {
        res = splitNumAndOrder(form.id, form.num);
      } else res = splitNum(form.id, form.num);
      res
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.split.visible = false;
          this.split.form = {};
        })
        .finally(() => {
          done();
        });
    },
    handleContractSubmit(selectList) {
      if (selectList && selectList.length > 0) {
        const orderIds = new Map();
        this.selectionList.forEach((item) => {
          if (!orderIds.has(item.orderId)) {
            orderIds.set(item.orderId, true);
          }
        });
        linkContract(Array.from(orderIds).join(","), selectList[0].id).then(
          () => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!",
            });
          }
        );
      }
    },
    handleGenerateContractSerialNo(row) {
      this.$confirm("是否生成合同编号？", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        buildContractByOrder(row.orderId).then((res) => {
          const data = res.data.data;
          if (data) {
            row.contractSerialNo = data.serialNo;
            row.contractId = data.id;
          }
          this.$message.success("操作成功");
        });
        // generateContractSerialNo({ id: row.orderId }).then((res) => {
        //   const data = res.data.data;
        //   if (data) {
        //     row.contractSerialNo = data.contractSerialNo;
        //   }
        //   this.$message.success("操作成功");
        // });
      });
    },
    handleBack() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择退货的数据");
        return;
      }
      const brands = new Set();
      const supplierIds = new Set();
      const purchaseUserId = new Set();
      const notAlipayPayType = new Set();
      this.selectionList.forEach((item) => {
        brands.add(item.brand);
        supplierIds.add(item.supplierId);
        purchaseUserId.add(item.purchaseUserId);
        if (item.payType !== "3") notAlipayPayType.add(item.payType);
      });
      if (brands.size > 1) {
        this.$message.warning("请选择同一账套的数据");
        return;
      }
      if (supplierIds.size > 1 && notAlipayPayType.size > 0) {
        this.$message.warning("请选择同一供应商的数据");
        return;
      }
      const form = {
        title: "采购退货单",
        purchaseTime: dateNow1(),
        back: false,
        brand: this.selectionList[0].brand,
        purchaseUserName: this.selectionList[0].purchaseUserName,
        purchaseUserId: this.selectionList[0].purchaseUserId,
        supplierId: this.selectionList[0].supplierId,
        items: this.selectionList.map((item) => {
          return {
            row: item.row,
            applyUserName: item.applyUserName,
            applyUserId: item.applyUserId,
            orderId: item.orderId,
            applyId: item.applyId,
            orderItemId: item.id,
            applyItemId: item.applyItemId,
            orderNum: item.num,
            applyTitle: item.applyTitle,
            applySerialNo: item.applySerialNo,
            materialId: item.materialId,
            materialCode: item.materialCode,
            materialName: item.materialName,
            specification: item.specification,
            quality: item.quality,
            gb: item.gb,
            unit: item.unit,
            finishNum: item.arrivalNum,
            brand: item.brand,
            depotLocation: item.depotLocation,
            receiveRemark: item.receiveRemark,
            amount:
              Number(item.amount) -
              Number(item.arrivalNum) * Number(item.price),
            price: item.price,
            arrivalNum: Number(item.num) - Number(item.arrivalNum),
            pv: item.pv,
          };
        }),
      };
      this.$refs.orderArrivalDialogRef.init(form);
    },
    async handleArrival() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择到货的数据");
        return;
      }
      const noSupplier = this.selectionList.some((item) => !item.supplierId);
      if (noSupplier) {
        this.$message.warning("数据中存在未关联供应商的数据，请重新选择");
        return;
      }
      const arrival = this.selectionList.some(
        (item) => item.arrivalState === "2"
      );
      if (arrival) {
        this.$message.warning("数据中存在已到货数据，请重新选择");
        return;
      }
      const pvs = this.selectionList.some(
        (item) => item.pv && !item.manufacturer
      );
      if (pvs) {
        this.$message.warning(
          "数据中存在压力容器数据，且未选择制造商，请选择制造商后再操作"
        );
        return;
      }
      const supplierIds = new Set();
      const purchaseUserId = new Set();
      const notAlipayPayType = new Set();
      this.selectionList.forEach((item) => {
        supplierIds.add(item.supplierId);
        purchaseUserId.add(item.purchaseUserId);
        if (item.payType !== "3") notAlipayPayType.add(item.payType);
      });
      if (supplierIds.size > 1 && notAlipayPayType.size > 0) {
        this.$message.warning("请选择同一供应商的数据");
        return;
      }
      if (purchaseUserId.size > 1) {
        this.$message.warning("请选择同一采购的数据");
        return;
      }
      let supplierName = this.selectionList[0].supplier;
      let supplierId = this.selectionList[0].supplierId;
      if (notAlipayPayType.size === 0) {
        //如果是支付宝，则取支付宝的供应商  050337
        const res = await getDetail1({ code: "050337" });
        supplierName = res.data.data.name;
        supplierId = res.data.data.id;
      }
      const row = {
        arrivalDate: dateFormat(new Date(), "yyyy-MM-dd"),
        brand: this.selectionList[0].brand,
        purchaseUserName: this.selectionList[0].purchaseUserName,
        purchaseUserId: this.selectionList[0].purchaseUserId,
        supplierId,
        supplierName,
        items: this.selectionList.map((item) => {
          return {
            cost: item.cost,
            row: item.row,
            applyUserName: item.applyUserName,
            applyUserId: item.applyUserId,
            orderId: item.orderId,
            applyId: item.applyId,
            orderItemId: item.id,
            applyItemId: item.applyItemId,
            orderNum: item.num,
            applyTitle: item.applyTitle,
            applySerialNo: item.applySerialNo,
            materialId: item.materialId,
            materialCode: item.materialCode,
            materialName: item.materialName,
            specification: item.specification,
            quality: item.quality,
            gb: item.gb,
            unit: item.unit,
            finishNum: item.arrivalNum,
            brand: item.brand,
            depotLocation: item.depotLocation,
            receiveRemark: item.receiveRemark,
            arrivalAmount:
              Number(item.amount) -
              Number(item.arrivalNum) * Number(item.price),
            price: item.price,
            arrivalNum: Number(item.num) - Number(item.arrivalNum),
            pv: item.pv,
            manufacturer: item.manufacturer,
          };
        }),
      };
      this.$refs.orderArrivalDialogRef.init(row);
    },
    rowTagsChange(row) {
      this.$refs.changeTagRef.init(row);
    },
    handleTagsSubmit(id, tags) {
      for (const item of this.data) {
        if (item.id === id) {
          item.tags = tags.join(",");
          break;
        }
      }
    },
    rowSupplierChange(row) {
      if (row.paymentStatus>0) {
        this.$message.warning("该订单已提交付款，无法修改供应商");
        return;
      }
      if (row.depotState && ["1", "2"].includes(row.depotState)) {
        this.$message.warning("该订单已入库，无法修改供应商");
        return;
      }
      if (row.arrivalState && ["1", "2"].includes(row.arrivalState)) {
        this.$message.warning("该订单已到货，无法修改供应商");
        return;
      }
      this.$refs.changeSupplierDialogRef.init({ id: row.orderId });
    },
    searchReset() {
      this.query = {};
      this.pv = false;
      this.form = {};
      this.onLoad(this.page);
      this.handleSelectionClear();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    currentChange({ currentPage, pageSize }) {
      this.page.currentPage = currentPage;
      this.page.pageSize = pageSize;
      this.onLoad(this.page);
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      const query = {
        ...params,
        ...this.form,
      };
      if (query.needDate && query.needDate.length > 1) {
        query.startNeedDate = query.needDate[0];
        query.endNeedDate = query.needDate[1];
        query.needDate = null;
      }
      if (this.dataType === "all") {
        query.payType = null;
      } else {
        query.payType = this.dataType;
      }
      if (this.dataBrand === "all") {
        query.brand = null;
      } else {
        query.brand = this.dataBrand;
      }
      if (this.dataArrival === "all") {
        query.arrivalState = null;
        query.soa = null;
        query.bill = null;
      } else if (this.dataArrival === "unArrival") {
        query.arrivalState = "0,1";
        query.soa = null;
        query.bill = null;
      } else if (this.dataArrival === "unSoa") {
        query.soa = 0;
        if (["all"].includes(this.dataType)) {
          query.payType = "1,3";
        } else if (this.dataType === "1") {
          query.payType = "1";
        } else if (this.dataType === "3") {
          query.payType = "3";
        } else {
          //只有货到付款才有对账，所以非货到付款的数据都筛掉
          query.payType = "-";
        }
        query.bill = null;
        query.arrivalState = null;
      } else if (this.dataArrival === "soa") {
        query.soa = 1;
        if (["all"].includes(this.dataType)) {
          query.payType = "1,3";
        } else if (this.dataType === "1") {
          query.payType = "1";
        } else if (this.dataType === "3") {
          query.payType = "3";
        } else {
          //只有货到付款才有对账，所以非货到付款的数据都筛掉
          query.payType = "-";
        }
        query.bill = null;
        query.arrivalState = null;
      } else if (this.dataArrival === "unBill") {
        query.bill = false;
        query.soa = null;
        query.arrivalState = null;
      }
      if (query.arrivalStates != null) {
        this.dataArrival = "all";
        query.arrivalState = query.arrivalStates;
      }
      if (query.purchaseTime != null && query.purchaseTime.length === 2) {
        query.startPurchaseTime = query.purchaseTime[0] + " 00:00:00";
        query.endPurchaseTime = query.purchaseTime[1] + " 23:59:59";
        query.purchaseTime = null;
      }
      this.loading = true;
      getPage(page.currentPage, page.pageSize, query)
        .then((res) => {
          const data = res.data.data;
          this.page.total = data.total;
          data.records.forEach((item) => {
            item.checked = false;
            if (!item.tags) item.tags = "";
            item.tagVisible = false;
            item.tagValue = null;
            if (item.no) {
              item.rowNo = item.row + "-" + item.no;
            } else {
              item.rowNo = item.row;
            }
            if (item.purchaseTime) {
              item.purchaseTimeStr = item.purchaseTime.substr(0, 10);
            }
            if (!item.receive) {
              item.receive = false;
            }
            if (item.num) {
              item.numStr = Number(item.num).toLocaleString("zh-CN", {
                minimumFractionDigits: 2,
              });
            }
            if (item.backNum) {
              item.backNumStr = Number(item.backNum).toLocaleString("zh-CN", {
                minimumFractionDigits: 2,
              });
            }
            if (item.price) {
              item.priceStr = Number(item.price).toLocaleString("zh-CN", {
                minimumFractionDigits: 2,
              });
            }
            if (item.amount) {
              item.amountStr = Number(item.amount).toLocaleString("zh-CN", {
                minimumFractionDigits: 2,
              });
            }
            if (item.backAmount) {
              item.backAmountStr = Number(item.backAmount).toLocaleString(
                "zh-CN",
                {
                  minimumFractionDigits: 2,
                }
              );
            }
            if (item.arrivalAmount) {
              item.arrivalAmountStr = Number(item.arrivalAmount).toLocaleString(
                "zh-CN",
                {
                  minimumFractionDigits: 2,
                }
              );
            }
            if (item.applyPrice) {
              item.applyPriceStr = Number(item.applyPrice).toLocaleString(
                "zh-CN",
                {
                  minimumFractionDigits: 2,
                }
              );
            }
            if (item.applyAmount) {
              item.applyAmountStr = Number(item.applyAmount).toLocaleString(
                "zh-CN",
                {
                  minimumFractionDigits: 2,
                }
              );
            }
            if (item.orderAmount) {
              item.orderAmountStr = Number(item.orderAmount).toLocaleString(
                "zh-CN",
                {
                  minimumFractionDigits: 2,
                }
              );
            }
            if (item.unit) {
              item.unitStr = this.unitDictKeyValue[item.unit];
            }
            if (!item.isBill) {
              item.isBill = "0";
            }
            if (!item.billType) {
              item.billType = "-1";
            }
          });
          this.data = data.records;
          this.handleSelectionClear();
        })
        .finally(() => {
          this.loading = false;
        });
    },
    handleSelectionChange() {
      this.selectionList = this.$refs.crud.getCheckboxRecords();
    },
    //数据导出
    async handleExportData(type = "export") {
      this.exportList = "";
      const query = {
        ...this.form,
      };
      if (query.needDate && query.needDate.length > 1) {
        query.startNeedDate = query.needDate[0];
        query.endNeedDate = query.needDate[1];
        query.needDate = null;
      }
      if (this.dataType === "all") {
        query.payType = null;
      } else {
        query.payType = this.dataType;
      }
      if (this.dataBrand === "all") {
        query.brand = null;
      } else {
        query.brand = this.dataBrand;
      }
      if (this.dataArrival === "all") {
        query.arrivalState = null;
        query.soa = null;
        query.bill = null;
      } else if (this.dataArrival === "unArrival") {
        query.arrivalState = "0,1";
        query.soa = null;
        query.bill = null;
      } else if (this.dataArrival === "unSoa") {
        query.soa = 0;
        if (["all"].includes(this.dataType)) {
          query.payType = "1,3";
        } else if (this.dataType === "1") {
          query.payType = "1";
        } else if (this.dataType === "3") {
          query.payType = "3";
        } else {
          //只有货到付款才有对账，所以非货到付款的数据都筛掉
          query.payType = "-";
        }
        query.bill = null;
        query.arrivalState = null;
      } else if (this.dataArrival === "soa") {
        query.soa = 1;
        if (["all"].includes(this.dataType)) {
          query.payType = "1,3";
        } else if (this.dataType === "1") {
          query.payType = "1";
        } else if (this.dataType === "3") {
          query.payType = "3";
        } else {
          //只有货到付款才有对账，所以非货到付款的数据都筛掉
          query.payType = "-";
        }
        query.bill = null;
        query.arrivalState = null;
      } else if (this.dataArrival === "unBill") {
        query.bill = false;
        query.soa = null;
        query.arrivalState = null;
      }
      if (query.arrivalStates != null) {
        this.dataArrival = "all";
        query.arrivalState = query.arrivalStates;
      }
      if (query.purchaseTime != null && query.purchaseTime.length === 2) {
        query.startPurchaseTime = query.purchaseTime[0] + " 00:00:00";
        query.endPurchaseTime = query.purchaseTime[1] + " 23:59:59";
        query.purchaseTime = null;
      }
      let data;
      if (this.selectionList.length > 0) {
        data = this.selectionList;
      } else {
        const res = await getList(query);
        data = res.data.data;
      }
      const items = data.map((item) => {
        let depotState = "未入库";
        if (item.depotState === "2") {
          depotState = "已入库";
        } else if (item.depotState === "1") {
          depotState = "部分入库";
        }
        return {
          ...item,
          depotState,
          paymentStatus: this.paymentStatusDictKeyValue[item.paymentStatus],
          price: Number(item.price) != null ? Number(item.price) : "",
          amount: Number(item.amount) != null ? Number(item.amount) : "",
          arrivalAmount:
            Number(item.arrivalAmount) != null
              ? Number(item.arrivalAmount)
              : "",
          applyPrice:
            Number(item.applyPrice) != null ? Number(item.applyPrice) : "",
          applyAmount:
            Number(item.applyAmount) != null ? Number(item.applyAmount) : "",
          unit: this.unitDictKeyValue[item.unit],
          orderAmountStr: Number(item.orderAmount).toLocaleString("zh-CN", {
            minimumFractionDigits: 2,
          }),
          payType: this.payTypeDictKeyValue[item.payType],
          arrivalState: this.arrivalStateDictKeyValue[item.arrivalState],
          pv: item.pv ? "是" : "否",
          billType: this.billTypeDictKeyValue[item.billType],
          brand: this.brandDictKeyValue[item.brand],
          receive: item.receive ? "是" : "否",
          soa: item.soa ? "是" : "否",
          receivingAddress:
            this.receivingAddressDictKeyValue[item.receivingAddress],
        };
      });
      if (type === "copy") {
        this.exportList =
          "序号\t采购日期\t采购人\t需用日期\t品名\t规格\t材质\t单位\t申请备注\t数量\t单价\t金额\t备注\t到货单号\t供应商\t未到数量\t合计金额\t付款方式\t合同编号\t到货状态\t付款状态\t入库状态\t采购申请\t付款申请\t到货数\t合格数\t退货数\t到货金额\t入库数量\t建议单价\t建议金额\t申购人\t项目号\t预算编号\t订单编号\t压力容器\t制造商\t发票类型\t关联发票\t支付宝单号\t对账\t对账单号\t标签\t用途\t账套\t收货\t暂存位置\t收货描述\t收货地\r\n";
        items.forEach((ele) => {
          let values = [
            this.getField(ele.row),
            this.getField(ele.purchaseTime),
            this.getField(ele.purchaseUserName),
            this.getField(ele.needDate),
            this.getField(ele.materialName),
            this.getField(ele.specification),
            this.getField(ele.quality),
            this.getField(ele.unit),
            this.getField(ele.applyRemark),
            this.getField(ele.num),
            this.getField(ele.price),
            this.getField(ele.amount),
            this.getField(ele.remark),
            this.getField(ele.arrivalSerialNo),
            this.getField(ele.supplier),
            this.getField(ele.unArrivalNum),
            this.getField(ele.orderAmountStr),
            this.getField(ele.payType),
            this.getField(ele.contractSerialNo),
            this.getField(ele.arrivalState),
            this.getField(ele.paymentStatus),
            this.getField(ele.depotState),
            this.getField(ele.applySerialNo),
            this.getField(ele.payApplySerialNo),
            this.getField(ele.arrivalNum),
            this.getField(ele.qualifiedNum),
            this.getField(ele.backNum),
            this.getField(ele.arrivalAmount),
            this.getField(ele.depotNum),
            this.getField(ele.applyPrice),
            this.getField(ele.applyAmount),
            this.getField(ele.applyUserName),
            this.getField(ele.projectSerialNo),
            this.getField(ele.budgetSerialNo),
            this.getField(ele.serialNo),
            this.getField(ele.pv),
            this.getField(ele.manufacturer),
            this.getField(ele.billType),
            this.getField(ele.billSerialNo),
            this.getField(ele.alipayBusinessNo),
            this.getField(ele.soa),
            this.getField(ele.soaSerialNo),
            this.getField(ele.tags),
            this.getField(ele.purpose),
            this.getField(ele.brand),
            this.getField(ele.receive),
            this.getField(ele.depotLocation),
            this.getField(ele.receiveRemark),
            this.getField(ele.receivingAddress),
          ];
          this.exportList += values.join("\t") + "\r\n";
        });
        this.$Clipboard({
          text: this.exportList,
        })
          .then(() => {
            this.$message.success("数据导出完成，请复制到excel中");
          })
          .catch(() => {
            this.$message({
              type: "waning",
              message: "该浏览器不支持自动复制",
            });
          })
          .finally(() => {
            //  操作完成后清空 exportList
            this.exportList = "";
          });
      } else {
        this.$Export.excel({
          title: "订单明细",
          columns: this.export.column,
          data: items,
        });
      }
    },
    getField(value) {
      return value ? `${value}`.replace(/[\r\n\s]+/g, "") : "";
    },
    handleAlipayBusinessNo() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择关联的明细");
        return;
      }
      this.$refs.changeAlipayBusinessNoDialogRef.init(this.ids);
    },
    handleMerge() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择要合并的明细");
        return;
      }
      const orderIds = new Set();
      const contractIds = new Set();
      this.selectionList.forEach((item) => {
        orderIds.add(item.orderId);
        contractIds.add(item.contractId);
      });
      if (orderIds.size === 1) {
        this.$message.warning("选择的明细属于同一订单，无需合并");
        return;
      }
      if (contractIds.size > 0) {
        this.$confirm("选择的订单中存在已生成合同的订单, 是否继续?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(() => {
          this.$refs.mergeDialogRef.onShow(Array.from(orderIds));
        });
        return;
      }
      this.$refs.mergeDialogRef.onShow(Array.from(orderIds));
    },
    handleBillSerialNo() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择关联的明细");
        return;
      }
      this.$refs.changeBillDialogRef.init(this.ids);
      // this.bill.visible = true;
      // this.$prompt("请输入发票号", "提示", {
      //   confirmButtonText: "确定",
      //   cancelButtonText: "取消",
      // }).then(({ value }) => {
      //   changeBillSerialNo(this.ids, value).then(() => {
      //     this.data.forEach((item) => {
      //       if (this.ids.split(",").includes(item.id)) {
      //         item.billSerialNo = value;
      //       }
      //     });
      //     // this.onLoad(this.page);
      //     this.$message({
      //       type: "success",
      //       message: "操作成功!",
      //     });
      //   });
      // });
    },
    changeAlipayBusinessNoSubmit(form) {
      this.data.forEach((item) => {
        if (this.ids.split(",").includes(item.id)) {
          if (form.alipayBusinessNo)
            item.alipayBusinessNo = form.alipayBusinessNo;
        }
      });
    },
    mergeOrderSubmit(order) {
      mergeOrderItem(order.id, this.ids).then(() => {
        const ids = new Set();
        this.selectionList.forEach((item) => ids.add(item.id));
        this.data.forEach((item) => {
          if (ids.has(item.id)) {
            item.orderId = order.id;
            item.serialNo = order.serialNo;
          }
        });
        this.$message({
          type: "success",
          message: "操作成功!",
        });
      });
    },
    changeBillSubmit(form) {
      this.data.forEach((item) => {
        if (this.ids.split(",").includes(item.id)) {
          if (form.billSerialNo) item.billSerialNo = form.billSerialNo;
          if (form.billType) item.billType = form.billType;
          if (form.isBill) item.isBill = form.isBill;
        }
      });
    },
    handleSplit() {
      this.selectionList = this.$refs.crud.getCheckboxRecords();
      console.log(this.selectionList);
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择要拆分的数据");
        return;
      }
      if (this.selectionList.length > 1) {
        this.$message.warning("只能选择一条数据");
        return;
      }
      this.split.form.num = this.selectionList[0].num;
      this.split.form.id = this.selectionList[0].id;
      this.split.form.order = 0;
      this.split.visible = true;
    },
    handleLinkBill() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.linkTitle = `选中金额:${this.amount}`;
      this.$refs.billReceiptRef.visible = true;
    },
    handleLinkBillConfirm(selectionList) {
      if (selectionList) {
        linkBill(this.ids, selectionList.map((item) => item.id).join(",")).then(
          () => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "关联成功",
            });
          }
        );
      }
    },
    handleExport(type = "export") {
      const typeDic = {
        export: "导出",
        copy: "复制",
      };
      let msg = `是否<span style="color: #F56C6C;font-weight: bold">${typeDic[type]}</span>当前筛选的所有数据？`;
      if (this.selectionList.length > 0) {
        msg = `是否<span style="color: #F56C6C;font-weight: bold">${typeDic[type]}</span>当前选中的数据？`;
      }
      this.$confirm(msg, {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        dangerouslyUseHTMLString: true,
        type: "warning",
      }).then(() => {
        this.handleExportData(type);
      });
    },
    handleSearch() {
      this.page.currentPage = 1;
      this.onLoad(this.page);
    },
    handleClear() {
      this.form = {
        payType: null,
        arrivalState: null,
        paymentStatus: null,
        depotState: null,
        billType: null,
        receive: null,
      };
      this.dataType = "all";
      this.dataArrival = "all";
      this.yy = false;
      this.pv = false;
      this.page.currentPage = 1;
      this.onLoad(this.page);
    },
  },
};
</script>

<style lang="scss" scoped>
/deep/ .vxe-table .vxe-body--column.col-red {
  background-color: #f56c6c;
  color: #fff;
}

/deep/ .vxe-table .vxe-body--column.col-orange {
  background-color: #e6a23c;
  color: #fff;
}

/deep/ .vxe-table .vxe-body--column.col-green {
  background-color: #49bb7f;
  color: #fff;
}

/deep/ .vxe-table .vxe-body--column.col-yzdyzb {
  color: #fff;
  background: #f56c6c url(/img/yzdyzb.png) no-repeat left center / contain;
  position: relative;
}

/deep/ .el-tag + .el-tag {
  margin-left: 10px;
}

/deep/ .button-new-tag {
  cursor: pointer;
  margin-left: 10px;
}

/deep/ .input-new-tag {
  width: 80px;
  margin-left: 10px;
  vertical-align: bottom;
}
</style>

<template>
  <div>
    <el-input
      v-model="faHuoBianHao"
      autosize
      :size="size"
      :disabled="disabled"
      suffix-icon="el-icon-search"
      clearable
      @focus="bpFocus"
      @clear="handleClear"
    />
    <xiao-shou-wai-ku-bu-huo-select-dialog
      ref="xiaoShouWaiKuBuHuoSelectDialogRef"
      :multiple="multiple"
      @confirm="handleConfirm"
    />
  </div>
</template>
<script>
import {getDetail} from "@/api/ni/old/xiaoShouWaiKuBuHuo";
import XiaoShouWaiKuBuHuoSelectDialog from "@/views/ni/old/components/XiaoShouWaiKuBuHuoSelectDialog.vue";

export default {
  components: {XiaoShouWaiKuBuHuoSelectDialog,},
  props: {
    value: {
      type: String,
    },
    params: {
      type: Object,
      default: () => {
      },
    },
    size: {
      type: String,
      default: "mini",
    },
    disabled: Boolean,
    multiple: {
      type: <PERSON>olean,
      default: false,
    },
    url: {
      type: String,
      default: "/api/ni/old/xiaoShouWaiKuBuHuo/list",
    },
  },
  watch: {
    value: {
      handler(val) {
        if (val) {
          const checks = String(val).split(",");
          if (checks.length > 0) {
            this.selectionList = []
            checks.forEach(async (c) => {
              let res = await getDetail(c); // 接口查找
              if (res.data.data) {
                this.selectionList.push(res.data.data);
              }
              this.faHuoBianHao = this.names;
            });
          }
        }
        if (!val) {
          this.handleClear();
        }
      },
      immediate: true,
    },
  },
  data() {
    return {
      faHuoBianHao: "",
      selectionList: [],
    };
  },
  computed: {
    ids() {
      let ids = new Set();
      this.selectionList.forEach((ele) => {
        ids.add(ele.id);
      });
      return Array.from(ids).join(",");
    },
    names() {
      let names = new Set();
      this.selectionList.forEach((ele) => {
        names.add(ele.faHuoBianHao);
      });
      return Array.from(names).join(",");
    },
  },
  mounted() {
    this.init();
  },
  methods: {
    handleClear() {
      this.faHuoBianHao = "";
      if (!this.multiple) {
        this.selectionList = [];
        this.$set(this.form, "radio", null);
      } else this.selectionClear();
      this.$emit("input", this.ids);
      this.$emit("clear");
    },
    bpFocus() {
      this.$refs.xiaoShouWaiKuBuHuoSelectDialogRef.onSelect(this.params, this.url)
    },
    init() {
      if (!this.isInit) {
        this.isInit = true;
      }
    },
    handleConfirm(selectionList) {
      if (selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.selectionList = selectionList
      this.$emit("input", this.ids);
      this.$emit("confirm", this.selectionList);
    },
  },
};
</script>
<style lang="scss">
.us-dialog {
  display: flex;
  flex-direction: column;
  margin: 0 !important;
  position: absolute;
  top: 40%;
  left: 50%;
  transform: translate(-50%, -40%);
  max-height: calc(100% - 30px);
  max-width: calc(100% - 30px);

  .el-dialog__body {
    flex: 1;
    overflow: auto;
  }
}
</style>

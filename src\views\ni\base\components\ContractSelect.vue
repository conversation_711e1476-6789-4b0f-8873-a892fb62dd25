<template>
  <div>
    <el-input
      v-model="contractSerialNo"
      :size="size"
      :disabled="disabled"
      suffix-icon="el-icon-search"
      clearable
      @clear="bpClear"
      @focus="bpFocus"
    />
    <el-dialog
      ref="us-dialog"
      v-dialogdrag
      custom-class="us-dialog"
      :visible.sync="visible"
      title="合同选择"
      width="60%"
      :before-close="handleClose"
      append-to-body
    >
      <avue-crud
        v-if="isInit && visible"
        :option="option"
        :table-loading="loading"
        :data="data"
        :page.sync="page"
        v-model="form"
        ref="crud"
        @search-change="searchChange"
        @search-reset="searchReset"
        @selection-change="selectionList = $event"
        @current-change="page.currentPage = $event"
        @size-change="page.pageSize = $event"
        @row-click="rowClick"
        @on-load="onLoad"
      >
        <template #radio="{ row }">
          <el-radio v-model="form.radio" :label="row.id"><i></i></el-radio>
        </template>
        <template #orderAmount="{ row, index }">
          <span
            v-if="row.orderAmount"
            :style="{
              color:
                Number(row.orderAmount) != Number(row.amount) ? '#F56C6C' : '',
            }"
            >{{
              Number(row.orderAmount).toLocaleString("zh-CN", {
                minimumFractionDigits: 2,
              })
            }}</span
          >
        </template>
        <template #amount="{ row, index }">
          <span v-if="row.amount">{{
            Number(row.amount).toLocaleString("zh-CN", {
              minimumFractionDigits: 2,
            })
          }}</span>
        </template>
        <template #payType="{ row, index }">
          <el-tag v-if="row.payType == '1'" size="mini" effect="plain"
            >{{ payTypeDictKeyValue[row.payType] }}
          </el-tag>
          <el-tag
            v-else-if="row.payType == '2'"
            size="mini"
            type="danger"
            effect="plain"
          >
            {{ payTypeDictKeyValue[row.payType] }}
          </el-tag>
          <el-tag v-else size="mini" type="info" effect="plain"
            >{{ payTypeDictKeyValue[row.payType] }}
          </el-tag>
        </template>
        <template #template="{ row, index }">
          <el-tag v-if="row.template" size="mini" type="info" effect="plain"
            >是
          </el-tag>
          <el-tag v-else size="mini" type="danger" effect="dark">否</el-tag>
        </template>
        <template #status="{ row, index }">
          <el-tag v-if="row.status === 0" size="mini" type="info">
            {{ row.$status }}
          </el-tag>
          <el-tag v-else-if="row.status === 1" size="mini" effect="plain">
            {{ row.$status }}
          </el-tag>
          <el-tag v-else-if="row.status === 2" size="mini" effect="plain">
            {{ row.$status }}
          </el-tag>
          <el-tag v-else-if="row.status === 3" size="mini" type="danger">
            {{ row.$status }}
          </el-tag>
          <el-tag
            v-else-if="row.status === 4"
            size="mini"
            type="warning"
            effect="plain"
          >
            {{ row.$status }}
          </el-tag>
          <el-tag
            v-else-if="row.status === 5"
            size="mini"
            type="warning"
            effect="plain"
          >
            {{ row.$status }}
          </el-tag>
          <el-tag
            v-else-if="row.status === 6"
            size="mini"
            type="danger"
            effect="plain"
          >
            {{ row.$status }}
          </el-tag>
          <el-tag
            v-else-if="row.status === 9"
            size="mini"
            type="success"
            effect="plain"
          >
            {{ row.$status }}
          </el-tag>
        </template>
        <template #brand="{ row, disabled, size, index }">
          <el-tag
            v-if="row.brand"
            :size="size"
            :effect="row.brand === '1' ? 'dark ' : 'plain'"
          >
            {{ brandDictKeyValue[row.brand] }}
          </el-tag>
        </template>
        <template #type="{ row, index }">
          <el-tag
            v-for="(type, index) in row.type.split(',')"
            :key="index"
            size="mini"
            effect="plain"
          >
            {{ typeDictKeyValue[type] }}
          </el-tag>
        </template>
        <template #aname="{ row, index }">
          <span v-if="row.a === 0">山东能特异能源科技有限公司</span>
          <span v-else style="color: #167c46">{{ row.aname }}</span>
        </template>
        <template #bname="{ row, index }">
          <span v-if="row.b === 0">山东能特异能源科技有限公司</span>
          <span v-else style="color: #167c46">{{ row.bname }}</span>
        </template>
      </avue-crud>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose" size="mini">取 消</el-button>
        <el-button type="primary" @click="handleConfirm" size="mini"
          >确 定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { getDetail, getPage } from "@/api/ni/base/contract";
import Emitter from "element-ui/src/mixins/emitter";

export default {
  name: "contractSelect",
  mixins: [Emitter],
  props: {
    value: {
      type: String,
    },
    payType: String,
    size: {
      type: String,
      default: "mini",
    },
    lazy: {
      type: Boolean,
      default: false,
    },
    params: Object,
    disabled: Boolean,
    customOption: Object,
    beforeSelect: {
      type: Function,
    },
  },
  watch: {
    value: {
      async handler(val) {
        if (val && !this.visible && !this.contractSerialNo) {
          const checks = val.split(",");
          if (checks.length > 0) {
            for (const c of checks) {
              let res = await getDetail(c); // 接口查找
              if (res.data.data) {
                this.selectionList.push(res.data.data);
              }
            }
            this.contractSerialNo = this.serialNos;
            if (!this.lazy)
              this.$emit(
                "confirm",
                this.selectionList,
                this.ids,
                this.serialNos
              );
          }
        }
        if (!val) {
          this.contractSerialNo = "";
        }
      },
      immediate: true,
    },
  },
  computed: {
    ids() {
      let ids = new Set();
      this.selectionList.forEach((ele) => {
        ids.add(ele.id);
      });
      return Array.from(ids).join(",");
    },
    names() {
      let names = new Set();
      this.selectionList.forEach((ele) => {
        names.add(ele.name);
      });
      return Array.from(names).join(",");
    },
    serialNos() {
      let serialNos = new Set();
      this.selectionList.forEach((ele) => {
        serialNos.add(ele.serialNo);
      });
      return Array.from(serialNos).join(",");
    },
  },
  data() {
    return {
      contractSerialNo: "",
      visible: false,
      isInit: false,
      form: {},
      query: {},
      loading: false,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
        descs: "id",
      },
      selectionList: [],
      data: [],
      props: {
        id: "id",
        name: "name",
        records: "data.data.records",
        total: "data.data.total",
      },
      option: {
        size: "mini",
        searchSize: "mini",
        align: "center",
        menu: false,
        addBtn: false,
        header: false,
        border: true,
        tip: false,
        reserveSelection: true,
        highlightCurrentRow: true,
        gutter: 5,
        searchMenuSpan: 6,
        column: [
          {
            label: "",
            prop: "radio",
            type: "radio",
            fixed: "left",
            width: 55,
          },
          {
            label: "合同名称",
            prop: "name",
            minWidth: 120,
            overHidden: true,
            search: true,
          },
          {
            label: "合同编号",
            prop: "serialNo",
            minWidth: 110,
            placeholder: "系统自动生成",
            search: true,
            overHidden: true,
          },
          {
            label: "合同类型",
            prop: "type",
            type: "cascader",
            dicUrl:
              "/api/blade-system/dict-biz/dictionary-tree?code=ni_base_contract_type",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            disabled: false,
            slot: true,
            search: true,
            minWidth: 150,
            overHidden: true,
          },
          {
            label: "合同甲方",
            prop: "aname",
            overHidden: true,
            minWidth: 120,
          },
          {
            label: "合同乙方",
            prop: "bname",
            minWidth: 140,
            overHidden: true,
          },

          {
            label: "合同金额",
            prop: "amount",
            minWidth: 120,
            overHidden: true,
            type: "number",
          },
          {
            label: "订单金额",
            prop: "orderAmount",
            minWidth: 120,
            overHidden: true,
            type: "number",
          },
          {
            label: "申请付款金额",
            prop: "payApplyAmount",
            minWidth: 100,
            overHidden: true,
            type: "number",
          },
          {
            label: "签订日期",
            prop: "contractDate",
            type: "date",
            minWidth: 110,
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            search: true,
            searchRange: true,
          },
          {
            label: "审核状态",
            prop: "status",
            search: true,
            type: "select",
            dicUrl: "/api/blade-system/dict/dictionary?code=data_status",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            dataType: "number",
            minWidth: 70,
          },
          {
            label: "申请人",
            prop: "createUserName",
            minWidth: 70,
            display: false,
          },
          {
            label: "账套",
            prop: "brand",
            placeholder: " ",
            type: "radio",
            overHidden: true,
            dicData: [],
            order: 98,
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            search: true,
          },
        ],
      },
      typeDict: [],
      typeDictKeyValue: {},
      payTypeDict: [],
      payTypeDictKeyValue: {},
      brandDict: [],
      brandDictKeyValue: {},
    };
  },
  mounted() {
    this.init();
    this.dictInit();
  },
  methods: {
    dictInit() {
      this.$http
        .get("/api/blade-system/dict-biz/dictionary?code=ni_base_contract_type")
        .then((res) => {
          this.typeDict = res.data.data;
          this.typeDictKeyValue = this.typeDict.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });
      this.$http
        .get("/api/blade-system/dict-biz/dictionary?code=ni_brand")
        .then((res) => {
          const column = this.findObject(this.option.column, "brand");
          column.dicData = res.data.data;
          this.brandDict = res.data.data;
          this.brandDictKeyValue = this.brandDict.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });
    },
    bpClear() {
      this.contractSerialNo = null;
      this.selectionClear();
      this.$emit("input", "");
    },
    bpFocus() {
      if (this.beforeSelect) {
        this.beforeSelect(() => {
          this.visible = true;
          this.selectionList = [];
        });
      } else {
        this.visible = true;
        this.selectionList = [];
      }
    },
    init() {
      if (!this.isInit) {
        if (this.customOption) {
          const { column, userProps } = this.customOption;
          if (column) this.$set(this.option, "column", column);
          if (userProps) this.$set(this, "props", userProps);
        }
        this.isInit = true;
      }
    },
    handleConfirm() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      console.log(this.ids);
      this.contractSerialNo = this.serialNos;
      this.$emit("input", this.ids);
      this.$emit("confirm", this.selectionList, this.ids, this.serialNos);
      this.$nextTick(() => {
        this.dispatch("ElFormItem", "el.form.blur", [this.ids]);
      });
      this.handleClose();
    },
    handleClose(done) {
      // this.selectionClear()
      this.visible = false;
      if (done && typeof done == "function") done();
    },
    searchReset() {
      this.query={}
      this.page.currentPage = 1;
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query=params
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionClear() {
      this.selectionList = [];
      if (this.$refs.crud) this.$refs.crud.toggleSelection();
    },
    rowClick(row) {
      this.selectionList = [row];
      this.$set(this.form, "radio", row.id);
    },
    async changeDefaultChecked() {
      if (!this.value || this.value.length < 1) return;
      let defaultChecked = this.value;

      let row = this.data.find((d) => d.id == defaultChecked);
      if (!row) {
        let res = await getDetail(defaultChecked);
        if (res.data.data) row = res.data.data;
      }

      if (row) {
        this.selectionList = [row];
        this.$set(this.form, "radio", defaultChecked);
      } else {
        this.selectionList = [];
        this.$set(this.form, "radio", "");
      }
      this.contractSerialNo = this.serialNos;
    },
    onLoad(page, params = {}) {
      this.loading = true;
      const query = {
        ...params,
        ...this.query,
        ...this.params,
      };
      if (query.contractDate && query.contractDate.length == 2) {
        query.startContractDate = query.contractDate[0];
        query.endContractDate = query.contractDate[1];
        query.contractDate = null;
      }
      query.descs = this.page.descs;
      query.payType = this.payType;
      getPage(page.currentPage, page.pageSize, query).then((res) => {
        this.page.total = this.getAsVal(res, this.props.total);
        this.data = this.getAsVal(res, this.props.records) || [];
        this.loading = false;
        this.changeDefaultChecked();
      });
    },
    getAsVal(obj, bind = "") {
      let result = this.deepClone(obj);
      if (this.validatenull(bind)) return result;
      bind.split(".").forEach((ele) => {
        if (!this.validatenull(result[ele])) {
          result = result[ele];
        } else {
          result = "";
        }
      });
      return result;
    },
  },
};
</script>
<style lang="scss">
.us-dialog {
  display: flex;
  flex-direction: column;
  margin: 0 !important;
  position: absolute;
  top: 40%;
  left: 50%;
  transform: translate(-50%, -40%);
  max-height: calc(100% - 30px);
  max-width: calc(100% - 30px);

  .el-dialog__body {
    flex: 1;
    overflow: auto;
  }
}
</style>

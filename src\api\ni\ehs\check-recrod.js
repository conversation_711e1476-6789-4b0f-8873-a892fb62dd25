import request from "@/router/axios";

export const getPage = (current, size, params) => {
  return request({
    url: "/api/ni/ehs/extinguisher/check/recordPage",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/ni/ehs/extinguisher/recordDetail",
    method: "get",
    params: {
      id,
    },
  });
};

export const getCheckCondition = () => {
  return request({
    url: "/api/ni/ehs/extinguisher/check/checkCondition",
    method: "get",
  });
};

export const remove = (ids) => {
  return request({
    url: "/api/ni/ehs/extinguisher/check/remove",
    method: "post",
    params: {
      ids,
    },
  });
};
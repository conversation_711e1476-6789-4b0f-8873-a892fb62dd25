<template>
  <el-dialog
    ref="cost-apply-item-dialog"
    v-dialogdrag
    custom-class="cost-apply-item-dialog"
    :visible.sync="visible"
    title="费用申请明细选择"
    width="60%"
    :before-close="handleClose"
    append-to-body
  >
    <avue-crud
      v-if="isInit && visible"
      :option="option"
      :table-loading="loading"
      :page.sync="page"
      :data="data"
      v-model="form"
      ref="crud"
      @search-change="searchChange"
      @search-reset="searchReset"
      @current-change="page.currentPage = $event"
      @size-change="page.pageSize = $event"
      @selection-change="selectionList = $event"
      @row-click="rowClick"
      @on-load="onLoad"
    >
      <template v-if="!multiple" #radio="{ row }">
        <el-radio v-model="form.radio" :label="row.id"><i></i></el-radio>
      </template>
    </avue-crud>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose" size="mini">取 消</el-button>
      <el-button type="primary" @click="handleConfirm" size="mini">
        确 定
      </el-button>
    </span>
  </el-dialog>
</template>
<script>
import { getDetail, getPage } from "@/api/ni/fin/cost-apply-item";

export default {
  props: {
    defaultChecked: String,
    customOption: Object,
    multiple: {
      type: Boolean,
      default: false,
    },
    applyId: [String, Number],
  },
  watch: {
    multiple: {
      handler(val) {
        if (!val) {
          this.$set(this.option, "selection", false);
          this.findObject(this.option.column, "radio").hide = false;
        } else {
          this.$set(this.option, "selection", true);
          this.findObject(this.option.column, "radio").hide = true;
        }
      },
      immediate: true,
    },
  },
  computed: {
    ids() {
      let ids = new Set();
      this.selectionList.forEach((ele) => {
        ids.add(ele.id);
      });
      return Array.from(ids).join(",");
    },
    names() {
      let names = new Set();
      this.selectionList.forEach((ele) => {
        names.add(ele.serialNo);
      });
      return Array.from(names).join(",");
    },
  },
  data() {
    return {
      isInit: false,
      visible: false,
      form: {},
      query: {},
      loading: false,
      selectionList: [],
      data: [],
      props: {
        id: "id",
        name: "title",
        records: "data.data.records",
        total: "data.data.total",
      },
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      option: {
        searchEnter: true,
        size: "mini",
        searchSize: "mini",
        align: "center",
        menu: false,
        addBtn: false,
        header: false,
        border: true,
        tip: false,
        reserveSelection: true,
        highlightCurrentRow: true,
        gutter: 5,
        searchMenuSpan: 6,
        selection: true,
        column: [
          {
            label: "",
            prop: "radio",
            type: "radio",
            width: 55,
            hide: true,
          },
          {
            label: "费用名称",
            prop: "materialName",
            type: "textarea",
            minRows: 1,
            placeholder: " ",
            minWidth: 100,
            overHidden: true,
          },
          {
            label: "用途",
            prop: "purpose",
            type: "textarea",
            minRows: 1,
            placeholder: " ",
            overHidden: true,
            cell: true,
          },
          {
            label: "编码",
            prop: "materialId",
            placeholder: " ",
          },
          {
            label: "规格型号",
            placeholder: " ",
            prop: "specification",
            disabled: true,
          },
          {
            label: "数量",
            prop: "num",
            type: "number",
            placeholder: " ",
            minWidth: 100,
            rules: [
              {
                required: true,
                message: "请输入数量",
                trigger: "blur",
              },
            ],
          },
          {
            label: "单价",
            prop: "price",
            type: "number",
            controls: false,
            precision: 2,
            placeholder: " ",
            cell: true,
            change: ({ row, value }) => {
              const that = this;
              if (row.num) {
                row.amount = Number(value) * Number(row.num);
              } else {
                row.amount = 0;
              }
              let amount = 0;
              that.form.items.forEach((item) => {
                amount += Number(item.amount);
              });
              that.form.amount = amount;
            },
          },
          {
            label: "金额",
            prop: "amount",
            overHidden: true,
            controls: false,
            type: "number",
            minWidth: 100,
            precision: 2,
            placeholder: " ",
            rules: [
              {
                required: true,
                message: "请输入金额",
                trigger: "blur",
              },
            ],
            change: ({ row, value }) => {
              const that = this;
              if (row.num) {
                row.price = value / row.num;
              } else {
                row.price = 0;
              }
              let amount = 0;
              that.form.items.forEach((item) => {
                amount += Number(item.amount);
              });
              that.form.amount = amount;
            },
          },
        ],
      },
    };
  },
  mounted() {
    this.init();
  },
  methods: {
    init() {
      if (!this.isInit) {
        if (this.customOption) {
          const { column, props } = this.customOption;
          if (column) this.$set(this.option, "column", column);
          if (props) this.$set(this, "props", props);
        }
        this.isInit = true;
      }
    },
    handleConfirm() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$emit("onConfirm", this.selectionList);
      this.handleClose();
    },
    handleClose(done) {
      // this.selectionClear()
      this.visible = false;
      if (done && typeof done == "function") done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionClear() {
      this.selectionList = [];
      if (this.$refs.crud) this.$refs.crud.toggleSelection();
    },
    rowClick(row) {
      if (!this.multiple) {
        this.selectionList = [row];
        this.$set(this.form, "radio", row.id);
      } else this.$refs.crud.toggleSelection([row]);
    },
    async changeDefaultChecked() {
      if (!this.defaultChecked) return;
      let defaultChecked = this.defaultChecked;

      if (this.multiple) {
        // this.selectionClear()
        const checks = defaultChecked.split(",");
        if (checks.length > 0) {
          setTimeout(() => {
            checks.forEach(async (c) => {
              let row = this.data.find((d) => d.id == c); // 当前页查找
              if (!row) {
                row = this.selectionList.find((d) => d.id == c); // 勾选列表查找
                if (!row) {
                  let res = await getDetail(c); // 接口查找
                  if (res.data.data) row = res.data.data;
                }
              }
              if (row && this.$refs.crud)
                this.$refs.crud.toggleRowSelection(row, true);
            });
          }, 500);
        }
      } else {
        let row = this.data.find((d) => d.id == defaultChecked);
        if (!row) {
          let res = await getDetail(defaultChecked);
          if (res.data.data) row = res.data.data;
        }

        if (row) {
          this.selectionList = [row];
          this.$set(this.form, "radio", defaultChecked);
        } else {
          this.selectionList = [];
          this.$set(this.form, "radio", "");
        }
      }
    },
    onLoad(page, params = {}) {
      this.loading = true;
      const query = {
        ...params,
        ...this.query,
        status: 9,
        applyId: this.applyId,
      };
      getPage(page.currentPage, page.pageSize, query).then((res) => {
        this.page.total = this.getAsVal(res, this.props.total);
        this.data = this.getAsVal(res, this.props.records) || [];
        this.loading = false;
        this.changeDefaultChecked();
      });
    },
  },
};
</script>
<style lang="scss">
.cost-apply-dialog {
  display: flex;
  flex-direction: column;
  margin: 0 !important;
  position: absolute;
  top: 40%;
  left: 50%;
  transform: translate(-50%, -40%);
  max-height: calc(100% - 30px);
  max-width: calc(100% - 30px);

  .el-dialog__body {
    flex: 1;
    overflow: auto;
  }
}
</style>

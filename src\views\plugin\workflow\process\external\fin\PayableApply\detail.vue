<template>
  <basic-container>
    <avue-skeleton :loading="waiting" avatar :rows="8">
      <avue-affix id="avue-view" :offset-top="114">
        <div class="header">
          <avue-title :value="process.processDefinitionName"></avue-title>
          <div v-if="process.status != 'todo'">
            主题：
            <avue-select
              v-model="theme"
              size="mini"
              :clearable="false"
              :dic="themeList"
            ></avue-select>
          </div>
        </div>
      </avue-affix>
      <el-tabs v-model="activeName">
        <el-tab-pane label="申请信息" name="first">
          <el-card shadow="never">
            <div
              id="printBody"
              :class="process.status !== 'todo' ? `wf-theme-${theme}` : ''"
            >
              <avue-form
                v-if="
                  option &&
                  ((option.column && option.column.length > 0) ||
                    (option.group && option.group.length > 0))
                "
                v-model="form"
                ref="form"
                :defaults.sync="defaults"
                :option="option"
                :upload-preview="handleUploadPreview"
              >
                <template #financeBank="{ size, disabled }">
                  <supplier-finance-select
                    v-model="form.financeBank"
                    :supplier-id="form.supplierId"
                    :size="size"
                    :disabled="disabled"
                    @submit="handleFinanceSelect"
                  />
                </template>
                <template #adjustId="{ size, index }">
                  <fin-adjust-select
                    v-model="form.adjustId"
                    :size="size"
                    :params="{
                      status: 9,
                      supplierId: form.supplierId,
                      brand: form.brand,
                    }"
                    disabled
                    :before-select="beforeAdjustSelect"
                    @change="handleAdjustChange"
                  />
                </template>
                <template #costApplyId="{ size, disabled, index }">
                  <cost-apply-select
                    v-model="form.costApplyId"
                    :size="size"
                    :disabled="disabled"
                    :change="rowCostApplyChange"
                  />
                </template>
                <template #payState>
                  <span style="font-weight: bolder; color: #f56c6c">
                    {{ form.$payState }}
                  </span>
                </template>
                <template #payAmount="{ value }">
                  <span style="font-weight: bolder; color: #f56c6c">
                    {{ value }}
                  </span>
                </template>
                <template #payableRemark="{ value }">
                  <span style="font-weight: bolder; color: #f56c6c">
                    {{ value }}
                  </span>
                </template>
                <template #payableDate="{ value }">
                  <span style="font-weight: bolder; color: #f56c6c">
                    {{ value }}
                  </span>
                </template>
                <template #financeAccount="{ size, disabled }">
                  <bank-card-input
                    v-model="form.financeAccount"
                    :size="size"
                    :disabled="disabled"
                  />
                </template>
                <template #alipayId="{ size, disabled }">
                  <alipay-bill-select
                    multiple
                    v-model="form.alipayId"
                    :disabled="disabled"
                    :params="{
                      payApplyStates: '0,1',
                      brand: form.brand,
                      supplierId: form.supplierId,
                    }"
                    :before-select="beforeContractSelect"
                    @confirm="handleAlipayBillSelectConfirm"
                  />
                </template>
                <template #budgetId="{ size, disabled }">
                  <un-finish-budget-select
                    v-model="form.budgetId"
                    :size="size"
                    :disabled="disabled"
                    @clear="handleBudgetClear"
                    @confirm="handleBudgetConfirm"
                  />
                </template>
                <template #contractId="{ disabled, size, index }">
                  <contract-select
                    v-model="form.contractId"
                    :pay-type="payType"
                    :disabled="disabled"
                    :size="size"
                    :params="{
                      b: form.supplierId,
                      status: 9,
                    }"
                    lazy
                    :before-select="beforeContractSelect"
                    @confirm="handleContractIdConfirm"
                  />
                </template>
                <template #amount="{ size }">
                  <el-input-number
                    v-if="!['3', '9'].includes(form.type)"
                    :size="size"
                    style="width: 100%"
                    v-model="form.amount"
                    precision="2"
                    controls-position="right"
                    @change="handleAmount"
                  ></el-input-number>
                  <el-input-number
                    v-else-if="['9'].includes(form.type)"
                    :size="size"
                    style="width: 100%"
                    v-model="form.amount"
                    precision="2"
                    controls-position="right"
                    :max="form.unPayAmount"
                    @change="handleAmount"
                  ></el-input-number>
                  <el-input-number
                    v-else
                    :size="size"
                    style="width: 100%"
                    v-model="form.amount"
                    precision="2"
                    disabled
                    controls-position="right"
                    @change="handleAmount"
                  ></el-input-number>
                </template>
                <template #supplierId="{ disabled, size, index }">
                  <supplier-multiple-select
                    v-model="form.supplierId"
                    :size="size"
                    :multiple="false"
                    :disabled="disabled"
                    @submit="handleSupplierSubmit"
                  />
                </template>
                <template #paySoaId="{ disabled, size, index }">
                  <pay-soa-select
                    v-model="form.paySoaId"
                    :size="size"
                    :disabled="disabled"
                    :before-select="beforeContractSelect"
                    :params="{
                      brand: form.brand,
                      supplierId: form.supplierId,
                      confirm: true,
                      payApplyStates: '0,1',
                    }"
                    :lazy="
                      !['recall', 'reject'].includes(process.processIsFinished)
                    "
                    @submit="handlePaySoaSubmit"
                  />
                </template>
                <template #porOrderId="{ disabled, size, index }">
                  <por-order-select
                    v-model="form.porOrderId"
                    :label.sync="form.porOrderSerialNo"
                    :size="size"
                    :disabled="disabled"
                    :params="{
                      brand: form.brand,
                      supplierId: form.supplierId,
                      payType: '2',
                      payApplyStates: '0,1',
                      payState: 0,
                    }"
                    :before-select="beforeContractSelect"
                    @submit="handleOrderSubmit"
                    @clear="handleOrderClear"
                  />
                </template>
                <template #alipaysLabel>
                  <span style="font-size: 16px; font-weight: 500"
                    >支付宝明细</span
                  >
                </template>
                <template #alipays="{ row, index, size }">
                  <payable-apply-item-material-edit
                    v-if="
                      form.updateMaterialCode === '1' &&
                      !['recall', 'reject'].includes(processIsFinished)
                    "
                    v-model="form.items"
                    :selectionList.sync="item.selectionList"
                    :cost="form.type === '3'"
                    @sumAmount="handleSumAmount"
                    @attachClick="rowAttach"
                  />
                  <payable-apply-order-alipay v-else v-model="form.items" />
                </template>
                <template #itemsLabel>
                  <span style="font-size: 16px; font-weight: 500">
                    付款明细
                  </span>
                  <el-divider
                    v-if="
                      ['1', '3'].includes(form.type) &&
                      ['recall', 'reject'].includes(process.processIsFinished)
                    "
                    direction="vertical"
                  />
                  <el-button-group
                    v-if="
                      ['1', '3'].includes(form.type) &&
                      ['recall', 'reject'].includes(process.processIsFinished)
                    "
                  >
                    <el-button
                      type="primary"
                      size="mini"
                      icon="el-icon-plus"
                      @click="costItemAdd"
                      >添加
                    </el-button>
                    <el-button
                      type="danger"
                      size="mini"
                      icon="el-icon-delete"
                      :disabled="item.selectionList.length <= 0"
                      @click="costItemDelete"
                      >删 除
                    </el-button>
                  </el-button-group>
                  <el-divider direction="vertical" />
                  <el-tag>
                    金额总计:
                    <span style="font-weight: bolder; color: #f56c6c">
                      {{
                        Number(amount).toLocaleString("zh-CN", {
                          minimumFractionDigits: 2,
                        })
                      }}
                    </span>
                  </el-tag>
                  <el-divider direction="vertical" v-if="form.adjustAmount" />
                  <el-tag v-if="form.adjustAmount">
                    {{
                      form.adjustType && form.adjustType === "1"
                        ? "调增"
                        : "调减"
                    }}金额:
                    <span style="font-weight: bolder; color: #f56c6c">
                      {{
                        Number(form.adjustAmount).toLocaleString("zh-CN", {
                          minimumFractionDigits: 2,
                        })
                      }}
                    </span>
                  </el-tag>
                </template>
                <template #items="{ row, index, size }">
                  <payable-apply-order-alipay
                    v-if="form.soaType === '2'"
                    v-model="form.items"
                    :selectionList="item.selectionList"
                    :cost="form.type === '3'"
                    :material-edit="
                      form.updateMaterialCode === '1' &&
                      !['recall', 'reject'].includes(processIsFinished)
                    "
                    :detail="option.detail"
                    @sumAmount="handleSumAmount"
                    @attachClick="rowAttach"
                  />
                  <payable-apply-item-material-edit
                    v-else-if="
                      form.updateMaterialCode === '1' &&
                      !['recall', 'reject'].includes(processIsFinished)
                    "
                    v-model="form.items"
                    :selectionList.sync="item.selectionList"
                    :cost="form.type === '3'"
                    @sumAmount="handleSumAmount"
                    @attachClick="rowAttach"
                  />

                  <payable-apply-order-item
                    v-else
                    v-model="form.items"
                    :selectionList="item.selectionList"
                    :cost="form.type === '3'"
                    :detail="option.detail"
                    @sumAmount="handleSumAmount"
                    @attachClick="rowAttach"
                  />
                </template>
              </avue-form>
            </div>
          </el-card>
          <el-card
            shadow="never"
            style="margin-top: 20px"
            v-if="process.status == 'todo'"
          >
            <wf-examine-form
              ref="examineForm"
              :comment.sync="comment"
              :process="process"
              @user-select="handleUserSelect"
            ></wf-examine-form>
          </el-card>
        </el-tab-pane>
        <el-tab-pane label="流转信息" name="second">
          <el-card shadow="never" style="margin-top: 5px">
            <wf-flow :flow="flow"></wf-flow>
          </el-card>
        </el-tab-pane>
        <el-tab-pane label="流程跟踪" name="third">
          <template v-if="activeName == 'third'">
            <el-card shadow="never" style="margin-top: 5px">
              <wf-design
                ref="bpmn"
                style="height: 500px"
                :options="bpmnOption"
              ></wf-design>
            </el-card>
          </template>
        </el-tab-pane>
      </el-tabs>
    </avue-skeleton>

    <!-- 底部按钮 -->
    <wf-button
      :loading="submitLoading"
      :button-list="buttonList"
      :process="process"
      :comment="comment"
      @examine="handleExamine"
      @user-select="handleUserSelect"
      @print="handlePrint"
      @rollback="handleRollbackTask"
      @terminate="handleTerminateProcess"
      @withdraw="handleWithdrawTask"
    ></wf-button>
    <budget-cost-item-dialog
      ref="porBudgetCostItemRef"
      multiple
      :params="{ used: false }"
      @confirm="handleCostItemSelect"
    />
    <!-- 人员选择弹窗 -->
    <user-select
      ref="user-select"
      :check-type="checkType"
      :default-checked="defaultChecked"
      @onConfirm="handleUserSelectConfirm"
    ></user-select>
    <attach-dialog
      ref="attachRef"
      code="public"
      :delBtn="false"
      @close="handleRefreshAttach"
    />
    <cost-apply-item-dialog
      ref="costApplyItemRef"
      :apply-id="form.costApplyId"
      multiple
      @onConfirm="handleCostApplySelectConfirm"
    />
  </basic-container>
</template>

<script>
import WfExamineForm from "@/views/plugin/workflow/process/components/examForm.vue";
import WfButton from "@/views/plugin/workflow/process/components/button.vue";
import WfFlow from "@/views/plugin/workflow/process/components/flow.vue";
import userSelect from "@/views/plugin/workflow/process/components/user-select";
import WfUserSelect from "@/views/plugin/workflow/process/components/user-select";

import exForm from "@/views/plugin/workflow/mixins/ex-form";
import theme from "@/views/plugin/workflow/mixins/theme";
import { getDetail as getSupplierDetail } from "@/api/ni/base/supplier/supplierinfo";
import { numToCapital } from "@/util/util";
import SupplierMultipleSelect from "@/views/ni/base/components/SupplierSelect1";
import PayableApplyOrderItem from "@/views/ni/fin/components/PayableApplyItem";
import PayableApplyItemMaterialEdit from "@/views/ni/fin/components/PayableApplyItemMaterialEdit";

import PayableApplyCostItem from "@/views/ni/fin/components/PayableApplyCostItem";
import PorOrderSelect from "@/views/ni/por/components/OrderSelect";
import PayableApplyOrderAlipay from "@/views/ni/fin/components/PayableApplyOrderAlipay";
import FinPayableApplyForm from "@/views/ni/fin/components/FinPayableApplyForm";
import PayableApplyDetail from "@/views/ni/fin/payable-apply-detail";
import AlipayBillList from "@/views/ni/por/components/AlipayBillList";
import AlipayBillSelect from "@/views/ni/por/components/AlipayBillSelect";
import BankCardInput from "@/components/bank-card-input";
import PaySoaSelect from "@/views/ni/fin/components/PaySoaSelect";
import ContractSelect from "@/views/ni/base/components/ContractSelect";
import unFinishBudgetSelect from "@/views/ni/por/components/UnFinishBudgetSelect";
import budgetCostItemDialog from "@/views/ni/por/components/BudgetCostItemDialog1";
import { getDetail as getPaySoaDetail } from "@/api/ni/fin/pay-soa";
import { getAlipayList } from "@/api/ni/por/order-alipay";
import {
  getList as getOrderItemList,
  getList as getItemList,
} from "@/api/ni/por/order-item";
import AttachDialog from "@/components/attach-dialog";
import { getList as getAttachList } from "@/api/resource/attach";
import CostApplyItemDialog from "@/views/ni/fin/components/CostApplyItemDialog";
import CostApplySelect from "@/views/ni/fin/components/CostApplySelect";
import { getDetail as getCostApplyDetail } from "@/api/ni/fin/cost-apply";
import { dateFormat } from "@/util/date";
import MaterialSelectDialog from "@/views/ni/base/components/MaterialSelectDialog";
import FinAdjustSelect from "@/views/ni/fin/components/FinAdjustSelect";
import SupplierFinanceSelect from "@/views/ni/base/components/SupplierFinanceSelect";

export default {
  mixins: [exForm, theme],
  components: {
    MaterialSelectDialog,
    CostApplySelect,
    CostApplyItemDialog,
    AttachDialog,
    userSelect,
    WfExamineForm,
    WfButton,
    WfFlow,
    PayableApplyOrderItem,
    PayableApplyCostItem,
    SupplierMultipleSelect,
    PorOrderSelect,
    PayableApplyOrderAlipay,
    FinPayableApplyForm,
    PayableApplyDetail,
    AlipayBillList,
    AlipayBillSelect,
    BankCardInput,
    PaySoaSelect,
    ContractSelect,
    WfUserSelect,
    unFinishBudgetSelect,
    budgetCostItemDialog,
    PayableApplyItemMaterialEdit,
    FinAdjustSelect,
    SupplierFinanceSelect,
  },
  watch: {
    "$route.query.p": {
      immediate: true,
      handler(val) {
        if (val) {
          this.submitLoading = true;
          Object.keys(this.form).forEach((key) => (this.form[key] = ""));
          const param = JSON.parse(Buffer.from(val, "base64").toString());
          const { taskId, processInsId } = param;
          if (processInsId) {
            this.getDetail(taskId, processInsId);
          }
        }
      },
    },
  },
  data() {
    const PAY_TYPE_PAY = "2";
    return {
      isInit: true,
      payType: PAY_TYPE_PAY,
      activeName: "first",
      defaults: {},
      form: {},
      option: {
        labelWidth: 150,
        calcHeight: 30,
        size: "mini",
        menuBtn: false,
        column: [
          {
            type: "input",
            label: "申请人",
            display: true,
            prop: "createUserName",
            value: "${this.$store.getters.userInfo.nick_name}",
            readonly: true,
            span: 6,
          },
          {
            type: "input",
            label: "申请部门",
            display: true,
            prop: "createDeptName",
            value: "${this.$store.getters.userInfo.dept_name}",
            readonly: true,
            span: 6,
            row: true,
          },
          {
            label: "付款用途",
            prop: "useTo",
            overHidden: true,
            placeholder: " ",
            search: true,
            minWidth: 120,
            span: 6,
            rules: [
              {
                required: true,
                message: "请输入付款用途",
                trigger: "blur",
              },
            ],
          },
          {
            label: "编号",
            prop: "serialNo",
            placeholder: "系统自动生成",
            disabled: true,
            span: 6,
          },
          {
            label: "账套",
            prop: "brand",
            placeholder: " ",
            type: "radio",
            span: 6,
            dicData: [],
            row: true,
            value: "1",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            rules: [
              {
                required: true,
                message: "请选择账套",
                trigger: "blur",
              },
            ],
          },
          {
            label: "供应商",
            prop: "supplierId",
            minWidth: 100,
            overHidden: true,
            span: 6,
            search: true,
            rules: [
              {
                required: true,
                message: "请选择供应商",
                trigger: "blur",
              },
            ],
          },
          {
            label: "开户银行",
            prop: "financeBank",
            placeholder: " ",
            span: 6,
            hide: true,
            rules: [
              {
                required: true,
                message: "请输入开户银行",
                trigger: "blur",
              },
            ],
          },
          {
            label: "收款人全称",
            prop: "financeName",
            placeholder: " ",
            span: 6,
            hide: true,
            rules: [
              {
                required: true,
                message: "请输入收款人全称",
                trigger: "blur",
              },
            ],
          },
          {
            label: "银行账号",
            prop: "financeAccount",
            placeholder: " ",
            span: 6,
            row: true,
            hide: true,
            rules: [
              {
                required: true,
                message: "请输入银行账号",
                trigger: "blur",
              },
            ],
          },

          {
            label: "申请类型",
            prop: "type",
            type: "select",
            disabled: true,
            dicData: [],
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            value: "2",
            row: false,
            minWidth: 100,
            span: 6,
            rules: [
              {
                required: true,
                message: "请选择申请类型",
                trigger: "blur",
              },
            ],
            control: (val) => {
              // this.form.porOrderId = null;
              // this.form.porOrderSerialNo = null;
              // this.form.porOrderAmount = null;
              // this.form.contractId = null;
              // this.form.contractAmount = null;
              // this.form.alipayId = null;
              // this.form.alipayAmount = null;
              // this.form.unPayAmount = null;
              // this.form.budgetId = null;
              // this.form.paySoaAmount = null;
              // this.form.paySoaId = null;
              // this.form.items = [];
              // this.form.alipays = [];
              if (val === "1") {
                //合同
                return {
                  type: {
                    row: false,
                  },
                  paySoaId: {
                    display: false,
                  },
                  paySoaAmount: {
                    display: false,
                  },
                  porOrderId: {
                    display: false,
                  },
                  porOrderAmount: {
                    display: false,
                  },
                  contractId: {
                    display: true,
                  },
                  contractAmount: {
                    display: true,
                  },
                  alipayId: {
                    display: false,
                  },
                  alipayAmount: {
                    display: false,
                  },
                  unPayAmount: {
                    display: false,
                  },
                  costApplyId: {
                    display: false,
                  },
                  budgetId: {
                    disabled: false,
                    display: true,
                    rules: [
                      {
                        required: false,
                      },
                    ],
                  },
                  alipays: {
                    display: false,
                  },
                  items: {
                    display: true,
                  },
                  amount: {
                    disabled: false,
                  },
                };
              } else if (val === "2") {
                //采购
                return {
                  type: {
                    row: false,
                  },
                  paySoaId: {
                    display: true,
                  },
                  paySoaAmount: {
                    display: true,
                  },
                  porOrderId: {
                    display: false,
                  },
                  porOrderAmount: {
                    display: false,
                  },
                  contractId: {
                    display: false,
                  },
                  contractAmount: {
                    display: false,
                  },
                  alipayId: {
                    display: false,
                  },
                  alipayAmount: {
                    display: false,
                  },
                  unPayAmount: {
                    display: false,
                  },
                  costApplyId: {
                    display: false,
                  },
                  budgetId: {
                    disabled: false,
                    display: false,
                  },
                  alipays: {
                    display: false,
                  },
                  items: {
                    display: true,
                  },
                  amount: {
                    disabled: false,
                  },
                };
              } else if (val === "3") {
                //费用
                return {
                  type: {
                    row: false,
                  },
                  paySoaId: {
                    display: false,
                  },
                  paySoaAmount: {
                    display: false,
                  },
                  porOrderId: {
                    display: false,
                  },
                  porOrderAmount: {
                    display: false,
                  },
                  contractId: {
                    display: false,
                  },
                  contractAmount: {
                    display: false,
                  },
                  alipayId: {
                    display: false,
                  },
                  alipayAmount: {
                    display: false,
                  },
                  unPayAmount: {
                    display: false,
                  },
                  costApplyId: {
                    display: true,
                    rules: [
                      {
                        required: false,
                      },
                    ],
                  },
                  budgetId: {
                    display: true,
                    rules: [
                      {
                        required: true,
                      },
                    ],
                  },
                  alipays: {
                    display: false,
                  },
                  items: {
                    display: true,
                  },
                  amount: {
                    disabled: true,
                  },
                };
              } else if (val === "4") {
                return {
                  type: {
                    row: false,
                  },
                  paySoaId: {
                    display: false,
                  },
                  paySoaAmount: {
                    display: false,
                  },
                  porOrderId: {
                    display: false,
                  },
                  porOrderAmount: {
                    display: false,
                  },
                  contractId: {
                    display: false,
                  },
                  contractAmount: {
                    display: false,
                  },
                  alipayId: {
                    display: true,
                  },
                  alipayAmount: {
                    display: true,
                  },
                  unPayAmount: {
                    display: false,
                  },
                  costApplyId: {
                    display: false,
                  },
                  budgetId: {
                    disabled: false,
                    display: false,
                  },
                  alipays: {
                    display: true,
                  },
                  items: {
                    display: false,
                  },
                  amount: {
                    disabled: false,
                  },
                };
              } else if (val === "5") {
                //预付款
                return {
                  type: {
                    row: false,
                  },
                  paySoaId: {
                    display: false,
                  },
                  paySoaAmount: {
                    display: false,
                  },
                  porOrderId: {
                    display: true,
                  },
                  porOrderAmount: {
                    display: true,
                  },
                  contractId: {
                    display: false,
                  },
                  contractAmount: {
                    display: false,
                  },
                  alipayId: {
                    display: false,
                  },
                  alipayAmount: {
                    display: false,
                  },
                  unPayAmount: {
                    display: false,
                  },
                  costApplyId: {
                    display: false,
                  },
                  budgetId: {
                    disabled: false,
                    display: false,
                  },
                  alipays: {
                    display: false,
                  },
                  items: {
                    display: true,
                  },
                  amount: {
                    disabled: false,
                  },
                };
              } else {
                //其他
                return {
                  type: {
                    row: true,
                  },
                  paySoaId: {
                    display: false,
                  },
                  paySoaAmount: {
                    display: false,
                  },
                  porOrderId: {
                    display: false,
                  },
                  porOrderAmount: {
                    display: false,
                  },
                  contractId: {
                    display: false,
                  },
                  contractAmount: {
                    display: false,
                  },
                  alipayId: {
                    display: false,
                  },
                  alipayAmount: {
                    display: false,
                  },
                  unPayAmount: {
                    display: false,
                  },
                  costApplyId: {
                    display: false,
                  },
                  budgetId: {
                    disabled: false,
                    display: false,
                  },
                  alipays: {
                    display: false,
                  },
                  items: {
                    display: false,
                  },
                  amount: {
                    disabled: false,
                  },
                };
              }
            },
          },
          {
            label: "采购对账单",
            prop: "paySoaId",
            span: 6,
            display: false,
            disabled: true,
            hide: true,
            rules: [
              {
                required: true,
                message: "请选择采购对账单",
                trigger: "blur",
              },
            ],
          },
          {
            label: "采购订单",
            prop: "porOrderId",
            span: 6,
            disabled: true,
            display: false,
            hide: true,
            rules: [
              {
                required: true,
                message: "请选择采购订单",
                trigger: "blur",
              },
            ],
          },
          {
            label: "关联合同",
            prop: "contractId",
            span: 6,
            disabled: true,
            display: false,
            hide: true,
            rules: [
              {
                required: true,
                message: "请选择关联合同",
                trigger: "blur",
              },
            ],
          },
          {
            label: "关联订单",
            prop: "alipayId",
            placeholder: " ",
            span: 6,
            disabled: true,
            display: false,
            hide: true,
            rules: [
              {
                required: true,
                message: "请选择支付宝关联订单",
                trigger: "blur",
              },
            ],
          },
          {
            label: "费用申请",
            prop: "costApplyId",
            display: false,
            span: 6,
            rules: [
              {
                required: false,
                message: "请选择费用申请",
                trigger: "blur",
              },
            ],
          },
          {
            label: "关联预算",
            prop: "budgetId",
            placeholder: " ",
            span: 6,
            row: true,
            display: false,
            hide: true,
            rules: [
              {
                required: true,
                message: "请选择关联预算",
                trigger: "blur",
              },
            ],
          },
          {
            label: "快递对账单",
            prop: "ofcSoaId",
            placeholder: " ",
            span: 6,
            row: true,
            display: false,
            hide: true,
            rules: [
              {
                required: true,
                message: "请选择快递对账单",
                trigger: "blur",
              },
            ],
          },
          {
            label: "对账金额",
            prop: "paySoaAmount",
            type: "number",
            placeholder: " ",
            disabled: true,
            display: false,
            hide: true,
            showColumn: false,
            precision: 2,
            span: 6,
            row: true,
          },
          {
            label: "订单金额",
            prop: "porOrderAmount",
            type: "number",
            placeholder: " ",
            disabled: true,
            display: false,
            hide: true,
            showColumn: false,
            precision: 2,
            span: 6,
            row: true,
          },
          {
            label: "合同金额",
            prop: "contractAmount",
            type: "number",
            precision: 2,
            placeholder: " ",
            disabled: true,
            display: false,
            hide: true,
            showColumn: false,
            span: 6,
            row: true,
          },
          {
            label: "订单金额",
            prop: "alipayAmount",
            type: "number",
            placeholder: " ",
            disabled: true,
            display: false,
            hide: true,
            showColumn: false,
            precision: 2,
            span: 6,
            row: true,
          },
          {
            span: 6,
            label: "是否调账",
            prop: "adjust",
            type: "radio",
            value: 0,
            row: false,
            dicData: [
              {
                label: "是",
                value: 1,
              },
              {
                label: "否",
                value: 0,
              },
            ],
            change: ({ column, value }) => {
              const adjustId = this.findObject(this.option.column, "adjustId");
              const adjustAmount = this.findObject(
                this.option.column,
                "adjustAmount"
              );
              if (value === 1) {
                adjustId.display = true;
                adjustAmount.display = true;
                column.row = false;
              } else {
                adjustId.display = false;
                adjustAmount.display = false;
                column.row = true;
                if (this.form.adjustId && this.form.adjustAmount) {
                  this.form.unPayAmount += Number(this.form.adjustAmount);
                  this.form.amount = null;
                  this.form.upperAmount = null;
                }
                this.form.adjustId = null;
                this.form.adjustAmount = 0;
              }
            },
          },
          {
            label: "调账申请",
            prop: "adjustId",
            display: false,
            span: 6,
            rules: [
              {
                required: true,
                message: "请选择调账申请",
                trigger: "blur",
              },
            ],
          },
          {
            label: "调账金额",
            prop: "adjustAmount",
            type: "number",
            placeholder: " ",
            disabled: true,
            precision: 2,
            span: 6,
            row: true,
            display: false,
          },
          {
            label: "待付款金额",
            prop: "unPayAmount",
            type: "number",
            placeholder: " ",
            disabled: true,
            display: false,
            precision: 2,
            span: 6,
          },
          {
            label: "本次付款金额",
            labelTip: "含税",
            prop: "amount",
            type: "number",
            placeholder: " ",
            minWidth: 100,
            precision: 2,
            span: 6,
            rules: [
              {
                required: true,
                message: "请输入本次付款金额(含税)",
                trigger: "blur",
              },
            ],
          },
          {
            label: "金额大写",
            prop: "upperAmount",
            readonly: true,
            placeholder: " ",
            hide: true,
            showColumn: false,
            span: 6,
          },
          {
            label: "币种",
            prop: "currency",
            type: "select",
            search: true,
            minWidth: 70,
            dicUrl: "/api/blade-system/dict/dictionary?code=currency",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            value: "RMB",
            placeholder: " ",
            span: 6,
            row: true,
            rules: [
              {
                required: true,
                message: "请选择币种",
                trigger: "blur",
              },
            ],
            control: (val) => {
              if (val === "RMB") {
                return {
                  excRate: {
                    display: false,
                    value: 1,
                  },
                  currency: {
                    row: true,
                  },
                };
              } else {
                return {
                  excRate: {
                    display: true,
                  },
                  currency: {
                    row: false,
                  },
                };
              }
            },
          },
          {
            label: "汇率",
            prop: "excRate",
            labelTip:
              "汇率=本位币/原币.如本位币为人民币，原币为美元: 汇率为:0.1439.",
            type: "number",
            placeholder: " ",
            hide: true,
            display: false,
            span: 6,
            row: false,
            rules: [
              {
                required: true,
                message: "请输入汇率",
                trigger: "blur",
              },
            ],
          },
          {
            label: "付款日期",
            prop: "applyPayableDate",
            type: "date",
            minWidth: 95,
            placeholder: " ",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            span: 6,
            rules: [
              {
                required: true,
                message: "请选择申请付款日期",
                trigger: "blur",
              },
            ],
          },
          {
            label: "付款方式",
            prop: "paymentType",
            type: "select",
            minWidth: 85,
            span: 6,
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_fin_payment_type",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            placeholder: " ",
            rules: [
              {
                required: true,
                message: "请选择付款方式",
                trigger: "blur",
              },
            ],
          },
          {
            label: "发票类型",
            prop: "billType",
            minWidth: 70,
            type: "select",
            dicUrl: "/api/blade-system/dict-biz/dictionary?code=ni_bill_type",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            placeholder: " ",
            span: 6,
            rules: [
              {
                required: true,
                message: "请输入",
                trigger: "blur",
              },
            ],
          },
          {
            label: "税率(%)",
            prop: "taxRate",
            minWidth: 65,
            placeholder: " ",
            type: "number",
            precision: 2,
            controls: false,
            hide: true,
            span: 6,
          },
          {
            label: "付款状态",
            prop: "payState",
            type: "select",
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_fin_payable_apply_pay_state",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            dataType: "number",
            display: false,
            span: 6,
            change: ({ value }) => {
              if (value && value !== "0" && !this.form.payableDate) {
                this.$set(
                  this.form,
                  "payableDate",
                  dateFormat(new Date(), "yyyy-MM-dd")
                );
              }
            },
          },
          {
            label: "付款金额",
            prop: "payAmount",
            span: 6,
            display: false,
          },
          {
            label: "付款时间",
            prop: "payableDate",
            type: "date",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            span: 6,
            display: false,
          },
          {
            label: "付款备注",
            prop: "payableRemark",
            span: 6,
            display: false,
          },
          {
            label: "备注",
            prop: "remark",
            type: "textarea",
            minRows: 2,
            span: 24,
            overHidden: true,
            minWidth: 110,
          },
          {
            label: "附件",
            type: "upload",
            propsHttp: {
              res: "data",
              url: "attachId",
              name: "originalName",
            },
            action:
              "/api/blade-resource/oss/endpoint/put-file-attach?code=public",
            display: true,
            span: 24,
            showFileList: true,
            multiple: true,
            limit: 10,
            prop: "attachment",
          },
          {
            label: "支付宝明细",
            prop: "alipays",
            labelPosition: "top",
            display: false,
            hide: true,
            span: 24,
            showColumn: false,
          },
          {
            label: "申请明细",
            prop: "items",
            labelPosition: "top",
            display: false,
            hide: true,
            span: 24,
            showColumn: false,
          },
        ],
      },
      item: {
        selectionList: [],
      },
      vars: [], // 需要提交的字段
      submitLoading: false, // 提交时按钮loading
      typeDict: [],
      typeDictKeyValue: {},
      inquiries: {},
      payment: 0,
      financeSearch: false,
      financeVisible: false,
      financeOption: {
        refreshBtn: false,
        columnBtn: false,
        menu: false,
        highlightCurrentRow: true,
        size: "mini",
        searchSize: "mini",
        align: "center",
        searchIndex: 3,
        searchIcon: true,
        calcHeight: 30,
        tip: false,
        border: true,
        viewBtn: true,
        reserveSelection: true,
        labelWidth: 120,
        dialogClickModal: false,
        addBtn: false,
        delBtn: false,
        editBtn: false,
        column: [
          {
            label: "",
            prop: "radio",
            type: "radio",
            width: 55,
          },
          {
            label: "开户银行",
            prop: "financeBank",
            minWidth: 110,
            placeholder: " ",
          },
          {
            label: "收款人全称",
            prop: "financeName",
            placeholder: " ",
          },
          {
            label: "银行账号",
            prop: "financeAccount",
            placeholder: " ",
            minWidth: 180,
          },
          {
            label: "付款方式",
            prop: "payType",
            placeholder: " ",
            minWidth: 120,
          },
          {
            label: "发票类型",
            prop: "bill",
            placeholder: " ",
            minWidth: 120,
          },
          {
            label: "备注",
            prop: "remark",
            placeholder: " ",
            minWidth: 120,
          },
        ],
      },
      processIsFinished: "",
      financeSelectionList: [],
      financeForm: {},
      financeData: [],
      itemDialogShow: false,
      actionItem: null,
      actionItemIndex: null,
      fromPath: "",
    };
  },
  computed: {
    amount() {
      if (this.form.items)
        return (
          this.form.items
            .map((item) => item.amount)
            .reduce((prev, curr) => prev + Number(curr) * 10000, 0) / 10000
        );
      return "0";
    },
  },
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      console.log(from, "from");
      //  这里的vm指的就是vue实例，可以用来当做this使用
      vm.fromPath = from.path; //获取上一级路由的路径
    });
  },
  created() {
    this.dictInit();
  },
  methods: {
    handleFinanceSelect(selectionList) {
      this.form.financeName = selectionList[0].financeName;
      this.form.financeAccount = selectionList[0].financeAccount;
      this.form.bill = selectionList[0].bill;
    },
    beforeAdjustSelect(done) {
      if (!this.form.brand) {
        this.$message({
          type: "warning",
          message: "请选择账套!",
        });
        return;
      }
      if (!this.form.supplierId) {
        this.$message({
          type: "warning",
          message: "请选择供应商!",
        });
        return;
      }
      done();
    },
    handleAdjustChange(selection) {
      if (selection) {
        const adjust = selection[0];
        this.form.adjustAmount = adjust.amount;
        this.form.adjustType = adjust.subType;
        this.form.unPayAmount = this.getUnPayAmountWithoutAdjust(this.form);

        //调增
        if (adjust.subType === "1") {
          this.form.unPayAmount += adjust.amount;
          //调减
        } else if (adjust.subType === "2") {
          this.form.unPayAmount -= adjust.amount;
        }
        this.form.amount = this.form.unPayAmount;
        this.form.upperAmount = numToCapital(this.form.amount);
      }
    },
    getUnPayAmountWithoutAdjust(form) {
      let amount = 0;
      if (this.form.type === "1") {
        amount = Number(form.contractAmount);
      } else if (this.form.type === "2") {
        amount = Number(form.paySoaAmount);
      } else if (this.form.type === "3") {
        if (form.costAmount) amount = Number(form.costAmount);
        else {
          form.items.forEach((item) => {
            amount += Number(item.amount);
          });
        }
      } else if (this.form.type === "5") {
        amount = Number(form.porOrderAmount);
      }
      return amount;
    },
    handleBudgetConfirm(selectionList) {
      if (selectionList) {
        this.form.budgetSerialNo = selectionList[0].serialNo;
        this.form.budgetType = selectionList[0].type;
      }
    },
    handleCostApplySelectConfirm(selectionList) {
      selectionList.forEach((item) => {
        this.form.items.push({
          budgetId: item.budgetId,
          budgetItemId: item.budgetItemId,
          costApplyItemId: item.id,
          materialName: item.materialName,
          materialCode: item.materialCode,
          specification: item.specification,
          quality: item.quality,
          unit: item.unit,
          num: item.num,
          amount: item.amount,
          remark: item.remark,
          cost: true,
          budgetSerialNo: item.serialNo,
          gb: item.gb,
          price: item.price,
          $cellEdit: false,
        });
      });
      this.sumAmount();
    },
    rowCostApplyChange(row) {
      if (row && row.value)
        getCostApplyDetail(row.value).then((res) => {
          const data = res.data.data;
          this.form.budgetId = data.budgetId;
        });
    },
    rowAttach(row, index) {
      if (!this.option.detail && this.form.updateMaterialCode !== "1") {
        return;
      }
      this.actionItem = row;
      this.actionItemIndex = index;
      this.$refs.attachRef.init(row.id, "ni_fin_payable_apply");
    },
    handleRefreshAttach() {
      getAttachList({
        businessName: "ni_fin_payable_apply",
        businessKey: this.actionItem.id,
      }).then((res) => {
        const data = res.data.data;
        this.actionItem.attach = data.map((item) => {
          return {
            label: item.originalName,
            value: item.id,
          };
        });
        this.form.items.splice(this.actionItemIndex, 1, { ...this.actionItem });
      });
    },
    handleSumAmount(amount) {
      if (["1", "3"].includes(this.form.type)) {
        this.form.costAmount = amount;
        if (
          this.form.adjust &&
          this.form.adjustId &&
          this.form.adjustType === "1" &&
          this.form.adjustAmount
        ) {
          this.form.amount = amount + Number(this.form.adjustAmount);
        } else if (
          this.form.adjust &&
          this.form.adjustId &&
          this.form.adjustType === "2" &&
          this.form.adjustAmount
        ) {
          this.form.amount = amount - Number(this.form.adjustAmount);
        } else this.form.amount = amount;
        this.form.upperAmount = numToCapital(this.form.amount);
      }
    },
    sumAmount() {
      this.$nextTick(() => {
        const itemAmount = this.form.items.reduce((acc, cur) => {
          return Number(acc) + Number(cur.amount);
        }, 0);
        this.form.amount = Number(itemAmount.toFixed(2));
        if (
          this.form.adjust &&
          this.form.adjustId &&
          this.form.adjustType === "1" &&
          this.form.adjustAmount
        ) {
          this.form.amount = itemAmount + Number(this.form.adjustAmount);
        } else if (
          this.form.adjust &&
          this.form.adjustId &&
          this.form.adjustType === "2" &&
          this.form.adjustAmount
        ) {
          this.form.amount = itemAmount - Number(this.form.adjustAmount);
        } else this.form.amount = itemAmount;
        this.form.upperAmount = numToCapital(this.form.amount);
      });
    },
    costItemDelete() {
      const indexList = this.item.selectionList.map((item) => item.$index);
      const [...items] = this.form.items.filter(
        (item, index) => !indexList.includes(index)
      );
      this.form.items = items;
      this.sumAmount();
    },
    costItemAdd() {
      if (
        this.form.type === "3" &&
        !this.form.costApplyId &&
        !this.form.budgetId
      ) {
        this.$message.warning("请选择费用/预算");
        return;
      }
      //其他合同也需要添加明细
      if (
        this.form.type === "1" &&
        this.form.contractType === "9" &&
        !this.form.budgetId
      ) {
        this.form.items.push({
          cost: 1,
          $cellEdit: true,
        });
      } else if (this.form.type === "3" && this.form.costApplyId) {
        this.$refs.costApplyItemRef.visible = true;
      } else this.$refs.porBudgetCostItemRef.init(this.form.budgetId);
    },
    handlePaySoaSubmit(selectList) {
      if (selectList && selectList.length > 0) {
        getPaySoaDetail(selectList[0].id).then((res) => {
          const itemColumn = this.findObject(this.option.column, "items");
          const alipaysColumn = this.findObject(this.option.column, "alipays");
          const data = res.data.data;
          this.form.paySoaAmount = data.amount;
          this.form.unPayAmount =
            Number(data.amount) - Number(data.payApplyAmount);
          if (this.form.adjust === 1) console.log(222);
          this.form.unPayAmount -= Number(
            this.form.adjustAmount ? this.form.adjustAmount : 0
          );
          this.form.billType = data.billType;
          this.form.taxRate = data.taxRate;
          const items = [];
          if (data.items) {
            data.items.forEach((item) => {
              items.push({
                ...item,
                porOrderItemId: item.id,
                supplierName: item.supplier,
                id: null,
              });
            });
          }
          if (data.backItems) {
            data.backItems.forEach((item) => {
              items.push({
                ...item,
                num: -Math.abs(Number(item.num)),
                amount: -Math.abs(Number(item.amount)),
                supplierName: item.supplier,
                purchaseBackItemId: item.id,
                id: null,
              });
            });
          }
          this.form.items = items;
          if (data.type === "1") {
            itemColumn.display = true;
            alipaysColumn.display = false;
          } else if (data.type === "2") {
            itemColumn.display = false;
            alipaysColumn.display = true;
          }
        });
      }
    },
    handleAmount(value) {
      if (value) {
        this.form.upperAmount = numToCapital(value);
      }
    },
    handleBudgetClear() {
      this.form.budgetId = null;
      this.form.items = [];
    },
    async handleAlipayBillSelectConfirm(selectList) {
      if (selectList && selectList.length > 0) {
        const itemRes = await getAlipayList({
          ids: selectList.map((item) => item.id).join(","),
        });
        this.form.alipayAmount = selectList.reduce((acc, cur) => {
          return Number(acc) + Number(cur.amount);
        }, 0);
        this.form.unPayAmount = this.form.alipayAmount;
        this.form.alipays = itemRes.data.data;
      }
    },
    handleCostItemSelect(selectionList) {
      this.form.items = selectionList.map((item) => {
        return {
          budgetSerialNo: item.serialNo,
          budgetId: item.budgetId,
          budgetItemId: item.id,
          materialName: item.materialName,
          materialCode: item.materialCode,
          specification: item.specification,
          quality: item.quality,
          gb: item.gb,
          unit: item.unit,
          price: item.price,
          usedNum: item.applyNum ? item.applyNum : 0,
          budgetNum: item.num,
          remark: item.remark,
          cost: item.cost,
        };
      });
    },
    dictInit() {
      const typeColumn = this.findObject(this.option.column, "type");
      const brandColumn = this.findObject(this.option.column, "brand");
      this.$http
        .get(
          "/api/blade-system/dict-biz/dictionary?code=ni_fin_payable_apply_type"
        )
        .then((res) => {
          const data = res.data.data;
          data.forEach((item) => {
            if (item.dictKey === "1" || item.dictKey === "9") {
              item.disabled = true;
            }
          });
          typeColumn.dicData = res.data.data;
        });
      this.$http
        .get("/api/blade-system/dict-biz/dictionary?code=ni_brand")
        .then((res) => {
          brandColumn.dicData = res.data.data;
        });
    },
    async handleContractIdConfirm(selectList) {
      if (selectList != null && selectList.length > 0) {
        const contract = selectList[0];
        this.form.contractAmount = contract.orderAmount
          ? contract.orderAmount
          : contract.amount;
        this.form.unPayAmount =
          Number(
            contract.unPayAmount ? contract.unPayAmount : contract.amount
          ) - Number(contract.payApplyAmount);
        if (this.form.adjust === 1)
          this.form.unPayAmount -= Number(
            this.form.adjustAmount ? this.form.adjustAmount : 0
          );
        if (this.form.contractId && this.form.type === "1") {
          const itemRes = await getItemList({
            contractId: this.form.contractId,
          });
          if (itemRes.data.data && itemRes.data.data.length > 0)
            this.form.items = itemRes.data.data
              .filter((item) => item.payType === "4")
              .map((item) => {
                return {
                  ...item,
                  price: (item.amount / item.num).toFixed(2),
                  porOrderItemId: item.id,
                  id: null,
                };
              });
        }
      }
    },
    handleFinanceConfirm() {
      this.form.financeBank = this.financeSelectionList[0].financeBank;
      this.form.financeName = this.financeSelectionList[0].financeName;
      this.form.financeAccount = this.financeSelectionList[0].financeAccount;
      this.form.payType = this.financeSelectionList[0].payType;
      this.form.bill = this.financeSelectionList[0].bill;
      this.financeVisible = false;
    },
    rowClick(row) {
      this.financeSelectionList = [row];
      this.$set(this.form, "radio", row.$index);
    },
    handleSupplierSubmit(selectList) {
      this.form.supplier = selectList[0].name;
      this.form.financeBank = null;
      this.form.financeName = null;
      this.form.financeAccount = null;
      //选择付款账户
      this.buildFinance(selectList[0].id);
    },
    buildFinance(supplierId) {
      getSupplierDetail(supplierId).then((res) => {
        const data = res.data.data;
        const finance = data.finance;
        if (finance && finance.length === 1) {
          this.form.financeBank = finance[0].financeBank;
          this.form.financeName = finance[0].financeName;
          this.form.payType = finance[0].payType;
          this.form.bill = finance[0].bill;
          this.form.financeAccount = finance[0].financeAccount;
        } else if (finance && finance.length > 1) {
          this.financeData = finance;
          this.financeVisible = true;
        }
      });
    },
    beforeContractSelect(done) {
      if (!this.form.brand) {
        this.$message({
          type: "warning",
          message: "请选择账套!",
        });
        return;
      }
      if (!this.form.supplierId) {
        this.$message({
          type: "warning",
          message: "请选择供应商!",
        });
        return;
      }
      done();
    },
    handleOrderClear() {
      this.form.porOrderSerialNo = null;
      this.form.amount = null;
      this.form.porOrderAmount = null;
      this.form.items = [];
    },
    async handleOrderSubmit(selectList) {
      this.form.currency = selectList[0].currency
        ? selectList[0].currency
        : "RMB";
      this.form.porOrderAmount = selectList.reduce((acc, cur) => {
        return acc + Number(cur.amount);
      }, 0);
      const itemRes = await getOrderItemList({
        orderIds: selectList.map((item) => item.id).join(","),
      });
      const items = itemRes.data.data;
      items.forEach(
        (item) =>
          (item.price = (Number(item.amount) / Number(item.num)).toFixed(2))
      );
      this.form.items = items;
      this.form.unPayAmount = selectList.reduce((acc, cur) => {
        return acc + Number(cur.amount) - Number(cur.payApplyAmount);
      }, 0);
      if (this.form.adjust === 1)
        this.form.unPayAmount -= Number(
          this.form.adjustAmount ? this.form.adjustAmount : 0
        );
    },
    // 获取任务详情
    getDetail(taskId, processInsId) {
      this.getTaskDetail(taskId, processInsId).then((res) => {
        const { process } = res;
        const { variables, status, processIsFinished } = process;
        this.processIsFinished = processIsFinished;
        const option = this.option;
        option.menuBtn = false;
        const { column, group } = option;
        if (status !== "todo") {
          // 已办，删除字段默认值
          //如果是需要编辑费用的编码，则需要放开编辑，
          option.detail = variables["updateMaterialCode"] !== "1";
          if (column && column.length > 0) {
            // 处理column
            column.forEach((col) => {
              if (col.type === "dynamic") {
                col.children.column.forEach((cc) => {
                  delete cc.value;
                });
              }
              if (variables["updateMaterialCode"] === "1") {
                col.disabled = true;
              }
              delete col.value;
            });
          }

          if (group && group.length > 0) {
            // 处理group
            group.forEach((gro) => {
              if (gro.column && gro.column.length > 0) {
                gro.column.forEach((col) => {
                  if (col.type === "dynamic") {
                    col.children.column.forEach((cc) => {
                      delete cc.value;
                    });
                  }
                  if (variables["updateMaterialCode"] === "1") {
                    col.disabled = true;
                  }
                  delete col.value;
                });
              }
            });
          }
        } else {
          option.detail = !(variables["updateMaterialCode"] === "1" ||
            ["recall", "reject"].includes(processIsFinished));
          let vars = [];
          column.forEach((col) => {
            if (col.value) col.value = this.getDefaultValues(col.value);
            vars.push(col.prop);
            if (
              variables["updateMaterialCode"] !== "1" &&
              !["recall", "reject"].includes(processIsFinished)
            ) {
              col.disabled = true;
            }
          });
          if (group && group.length > 0) {
            // 处理group
            group.forEach((gro) => {
              gro.column.forEach((col) => {
                if (col.value) col.value = this.getDefaultValues(col.value);
                if (
                  variables["updateMaterialCode"] !== "1" &&
                  !["recall", "reject"].includes(processIsFinished)
                ) {
                  col.disabled = true;
                }
              });
            });
          }
          this.vars = vars;
        }
        for (let key in variables) {
          if (!variables[key]) delete variables[key];
        }
        const serialNumber = this.findObject(
          this.option.column,
          "serialNumber"
        );
        if (
          option.column &&
          process.variables &&
          process.variables.serialNumber &&
          (!serialNumber || serialNumber === -1)
        ) {
          option.column.unshift({
            label: "流水号",
            prop: "serialNumber",
            span: 24,
            detail: true,
          });
        }
        this.option = option;
        this.form = variables;
        //不知道什么原因导致的明细没有加载，在没有明细，且是采购合同的情况下，重新拉一下明细
        if (
          this.form.type === "1" &&
          this.form.contractId &&
          (!this.form.items || this.form.items.length <= 0)
        ) {
          getItemList({
            contractId: this.form.contractId,
          }).then((res) => {
            this.form.items = res.data.data.map((item) => {
              return {
                ...item,
                price: (item.amount / item.num).toFixed(2),
              };
            });
          });
        }
        if (
          this.form.type === "2" &&
          this.form.paySoaId &&
          (!this.form.items || this.form.items.length <= 0)
        ) {
          this.handlePaySoaSubmit([{ id: this.form.paySoaId }]);
        }
        if (
          this.form.type === "5" &&
          (!this.form.items || this.form.items.length <= 0)
        ) {
          getItemList({
            orderId: this.form.porOrderId,
          }).then((res) => {
            this.form.items = res.data.data.map((item) => {
              return {
                ...item,
                price: (item.amount / item.num).toFixed(2),
              };
            });
          });
        }
        this.loadAttach();
        this.waiting = false;
        this.submitLoading = false;
        this.isInit = false;
        const payState = this.findObject(this.option.column, "payState");
        const payAmount = this.findObject(this.option.column, "payAmount");
        const payableDate = this.findObject(this.option.column, "payableDate");
        const payableRemark = this.findObject(
          this.option.column,
          "payableRemark"
        );
        if (this.form.payState) {
          payState.display = true;
          payableDate.display = true;
          payAmount.display = true;
          payableRemark.display = true;
        } else {
          if (!this.form.payableDate) {
            this.form.payableDate = dateFormat(new Date(), "yyyy-MM-dd");
          }
          payState.display = false;
          payableDate.display = false;
          payableRemark.display = false;
          payAmount.display = false;
        }
        const itemsColumn = this.findObject(this.option.column, "items");
        const alipayColumn = this.findObject(this.option.column, "alipays");
        itemsColumn.display = !(
          this.form.items == null || this.form.items.length === 0
        );
        alipayColumn.display = !(
          this.form.alipays == null || this.form.alipays.length === 0
        );
      });
    },
    async loadAttach() {
      this.form.attachment = [];
      if (!this.form.id) return;
      const res = await getAttachList({
        businessName: "ni_fin_payable_apply",
        businessKey: this.form.id,
      });
      const data = res.data.data;
      this.form.attachment = data.map((item) => {
        return {
          label: item.originalName,
          value: item.id,
        };
      });
    },
    // 审核
    handleExamine(pass) {
      this.submitLoading = true;
      this.$refs.form.validate(async (valid, done) => {
        if (valid) {
          const variables = {};
          this.option.column.forEach((v) => {
            if (this.form[v.prop]) variables[v.prop] = this.form[v.prop];
          });
          if (
            this.form.updateMaterialCode === "1" &&
            this.form.items &&
            this.form.items.length > 0
          ) {
            //校验编码
            const unSetMaterialCode = this.form.items.some(
              (item) => !item.materialId || item.materialId <= 0
            );
            if (
              unSetMaterialCode &&
              !["recall", "reject"].includes(this.processIsFinished) &&
              pass
            ) {
              this.$message.warning("有未设置编码的费用，请设置后再提交");
              done();
              this.submitLoading = false;
              return;
            }
          }
          //检测应付对账是否是审核状态
          if (this.form.type === "2" && this.form.paySoaId) {
            const res = await getPaySoaDetail(this.form.paySoaId);
            const { data } = res.data;
            if (!data.confirm) {
              this.$message.warning(
                "关联的应付对账未提交审核，请重新提交审核后再提交"
              );
              done();
              this.submitLoading = false;
              return;
            }
            if (Number(this.form.amount) > Number(this.form.paySoaAmount)) {
              this.$message.warning("本次付款金额大于对账金额，请重新输入");
              done();
              this.submitLoading = false;
              return;
            }
          }
          this.handleCompleteTask(pass, variables)
            .then(() => {
              this.$message.success("处理成功");
              if (this.fromPath) {
                this.handleCloseTag(this.fromPath);
              } else this.handleCloseTag("/plugin/workflow/process/todo");
            })
            .catch(() => {
              done();
              this.submitLoading = false;
            });
        } else {
          done();
          this.submitLoading = false;
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.header {
  width: 100%;
  height: 50px;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 10px 10px 0;
}
</style>

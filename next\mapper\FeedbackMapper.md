```java
// 定义包路径，属于问题反馈模块的数据访问层（Mapper）
package com.natergy.ni.feedback.mapper;

// 导入问题反馈的实体类、查询参数类和视图对象类
import com.natergy.ni.feedback.entity.FeedbackEntity;
import com.natergy.ni.feedback.params.FeedbackParams;
import com.natergy.ni.feedback.vo.FeedbackVO;
// 导入MyBatis-Plus的基础Mapper接口（封装了CRUD基础方法）
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
// 导入MyBatis-Plus的分页接口
import com.baomidou.mybatisplus.core.metadata.IPage;
// 导入Lettuce的动态注解（用于标识参数名称，兼容MyBatis的参数绑定）
import io.lettuce.core.dynamic.annotation.Param;

// 导入Java集合框架的List接口
import java.util.List;

/**
 * 问题反馈 Mapper 接口
 * （数据访问层，负责与数据库交互，定义问题反馈的查询方法）
 *
 * <AUTHOR>
 * @since 2025-02-26
 */
// 继承BaseMapper接口，泛型为问题反馈的实体类FeedbackEntity，获得基础CRUD操作能力
public interface FeedbackMapper extends BaseMapper<FeedbackEntity> {

	/**
	 * 自定义分页查询问题反馈列表
	 * （支持多条件筛选，返回视图对象VO用于前端展示）
	 *
	 * @param page            分页对象（包含页码、每页条数等分页参数，由MyBatis-Plus自动处理分页）
	 * @param feedbackParams  封装的查询参数对象（如状态、日期范围等）
	 * @param description     问题描述关键词（用于模糊查询）
	 * @param userIdsStr      用户ID列表字符串（用分隔符拼接，如"1,2,3"，用于筛选特定用户相关的问题）
	 * @return 问题反馈视图对象列表（List<FeedbackVO>），包含分页信息
	 */
	List<FeedbackVO> selectFeedbackPage(
		IPage page, 
		@Param("feedbackParams") FeedbackParams feedbackParams, 
		@Param("description") String description, 
		@Param("userIdsStr") String userIdsStr
	);

}
```

### 接口功能说明

该接口是问题反馈模块的**数据访问层（Mapper）**，基于 MyBatis-Plus 的`BaseMapper`扩展，主要负责与数据库交互，定义问题反馈的查询方法，特别是支持复杂条件的分页查询。

#### 核心方法解析

- **继承基础功能**：通过继承`BaseMapper<FeedbackEntity>`，自动获得`insert`（新增）、`update`（更新）、`delete`（删除）、`selectById`（根据 ID 查询）等基础 CRUD 方法，无需手动编写 SQL。

- **自定义分页查询**：`selectFeedbackPage`是核心自定义方法，用于满足复杂业务场景的查询需求：

  - 参数说明

    ：

    - `page`：MyBatis-Plus 的`IPage`对象，用于传递分页参数（页码、每页条数），并接收查询后的总条数等分页信息。
    - `feedbackParams`：封装了业务相关的查询条件（如问题状态、创建时间范围等），通过`@Param`注解指定参数名称，便于在 XML 映射文件中引用。
    - `description`：问题描述的关键词，支持模糊查询（如搜索包含特定文本的问题）。
    - `userIdsStr`：用户 ID 的字符串列表（如 “1,2,3”），用于筛选与特定用户相关的问题（如负责人、创建人等）。

  - **返回值**：`List<FeedbackVO>`，即查询到的问题反馈视图对象列表，包含前端展示所需的完整信息（如关联的解决记录、部门名称等，由 VO 封装）。

#### 实现方式

该接口的具体 SQL 实现通常放在对应的 XML 映射文件（如`FeedbackMapper.xml`）中，通过`selectFeedbackPage`方法的 ID 关联，编写动态 SQL 语句，根据传入的参数构建查询条件（如`WHERE`子句中的状态筛选、模糊查询、用户 ID 匹配等），并通过 MyBatis-Plus 的分页插件自动实现分页逻辑。

#### 作用

作为服务层（Service）与数据库之间的桥梁，`FeedbackMapper`封装了数据访问逻辑，服务层通过调用该接口的方法获取数据，无需直接处理 SQL，降低了业务逻辑与数据访问的耦合度。同时，自定义分页方法支持多条件组合查询，满足前端页面的筛选、搜索等交互需求。



```xml
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- 定义Mapper接口的命名空间，与FeedbackMapper接口的全类名对应 -->
<mapper namespace="com.natergy.ni.feedback.mapper.FeedbackMapper">

    <!-- 通用查询映射结果：定义数据库字段与实体类属性的映射关系 -->
    <resultMap id="feedbackResultMap" type="com.natergy.ni.feedback.entity.FeedbackEntity">
        <result column="apply_user_id" property="applyUserId"/>          <!-- 申请人ID -->
        <result column="apply_user_name" property="applyUserName"/>      <!-- 申请人姓名 -->
        <result column="responsibility" property="responsibility"/>      <!-- 负责人ID列表 -->
        <result column="description" property="description"/>            <!-- 问题描述 -->
        <result column="feedback_category" property="feedbackCategory"/>  <!-- 问题分类 -->
        <result column="feedback_collection" property="feedbackCollection"/>  <!-- 问题合集 -->
        <result column="is_ten_non_neglect" property="isTenNonNeglect"/>  <!-- 是否属于"十不放过" -->
        <result column="ai_guidance" property="aiGuidance"/>             <!-- AI建议 -->
        <result column="final_resolution_time" property="finalResolutionTime"/>  <!-- 最终解决时间 -->
        <result column="final_report" property="finalReport"/>           <!-- 最终报告 -->
        <result column="process_instance_id" property="processInstanceId"/>  <!-- 流程实例ID -->
        <result column="status" property="status"/>                      <!-- 问题状态 -->
        <result column="id" property="id"/>                              <!-- 主键ID -->
        <result column="update_time" property="updateTime"/>              <!-- 更新时间 -->
        <result column="create_dept" property="createDept"/>              <!-- 创建部门 -->
        <result column="tenant_id" property="tenantId"/>                  <!-- 租户ID -->
        <result column="create_user" property="createUser"/>              <!-- 创建人ID -->
        <result column="update_user" property="updateUser"/>              <!-- 更新人ID -->
        <result column="create_time" property="createTime"/>              <!-- 创建时间 -->
        <result column="is_deleted" property="isDeleted"/>                <!-- 是否删除（逻辑删除标识） -->
    </resultMap>

    <!-- 自定义分页查询：对应FeedbackMapper接口的selectFeedbackPage方法 -->
    <select id="selectFeedbackPage" resultMap="feedbackResultMap">
        <!-- 基础查询语句：查询未删除的问题反馈记录 -->
        select * from ni_feedback as fb where is_deleted = 0

        <!-- 条件1：创建时间筛选（支持单日期或日期范围） -->
        <if test="feedbackParams.createDate!=null and feedbackParams.createDate.size()!=0">
            <if test="feedbackParams.createDate.size()==1">
                <!-- 单日期：查询当天00:00:00至23:59:59的数据 -->
                AND (fb.create_time &gt;= CONCAT( #{feedbackParams.createDate[0]}, ' 00:00:00') 
                     AND fb.create_time &lt;= CONCAT( #{feedbackParams.createDate[0]}, ' 23:59:59'))
            </if>
            <if test="feedbackParams.createDate.size()==2">
                <!-- 日期范围：查询起始日期00:00:00至结束日期23:59:59的数据 -->
                AND (fb.create_time &gt;= CONCAT( #{feedbackParams.createDate[0]}, ' 00:00:00') 
                     AND fb.create_time &lt;= CONCAT( #{feedbackParams.createDate[1]}, ' 23:59:59'))
            </if>
        </if>

        <!-- 条件2：问题状态筛选（支持多状态） -->
        <if test="feedbackParams.feedbackStatuss!=null and feedbackParams.feedbackStatuss.size()!=0">
            AND fb.status IN
            <!-- 遍历状态列表，生成IN条件（如status IN (1,2,3)） -->
            <foreach collection="feedbackParams.feedbackStatuss" item="status" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>

        <!-- 条件3：向量点ID筛选（支持多点ID） -->
        <if test="feedbackParams.pointIds!=null and feedbackParams.pointIds.size()!=0">
            AND fb.point_id IN
            <!-- 遍历点ID列表，生成IN条件 -->
            <foreach collection="feedbackParams.pointIds" item="pointId" open="(" separator="," close=")">
                #{pointId}
            </foreach>
        </if>

        <!-- 条件4：是否属于"十不放过"筛选 -->
        <if test="feedbackParams.isTenNonNeglect!=null">
            <if test="feedbackParams.isTenNonNeglect==true">
                AND fb.is_ten_non_neglect = 1  <!-- 是"十不放过" -->
            </if>
            <if test="feedbackParams.isTenNonNeglect==false">
                AND fb.is_ten_non_neglect = 0  <!-- 不是"十不放过" -->
            </if>
        </if>

        <!-- 条件5：负责人或申请人筛选（通过feedbackParams.responsibilityOrOwer区分） -->
        <if test="feedbackParams.responsibilityOrOwer==1">
            <!-- 筛选条件：当前用户是负责人之一 -->
            <if test="feedbackParams.canAllUser==false and userIdsStr!=null and userIdsStr!=''">
                <!-- 使用子查询和字符串拆分，判断用户ID是否在负责人列表中 -->
                AND EXISTS (
                    SELECT 1
                    FROM STRING_SPLIT(#{userIdsStr}, ',') AS p_uuids  <!-- 拆分用户ID字符串为多行 -->
                    WHERE ',' + responsibility + ',' LIKE '%,' + p_uuids.value + ',%'  <!-- 模糊匹配，避免部分匹配 -->
                )
            </if>
        </if>
        <if test="feedbackParams.responsibilityOrOwer==2">
            <!-- 筛选条件：当前用户是申请人 -->
            AND fb.apply_user_id = #{feedbackParams.currentUserId}
        </if>

        <!-- 条件6：问题描述全文搜索（使用FREETEXT全文索引） -->
        <if test="description!=null and description!=''">
            AND FREETEXT((fb.Description), #{description})
        </if>

        <!-- 排序逻辑 -->
        ORDER BY
        <if test="feedbackParams.sortBy==2">
            fb.create_time ASC  <!-- 按创建时间升序（旧→新） -->
        </if>
        <if test="feedbackParams.sortBy!=2">
            fb.create_time DESC  <!-- 按创建时间降序（新→旧，默认） -->
        </if>

    </select>

</mapper>
```

### XML 映射文件说明

该文件是`FeedbackMapper`接口的 SQL 实现，定义了问题反馈的查询逻辑，通过动态 SQL 支持多条件组合筛选，主要用于分页查询问题反馈列表。

#### 核心组件解析

1. **resultMap（结果映射）**：
   - `id="feedbackResultMap"`：唯一标识该映射关系。
   - `type="FeedbackEntity"`：指定映射的目标实体类。
   - 子标签`<result>`：定义数据库字段（`column`）与实体类属性（`property`）的映射，解决字段名与属性名可能存在的不一致问题（如数据库用下划线`apply_user_id`，实体类用小驼峰`applyUserId`）。
2. **select 标签（查询语句）**：
   - `id="selectFeedbackPage"`：与`FeedbackMapper`接口的`selectFeedbackPage`方法对应，MyBatis 通过 ID 关联接口方法与 SQL。
   - `resultMap="feedbackResultMap"`：指定查询结果使用上述定义的映射关系封装为`FeedbackEntity`对象。

#### 动态 SQL 逻辑说明

该查询通过`<if>`和`<foreach>`等标签实现动态条件拼接，支持多场景筛选：

| 筛选条件                | 实现逻辑                                                     |
| ----------------------- | ------------------------------------------------------------ |
| **创建时间**            | 支持单日期（当天）或日期范围查询，通过`CONCAT`拼接时间字符串补全时分秒。 |
| **问题状态**            | 用`<foreach>`遍历状态列表，生成`IN`条件（如`status IN (1,2)`）。 |
| **向量点 ID**           | 类似状态筛选，遍历点 ID 列表生成`IN`条件。                   |
| **十不放过标识**        | 根据布尔值匹配`is_ten_non_neglect`字段（1 为是，0 为否）。   |
| **负责人 / 申请人筛选** | - 负责人：通过`STRING_SPLIT`拆分用户 ID 字符串，用`LIKE`模糊匹配负责人列表（避免部分匹配）。 - 申请人：直接匹配`apply_user_id`。 |
| **描述全文搜索**        | 使用`FREETEXT`函数进行全文检索（需数据库支持全文索引）。     |
| **排序**                | 根据`sortBy`参数决定按创建时间升序（`ASC`）或降序（`DESC`，默认）。 |

#### 技术亮点

- **防部分匹配**：负责人筛选时用`,+字段+,`和`%,+值+,%`的模糊匹配（如`,1,2,3,`匹配`,2,`），避免误判（如 ID=12 匹配 ID=1）。
- **兼容性**：通过动态 SQL 适配不同查询场景，减少冗余 SQL，一个方法支持多种筛选组合。
- **性能优化**：使用`EXISTS`子查询而非`JOIN`，减少数据关联开销；条件判断前置，避免无效查询。

该 XML 文件是问题反馈模块数据查询的核心，通过灵活的动态 SQL 满足前端多样化的筛选需求，同时确保查询效率和结果准确性。
import request from "@/router/axios";

export const getDetail = (id) => {
  return request({
    url: "/api/ni/old/xiaoShouDingDan/detail",
    method: "get",
    params: {
      id,
    },
  });
};
export const getShippingList = (current, size, params) => {
  return request({
    url: "/api/ni/old/xiaoShouDingDan/shippingList",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const list = (current, size, params) => {
  return request({
    url: "/api/ni/old/xiaoShouDingDan/list",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const dingDanMingXiList = (orderId) => {
  return request({
    url: "/api/ni/old/xiaoShouDingDan/dingDanMingXiList",
    method: "get",
    params: {
      orderId,
    },
  });
};

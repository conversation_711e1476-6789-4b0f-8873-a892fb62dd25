/**
 * 全局配置文件
 */
export default {
  title: process.env.VUE_APP_TITLE || "NI",
  logo: process.env.VUE_APP_LOGO || "NI",
  key: process.env.VUE_APP_KEY||"saber", //配置主键,目前用于存储
  indexTitle: process.env.VUE_APP_INDEX_TITLE || "Natergy Integration",
  fullTitle: process.env.VUE_APP_FULL_TITLE || "山东能特异能源科技有限公司",
  clientId: "saber", // 客户端id
  clientSecret: "saber_secret", // 客户端密钥
  tenantMode: true, // 是否开启租户模式
  tenantId: "000000", // 管理组租户编号
  tenantHeader: "tenant",
  tenants: [
    {
      label: "山东能特异能源科技有限公司",
      value: "000000",
    },
    {
      label: "淄博高新区联信智能制造研究院",
      value: "460198",
    },
  ], //默认的租户列表
  captchaMode: false, // 是否开启验证码模式
  switchMode: false, // 是否开启部门切换模式
  lockPage: "/lock",
  tokenTime: 3000,
  tokenHeader: "Blade-Auth",
  //http的status默认放行列表
  statusWhiteList: [],
  //配置首页不可关闭
  isFirstPage: false,
  fistPage: {
    label: "首页",
    value: "/wel/index",
    params: {},
    query: {},
    meta: {
      i18n: "dashboard",
    },
    group: [],
    close: false,
  },
  //配置菜单的属性
  menu: {
    iconDefault: "iconfont icon-caidan",
    props: {
      label: "name",
      path: "path",
      icon: "source",
      children: "children",
    },
  },
  // 第三方系统授权地址
  authUrl: "http://localhost/blade-auth/oauth/render",
  // 报表设计器地址(cloud端口为8108,boot端口为80)
  reportUrl: "http://**************:8888/ureport",
  // 单点登录系统认证(blade-auth服务的地)
  ssoUrl:
    "http://localhost:8100/oauth/authorize?client_id=saber&response_type=code&redirect_uri=",
  // 单点登录回调地址(Saber服务的地址)
  redirectUri: "http://localhost:1888",
  //预览文件地址
  fileViewUrl: "/kkf/onlinePreview?url=",
  //研究院网址
  instituteUrl: process.env.VUE_APP_INSTITUTE_URL,
};

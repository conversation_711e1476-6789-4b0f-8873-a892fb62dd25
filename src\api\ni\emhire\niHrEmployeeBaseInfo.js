import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/employeeHireRecord/employeeHireRecord/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/employeeHireRecord/employeeHireRecord/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/employeeHireRecord/employeeHireRecord/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/employeeHireRecord/employeeHireRecord/save',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/employeeHireRecord/employeeHireRecord/submit',
    method: 'post',
    data: row
  })
}

export const fullInfo = (employeeId) => {
  return request({
    url: '/api/employeeHireRecord/employeeHireRecord/full',
    method: 'get',
    params: {
      employeeId
    }
  })
}
export const queryEmployeeByName = (query) => {
  return request({
    url: '/api/employeeHireRecord/employeeHireRecord/filter',
    method: 'get',
    params: {
      name : query
    }
  })
}
export const rewrite = (id) => {
  return request({
    url: '/api/employeeHireRecord/employeeHireRecord/rewrite',
    method: 'post',
    params: {
      id
    }
  })
}

export const invite = (inviteName,invitePhone) => {
  return request({
    url: '/api/employeeHireRecord/employeeHireRecord/invite',
    method: 'post',
    params: {
      inviteName: inviteName,
      invitePhone: invitePhone
    }
  })
}



import request from "@/router/axios";

export const getList = (current, size, params) => {
  return request({
    url: "/api/ni/fin/paySoa/list",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/ni/fin/paySoa/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const remove = (ids) => {
  return request({
    url: "/api/ni/fin/paySoa/remove",
    method: "post",
    params: {
      ids,
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/ni/fin/paySoa/submit",
    method: "post",
    data: row,
  });
};

export const update = (row) => {
  return request({
    url: "/api/ni/fin/paySoa/submit",
    method: "post",
    data: row,
  });
};
export const confirm = (ids) => {
  return request({
    url: "/api/ni/fin/paySoa/confirm",
    method: "post",
    params: {
      ids,
    },
  });
};
export const confirmBack = (ids) => {
  return request({
    url: "/api/ni/fin/paySoa/confirmBack",
    method: "post",
    params: {
      ids,
    },
  });
};
/**
 * 在原有对账单基础上补充对账单
 * @param id
 * @param data
 * @returns {*}
 */
export const addTo = (id, data) => {
  return request({
    url: "/api/ni/fin/paySoa/addTo",
    method: "post",
    params: {
      id,
    },
    data,
  });
};

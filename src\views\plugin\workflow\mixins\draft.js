import { getDetail, submit } from "@/api/plugin/workflow/draft";

export default {
  methods: {
    initDraft(processDefId, processKey) {
      return new Promise((resolve) => {
        getDetail({ processDefId, platform: "pc", processKey }).then((res) => {
          const { data } = res.data;
          if (data && Array.isArray(data) && data.length > 0) {
            resolve(data);
          } else {
            resolve([]);
          }
        });
      });
    },
    handleDraft(processDefId, formKey, variables, processKey) {
      this.$prompt("请输入草稿标题:", "保存至草稿箱并关闭", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        inputPlaceholder: "请输入草稿标题",
        inputValidator: (value) => {
          if (!value) {
            return "草稿标题不能为空";
          }
          return true;
        },
      })
        .then(({ value }) => {
          const draftTitle = value;
          submit({
            processDefId,
            formKey,
            processKey,
            variables: JSON.stringify(variables),
            platform: "pc",
            draftTitle,
          }).then(() => {
            this.handleCloseTag("/plugin/workflow/process/start");
          });
        })
        .catch(() => {});
    },
    handleDraftNotClose(processDefId, formKey, variables, processKey) {
      this.$prompt("请输入草稿标题:", "保存至草稿箱并关闭", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        inputPlaceholder: "请输入草稿标题",
        inputValidator: (value) => {
          if (!value) {
            return "草稿标题不能为空";
          }
          return true;
        },
      })
        .then(({ value }) => {
          const draftTitle = value;
          submit({
            processDefId,
            formKey,
            processKey,
            variables: JSON.stringify(variables),
            platform: "pc",
            draftTitle,
          }).then(() => {
            this.$message({
              type: "success",
              message: "保存成功!",
            });
            console.log(processDefId, processKey);
            this.initDraft(processDefId, processKey).then((data) => {
              this.draftCount = data.length;
              this.draftList = data;
            });
          });
        })
        .catch(() => {});
    },
  },
};

import request from "@/router/axios";

/**
 * 获取公司列表
 * @param tenantId
 * @returns {AxiosPromise}
 */
export const getCompanyItem = () => {
  return request({
    url: "/api/blade-system/dict/dictionary?code=natergy_company",
    method: "get",
  });
};
/**
 * 获取用户列表
 * @returns {AxiosPromise}
 */
export const getUserItem = () => {
  return request({
    url: "/api/blade-user/get-user-info",
    method: "get",
  });
};
/**
 * 获取考勤列表
 * @returns {AxiosPromise}
 */
export const getZkEccItem = () => {
  return request({
    url: "/api/ni/pa/paAttUserInfo/badge-number-list",
    method: "get",
  });
};
/**
 * 获取政治面貌
 * @returns {AxiosPromise}
 */
export const getPoliticalItem = () => {
  return request({
    url: "/api/blade-system/dict-biz/dictionary?code=ni_pa_political_status",
    method: "get",
  });
};
/**
 * 获取学历级别
 * @returns {AxiosPromise}
 */
export const getEduItem = () => {
  return request({
    url: "/api/blade-system/dict-biz/dictionary-tree?code=ni_pa_eduLevel",
    method: "get",
  });
};
/**
 * 获取部门
 * @returns {AxiosPromise}
 */
export const getDeptItem = () => {
  return request({
    url: "/api/blade-system/dept/options",
    method: "get",
  });
};




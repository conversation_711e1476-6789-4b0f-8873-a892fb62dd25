import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/ni/por/purchaseBack/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/ni/por/purchaseBack/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/ni/por/purchaseBack/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/ni/por/purchaseBack/save',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/ni/por/purchaseBack/update',
    method: 'post',
    data: row
  })
}

export const submit = (ids) => {
  return request({
    url: '/api/ni/por/purchaseBack/submit',
    method: 'post',
    params: {
      ids,
    }
  })
}
export const back = (ids) => {
  return request({
    url: '/api/ni/por/purchaseBack/back',
    method: 'post',
    params: {
      ids,
    }
  })
}


export const toVoid = (ids) => {
  return request({
    url: '/api/ni/por/purchaseBack/toVoid',
    method: 'post',
    params: {
      ids,
    }
  })
}

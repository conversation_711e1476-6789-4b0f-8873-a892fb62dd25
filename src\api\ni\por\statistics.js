import request from "@/router/axios";

export const getPorOrderTotalAmount = (startDate, endDate) => {
  return request({
    url: "/api/ni/por/statistics/totalAmount",
    method: "get",
    params: {
      startDate,
      endDate,
    },
  });
};
export const getPorOrderTotal = (startDate, endDate) => {
  return request({
    url: "/api/ni/por/statistics/orderTotal",
    method: "get",
    params: {
      startDate,
      endDate,
    },
  });
};
export const getPorOrderItemTotal = (startDate, endDate) => {
  return request({
    url: "/api/ni/por/statistics/orderItemTotal",
    method: "get",
    params: {
      startDate,
      endDate,
    },
  });
};
export const getPorBackTotal = (startDate, endDate) => {
  return request({
    url: "/api/ni/por/statistics/backTotal",
    method: "get",
    params: {
      startDate,
      endDate,
    },
  });
};
export const getPorOrderLineData = (startDate, endDate) => {
  return request({
    url: "/api/ni/por/statistics/orderLineData",
    method: "get",
    params: {
      startDate,
      endDate,
    },
  });
};

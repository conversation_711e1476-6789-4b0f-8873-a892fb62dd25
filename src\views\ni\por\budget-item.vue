<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :search.sync="query"
      :data="data"
      :page.sync="page"
      v-model="form"
      ref="crud"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
      :cell-style="cellStyle"
    >
      <template #projectSerialNo="{ row, index }">
        <span>{{ row.projectSerialNo }}</span>
        <span v-if="row.year === '1' && row.subCode">-{{ row.subCode }}</span>
      </template>
      <template #serialNo="{ row, index }">
        <flow-timeline-popover
          v-if="row.processInsId"
          :process-ins-id="row.processInsId"
          :form-key="
            row.repair
              ? repairFormKey
              : row.year === '2'
              ? yearFormKey
              : formKey
          "
          :process-def-key="
            row.repair
              ? repairProcessDefKey
              : row.year === '2'
              ? yearProcessDefKey
              : processDefKey
          "
          v-model="row.serialNo"
          trigger="click"
          lazy
        />
        <span v-else>{{ row.serialNo }}</span>
        <span v-if="row.year === '2' && row.subCode">-{{ row.subCode }}</span>
      </template>
      <template #subCode="{ row, index }">
        <span v-if="row.subCode">[{{ row.subCode }}]{{ row.subName }}</span>
      </template>
      <template #menu="{ row, index, size }">
        <el-button
          type="text"
          icon="el-icon-download"
          :size="size"
          @click="rowAttach(row)"
          >附件
        </el-button>
      </template>
      <template #menuLeft>
        <el-dropdown
          @command="handlePorApply"
          v-if="permission.ni_por_budget_item_por_apply"
        >
          <el-button type="primary" size="mini" icon="el-icon-plus" plain
            >采购申请<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
              v-for="(type, index) in typeDict"
              :key="index"
              :command="type.dictKey"
              v-if="permission[`porapply_apply_${type.dictKey}`]"
            >
              {{ type.dictValue }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-button
          type="warning"
          size="mini"
          icon="el-icon-share"
          @click="handleSub"
          >分配小项
        </el-button>
        <!-- <el-button
          type="success"
          size="mini"
          icon="el-icon-document-copy"
          plain
          @click="handleCopy"
          >复制
        </el-button> -->
        <el-button
          type="success"
          size="mini"
          icon="el-icon-download"
          plain
          @click="handleExport"
          >导出
        </el-button>
        <el-divider direction="vertical"></el-divider>
        <el-radio-group v-model="brand" size="mini" @input="onLoad(page)">
          <el-radio-button label="natergy">能特异</el-radio-button>
          <el-radio-button label="yy">演绎</el-radio-button>
        </el-radio-group>
        <el-divider direction="vertical" />
        <el-checkbox v-model="unFinish">未完成</el-checkbox>
        <el-checkbox v-model="pv">压力容器</el-checkbox>
      </template>
      <template #cost="{ row, index }">
        <el-tag size="mini" type="danger" effect="dark" v-if="row.cost">
          费用
        </el-tag>
        <el-tag size="mini" type="info" effect="plain" v-else> 实物</el-tag>
      </template>
      <template #title="{ row, index, size }">
        <el-tag v-if="row.over" :size="size" type="danger" effect="plain">
          超预算
        </el-tag>
        <el-tag v-if="row.year === '2'" size="mini" type="danger">
          {{ row.yearDate }}
        </el-tag>
        <span>{{ row.title }}</span>
        <el-tag v-if="row.repair" type="danger" size="mini">增补</el-tag>
      </template>
      <template #type="{ row, disabled, size, index }">
        <el-tag v-if="row.type === 'SC'" :size="size" effect="dark"
          >{{ row.$type }}
        </el-tag>
        <el-tag
          v-else-if="row.type === 'SZ'"
          :size="size"
          type="success"
          effect="dark"
          >{{ row.$type }}
        </el-tag>
        <el-tag
          v-else-if="row.type === 'GW'"
          :size="size"
          type="info"
          effect="dark"
          >{{ row.$type }}
        </el-tag>
        <el-tag
          v-else-if="row.type === 'SW'"
          :size="size"
          type="warning"
          effect="dark"
          >{{ row.$type }}
        </el-tag>
        <el-tag
          v-else-if="row.type === 'ZL'"
          :size="size"
          type="danger"
          effect="dark"
          >{{ row.$type }}
        </el-tag>
        <el-tag v-else-if="row.type === 'DQ'" :size="size" effect="plain"
          >{{ row.$type }}
        </el-tag>
        <el-tag
          v-else-if="row.type === 'RC'"
          :size="size"
          type="success"
          effect="plain"
          >{{ row.$type }}
        </el-tag>
        <el-tag
          v-else-if="row.type === 'JY'"
          :size="size"
          type="info"
          effect="plain"
          >{{ row.$type }}
        </el-tag>
        <el-tag
          v-else-if="row.type === 'GN'"
          :size="size"
          type="warning"
          effect="plain"
          >{{ row.$type }}
        </el-tag>
        <el-tag
          v-else-if="row.type === 'WL'"
          :size="size"
          type="danger"
          effect="plain"
          >{{ row.$type }}
        </el-tag>
        <el-tag v-else-if="row.type === 'AH'" :size="size"
          >{{ row.$type }}
        </el-tag>
        <el-tag v-else-if="row.type === 'QG'" :size="size" type="info"
          >{{ row.$type }}
        </el-tag>
        <el-tag v-else-if="row.type === 'JM'" :size="size" type="warning"
          >{{ row.$type }}
        </el-tag>
        <el-tag v-else :size="size" type="danger">{{ row.$type }}</el-tag>
      </template>
      <template #brand="{ row, disabled, size, index }">
        <el-tag
          v-if="row.brand"
          :size="size"
          :effect="row.brand === '1' ? 'dark ' : 'plain'"
        >
          {{ row.$brand }}
        </el-tag>
      </template>
      <template #budgetStatus="{ row, index }">
        <el-tag v-if="row.budgetStatus === 0" size="mini" type="info">
          {{ row.$budgetStatus }}
        </el-tag>
        <el-tag v-else-if="row.budgetStatus === 1" size="mini" effect="plain">
          {{ row.$budgetStatus }}
        </el-tag>
        <el-tag v-else-if="row.budgetStatus === 2" size="mini" effect="plain">
          {{ row.$budgetStatus }}
        </el-tag>
        <el-tag v-else-if="row.budgetStatus === 3" size="mini" type="danger">
          {{ row.$budgetStatus }}
        </el-tag>
        <el-tag
          v-else-if="row.budgetStatus === 4"
          size="mini"
          type="warning"
          effect="plain"
        >
          {{ row.$budgetStatus }}
        </el-tag>
        <el-tag
          v-else-if="row.budgetStatus === 5"
          size="mini"
          type="warning"
          effect="plain"
        >
          {{ row.$budgetStatus }}
        </el-tag>
        <el-tag
          v-else-if="row.budgetStatus === 6"
          size="mini"
          type="danger"
          effect="plain"
        >
          {{ row.$budgetStatus }}
        </el-tag>
        <el-tag
          v-else-if="row.budgetStatus === 9"
          size="mini"
          type="success"
          effect="plain"
        >
          {{ row.$budgetStatus }}
        </el-tag>
      </template>
      <template #amount="{ row, index, size }">
        <span style="color: green; font-weight: bolder">{{
          Number(row.amount).toLocaleString("zh-CN", {
            minimumFractionDigits: 2,
          })
        }}</span>
      </template>
      <template #orderNum="{ row, index, size }">
        <span v-if="row.orderNum">
          {{
            Number(row.orderNum).toLocaleString("zh-CN", {
              minimumFractionDigits: 2,
            })
          }}
        </span>
      </template>
      <template #orderAmount="{ row, index, size }">
        <span v-if="row.orderAmount">{{
          Number(row.orderAmount).toLocaleString("zh-CN", {
            minimumFractionDigits: 2,
          })
        }}</span>
      </template>
      <template #pv="{ row, index }">
        <el-tag size="mini" v-if="row.pv" type="danger" effect="dark">
          是
        </el-tag>
        <el-tag size="mini" v-else type="info" effect="plain">否</el-tag>
      </template>
      <template #price="{ row, size }">
        <span>{{
          (Number(row.amount) / Number(row.num)).toLocaleString("zh-CN", {
            minimumFractionDigits: 2,
          })
        }}</span>
      </template>
      <template #used="{ row, index, size }">
        <el-tag v-if="row.used" size="mini">是</el-tag>
        <el-tag v-else size="mini" type="info">否</el-tag>
      </template>
    </avue-crud>
    <attach-dialog
      ref="attachDialogRef"
      code="public"
      :del-btn="action.budgetStatus === 0"
    />
    <el-dialog
      title="修改小项"
      append-to-body
      :visible.sync="sub.visible"
      width="500px"
    >
      <avue-form
        v-if="sub.visible"
        :option="sub.option"
        v-model="sub.form"
        @submit="handleSubSubmit"
      >
      </avue-form>
    </el-dialog>
  </basic-container>
</template>

<script>
import { changeSubCode, getPage } from "@/api/ni/por/budget-item";
import UserSelect from "@/components/user-select";
import ProjectSelect from "@/views/ni/project/components/ProjectSelect";
import exForm from "@/views/plugin/workflow/mixins/ex-form";
import AttachDialog from "@/components/attach-dialog";
import { mapGetters } from "vuex";
import FlowTimelinePopover from "@/components/wf-flow-timeline/popover";

export default {
  mixins: [exForm],
  components: {
    UserSelect,
    ProjectSelect,
    AttachDialog,
    FlowTimelinePopover,
  },
  data() {
    return {
      module: "ni_por_budget_item",
      formKey: "wf_ex_ProjectBudget",
      processDefKey: "process_project_budget",
      yearFormKey: "wf_ex_por/BudgetYear",
      yearProcessDefKey: "process_project_budget_year",
      repairFormKey: "wf_ex_por/BudgetRepair",
      repairProcessDefKey: "process_project_budget_repair",
      exportList: "", //数据导出列表
      exportData: [], //数据导出存储
      budgetStatusDict: [],
      budgetStatusDictKeyValue: {},
      typeDict: [],
      typeDictKeyValue: {},
      form: {},
      query: {},
      unitDict: [],
      unitDictKeyValue: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        searchEnter: true,
        editBtn: false,
        delBtn: false,
        selection: true,
        align: "center",
        searchLabelWidth: 110,
        searchIndex: 3,
        searchIcon: true,
        addBtn: false,
        menuWidth: 70,
        size: "mini",
        searchSize: "mini",
        height: "auto",
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        dialogClickModal: false,
        column: [
          {
            label: "状态",
            prop: "budgetStatus",
            dicData: [],
            search: true,
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            dataType: "number",
            display: false,
            type: "select",
            width: 69,
          },
          {
            label: "申请人",
            prop: "createUser",
            remote: true,
            dicUrl: `/api/blade-user/search/otj/user?name={{key}}`,
            props: {
              label: "realName",
              value: "id",
            },
            dicFormatter: (data) => {
              return data.data.records;
            },
            placeholder: " ",
            filterable: true,
            type: "select",
            overHidden: true,
            hide: true,
            showColumn: false,
            search: true,
          },
          {
            label: "申请人",
            prop: "createUserName",
            addDisplay: false,
            editDisplay: false,
            width: 59,
          },
          {
            label: "申请部门",
            prop: "createDeptName",
            hide: true,
            addDisplay: false,
            editDisplay: false,
            overHidden: true,
          },
          {
            label: "申请时间",
            prop: "createTime",
            type: "datetime",
            minWidth: 100,
            display: false,
            overHidden: true,
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
          },
          {
            label: "预算名称",
            prop: "title",
            search: true,
            minWidth: 110,
            overHidden: true,
          },
          {
            label: "预算编号",
            prop: "serialNo",
            search: true,
            searchOrder: 98,
            overHidden: true,
            width: 86,
          },
          {
            label: "预算类型",
            prop: "type",
            search: true,
            placeholder: " ",
            type: "select",
            filterable: true,
            width: 80,
            dicData: [],
            props: {
              label: "name",
              value: "code",
              desc: "code",
            },
            overHidden: true,
          },
          {
            label: "年度预算",
            prop: "year",
            labelTip: "年度预算可以使用多次",
            type: "radio",
            dicUrl: "/api/blade-system/dict/dictionary?code=yes_no",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            search: true,
            hide: true,
          },
          {
            label: "小项",
            prop: "subCode",
            overHidden: true,
            type: "select",
            dicData: [],
            props: {
              label: "name",
              value: "code",
              desc: "code",
            },
            placeholder: " ",
          },
          {
            label: "物/费",
            prop: "cost",
            type: "select",
            dicData: [
              {
                label: "费用",
                value: true,
              },
              {
                label: "实物",
                value: false,
              },
            ],
            placeholder: " ",
            width: 57,
            disabled: true,
          },
          {
            label: "品名",
            prop: "materialName",
            minWidth: 90,
            overHidden: true,
            search: true,
            searchOrder: 97,
          },
          {
            label: "用途",
            prop: "purpose",
            type: "textarea",
            search: true,
            minRows: 1,
            placeholder: " ",
            overHidden: true,
            cell: true,
            searchOrder: 96,
            minWidth: 90,
          },
          {
            label: "编码",
            prop: "materialCode",
            minWidth: 90,
            overHidden: true,
            search: true,
            searchOrder: 95,
          },
          {
            label: "规格型号",
            overHidden: true,
            prop: "specification",
            minWidth: 90,
            search: true,
            searchOrder: 94,
          },
          {
            label: "材质",
            prop: "quality",
            minWidth: 80,
            overHidden: true,
          },
          {
            label: "国标",
            overHidden: true,
            prop: "gb",
            minWidth: 70,
          },
          {
            label: "单位",
            prop: "unit",
            overHidden: true,
            type: "select",
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_material_unit",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            width: 45,
            slot: true,
          },
          {
            label: "数量",
            prop: "num",
            type: "number",
            placeholder: " ",
            minWidth: 90,
          },
          {
            label: "单价",
            prop: "price",
            type: "number",
            disabled: true,
            minWidth: 95,
            controls: false,
            precision: 2,
            placeholder: " ",
          },
          {
            label: "金额",
            prop: "amount",
            overHidden: true,
            controls: false,
            type: "number",
            minWidth: 95,
            precision: 2,
            placeholder: " ",
          },
          {
            label: "已采购数量",
            prop: "orderNum",
            type: "number",
            placeholder: " ",
            minWidth: 90,
          },
          {
            label: "已采购金额",
            prop: "orderAmount",
            overHidden: true,
            controls: false,
            type: "number",
            minWidth: 95,
            precision: 2,
            placeholder: " ",
          },
          {
            label: "备注",
            prop: "remark",
            minWidth: 110,
            overHidden: true,
          },
          {
            label: "项目名称",
            prop: "projectId",
            type: "select",
            remote: true,
            dicUrl: "/api/ni/project/page?status=9&keyword={{key}}",
            props: {
              label: "title",
              value: "id",
              desc: "serialNo",
            },
            dicFormatter: (data) => {
              return data.data.records;
            },
            placeholder: " ",
            hide: true,
            showColumn: false,
            search: true,
            searchOrder: 95,
          },
          {
            label: "项目名称",
            prop: "projectTitle",
            overHidden: true,
            minWidth: 90,
          },
          {
            label: "项目编号",
            prop: "projectSerialNo",
            overHidden: true,
            minWidth: 90,
          },
          {
            label: "预算备注",
            prop: "budgetRemark",
            overHidden: true,
            display: false,
            minWidth: 90,
          },
          {
            label: "压力容器",
            prop: "pv",
            type: "radio",
            width: 70,
            dicData: [
              {
                label: "是",
                value: 1,
              },
              {
                label: "否",
                value: 0,
              },
            ],
            display: false,
          },
          {
            label: "完成",
            prop: "used",
            type: "radio",
            fixed: "right",
            minWidth: 50,
            dicData: [
              {
                label: "是",
                value: true,
              },
              {
                label: "否",
                value: false,
              },
            ],
            search: true,
            display: false,
          },
        ],
      },
      data: [],
      pv: false,
      unFinish: false,
      brand: "natergy",
      needMergeArr: [
        "createUserName",
        "createTime",
        "budgetStatus",
        "serialNo",
        "title",
        "type",
      ], // 有合并项的列
      rowMergeArrs: {}, // 包含需要一个或多个合并项信息的对象
      action: {},
      dataType: "all",
      sub: {
        visible: false,
        option: {
          span: 24,
          size: "mini",
          emptyBtn: false,
          column: [
            {
              label: "小项",
              prop: "subCode",
              overHidden: true,
              type: "select",
              dicData: [],
              props: {
                label: "name",
                value: "code",
                desc: "code",
              },
              placeholder: " ",
              rules: [
                {
                  required: true,
                  message: "请选择小项",
                  trigger: "blur",
                },
              ],
            },
          ],
        },
        form: {},
      },
    };
  },
  created() {
    this.dictInit();
  },
  computed: {
    ...mapGetters(["userInfo"]),
    ids() {
      let ids = new Set();
      this.selectionList.forEach((ele) => {
        ids.add(ele.id);
      });
      return Array.from(ids).join(",");
    },
  },
  watch: {
    unFinish: {
      handler(val) {
        if (val) {
          this.query.used = false;
        } else {
          this.query.used = null;
        }
        this.page.currentPage = 1;
        this.onLoad(this.page);
      },
      immediate: false,
    },
    pv: {
      handler(val) {
        if (val) {
          this.query.pv = 1;
        } else {
          this.query.pv = null;
        }
        this.page.currentPage = 1;
        this.onLoad(this.page);
      },
      immediate: false,
    },
  },
  beforeRouteEnter(to, from, next) {
    to.meta.keepAlive = true;
    next();
  },
  methods: {
    handleCopy() {
      this.$confirm("是否复制当前筛选的所有数据？", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.handleCopyData();
      });
    },
    handleExport() {
      this.$confirm("是否导出当前筛选的所有数据？", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.handleExportData();
      });
    },
    async handleExportData() {
      this.exportList = "";
      let data = await this.getExportData();
      let opt = {
        column: [
          {
            label: "状态",
            prop: "budgetStatus",
          },
          {
            label: "申请人",
            prop: "createUserName",
          },
          {
            label: "申请时间",
            prop: "createTime",
          },
          {
            label: "预算名称",
            prop: "title",
          },
          {
            label: "预算编号",
            prop: "serialNo",
          },
          {
            label: "预算类型",
            prop: "type",
          },
          {
            label: "小项",
            prop: "subCode",
          },
          {
            label: "物/费",
            prop: "cost",
          },
          {
            label: "品名",
            prop: "materialName",
          },
          {
            label: "用途",
            prop: "purpose",
          },
          {
            label: "编码",
            prop: "materialCode",
          },
          {
            label: "规格型号",
            prop: "specification",
          },
          {
            label: "材质",
            prop: "quality",
          },
          {
            label: "国标",
            prop: "gb",
          },
          {
            label: "单位",
            prop: "unit",
          },
          {
            label: "数量",
            prop: "num",
          },
          {
            label: "单价",
            prop: "price",
          },
          {
            label: "金额",
            prop: "amount",
          },
          {
            label: "已采购数量",
            prop: "orderNum",
          },
          {
            label: "已采购金额",
            prop: "orderAmount",
          },
          {
            label: "备注",
            prop: "remark",
          },
          {
            label: "项目名称",
            prop: "projectTitle",
          },
          {
            label: "项目编号",
            prop: "projectSerialNo",
          },
          {
            label: "预算备注",
            prop: "budgetRemark",
          },
          {
            label: "压力容器",
            prop: "pv",
          },
          {
            label: "完成",
            prop: "used",
          },
        ],
      };
      this.$Export.excel({
        title: "预算明细",
        columns: opt.column,
        data: data.map((item) => {
          return {
            ...item,
            budgetStatus: this.budgetStatusDictKeyValue[item.budgetStatus],
            unit: this.unitDictKeyValue[item.unit],
            cost: item.cost ? "费用" : "实物",
            pv: item.pv ? "是" : "否",
            used: item.used ? "是" : "否",
            subCode: item.subCode ? `${item.subCode}` + item.subName : "",
          };
        }),
      });
    },
    async handleCopyData() {
      this.exportList = "";
      let data = await this.getExportData();
      let opt = {
        column: [
          {
            label: "状态",
            prop: "budgetStatus",
          },
          {
            label: "申请人",
            prop: "createUserName",
          },
          {
            label: "申请时间",
            prop: "createTime",
          },
          {
            label: "预算名称",
            prop: "title",
          },
          {
            label: "预算编号",
            prop: "serialNo",
          },
          {
            label: "预算类型",
            prop: "type",
          },
          {
            label: "小项",
            prop: "subCode",
          },
          {
            label: "物/费",
            prop: "cost",
          },
          {
            label: "品名",
            prop: "materialName",
          },
          {
            label: "用途",
            prop: "purpose",
          },
          {
            label: "编码",
            prop: "materialCode",
          },
          {
            label: "规格型号",
            prop: "specification",
          },
          {
            label: "材质",
            prop: "quality",
          },
          {
            label: "国标",
            prop: "gb",
          },
          {
            label: "单位",
            prop: "unit",
          },
          {
            label: "数量",
            prop: "num",
          },
          {
            label: "单价",
            prop: "price",
          },
          {
            label: "金额",
            prop: "amount",
          },
          {
            label: "已采购数量",
            prop: "orderNum",
          },
          {
            label: "已采购金额",
            prop: "orderAmount",
          },
          {
            label: "备注",
            prop: "remark",
          },
          {
            label: "项目名称",
            prop: "projectTitle",
          },
          {
            label: "项目编号",
            prop: "projectSerialNo",
          },
          {
            label: "预算备注",
            prop: "budgetRemark",
          },
          {
            label: "压力容器",
            prop: "pv",
          },
          {
            label: "完成",
            prop: "used",
          },
        ],
      };
      opt.column.map((item, index) => {
        if (index < opt.column.length - 1) {
          this.exportList += item.label + "\t";
        } else {
          this.exportList += item.label += "\r\n";
        }
      });
      const items = data.map((item) => {
        return {
          ...item,
          budgetStatus: this.budgetStatusDictKeyValue[item.budgetStatus],
          unit: this.unitDictKeyValue[item.unit],
          cost: item.cost ? "费用" : "实物",
          pv: item.pv ? "是" : "否",
          used: item.used ? "是" : "否",
          subCode: item.subCode ? `${item.subCode}` + item.subName : "",
        };
      });
      items.forEach((ele) => {
        let values = [];
        opt.column.forEach((item) => {
          values.push(
            ele[item.prop] !== undefined ? this.getField(ele[item.prop]) : ""
          );
        });
        this.exportList += values.join("\t") + "\r\n";
      });
      this.$Clipboard({
        text: this.exportList,
      })
        .then(() => {
          this.$message.success("数据导出完成，请复制到excel中");
        })
        .catch(() => {
          this.$message({ type: "waning", message: "该浏览器不支持自动复制" });
        })
        .finally(() => {
          //  操作完成后清空 exportList
          this.exportList = "";
        });
    },
    //获取导出数据
    async getExportData() {
      this.exportList = "";
      const promises = [];
      this.exportData = [];
      // for (var i = 1; i <= this.page.total / this.page.pageSize + 1; i++) {
      //   const promise = getPage(i, this.page.pageSize, {
      //     ...this.params,
      //     ...this.query,
      //   }).then((res) => {
      //     const data = res.data.data.records;
      //     this.exportData = this.exportData.concat(data);
      //   });
      //   promises.push(promise);
      // }
      const promise = getPage(1, 99999,{
          ...this.params,
          ...this.query,
        }).then((res) => {
          const data = res.data.data.records;
          this.exportData = this.exportData.concat(data);
        });
      promises.push(promise);
      // 等待所有异步请求完成
      await Promise.all(promises);
      return this.exportData;
    },
    getField(value) {
      return value ? `${value}`.replace(/[\r\n\s]+/g, "") : "";
    },
    handleSubSubmit(form, done) {
      const ids = this.selectionList.map((item) => item.id);
      changeSubCode(this.ids, form.subCode)
        .then(() => {
          this.$message({
            type: "warning",
            message: "操作成功!",
          });
          this.data.forEach((item) => {
            if (ids.includes(item.id)) {
              item.subCode = form.subCode;
            }
          });
          this.sub.visible = false;
        })
        .finally(() => {
          done();
        });
    },
    rowAttach(row) {
      this.action = row;
      this.$refs.attachDialogRef.init(row.id, this.module);
    },
    rowDetail(row) {
      if (!row.processInsId) {
        return;
      }
      let formKey = "wf_ex_ProjectBudget";
      let processDefKey = "process_project_budget";
      if (row.year === "2") {
        formKey = "wf_ex_por/BudgetYear";
        processDefKey = "process_project_budget_year";
      }
      if (row.repair) {
        formKey = "wf_ex_por/BudgetRepair";
        processDefKey = "process_project_budget_repair";
      }
      this.dynamicRoute(
        {
          processInstanceId: row.processInsId,
          formKey,
          processDefKey,
        },
        "detail"
      );
    },
    async handleSub() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      const projectIds = new Set();
      this.selectionList.forEach((item) => {
        if (item.year === "2") {
          projectIds.add(item.repair ? item.parentId : item.budgetId);
        } else projectIds.add(item.projectId);
      });
      if (projectIds.size === 1) {
        const subCode1 = this.findObject(this.sub.option.column, "subCode");
        const res = await this.$http.get(
          "/api/ni/project/sub/select?projectId=" + Array.from(projectIds)[0]
        );
        subCode1.dicData = res.data.data;
        this.sub.visible = true;
      } else {
        this.$prompt("小项编号", "请输入小项编号", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
        }).then(({ value }) => {
          this.handleSubSubmit({ subCode: value }, () => {});
        });
      }
    },
    handlePorApply(type) {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      const budgetStatus = this.selectionList.some(
        (item) => item.budgetStatus !== 9
      );
      if (budgetStatus) {
        this.$message.warning("选中的数据中存在未审核完成的数据，请重新选择");
        return;
      }
      const budgetIds = new Set();
      const useds = [];
      const createUsers = new Set();
      this.selectionList.forEach((item) => {
        createUsers.add(item.createUser);
        if (item.personIds && item.year === "2") {
          item.personIds
            .split(",")
            .forEach((personId) => createUsers.add(personId));
        }
        budgetIds.add(item.budgetId);
        if (item.used) {
          useds.push(item);
        }
      });
      if (budgetIds.size > 1) {
        this.$message.warning("请选择同一条预算中的明细");
        return;
      }
      if (useds.length > 0) {
        this.$message.warning("所选数据中存在已完成的明细，请重新选择");
        return;
      }
      if (!createUsers.has(this.userInfo.user_id)) {
        this.$message.warning("所选数据中存在非本人的预算，请重新选择");
        return;
      }
      const form = {
        type,
        projectId: this.selectionList[0].projectId,
        projectSerialNo: this.selectionList[0].projectSerialNo,
        projectTitle: this.selectionList[0].projectTitle,
        budgetId: this.selectionList[0].budgetId,
        budgetTitle:this.selectionList[0].title,
        budgetSerialNo: this.selectionList[0].serialNo,
        brand: this.selectionList[0].brand,
        items: this.selectionList.map((item) => {
          return {
            purpose: item.purpose,
            budgetItemId: item.id,
            materialCode: item.materialCode,
            materialName: item.materialName,
            materialId: item.materialId,
            specification: item.specification,
            quality: item.quality,
            gb: item.gb,
            unit: item.unit,
            num: item.num,
            budgetNum: item.num,
            usedNum: item.applyNum ? item.applyNum : 0,
            amount: item.amount,
            price: item.price,
            remark: item.remark,
            cost: item.cost,
          };
        }),
      };
      this.dynamicRoute(
        {
          processDefKey: "process_por_apply",
          formKey: "wf_ex_por/Apply",
          form: encodeURIComponent(
            Buffer.from(JSON.stringify(form)).toString("utf8")
          ),
        },
        "start"
      );
    },
    dictInit() {
      this.$http
        .get("/api/blade-system/dict/dictionary?code=data_status")
        .then((res) => {
          const column = this.findObject(this.option.column, "budgetStatus");
          column.dicData = res.data.data;

          this.budgetStatusDict = res.data.data;
          this.budgetStatusDictKeyValue = this.budgetStatusDict.reduce(
            (acc, cur) => {
              acc[cur.dictKey] = cur.dictValue;
              return acc;
            },
            {}
          );
        });
      this.$http
        .get("/api/blade-system/dict-biz/dictionary?code=ni_brand")
        .then((res) => {
          const column = this.findObject(this.option.column, "brand");
          column.dicData = res.data.data;
        });
      this.$http.get("/api/ni/por/type/list?status=0").then((res) => {
        const column = this.findObject(this.option.column, "type");
        column.dicData = res.data.data;
      });
      this.$http
        .get("/api/blade-system/dict-biz/dictionary?code=ni_por_type")
        .then((res) => {
          const column = this.findObject(this.option.column, "type");
          this.typeDict = res.data.data.map((item) => {
            const i = { ...item };
            //禁用原材料/包装物/费用采购
            if (["1", "2", "6"].includes(i.dictKey)) {
              i.disabled = true;
            }
            return i;
          });
          column.dicData = this.typeDict;
          this.typeDictKeyValue = this.typeDict.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });
      this.$http
        .get("/api/blade-system/dict-biz/dictionary?code=ni_material_unit")
        .then((res) => {
          this.unitDict = res.data.data;
          this.unitDictKeyValue = this.unitDict.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });
    },
    searchReset() {
      this.query = {};
      this.unFinish = false;
      this.pv = false;
      this.onLoad(this.page);
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      if (this.brand === "natergy") {
        this.query.brand = "1,2";
      } else if (this.brand === "yy") {
        this.query.brand = "4";
      }

      getPage(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
    cellStyle({ row, column }) {
      if ("materialName" === column.columnKey && row.pv) {
        return {
          backgroundColor: "#F56C6C",
          color: "#fff",
        };
      } else if ("keeperName" === column.columnKey && row.dataPrint) {
        return {
          backgroundColor: "#909399",
          color: "#fff",
        };
      }
    },
    //"createUserName","createTime", "budgetStatus", "serialNo", "title","type"
    arraySpanMethod({ column, rowIndex }) {
      if (this.needMergeArr.includes(column.property)) {
        return this.mergeAction(column.property, rowIndex, column);
      }
    },
    mergeAction(val, rowIndex) {
      let _row = this.rowMergeArrs[val].rowArr[rowIndex];
      let _col = _row > 0 ? 1 : 0;
      return {
        rowspan: _row,
        colspan: _col,
      };
    },
    rowMergeHandle(arr, data) {
      if (!Array.isArray(arr) && !arr.length) return false;
      if (!Array.isArray(data) && !data.length) return false;
      let needMerge = {};
      arr.forEach((i) => {
        needMerge[i] = {
          rowArr: [],
          rowMergeNum: 0,
        };
        data.forEach((item, index) => {
          if (index === 0) {
            needMerge[i].rowArr.push(1);
            needMerge[i].rowMergeNum = 0;
          } else {
            if (item[i] === data[index - 1][i]) {
              needMerge[i].rowArr[needMerge[i].rowMergeNum] += 1;
              needMerge[i].rowArr.push(0);
            } else {
              needMerge[i].rowArr.push(1);
              needMerge[i].rowMergeNum = index;
            }
          }
        });
      });
      return needMerge;
    },
  },
};
</script>

<style></style>

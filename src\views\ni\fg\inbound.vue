<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      :cell-style="cellStyle"
      @on-load="onLoad"
    >
      <template #serialNo="{ row, index }">
        <span
          :style="{
            textDecoration: 'underline',
            cursor: 'pointer',
          }"
          @click="rowDetail(row, index)"
          >{{ row.serialNo }}</span
        >
      </template>
      <template #batchNoForm="{ row, index, disabled }">
        <el-input
          size="mini"
          :disabled="disabled"
          placeholder=" "
          v-model="row.batchNo"
          clearable
        >
          <el-button
            slot="append"
            icon="el-icon-plus"
            @click="handleAddBatchNo(row, index)"
          ></el-button>
        </el-input>
      </template>
      <template #menuLeft>
        <el-button
          type="danger"
          size="mini"
          icon="el-icon-delete"
          plain
          v-if="permission.fgInbound_delete"
          @click="handleDelete"
          >删 除
        </el-button>
        <!--导入-->
        <el-button
          type="primary"
          size="mini"
          icon="el-icon-upload2"
          plain
          v-if="permission.fgInbound_import"
          @click="handleImport"
          >导入
        </el-button>
        <el-button
          type="warning"
          size="mini"
          icon="el-icon-s-check"
          plain
          v-if="permission.fgInbound_audit"
          @click="handleAudit"
          >审核
        </el-button>
        <el-button
          type="primary"
          size="mini"
          icon="el-icon-refresh"
          plain
          @click="handleSync"
          v-if="permission.fgInbound_sync && 1 !== 1"
          >手动同步
        </el-button>
        <el-button
          type="primary"
          size="mini"
          icon="el-icon-refresh"
          plain
          @click="handleSync1"
          v-if="permission.fgInbound_sync"
          >手动同步v1
        </el-button>
        <el-divider direction="vertical" />
        <el-checkbox v-model="unAudit" @change="handleUnAuditChange"
          >待审核
        </el-checkbox>
      </template>
      <template #menu="{ row }">
        <el-button
          type="text"
          icon="el-icon-edit"
          size="mini"
          v-if="row.status === 0 && permission.fgInbound_edit"
          @click.stop="$refs.crud.rowEdit(row, index)"
          >编辑
        </el-button>
        <el-button
          type="text"
          icon="el-icon-delete"
          size="mini"
          v-if="row.status === 0 && permission.fgInbound_delete"
          @click.stop="$refs.crud.rowDel(row, index)"
          >删除
        </el-button>
        <el-button
          type="text"
          icon="el-icon-s-check"
          size="mini"
          v-if="row.status === 0 && permission.fgInbound_audit"
          @click.stop="rowAudit(row)"
          >审核
        </el-button>
        <el-button
          type="text"
          icon="el-icon-back"
          size="mini"
          v-if="row.status === 1 && !row.red && permission.fgInbound_red"
          @click.stop="rowRed(row)"
          >冲红
        </el-button>
        <el-button
          type="text"
          icon="el-icon-document-copy"
          size="mini"
          style="color: #e6a23c"
          v-if="row.status === 2 && !row.red && permission.fgInbound_add"
          @click.stop="rowCopy(row)"
          >复制新增
        </el-button>
        <el-button
          type="text"
          icon="el-icon-reading"
          size="mini"
          @click.stop="handleDataSub(row)"
          >明细
        </el-button>
        <el-button
          type="text"
          icon="el-icon-download"
          :size="size"
          @click="rowAttach(row)"
          >附件
        </el-button>
      </template>
    </avue-crud>
    <el-drawer
      :title="fgTransactionName"
      :visible.sync="subVisible"
      :direction="direction"
      append-to-body
      :before-close="handleSubClose"
      size="1200px"
    >
      <basic-container>
        <avue-crud
          v-if="subVisible"
          :option="optionSub"
          :data="dataSub"
          :page.sync="pageSub"
          v-model="formSub"
          :table-loading="loadingSub"
          ref="crudSub"
          @search-change="searchChangeSub"
          @search-reset="searchResetSub"
          @selection-change="selectionChangeSub"
          @current-change="currentChangeSub"
          @refresh-change="refreshChangeSub"
          @size-change="sizeChangeSub"
          @on-load="onLoadSub"
          :cell-style="cellStyleSub"
        >
          <template #menuLeft>
            <el-button
              type="primary"
              size="mini"
              icon="el-icon-edit"
              plain
              v-if="permission.fgInbound_remark && transaction.status === 0"
              @click="handleChangeRemark"
              >修改备注
            </el-button>
          </template>
        </avue-crud>
      </basic-container>
    </el-drawer>
    <sku-select-dialog
      ref="skuSelectDialogRef"
      multiple
      @confirm="handleSkuSelectChange"
      :params="{ status: 1 }"
    />
    <el-dialog
      title="喷码同步"
      :visible.sync="pen.visible"
      width="400px"
      append-to-body
    >
      <avue-form
        v-if="pen.visible"
        :option="pen.option"
        :form="pen.form"
        ref="penFormRef"
        @submit="handlePenSubmit"
      />
    </el-dialog>
    <el-dialog
      title="数据导入"
      append-to-body
      :visible.sync="excelBox"
      width="555px"
    >
      <avue-form
        :option="excelOption"
        v-model="excelForm"
        :upload-after="uploadAfter"
      >
        <template #excelTemplate>
          <el-button size="mini" type="primary" @click="handleTemplate">
            点击下载<i class="el-icon-download el-icon--right"></i>
          </el-button>
        </template>
      </avue-form>
    </el-dialog>
    <el-dialog
      title="批号录入"
      append-to-body
      :visible.sync="batchNo.visible"
      width="455px"
    >
      <avue-form
        :option="batchNo.option"
        v-model="batchNo.form"
        @submit="handleAddBatchNoSubmit"
      >
        <template #endBatchNo="{ size, disabled }">
          <el-input placeholder=" " v-model="batchNo.form.endBatchNo">
            <el-button slot="append" @click="handleGenBatchNo">生成</el-button>
          </el-input>
        </template>
      </avue-form>
    </el-dialog>
    <pen-ma-chu-duo-count-dialog
      ref="penMaChuKuCountDialogRef"
      @confirm="handlePenmaChukuCountSubmit"
    />
    <product-batch-summary-dialog
      ref="productBatchSummaryDialogRef"
      @confirm="handleProductBatchSummarySubmit"
    />
    <attach-dialog ref="attachDialogRef" />
  </basic-container>
</template>

<script>
import {
  add,
  audit,
  changeItemRemark,
  getDetail,
  getList,
  penmaSync,
  productionBatchSync1,
  red,
  remove,
  update,
} from "@/api/ni/fg/fgInbound";
import {
  add as addSub,
  getList as getListSub,
  remove as removeSub,
  update as updateSub,
} from "@/api/ni/fg/fgTransactionItem";
import { mapGetters } from "vuex";
import { dateFormat } from "@/util/date";
import SkuSelectDialog from "@/views/ni/product/components/SkuSelectDialog.vue";
import { exportBlob } from "@/api/common";
import { getToken } from "@/util/auth";
import { downloadXls } from "@/util/util";
import InboundImportDialog from "@/views/ni/fg/components/InboundImportDialog.vue";
import PenMaChuDuoCountDialog from "@/views/ni/old/components/PenMaChuDuoCountDialog.vue";
import ProductBatchSummaryDialog from "@/views/ni/tracing/components/ProductBatchSummaryDialog.vue";
import AttachDialog from "@/components/attach-dialog/index.vue";

export default {
  components: {
    AttachDialog,
    InboundImportDialog,
    SkuSelectDialog,
    PenMaChuDuoCountDialog,
    ProductBatchSummaryDialog,
  },
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      data: [],
      selectionList: [],
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      option: {
        editBtn: false,
        delBtn: false,
        dialogFullscreen: true,
        dialogDrag: true,
        size: "mini",
        searchSize: "mini",
        align: "center",
        searchIndex: 3,
        searchIcon: true,
        searchEnter: true,
        height: "auto",
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        span: 8,
        border: true,
        index: true,
        selection: true,
        menuWidth: 300,
        column: [
          {
            label: "入库编号",
            prop: "serialNo",
            placeholder: "系统自动生成",
            overHeight: true,
            search: true,
            minWidth: 110,
          },
          {
            label: "入库主题",
            prop: "title",
            minWidth: 150,
            overHidden: true,
            search: true,
            rules: [
              {
                required: true,
                message: "请输入单据主题",
                trigger: "blur",
              },
            ],
          },
          {
            label: "入库类型",
            prop: "type",
            type: "select",
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_fg_inbound_type",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            search: true,
            minWidth: 90,
            rules: [
              {
                required: true,
                message: "请选择入库类型",
                trigger: "blur",
              },
            ],
          },
          {
            label: "仓库",
            prop: "depotId",
            type: "select",
            dicUrl: "/api/ni/base/depot/info/list?status=2&type=1",
            props: {
              label: "name",
              value: "id",
              desc: "code",
            },
            minWidth: 100,
            overHidden: true,
            editDisabled: true,
            search: true,
            rules: [
              {
                required: true,
                message: "请选择仓库",
                trigger: "blur",
              },
            ],
          },
          {
            label: "操作人",
            prop: "opUserId",
            type: "select",
            remote: true,
            dicUrl: `/api/blade-user/search/otj/user?name={{key}}`,
            props: {
              label: "realName",
              value: "id",
            },
            dicFormatter: (data) => {
              return data.data.records;
            },
            placeholder: " ",
            filterable: true,
            overHidden: true,
            rules: [
              {
                required: true,
                message: "请选择操作人",
                trigger: "blur",
              },
            ],
            search: true,
            hide: true,
            showColumn: false,
          },
          {
            label: "操作人",
            prop: "opUserName",
            display: false,
            minWidth: 80,
          },
          {
            label: "入库时间",
            prop: "opDate",
            search: true,
            type: "date",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            searchRange: true,
            placeholder: " ",
            editDisabled: true,
            minWidth: 120,
            rules: [
              {
                required: true,
                message: "请选择操作时间",
                trigger: "blur",
              },
            ],
          },
          {
            label: "关联单据",
            prop: "relatedOrderId",
            type: "input",
            hide: true,
            showColumn: false,
          },
          {
            label: "关联单据",
            prop: "relatedOrderText",
            type: "input",
            display: false,
            overHidden: true,
            minWidth: 100,
            search: true,
          },
          {
            label: "总箱数",
            prop: "total",
            overHidden: true,
            minWidth: 100,
            display: false,
          },
          {
            label: "备注",
            prop: "remark",
            type: "textarea",
            span: 24,
            minRows: 2,
            minWidth: 150,
            search: true,
            overHidden: true,
          },
          {
            label: "创建人",
            prop: "createUserName",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            hide: true,
          },
          {
            label: "创建时间",
            prop: "createTime",
            type: "date",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            addDisplay: false,
            editDisplay: false,
            hide: true,
          },
          {
            label: "状态",
            prop: "status",
            type: "select",
            dicData: [
              {
                label: "待审核",
                value: 0,
              },
              {
                label: "正常",
                value: 1,
              },
              {
                label: "已冲红",
                value: 2,
              },
            ],
            addDisplay: false,
            editDisplay: false,
          },
          {
            label: "入库明细",
            prop: "items",
            span: 24,
            type: "dynamic",
            hide: true,
            showColumn: false,
            children: {
              rowAdd: () => {
                this.handleSkuSelect();
              },
              size: "mini",
              align: "center",
              headerAlign: "center",
              showSummary: true,
              sumColumnList: [
                {
                  name: "num",
                  type: "sum",
                  decimals: 1,
                },
                {
                  name: "boxNum",
                  type: "sum",
                  decimals: 0,
                },
              ],
              column: [
                {
                  label: "存货编码",
                  prop: "materialCode",
                  placeholder: " ",
                  width: 110,
                  cell: false,
                  display: false,
                },
                {
                  label: "规格",
                  prop: "specText",
                  placeholder: " ",
                  display: false,
                  overHidden: true,
                  cell: false,
                  width: 150,
                },

                {
                  label: "外包装",
                  prop: "packageText",
                  placeholder: " ",
                  display: false,
                  overHidden: true,
                  cell: false,
                  width: 115,
                },
                {
                  label: "内包装",
                  prop: "innerPackageText",
                  placeholder: " ",
                  display: false,
                  overHidden: true,
                  cell: false,
                  width: 115,
                },
                {
                  label: "质量",
                  prop: "qualityLevel",
                  type: "select",
                  dicUrl:
                    "/api/blade-system/dict-biz/dictionary?code=ni_product_sku_quality_level",
                  props: {
                    label: "dictValue",
                    value: "dictKey",
                    desc: "dictKey",
                  },
                  placeholder: " ",
                  width: 100,
                  cell: false,
                },
                {
                  label: "批号",
                  prop: "batchNo",
                  placeholder: " ",
                  minWidth: 110,
                },
                {
                  label: "生产日期",
                  prop: "productionTime",
                  type: "datetime",
                  format: "yyyy-MM-dd HH:mm:ss",
                  valueFormat: "yyyy-MM-dd HH:mm:ss",
                  hide: false,
                  minWidth: 110,
                },
                {
                  label: "箱数",
                  prop: "num",
                  placeholder: " ",
                  type: "number",
                  precision: 0,
                  minWidth: 80,
                  rules: [
                    {
                      required: true,
                      message: "请输入箱数",
                      trigger: "blur",
                    },
                  ],
                  change: ({ value, row }) => {
                    if (value != null) row.weight = value * row.capacity;
                  },
                },
                {
                  label: "重量",
                  prop: "weight",
                  placeholder: " ",
                  type: "number",
                  minWidth: 80,
                  rules: [
                    {
                      required: true,
                      message: "请输入重量",
                      trigger: "blur",
                    },
                  ],
                },
                {
                  label: "单位",
                  prop: "unit",
                  type: "select",
                  filterable: true,
                  width: 80,
                  cell: false,
                  dicUrl:
                    "/api/blade-system/dict-biz/dictionary?code=ni_material_unit",
                  props: {
                    label: "dictValue",
                    value: "dictKey",
                  },
                  values: "16",
                  rules: [
                    {
                      required: true,
                      message: "请选择单位",
                      trigger: "blur",
                    },
                  ],
                  slot: true,
                  placeholder: " ",
                },

                {
                  label: "备注",
                  prop: "remark",
                  type: "textarea",
                  placeholder: " ",
                  minRows: 1,
                  overHidden: true,
                  minWidth: 120,
                },
              ],
            },
          },
        ],
      },
      subVisible: false,
      direction: "rtl",
      transactionId: 0,
      transaction: {},
      fgTransactionName: "入库明细",
      formSub: {},
      querySub: {},
      loadingSub: true,
      dataSub: [],
      selectionListSub: [],
      pageSub: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      optionSub: {
        menu: false,
        size: "mini",
        searchSize: "mini",
        align: "center",
        searchIndex: 3,
        searchIcon: true,
        searchEnter: true,
        addBtn: false,
        delBtn: false,
        editBtn: false,
        height: "auto",
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        selection: true,
        dialogClickModal: false,
        column: [
          {
            label: "存货编码",
            prop: "materialCode",
            placeholder: " ",
            width: 110,
            cell: false,
            display: false,
          },
          {
            label: "规格",
            prop: "specText",
            placeholder: " ",
            display: false,
            overHidden: true,
            cell: false,
            width: 120,
          },
          {
            label: "外包装",
            prop: "packageText",
            placeholder: " ",
            display: false,
            overHidden: true,
            cell: false,
            width: 115,
          },
          {
            label: "内包装",
            prop: "innerPackageText",
            placeholder: " ",
            display: false,
            overHidden: true,
            cell: false,
            width: 115,
          },
          {
            label: "质量",
            prop: "qualityLevel",
            type: "select",
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_product_sku_quality_level",
            props: {
              label: "dictValue",
              value: "dictKey",
              desc: "dictKey",
            },
            placeholder: " ",
            width: 100,
          },
          {
            label: "批号",
            prop: "batchNo",
            placeholder: " ",
            minWidth: 110,
            rules: [
              {
                required: true,
                message: "请输入批号",
                trigger: "blur",
              },
            ],
          },
          {
            label: "生产日期",
            prop: "productionTime",
            type: "datetime",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
            minWidth: 130,
            overHidden: true,
          },
          {
            label: "数量",
            prop: "num",
            placeholder: " ",
            type: "number",
            minWidth: 80,
            rules: [
              {
                required: true,
                message: "请输入数量",
                trigger: "blur",
              },
            ],
          },

          {
            label: "备注",
            prop: "remark",
            type: "textarea",
            placeholder: " ",
            minRows: 1,
            overHidden: true,
            minWidth: 120,
          },
        ],
      },
      qualityLevelColorMap: {
        A: "#67C23A", // 高吸附 - 绿色
        P: "#409EFF", // 优等品 - 蓝色
        Q: "#E6A23C", // 合格品 - 橙色
      },
      excelBox: false,
      excelOption: {
        size: "mini",
        searchSize: "mini",
        submitBtn: false,
        emptyBtn: false,
        column: [
          {
            label: "数据上传",
            prop: "excelFile",
            type: "upload",
            drag: true,
            loadText: "模板上传中，请稍等",
            span: 24,
            data: {},
            propsHttp: {
              res: "data",
            },
            tip: "请上传 .xls,.xlsx 标准格式文件",
            action: "/api/ni/fg/inbound/import",
          },
          {
            label: "模板下载",
            prop: "excelTemplate",
            formslot: true,
            span: 24,
          },
        ],
      },
      excelForm: {},
      pen: {
        version: null,
        visible: false,
        form: {},
        option: {
          size: "mini",
          span: 24,
          emptyBtn: false,
          labelPosition: "top",
          column: [
            {
              label: "选择日期",
              prop: "date",
              type: "datetimerange",
              format: "yyyy-MM-dd HH:mm:ss",
              valueFormat: "yyyy-MM-dd HH:mm:ss",
              defaultTime: ["06:50:00", "06:50:00"],
              placeholder: "请选择日期",
              rules: [
                {
                  required: true,
                  message: "请选择日期",
                  trigger: "blur",
                },
              ],
            },
          ],
        },
      },
      unAudit: false,
      batchNo: {
        visible: false,
        form: {},
        option: {
          size: "mini",
          span: 24,
          emptyBtn: false,
          column: [
            {
              label: "起始批号",
              prop: "startBatchNo",
              type: "input",
              placeholder: " ",
              rules: [
                {
                  required: true,
                  message: "请输入起始批号",
                  trigger: "blur",
                },
              ],
            },
            {
              label: "结束批号",
              prop: "endBatchNo",
              type: "input",
              placeholder: " ",
              rules: [
                {
                  required: true,
                  message: "请输入结束批号",
                  trigger: "blur",
                },
              ],
            },
            {
              label: "登记批号",
              prop: "batchNo",
              type: "select",
              placeholder: " ",
              allowCreate: true,
              filterable: true,
              multiple: true,
              rules: [
                {
                  required: true,
                  message: "请选择登记批号",
                  trigger: "blur",
                },
              ],
            },
            {
              label: "每批箱数",
              prop: "num",
              type: "number",
              placeholder: " ",
              rules: [
                {
                  required: true,
                  message: "请输入每批箱数",
                  trigger: "blur",
                },
              ],
            },
          ],
        },
      },
    };
  },
  computed: {
    ...mapGetters(["permission", "userInfo", "colorName"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.fgInbound_add, false),
        viewBtn: this.vaildData(this.permission.fgInbound_view, false),
        delBtn: this.vaildData(this.permission.fgInbound_delete, false),
        editBtn: this.vaildData(this.permission.fgInbound_edit, false),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
    subIds() {
      let ids = [];
      this.selectionListSub.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
    items() {
      return this.form.items || [];
    },
  },
  methods: {
    handleGenBatchNo() {
      if (
        this.batchNo.form.startBatchNo === "" ||
        this.batchNo.form.startBatchNo === null
      ) {
        this.$message.error("请输入起始批号");
        return;
      }
      if (
        this.batchNo.form.endBatchNo === "" ||
        this.batchNo.form.endBatchNo === null
      ) {
        this.$message.error("请输入结束批号");
        return;
      }
      this.batchNo.form.batchNo = this.generateComplexStrings(
        this.batchNo.form.startBatchNo,
        this.batchNo.form.endBatchNo
      );
    },
    handleAddBatchNo(row, index) {
      this.batchNo.form = {
        startBatchNo: row.startBatchNo,
        endBatchNo: row.endBatchNo,
        batchNos: row.batchNos,
        index,
      };
      if (row.num && row.batchNos && row.batchNos.length > 0) {
        this.batchNo.form.num = row.num / row.batchNos.length;
      }
      this.batchNo.visible = true;
    },
    handleAddBatchNoSubmit(form, done) {
      if (form.index != null) {
        this.form.items[form.index].batchNo =
          form.startBatchNo + "~" + form.endBatchNo;
        this.form.items[form.index].batchNos = form.batchNo;
        this.form.items[form.index].startBatchNo = form.startBatchNo;
        this.form.items[form.index].endBatchNo = form.endBatchNo;
        this.form.items[form.index].num = form.num * form.batchNo.length;
        done();
        this.batchNo.visible = false;
      }
    },
    generateComplexStrings(startStr, endStr) {
      // 提取最后一组数字
      const extractLastNumber = (str) => {
        const matches = str.match(/\d+/g);
        return matches ? parseInt(matches[matches.length - 1]) : NaN;
      };

      const startNum = extractLastNumber(startStr);
      const endNum = extractLastNumber(endStr);

      if (isNaN(startNum) || isNaN(endNum)) {
        this.$message({
          type: "warning",
          message: "批号中未找到有效数字",
        });
        return [];
      }

      if (startNum > endNum) {
        this.$message({
          type: "warning",
          message: "起始批号不能大于结束批号",
        });
        return [];
      }

      // 提取数字前后的部分
      const splitParts = (str) => {
        const parts = str.split(/(\d+)/g);
        const numbers = parts.filter((_, i) => i % 2 === 1);
        const lastNumIndex = parts.lastIndexOf(numbers[numbers.length - 1]);

        return {
          prefix: parts.slice(0, lastNumIndex).join(""),
          suffix: parts.slice(lastNumIndex + 1).join(""),
          lastNum: numbers[numbers.length - 1],
        };
      };

      const startParts = splitParts(startStr);
      const endParts = splitParts(endStr);

      // 验证非数字部分是否匹配
      if (
        startParts.prefix !== endParts.prefix ||
        startParts.suffix !== endParts.suffix
      ) {
        this.$message({
          type: "warning",
          message: "起始批号和结束批号的非数字部分不匹配",
        });
        return [];
      }
      // 计算生成的批号数量
      const batchCount = endNum - startNum + 1;

      // 如果批号数量大于等于100，弹出警告并返回空数组
      if (batchCount >= 100) {
        this.$message({
          type: "warning",
          message: "生成的批号数量超过100个，无法继续生成",
        });
        return [];
      }
      // 生成所有字符串
      const result = [];
      const numLength = startParts.lastNum.length; // 保持数字位数

      for (let i = startNum; i <= endNum; i++) {
        const numStr = i.toString().padStart(numLength, "0");
        result.push(`${startParts.prefix}${numStr}${startParts.suffix}`);
      }

      return result;
    },
    handleUnAuditChange(val) {
      this.query.status = val ? 0 : null;
      this.onLoad(this.page);
    },
    uploadAfter(res, done) {
      this.excelBox = false;
      this.refreshChange();
      done();
      if (res) {
        this.$confirm(res, "提示", {
          confirmButtonText: "确定",
          type: "warning",
          center: true,
        });
      }
    },
    handleTemplate() {
      exportBlob(
        `/api/ni/fg/inbound/export-template?${
          this.website.tokenHeader
        }=${getToken()}`
      ).then((res) => {
        downloadXls(res.data, "产成品入库导入模板.xlsx");
      });
    },
    handleSync1() {
      this.pen.version = 1;
      this.pen.visible = true;
    },
    handleSync() {
      this.pen.version = null;
      this.pen.visible = true;
    },
    handlePenSubmit(form, done) {
      done();
      this.pen.visible = false;
      if (this.pen.version && this.pen.version === 1) {
        this.handleProductBatchSummaryShow(form);
      } else {
        let title = `[${form.date[0]}~${form.date[1]}]喷码出垛信息统计`;
        if (form.date[0] === form.date[1])
          title = `[${form.date[0]}]喷码出垛信息统计`;
        this.$refs.penMaChuKuCountDialogRef.onShow(
          { startDate: form.date[0], endDate: form.date[1] },
          title
        );
      }
    },
    handleProductBatchSummaryShow(form) {
      let title = `[${form.date[0]}~${form.date[1]}]生产批次信息统计`;
      if (form.date[0] === form.date[1])
        title = `[${form.date[0]}]生产批次信息统计`;
      this.$refs.productBatchSummaryDialogRef.onShow(
        { startDate: form.date[0], endDate: form.date[1] },
        title
      );
    },
    handleProductBatchSummarySubmit(form, skuIds) {
      productionBatchSync1(form.startDate, form.endDate, skuIds).then(() => {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!",
        });
      });
    },
    handlePenmaChukuCountSubmit(form) {
      penmaSync(form.startDate, form.endDate).then(() => {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!",
        });
      });
    },
    rowAudit(row) {
      this.$confirm("审核后将更新库存，是否继续?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return audit(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    async rowCopy(row) {
      const res = await getDetail(row.id);
      const form = res.data.data;
      form.id = null;
      form.serialNo = null;
      form.opUserId = this.userInfo.user_id;
      form.opDate = dateFormat(new Date(), "yyyy-MM-dd");
      form.status = null;
      form.items.forEach((item) => {
        item.id = null;
        item.transactionId = null;
      });
      this.form = form;
      this.$refs.crud.rowAdd();
    },
    rowRed(row) {
      this.$prompt("请输入冲红原因", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
      }).then(({ value }) => {
        if (!value) {
          this.$message({
            type: "error",
            message: "请输入冲红原因",
          });
          return;
        }
        red(row.id, value).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
      });
    },
    handleSkuSelect() {
      this.$refs.skuSelectDialogRef.onShow();
    },
    handleSkuSelectChange(selectionList) {
      selectionList.forEach((item) => {
        this.items.push({
          skuId: item.id,
          skuText: item.sku,
          specText: item.specText,
          packageText: item.packageText,
          innerPackageText: item.innerPackageText,
          materialId: item.materialId,
          materialCode: item.materialCode,
          qualityLevel: item.qualityLevel,
          unit: item.unit,
          capacity: item.capacity,
        });
      });
    },
    rowDetail(row, index) {
      this.$refs.crud.rowView(row, index);
    },
    // 主表模块
    rowSave(row, done, loading) {
      //如果有批号，则必须要登记生产日期
      if (row.items) {
        const noProductionTime = row.items.some((item) => {
          return item.batchNo && !item.productionTime;
        });
        if (noProductionTime) {
          this.$message({
            type: "warning",
            message: "生产日期未登记",
          });
          loading();
          return false;
        }
      }
      add(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          window.console.log(error);
          loading();
        }
      );
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          window.console.log(error);
          loading();
        }
      );
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    handleImport() {
      this.excelForm = {};
      this.excelBox = true;
    },
    handleAudit() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      // 检查是否存在状态非0的项
      const hasNonZeroStatus = this.selectionList.some(
        (ele) => ele.status !== 0 && ele.status !== undefined
      );

      if (hasNonZeroStatus) {
        this.$message.error("选择的数据包含已提交审核的条目，请重新选择");
        return;
      }
      this.$confirm("审核后将更新库存，是否继续?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return audit(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      // 检查是否存在状态非0的项
      const hasNonZeroStatus = this.selectionList.some(
        (ele) => ele.status !== 0 && ele.status !== undefined
      );

      if (hasNonZeroStatus) {
        this.$message.error("选择的数据包含已提交审核的条目，请重新选择");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen(done, type) {
      if ("add" === type) {
        this.form.opUserId = this.userInfo.user_id;
        this.form.opDate = dateFormat(new Date(), "yyyy-MM-dd");
      }
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then((res) => {
          this.form = res.data.data;
        });
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    onLoad(page, params = {}) {
      this.loading = true;
      const q = {
        ...params,
        ...this.query,
      };
      if (q.opDate != null && q.opDate.length === 2) {
        q.startOpDate = q.opDate[0];
        q.endOpDate = q.opDate[1];
        q.opDate = null;
      }
      getList(page.currentPage, page.pageSize, q).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
    cellStyle({ row, column }) {
      if ("status" === column.columnKey) {
        if (row.status === 0) {
          return {
            backgroundColor: "#f5f7fa",
            color: "##909399",
          };
        } else if (row.status === 1) {
          return {
            backgroundColor: this.colorName,
            color: "#fff",
          };
        } else if (row.status === 2) {
          return {
            backgroundColor: "#F56C6C",
            color: "#fff",
          };
        }
      }
      if ("serialNo" === column.columnKey && row.red) {
        return {
          backgroundColor: "#F56C6C",
          color: "#fff",
        };
      }
      if ("total" === column.columnKey && row.total <= 0) {
        return {
          color: "#F56C6C",
        };
      }
    },
    rowAttach(row) {
      return this.$refs.attachDialogRef.init(row.id, "ni_fg_inbound");
    },
    // 子表模块
    handleDataSub(row) {
      this.transactionId = row.id;
      this.transaction = { ...row };
      this.subVisible = true;
      // this.onLoadSub(this.pageSub)
    },
    handleSubClose(hide) {
      hide();
    },
    rowSaveSub(row, loading, done) {
      row = {
        ...row,
        transactionId: this.transactionId,
      };
      addSub(row).then(
        () => {
          loading();
          this.onLoadSub(this.pageSub);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        },
        (error) => {
          done();
          window.console.log(error);
        }
      );
    },
    rowUpdateSub(row, index, loading, done) {
      row = {
        ...row,
        transactionId: this.transactionId,
      };
      updateSub(row).then(
        () => {
          loading();
          this.onLoadSub(this.pageSub);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        },
        (error) => {
          done();
          window.console.log(error);
        }
      );
    },
    rowDelSub(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return removeSub(row.id);
        })
        .then(() => {
          this.onLoadSub(this.pageSub);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    searchResetSub() {
      this.querySub = {};
      this.onLoadSub(this.pageSub);
    },
    searchChangeSub(params) {
      this.querySub = params;
      this.onLoadSub(this.pageSub, params);
    },
    selectionChangeSub(list) {
      this.selectionListSub = list;
    },
    currentChangeSub(currentPage) {
      this.pageSub.currentPage = currentPage;
    },
    sizeChangeSub(pageSize) {
      this.pageSub.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    refreshChangeSub() {
      this.onLoadSub(this.pageSub, this.querySub);
    },
    onLoadSub(page, params = {}) {
      this.loadingSub = true;
      const values = {
        ...params,
        transactionId: this.transactionId,
      };
      getListSub(
        page.currentPage,
        page.pageSize,
        Object.assign(values, this.querySub)
      ).then((res) => {
        const data = res.data.data;
        this.pageSub.total = data.total;
        this.dataSub = data.records;
        this.selectionListSub = [];
        this.loadingSub = false;
      });
    },
    handleChangeRemark() {
      if (this.selectionListSub.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$prompt("", "请填写备注", {
        showClose: false,
        showCancelButton: false,
        inputPattern: /^\s*\S+\s*$/,
        inputErrorMessage: "请填写备注",
      }).then(({ value }) => {
        changeItemRemark(this.subIds, value).then(() => {
          this.onLoadSub(this.pageSub);
          this.$message({
            type: "success",
            message: "操作成功",
          });
          this.onLoad(this.page);
        });
      });
    },
    cellStyleSub({ row, column }) {
      if ("qualityLevel" === column.columnKey) {
        const color = this.qualityLevelColorMap[row.qualityLevel];
        return {
          backgroundColor: color || "#909399", // 默认颜色为灰色
          color: "#fff",
        };
      }
    },
  },
};
</script>

<style></style>

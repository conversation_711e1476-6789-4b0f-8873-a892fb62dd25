<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <!-- <template slot="menuLeft">
        <el-button
          type="danger"
          size="small"
          icon="el-icon-delete"
          plain
          v-if="permission.productionBatch_delete"
          @click="handleDelete"
          >删 除
        </el-button>
      </template> -->
      <template #menu="{ row, index, size }">
        <el-button
          type="text"
          icon="el-icon-setting"
          size="mini"
          plain
          style="border: 0; background-color: transparent !important"
          @click.stop="handleLog(row)"
        >
          扫码记录
        </el-button>
      </template>
    </avue-crud>

    <el-drawer
      :title="`[${scopePropertyName}] 扫码记录`"
      :visible.sync="logDrawerVisible"
      :direction="direction"
      append-to-body
      :before-close="handleLogDrawerClose"
      size="50%"
    >
      <div>
        <scan-log v-if="logDrawerVisible" :box-id="boxId" />
      </div>
    </el-drawer>
  </basic-container>
</template>

<script>
import {
  getList,
  getDetail,
  add,
  update,
  remove,
} from "@/api/ni/tracing/boxInfo";
import option from "@/const/ni/tracing/boxInfo";
import ScanLog from "./qrCodeScanLog.vue";
import { mapGetters } from "vuex";

export default {
  components: {
    ScanLog,
  },
  props: {
    batchId: {
      type: String,
      require: true,
    },
  },
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: option,
      data: [],
      scopePropertyName: "二维码",
      logDrawerVisible: false,
      boxId: "",
      direction: "rtl",
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.productionBatch_add, false),
        viewBtn: this.vaildData(this.permission.productionBatch_view, false),
        delBtn: this.vaildData(this.permission.productionBatch_delete, false),
        editBtn: this.vaildData(this.permission.productionBatch_edit, false),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  methods: {
    handleLog(row) {
      this.scopePropertyName = row.boxCode;
      this.boxId = row.id;
      this.logDrawerVisible = true;
    },
    handleLogDrawerClose(hide) {
      hide();
    },
    rowSave(row, done, loading) {
      row.batchId = this.batchId;
      add(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          window.console.log(error);
        }
      );
    },
    rowUpdate(row, index, done, loading) {
      row.batchId = this.batchId;
      update(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          console.log(error);
        }
      );
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen(done, type) {
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then((res) => {
          this.form = res.data.data;
        });
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;

      if (this.batchId) {
        this.query.batchId = this.batchId;
      }

      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        data.records.forEach((item) => {
          item.qrCode = "";
        });
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
  },
};
</script>

<style></style>

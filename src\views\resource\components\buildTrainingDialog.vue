<template>
  <el-dialog
    ref="upload-training-dialog"
    title="新建文件夹"
    append-to-body
    :visible.sync="visible"
    :destroy-on-close="true"
    @open="beforeOpen"
    @close="handleClose"
    @closed="handleClose"
    width="60%"
  >
    <el-container>
      <avue-form
        :option="option"
        v-model="form"
        :data="data"
        @submit="handleSubmit"
      >
      </avue-form>
      <training-scope-dialog
    ref="fileScopeDialog"
    :user-option="userOption"
    all
    @submit="handleScopeSubmit"
  />
    </el-container>
  </el-dialog>
</template>

<!--  -->

<script>
import { buildFileSave } from "@/api/resource/training";
import { mapGetters } from "vuex";
import TrainingScopeDialog from "@/views/resource/components/trainingScopeDialog";
import UserOption from "@/views/plugin/workflow/process/components/user-option.vue";
export default {
  components: {
    TrainingScopeDialog,
    UserOption,
  },
  props: {
    parentId: String, //父组件传过来的父ID
  },
  data() {
    return {
      visible: false,
      form: {},
      data: [],
      parentIdDict: [],
      userOption: {
        userUrl: "/api/blade-user/search/user?status=1",
        roleUrl: "/api/blade-system/search/role",
        deptUrl: "/api/blade-system/search/dept",
        postUrl: "/api/blade-system/search/post",
      },
      option: {
        column: [
          {
            label: "上级文件夹",
            prop: "parentId",
            dicData: [],
            type: "select",
            hide: true,
            span: 24,
            disabled: true,
            props: {
              label: "fileName",
              value: "id",
            },
          },
          {
            label: "文件夹名称",
            prop: "fileName",
            display: true,
            search: true,
            rules: [
              {
                required: true,
                message: "请输入文件夹名称",
                trigger: "blur",
              },
            ],
          },
          {
            label: "文件类型",
            prop: "fileType",
            display: false,
          },
          {
            label: "文件夹说明",
            prop: "fileAbstract",
            type: "textarea",
            minRows: 3,
            maxRows: 5,
            maxlength: 100,
            span: 24,
            showWordLimit: true,
          },
          {
            label: "文件夹大小",
            prop: "fileSize",
            display: false,
          },
          {
            label: "创建者",
            prop: "createUser",
            display: false,
          },
          {
            label: "部门",
            prop: "createDept",
            display: false,
          },
          {
            label: "创建日期",
            prop: "createTime",
            display: false,
          },
          {
            label: "访问控制",
            prop: "fileAccess",
            type: "radio",
            span: 24,
            dicData: [
              {
                label: "公开",
                value: 0,
              },
              {
                label: "私人",
                value: 1,
              },
              {
                label: "权限",
                value: 2,
              },
            ],
            change: ({ value }) => {
              if (value == 2) {
                this.handleFileScope();
              }
            },
          },
          {
            label: "访问权限",
            prop: "fileScope",
            display: false,
            hide: true,
          },
        ],
      },
    };
  },

  computed: {
    ...mapGetters(["userInfo"]),
  },
  methods: {
    handleScopeSubmit(list) {
      this.form.fileScope = list;
    },
    handleFileScope() {
      this.$refs.fileScopeDialog.visible = true;
    },
    handleClose(){
        this.$refs.fileScopeDialog.visible = false;
    },
    // 弹框打开前的初始化回调
    beforeOpen() {
      this.$set(this.form, "parentId", this.parentId.toString());
    },
    //提交
    handleSubmit(row, done, loading) {
      row = {
        ...row,
      };
      buildFileSave(row).then(
        () => {
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.visible = false;
          done();
          // 父组件刷新界面
          this.$emit("submit");
        },
        (error) => {
          loading();
          window.console.log(error);
        }
      );
    },
  },
};
</script>

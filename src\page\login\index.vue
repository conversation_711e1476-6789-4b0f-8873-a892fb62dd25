<template>
  <div class="login-container" ref="login" @keyup.enter.native="handleLogin">
    <div class="bg-carousel-container">
      <el-carousel
        ref="carousel"
        :interval="10000"
        style="height: 85%"
        :autoplay="true"
      >
        <el-carousel-item v-for="(bg, index) in bgList" :key="index">
          <el-image :src="bg.url" fit="fill" style="width: 100%"></el-image>
        </el-carousel-item>
      </el-carousel>
      <div class="footer-wrapper">
        <el-row :gutter="10">
          <el-col :span="8" v-for="(footer, index) in footerList" :key="index">
            <div class="icon">
              <div class="footer-img">
                <el-image
                  :src="footer.url"
                  fit="fill"
                  style="width: 80px; height: 80px; float: left"
                ></el-image>
              </div>
              <span>
                <div class="footer-title">{{ footer.title }}</div>
                <div class="footer-desc">{{ footer.desc }}</div>
              </span>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
    <div class="header-logo" />
    <div class="login-weaper animated bounceInDown">
      <div class="login-border">
        <div class="login-main">
          <div class="login-time">
            {{ time }}
          </div>
          <h4 class="login-title">
            {{ $t("login.info") }}
            <!--            <top-lang></top-lang>-->
          </h4>
          <userLogin v-if="activeName === 'user'"></userLogin>
          <codeLogin v-else-if="activeName === 'code'"></codeLogin>
          <thirdLogin v-else-if="activeName === 'third'"></thirdLogin>
          <div class="login-switch-institute" v-if="website.instituteUrl">
            <el-link
              type="primary"
              :underline="false"
              @click="goToInstituteLogin"
            >
              登录研究院系统
            </el-link>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import userLogin from "./userlogin";
import codeLogin from "./codelogin";
import thirdLogin from "./thirdlogin";
import { mapGetters } from "vuex";
import { dateFormat } from "@/util/date";
import { validatenull } from "@/util/validate";
import topLang from "@/page/index/top/top-lang";
import topColor from "@/page/index/top/top-color";
import { getQueryString, getTopUrl } from "@/util/util";

export default {
  name: "login",
  components: {
    userLogin,
    codeLogin,
    thirdLogin,
    topLang,
    topColor,
  },
  data() {
    return {
      time: "",
      activeName: "user",
      socialForm: {
        tenantId: "000000",
        source: "",
        code: "",
        state: "",
      },
      bgList: [
        {
          url: "/img/bg/login-bg1.jpg",
        },
        {
          url: "/img/bg/login-bg2.jpg",
        },
        {
          url: "/img/bg/login-bg3.jpg",
        },
        {
          url: "/img/bg/login-bg4.jpg",
        },
        {
          url: "/img/bg/login-bg5.jpg",
        },
      ],
      footerList: [
        {
          url: "/img/bg/footer1.png",
          title: "清洁",
          desc: "吸附空气中的浮尘（在水的作用下）自身落粉量很低",
        },
        {
          url: "/img/bg/footer2.png",
          title: "节能",
          desc: "可以循环利用，对环境无害，可以回收再加工再利用...",
        },
        {
          url: "/img/bg/footer3.png",
          title: "环保",
          desc: "用于中空玻璃，与中空玻璃铝条，密封胶等合理配合，保障节能作用...",
        },
      ],
    };
  },
  watch: {
    $route() {
      this.handleLogin();
    },
  },
  created() {
    localStorage.clear();
    this.$store.commit("CLEAR_USER_INFO");
    this.handleLogin();
    this.getTime();
  },
  mounted() {
    this.$nextTick(() => {
      this.$refs.carousel.update();
    });
  },
  computed: {
    ...mapGetters(["website", "tagWel", "userInfo"]),
  },
  props: [],
  methods: {
    goToInstituteLogin() {
      // 从 website 配置中获取研究院的URL
      const instituteUrl = this.website.instituteUrl;
      if (instituteUrl) {
        // 使用 window.location.href 直接跳转到外部链接
        const redirectUri = encodeURIComponent(window.location.href);
        window.location.href = `${instituteUrl}?redirect_uri=${redirectUri}`;
      } else {
        // 如果URL未配置，给出提示，方便调试
        this.$message.error("研究院登录地址未配置！");
        console.error("研究院登录地址 'instituteUrl' 未在 Vuex store 中配置。");
      }
    },
    getTime() {
      setInterval(() => {
        this.time = dateFormat(new Date());
      }, 1000);
    },
    handleLogin() {
      const topUrl = getTopUrl();
      const redirectUrl = "/oauth/redirect/";
      const ssoCode = "?code=";
      this.socialForm.source = getQueryString("source");
      this.socialForm.code = getQueryString("code");
      this.socialForm.state = getQueryString("state");
      if (
        validatenull(this.socialForm.source) &&
        topUrl.includes(redirectUrl)
      ) {
        let source = topUrl.split("?")[0];
        source = source.split(redirectUrl)[1];
        this.socialForm.source = source;
      }
      if (
        topUrl.includes(redirectUrl) &&
        !validatenull(this.socialForm.source) &&
        !validatenull(this.socialForm.code) &&
        !validatenull(this.socialForm.state)
      ) {
        const loading = this.$loading({
          lock: true,
          text: "第三方系统登录中,请稍后。。。",
          spinner: "el-icon-loading",
        });
        this.$store
          .dispatch("LoginBySocial", this.socialForm)
          .then(() => {
            window.location.href = topUrl.split(redirectUrl)[0];
            this.$router.push({ path: this.tagWel.value });
            loading.close();
          })
          .catch(() => {
            loading.close();
          });
      } else if (
        !topUrl.includes(redirectUrl) &&
        !validatenull(this.socialForm.code) &&
        !validatenull(this.socialForm.state)
      ) {
        const loading = this.$loading({
          lock: true,
          text: "单点系统登录中,请稍后。。。",
          spinner: "el-icon-loading",
        });
        this.$store
          .dispatch("LoginBySso", this.socialForm)
          .then(() => {
            window.location.href = topUrl.split(ssoCode)[0];
            this.$router.push({ path: this.tagWel.value });
            loading.close();
          })
          .catch(() => {
            loading.close();
          });
      }
    },
  },
};
</script>

<style lang="scss">
@import "@/styles/login.scss";

.login-container {
  background-image: none;
}

.footer-wrapper {
  display: flex; /* 启用 Flexbox 布局 */
  align-items: center; /* 垂直方向居中对齐 */
  justify-content: center; /* 水平方向居中对齐（可选） */
  height: 100%; /* 确保容器高度占满父级容器 */
}

.login-switch-institute {
  margin-top: 15px;
  text-align: center;
}
</style>

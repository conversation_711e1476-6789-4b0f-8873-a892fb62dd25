<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
      :cell-style="cellStyle"
    >
      <template #menuLeft>
        <el-button
          type="danger"
          size="mini"
          icon="el-icon-delete"
          plain
          v-if="permission.niMealTakeoutUserGroups_delete"
          @click="handleDelete"
          >删 除
        </el-button>
      </template>
      <template #menu="{ row, index }">
        <el-button
          type="text"
          icon="el-icon-video-play"
          size="mini"
          :style="{ color: colorName }"
          v-if="row.status === 2"
          @click="rowPlay(row)"
          >启用
        </el-button>
        <el-button
          type="text"
          icon="el-icon-video-pause"
          size="mini"
          style="color: #f56c6c"
          v-if="row.status === 1"
          @click="rowPause(row)"
          >停用
        </el-button>
        <el-button
          type="text"
          icon="el-icon-user"
          size="mini"
          @click="rowMembers(row)"
          >用户
        </el-button>
      </template>
    </avue-crud>
    <user-group-members-drawer ref="userGroupMembersRef" />
  </basic-container>
</template>

<script>
import {
  add,
  getDetail,
  getList,
  pause,
  play,
  remove,
  update,
} from "@/api/ni/meal/takeout-user-groups";
import { mapGetters } from "vuex";
import UserGroupMembersDrawer from "@/views/ni/meal/takeout/components/UserGroupMembersDrawer.vue";

export default {
  components: {
    UserGroupMembersDrawer,
  },
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        delBtn: false,
        span: 12,
        dialogDrag: true,
        size: "mini",
        searchSize: "mini",
        align: "center",
        searchIndex: 3,
        searchIcon: true,
        searchEnter: true,
        height: "auto",
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        selection: true,
        dialogClickModal: false,
        column: [
          {
            label: "名称",
            prop: "name",
            search: true,
            minWidth: 140,
            rules: [
              {
                required: true,
                message: "请输入名称",
                trigger: "blur",
              },
            ],
          },
          {
            label: "人员",
            prop: "members",
            minWidth: 200,
            addDisplay: false,
            editDisplay: false,
          },
          {
            label: "备注",
            prop: "remark",
            type: "textarea",
            span: 24,
            minRows: 2,
            minWidth: 180,
          },
          {
            label: "创建人",
            prop: "createUserName",
            type: "input",
            minWidth: 100,
            addDisplay: false,
            editDisplay: false,
          },
          {
            label: "创建时间",
            prop: "createTime",
            minWidth: 130,
            addDisplay: false,
            editDisplay: false,
          },
          {
            label: "状态",
            prop: "status",
            type: "select",
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_meal_takeout_status",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            minWidth: 90,
            dataType: "number",
            search: true,
            addDisplay: false,
            editDisplay: false,
          },
        ],
      },
      data: [],
    };
  },
  computed: {
    ...mapGetters(["permission", "colorName"]),
    permissionList() {
      return {
        addBtn: this.vaildData(
          this.permission.niMealTakeoutUserGroups_add,
          false
        ),
        viewBtn: this.vaildData(
          this.permission.niMealTakeoutUserGroups_view,
          false
        ),
        delBtn: this.vaildData(
          this.permission.niMealTakeoutUserGroups_delete,
          false
        ),
        editBtn: this.vaildData(
          this.permission.niMealTakeoutUserGroups_edit,
          false
        ),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  methods: {
    rowSave(row, done, loading) {
      add(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          window.console.log(error);
        }
      );
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          console.log(error);
        }
      );
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    rowMembers(row) {
      this.$refs.userGroupMembersRef.onShow(row.id);
    },
    rowPause(row) {
      this.$confirm("确定停用选择数据?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return pause(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    rowPlay(row) {
      this.$confirm("确定启用选择数据?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return play(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen(done, type) {
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then((res) => {
          this.form = res.data.data;
        });
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
    cellStyle({ row, column }) {
      if ("status" === column.columnKey) {
        return {
          backgroundColor: row.status === 2 ? "#F56C6C" : this.colorName,
          color: "#fff",
        };
      }
    },
  },
};
</script>

<style></style>

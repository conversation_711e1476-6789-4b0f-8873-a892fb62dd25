<template>
  <basic-container>
    <avue-crud :option="option" :table-loading="loading" :data="data" :page.sync="page" :permission="permissionList"
      :before-open="beforeOpen" v-model="form" ref="crud" @row-update="rowUpdate" @row-save="rowSave" @row-del="rowDel"
      @search-change="searchChange" @search-reset="searchReset" @selection-change="selectionChange"
      @current-change="currentChange" @size-change="sizeChange" @refresh-change="refreshChange" @on-load="onLoad">
      <!-- ----------------------------------- -->
      <!-- -------------显示详情-------------- -->
      <template #idNo="{ row, index }">
        <el-button size="mini" @click="$refs.crud.rowView(row, index)" type="text">{{ row.idNo }}</el-button>
      </template>

      <template #status="{ row, index }">
        <el-tag v-if="row.status === 1" size="mini" type="info">
          {{ statusDictKeyValue[row.status] }}
        </el-tag>
        <el-tag v-if="row.status === 2" size="mini" type="success">
          {{ statusDictKeyValue[row.status] }}
        </el-tag>
        <el-tag v-if="row.status === 3" size="mini" type="warning">
          {{ statusDictKeyValue[row.status] }}
        </el-tag>
        <el-tag v-if="row.status === 4" size="mini" type="danger">
          {{ statusDictKeyValue[row.status] }}
        </el-tag>
      </template>

      <template #condition="{ row, label }">
        <span v-if="row.condition == 1" style="color: #176d06; fonrgb(20, 107, 2)t: bolder">
          {{ label }}</span>
        <span v-else-if="row.condition == 2" style="color: #ff0000; fonrgb(20, 107, 2)t: bolder">
          {{ label }}</span>
        <span v-else-if="row.condition == 3" style="color: #ff9900; fonrgb(20, 107, 2)t: bolder">
          {{ label }}</span>
        <span v-else-if="row.condition == 4" style="color: #282729; fonrgb(20, 107, 2)t: bolder">
          {{ label }}</span>
      </template>

      <!-- 上次检修日期 -->
      <template #lastCheckDate="{ row }">
        <div v-if="row.lastCheckDate == null">
          未曾检修
        </div>
        <div v-else>
          {{ row.lastCheckDate }}
        </div>
      </template>
      <!-- 下次检修日期 -->
      <template #nextCheckDate="{ row }">
        <div v-if="row.nextCheckDate == null || row.nextCheckDate == row.scrapDate">
          即将报废
        </div>
        <div v-else>
          {{ row.nextCheckDate }}
        </div>
      </template>




      <template #menuLeft v-if="authority != 2">
        <el-button type="danger" size="mini" icon="el-icon-delete" plain @click="handleDelete">删 除
        </el-button>

        <el-button type="warning" size="mini" icon="el-icon-s-check" plain @click="handleMassCheck">批量登记
        </el-button>

        <!-- <el-button type="success"
                     size="mini"
                     icon="el-icon-s-custom"
                     plain
                     @click="handleChangeMananger"
          >修改管理员
          </el-button>-->

        <el-button type="info" icon="el-icon-setting" size="mini" @click="handleNotice">
          通知设置
        </el-button>

        <el-button type="warning" size="mini" icon="el-icon-upload2" plain @click="handleImport">导入
        </el-button>

        <el-button type="success" size="mini" icon="el-icon-download" plain @click="handleExport">导出
        </el-button>
      </template>



      <template #menuRight="{ size }">
        <el-button icon="el-icon-time" circle :size="size" @click="handleLog"></el-button>
      </template>

      <!-------------------------------------------------- 操作栏------------------------------------------ -->
      <template #menu="{ row, index, size }">
        <el-button type="text" icon="el-icon-edit" :size="size" v-if="authority != 2 && [1, 3].includes(row.status)"
          @click="$refs.crud.rowEdit(row, index)">编 辑
        </el-button>
        <el-button type="text" icon="el-icon-s-promotion" :size="size" v-if="authority != 2 && [1, 3].includes(row.status)"
          @click="rowSubmit(row)">提交
        </el-button>
        <el-button type="text" icon="el-icon-delete" :size="size" v-if="row.status === 2 && authority == 1"
          @click="rowBack(row)">撤回
        </el-button>

        <el-button type="text" icon="el-icon-s-check" size="small" v-if="authority == 1"
          @click.stop="handleDataSub(row)">登记
        </el-button>
        <el-button type="text" icon="el-icon-s-check" size="small"
          v-else-if="[3, 4].includes(row.condition) || row.status != 2">暂时无法登记
        </el-button>
        <el-button type="text" icon="el-icon-s-check" size="small" v-else @click.stop="handleDataSub(row)">登记
        </el-button>

        <el-divider direction="vertical" />
        <el-dropdown>
          <el-button type="text" :size="size" v-if="authority == 1">
            更多<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item type="text" icon="el-icon-plus" :size="size" @click.native="rowAddSecondHeadUser(row)">
              新增第二负责人
            </el-dropdown-item>

            <el-dropdown-item type="text" icon="el-icon-upload2" :size="size" @click.native="rowAttach(row)">
              上传图片
            </el-dropdown-item>

            <el-dropdown-item type="text" icon="el-icon-time" :size="size" @click.native="rowLog(row, index)">
              操作日志
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>


      </template>
      <!--------------------------------------------------- 弹框 ------------------------------------------------->
      <template #menuForm="{ row, index, type }">
        <el-button type="primary" icon="el-icon-plus" size="mini" v-if="type === 'add'" @click="$refs.crud.rowSave()">
          新增
        </el-button>

        <el-button type="primary" icon="el-icon-s-promotion" size="mini" v-if="['edit', 'add'].includes(type)"
          @click="handleSubmit(type)">
          提交
        </el-button>
        <el-button icon="el-icon-check" size="mini" v-if="['edit', 'add'].includes(type)"
          @click="$refs.crud.closeDialog()">
          取消
        </el-button>
      </template>
    </avue-crud>

    <!-- -----------------------------------修改管理员弹窗---------------------------------------------- -->
    <!-- <el-dialog
        :visible.sync="exAdminSelectVisible"
        width="400px"
        title="灭火器管理员选择"
        center
        append-to-body
        destroy-on-close="true"
      >
      <user-select
            :size="size"
            :disabled="disabled"
            multiple="true"
            @confirm="userSelectConfirm"
        />
      <avue-form
        @submit="handleUserSelectSubmit">
      </avue-form>
      </el-dialog> -->

    <!-- -------------------------- --------------------------------------------------------->
    <el-drawer :title="'灭火器检查登记'" :visible.sync="subVisible" :direction="direction" append-to-body
      :before-close="handleSubClose" size="1000px">
      <basic-container>
        <avue-crud :option="optionSub" :data="dataSub" :page.sync="pageSub" v-model="formSub"
          :table-loading="loadingSub" ref="crudSub" @row-del="rowDelSub" @row-update="rowUpdateSub"
          @row-save="rowSaveSub" :before-open="beforeOpenSub" @search-change="searchChangeSub"
          @search-reset="searchResetSub" @selection-change="selectionChangeSub" @current-change="currentChangeSub"
          @size-change="sizeChangeSub" @on-load="onLoadSub">
          <!-- --------------------条件渲染----------------------- -->
          <!-- 瓶体状态 -->
          <template #bodyStatus="{ row }">
            <el-tag v-if="row.bodyStatus === 1" size="mini" type="success">
              正常
            </el-tag>
            <el-tag v-if="row.bodyStatus === 2" size="mini" type="danger">
              故障
            </el-tag>
          </template>
          <!-- 把手状态 -->
          <template #handleStatus="{ row }">
            <el-tag v-if="row.handleStatus === 1" size="mini" type="success">
              正常
            </el-tag>
            <el-tag v-if="row.handleStatus === 2" size="mini" type="danger">
              故障
            </el-tag>
          </template>
          <!-- 喷咀状态 -->
          <template #jetStatus="{ row }">
            <el-tag v-if="row.jetStatus === 1" size="mini" type="success">
              正常
            </el-tag>
            <el-tag v-if="row.jetStatus === 2" size="mini" type="danger">
              故障
            </el-tag>
          </template>
          <!-- 压力状态 -->
          <template #pressureStatus="{ row }">
            <el-tag v-if="row.pressureStatus === 1" size="mini" type="success">
              正常
            </el-tag>
            <el-tag v-if="row.pressureStatus === 2" size="mini" type="danger">
              故障
            </el-tag>
          </template>
          <!-- 保险销状态 -->
          <template #safetyPinStatus="{ row }">
            <el-tag v-if="row.safetyPinStatus === 1" size="mini" type="success">
              正常
            </el-tag>
            <el-tag v-if="row.safetyPinStatus === 2" size="mini" type="danger">
              故障
            </el-tag>
          </template>


          <!-- ------------------------------------------ -->
          <!-- 上按钮栏 -->
          <template slot="menuLeft" v-if="authority != 2">
            <el-button type="danger" size="mini" icon="el-icon-delete" plain @click="handleDeleteSub">删 除
            </el-button>

          </template>
        </avue-crud>
      </basic-container>
    </el-drawer>
    <!-- ------------------------------------------------------------------------------- -->
    <!-- 外层弹窗 通知设置 -->
    <el-dialog :visible.sync="noticeVisible" width="400px" title="通知设置" center append-to-body>
      <avue-form :option="noticeOption" v-model="noticeForm" @submit="handleNoticeSubmit">
      </avue-form>
      <el-button type="primary" @click="handleRummagerInfo">巡检人信息</el-button>
      <!-- 内层弹窗 巡检信息 -->
      <el-dialog width="30%" title="巡检人信息" :visible.sync="innerVisible" append-to-body>
        <el-table :data="gridData">
          <el-table-column property="noticeUserName" label="负责人姓名" width="120"></el-table-column>
          <el-table-column property="noticeUserDept" label="部门" width="150"></el-table-column>
          <el-table-column label="巡检日期" width="100">
            <!-- 使用自定义插槽来渲染单元格内容 -->
            <template slot-scope="scope">
              <!-- 在数字后面添加 "号" 字 -->
              {{ scope.row.checkDay }}号
            </template>
          </el-table-column>
          <el-table-column label="通知日期" width="130">
            <!-- 使用自定义插槽来渲染单元格内容 -->
            <template slot-scope="scope">
              <!-- 在数字后面添加 "天" 字 -->
              {{ scope.row.checkDay - scope.row.aheadDays }}号
            </template>
          </el-table-column>
        </el-table>
      </el-dialog>

    </el-dialog>
    <!-- ------------------------------------------------------------------------------- -->
    <attach-dialog ref="attachDialogRef" />
    <log-opt-dialog ref="logOptDialogRef" :module="module" />
    <user-select-dialog ref="userSelectDialogRef" @confirm="headUserSelectConfirm" />

    <el-dialog title="灭火器数据导入" append-to-body :visible.sync="excelBox" width="555px">
      <avue-form :option="excelOption" v-model="excelForm" :upload-after="uploadAfter">
        <template slot="excelTemplate">
          <el-button type="primary" @click="handleTemplate">
            点击下载<i class="el-icon-download el-icon--right"></i>
          </el-button>
        </template>
      </avue-form>
    </el-dialog>

  </basic-container>
</template>



<script>
// 导入js包下的方法
import { getDetail, getPage, remove, update, submit, back, save, notice, getRummagerInfo, addSecondUser } from "@/api/ni/ehs/extinguisher";
import {
  save as addSub,
  getDetail as getDetailSub,
  getPage as getListSub,
  remove as removeSub,
  update as updateSub,
  massCheck
} from "@/api/ni/ehs/extinguisher-check";
import { mapGetters } from "vuex";
import LogOptDialog from "@/components/log-opt-dialog";
/////////////////////////////////////////////////////////
// import { getDeptTree } from "@/api/system/dept";
import AttachDialog from "@/components/attach-dialog";//添加附件
import { dateNow1 } from "@/util/date";//默认当前时间
// import UserSelect from "@/components/user-select";
import UserSelectDialog from "@/components/user-select-dialog";

import { downloadXls } from "@/util/util";
import { exportBlob } from "@/api/common";
import { getToken } from "@/util/auth";

///////////////////////////////////////////////////////
/////////////////////////////////////////////////////

export default {
  components: {
    LogOptDialog,
    AttachDialog,//添加附件
    // UserSelect,
    UserSelectDialog,
  },
  data() {
    return {

      // /////////////////////////////////////////////////////////////

      // ///////////////////////////////////////////////////////////////////

      module: 'ni_ehs_extinguisher',
      exAdminSelectVisible: false,//管理员选择弹窗
      finishTaskShow: false,
      taskLogShow: false,
      taskShow: false,
      row: {},
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      selectionList: [],

      authority: 0,//权限=> 负责人：2,管理员：1

      option: {
        labelWidth: 110,
        span: 12,
        addBtn: true,
        saveBtn: false,
        cancelBtn: false,
        delBtn: false,
        editBtn: false,
        updateBtn: false,
        searchIndex: 3,
        searchIcon: true,
        size: 'mini',
        searchSize: 'mini',
        align: 'center',
        rowKey: 'id',
        rowParentKey: 'parentId',
        defaultExpandAll: true,
        height: 'auto',
        calcHeight: -30,//表格高度
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        viewBtn: false,
        selection: true,
        dialogClickModal: false,
        receiveAdminId: null,//管理员选择弹窗接收的id
        receiveSecondUserRowId: null,//第二负责人的灭火器id
        column: [
          {
            label: "状态",
            prop: "status",
            dicData: [],
            // search: true,
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            dataType: "number",
            display: false,
            // type: "select",
          },
          {
            label: "灭火器编号",
            prop: "idNo",
            type: "input",
            search: true,
            display: true,
            overHidden: true,
            width: 130,
            rules: [
              {
                required: true,
                message: "请输入灭火器唯一标识码",
                trigger: "blur",
              },
            ],
          },
          {
            label: "灭火器型号",
            prop: "extinguisherType",
            type: "select",
            search: true,
            dataType: "number",
            dicUrl: `/api/blade-system/dict-biz/dictionary?code=ni_ehs_extinguisher_type`,
            display: true,
            overHidden: true,
            props: {
              label: "dictValue",
              value: "dictKey"
            },
            change: ({ value }) => {
              if (value == 1) {
                this.form.extinguisherClass = "8kg干粉灭火器";
                this.form.fireClass = "4A 144B C E"
              } else if (value == 2) {
                this.form.extinguisherClass = "4kg干粉灭火器";
                this.form.fireClass = "2A 55B C E"
              } else if (value == 3) {
                this.form.extinguisherClass = "3kg二氧化碳灭火器(旧)";
                this.form.fireClass = "21B"
              } else if (value == 4) {
                this.form.extinguisherClass = "3kg二氧化碳灭火器(新)";
                this.form.fireClass = "21B"
              } else if (value == 5) {
                this.form.extinguisherClass = "2kg干粉灭火器";
                this.form.fireClass = "1A 21B C E"
              } else if (value == 6) {
                this.form.extinguisherClass = "3kg干粉灭火器";
                this.form.fireClass = "2A 34B C E"
              }
            },
            rules: [
              {
                required: true,
                message: "请选择灭火器型号",
                trigger: "blur",
              },
            ],
          },
          {
            label: "灭火器类别",
            prop: "extinguisherClass",
            type: "input",
            disabled: true,
            search: true,
            display: true,
            overHidden: true,
          },
          {
            label: "灭火级别",
            prop: "fireClass",
            type: "input",
            disabled: true,
            search: true,
            display: true,
            overHidden: true,
          },
          {
            label: "购买（充装）日期",
            prop: "buyDate",
            type: "date",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            search: false,
            display: true,
            labelWidth: 150,
            overHidden: true,
            rules: [
              {
                required: true,
                message: "请选择购买（充装）日期",
                trigger: "blur",
              },
            ],
          },
          {
            label: "购买（充装）日期",
            prop: "daterange",
            type: "daterange",
            format: 'yyyy-MM-dd',
            valueFormat: "yyyy-MM-dd",
            startPlaceholder: '日期开始范围',
            endPlaceholder: '日期结束范围',
            search: true,
            searchRange: true,
            hide: true,
            display: false,
          },
          {
            label: "配备位置",
            prop: "position",
            type: "input",
            search: true,
            display: true,
            overHidden: true,
            width: 130,
            rules: [
              {
                required: true,
                message: "请输入配备位置",
                trigger: "blur",
              },
            ],
          },
          {
            label: "责任部门",
            prop: "headDept",
            // type: "input",
            search: true,
            display: false,
            hide: true,
            type: "tree",
            dicUrl: `/api/blade-system/dept/list`,
            props: {
              label: "deptName",
              value: "id",
            },
          },
          {
            label: "责任部门",
            prop: "headDeptName",
            type: "input",
            // search: true,
            display: false,
            overHidden: true,
          },
          {
            label: "风险控制组（负责人）",
            prop: "headUser",
            type: "tree",
            search: true,
            display: true,
            // hide:true,
            labelWidth: 170,
            hide: true,
            dicUrl: `/api/blade-user/user-list`,
            props: {
              label: "name",
              value: "id",
            },
            rules: [
              {
                required: true,
                message: "请输入负责人",
                trigger: "blur",
              },
            ],
          },
          {
            label: "风险控制组（负责人）",
            prop: "headUserName",
            type: "input",
            // search: true,
            display: false,
            labelWidth: 170,
          },
          {
            label: "是否检修过",
            prop: "isChecked",
            type: "radio",
            display: true,
            hide: true,
            dicData: [{
              label: '是',
              value: 1
            }, {
              label: '否',
              value: 0
            }],
            value: 1,
            change: ({ value }) => {
              const lastCheckDate = this.findObject(this.option.column, "lastCheckDate");
              if (value == 1) {
                console.log(lastCheckDate);
                lastCheckDate.display = true;
              } else if (value == 0) {
                lastCheckDate.display = false;
              }
            },
            rules: [
              {
                required: true,
                message: "请选择",
                trigger: "blur",
              },
            ],
          },
          {
            label: "上次检修日期",
            prop: "lastCheckDate",
            type: "date",
            format: 'yyyy-MM-dd',
            valueFormat: "yyyy-MM-dd",
            display: false,
            rules: [
              {
                required: true,
                message: "请选择上次检修日期",
                trigger: "blur",
              },
            ],
          },
          {
            label: "下次检修日期",
            prop: "nextCheckDate",
            type: "date",
            format: 'yyyy-MM-dd',
            valueFormat: "yyyy-MM-dd",
            display: false,
          },
          {
            label: "下次检修日期",
            prop: "nextCheckDaterange",
            type: "daterange",
            format: 'yyyy-MM-dd',
            valueFormat: "yyyy-MM-dd",
            startPlaceholder: '日期开始范围',
            endPlaceholder: '日期结束范围',
            search: true,
            searchRange: true,
            hide: true,
            display: false,
          },
          {
            label: "报废日期",
            prop: "scrapDate",
            type: "date",
            display: false,
            format: 'yyyy-MM-dd',
            valueFormat: "yyyy-MM-dd",
            rules: [
              {
                required: true,
                message: "请选择报废日期",
                trigger: "blur",
              },
            ],
          },
          {
            label: "报废日期",
            prop: "scrapDaterange",
            type: "daterange",
            format: 'yyyy-MM-dd',
            valueFormat: "yyyy-MM-dd",
            startPlaceholder: '日期开始范围',
            endPlaceholder: '日期结束范围',
            search: true,
            searchRange: true,
            hide: true,
            display: false,
          },
          // {
          //       label: "灭火器管理员",
          //       prop: "exAdmin",
          //       type: "tree",
          //       search: true,
          //       display: true,
          //       dicUrl:`/api/blade-user/user-list`,
          //       props: {
          //         label: "name",
          //         value: "id",
          //       },
          //       rules: [
          //         {
          //           required: true,
          //            message: "请输入管理员",
          //            trigger: "blur",
          //         },
          //       ],
          // },
          {
            label: "灭火器状况",
            prop: "condition",
            type: "select",
            // dataType: "number",
            search: true,
            display: false,
            dataType: "number",
            dicUrl: `/api/blade-system/dict-biz/dictionary?code=ni_ehs_extinguisher_condition`,
            props: {
              label: "dictValue",
              value: "dictKey"
            },
          },
        ]

      },

      // //////////////////////////////////////////////////////
      subVisible: false,
      direction: "rtl",
      extinguisherId: 0,
      exType: 0,
      formSub: {},
      querySub: {},
      loadingSub: true,
      dataSub: [],
      selectionListSub: [],
      pageSub: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },

      optionSub: {
        searchIndex: 3,
        searchIcon: true,
        size: "mini",
        searchSize: "mini",
        height: "auto",
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        viewBtn: true,
        selection: true,
        menuWidth: 300,
        column: [
          {
            label: "灭火器id",
            prop: "extinguisherId",
            type: "input",
            hide: true,
            display: false,
          },
          {
            label: "检查时间",
            prop: "checkDate",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            type: "date",
            width: 100,
            rules: [
              {
                required: true,
                message: "请输入检查时间",
                trigger: "blur",
              },
            ],
          },
          {
            label: "检查人",
            prop: "rummager",
            type: "tree",
            dicUrl: `/api/blade-user/user-list`,
            props: {
              label: "name",
              value: "id",
            },
            allowCreate: true,
            filterable: true,
            rules: [
              {
                required: true,
                message: "请输入检查人",
                trigger: "blur",
              },
            ],
          },
          {
            label: "瓶体状态",
            prop: "bodyStatus",
            type: "radio",
            span: 24,
            dicUrl: '/api/blade-system/dict-biz/dictionary?code=ni_ehs_extinguisher_status',
            dataType: "number",
            value: 1,
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            rules: [
              {
                required: true,
                message: "请输入瓶体状态",
                trigger: "blur",
              },
            ],
          },
          {
            label: "把手状态",
            prop: "handleStatus",
            type: "radio",
            span: 24,
            dataType: "number",
            value: 1,
            dicUrl: '/api/blade-system/dict-biz/dictionary?code=ni_ehs_extinguisher_status',
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            rules: [
              {
                required: true,
                message: "请输入把手状态",
                trigger: "blur",
              },
            ],
          },
          {
            label: "喷咀状态",
            prop: "jetStatus",
            type: "radio",
            dataType: "number",
            value: 1,
            span: 24,
            dicUrl: '/api/blade-system/dict-biz/dictionary?code=ni_ehs_extinguisher_status',
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            rules: [
              {
                required: true,
                message: "请输入喷咀状态",
                trigger: "blur",
              },
            ],
          },
          {
            label: "压力状态",
            prop: "pressureStatus",
            type: "radio",
            span: 24,
            dataType: "number",
            value: 1,
            dicUrl: '/api/blade-system/dict-biz/dictionary?code=ni_ehs_extinguisher_status',
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            rules: [
              {
                required: true,
                message: "请输入压力状态",
                trigger: "blur",
              },
            ],
          },
          {
            label: "保险销状态",
            prop: "safetyPinStatus",
            type: "radio",
            span: 24,
            dataType: "number",
            value: 1,
            dicUrl: '/api/blade-system/dict-biz/dictionary?code=ni_ehs_extinguisher_status',
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            labelWidth: 100,
            width: 100,
            rules: [
              {
                required: true,
                message: "请输入保险销状态",
                trigger: "blur",
              },
            ],
          },

          {
            label: "备注",
            prop: "remark",
            type: "textarea",
            span: 24,
            width: 120,
            overHidden: true
          },
          {
            label: "图片上传",
            prop: "imgUrl",
            type: "upload",
            drag: true,
            loadText: "图片上传中，请稍等",
            span: 24,
            dataType: 'string',
            // props: {
            //   label: 'link',
            //   value: 'url'
            // },
            propsHttp: {
              res: 'data',
              url: 'link',
              // name: 'originalName'
            },
            action:
              "/api/blade-resource/oss/endpoint/put-file-attach-watermark?code=public",
          },
        ],
      },
      // /////////////////////////灭火器检查通知设置弹窗/////////////////////////////
      noticeVisible: false,//外层弹窗
      innerVisible: false,//内层弹窗
      // 内层弹窗数据
      gridData: [],
      noticeForm: {},
      noticeOption: {
        span: 24,
        size: "mini",
        column: [
          {
            label: "每月定期检查日",
            prop: "checkDay",
            type: "number",
            value: 8,
            labelWidth: 120,
            min: 1,
            max: 31,
          },
          {
            label: "提前通知天数",
            prop: "aheadDays",
            type: "number",
            value: "3",
            labelWidth: 110,
            min: 0,
            max: 30
          }
        ],
      },
      // /////////////////////////////////////////////////////////////////////////

      data: [],
      statusDict: [],
      statusDictKeyValue: {},
      typeDict: [],
      typeDictKeyValue: {},
      conditionList:["正常","故障","待检修","已报废"],

      /*
      *数据导入
      */
      excelBox: false,
      excelOption: {
        size: "mini",
        searchSize: "mini",
        submitBtn: false,
        emptyBtn: false,

        column: [
          {
            label: "数据上传",
            prop: "excelFile",
            type: "upload",
            drag: true,
            loadText: "数据上传中，请稍等",
            span: 24,
            data: {},
            propsHttp: {
              res: "data",
            },
            tip: "请上传 .xls,.xlsx 标准格式文件",
            action: "/api/ni/ehs/extinguisher/import",
          },
          {
            label: "模板下载",
            prop: "excelTemplate",
            formslot: true,
            span: 24,
          },
        ],
      },
      excelForm: {},

    };
  },
  computed: {
    ...mapGetters(["permission"]),
    ////////////////////////////////

    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.projecttask_add, false),
        viewBtn: this.vaildData(this.permission.projecttask_view, false),
        delBtn: this.vaildData(this.permission.projecttask_delete, false),
        editBtn: this.vaildData(this.permission.projecttask_edit, false)
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach(ele => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
    subIds() {
      let ids = [];
      this.selectionListSub.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },

  },
  mounted() {
    this.dictInit();
  },
  created() {
    this.$http
      .post("/api/ni/ehs/extinguisher/getAuthority")
      .then((res) => {
        this.authority = res.data.data;
        if (this.authority != 1) {
          this.option.addBtn = false;
        }
        // console.log(res.data.data);
      })
  },

  methods: {

    //数据导出
    handleExport() {
      this.$confirm("是否导出当前筛选的所有数据？", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.handleExportData();
      });
    },
    // 导出数据处理
    async handleExportData() {
      let opt = {
        column: [
          {
            label: "灭火器编号",
            prop: "idNo",
          },
          {
            label: "灭火器类别",
            prop: "extinguisherClass",
          },
          {
            label: "灭火级别",
            prop: "fireClass",
          },
          {
            label: "购买（充装）日期",
            prop: "buyDate",
          },
          {
            label: "配备位置",
            prop: "position",
          },
          {
            label: "责任部门",
            prop: "headDeptName",
          },
          {
            label: "风险控制组（负责人）",
            prop: "headUserName",
          },
          {
            label: "上次检修日期",
            prop: "lastCheckDate",
          },
          {
            label: "下次检修日期",
            prop: "nextCheckDate",
          },
          {
            label: "报废日期",
            prop: "scrapDate",
          },
          {
            label: "灭火器状况",
            prop: "condition",
          },

        ],
      };
      await this.getExportData().then(() => {
        this.$Export.excel({
          title: "灭火器导出数据",
          columns: opt.column,
          data: this.exportData.map((item) => {
            return {
              ...item,
              condition: this.conditionList[item.condition - 1],
            };
          }),
        });
        this.exportData = [];
      });
    },
    //获取搜索的打印数据
    async getExportData() {
      //每次获取必须初始化一下，不然会把之前缓存额数据一并带入
      this.exportData = [];
      const query = { ...this.params, ...this.query };
      await getPage(1, 10000, query).then(res => {
        this.exportData = res.data.data.records;
      });
    },

    rowLog(row) {
      this.$refs.logOptDialogRef.init(row.id)
    },
    handleLog() {
      this.$refs.logOptDialogRef.init()
    },

    dictInit() {

      this.$http
        .get("/api/blade-system/dict-biz/dictionary?code=ni_ehs_status")
        .then((res) => {
          const column = this.findObject(this.option.column, "status");
          column.dicData = res.data.data;
          this.statusDict = res.data.data;
          this.statusDictKeyValue = this.statusDict.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });


    },

    handleTaskLog(row) {
      this.row = row
      this.taskLogShow = true
    },

    handleFinishTask(row) {
      this.row = row
      this.finishTaskShow = true
    },
    handleStartTask(row) {
      this.row = row
      this.taskShow = true
    },

    /////////////////////////////////////////////////////////
    handleNotice() {
      this.noticeVisible = true;
    },
    // 巡检通知提交
    handleNoticeSubmit(row, done) {
      notice(row).then(
        () => {
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
          this.noticeVisible = false;
        }, (error) => {
          // loading();
          alert(error);
          window.console.log(error);
        }
      )
      console.log(row);
    },
    //获取巡检人信息
    handleRummagerInfo() {
      this.innerVisible = true;
      // 调用后端接口获取数据
      getRummagerInfo().then((res) => {
        this.gridData = res.data.data;
        console.log(this.gridData);
      });
    },
    handleMassCheck() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("请确保批量登记的灭火器无故障", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return massCheck(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          this.$refs.crud.toggleSelection();
        });
    },
    /////////////////////////////////////////////////////灭火器管理员选择
    // userSelectConfirm(ids){
    //   this.form.receiveAdminId = ids;
    //   console.log(this.form.receiveAdminId);
    // },
    // handleChangeMananger(){
    //   if (this.selectionList.length === 0) {
    //     this.$message.warning("请选择至少一条数据");
    //     return;
    //   }
    //   this.exAdminSelectVisible = true;
    // },
    // handleUserSelectSubmit(done){
    //     if(this.form.receiveAdminId != null){
    //       console.log(this.ids)
    //       console.log(this.form.receiveAdminId)
    //       changeMananger(this.ids,this.form.receiveAdminId).then(() => {
    //         this.onLoad(this.page);
    //         this.$message({
    //         type: "success",
    //         message: "操作成功!"
    //       });
    //         this.exAdminSelectVisible = false;
    //         done();
    //       })
    //     }
    //   },
    /////////////////////////////////////////////////////////////模板下载
    handleTemplate() {
      exportBlob(
        `/api/ni/ehs/extinguisher/export-template?${this.website.tokenHeader
        }=${getToken()}`
      ).then((res) => {
        downloadXls(res.data, "灭火器数据导入模板.xlsx");
      });
    },
    handleImport() {
      this.excelBox = true;
    },
    //////////////////////////////////////////////////////////////
    rowSave(row, done, loading) {

      // add(row).then(() => {
      save(row).then(() => {
        console.log(row);
        this.onLoad(this.page);

        this.$message({
          type: "success",
          message: "操作成功!"
        });
        done();
      }, error => {
        loading();
        window.console.log(error);
      });
    },
    rowUpdate(row, index, done, loading) {

      update(row).then(() => {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
        done();
      }, error => {
        loading();
        console.log(error);
      });
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        });
    },
    // ////////////////////////////////////////////////提交数据
    rowSubmit(row) {
      this.$confirm("此操作将提交该数据，是否继续?", '提示', {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        center: true
      })
        .then(() => {
          return submit(row.id)
          // return save(row);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        });
    },

    ////////////////////////////////////////////撤回
    rowBack(row) {
      this.$confirm("此操作撤回提交的数据，是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        center: true,
      })
        .then(() => {
          return back(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    // ///////////////////////////////////////////////新增第二负责人
    rowAddSecondHeadUser(row) {
      this.$refs.userSelectDialogRef.init();
      this.form.receiveSecondUserRowId = row.id;
    },
    headUserSelectConfirm(id) {
      if (this.form.receiveSecondUserRowId == null) return null;
      console.log(this.form.receiveSecondUserRowId);
      addSecondUser(this.form.receiveSecondUserRowId, id).then(() => {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
      });
    },
    //////////////////////////////////////////////////
    rowAttach(row) {
      this.$refs.attachDialogRef.init(row.id, this.module);
    },
    ////////////////////////////////////////////////

    handleSubmit(type) {
      this.form.status = 2
      if (type === 'add') {
        this.$refs.crud.rowSave()
      } else if (type === 'edit') {
        this.$refs.crud.rowUpdate()
      }
    },

    //////////////////////////////////////////////
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen(done, type) {
      console.log(this.data);
      for (var i = 0; i < this.data.length; i++) {
        console.log(this.data[i].headId);

      }
      /////////////////////////////默认当前时间
      if ("add" == type) {
        this.form = {
          taskTime: dateNow1(),
        }
      }

      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then((res) => {
          this.form = res.data.data;
        });
      }
      done();
    }
    ,
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    }
    ,
    //////////////////////////////////////////////搜索按钮
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;

      // 购买充装日期
      if (params.daterange && params.daterange.length === 2) {
        params.startTime = params.daterange[0]; // 添加 startTime 参数
        params.endTime = params.daterange[1];
      }
      // 下次检修日期
      if (params.nextCheckDaterange && params.nextCheckDaterange.length === 2) {
        params.startTime1 = params.nextCheckDaterange[0];
        params.endTime1 = params.nextCheckDaterange[1];
      }
      // 报废日期
      if (params.scrapDaterange && params.scrapDaterange.length === 2) {
        params.startTime2 = params.scrapDaterange[0];
        params.endTime2 = params.scrapDaterange[1];
      }


      this.onLoad(this.page, params);

      done();
    }
    ,
    selectionChange(list) {
      this.selectionList = list;
    }
    ,
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    }
    ,
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    }
    ,
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    }
    ,
    /////////刷新按钮的单击事件
    refreshChange() {
      this.onLoad(this.page, this.query);
    }
    ,
    onLoad(page, params = {}) {
      this.loading = true;


      getPage(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data.data;

        this.page.total = data.total;/////////////记录总条数
        this.data = data.records;

        this.loading = false;
        this.selectionClear();
      });
    },

    // 子表模块
    handleDataSub(row) {
      console.log(row);
      this.subVisible = true;
      this.extinguisherId = row.id;
      this.exType = row.extinguisherType;
      console.log(this);
      console.log(this.extinguisherId);
      this.onLoadSub(this.pageSub);
    },
    handleSubClose(hide) {
      hide();
    },
    rowSaveSub(row, loading, done) {
      row = {
        ...row,
        extinguisherId: this.extinguisherId,
      };
      addSub(row).then(
        () => {
          loading();
          this.onLoadSub(this.pageSub);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        },
        (error) => {
          done();
          window.console.log(error);
        }
      );
    },
    rowUpdateSub(row, index, loading, done) {
      row = {
        ...row,
        extinguisherId: this.extinguisherId,
      };
      updateSub(row).then(
        () => {
          loading();
          this.onLoadSub(this.pageSub);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        },
        (error) => {
          done();
          window.console.log(error);
        }
      );
    },
    rowDelSub(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return removeSub(row.id);
        })
        .then(() => {
          this.onLoadSub(this.pageSub);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    handleDeleteSub() {
      if (this.selectionListSub.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return removeSub(this.subIds);
        })
        .then(() => {
          this.onLoadSub(this.pageSub);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crudSub.toggleSelection();
        });
    },
    ///////////////////////////登记弹窗新增打开前的操作////////////////////////////////
    beforeOpenSub(done, type) {
      // var pressure = this.findObject(this.optionSub.column, "pressureStatus");
      // pressure.display = true;
      if (["edit", "view"].includes(type)) {
        getDetailSub(this.formSub.id).then((res) => {
          this.formSub = res.data.data;
        });
      }

      // console.log(this.$refs.crudSub);
      //二氧化碳灭火器没有压力检查
      // console.log(this.exType)
      // if(this.exType == 3 || this.exType == 4){
      //     var pressureOx = this.findObject(this.optionSub.column, "pressureStatus");
      //     console.log(pressureOx);
      //     pressureOx.display = false;
      //     this.$forceUpdate(); // 强制更新组件
      // }
      this.formSub.checkDate = dateNow1(),
        done();
    },
    searchResetSub() {
      this.querySub = {};
      this.onLoadSub(this.pageSub);
    },
    searchChangeSub(params) {
      this.querySub = params;
      this.onLoadSub(this.pageSub, params);
    },
    selectionChangeSub(list) {
      this.selectionListSub = list;
    },
    currentChangeSub(currentPage) {
      this.pageSub.currentPage = currentPage;
    },
    sizeChangeSub(pageSize) {
      this.pageSub.pageSize = pageSize;
    },
    // refreshChange() {
    //   this.onLoad(this.page, this.query);
    // },
    onLoadSub(page, params = {}) {
      this.loadingSub = true;
      const values = {
        ...params,
        extinguisherId: this.extinguisherId,
      };
      getListSub(
        page.currentPage,
        page.pageSize,
        Object.assign(values, this.querySub)
      ).then((res) => {
        const data = res.data.data;
        this.pageSub.total = data.total;
        this.dataSub = data.records;
        this.selectionListSub = [];
        this.loadingSub = false;
      });
    },
  }
}
  ;
</script>

import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/ni/depot/allocation/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/ni/depot/allocation/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/ni/depot/allocation/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/ni/depot/allocation/save',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/ni/depot/allocation/update',
    method: 'post',
    data: row
  })
}

export const audit = (ids) => {
  return request({
    url: '/api/ni/depot/allocation/audit',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const back = (ids) => {
  return request({
    url: '/api/ni/depot/allocation/back',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const toVoid = (ids) => {
  return request({
    url: '/api/ni/depot/allocation/toVoid',
    method: 'post',
    params: {
      ids,
    }
  })
}


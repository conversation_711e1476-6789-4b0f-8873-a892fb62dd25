import request from '@/router/axios';

export const getList = (current, size, params, createDept) => {
  return request({
    url: '/api/ni/pa/PaLeave/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
      createDept,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/ni/pa/PaLeave/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/ni/pa/PaLeave/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/ni/pa/PaLeave/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/ni/pa/PaLeave/submit',
    method: 'post',
    data: row
  })
}

export const getDetailByProcessInsId = (processInsId) => {
  return request({
    url: '/api/ni/pa/PaLeave/detail',
    method: 'get',
    params: {
      processInsId
    }
  })
}

export const start = (row) => {
  return request({
    url: '/api/ni/pa/PaLeave/start',
    method: 'post',
    data: row
  })
}

export const saveAndStart = (processDefKey, row) => {
  return request({
    url: '/api/ni/pa/PaLeave/saveAndStart',
    method: 'post',
    params: {
      processDefKey
    },
    data: row
  })
}

export const getListAnnual = (current, size, params, createDept) => {
  return request({
    url: '/api/ni/pa/PaLeave/list-annual',
    method: 'get',
    params: {
      ...params,
      current,
      size,
      createDept,
    }
  })
}

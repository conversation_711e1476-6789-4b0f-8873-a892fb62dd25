<template>
  <basic-container>
    <avue-crud :option="option" :table-loading="loading" :data="data" :page.sync="page" ref="crud" v-model="form"
               :permission="permissionList" :before-open="beforeOpen" @row-save="rowSave" @row-update="rowUpdate"
               @row-del="rowDel" @search-change="searchChange" @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange" @size-change="sizeChange" @refresh-change="refreshChange"
               @on-load="onLoad"
               :cell-style="cellStyle">
      <template #serialNo="{ row }">
        <span @click="rowView(row)">{{ row.serialNo }}</span>
      </template>
      <template #menuLeft>
        <el-button type="primary" size="mini" icon="el-icon-plus" v-if="permission.transfer_add" @click="handleAdd">新增
        </el-button>
        <el-button type="danger" size="mini" icon="el-icon-delete" plain v-if="permission.transfer_delete"
                   @click="handleDelete">删除
        </el-button>
        <el-button type="warning" size="mini" icon="el-icon-truck" v-if="permission.transfer_add" @click="handleShip">发货
        </el-button>
      </template>
      <template #menu="{ row, index }">
        <el-button type="text" icon="el-icon-back" size="mini"
                   v-if="permission.transfer_red && row.status === 3 && !row.red"
                   @click="rowRed(row, index)">冲红
        </el-button>
        <el-button type="text" icon="el-icon-edit" size="mini" v-if="permission.transfer_edit && row.status === 1"
                   @click="$refs.crud.rowEdit(row, index)">编辑
        </el-button>
        <el-button type="text" icon="el-icon-delete" size="mini" v-if="permission.transfer_delete && row.status === 1"
                   @click="$refs.crud.rowDel(row, index)">删除
        </el-button>
        <el-button type="text" icon="el-icon-truck" size="mini" v-if="permission.transfer_ship && row.status === 1"
                   @click="rowShip(row)">发货
        </el-button>
        <el-button type="text" icon="el-icon-box" size="mini" v-if="permission.transfer_receive && row.status === 2"
                   @click="handleReceive(row)">收货
        </el-button>
      </template>
    </avue-crud>
    <transfer-form-dialog ref="transferFormDialogRef" @confirm="onLoad(page,query)"/>
  </basic-container>
</template>

<script>
import {add, audit, getDetail, getList, red, remove, ship, update} from '@/api/ni/fg/fgTransfer'
import {mapGetters} from 'vuex'
import {dateFormat} from "@/util/date";
import TransferFormDialog from "@/views/ni/fg/components/TransferFormDialog.vue";

export default {
  components: {TransferFormDialog},
  data() {
    return {
      form: {},
      loading: true,
      dialogVisible: false,
      query: {},
      data: [],
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        addBtn: false,
        editBtn: false,
        delBtn: false,
        span: 12,
        dialogDrag: true,
        size: "mini",
        searchSize: "mini",
        align: "center",
        searchIndex: 3,
        searchIcon: true,
        searchEnter: true,
        height: 'auto',
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        selection: true,
        dialogClickModal: false,
        column: [
          {
            label: '调拨单号',
            prop: 'serialNo',
            search: true,
          },
          {
            label: '调拨主题',
            prop: 'title',
            search: true,
          },
          {
            label: '调出仓库',
            prop: 'fromDepotId',
            type: 'select',
            dicUrl: "/api/ni/base/depot/info/list?status=2&type=1",
            props: {
              label: "name",
              value: "id",
              desc: "code",
            },
            search: true,
          },
          {
            label: '调入仓库',
            prop: 'toDepotId',
            type: 'select',
            dicUrl: "/api/ni/base/depot/info/list?status=2&type=1",
            props: {
              label: "name",
              value: "id",
              desc: "code",
            },
            search: true,
          },
          {
            label: "操作人",
            prop: "operator",
            type: "select",
            remote: true,
            dicUrl: `/api/blade-user/search/otj/user?name={{key}}`,
            props: {
              label: "realName",
              value: "id",
            },
            dicFormatter: (data) => {
              return data.data.records;
            },
            placeholder: " ",
            filterable: true,
            overHidden: true,
            rules: [
              {
                required: true,
                message: "请选择操作人",
                trigger: "blur",
              },
            ],
            search: true,
            hide: true,
            showColumn: false,
          },
          {
            label: "操作人",
            prop: "operatorName",
            display: false,
            minWidth: 80
          },
          {
            label: "调拨日期",
            prop: "transferDate",
            search: true,
            type: "date",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            searchRange: true,
            placeholder: " ",
            clearable: false,
            minWidth: 120,
            rules: [
              {
                required: true,
                message: "请选择调拨日期",
                trigger: "blur",
              },
            ],
          },
          {
            label: "关联单据",
            prop: "relatedOrderId",
            type: "input",
            hide: true,
            showColumn: false,
          },
          {
            label: "关联单据",
            prop: "relatedOrderText",
            type: "input",
            display: false,
            overHidden: true,
            minWidth: 100,
            search:true
          },
          {
            label: '调拨数量',
            prop: 'total',
            display: false,
          },
          {
            label: '状态',
            prop: 'status',
            type: 'select',
            search: true,
            display: false,
            dicData: [
              {
                label: '待发货',
                value: 1
              },
              {
                label: '在途中',
                value: 2
              },
              {
                label: '已入库',
                value: 3
              },
              {
                label: '已冲红',
                value: 4
              }
            ]
          },
          {
            label: '备注',
            prop: 'remark',
            type: 'textarea',
            span: 24
          }
        ]
      },
      formOption: {
        submitBtn: true,
        emptyBtn: true,
        submitText: '提交',
        emptyText: '清空',
        span: 12,
        column: [
          {
            label: '调出仓库',
            prop: 'fromWarehouse',
            type: 'select',
            dicUrl: '/api/blade-system/dict-biz/dictionary?code=fg_warehouse',
            props: {
              label: 'dictValue',
              value: 'dictKey'
            },
            rules: [{
              required: true,
              message: '请选择调出仓库',
              trigger: 'blur'
            }]
          },
          {
            label: '调入仓库',
            prop: 'toWarehouse',
            type: 'select',
            dicUrl: '/api/blade-system/dict-biz/dictionary?code=fg_warehouse',
            props: {
              label: 'dictValue',
              value: 'dictKey'
            },
            rules: [{
              required: true,
              message: '请选择调入仓库',
              trigger: 'blur'
            }]
          },
          {
            label: '调拨日期',
            prop: 'transferDate',
            type: 'date',
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyy-MM-dd',
            rules: [{
              required: true,
              message: '请选择调拨日期',
              trigger: 'blur'
            }]
          },
          {
            label: '备注',
            prop: 'remark',
            type: 'textarea',
            span: 24
          },
          {
            label: '调拨明细',
            prop: 'items',
            type: 'dynamic',
            span: 24,
            children: {
              align: 'center',
              type: 'form',
              index: true,
              labelWidth: 120,
              addBtn: true,
              delBtn: true,
              span: 24,
              column: [
                {
                  label: '物料',
                  prop: 'materialId',
                  type: 'select',
                  dicUrl: '/api/ni/base/material/select',
                  props: {
                    label: 'name',
                    value: 'id'
                  },
                  rules: [{
                    required: true,
                    message: '请选择物料',
                    trigger: 'blur'
                  }]
                },
                {
                  label: '数量',
                  prop: 'num',
                  type: 'number',
                  rules: [{
                    required: true,
                    message: '请输入数量',
                    trigger: 'blur'
                  }]
                },
                {
                  label: '备注',
                  prop: 'remark',
                  type: 'textarea'
                }
              ]
            }
          }
        ]
      }
    }
  },
  computed: {
    ...mapGetters(['permission', "userInfo"]),
    permissionList() {
      return {
        addBtn: this.permission.transfer_add,
        viewBtn: this.permission.transfer_view,
        delBtn: this.permission.transfer_delete,
        editBtn: this.permission.transfer_edit,
      }
    },
    ids() {
      let ids = []
      this.selectionList.forEach(ele => {
        ids.push(ele.id)
      })
      return ids.join(',')
    }
  },
  methods: {
    rowView(row) {
      this.$refs.transferFormDialogRef.onShow(row.id)
    },
    rowRed(row) {
      this.$prompt('请输入冲红原因', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
      }).then(({value}) => {
        if (!value) {
          this.$message({
            type: 'error',
            message: '请输入冲红原因'
          })
          return
        }
        red(row.id, value).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        })
      })
    },
    rowShip(row) {
      this.$confirm('确认发货？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        return ship(row.id);
      }).then(() => {
        this.$message.success('发货成功');
        this.onLoad(this.page);
      });
    },
    handleReceive(row) {
      this.$refs.transferFormDialogRef.onReceive(row.id);
    },
    handleSubmit(form, done) {
      if (form.id) {
        update(form).then(res => {
          this.dialogVisible = false
          this.$message.success('修改成功')
          this.onLoad(this.page)
          done()
        }).catch(() => {
          done()
        })
      } else {
        add(form).then(res => {
          this.dialogVisible = false
          this.$message.success('添加成功')
          this.onLoad(this.page)
          done()
        }).catch(() => {
          done()
        })
      }
    },
    handleClose() {
      this.dialogVisible = false
    },
    rowAudit(row) {
      this.$confirm('是否确认审核?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        return audit(row.id)
      }).then(() => {
        this.$message.success('审核成功')
        this.onLoad(this.page)
      })
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning('请选择至少一条数据')
        return
      }
      this.$confirm('确定将选择数据删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        return remove(this.ids)
      }).then(() => {
        this.$message.success('删除成功')
        this.onLoad(this.page)
      })
    },
    handleShip() {
      if (this.selectionList.length === 0) {
        this.$message.warning('请选择至少一条数据')
        return
      }

      // Check if all selected items are in status 1 (待发货)
      const invalidItems = this.selectionList.filter(item => item.status !== 1)
      if (invalidItems.length > 0) {
        this.$message.warning('只能发货状态为待发货的数据')
        return
      }

      this.$confirm('确认批量发货?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        return ship(this.ids)
      }).then(() => {
        this.$message.success('发货成功')
        this.onLoad(this.page)
      })
    },
    handleAdd() {
      this.$refs.transferFormDialogRef.onAdd()
    },
    beforeOpen(done, type) {
      if ('add' === type) {
        this.form.operator = this.userInfo.user_id
        this.form.transferDate = dateFormat(new Date(), 'yyyy-MM-dd')
      }
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then(res => {
          this.form = res.data.data;
        });
      }
      done();
    },
    rowSave(row, done, loading) {
      add(row).then(() => {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
        done();
      }, error => {
        loading();
        window.console.log(error);
      });
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(() => {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
        done();
      }, error => {
        loading();
        console.log(error);
      });
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        });
    },
    searchReset() {
      this.query = {}
      this.onLoad(this.page)
    },
    searchChange(params, done) {
      this.query = params
      this.page.currentPage = 1
      this.onLoad(this.page, params)
      done()
    },
    selectionChange(list) {
      this.selectionList = list
    },
    selectionClear() {
      this.selectionList = []
      this.$refs.crud.toggleSelection()
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize
    },
    refreshChange() {
      this.onLoad(this.page)
    },
    onLoad(page, params = {}) {
      this.loading = true
      getList(page.currentPage, page.pageSize, {
        ...params,
        ...this.query
      }).then(res => {
        const data = res.data.data
        this.page.total = data.total
        this.data = data.records
        this.loading = false
        this.selectionClear()
      })
    },
    cellStyle({row, column}) {
      if ("status" === column.columnKey && row.status === 1) {
        return {
          backgroundColor: '#E6A23C',
          color: "#fff",
        }
      } else if ("status" === column.columnKey && row.status === 2) {
        return {
          backgroundColor: '#409EFF',
          color: "#fff",
        }
      } else if ("status" === column.columnKey && row.status === 3) {
        return {
          backgroundColor: '#67C23A',
          color: "#fff",
        }
      } else if ("status" === column.columnKey && row.status === 4) {
        return {
          backgroundColor: '#F56C6C',
          color: "#fff",
        }
      }
      if ("serialNo" === column.columnKey) {
        return {
          backgroundColor: row.red ? '#F56C6C' : '',
          color: row.red ? "#fff" : this.colorName,
          textDecoration: 'underline',
          cursor: 'pointer'
        }
      }
    },
  }
}
</script>

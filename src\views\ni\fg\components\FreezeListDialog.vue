<script>
import { mapGetters } from "vuex";
import { getList,cancel } from "@/api/ni/fg/fgFreeze";

export default {
  name: "OutboundFormDialog",
  data() {
    return {
      visible: false,
      loading: false,
      selectionList: [],
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      option: {
        menu: false,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        span: 12,
        dialogDrag: true,
        size: "mini",
        searchSize: "mini",
        align: "center",
        searchIndex: 2,
        searchIcon: true,
        searchEnter: true,
        calcHeight: 30,
        tip: false,
        searchShow: false,
        border: true,
        selection: true,
        dialogClickModal: false,
        column: [
          {
            label: "存货编码",
            prop: "materialCode",
            overHidden: true,
            width: 110,
          },
          {
            label: "规格",
            prop: "specText",
            overHidden: true,
            width: 90,
          },
          {
            label: "外包装",
            prop: "packageText",
            overHidden: true,
            width: 110,
          },
          {
            label: "内包装",
            prop: "innerPackageText",
            overHidden: true,
            width: 90,
          },
          {
            label: "质量",
            prop: "qualityLevel",
            type: "select",
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_product_sku_quality_level",
            props: {
              label: "dictValue",
              value: "dictKey",
              desc: "dictKey",
            },
            placeholder: " ",
            width: 70,
          },
          {
            label: "批次",
            prop: "batchNo",
            width: 110,
            overHidden: true,
          },
          {
            label: "数量",
            prop: "num",
          },
          {
            label: "重量",
            prop: "weight",
            overHidden: true,
          },
          {
            label: "生产日期",
            prop: "productionDate",
            type: "date",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            placeholder: " ",
            width: 100,
            overHidden: true,
          },
          {
            label: "冻结原因",
            prop: "reason",
            overHidden: true,
            fixed: "right",
          },
          {
            label: "冻结类型",
            prop: "type",
            type: "select",
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_fg_inventory_freeze_type",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            fixed: "right",
            overHidden: true,
          },
          {
            label: "操作人",
            prop: "createUserName",
            display: false,
            minWidth: 80,
          },
          {
            label: "操作时间",
            prop: "createTime",
            search: true,
            type: "date",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            searchRange: true,
            placeholder: " ",
            clearable: false,
            minWidth: 120,
            rules: [
              {
                required: true,
                message: "请选择操作时间",
                trigger: "blur",
              },
            ],
          },
        ],
      },
      data: [],
      form: {},
      skuId: null,
    };
  },
  computed: {
    ...mapGetters(["userInfo"]),
    items() {
      return this.form.items || [];
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  methods: {
    onShow(skuId) {
      this.skuId = skuId;
      this.visible = true;
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      getList(page.currentPage, page.pageSize, {
        ...params,
        ...this.query,
        skuId: this.skuId,
        status: 1,
        red: false,
      }).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
    handleFreezeCancel() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定取消选择数据的冻结状态?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return cancel(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
  },
};
</script>

<template>
  <el-dialog :visible.sync="visible" append-to-body title="冻结明细">
    <avue-crud
      ref="crud"
      v-if="visible"
      :option="option"
      :data="data"
      v-model="form"
      :page.sync="page"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template #menuLeft>
        <el-button
          type="info"
          icon="el-icon-turn-off-microphone"
          size="mini"
          @click="handleFreezeCancel"
          >取消冻结
        </el-button>
      </template>
    </avue-crud>
  </el-dialog>
</template>

<style scoped></style>

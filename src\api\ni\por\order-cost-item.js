import request from "@/router/axios";

export const getPage = (current, size, params) => {
  return request({
    url: "/api/ni/por/order/cost/item/page",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};
export const getList = (params) => {
  return request({
    url: "/api/ni/por/order/cost/item/list",
    method: "get",
    params: {
      ...params,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/ni/por/order/cost/item/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const remove = (ids) => {
  return request({
    url: "/api/ni/por/order/cost/item/remove",
    method: "post",
    params: {
      ids,
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/ni/por/order/cost/item/submit",
    method: "post",
    data: row,
  });
};

export const update = (row) => {
  return request({
    url: "/api/ni/por/order/cost/item/submit",
    method: "post",
    data: row,
  });
};

export const getOtherPage = (current, size, params) => {
  return request({
    url: "/api/ni/por/order/cost/item/otherPage",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

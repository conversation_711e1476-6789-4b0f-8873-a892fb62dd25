import request from "@/router/axios";

export const getList = (params) => {
  return request({
    url: "/api/blade-resource/attach/list",
    method: "get",
    params: {
      ...params,
    },
  });
};
export const getPage = (current, size, params) => {
  return request({
    url: "/api/blade-resource/attach/page",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/blade-resource/attach/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const remove = (ids) => {
  return request({
    url: "/api/blade-resource/attach/remove",
    method: "post",
    params: {
      ids,
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/blade-resource/attach/submit",
    method: "post",
    data: row,
  });
};

export const update = (row) => {
  return request({
    url: "/api/blade-resource/attach/submit",
    method: "post",
    data: row,
  });
};

export const fileLink = (id) => {
  return request({
    url: "/api/blade-resource/attach/file-link",
    method: "get",
    params: {
      id,
    },
  });
};
export const download = (id) => {
  return request({
    url: "/api/blade-resource/attach/download",
    method: "get",
    params: {
      id,
    },
    responseType: 'blob'
  });
};

export const fileLinkByBusinessKeys = (businessKeys,businessName) => {
  return request({
    url: "/api/blade-resource/attach/fileLinkByBusinessKeys",
    method: "get",
    params: {
      businessKeys,businessName
    },
  });
};

import request from "@/router/axios";

export const getList = (params) => {
  return request({
    url: "/api/ni/base/material/type/list",
    method: "get",
    params: {
      ...params,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/ni/base/material/type/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const remove = (ids) => {
  return request({
    url: "/api/ni/base/material/type/remove",
    method: "post",
    params: {
      ids,
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/ni/base/material/type/save",
    method: "post",
    data: row,
  });
};

export const update = (row) => {
  return request({
    url: "/api/ni/base/material/type/update",
    method: "post",
    data: row,
  });
};

export const getTree = (params) => {
  return request({
    url: "/api/ni/base/material/type/tree",
    method: "get",
    params: {
      ...params,
    },
  });
};
export const sync = (id, isCovered = false) => {
  return request({
    url: "/api/ni/base/material/type/sync",
    method: "post",
    params: {
      id,
      isCovered,
    },
  });
};

export const getLazyList = (parentId, params) => {
  return request({
    url: "/api/ni/base/material/type/lazy-list",
    method: "get",
    params: {
      parentId,
      ...params,
    },
  });
};

export const searchMaterialByName = (materialName) => {
  return request({
    url: "/api/ni/base/material/type/searchMaterialByName",
    method: "get",
    params: {
      materialName,
    },
  });
};
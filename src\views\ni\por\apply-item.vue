<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :search.sync="query"
      :page.sync="page"
      v-model="form"
      ref="crud"
      @search-change="searchChange"
      @search-reset="searchReset"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @selection-change="selectionChange"
      @on-load="onLoad"
      :cell-style="cellStyle"
    >
      <!-- --------------附件 ----------------->
      <template #attaches="{ row, index }">
        <el-link type="primary" target="_blank" @click="rowAttach(row)">
          {{
            !row.attachNum || row.attachNum <= 0
              ? "无附件"
              : `附件[${row.attachNum}]`
          }}
        </el-link>
      </template>
      <!-- --------------压力容器 ----------------->
      <template #pv="{ row, size, index, dic }">
        <el-dropdown
          v-if="userInfo.role_name.includes('admin')"
          trigger="click"
          @command="rowPvChange($event, row, index)"
        >
          <el-tag
            size="mini"
            v-if="row.pv"
            type="danger"
            effect="dark"
            style="cursor: pointer"
          >
            是
          </el-tag>
          <el-tag
            size="mini"
            v-else
            type="info"
            effect="plain"
            style="cursor: pointer"
          >否
          </el-tag>
          <i class="el-icon-arrow-down el-icon--right"></i>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
              v-for="item in dic"
              :key="item.value"
              :disabled="item.value === row.pv"
              :command="item.value"
            >{{ item.label }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <template v-else>
          <el-tag
            size="mini"
            v-if="row.pv"
            type="danger"
            effect="dark"
            style="cursor: pointer"
          >
            是
          </el-tag>
          <el-tag
            size="mini"
            v-else
            type="info"
            effect="plain"
            style="cursor: pointer"
          >否
          </el-tag>
        </template>
      </template>
      <!-- --------------固定资产 ----------------->
      <template #fa="{ row, size, index, dic }">
        <el-dropdown
          v-if="userInfo.role_name.includes('admin')"
          trigger="click"
          @command="rowFaChange($event, row, index)"
        >
          <el-tag
            size="mini"
            v-if="row.fa"
            type="danger"
            effect="dark"
            style="cursor: pointer"
          >
            是
          </el-tag>
          <el-tag
            size="mini"
            v-else
            type="info"
            effect="plain"
            style="cursor: pointer"
          >否
          </el-tag>
          <i class="el-icon-arrow-down el-icon--right"></i>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
              v-for="item in dic"
              :key="item.value"
              :disabled="item.value === row.fa"
              :command="item.value"
            >{{ item.label }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <template v-else>
          <el-tag
            size="mini"
            v-if="row.fa"
            type="danger"
            effect="dark"
            style="cursor: pointer"
          >
            是
          </el-tag>
          <el-tag
            size="mini"
            v-else
            type="info"
            effect="plain"
            style="cursor: pointer"
          >否
          </el-tag>
        </template>
      </template>
      <template #inquiries="{ row, index, size }">
        <a
          v-if="row.inquiries && row.inquiries.length > 0"
          href="#"
          style="color: #f56c6c; text-decoration: underline; font-weight: bold"
          @click.p.prevent="rowInquiry(row, index)"
        >
          比价
          <span v-if="row.inquiries"> [{{ row.inquiries.length }}] </span>
        </a>
        <span v-else> 未比价 </span>
      </template>
      <template #applySerialNo="{ row, index }">
        <flow-timeline-popover
          v-if="row.processInsId"
          :process-ins-id="row.processInsId"
          :form-key="formKey"
          :process-def-key="processDefKey"
          v-model="row.applySerialNo"
          :flow.sync="row.flow"
          trigger="click"
          lazy
        />
        <span v-else>{{ row.applySerialNo }}</span>
      </template>
      <template #menuLeft>
        <el-button
          type="success"
          size="mini"
          icon="el-icon-download"
          plain
          @click="handleExport"
        >导出
        </el-button>
        <el-button
          type="warning"
          size="mini"
          icon="el-icon-edit"
          @click="handleRemark"
        >修改备注
        </el-button>
        <!--                <el-button-->
        <!--                  type="primary"-->
        <!--                  size="mini"-->
        <!--                  icon="el-icon-edit"-->
        <!--                  @click="handleInquiry"-->
        <!--                  >修改比价-->
        <!--                </el-button>-->
        <el-button
          type="warning"
          size="mini"
          icon="el-icon-edit"
          v-if="permission.porapply_item_inquiry"
          @click="handleInquiryFlow"
        >修改比价
        </el-button>
        <el-button
          type="danger"
          size="mini"
          icon="el-icon-collection-tag"
          v-if="permission.porapply_item_year"
          @click="handleYearsAgo"
        >标记年前必须购回
        </el-button>
        <el-divider direction="vertical"></el-divider>
        <el-radio-group v-model="brand" size="mini" @input="onLoad(page)">
          <el-radio-button label="natergy">能特异</el-radio-button>
          <el-radio-button label="yy">演绎</el-radio-button>
        </el-radio-group>
        <el-divider direction="vertical"/>
        <el-radio-group v-model="dataType" size="mini" @input="onLoad(page)">
          <el-radio-button label="all">全部</el-radio-button>
          <el-radio-button label="unArrival">未到货</el-radio-button>
          <el-radio-button label="unStock">未入库</el-radio-button>
        </el-radio-group>
        <el-divider direction="vertical"></el-divider>
        <el-checkbox v-model="owner" @change="handleOwnerChange">自己</el-checkbox>
        <el-checkbox v-model="auto">自动申购</el-checkbox>
        <el-checkbox v-model="pv">压力容器</el-checkbox>
      </template>
      <template #num="{ row, index }">
        <el-badge
          v-if="row.reject && row.rejectNum && row.rejectNum < row.num"
          :value="`驳回:${Number(row.rejectNum)}`"
          class="item"
          style="margin-top: 10px; margin-right: 40px"
        >
          <span>{{
              Number(row.num).toLocaleString("zh-CN", {
                minimumFractionDigits: 2,
              })
            }}</span>
        </el-badge>
        <span v-else>{{
            Number(row.num).toLocaleString("zh-CN", {
              minimumFractionDigits: 2,
            })
          }}</span>
      </template>
      <template #arrivalNum="{ row, index }">
        <el-popover
          v-if="row.arrivalNum"
          placement="right"
          width="500"
          trigger="click"
          @show="showArrivalItem(row)"
        >
          <avue-crud :option="arrival.option" :data="arrival.data"></avue-crud>

          <el-button type="text" size="mini" slot="reference"
          >{{
              Number(row.arrivalNum).toLocaleString("zh-CN", {
                minimumFractionDigits: 2,
              })
            }}
          </el-button>
        </el-popover>
      </template>
      <template #price="{ row, index }">
        <span>{{
            Number(row.price).toLocaleString("zh-CN", {
              minimumFractionDigits: 2,
            })
          }}</span>
      </template>
      <template #cost="{ row, index }">
        <el-tag size="mini" type="danger" effect="dark" v-if="row.cost">
          费用
        </el-tag>
        <el-tag size="mini" type="info" effect="plain" v-else> 实物</el-tag>
      </template>
      <template #budgetTitle="{ row, index, size }">
        <el-tag v-if="row.budgetRepair" size="mini" type="warning">增补</el-tag>
        <el-tag v-if="row.budgetYear === '2'" size="mini" type="danger">
          {{ row.budgetYearDate }}
        </el-tag>
        <span>{{ row.budgetTitle }}</span>
      </template>
      <template #brand="{ row, disabled, size, index }">
        <el-tag
          v-if="row.brand"
          :size="size"
          :effect="row.brand === '1' ? 'dark ' : 'plain'"
        >
          {{ brandDictKeyValue[row.brand] }}
        </el-tag>
      </template>
      <template #applyStatus="{ row, index }">
        <el-tooltip
          v-if="
            row.reject &&
            (!row.rejectNum || row.rejectNum === 0 || row.rejectNum >= row.num)
          "
          class="item"
          effect="dark"
          :content="'驳回原因：' + row.rejectReason"
          placement="top-start"
        >
          <el-tag size="mini" type="danger" effect="plain"> 被驳回</el-tag>
        </el-tooltip>
        <template v-else>
          <el-tag v-if="row.applyStatus === 0" size="mini" type="info">
            {{ row.$applyStatus }}
          </el-tag>
          <el-tag v-else-if="row.applyStatus === 1" size="mini" effect="plain">
            {{ row.$applyStatus }}
          </el-tag>
          <el-tag v-else-if="row.applyStatus === 2" size="mini" effect="plain">
            {{ row.$applyStatus }}
          </el-tag>
          <el-tag v-else-if="row.applyStatus === 3" size="mini" type="danger">
            {{ row.$applyStatus }}
          </el-tag>
          <el-tag
            v-else-if="row.applyStatus === 4"
            size="mini"
            type="warning"
            effect="plain"
          >
            {{ row.$applyStatus }}
          </el-tag>
          <el-tag
            v-else-if="row.applyStatus === 5"
            size="mini"
            type="warning"
            effect="plain"
          >
            {{ row.$applyStatus }}
          </el-tag>
          <el-tag
            v-else-if="row.applyStatus === 6"
            size="mini"
            type="danger"
            effect="plain"
          >
            {{ row.$applyStatus }}
          </el-tag>
          <el-tag
            v-else-if="row.applyStatus === 9"
            size="mini"
            type="success"
            effect="plain"
          >
            {{ row.$applyStatus }}
          </el-tag>
          <el-tag v-else size="mini" type="warning" effect="dark">
            {{ row.$applyStatus }}
          </el-tag>
        </template>
      </template>
      <template #amount="{ row, index, size }">
        <span style="color: green; font-weight: bolder">{{
            Number(row.amount).toLocaleString("zh-CN", {
              minimumFractionDigits: 2,
            })
          }}</span>
      </template>
      <template #orderAmount="{ row, index, size }">
        <span
          style="color: red; font-weight: bolder"
          v-if="row.orderAmount && Number(row.orderAmount) > Number(row.amount)"
        >{{ row.orderAmount }}</span
        >
        <span style="font-weight: bolder" v-else>{{
            row.orderAmount ? row.orderAmount : 0
          }}</span>
      </template>
      <template #crash="{ row, index, size }">
        <el-tag v-if="row.crash" size="mini" type="danger" effect="dark">
          是
        </el-tag>
        <el-tag v-else size="mini" type="info">否</el-tag>
      </template>
    </avue-crud>
    <attach-dialog ref="attachRef" code="public" :delBtn="false"/>
    <el-dialog
      title="比价"
      append-to-body
      :visible.sync="inquiry.visible"
      width="1000px"
    >
      <div slot="title" class="clearfix">
        <span>比价</span>
      </div>
      <avue-form
        v-if="inquiry.visible"
        :option="inquiry.option"
        v-model="inquiry.form"
        @submit="handleInquirySubmit"
      >
      </avue-form>
    </el-dialog>
    <el-dialog
      title=""
      append-to-body
      :visible.sync="yearsAgo.visible"
      width="300px"
    >
      <div slot="title" class="clearfix"></div>
      <avue-form
        v-if="yearsAgo.visible"
        :option="yearsAgo.option"
        v-model="yearsAgo.form"
        @submit="handleYearsAgoSubmit"
        @reset-change="() => (yearsAgo.visible = false)"
      >
      </avue-form>
    </el-dialog>
  </basic-container>
</template>

<script>
import {
  changeInquiry,
  changePv,
  changeRemark,
  changeYearsAgo,
  getList,
  getPage,
  changeFa,
} from "@/api/ni/por/apply-item";
import exForm from "@/views/plugin/workflow/mixins/ex-form";
import {getItemListByApplyItemId} from "@/api/ni/por/order-arrival";
import AttachDialog from "@/components/attach-dialog";
import FlowTimelinePopover from "@/components/wf-flow-timeline/popover";
import {mapGetters} from "vuex";

export default {
  mixins: [exForm],
  name: "PorApplyItem",
  components: {
    AttachDialog,
    FlowTimelinePopover,
  },
  beforeRouteEnter(to, from, next) {
    to.meta.keepAlive = true;
    next();
  },
  data() {
    return {
      module: "ni_por_apply",
      processDefKey: "process_por_apply",
      formKey: "wf_ex_por/Apply",
      rejectProcessDefKey: "process_por_apply_reject",
      rejectFormKey: "wf_ex_por/ApplyReject",
      inquiryProcessDefKey: "process_ni_por_apply_inquiry",
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        searchEnter: true,
        index: false,
        align: "center",
        searchLabelWidth: 110,
        searchIndex: 3,
        searchIcon: true,
        addBtn: false,
        menu: false,
        size: "mini",
        searchSize: "mini",
        height: "auto",
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        viewBtn: true,
        selection: true,
        dialogClickModal: false,
        column: [
          {
            label: "序号",
            prop: "row",
            width: 55,
            search: true,
          },
          {
            label: "状态",
            prop: "applyStatus",
            dicData: [],
            search: true,
            minWidth: 80,
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            dataType: "number",
            display: false,
            type: "select",
          },
          {
            label: "品名",
            prop: "materialName",
            minWidth: 90,
            overHidden: true,
            search: true,
            searchOrder: 99,
          },
          {
            label: "规格型号",
            overHidden: true,
            prop: "specification",
            minWidth: 90,
            search: true,
            searchOrder: 98,
          },
          {
            label: "材质",
            prop: "quality",
            minWidth: 90,
            overHidden: true,
          },
          {
            label: "数量",
            prop: "num",
            type: "number",
            placeholder: " ",
            minWidth: 80,
            overHidden: true,
          },
          {
            label: "单位",
            prop: "unit",
            overHidden: true,
            type: "select",
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_material_unit",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            width: 55,
            slot: true,
          },
          {
            label: "单价",
            prop: "price",
            type: "number",
            disabled: true,
            minWidth: 100,
            controls: false,
            precision: 2,
            placeholder: " ",
            overHidden: true,
          },
          {
            label: "备注",
            prop: "remark",
            minWidth: 110,
            overHidden: true,
            search: true,
          },
          {
            label: "附件",
            prop: "attaches",
            minWidth: 90,
            overHidden: true,
          },
          {
            label: "工作表",
            prop: "inquiries",
            width: 75,
          },
          {
            label: "申请日期",
            prop: "createTime",
            type: "date",
            minWidth: 85,
            overHidden: true,
            display: false,
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            search: true,
            searchRange: true,
          },
          {
            label: "用途",
            prop: "purpose",
            type: "textarea",
            minRows: 1,
            placeholder: " ",
            overHidden: true,
            minWidth: 90,
          },
          {
            label: "编码",
            prop: "materialCode",
            minWidth: 120,
            overHidden: true,
            search: true,
          },
          {
            label: "收货地",
            prop: "receivingAddress",
            type: "radio",
            dicUrl: "/api/blade-system/dict-biz/dictionary?code=ni_por_receiving_address",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            clearable: false,
            width: 90,
            overHidden: true,
          },
          {
            label: "到货数",
            prop: "arrivalNum",
            type: "number",
            placeholder: " ",
            minWidth: 80,
            overHidden: true,
          },
          {
            label: "入库数",
            prop: "stockNum",
            type: "number",
            placeholder: " ",
            minWidth: 80,
            overHidden: true,
          },
          {
            label: "退货数",
            prop: "backNum",
            type: "number",
            placeholder: " ",
            minWidth: 80,
            overHidden: true,
          },
          {
            label: "申请人",
            prop: "createUserName",
            addDisplay: false,
            editDisplay: false,
            overHidden: true,
            minWidth: 80,
            search: true,
          },
          {
            label: "申请部门",
            prop: "createDeptName",
            addDisplay: false,
            editDisplay: false,
            overHidden: true,
            minWidth: 80,
          },
          {
            label: "申请部门",
            prop: "createDept",
            type: "tree",
            dicUrl: `/api/blade-system/dept/list`,
            props: {label: "deptName", value: "id"},
            search: true,
            viewDisplay: true,
            hide: true,
            showColumn: false,
            addDisplay: false,
            editDisplay: false,
            overHidden: true,
            minWidth: 80,
          },
          {
            label: "国标",
            overHidden: true,
            prop: "gb",
            minWidth: 80,
          },
          {
            label: "申请金额",
            prop: "amount",
            overHidden: true,
            controls: false,
            type: "number",
            minWidth: 100,
            precision: 2,
            placeholder: " ",
          },
          {
            label: "压力容器",
            prop: "pv",
            type: "radio",
            width: 70,
            dicData: [
              {
                label: "是",
                value: 1,
              },
              {
                label: "否",
                value: 0,
              },
            ],
            display: false,
          },
          {
            label: "固定资产",
            prop: "fa",
            type: "radio",
            width: 70,
            dicData: [
              {
                label: "是",
                value: 1,
              },
              {
                label: "否",
                value: 0,
              },
            ],
            display: false,
          },
          {
            label: "申请编号",
            prop: "applySerialNo",
            search: true,
            overHidden: true,
            minWidth: 116,
          },
          {
            label: "采购申请",
            prop: "applyTitle",
            overHidden: true,
            minWidth: 110,
          },
          {
            label: "采购类型",
            prop: "type",
            type: "select",
            minWidth: 80,
            dicUrl: "/api/blade-system/dict-biz/dictionary?code=ni_por_type",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            overHidden: true,
            search: true,
          },
          {
            label: "需用日期",
            prop: "needDate",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            overHidden: true,
            width: 84,
            type: "date",
          },
          {
            label: "申请备注",
            prop: "applyRemark",
            overHidden: true,
            minWidth: 100,
            search: true,
          },
          {
            label: "类型",
            prop: "cost",
            type: "select",
            dicData: [
              {
                label: "费用",
                value: true,
              },
              {
                label: "实物",
                value: false,
              },
            ],
            search: true,
            placeholder: " ",
            width: 60,
            disabled: true,
          },
          {
            label: "预算编号",
            prop: "budgetSerialNo",
            search: true,
            overHidden: true,
            minWidth: 110,
          },
          {
            label: "项目编号",
            prop: "projectSerialNo",
            search: true,
            minWidth: 110,
            overHidden: true,
          },
          {
            label: "账套",
            prop: "brand",
            type: "radio",
            minWidth: 70,
            value: "1",
            dicData: [],
            disabled: true,
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            search: true,
          },
          {
            label: "紧急申购",
            prop: "crash",
            type: "radio",
            value: false,
            minWidth: 70,
            dicData: [
              {
                label: "是",
                value: true,
              },
              {
                label: "否",
                value: false,
              },
            ],
            search: true,
          },
          {
            label: "采购人",
            prop: "purchaseUserName",
            minWidth: 70,
          },
          {
            label: "暂存位置",
            prop: "depotLocation",
            minWidth: 70,
          },
          {
            label: "采购驳回",
            prop: "reject",
            type: "radio",
            minWidth: 70,
            hide: true,
            dicData: [
              {
                label: "是",
                value: true,
              },
              {
                label: "否",
                value: false,
              },
            ],
            search: true,
          },
        ],
      },
      data: [],
      statusDict: [],
      statusDictKeyValue: {},
      brandDict: [],
      brandDictKeyValue: {},
      auto: false,
      pv: false,
      owner: false,
      dataType: "all",
      brand: "natergy",
      exportData: [], //存储导出数据
      typeDictKeyValue: {}, //采购类型字典转化
      unitDictKeyValue: {}, //单位类型字典转化
      arrival: {
        option: {
          header: false,
          menu: false,
          addBtn: false,
          size: "mini",
          search: false,
          column: [
            {
              label: "暂存位置",
              prop: "depotLocation",
              overHidden: true,
              minWidth: 80,
            },
            {
              label: "到货日期",
              prop: "arrivalDate",
              minWidth: 85,
            },
            {
              label: "合格数",
              prop: "qualifiedNum",
              minWidth: 70,
            },
            {
              label: "到货数",
              prop: "arrivalNum",
              minWidth: 70,
            },
            {
              label: "验收人员",
              prop: "inspectionUserName",
              minWidth: 70,
              overHidden: true,
            },
            {
              label: "验收时间",
              prop: "inspectionDate",
              minWidth: 85,
            },
            {
              label: "验收结果",
              prop: "qualified",
              type: "select",
              fixed: "right",
              dicUrl:
                "/api/blade-system/dict-biz/dictionary?code=ni_por_order_arrival_inspection_state",
              placeholder: " ",
              props: {
                label: "dictValue",
                value: "dictKey",
              },
              minWidth: 70,
            },
            {
              label: "验收情况",
              prop: "inspectionRemark",
              minWidth: 70,
              overHidden: true,
            },
            {
              label: "登记人",
              prop: "createUserName",
              minWidth: 70,
              overHidden: true,
            },
            {
              label: "流水号",
              prop: "serialNo",
              minWidth: 80,
              overHidden: true,
            },
          ],
        },
        data: [],
      },
      export1: {
        column: [
          {
            label: "序号",
            prop: "row",
          },
          {
            label: "状态",
            prop: "applyStatus",
          },
          {
            label: "品名",
            prop: "materialName",
          },
          {
            label: "编码",
            prop: "materialCode",
          },
          {
            label: "规格型号",
            prop: "specification",
          },
          {
            label: "材质",
            prop: "quality",
          },
          {
            label: "数量",
            prop: "num",
          },
          {
            label: "单位",
            prop: "unit",
          },
          {
            label: "单价",
            prop: "price",
          },
          {
            label: "申请时间",
            prop: "createTime",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
          },
          {
            label: "备注",
            prop: "remark",
          },
          {
            label: "到货数",
            prop: "arrivalNum",
          },
          {
            label: "入库数",
            prop: "stockNum",
          },
          {
            label: "退货数",
            prop: "backNum",
          },
          {
            label: "申请人",
            prop: "createUserName",
          },
          {
            label: "申请部门",
            prop: "createDeptName",
          },
          {
            label: "国标",
            prop: "gb",
          },
          {
            label: "申请金额",
            prop: "amount",
          },
          {
            label: "申请编号",
            prop: "applySerialNo",
          },
          {
            label: "采购申请",
            prop: "applyTitle",
          },
          {
            label: "采购类型",
            prop: "type",
          },
          {
            label: "需用日期",
            prop: "needDate",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
          },
          {
            label: "类型",
            prop: "cost",
          },
          {
            label: "预算名称",
            prop: "budgetTitle",
          },
          {
            label: "预算编号",
            prop: "budgetSerialNo",
          },
          {
            label: "项目名称",
            prop: "projectTitle",
          },
          {
            label: "项目编号",
            prop: "projectSerialNo",
          },
          {
            label: "账套",
            prop: "brand",
          },
          {
            label: "紧急申购",
            prop: "crash",
          },
          {
            label: "采购人",
            prop: "purchaseUserName",
          },
          {
            label: "暂存位置",
            prop: "depotLocation",
          },
        ],
      },
      inquiry: {
        visible: false,
        option: {
          submitText: "修改",
          detail: false,
          size: "mini",
          emptyBtn: false,
          labelWidth: 120,
          column: [
            {
              label: "采购原因",
              prop: "reason",
              type: "textarea",
              minRows: 1,
              placeholder: " ",
              rules: [
                {
                  required: true,
                  message: "请输入采购原因",
                  trigger: "blur",
                },
              ],
            },
            {
              label: "采购风险",
              prop: "risk",
              type: "textarea",
              minRows: 1,
              placeholder: " ",
              rules: [
                {
                  required: true,
                  message: "请输入采购风险",
                  trigger: "blur",
                },
              ],
            },
            {
              label: "采购带来的收益及效果",
              prop: "profit",
              span: 24,
              type: "textarea",
              minRows: 2,
              placeholder: " ",
              rules: [
                {
                  required: true,
                  message: "请输入采购带来的收益及效果",
                  trigger: "blur",
                },
              ],
            },
            {
              label: "项目安全性分析及风险控制",
              prop: "security",
              span: 24,
              minRows: 2,
              type: "textarea",
              placeholder: " ",
              rules: [
                {
                  required: true,
                  message: "请输入项目安全性分析及风险控制",
                  trigger: "blur",
                },
              ],
            },
            {
              label: "项目环保因素分析及风险控制",
              prop: "ep",
              span: 24,
              minRows: 2,
              type: "textarea",
              placeholder: " ",
              rules: [
                {
                  required: true,
                  message: "请输入项目环保因素分析及风险控制",
                  trigger: "blur",
                },
              ],
            },
            {
              label: "验收标准",
              prop: "ac",
              span: 12,
              minRows: 1,
              type: "textarea",
              placeholder: " ",
            },
            {
              label: "技术参数",
              prop: "technology",
              span: 12,
              minRows: 1,
              type: "textarea",
              placeholder: " ",
            },
            {
              label: "供应商比价",
              prop: "inquiries",
              span: 24,
              type: "dynamic",
              children: {
                span: 8,
                addBtn: false,
                delBtn: false,
                align: "center",
                headerAlign: "center",
                size: "mini",
                column: [
                  {
                    label: "供应商名称",
                    prop: "supplier",
                    placeholder: " ",
                    type: "textarea",
                    overHidden: true,
                    minRows: 1,
                    rules: [
                      {
                        required: true,
                        message: "请填写供应商名称",
                        trigger: "blur",
                      },
                    ],
                  },
                  {
                    label: "联系方式",
                    prop: "supplierLinkman",
                    placeholder: " ",
                    type: "textarea",
                    overHidden: true,
                    minRows: 1,
                    rules: [
                      {
                        required: true,
                        message: "请填写供应商联系人",
                        trigger: "blur",
                      },
                    ],
                  },

                  {
                    label: "比价详情",
                    prop: "remark",
                    type: "textarea",
                    minRows: 1,
                    placeholder: " ",
                    overHidden: true,
                    rules: [
                      {
                        required: true,
                        message: "请填写比价详情",
                        trigger: "blur",
                      },
                    ],
                  },
                  {
                    label: "建议供应商",
                    prop: "recommend",
                    type: "radio",
                    value: false,
                    dicData: [
                      {
                        label: "是",
                        value: true,
                      },
                      {
                        label: "否",
                        value: false,
                      },
                    ],
                    placeholder: " ",
                    rules: [
                      {
                        required: true,
                        message: "请选择建议供应商",
                        trigger: "blur",
                      },
                    ],
                    change: ({value, index}) => {
                      if (value)
                        this.inquiry.form.inquiries.forEach((item, i) => {
                          if (i === index) {
                            return;
                          }
                          item.recommend = false;
                        });
                    },
                  },
                ],
              },
            },
            {
              label: "部门主管意见",
              prop: "comments1",
              minRows: 1,
              type: "textarea",
              placeholder: " ",
              disabled: true,
              span: 12,
            },
            {
              label: "工艺组意见",
              prop: "comments2",
              minRows: 1,
              type: "textarea",
              placeholder: " ",
              disabled: true,
              span: 12,
            },
            {
              label: "总经理意见",
              prop: "comments3",
              minRows: 1,
              type: "textarea",
              placeholder: " ",
              disabled: true,
              span: 12,
            },
            {
              label: "总经办意见",
              prop: "comments4",
              minRows: 1,
              type: "textarea",
              placeholder: " ",
              disabled: true,
              span: 12,
            },
          ],
        },
        form: {},
        historyShow: false,
      },
      yearsAgo: {
        visible: false,
        option: {
          labelWidth: 120,
          submitText: "确认",
          emptyText: "取消",
          detail: false,
          size: "mini",
          span: 24,
          column: [
            {
              label: "年前必须购回",
              prop: "yearsAgo",
              type: "switch",
              dicData: [
                {
                  label: "取消",
                  value: 0,
                },
                {
                  label: "标记",
                  value: 1,
                },
              ],
              placeholder: " ",
              rules: [
                {
                  required: true,
                  message: "请选择标记还是取消",
                  trigger: "blur",
                },
              ],
            },
          ],
        },
        form: {},
      },
    };
  },
  created() {
    this.dictInit();
  },
  computed: {
    ...mapGetters(["userInfo"]),
    ids() {
      let ids = new Set();
      this.selectionList.forEach((ele) => {
        ids.add(ele.id);
      });
      return Array.from(ids).join(",");
    },
  },
  watch: {
    pv: {
      handler(val) {
        if (val) {
          this.query.pv = 1;
        } else {
          this.query.pv = null;
        }
        this.page.currentPage = 1;
        this.onLoad(this.page);
      },
      immediate: false,
    },
    auto: {
      handler(val) {
        if (val) {
          this.query.auto = 1;
        } else {
          this.query.auto = null;
        }
        this.page.currentPage = 1;
        this.onLoad(this.page);
      },
      immediate: false,
    },
  },
  methods: {
    handleOwnerChange(val) {
      this.query.createUser = val ? this.userInfo.user_id : null;
      this.page.currentPage = 1
      this.onLoad(this.page, this.query);
    },
    handleYearsAgo() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择要标记的数据");
        return;
      }
      const notOwner = this.selectionList.some(
        (item) =>
          item.createUser !== this.userInfo.user_id &&
          !this.userInfo.role_name.includes("admin")
      );
      if (notOwner) {
        this.$message.warning("请选择自己申购的数据");
        return;
      }
      this.yearsAgo.form.yearsAgo = 1;
      this.yearsAgo.form.ids = this.ids;
      this.yearsAgo.visible = true;
    },
    handleYearsAgoSubmit(form, done) {
      changeYearsAgo(form.ids, form.yearsAgo)
        .then(() => {
          this.yearsAgo.visible = false;
          const ids = this.selectionList.map((item) => item.id);
          this.data.forEach((item) => {
            if (ids.includes(item.id)) {
              item.yearsAgo = form.yearsAgo;
            }
          });
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        })
        .finally(() => {
          done();
        });
    },
    rowPvChange(pv, row, index) {
      changePv(row.id, pv).then(() => {
        this.data[index].pv = pv;
        this.$message({
          type: "success",
          message: "操作成功!",
        });
      });
    },
    rowFaChange(fa, row, index) {
      changeFa(row.id, fa).then(() => {
        this.data[index].fa = fa;
        this.$message({
          type: "success",
          message: "操作成功!",
        });
      });
    },
    rowInquiry(row, index) {
      Object.keys(this.inquiry.form).forEach(
        (key) => (this.inquiry.form[key] = "")
      );
      this.inquiry.form = {
        ...row,
        index: [index],
      };
      this.inquiry.option.detail = true;
      this.inquiry.visible = true;
    },
    handleInquirySubmit(form, done) {
      if (!form.inquiries || form.inquiries.length === 0) {
        this.$message({type: "warning", message: "请添加比价明细"});
        done();
        return;
      }
      const recommend = form.inquiries.some((item) => item.recommend);
      if (!recommend) {
        this.$message({type: "warning", message: "请选择建议供应商"});
        done();
        return;
      }
      changeInquiry(form)
        .then(() => {
          this.onLoad(this.page);
          this.inquiry.visible = false;
          this.$message({
            type: "success",
            message: "操作成功",
          });
        })
        .finally(() => {
          done();
        });
    },
    handleAutoChange() {
      this.page.currentPage = 1;
      this.onLoad(this.page);
    },
    showArrivalItem(row) {
      getItemListByApplyItemId(row.id).then((res) => {
        this.arrival.data = res.data.data;
      });
    },
    rowDetail(row, index) {
      if (row.processInsId) {
        this.dynamicRoute(
          {
            processInstanceId: row.processInsId,
            formKey: row.status === 1 ? this.formKey : this.rejectFormKey,
            processDefKey:
              row.status === 1 ? this.processDefKey : this.rejectProcessDefKey,
          },
          "detail"
        );
      } else {
        this.$refs.crud.rowView(row, index);
      }
    },
    handleDataTypeChange() {
      this.onLoad(this.page);
    },
    dictInit() {
      this.$http
        .get("/api/blade-system/dict/dictionary?code=data_status")
        .then((res) => {
          const column = this.findObject(this.option.column, "applyStatus");
          column.dicData = res.data.data;
          this.statusDict = res.data.data;
          this.statusDictKeyValue = this.statusDict.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });
      this.$http
        .get("/api/blade-system/dict-biz/dictionary?code=ni_brand")
        .then((res) => {
          const column = this.findObject(this.option.column, "brand");
          column.dicData = res.data.data;
          this.brandDict = res.data.data;
          this.brandDictKeyValue = this.brandDict.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });
      //单位
      this.$http
        .get("/api/blade-system/dict-biz/dictionary?code=ni_material_unit")
        .then((res) => {
          const unitDict = res.data.data;
          this.unitDictKeyValue = unitDict.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });
      //采购类型数据转化
      this.$http
        .get("/api/blade-system/dict-biz/dictionary?code=ni_por_type")
        .then((res) => {
          const typeDict = res.data.data;
          this.typeDictKeyValue = typeDict.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });
    },
    searchReset() {
      this.query = {};
      this.owner = false
      this.pv = null;
      this.auto = null;
      this.page.currentPage = 1;
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    onLoad(page, params = {}) {
      this.loading = true;
      if (this.dataType === "all") {
        this.query.unArrival = null;
        this.query.unStock = null;
      } else if (this.dataType === "unArrival") {
        this.query.unArrival = true;
        this.query.unStock = null;
      } else if (this.dataType === "unStock") {
        this.query.unStock = true;
        this.query.unArrival = null;
      }
      if (this.brand === "natergy") {
        this.query.brand = "1,2";
      } else if (this.brand === "yy") {
        this.query.brand = "4";
      }
      const query = {
        ...params,
        ...this.query,
        desc: "id",
      };
      if (query.createTime != null) {
        query.startCreateTime = query.createTime[0];
        query.endCreateTime = query.createTime[1];
        query.createTime = null;
      }
      getPage(page.currentPage, page.pageSize, query).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
    cellStyle({row, column}) {
      if ("createUserName" === column.columnKey && row.crash) {
        return {
          backgroundColor: '#F56C6C',
          color: "#fff",
        };
      }
      if ("row" === column.columnKey && row.yearsAgo) {
        return {
          backgroundColor: "#F56C6C",
          color: "#fff",
        };
      }
      if ("materialName" === column.columnKey && row.type === '21') {
        return {
          background: 'url(/img/yzdyzb.png) no-repeat left center / contain',
          backgroundColor: "#F56C6C",
          color: "#fff",
        };
      }
    },

    handleInquiryFlow() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择要比价的数据");
        return;
      }
      const applyIds = new Set();
      const status = new Set();
      for (let i = 0; i < this.selectionList.length; i++) {
        applyIds.add(this.selectionList[i].applyId);
        if (this.selectionList[i].status !== "9")
          status.add(this.selectionList[i].status);
      }
      if (applyIds.size > 1) {
        this.$message.warning("请选择同一采购申请的数据");
        return;
      }
      if (status.size > 1) {
        this.$message.warning("请选择已审核的数据");
        return;
      }
      Object.keys(this.inquiry.form).forEach(
        (key) => (this.inquiry.form[key] = "")
      );
      const form = {
        applyId: this.selectionList[0].applyId,
        itemIds: this.selectionList.map((item) => item.id),
      };
      this.dynamicRoute(
        {
          processDefKey: this.inquiryProcessDefKey,
          form: encodeURIComponent(
            Buffer.from(JSON.stringify(form)).toString("utf8")
          ),
        },
        "start"
      );
      // if (!this.inquiry.form.inquiries) {
      //   this.inquiry.form.inquiries = [];
      // }
      // if (this.inquiry.form.inquiries.length < 3) {
      //   console.log(this.inquiry.form.inquiries.length);
      //   const length = 3 - this.inquiry.form.inquiries.length;
      //   for (let i = 0; i < length; i++) {
      //     this.inquiry.form.inquiries.push({
      //       recommend: false,
      //       applyId: this.selectionList[0].applyId,
      //       applyItemId: this.selectionList[0].id,
      //     });
      //   }
      // }
      // this.inquiry.option.detail = false;
      // this.inquiry.visible = true;
    },
    handleInquiry() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择要补的数据");
        return;
      }
      if (this.selectionList.length > 1) {
        this.$message.warning("请选择一条数据");
        return;
      }
      Object.keys(this.inquiry.form).forEach(
        (key) => (this.inquiry.form[key] = "")
      );
      this.inquiry.form = {
        ...this.selectionList[0],
      };
      if (!this.inquiry.form.inquiries) {
        this.inquiry.form.inquiries = [];
      }
      if (this.inquiry.form.inquiries.length < 3) {
        console.log(this.inquiry.form.inquiries.length);
        const length = 3 - this.inquiry.form.inquiries.length;
        for (let i = 0; i < length; i++) {
          this.inquiry.form.inquiries.push({
            recommend: false,
            applyId: this.selectionList[0].applyId,
            applyItemId: this.selectionList[0].id,
          });
        }
      }
      this.inquiry.option.detail = false;
      this.inquiry.visible = true;
    },
    handleRemark() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择要修改的数据");
        return;
      }
      const ids = this.selectionList.map((item) => item.id).join(",");
      this.$prompt("备注", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
      })
        .then(({value}) => {
          return changeRemark(ids, value);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    async handleExport() {
      let msg =
        '是否导出<span style="color: #F56C6C;font-weight: bold">所有数据</span>?';
      if (this.selectionList.length > 0) {
        msg =
          '是否要导出<span style="color: #F56C6C;font-weight: bold">当前选中的数据</span>？';
      }
      let data = [];
      this.$confirm(msg, {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        dangerouslyUseHTMLString: true,
        type: "warning",
      }).then(async () => {
        if (this.selectionList.length > 0) {
          data = this.selectionList;
        } else {
          if (this.dataType === "all") {
            this.query.unArrival = null;
            this.query.unStock = null;
          } else if (this.dataType === "unArrival") {
            this.query.unArrival = true;
            this.query.unStock = null;
          } else if (this.dataType === "unStock") {
            this.query.unStock = true;
            this.query.unArrival = null;
          }
          if (this.brand === "natergy") {
            this.query.brand = "1,2";
          } else if (this.brand === "yy") {
            this.query.brand = "4";
          }
          const query = {
            ...this.query,
            desc: "id",
          };
          if (query.createTime != null) {
            query.startCreateTime = query.createTime[0];
            query.endCreateTime = query.createTime[1];
            query.createTime = null;
          }
          const res = await getList(query);
          data = res.data.data;
        }
        this.$Export.excel({
          title: "采购申请明细",
          columns: this.export1.column,
          data: data.map((item) => {
            return {
              ...item,
              applyStatus: this.statusDictKeyValue[item.applyStatus],
              cost: item.cost ? "费用" : "实物",
              unit: this.unitDictKeyValue[item.unit],
              brand: this.brandDictKeyValue[item.brand],
              crash: item.crash ? "是" : "否",
              type: this.typeDictKeyValue[item.type],
            };
          }),
        });
      });
    },
    rowAttach(row) {
      this.$refs.attachRef.init(row.id, "ni_por_apply_item");
    },
  },
};
</script>

<style></style>

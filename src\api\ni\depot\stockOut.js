import request from "@/router/axios";

export const getList = (current, size, params) => {
  return request({
    url: "/api/ni/depot/stock/out/page",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/ni/depot/stock/out/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const remove = (ids) => {
  return request({
    url: "/api/ni/depot/stock/out/remove",
    method: "post",
    params: {
      ids,
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/ni/depot/stock/out/save",
    method: "post",
    data: row,
  });
};
export const update = (row) => {
  return request({
    url: "/api/ni/depot/stock/out/update",
    method: "post",
    data: row,
  });
};

export const submit = (ids) => {
  return request({
    url: "/api/ni/depot/stock/out/submit",
    method: "post",
    params: {
      ids,
    },
  });
};

export const toVoid = (ids) => {
  return request({
    url: "/api/ni/depot/stock/out/toVoid",
    method: "post",
    params: {
      ids,
    },
  });
};
export const outReturn = (data) => {
  return request({
    url: "/api/ni/depot/stock/out/v1/outReturn",
    method: "post",
    data,
  });
};
export const sync = (id, isCovered = false) => {
  return request({
    url: "/api/ni/depot/stock/out/sync",
    method: "post",
    params: {
      id,
      isCovered,
    },
  });
};

export const syncRange = (startDate, endDate) => {
  return request({
    url: "/api/ni/depot/stock/out/syncRange",
    method: "post",
    params: {
      startDate,
      endDate,
    },
  });
};

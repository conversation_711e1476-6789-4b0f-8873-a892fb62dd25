<template>
  <basic-container>
    <avue-form
      :option="option"
      v-model="form"
      ref="form"
      @submit="handleSubmit"
      @reset-change="handleReset"
    >
    </avue-form>
  </basic-container>
</template>

<script>
import { add, update } from "@/api/ni/tracing/qualityInspectionModified";

export default {
  props: {
    modifiedInspection: {
      require: true,
    },
  },
  data() {
    return {
      form: this.modifiedInspection,
      loading: true,
      option: {
        labelWidth: 150,
        emptyBtn: false,
        column: [
        {
      label: "质检日期",
      prop: "date",
      type: "date",
      format: "yyyy-MM-dd",
      valueFormat: "yyyy-MM-dd",
      width: 100,
      search: true,
      labelSuffix: "<span style='color: red;'>*</span>",
      rules: [
        {
          required: true,
          message: "质检日期为必填项",
          trigger: ["blur", "change"]
        }
      ],
      html: true
    },
    {
      label: "含水550度",
      prop: "waterContent",
      type: "input",
      width: 100,
    },
    {
      label: "变异系数",
      prop: "coefficientOfVariation",
      type: "input",
      width: 100,
    },
    {
      label: "敦实堆积密度",
      prop: "solidBulkDensity",
      type: "input",
      width: 100,
    },
    {
      label: "欧标堆积密度",
      prop: "enBulkDensity",
      type: "input",
      width: 100,
    },
    {
      label: "粒度合格率(%)",
      prop: "particleSizeQualifiedRate",
      type: "input",
      width: 110,
    },
    {
      label: "粒度",
      prop: "particleSize",
      type: "input",
      width: 60,
    },
    {
      label: "温升10g",
      prop: "temperatureRise10",
      type: "input",
      width: 70,
    },
    {
      label: "温升30g",
      prop: "temperatureRise30",
      type: "input",
      width: 70,
    },
    {
      label: "温升50g",
      prop: "temperatureRise50",
      type: "input",
      width: 70,
    },
    {
      label: "5g落粉",
      prop: "fallingPowder5g",
      type: "input",
      width: 80,
    },
    {
      label: "磨前落粉",
      prop: "fallingPowderBeforeGrinding",
      type: "input",
      width: 80,
    },
    {
      label: "磨后落粉",
      prop: "fallingPowderAfterGrinding",
      type: "input",
      width: 80,
    },
    {
      label: "渣子",
      prop: "waste",
      type: "input",
      width: 60,
    },
    {
      label: "70度吸气量",
      prop: "inspiratoryVolume70t",
      type: "input",
      width: 90,
    },
    {
      label: "气体解吸量(ml/g)",
      prop: "gasDesorption",
      type: "input",
      width: 120,
    },
    {
      label: "PH值",
      prop: "ph",
      type: "input",
      width: 90,
    },
    {
      label: "KOH",
      prop: "koh",
      type: "input",
      width: 90,
    },
    {
      label: "强度",
      prop: "strength",
      type: "input",
      width: 90,
    },
    {
      label: "欧标吸附",
      prop: "adsorptionEN",
      type: "input",
      width: 90,
    },

      ],
      },
    };
  },
  methods: {
    handleSubmit(form, done) {
      this.$emit("submit-modified", form);
      done();
    },
    handleReset() {
      this.$emit("close-modified");
    },
    rowSave(row, done, loading) {
      add(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          window.console.log(error);
        }
      );
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          console.log(error);
        }
      );
    },
    onLoad(page, params = {}) {},
  },
};
</script>

<style></style>

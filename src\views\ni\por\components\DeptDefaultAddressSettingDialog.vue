<script>
import {getDetail} from "@/api/ni/por/apply";

export default {
  data() {
    return {
      visible: false,
      option: {
        menu: false,
        search: false,
        header: false,
        editBtn: false,
        addBtn: false,
        span: 6,
        dialogFullscreen: true,
        labelWidth: 110,
        size: "mini",
        searchSize: "mini",
        align: "center",
        calcHeight: 30,
        tip: false,
        border: true,
        index: false,
        viewBtn: false,
        selection: false,
        dialogClickModal: false,
        column: [
          {
            label: '部门',
            prop: 'deptName',
            overHidden: true
          },
          {
            label: '默认收货地',
            prop: 'receivingAddress',
            overHidden: true,
            type: "radio",
            dicUrl: "/api/blade-system/dict-biz/dictionary?code=ni_por_receiving_address",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            clearable: false,
            cell: true,
            rules: [
              {
                required: true,
                message: "请选择默认收货地",
                trigger: "blur",
              },
            ],
          }
        ]
      },
      data: [],
      loading: false,
      form: {}

    }
  },
  methods: {
    onSetting() {
      this.visible = true
    },
    beforeOpen(done, type) {
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then((res) => {
          this.form = res.data.data;
        });
      }
      done();
    },
    handleClose() {
      this.visible = false
    },
    handleConfirm() {
      this.$http.post('/api/ni/por/dept-receiving-address/save', this.data)
          .then((res) => {
            this.$message({
              type: 'success',
              message: '操作成功'
            })
            this.handleClose()
          })
    },
    refreshChange() {
      this.onLoad();
    },
    onLoad() {
      this.loading = true
      this.$http.get('/api/ni/por/dept-receiving-address/list')
          .then((res) => {
            const data = res.data.data;
            data.forEach((item) => item.$cellEdit = true)
            this.data = data;
            this.loading = false;
          })
    }
  }

}
</script>

<template>
  <el-dialog
      title="默认收货地"
      append-to-body
      :visible.sync="visible"
      width="555px"
  >
    <avue-crud
        v-if="visible"
        ref="crud"
        :option="option"
        :data="data"
        :table-loading="loading"
        :before-open="beforeOpen"
        v-model="form"
        @refresh-change="refreshChange"
        @on-load="onLoad"
    >
    </avue-crud>
    <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose" size="mini">取 消</el-button>
        <el-button type="primary" @click="handleConfirm" size="mini"
        >确 定</el-button
        >
      </span>
  </el-dialog>
</template>

<style scoped>

</style>

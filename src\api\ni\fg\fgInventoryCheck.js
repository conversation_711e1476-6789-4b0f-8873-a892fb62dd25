import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/ni/fg/inventory-check/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/ni/fg/inventory-check/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/ni/fg/inventory-check/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}
export const cancel = (ids) => {
  return request({
    url: '/api/ni/fg/inventory-check/cancel',
    method: 'post',
    params: {
      ids,
    }
  })
}
export const add = (row) => {
  return request({
    url: '/api/ni/fg/inventory-check/save',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/ni/fg/inventory-check/update',
    method: 'post',
    data: row
  })
}


export const finish = (id) => {
  return request({
    url: '/api/ni/fg/inventory-check/finish',
    method: 'post',
    params: {
      id,
    }
  })
}

export const confirm = (id) => {
  return request({
    url: '/api/ni/fg/inventory-check/confirm',
    method: 'post',
    params: {
      id,
    }
  })
}

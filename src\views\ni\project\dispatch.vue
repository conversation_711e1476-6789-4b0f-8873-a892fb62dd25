<template>
  <basic-container>
    <el-tabs v-model="dispatchStatus" @tab-click="handleDispatchStatusChange">
      <el-tab-pane label="全部" name="all"></el-tab-pane>
      <el-tab-pane label="未派工" name="0"></el-tab-pane>
      <el-tab-pane label="已派工" name="1"></el-tab-pane>
      <el-tab-pane label="已完工" name="9"></el-tab-pane>
      <el-tab-pane label="已驳回" name="2"></el-tab-pane>
      <el-tab-pane disabled>
        <template #label>
          人员状态:{{ summary.total }}/{{ summary.assigned }};
          请假:{{ summary.leave1 }};
          年假:{{ summary.nleave }};
          婚假:{{ summary.hleave }};
          产假:{{ summary.cleave }};
          陪产假:{{ summary.pleave }};
          工伤假:{{ summary.gsLeave }};
          病假:{{ summary.bleave }};
          丧假:{{ summary.sleave }};
          进修学习:{{ summary.jleave }};
          <el-button type="danger" size="mini" @click="handleLeave"
            >请假
          </el-button>
        </template>
      </el-tab-pane>
    </el-tabs>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      v-model="form"
      ref="crud"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
      @cell-click="cellClick"
      :cell-style="cellStyle"
    >
      <template #projectSerialNo="{ row, index }">
        <span>{{ row.projectSerialNo }}</span>
        <span v-if="row.employCount > 0">({{ row.employCount }})</span>
      </template>
      <template #employSerialNo="{ row, index }">
        <flow-timeline-popover
          v-if="row.employProcessInsId"
          :process-ins-id="row.employProcessInsId"
          :process-def-key="processDefKey"
          v-model="row.employSerialNo"
          :flow.sync="row.employFlow"
          trigger="click"
          lazy
        />
        <span v-else>{{ row.employSerialNo }}</span>
      </template>
      <template #employUserName="{ row, index }">
        <span
          v-if="row.employUserName"
          :style="{
            cursor: 'pointer',
            textDecoration: 'underline',
          }"
          @click="rowDispatchDetail(row)"
        >
          {{ row.employUserName }}
        </span>
      </template>
      <template #assignedToName="{ row, index }">
        <template
          v-if="row.id && row.status === 9 && row.items && row.items.length > 0"
        >
          <span v-for="(item, index) in row.items" :key="index"
            >{{ index > 0 ? "," : "" }}{{ item.personName }}(<span
              style="color: #f56c6c"
              >{{ Number(item.finishHours) }}H</span
            >)</span
          >
        </template>
        <span
          v-else-if="row.status !== 2"
          :style="
            !row.status || row.status !== 9
              ? { cursor: 'pointer', textDecoration: 'underline' }
              : {}
          "
          @click="rowAssignedToChange(row, index)"
        >
          {{ row.assignedToName ? row.assignedToName : "未指派" }}
        </span>
        <span v-else> {{ row.assignedToName }} </span>
      </template>
      <template #workingHours="{ row, index }">
        <span
          v-if="row.workingHours"
          :style="
            !row.status || row.status !== 9
              ? { cursor: 'pointer', textDecoration: 'underline' }
              : {}
          "
          @click="rowWorkingHoursChange(row, index)"
        >
          {{ row.workingHourStr ? row.workingHourStr : row.workingHours }}
        </span>
      </template>
      <template #employWorkDate="{ row, index }">
        <span>{{ row.employStartDate }}</span>
        <span
          v-if="row.employEndDate && row.employEndDate !== row.employStartDate"
        >
          ~{{ row.employEndDate }}
        </span>
      </template>
      <template #communication="{ row, index }">
        <el-tag
          v-if="row.communication"
          size="mini"
          type="danger"
          effect="dark"
        >
          是
        </el-tag>
        <el-tag v-else size="mini" type="info" effect="plain">否</el-tag>
      </template>
      <template #picking="{ row, index }">
        <el-tag v-if="row.picking" size="mini" type="danger" effect="dark">
          是
        </el-tag>
        <el-tag v-else size="mini" type="info" effect="plain">否</el-tag>
      </template>
      <template #drawing="{ row, index }">
        <el-tag v-if="row.drawing" size="mini" type="danger" effect="dark">
          是
        </el-tag>
        <el-tag v-else size="mini" type="info" effect="plain">否</el-tag>
      </template>
      <template #assignedToForm="{ disabled, size, index, row }">
        <user-select
          v-model="form.assignedTo"
          :size="size"
          multiple
          :disabled="disabled"
        ></user-select>
      </template>
      <template #person="{ row, index }">
        <el-tag v-if="row.person" size="mini" type="danger" effect="dark">
          是
        </el-tag>
        <el-tag v-else size="mini" type="info" effect="plain">否</el-tag>
      </template>
      <template #appointUserName="{ row, index }">
        <span v-if="row.person">{{ row.appointUserName }}</span>
      </template>
      <template #opPost="{ row, index }">
        <span v-if="!row.person">{{ row.$opPost }}</span>
      </template>
      <template #employStatus="{ row, index }">
        <el-tag
          v-if="row.employStatus === 23"
          size="mini"
          type="warning"
          effect="plain"
        >
          {{ row.$employStatus }}
        </el-tag>
        <el-tag
          v-else-if="row.employStatus === 24"
          size="mini"
          type="warning"
          effect="dark"
        >
          {{ row.$employStatus }}
        </el-tag>
        <el-tag v-else size="mini" type="success" effect="plain">
          {{ row.$employStatus }}
        </el-tag>
      </template>
      <template #dispatchDate="{ row, size, index }">
        <el-tag v-if="row.dispatchDate" :size="size" type="danger" effect="dark"
          >{{ row.dispatchDate }}
        </el-tag>
      </template>
      <template #applyWorkingHours="{ row, index }">
        <span v-if="row.applyWorkingHours">{{
          row.applyWorkingHours + "H"
        }}</span>
      </template>
      <template #opType="{ row, index }">
        <el-tag v-if="row.opType === '1'" size="mini" type="danger"
          >{{ row.$opType }}
        </el-tag>
        <el-tag v-else-if="row.opType === '2'" size="mini" type="warning"
          >{{ row.$opType }}
        </el-tag>
        <el-tag v-else-if="row.opType === '3'" size="mini"
          >{{ row.$opType }}
        </el-tag>
        <el-tag v-else size="mini" type="info" effect="plain"
          >{{ row.$opType }}
        </el-tag>
      </template>
      <template #menuLeft>
        <div style="display: flex; align-items: center">
          <el-date-picker
            size="mini"
            v-model="dispatchDate"
            type="date"
            placeholder="选择日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            @change="handleDateChange"
            clearable
          >
          </el-date-picker>

          <el-button
            style="margin-left: 5px"
            type="primary"
            size="mini"
            icon="el-icon-s-promotion"
            @click="handleDispatch"
            >开始派工
          </el-button>
          <el-button
            style="margin-left: 5px"
            type="primary"
            size="mini"
            plain
            icon="el-icon-time"
            @click="handleReferenceDispatch"
            >参照上次派工
          </el-button>
          <el-button
            type="warning"
            size="mini"
            icon="el-icon-s-promotion"
            @click="handleCancel"
            >取消派工
          </el-button>
          <el-button
            type="success"
            size="mini"
            icon="el-icon-s-check"
            v-if="1 !== 1"
            @click="handleDispatchFinish"
            >完成派工
          </el-button>
          <el-dropdown>
            <el-button size="mini" icon="el-icon-s-check" type="danger">
              驳回<i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item @click.native="handleEmployOverrule"
                >驳回申请
              </el-dropdown-item>
              <el-dropdown-item @click.native="handleDispatchOverrule"
                >驳回派工
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
          <el-button
            type="info"
            size="mini"
            v-if="selectionList.length > 0 && dispatchStatus === '2'"
            icon="el-icon-refresh-left"
            @click="handleDispatchOverruleCancel"
            >取消驳回
          </el-button>
          <el-dropdown @command="handleExportPlus">
            <el-button size="mini" icon="el-icon-s-check" type="warning">
              导出<i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="a">派工导出</el-dropdown-item>
              <el-dropdown-item command="b">全部导出</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
          <el-button
            type="danger"
            size="mini"
            icon="el-icon-upload"
            plain
            style="margin-left: 0"
            @click="handleDispatchImport"
            >导入派工
          </el-button>
          <!--              <el-checkbox v-model="unDispatch">未派工</el-checkbox>-->
          <template v-if="dispatchStatus === 'all'">
            <el-divider direction="vertical" />
            <el-checkbox v-model="over" @change="handleOverChange"
              >排除驳回数据
            </el-checkbox>
          </template>
        </div>
      </template>
      <template #menuRight>
        <el-radio-group
          v-model="workHours"
          size="mini"
          @input="handleWorkHoursChange"
        >
          <el-radio-button
            v-for="(value, key) in workingHoursMap"
            :key="key"
            :label="key"
          >
            {{ value }}
          </el-radio-button>
        </el-radio-group>
        <el-divider direction="vertical"></el-divider>
        <el-radio-group v-model="opType" size="mini">
          <el-radio-button label="all">全部</el-radio-button>
          <el-radio-button
            v-for="(item, index) in opTypeDict"
            :key="index"
            :label="item.dictKey"
            >{{ item.dictValue }}
          </el-radio-button>
        </el-radio-group>
        <el-divider direction="vertical"></el-divider>
        <el-button
          :icon="personShow ? 'el-icon-user-solid' : 'el-icon-user'"
          circle
          size="mini"
          @click="handlePersonShow"
        ></el-button>
        <el-button
          icon="el-icon-setting"
          circle
          size="mini"
          v-if="opType !== 'all'"
          @click="handleFooterSetting"
        ></el-button>
      </template>
      <template #menu="{ row, index, size }">
        <el-button
          type="text"
          size="mini"
          icon="el-icon-info"
          @click="rowDispatch(row)"
          >派工明细
        </el-button>
      </template>
    </avue-crud>

    <attach-dialog ref="attachDialogRef" />
    <dispatch-item-drawer ref="dispatchItemDrawerRef" />
    <dispatch-person-select
      ref="dispatchPersonSelectRef"
      @confirm="rowAssignedToChangeSubmit"
    />
    <el-dialog
      :visible.sync="workingHours.visible"
      title="指派工时"
      width="600px"
      append-to-body
    >
      <avue-crud
        v-if="workingHours.visible"
        :option="workingHours.options"
        :data="workingHours.data"
        v-model="workingHours.form"
        ref="crud"
      >
      </avue-crud>
      <template #footer>
        <el-button size="mini" @click="workingHours.visible = false"
          >取消
        </el-button>
        <el-button size="mini" type="primary" @click="handleWorkingHoursSubmit">
          确定
        </el-button>
      </template>
    </el-dialog>
    <el-dialog
      :visible.sync="workDate.visible"
      title="调整派工日期"
      width="400px"
      append-to-body
    >
      <avue-form
        ref="form"
        :option="workDate.options"
        v-model="workDate.form"
        @submit="handleWorkDateSubmit"
      />
    </el-dialog>
    <el-dialog
      :visible.sync="leave.visible"
      :title="leave.title"
      width="700px"
      append-to-body
    >
    <template #title>
  <div style="display: flex; align-items: center; flex-wrap: wrap;">
    {{ leave.title }}
    <el-divider direction="vertical"></el-divider>
    <el-date-picker
      size="mini"
      v-model="leave.date"
      type="date"
      placeholder="选择日期"
      format="yyyy-MM-dd"
      value-format="yyyy-MM-dd"
      @change="handleLeaveDateChange"
      clearable
    >
    </el-date-picker>
    <el-dropdown style="margin-left: 5px" @command="handleLeaveAdd">
      <el-button size="mini" icon="el-icon-plus" type="primary">
        新增<i class="el-icon-arrow-down el-icon--right"></i>
      </el-button>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item :command="'1'">假</el-dropdown-item>
        <el-dropdown-item :command="'N'">年假</el-dropdown-item>
        <el-dropdown-item :command="'H'">婚假</el-dropdown-item>
        <el-dropdown-item :command="'C'">产假</el-dropdown-item>
        <el-dropdown-item :command="'P'">陪产假</el-dropdown-item>
        <el-dropdown-item :command="'GS'">工伤假</el-dropdown-item>
        <el-dropdown-item :command="'B'">病假</el-dropdown-item>
        <el-dropdown-item :command="'S'">丧假</el-dropdown-item>
        <el-dropdown-item :command="'J'">进修学习</el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
    <el-button
      style="margin-left: 5px"
      type="danger"
      size="mini"
      icon="el-icon-delete"
      plain
      @click="handleLeaveDelete"
    >删 除
    </el-button>
    <el-button
      type="primary"
      size="mini"
      icon="el-icon-refresh"
      plain
      @click="handleLeaveRefresh"
    >刷新
    </el-button>
    <el-divider direction="vertical" />
    <el-checkbox v-model="overLeaveY" @change="leaveOnLoadLeaveY"
      >年假
    </el-checkbox>

    <!-- 单独换行显示的radio-group -->
    <el-radio-group v-model="opTypeLeave" size="mini" style="width: 100%; margin-top: 20px;">
      <el-radio-button label="all">全部</el-radio-button>
      <el-radio-button
        v-for="(item, index) in opTypeDict"
        :key="index"
        :label="item.dictKey"
      >{{ item.dictValue }}
      </el-radio-button>
    </el-radio-group>
    
  </div>
</template>
      <avue-crud
        ref="leaveRef"
        :key="randomKey"
        v-if="leave.visible"
        :option="leave.option"
        :table-loading="leave.loading"
        :page.sync="leave.page"
        :data="leave.data"
        v-model="leave.form"
        @selection-change="leaveSelectionChange"
        @row-del="leaveRowDel"
        @row-save="leaveRowSave"
        @on-load="leaveOnLoad"
      >
        <template #menuLeft></template>
      </avue-crud>
    </el-dialog>
    <dispatch-finish-dialog
      ref="dispatchFinishDialogRef"
      @submit="handleDispatchFinishSubmit"
    />
    <employ-apply-list-dialog ref="employApplyListDialogRef" />
    <el-dialog
      :visible.sync="footer.visible"
      title="打印追加内容设置"
      width="600px"
      append-to-body
    >
      <avue-crud
        v-if="footer.visible"
        :option="footer.option"
        :data="footer.data"
        v-model="footer.form"
        ref="crud"
      >
      </avue-crud>
      <template #footer>
        <el-button size="mini" @click="footer.visible = false">取消</el-button>
        <el-button
          size="mini"
          type="primary"
          @click="handleFooterSettingSubmit"
        >
          确定
        </el-button>
      </template>
    </el-dialog>
    <el-dialog
      title="派工导入"
      append-to-body
      :visible.sync="dispatchImport.visible"
      width="555px"
    >
      <avue-form
        :option="dispatchImport.option"
        v-model="dispatchImport.form"
        :upload-after="dispatchImportUploadAfter"
      >
        <template #excelTemplate>
          <el-button size="mini" type="primary" @click="handleExport">
            点击下载<i class="el-icon-download el-icon--right"></i>
          </el-button>
        </template>
      </avue-form>
      <el-alert
        title="登记说明"
        type="warning"
        description="派工时在指派人员列填写派工信息，多个人员用/分割，默认派工工时为当前工时，如果需要特别指定工时，则在人员后跟@+工时数.例如：张三/李四@8.5/王五@4.5"
        :closable="false"
      >
      </el-alert>
    </el-dialog>
  </basic-container>
</template>

<script>
import {
  addPersonLeave,
  assign,
  changeRemark,
  changeWorkDate,
  changeWorkingHours,
  deletePersonLeave,
  getFooterSetting,
  getItemList,
  getList,
  getPage,
  getPersonLeaveList,
  getPersonStateSummary,
  overrule as overruleDispatch,
  overruleCancel,
  publish,
  publishCancel,
  referenceDispatch,
  referenceDispatch1,
  updateFooterSetting,
} from "@/api/ni/project/dispatch";
import {
  finish as finishEmploy,
  overrule as overruleEmploy,
} from "@/api/ni/project/employ";
import { mapGetters } from "vuex";
import ProjectSelect from "@/views/ni/project/components/MyProjectSelect";
import UserSelect from "@/components/user-select";
import AttachDialog from "@/components/attach-dialog";
import { dateFormat } from "@/util/date";
import exForm from "@/views/plugin/workflow/mixins/ex-form";
import DispatchPersonSelect from "@/views/ni/project/components/DispatchPersonSelect";
import DispatchItem from "@/views/ni/project/components/DispatchItemDrawer";
import DispatchItemDrawer from "@/views/ni/project/components/DispatchItemDrawer";
import DispatchFinishDialog from "@/views/ni/project/components/DispatchFinishListDialog.vue";
import FlowTimelinePopover from "@/components/wf-flow-timeline/popover.vue";
import EmployApplyListDialog from "@/views/ni/project/components/EmployApplyListDialog.vue";
import NProgress from "nprogress";
import { exportBlob } from "@/api/common";
import { getToken } from "@/util/auth";
import { downloadXls } from "@/util/util";

export default {
  mixins: [exForm],
  components: {
    EmployApplyListDialog,
    FlowTimelinePopover,
    DispatchItem,
    DispatchPersonSelect,
    UserSelect,
    ProjectSelect,
    AttachDialog,
    DispatchItemDrawer,
    DispatchFinishDialog,
  },
  data() {
    return {
      processDefKey: "process_ni_project_employ",
      randomKey: Math.random(),
      row: {},
      dispatchShow: false,
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        showSummary: true,
        sumColumnList: [
          {
            name: "personNum",
            type: "sum",
          },
          {
            name: "assignedNum",
            type: "sum",
          },
        ],
        rowKey: "id",
        menu: false,
        searchShow: false,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        searchIndex: 3,
        searchIcon: true,
        labelWidth: 135,
        size: "mini",
        searchSize: "mini",
        align: "center",
        height: "auto",
        calcHeight: 30,
        tip: false,
        searchMenuSpan: 6,
        border: true,
        index: false,
        viewBtn: false,
        selection: true,
        dialogClickModal: false,
        column: [
          {
            label: "派工状态",
            prop: "status",
            type: "select",
            width: 85,
            dicData: [],
            span: 8,
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            dataType: "number",
            addDisplay: false,
            editDisplay: false,
          },
          {
            label: "申请人",
            prop: "employUserId",
            remote: true,
            dicUrl: `/api/blade-user/search/otj/user?name={{key}}`,
            props: {
              label: "realName",
              value: "id",
            },
            dicFormatter: (data) => {
              return data.data.records;
            },
            placeholder: " ",
            filterable: true,
            type: "select",
            hide: true,
            showColumn: false,
            display: false,
            search: true,
          },
          {
            label: "申请人",
            prop: "employUserName",
            display: false,
            disabled: true,
            width: 60,
            overHidden: true,
            span: 8,
          },
          {
            label: "申请部门",
            prop: "employDeptId",
            type: "tree",
            dicUrl: "/api/blade-system/dept/tree",
            props: {
              label: "title",
              value: "id",
            },
            multiple: true,
            checkStrictly: true,
            display: false,
            hide: true,
            search: true,
            searchMultiple: true,
            showColumn: false,
          },
          {
            label: "申请部门",
            prop: "employDeptName",
            display: false,
            width: 70,
            overHidden: true,
            span: 8,
          },

          {
            label: "项目编号",
            prop: "projectId",
            type: "select",
            remote: true,
            dicUrl: "/api/ni/project/page?status=9&keyword={{key}}",
            props: {
              label: "serialNo",
              value: "id",
              desc: "title",
            },
            dicFormatter: (data) => {
              return data.data.records;
            },
            search: true,
            hide: true,
            showColumn: false,
            span: 8,
            rules: [
              {
                required: true,
                message: "请选择项目",
                trigger: "blur",
              },
            ],
          },
          {
            label: "项目编号",
            prop: "projectSerialNo",
            overHidden: true,
            addDisplay: false,
            editDisplay: false,
            width: 105,
            span: 8,
          },
          {
            label: "负责人",
            prop: "chargerName",
            width: 70,
          },
          {
            label: "派工日期",
            prop: "workDate",
            type: "date",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            search: true,
            searchRange: true,
            searchOrder: 98,
            span: 8,
            width: 85,
            rules: [
              {
                required: true,
                message: "请选择派工日期",
                trigger: "blur",
              },
            ],
          },
          {
            label: "用工内容",
            prop: "content",
            type: "textarea",
            overHidden: true,
            span: 24,
            minRows: 2,
            minWidth: 120,
            rules: [
              {
                required: true,
                message: "请输入派工内容",
                trigger: "blur",
              },
            ],
          },
          {
            label: "每日用工数",
            prop: "personNum",
            type: "number",
            width: 85,
            span: 8,
            rules: [
              {
                required: true,
                message: "请输入用工数",
                trigger: "blur",
              },
            ],
          },
          {
            label: "操作人",
            prop: "operatorName",
            width: 80,
            overHidden: true,
          },
          {
            label: "指派人员",
            prop: "assignedTo",
            hide: true,
            showColumn: false,
            type: "select",
            remote: true,
            dicUrl: `/api/blade-user/search/otj/user?name={{key}}`,
            props: {
              label: "realName",
              value: "id",
            },
            dicFormatter: (data) => {
              return data.data.records;
            },
            search: true,
          },
          {
            label: "指派人员",
            prop: "assignedToName",
            display: false,
            overHidden: true,
            width: 110,
            span: 8,
          },
          {
            label: "指派工时/人",
            prop: "workingHours",
            display: false,
            overHidden: true,
            width: 90,
            span: 8,
          },
          {
            label: "备注",
            prop: "remark",
            display: false,
            overHidden: true,
            width: 90,
            span: 8,
          },
          {
            label: "状态",
            prop: "employStatus",
            type: "select",
            hide: true,
            width: 94,
            overHidden: true,
            dicData: [],
            span: 8,
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            dataType: "number",
            addDisplay: false,
            editDisplay: false,
          },

          {
            label: "用工类型",
            prop: "opType",
            type: "select",
            dicData: [],
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            width: 70,
            span: 8,
            rules: [
              {
                required: true,
                message: "请选择派工类型",
                trigger: "blur",
              },
            ],
          },
          {
            label: "派工数",
            prop: "assignedNum",
            type: "number",
            width: 85,
          },
          {
            label: "是否指定人员",
            prop: "person",
            value: 0,
            span: 8,
            type: "radio",
            minWidth: 70,
            hide: true,
            row: true,
            dicData: [
              {
                label: "否",
                value: 0,
              },
              {
                label: "是",
                value: 1,
              },
            ],
            placeholder: " ",
            rules: [
              {
                required: true,
                message: "请选择指定人员",
                trigger: "blur",
              },
            ],
            control: (val) => {
              if (val) {
                this.form.opPost = null;
                return {
                  appointUserId: {
                    display: true,
                  },
                  person: {
                    row: false,
                  },
                };
              } else {
                this.form.appointUserId = null;
                return {
                  appointUserId: {
                    display: false,
                  },
                  person: {
                    row: true,
                  },
                };
              }
            },
          },
          {
            label: "指定人员",
            prop: "appointUserId",
            placeholder: " ",
            display: false,
            span: 8,
            row: true,
            hide: true,
            showColumn: false,
            rules: [
              {
                required: true,
                message: "请选择指定人员",
                trigger: "blur",
              },
            ],
          },
          {
            label: "指定人员",
            prop: "appointUserName",
            width: 100,
            display: false,
            overHidden: true,
            span: 8,
            fixed: "right",
          },
          {
            label: "用工单号",
            prop: "employSerialNo",
            placeholder: " ",
            overHidden: true,
            width: 120,
            disabled: true,
            search: true,
            span: 8,
          },
          {
            label: "用工日期",
            prop: "employWorkDate",
            search: true,
            searchRange: true,
            searchOrder: 99,
            overHidden: true,
            span: 8,
            width: 159,
            type: "date",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
          },
          {
            label: "关联单号",
            prop: "employParentSerialNo",
            width: 110,
            overHidden: true,
          },
          {
            label: "夜班",
            prop: "night",
            span: 8,
            type: "radio",
            minWidth: 70,
            dicData: [
              {
                label: "否",
                value: 0,
              },
              {
                label: "是",
                value: 1,
              },
            ],
            placeholder: " ",
          },
          {
            label: "预计工时",
            labelTip: "人/小时",
            prop: "applyWorkingHours",
            type: "number",
            width: 70,
            span: 8,
            rules: [
              {
                required: true,
                message: "请输入 预计工时(小时)",
                trigger: "blur",
              },
            ],
            dataType: "number",
            min: 0,
            row: true,
            minWidth: 90,
          },
          {
            label: "材料备齐",
            prop: "ready",
            value: true,
            span: 8,
            type: "radio",
            minWidth: 70,
            dicData: [
              {
                label: "否",
                value: 0,
              },
              {
                label: "是",
                value: 1,
              },
            ],
            placeholder: " ",
          },
          {
            label: "是否落实图纸",
            prop: "drawing",
            value: 0,
            span: 8,
            type: "radio",
            minWidth: 70,
            dicData: [
              {
                value: 0,
                label: "不需要",
              },
              {
                label: "否",
                value: -1,
              },
              {
                label: "是",
                value: 1,
              },
            ],
            placeholder: " ",
          },
          {
            type: "radio",
            label: "是否与生产沟通",
            dicData: [
              {
                value: 0,
                label: "不需要",
              },
              {
                label: "是",
                value: 1,
              },
              {
                value: -1,
                label: "否",
              },
            ],
            span: 8,
            display: true,
            props: {
              label: "label",
              value: "value",
            },
            prop: "communication",
            value: 0,
            required: true,
          },
          {
            label: "是否需领料",
            prop: "picking",
            value: false,
            span: 8,
            hide: true,
            type: "radio",
            minWidth: 70,
            dicData: [
              {
                label: "否",
                value: false,
              },
              {
                label: "是",
                value: true,
              },
            ],
            placeholder: " ",
            control: (val) => {
              if (val) {
                return {
                  materials: {
                    display: true,
                  },
                };
              } else {
                return {
                  materials: {
                    display: false,
                  },
                };
              }
            },
          },
          {
            label: "领料明细",
            prop: "materials",
            labelPosition: "top",
            display: false,
            span: 24,
            width: 120,
            overHidden: true,
          },
          {
            label: "派工单号",
            prop: "serialNo",
            width: 110,
            overHidden: true,
          },
          {
            label: "评分",
            prop: "rate",
            type: "rate",
            max: 5,
            width: 150,
            texts: ["极差", "失望", "一般", "满意", "惊喜"],
            hide: true,
            fixed: "right",
            colors: ["#99A9BF", "#F7BA2A", "#FF9900"],
          },
        ],
      },
      data: [],
      dispatch: false,
      personShow: false,
      dispatchDate: dateFormat(new Date(), "yyyy-MM-dd"),
      opTypeDict: [],
      opTypeDictKeyValue: {},
      opPostDict: [],
      opPostDictKeyValue: {},
      detailVisible: false,
      unDispatch: false,
      unFinish: true,
      workingHours: {
        visible: false,
        options: {
          header: false,
          menu: false,
          searchShow: false,
          addBtn: false,
          editBtn: false,
          delBtn: false,
          size: "mini",
          align: "center",
          calcHeight: 30,
          tip: false,
          border: true,
          index: false,
          viewBtn: false,
          selection: false,
          dialogClickModal: false,
          column: [
            {
              label: "指派人员",
              prop: "personName",
              disabled: true,
              placeholder: " ",
            },
            {
              label: "指派工时",
              prop: "finishHours",
              type: "number",
              remote: true,
              placeholder: " ",
              cell: true,
            },
          ],
        },
        form: {},
        data: [],
      },
      workDate: {
        visible: false,
        options: {
          enter: true,
          size: "mini",
          span: 24,
          emptyBtn: false,
          column: [
            {
              label: "派工日期",
              prop: "workDate",
              type: "date",
              format: "yyyy-MM-dd",
              valueFormat: "yyyy-MM-dd",
              overHidden: true,
              placeholder: " ",
              rules: [
                {
                  required: true,
                  message: "请选择派工日期",
                  trigger: "blur",
                },
              ],
            },
          ],
        },
        form: {},
      },
      opType: "all",
      opTypeLeave: "all",
      workHours: "8.5",
      over: true,
      overLeaveY: false,
      export: {
        column: [
          {
            label: "申请人",
            prop: "employUserName",
            width: 100,
          },
          {
            label: "申请部门",
            prop: "employDeptName",
            width: 100,
          },
          {
            label: "项目号",
            prop: "projectSerialNo",
            width: 150,
          },
          {
            label: "负责人",
            prop: "chargerName",
            width: 100,
          },
          {
            label: "用工申请",
            prop: "employSerialNo",
            width: 150,
          },
          {
            label: "用工类型",
            prop: "opType",
          },
          {
            label: "用工日期",
            prop: "employWorkDate",
          },
          {
            label: "是否夜班",
            prop: "night",
          },
          {
            label: "用工内容",
            prop: "content",
            width: 280,
          },
          {
            label: "指定人员",
            prop: "appointUserName",
          },
          {
            label: "每日用工数",
            prop: "personNum",
            width: 100,
          },
          {
            label: "派工日期",
            prop: "workDate",
            width: 100,
          },
          {
            label: "指派人员",
            prop: "assignedToName",
            width: 200,
          },
          {
            label: "备注",
            prop: "remark",
            width: 200,
          },
        ],
      },
      finish: false,
      dispatchStatus: "all",
      summary: {
        total: "--",
        assigned: "--",
        leave1: "--",
        nleave: "--",
        hleave: "--",
        cleave: "--",
        pleave: "--",
        gsLeave: "--",
        bleave: "--",
        sleave: "--",
        jleave: "--",
      },
      leave: {
        selectionList: [],
        title: "请假管理",
        date: null,
        visible: false,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0,
        },
        option: {
          header: false,
          rowKey: "id",
          menuWidth: 100,
          searchShow: false,
          addBtn: false,
          editBtn: false,
          size: "mini",
          searchSize: "mini",
          align: "center",
          calcHeight: 30,
          tip: false,
          searchMenuSpan: 6,
          border: true,
          index: false,
          viewBtn: false,
          selection: true,
          dialogClickModal: false,
          column: [
          {
              label: "请假类型",
              prop: "leaveType",
              type: "select",
              disabled: true,
              dicData: [
                {
                  label: "假",
                  value: '1',
                },
                {
                  label: "年假",
                  value: 'N',
                },
                {
                  label: "婚假",
                  value: 'H',
                },
                {
                  label: "产假",
                  value: 'C',
                },
                {
                  label: "陪产假",
                  value: 'P',
                },
                {
                  label: "工伤假",
                  value: 'GS',
                },
                {
                  label: "病假",
                  value: 'B',
                },
                {
                  label: "丧假",
                  value: 'S',
                },
                {
                  label: "进修学习",
                  value: 'J',
                },
              ],
              width: 80,
              control: (val) => {
                if (val !== '1') {
                  return {
                    date: {
                      display: false,
                    },
                    dateRange: {
                      display: true,
                    },
                    duration: {
                      display: false,
                    },
                  };
                } else {
                  return {
                    date: {
                      display: true,
                    },
                    dateRange: {
                      display: false,
                    },
                    duration: {
                      display: true,
                    },
                  };
                }
              },
            },

            {
              label: "起始日期",
              prop: "startDate",
              width: 105,
              display: false,
              hide: this.overLeaveY,
            },
            {
              label: "终止日期",
              prop: "endDate",
              width: 105,
              display: false,
              hide: this.overLeaveY,
            },

            {
              label: "请假日期",
              prop: "date",
              type: "date",
              format: "yyyy-MM-dd",
              valueFormat: "yyyy-MM-dd",
              placeholder: " ",
              rules: [
                {
                  required: true,
                  message: "请选择请假日期",
                  trigger: "blur",
                },
              ],
              display: true,
              overHidden: true,
              width: 105,
              hide: this.overLeaveY,
            },
            {
              label: "请假日期",
              prop: "dateRange",
              type: "daterange",
              format: "yyyy-MM-dd",
              valueFormat: "yyyy-MM-dd",
              placeholder: " ",
              hide: true,
              showColumn: false,
              display: false,
              rules: [
                {
                  required: true,
                  message: "请选择请假日期",
                  trigger: "blur",
                },
              ],
              overHidden: true,
              width: 150,
            },
            {
              label: "请假人",
              prop: "userName",
              width: 100,
              display: false,
            },

            {
              label: "请假人",
              prop: "userIds",
              type: "select",
              props: {
                label: "realName",
                value: "id",
              },
              remote: false,
              dicData: [],
              filterable: true,
              filterMethod: (value, option) => {
                if (!value) return true;
                return option.realName.toLowerCase().includes(value.toLowerCase());
              },
              placeholder: "请选择请假人",
              showColumn: true,
              hide: true,
              overHidden: true,
              minWidth: 120,
              multiple: true,
              collapseTags: false, 
              search: true,
              multipleLimit: 0,
              formatter: (row, column) => {
                if (row.userIds && row.userIds.length > 0) {
                  return row.userIds.join('、');
                }
                return column == null ? '' : column.placeholder;
              },
              clearable: true,
            },

            {
              label: "请假部门",
              prop: "deptName",
              width: 110,
              display: false,
            },
            {
              label: "请假时长",
              labelTip: "不写的话默认全天，该工作日无法再继续派工",
              prop: "duration",
              type: "number",
              placeholder: " ",
              width: 105,
              min: 0.5,
              max: 24,
              display: true,
              hide: this.overLeaveY,
            },
          ],
        },
        form: {},
        data: [],
      },
      workingHoursMap: {
        8.5: "8:00~17:30",
        12: "7:00~19:00",
      },
      footer: {
        visible: false,
        option: {
          addBtn: false,
          addRowBtn: true,
          menu: false,
          rowKey: "id",
          menuWidth: 100,
          searchShow: false,
          size: "mini",
          searchSize: "mini",
          align: "center",
          calcHeight: 30,
          tip: false,
          searchMenuSpan: 6,
          border: true,
          index: false,
          viewBtn: false,
          selection: true,
          dialogClickModal: false,
          column: [
            {
              label: "标题",
              prop: "title",
              placeholder: " ",
              row: true,
              cell: true,
              rules: [
                {
                  required: true,
                  message: "请输入标题",
                  trigger: "blur",
                },
              ],
              overHidden: true,
              minWidth: 130,
            },
            {
              label: "内容",
              prop: "content",
              type: "textarea",
              minRows: 1,
              minWidth: 210,
              placeholder: " ",
              span: 24,
              cell: true,
              rules: [
                {
                  required: true,
                  message: "请输入内容",
                  trigger: "blur",
                },
              ],
            },
          ],
        },
        data: [],
        form: {},
      },
      dispatchImport: {
        visible: false,
        option: {
          size: "mini",
          searchSize: "mini",
          submitBtn: false,
          emptyBtn: false,
          column: [
            {
              label: "模板上传",
              prop: "excelFile",
              type: "upload",
              drag: true,
              loadText: "模板上传中，请稍等",
              span: 24,
              propsHttp: {
                res: "data",
              },
              tip: "请上传 .xls,.xlsx 标准格式文件",
              action: "/api/ni/project/dispatch/import-dispatch",
            },
            {
              label: "模板下载",
              prop: "excelTemplate",
              formslot: true,
              span: 24,
            },
          ],
        },
        form: {},
      },
    };
  },
  computed: {
    ...mapGetters(["permission", "userInfo", "colorName"]),
    permissionList() {
      return {};
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  watch: {
    opType: {
      handler(val) {
        this.$nextTick(() => {
          this.page.currentPage = 1;
          this.onLoad(this.page);
        });
      },
    },

    opTypeLeave: {
      handler(val) {
        this.$nextTick(() => {
          this.page.currentPage = 1;
          this.leaveOnLoad(this.page);
        });
      },
    },

    workHours(newVal) {
      localStorage.setItem("workHours", newVal);
    },

    overLeaveY: {
      handler(val) {
        const startDateCol = this.findObject(this.leave.option.column, "startDate");
      const endDateCol = this.findObject(this.leave.option.column, "endDate");
      const dateCol = this.findObject(this.leave.option.column, "date");
      const durationCol = this.findObject(this.leave.option.column, "duration");
      startDateCol.hide = !val;
      endDateCol.hide = !val;
      dateCol.hide = val;
      durationCol.hide = val;
      // 刷新列表布局
      if (this.$refs.leaveRef) {
        this.$refs.leaveRef.doLayout();
      }
      },
      immediate: true // 初始化时执行一次，确保默认状态正确
    },
  },
  created() {
    this.dictInit();
    this.loadAllUsers();
  },
  mounted() {
    const savedWorkHours = localStorage.getItem("workHours");
    if (savedWorkHours) {
      this.workHours = savedWorkHours;
    }
  },
  methods: {
    loadAllUsers() {
    this.$http.get("/api/blade-user/search/otj/allUser")
      .then(res => {
        const userColumn = this.findObject(this.leave.option.column, "userIds");
        userColumn.dicData = res.data.data || [];
      });
    },
    handleWorkHoursChange(val) {
      this.$alert(
        `工作时段切换为<span style="color: #F56C6C;font-weight: bold">${this.workingHoursMap[val]}</span>`,
        "提示",
        {
          confirmButtonText: "确定",
          dangerouslyUseHTMLString: true,
          center: true,
          callback: (action) => {
            this.$message({
              type: "success",
              message: "操作成功",
            });
          },
        }
      );
    },
    handleLeave() {
      this.leave.title = "请假管理";
      this.leave.date = this.dispatchDate;
      this.leave.visible = true;
    },
    handleLeaveDateChange(val) {
      this.leaveOnLoad(this.leave.page);
    },
    handleLeaveAdd(c) {
      this.leave.form = {
        date: this.leave.date,
        leaveType: c,
      };
      this.$refs.leaveRef.rowAdd();
    },
    leaveRowSave(row, done, loading) {
      if (row.leaveType!=='1') {
        row.startDate = row.dateRange[0];
        row.endDate = row.dateRange[1];
      }
      addPersonLeave(row).then(
        () => {
          this.leaveOnLoad(this.leave.page);
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          window.console.log(error);
          loading();
        }
      );
    },
    handleLeaveRefresh() {
      this.leaveOnLoad(this.leave.page);
    },
    handleLeaveDelete() {
      if (this.leave.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let ids = [];
          this.leave.selectionList.forEach((ele) => {
            ids.push(ele.id);
          });
          return deletePersonLeave(ids.join(","));
        })
        .then(() => {
          this.leaveOnLoad(this.leave.page);
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    leaveSelectionChange(list) {
      this.leave.selectionList = list;
    },
    leaveRowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return deletePersonLeave(row.id);
        })
        .then(() => {
          this.leaveOnLoad(this.leave.page);
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },

    leaveOnLoadLeaveY(val) {
      this.randomKey = Math.random();
      this.page.currentPage = 1;
      this.leaveOnLoad(this.page);
    },

    leaveOnLoad(page) {
      console.log(this.overLeaveY)
      this.leave.loading = true;
      const opTypeLeave = this.opTypeLeave === "all" ? null : this.opTypeLeave;
      let deptCode = opTypeLeave === "1" ? "011004" : "011005";
      if (this.opTypeLeave === "all") {
        deptCode = "011004,011005";
      }
      getPersonLeaveList(page.currentPage, page.pageSize,deptCode,this.overLeaveY,{
        date: this.leave.date,
      }).then((res) => {
        const data = res.data.data;
        this.leave.data = data.records;
        this.leave.page.total = data.total;
        this.leave.loading = false;
      });
    },
    handleAdd() {
      alert("针对没有申请的，需要直接派的");
    },
    handleDispatchStatusChange(val) {
      this.page.currentPage = 1;
      this.onLoad(this.page);
    },
    rowDispatchDetail(row) {
      this.$refs.dispatchItemDrawerRef.onShow(row.employId);
    },
    rowWorkingHoursChange(row) {
      if (row.status === 9) {
        return;
      }
      if (!this.dispatchDate) {
        this.$message({
          type: "warning",
          message: "请选择派工日期!",
        });
        return;
      }
      if (!row.id) {
        this.$message({
          type: "warning",
          message: "请先派工!",
        });
        return;
      }
      this.workingHours.form = row;
      this.workingHours.data = [];
      getItemList(row.id).then((res) => {
        this.workingHours.data = res.data.data.map((item) => {
          return {
            ...item,
            $cellEdit: true,
          };
        });
        this.workingHours.visible = true;
      });
    },
    handleDispatchFinishSubmit(form) {
      this.onLoad(this.page);
    },
    handleWorkDateSubmit(form, done) {
      changeWorkDate(form.id, form.workDate)
        .then((res) => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "提交成功!",
          });
          this.workDate.visible = false;
        })
        .finally(() => {
          done();
        });
    },
    handleWorkingHoursSubmit() {
      changeWorkingHours({ items: this.workingHours.data }).then(() => {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "提交成功!",
        });
        this.workingHours.visible = false;
      });
    },
    rowAssignedToChange(row, index) {
      if (row.status === 9) {
        return;
      }
      if (!this.dispatchDate) {
        this.$message({
          type: "warning",
          message: "请选择派工日期!",
        });
        return;
      }
      this.form = row;
      this.form._index = index;
      //制作 研发辅助一组
      //电工 研发辅助二组
      let deptCode = row.opType === "1" ? "011004" : "011005";

      this.$refs.dispatchPersonSelectRef.onSelect(
        this.dispatchDate,
        row.assignedTo,
        deptCode,
        this.workingHoursMap[this.workHours]
      );
    },
    rowAssignedToChangeSubmit(selectionList, cancel) {
      const personIds = selectionList.map((item) => item.userId);
      assign(
        this.form.employId,
        this.dispatchDate,
        personIds.join(","),
        cancel,
        this.workHours
      ).then((res) => {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "提交成功!",
        });
      });
    },
    rowDispatch(row) {
      this.$refs.dispatchItemDrawerRef.onShow(row);
    },
    rowAttach(row) {
      this.$refs.attachDialogRef.init(row.id, "ni_project_dispatch");
    },
    projectConfirm(selectionList) {
      if (selectionList) {
        if (
          this.$refs.crud &&
          "[object Function]" === toString.call(this.$refs.crud.clearValidate)
        ) {
          this.$refs.crud.clearValidate(["projectId"]);
        }
      }
    },
    dictInit() {
      this.$http
        .get(
          "/api/blade-system/dict-biz/dictionary?code=ni_project_dispatch_status"
        )
        .then((res) => {
          const column = this.findObject(this.option.column, "status");
          column.dicData = res.data.data;
        });
      this.$http
        .get(
          "/api/blade-system/dict-biz/dictionary?code=ni_project_employ_status"
        )
        .then((res) => {
          const column = this.findObject(this.option.column, "employStatus");
          column.dicData = res.data.data;
        });
      this.$http
        .get(
          "/api/blade-system/dict-biz/dictionary?code=ni_project_dispatch_type"
        )
        .then((res) => {
          const column = this.findObject(this.option.column, "opType");
          column.dicData = res.data.data;
          this.opTypeDict = res.data.data;
          this.opTypeDictKeyValue = this.opTypeDict.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });
    },
    handleCancel() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      const over = this.selectionList.some((item) => item.status === 2);
      if (over) {
        this.$message.warning("选择的数据中有已驳回的数据，请重新选择");
        return;
      }
      const finish = this.selectionList.some((item) => item.status === 9);
      if (finish) {
        this.$message.warning("选择数据中有已完成的数据，请重新选择");
        return;
      }
      const msg = `确认要取消选择的派工?`;
      this.$confirm(msg, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        dangerouslyUseHTMLString: true,
        type: "warning",
        center: true,
      }).then(() => {
        publishCancel(this.ids).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "提交成功!",
          });
        });
      });
    },
    handleReferenceDispatch() {
      // 1. 校验逻辑（复用现有校验）
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      // 3. 构建确认提示信息
      const confirmMessage =
        "确认使用最近的历史派工数据填充今日派工(<span style='color: #F56C6C;font-weight: bold'>已派工的将跳过</span>)？";
      // 4. 弹出确认框
      this.$confirm(confirmMessage, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        dangerouslyUseHTMLString: true,
      })
        .then(() => {
          // 5. 用户确认后执行填充逻辑
          this.fillWithHistoricalData1();
        })
        .catch(() => {
          // 6. 用户取消操作
          this.$message.info("操作已取消");
        });
    },
    handleDispatch() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      if (!this.dispatchDate) {
        this.$message.warning("请选择派工日期");
        return;
      }
      const dispatched = this.selectionList.some((item) => item.status !== 0);
      if (dispatched) {
        this.$message.warning("选择的数据存在已派工的数据，请重新选择");
        return;
      }
      const unDispatch = this.selectionList.some(
        (item) => !item.assignedToName
      );
      if (unDispatch) {
        this.$message.warning("选择的数据未指派人员");
        return;
      }
      const msg = `确认要将选择的<span style="color: #F56C6C;font-weight: bold">${this.selectionList.length}</span>条数据派工?`;
      this.$confirm(msg, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        dangerouslyUseHTMLString: true,
        type: "warning",
        center: true,
      }).then(() => {
        publish(this.ids).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "提交成功!",
          });
        });
      });
    },
    handleDispatchImport() {
      if (!this.opType || this.opType === "all") {
        this.$message.warning("请选择用工类型");
        return;
      }
      this.$confirm("导入后将会覆盖已派工的数据，是否继续？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        const column = this.findObject(
          this.dispatchImport.option.column,
          "excelFile"
        );
        column.action = `/api/ni/project/dispatch/import-dispatch?opType=${this.opType}&workingHours=${this.workHours}`;
        this.dispatchImport.visible = true;
      });
    },
    dispatchImportUploadAfter(res, done, loading, column) {
      window.console.log(column);
      this.dispatchImport.visible = false;
      this.onLoad(this.page);
      done();
    },
    handleExportPlus(command) {
      let msg =
        command === "b" ? "确认要导出所有数据吗?" : "确认要导出当日派工数据?";
      this.$confirm(msg, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        if (command === "b") {
          this.handleExport();
        } else {
          NProgress.start();
          const q = { ...this.query };
          if (q.workDate != null && q.workDate.length > 1) {
            q.workDateStart = q.workDate[0];
            q.workDateEnd = q.workDate[1];
            q.workDate = null;
          }
          if (q.employWorkDate != null && q.employWorkDate.length > 1) {
            q.employWorkDateStart = q.employWorkDate[0];
            q.employWorkDateEnd = q.employWorkDate[1];
            q.employWorkDate = null;
          }
          q.dispatchDate = this.dispatchDate;
          if (!q.queryEmployStatus) q.queryEmployStatus = "2,91,92,9";

          exportBlob(
            `/api/ni/project/dispatch/export-dispatch?${
              this.website.tokenHeader
            }=${getToken()}&dispatchDate=${q.dispatchDate}&queryEmployStatus=${
              q.queryEmployStatus
            }&opType=${this.opType === "all" ? "" : this.opType}&dispatch=${
              command === "a" ? 1 : 0
            }&workingHoursStr=${this.workingHoursMap[this.workHours]}`
          ).then((res) => {
            downloadXls(res.data, q.dispatchDate + "派工单.xlsx");
            NProgress.done();
          });
        }
      });
    },
    handleExport() {
      this.$confirm("是否导出当日派工数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        const q = { ...this.query };
        if (q.workDate != null && q.workDate.length > 1) {
          q.workDateStart = q.workDate[0];
          q.workDateEnd = q.workDate[1];
          q.workDate = null;
        }
        if (q.employWorkDate != null && q.employWorkDate.length > 1) {
          q.employWorkDateStart = q.employWorkDate[0];
          q.employWorkDateEnd = q.employWorkDate[1];
          q.employWorkDate = null;
        }
        q.dispatchDate = this.dispatchDate;
        if (!q.queryEmployStatus) q.queryEmployStatus = "2,91,92,9";

        if (q.employDeptId != null) {
          q.queryEmployDeptIds = q.employDeptId.join(",");
        }
        if (this.opType === "all") {
          q.opType = null;
        } else q.opType = this.opType;
        q.status = this.dispatchStatus === "all" ? null : this.dispatchStatus;
        if (q.status == null && this.over) {
          q.excludeStatus = "2";
        }
        getList(q).then((res) => {
          const data = res.data.data;
          data.forEach((item) => {
            if (item.assignedTo)
              item.personNum = item.assignedTo.split(",").length;
            item.employWorkDate = item.employStartDate;
            if (
              item.employEndDate &&
              item.employEndDate !== item.employStartDate
            ) {
              item.employWorkDate += "~" + item.employEndDate;
            }
            item.night = item.night ? "是" : "否";
            if (
              item.id &&
              item.status === 9 &&
              item.items &&
              item.items.length > 0
            ) {
              item.items.forEach((item) => {
                item.personName =
                  item.personName + "(" + item.finishHours + "H)";
              });
            }
            item.opType = this.opTypeDictKeyValue[item.opType];
          });
          this.$Export.excel({
            title: this.dispatchDate + "派工单",
            columns: this.export.column,
            data,
          });
        });
      });
    },
    handleDispatchOverruleCancel() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      const over = this.selectionList.some((item) => item.status !== 2);
      if (over) {
        this.$message.warning("选择的数据中存在非驳回的数据，请重新选择");
        return;
      }
      const employFinish = this.selectionList.some(
        (item) => item.employStatus === 92
      );
      if (employFinish) {
        this.$message.warning("选择的数据中存在已结束的用工申请，请重新选择");
        return;
      }
      this.$confirm("确定取消该驳回?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return overruleCancel(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    handleDispatchOverrule() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      const over = this.selectionList.some((item) => item.status === 2);
      if (over) {
        this.$message.warning("选择的数据中存在已驳回的数据，请重新选择");
        return;
      }
      const dispatch = this.selectionList.some((item) => item.status === 1);
      if (dispatch) {
        this.$message.warning("选择的数据中存在已派工的数据，请重新选择");
        return;
      }
      const f = this.selectionList.some((item) => item.status === 9);
      if (f) {
        this.$message.warning("选择的数据中存在已完工的数据，请重新选择");
        return;
      }
      const employFinish = this.selectionList.some(
        (item) => item.employStatus === 92
      );
      if (employFinish) {
        this.$message.warning("选择的数据中存在已结束的用工申请，请重新选择");
        return;
      }
      this.$confirm("确定将今日的派工驳回?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return overruleDispatch(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    handleEmployOverrule() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      const dispatch = this.selectionList.some((item) => item.id);
      if (dispatch) {
        this.$message.warning("选择的数据中存在已派工的数据，请重新选择");
        return;
      }
      const employFinish = this.selectionList.some(
        (item) => item.employStatus === 92
      );
      if (employFinish) {
        this.$message.warning("选择的数据中存在已结束的用工申请，请重新选择");
        return;
      }
      this.$confirm("确认要驳回选择的用工申请？", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          const employIds = this.selectionList.map((item) => item.employId);
          return overruleEmploy(employIds.join(","));
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    handleDispatchFinish() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      const unDispatch = this.selectionList.some((item) => !item.id);
      if (unDispatch) {
        this.$message.warning("选择的数据中存在未派工的数据，请重新选择");
        return;
      }
      const employFinish = this.selectionList.some(
        (item) => item.employStatus === 92
      );
      if (employFinish) {
        this.$message.warning("选择的数据中存在已结束的用工申请，请重新选择");
        return;
      }
      let finish = !this.selectionList.some((item) => item.status !== 9);

      if (finish) {
        this.handleFinishEmploy();
        return;
      }
      let status = new Set();
      this.selectionList.forEach((item) => {
        status.add(item.status);
      });
      if (status.size > 1 && status.has(2)) {
        this.$message.warning("选择的数据中存在不同状态的数据，请重新选择");
        return;
      }
      this.$refs.dispatchFinishDialogRef.onShow(this.selectionList);
    },
    handleOverChange(val) {
      this.page.currentPage = 1;
      this.onLoad(this.page);
    },
    handleFinishEmploy() {
      this.$confirm("选择的派工已完工，是否结束该用工申请?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          const employIds = this.selectionList.map((item) => item.employId);
          return finishEmploy(employIds.join(","));
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    handlePersonShow() {
      this.$refs.dispatchPersonSelectRef.onShow(this.dispatchDate);
    },
    handleFooterSetting() {
      if (this.opType === "all") {
        return;
      }
      getFooterSetting(this.opType).then((res) => {
        this.footer.data = res.data.data;
        this.footer.visible = true;
      });
    },
    handleFooterSettingSubmit() {
      if (this.opType === "all") {
        return;
      }
      updateFooterSetting(this.opType, this.footer.data).then(() => {
        this.$message({
          type: "success",
          message: "操作成功!",
        });
        this.footer.visible = false;
      });
    },
    searchReset() {
      this.query = {};
      this.opType = "all";
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    handleDateChange() {
      this.page.currentPage = 1;
      this.onLoad(this.page);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      const q = {
        ...params,
        ...this.query,
      };
      if (q.workDate != null && q.workDate.length > 1) {
        q.workDateStart = q.workDate[0];
        q.workDateEnd = q.workDate[1];
        q.workDate = null;
      }
      if (q.employWorkDate != null && q.employWorkDate.length > 1) {
        q.employWorkDateStart = q.employWorkDate[0];
        q.employWorkDateEnd = q.employWorkDate[1];
        q.employWorkDate = null;
      }
      if (q.employDeptId != null) {
        q.queryEmployDeptIds = q.employDeptId.join(",");
      }
      if (this.opType === "all") {
        q.opType = null;
      } else q.opType = this.opType;
      q.dispatchDate = this.dispatchDate;
      if (!q.queryEmployStatus) q.queryEmployStatus = "2,91,92,9";
      q.status = this.dispatchStatus === "all" ? null : this.dispatchStatus;
      if (q.status == null && this.over) {
        q.excludeStatus = "2";
      }
      getPage(page.currentPage, page.pageSize, q).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.loadPeronState();
        this.selectionClear();
      });
    },
    loadPeronState() {
      const opType = this.opType === "all" ? null : this.opType;
      let deptCode = opType === "1" ? "011004" : "011005";
      if (this.opType === "all") {
        deptCode = "011004,011005";
      }
      if (this.dispatchDate) {
        getPersonStateSummary(this.dispatchDate, opType, deptCode).then(
          (res) => {
            const summary = res.data.data;
            this.summary.total = summary.total ? summary.total : "--";
            this.summary.assigned = summary.assigned ? summary.assigned : "--";
            this.summary.leave1 = summary.leave1 ? summary.leave1 : "--";
            this.summary.nleave = summary.nleave ? summary.nleave : "--";
            this.summary.hleave = summary.hleave ? summary.hleave : "--";
            this.summary.cleave = summary.cleave ? summary.cleave : "--";
            this.summary.pleave = summary.pleave ? summary.pleave : "--";
            this.summary.gsLeave = summary.gsLeave ? summary.gsLeave : "--";
            this.summary.bleave = summary.bleave ? summary.bleave : "--";
            this.summary.sleave = summary.sleave ? summary.sleave : "--";
            this.summary.jleave = summary.jleave ? summary.jleave : "--";
          }
        );
      } else {
        this.summary = {
          total: "--",
          assigned: "--",
          leave1: "--",
          nleave: "--",
          hleave: "--",
          cleave: "--",
          pleave: "--",
          gsLeave: "--",
          bleave: "--",
          sleave: "--",
          jleave: "--",
        };
      }
    },
    cellStyle({ row, column }) {
      if ("status" === column.columnKey && row.status === 1) {
        return {
          backgroundColor: this.colorName,
          color: "#fff",
        };
      } else if ("status" === column.columnKey && row.status === 2) {
        return {
          backgroundColor: "rgb(255, 153, 0)",
          color: "#fff",
        };
      } else if ("status" === column.columnKey && row.status === 9) {
        return {
          backgroundColor: "#409EFF",
          color: "#fff",
        };
      }
      if ("employUserName" === column.columnKey && row.employStatus === 92) {
        return {
          backgroundColor: "#409EFF",
          color: "#fff",
        };
      }
      if ("employWorkDate" === column.columnKey && row.night) {
        return {
          backgroundColor: "#682593",
          color: "#fff",
        };
      }
      if ("projectSerialNo" === column.columnKey) {
        return {
          cursor: "pointer",
          textDecoration: "underline",
        };
      }
      if ("remark" === column.columnKey && row.id) {
        return {
          cursor: "pointer",
          textDecoration: "underline",
        };
      }
      if ("workDate" === column.columnKey && row.id && row.status === 0) {
        return {
          cursor: "pointer",
          textDecoration: "underline",
        };
      }
    },
    cellClick(row, column, cell, event) {
      if (column.property === "projectSerialNo") {
        this.$refs.employApplyListDialogRef.onShow({
          projectId: row.projectId,
          statuses: "9,91,92",
        });
      }
      if (column.property === "workDate" && row.id && row.status === 0) {
        this.workDate.form.id = row.id;
        this.workDate.form.workDate = row.workDate;
        this.workDate.visible = true;
      }
      if (column.property === "remark") {
        if (!row.id) return;
        this.$prompt("请填写备注", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
        }).then(({ value }) => {
          changeRemark(row.id, value).then(() => {
            this.$message({
              type: "success",
              message: "操作成功!",
            });
            this.onLoad(this.page);
          });
        });
      }
    },
    fillWithHistoricalData() {
      const employIds = this.selectionList.map((item) => item.employId);
      referenceDispatch(this.dispatchDate, employIds.join(",")).then(() => {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!",
        });
      });
    },
    fillWithHistoricalData1() {
      referenceDispatch1(this.dispatchDate, this.ids).then(() => {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!",
        });
      });
    },
  },
};
</script>

<style lang="scss" scoped>
/* 当前激活（选中）的行的背景色 */
/deep/ .el-table .el-table__body tr.current-row {
  background-color: inherit;
}

/* 当前激活（选中）的单元格的边框样式 */
/deep/ .el-table .cell.current-row {
  border: 2px solid #409eff; /* 加粗边框，并设置为需要的颜色 */
}

.dispatch-header {
  display: flex;
  justify-content: space-between;
}

/* 使按钮靠右显示 */
/deep/ .el-tabs__nav {
  width: 100%;
}

/deep/ .el-tabs__item.is-top:last-child {
  float: right;
  padding: 0 10px;
}
</style>

<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
      :cell-style="cellStyle"
      @cell-click="cellClick"
      @sort-change="sortChange"
    >
      <template #batchNo="{ row }">
        <span v-html="highlightDuplicates(row.batchNo)"></span>
      </template>
      <template #menuLeft>
        <el-dropdown v-if="permissionList.addBtn" @command="handleAdd">
          <el-button type="primary" size="mini" icon="el-icon-plus"
            >新 增<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="1">国内发货</el-dropdown-item>
            <el-dropdown-item command="2">国外发货</el-dropdown-item>
            <el-dropdown-item command="3">外库补货</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-button
          type="danger"
          size="mini"
          icon="el-icon-delete"
          plain
          v-if="permissionList.delBtn"
          @click="handleDelete"
          >删 除
        </el-button>
        <el-button
          type="success"
          size="mini"
          plain
          icon="el-icon-s-data"
          @click="handleCount"
          >统计
        </el-button>
        <el-button
          type="danger"
          size="mini"
          icon="el-icon-s-promotion"
          v-if="permissionList.syncBtn"
          @click="handleSync"
          >同步货物流转记录
        </el-button>
      </template>
      <template #menu="{ row, size, index }">
        <el-button
          type="text"
          icon="el-icon-edit-outline"
          :size="size"
          @click="rowBatchNo(row)"
          >批号
        </el-button>
        <el-button
          type="text"
          icon="el-icon-s-check"
          :size="size"
          style="color: #e6a23c"
          v-if="row.status === 1 && permissionList.auditBtn"
          @click="rowAudit(row)"
          >审核出库
        </el-button>
      </template>
    </avue-crud>
    <xiao-shou-ding-dan-select-dialog
      ref="xiaoShouDingDanSelectDialogRef"
      @confirm="handleXiaoShouDingDanSubmit"
    />
    <guo-wai-fa-huo-select-dialog
      ref="guoWaiFaHuoSelectDialogRef"
      @confirm="handleGuoWaiFaHuoSubmit"
    />
    <xiao-shou-wai-ku-bu-huo-select-dialog
      ref="xiaoShouWaiKuBuHuoSelectDialogRef"
      @confirm="handleXiaoShouWaiKuBuHuoSubmit"
    />
    <attach-dialog ref="attachDialogRef" />
    <shipping-batch-no-drawer ref="shippingBatchNoDrawerRef" />
    <shipping-count-dialog ref="outboundCountDialogRef" />
    <HuoWuLiuZhuanMingxiListDialog ref="huoWuLiuZhuanMingxiListDialogRef" />
  </basic-container>
</template>

<script>
import {
  add,
  audit,
  getDetail,
  getList,
  reloadShippingNum,
  remove,
  update,
  syncHuowuliuzhuanjilu,
} from "@/api/ni/fg/shipping";
import { mapGetters } from "vuex";
import XiaoShouDingDanSelectDialog from "@/views/ni/old/components/XiaoShouDingDanSelectDialog.vue";
import GuoWaiFaHuoSelectDialog from "@/views/ni/old/components/GuoWaiFaHuoSelectDialog.vue";
import XiaoShouWaiKuBuHuoSelectDialog from "@/views/ni/old/components/XiaoShouWaiKuBuHuoSelectDialog.vue";
import AttachDialog from "@/components/attach-dialog/index.vue";
import { dateFormat } from "@/util/date";
import { fileLinkByBusinessKeys } from "@/api/resource/attach";
import ShippingBatchNoDrawer from "@/views/ni/fg/components/ShippingBatchNoDrawer.vue";
import ShippingCountDialog from "@/views/ni/fg/components/ShippingCountDialog.vue";
import HuoWuLiuZhuanMingxiListDialog from "@/views/ni/old/components/HuoWuLiuZhuanJiLuListDialog.vue";

export default {
  components: {
    HuoWuLiuZhuanMingxiListDialog,
    ShippingCountDialog,
    ShippingBatchNoDrawer,
    AttachDialog,
    XiaoShouWaiKuBuHuoSelectDialog,
    GuoWaiFaHuoSelectDialog,
    XiaoShouDingDanSelectDialog,
  },
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        showSummary: true,
        sumColumnList: [
          {
            name: "num",
            type: "sum",
          },
          {
            name: "weight",
            type: "sum",
          },
          {
            name: "totalNum",
            type: "sum",
          },
          {
            name: "totalWeight",
            type: "sum",
          },
        ],
        editBtn: false,
        delBtn: false,
        addBtn: false,
        dialogFullscreen: true,
        dialogDrag: true,
        size: "mini",
        searchSize: "mini",
        align: "center",
        searchIndex: 3,
        searchIcon: true,
        searchEnter: true,
        height: "auto",
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        span: 8,
        border: true,
        selection: true,
        menuWidth: 200,
        column: [
          {
            label: "发货编号",
            prop: "serialNo",
            search: true,
            placeholder: " ",
            minWidth: 125,
          },
          {
            label: "发货类型",
            prop: "type",
            searchProp: "typeList",
            type: "select",
            placeholder: " ",
            minWidth: 80,
            multiple: true,
            dicData: [
              {
                label: "国内发货",
                value: "1",
              },
              {
                label: "国外发货",
                value: "2",
              },
              {
                label: "外库补货",
                value: "3",
              },
            ],
            search: true,
          },
          {
            label: "客户名称",
            prop: "sourceText",
            minWidth: 120,
            search: true,
            overHidden: true,
          },
          {
            label: "发货日期",
            prop: "date",
            type: "date",
            placeholder: " ",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            search: true,
            searchRange: true,
            minWidth: 85,
            rules: [
              {
                required: true,
                message: "请选择发货日期",
                trigger: "blur",
              },
            ],
          },
          {
            label: "发货人",
            prop: "opUserId",
            type: "select",
            remote: true,
            dicUrl: `/api/blade-user/search/otj/user?name={{key}}`,
            props: {
              label: "realName",
              value: "id",
            },
            dicFormatter: (data) => {
              return data.data.records;
            },
            placeholder: " ",
            filterable: true,
            overHidden: true,
            search: true,
            hide: true,
            showColumn: false,
            rules: [
              {
                required: true,
                message: "请选择发货人",
                trigger: "blur",
              },
            ],
          },
          {
            label: "发货人",
            prop: "opUserName",
            minWidth: 80,
            overHidden: true,
          },
          {
            label: "订单件数",
            prop: "num",
            minWidth: 70,
          },
          {
            label: "订单重量",
            prop: "weight",
            type: "input",
            minWidth: 70,
          },
          {
            label: "批号",
            prop: "batchNo",
            overHidden: true,
            minWidth: 120,
          },
          {
            label: "发货件数",
            prop: "totalNum",
            minWidth: 70,
          },
          {
            label: "发货重量",
            prop: "totalWeight",
            minWidth: 70,
          },
          {
            label: "质检附件",
            prop: "zjAttach",
            minWidth: 90,
          },
          {
            label: "发货附件",
            prop: "fhAttach",
            minWidth: 90,
          },
          {
            label: "理货单附件",
            prop: "lhdAttach",
            minWidth: 90,
          },
          {
            label: "备注",
            prop: "remark",
            type: "input",
            overHidden: true,
            minWidth: 120,
          },
          {
            label: "状态",
            prop: "status",
            type: "select",
            search: true,
            sortable: true,
            dicData: [
              {
                label: "未审核",
                value: 1,
              },
              {
                label: "已出库",
                value: 2,
              },
            ],
            overHidden: true,
            minWidth: 75,
          },
        ],
      },
      data: [],
      batchNoColorMap: {},
      colorPalette: [
        "#F56C6C",
        "#E6A23C",
        "#67C23A",
        "#409EFF",
        "#b37feb",
        "#ff85c0",
      ],
    };
  },
  computed: {
    ...mapGetters(["permission", "colorName"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.shipping_add, false),
        viewBtn: this.vaildData(this.permission.shipping_view, false),
        delBtn: this.vaildData(this.permission.shipping_delete, false),
        editBtn: this.vaildData(this.permission.shipping_edit, false),
        auditBtn: this.vaildData(this.permission.shipping_audit, false),
        syncBtn: this.vaildData(this.permission.shipping_sync, false),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  methods: {
    generateDistinctColor(index) {
      const hue = (index * 137.508) % 360;
      const saturation = "75%";
      const lightness = "50%";
      return `hsl(${hue}, ${saturation}, ${lightness})`;
    },
    processBatchNoDuplicates() {
      const allBatchNosOnPage = new Map();
      // ... (收集批号的逻辑不变) ...
      this.data.forEach((row) => {
        if (row.batchNo) {
          row.batchNo.split("/").forEach((bn) => {
            if (bn) {
              allBatchNosOnPage.set(bn, (allBatchNosOnPage.get(bn) || 0) + 1);
            }
          });
        }
      });

      const duplicateBatchNos = Array.from(allBatchNosOnPage.entries())
        .filter(([key, value]) => value > 1)
        .map(([key, value]) => key);

      const newColorMap = {};
      const paletteSize = this.colorPalette.length;

      duplicateBatchNos.forEach((bn, index) => {
        // *** 改动在这里：实现混合逻辑 ***
        if (index < paletteSize) {
          // 优先使用预定义颜色
          newColorMap[bn] = this.colorPalette[index];
        } else {
          // 预定义颜色用完后，动态生成新颜色
          // 传递 index - paletteSize 以确保生成的颜色序列从头开始，避免与预定义颜色在色相上过于接近
          newColorMap[bn] = this.generateDistinctColor(index - paletteSize);
        }
      });

      this.batchNoColorMap = newColorMap;
    },
    //用于生成带高亮样式的 HTML 字符串
    highlightDuplicates(batchNoString) {
      // 如果批号字符串为空，或当前页没有重复项，则直接返回原字符串
      if (!batchNoString || Object.keys(this.batchNoColorMap).length === 0) {
        return batchNoString;
      }

      // 分割批号，并为重复项包裹上带样式的 HTML 标签
      return batchNoString
        .split("/")
        .map((bn) => {
          const color = this.batchNoColorMap[bn];
          if (color) {
            // 如果是重复批号，用 strong 标签加粗并应用颜色
            return `<strong style="color: ${color};">${bn}</strong>`;
          }
          return bn;
        })
        .join("/"); // 最后再用 "/" 拼接回来
    },
    handleSync() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      const unAudit = this.selectionList.some((row) => row.status === 1);
      if (unAudit) {
        this.$message.warning("请选择已审核的数据");
        return;
      }
      //国外
      const foreign = this.selectionList.some((row) => row.type === "2");
      if (foreign) {
        this.$message.warning("国外数据无需同步货物流转记录");
        return;
      }
      this.$confirm("确定将选择数据同步到旧ni的货物流转记录中?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return syncHuowuliuzhuanjilu(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    handleCount() {
      if (this.selectionList.length === 0) {
        const q = {
          ...this.query,
        };
        if (q.date != null && q.date.length === 2) {
          q.startDate = q.date[0];
          q.endDate = q.date[1];
          q.date = null;
        }
        this.$message.info({
          message: "当前统计范围：查询结果",
          duration: 2000,
          iconClass: "el-icon-search",
        });
        this.$refs.outboundCountDialogRef.onShow(q);
      } else {
        this.$message.info({
          message: `当前统计范围：选中${this.selectionList.length}条数据`,
          duration: 2000,
          iconClass: "el-icon-s-check",
        });
        this.$refs.outboundCountDialogRef.onShow({ ids: this.ids });
      }
    },
    handleXiaoShouWaiKuBuHuoSubmit(selectionList) {
      add({
        sourceId: selectionList[0].id,
        type: "3",
        date: dateFormat(new Date(), "yyyy-MM-dd"),
        weight: selectionList[0].jingZhongKg,
      }).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          window.console.log(error);
        }
      );
    },
    handleGuoWaiFaHuoSubmit(selectionList) {
      add({
        sourceId: selectionList[0].id,
        type: "2",
        date: dateFormat(new Date(), "yyyy-MM-dd"),
        weight: selectionList[0].jingZhong,
      }).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          window.console.log(error);
        }
      );
    },
    handleXiaoShouDingDanSubmit(selectionList) {
      add({
        sourceId: selectionList[0].id,
        type: "1",
        date: dateFormat(new Date(), "yyyy-MM-dd"),
      }).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          window.console.log(error);
        }
      );
    },
    async rowAudit(row) {
      const res = await getDetail(row.id);
      const data = res.data.data;
      if (data.status !== 1) {
        this.$message({
          type: "error",
          message: "该发货单已审核，请重新选择!",
        });
        return;
      }
      if (!data.items || data.items.length === 0) {
        this.$message({
          type: "error",
          message: "该发货单没有登记批号，请登记后再审核!",
        });
        return;
      }
      let msg = "确定生成出库单?";
      if (Number(data.num) !== Number(data.totalNum)) {
        msg = `该发货单<span style="color: #F56C6C;font-weight: bold">登记的件数和发货数不同</span>，是否继续?`;
      }
      this.$confirm(msg, {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        dangerouslyUseHTMLString: true,
        type: "warning",
      })
        .then(() => {
          return audit(row.id);
        })
        .then((res) => {
          const data = res.data.data;
          const serialNos = data.map((item) => item.serialNo).join("/");
          this.$alert(
            `生成出库单:<span style="color: ${this.colorName};font-weight: bold">${serialNos}</span>`,
            "审核成功",
            {
              confirmButtonText: "确定",
              dangerouslyUseHTMLString: true,
              callback: (action) => {
                this.onLoad(this.page);
                this.$refs.crud.toggleSelection();
              },
            }
          );
        });
    },
    rowBatchNo(row) {
      getDetail(row.id).then((res) => {
        const data = res.data.data;
        if (data.status === 1) this.$refs.shippingBatchNoDrawerRef.onEdit(data);
        else this.$refs.shippingBatchNoDrawerRef.onShow(data);
      });
    },
    handleAdd(command) {
      console.log(command);
      if (command === "1") {
        this.$refs.xiaoShouDingDanSelectDialogRef.onSelect(
          { descs: "id" },
          "/api/ni/old/xiaoShouDingDan/shippingList"
        );
      } else if (command === "2") {
        this.$refs.guoWaiFaHuoSelectDialogRef.onSelect({ descs: "id" });
      } else if (command === "3") {
        this.$refs.xiaoShouWaiKuBuHuoSelectDialogRef.onSelect(
          { descs: "id" },
          "/api/ni/old/xiaoShouWaiKuBuHuo/shippingList"
        );
      }
    },
    rowSave(row, done, loading) {
      add(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          window.console.log(error);
        }
      );
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          console.log(error);
        }
      );
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen(done, type) {
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then((res) => {
          this.form = res.data.data;
        });
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      params.descs = "id";
      const q = Object.assign(params, this.query);
      if (q.date != null && q.date.length === 2) {
        q.startDate = q.date[0];
        q.endDate = q.date[1];
        q.date = null;
      }
      getList(page.currentPage, page.pageSize, q).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        data.records.forEach((ele) => {
          if (ele.num == null) ele.num = 0;
          if (ele.weight == null) ele.weight = 0;
          ele.zjAttach = 0;
          ele.fhAttach = 0;
          if (ele.type === "2") ele.lhdAttach = 0;
        });
        this.data = data.records;
        this.processBatchNoDuplicates();
        if (this.data.length > 0) {
          this.reloadShipping();
          let serialNos = data.records.map((item) => item.serialNo);
          this.loadZjAttachNum(serialNos.join(","));
          this.loadFhAttachNum(serialNos.join(","));
          serialNos = data.records
            .filter((item) => item.type === "2")
            .map((item) => item.serialNo);
          if (serialNos.length > 0) this.loadLhdAttachNum(serialNos.join(","));
        }
        this.loading = false;
        this.selectionClear();
      });
    },
    reloadShipping() {
      const ids = this.data.map((item) => item.id);
      reloadShippingNum(ids.join(",")).then((res) => {
        const { data } = res.data;
        const numMap = new Map();
        data.forEach((item) => {
          numMap.set(item.serialNo, item);
        });
        this.data.forEach((item) => {
          const updateData = numMap.get(item.serialNo);
          if (
            updateData &&
            (item.num !== updateData.num || item.weight !== updateData.weight)
          ) {
            item.num = updateData.num;
            item.weight = updateData.weight;
          }
        });
      });
    },
    loadLhdAttachNum(serialNos) {
      fileLinkByBusinessKeys(serialNos, "ni_fg_shipping_lhd").then((res) => {
        const data = res.data.data;
        const attachCountMap = data.reduce((acc, item) => {
          const key = item.businessKey;
          acc[key] = (acc[key] || 0) + 1;
          return acc;
        }, {});
        this.data.forEach((item) => {
          if (item.type === "2")
            item.lhdAttach = attachCountMap[item.serialNo] || 0;
        });
      });
    },
    loadZjAttachNum(serialNos) {
      fileLinkByBusinessKeys(serialNos, "ni_fg_shipping_zj").then((res) => {
        const data = res.data.data;
        const attachCountMap = data.reduce((acc, item) => {
          const key = item.businessKey;
          acc[key] = (acc[key] || 0) + 1;
          return acc;
        }, {});
        this.data.forEach((item) => {
          item.zjAttach = attachCountMap[item.serialNo] || 0;
        });
      });
    },
    loadFhAttachNum(serialNos) {
      fileLinkByBusinessKeys(serialNos, "ni_fg_shipping_fh").then((res) => {
        const data = res.data.data;
        const attachCountMap = data.reduce((acc, item) => {
          const key = item.businessKey;
          acc[key] = (acc[key] || 0) + 1;
          return acc;
        }, {});
        this.data.forEach((item) => {
          item.fhAttach = attachCountMap[item.serialNo] || 0;
        });
      });
    },
    cellStyle({ row, column }) {
      if ("status" === column.columnKey && row.status === 1) {
        return {
          backgroundColor: "#909399",
          color: "#fff",
        };
      } else if ("status" === column.columnKey && row.status === 2) {
        return {
          backgroundColor: "#409EFF",
          color: "#fff",
        };
      }
      if (
        "totalNum" === column.columnKey &&
        Number(row.totalNum) !== Number(row.num)
      ) {
        return {
          color: "#F56C6C",
          fontWeight: "bold",
        };
      } else if (
        "totalNum" === column.columnKey &&
        Number(row.totalNum) === Number(row.num)
      ) {
        return {
          backgroundColor: "#67C23A",
          color: "#fff",
        };
      }
      if (
        "totalWeight" === column.columnKey &&
        Number(row.totalWeight) !== Number(row.weight)
      ) {
        return {
          color: "#F56C6C",
          fontWeight: "bold",
        };
      } else if (
        "totalWeight" === column.columnKey &&
        Number(row.totalWeight) === Number(row.weight)
      ) {
        return {
          backgroundColor: "#67C23A",
          color: "#fff",
        };
      }
      if ("type" === column.columnKey && row.type === "1") {
        return {
          backgroundColor: "#F56C6C",
          color: "#fff",
        };
      } else if ("type" === column.columnKey && row.type === "2") {
        return {
          backgroundColor: "#67C23A",
          color: "#fff",
        };
      } else if ("type" === column.columnKey && row.type === "3") {
        return {
          backgroundColor: "#409EFF",
          color: "#fff",
        };
      }
      if ("serialNo" === column.columnKey && ["1", "3"].includes(row.type)) {
        return {
          textDecoration: "underline",
          cursor: "pointer",
        };
      }
      if (
        "zjAttach" === column.columnKey ||
        "fhAttach" === column.columnKey ||
        ("lhdAttach" === column.columnKey && row.type === "2")
      ) {
        return {
          textDecoration: "underline",
          cursor: "pointer",
        };
      }
    },
    cellClick(row, column) {
      if (column.property === "serialNo" && ["1", "3"].includes(row.type)) {
        this.$refs.huoWuLiuZhuanMingxiListDialogRef.onShow({
          shippingNo: row.serialNo,
        });
      }
      if (column.property === "zjAttach") {
        this.rowAttach(row.serialNo, "ni_fg_shipping_zj");
      }
      if (column.property === "fhAttach") {
        this.rowAttach(row.serialNo, "ni_fg_shipping_fh");
      }
      if (column.property === "lhdAttach" && row.type === "2") {
        this.rowAttach(row.serialNo, "ni_fg_shipping_lhd");
      }
    },
    rowAttach(businessKey, businessName) {
      return this.$refs.attachDialogRef.init(businessKey, businessName);
    },
    sortChange({ prop, order }) {
      if (order === "ascending") {
        this.query.ascs = prop;
        this.query.descs = null;
      } else if (order === "descending") {
        this.query.descs = prop;
        this.query.ascs = null;
      } else {
        this.query.descs = null;
        this.query.ascs = null;
      }
      this.onLoad(this.page);
    },
  },
};
</script>

<style></style>

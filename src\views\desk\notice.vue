<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      ref="crud"
      @row-del="rowDel"
      v-model="form"
      :permission="permissionList"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :before-open="beforeOpen"
      :upload-preview="handleUploadPreview"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template slot-scope="{ row, label, size }" slot="status">
        <el-tag :size="size" :type="rowTagType(row.status)">{{ label }}</el-tag>
      </template>
      <template slot-scope="{ row, size, label }" slot="category">
        <el-tag :size="size" :type="rowTagType(row.category)">{{
          label
        }}</el-tag>
      </template>
      <template slot-scope="{ row, size, label }" slot="releaseTarget">
        <el-tag :size="size" :type="rowTagType(row.releaseTarget)">{{
          label
        }}</el-tag>
      </template>
      <template slot-scope="{ size, type }" slot="userChooseForm">
        <el-button
          icon="el-icon-search"
          type="warning"
          plain
          :size="size"
          v-if="['edit', 'add'].includes(type)"
          @click="handleUserChoose"
        >
          选择
        </el-button>
        <el-button
          icon="el-icon-search"
          type="success"
          plain
          :size="size"
          v-if="['edit', 'add'].includes(type)"
          @click="handleUserChooseClear"
        >
          清空用户
        </el-button>
      </template>
      <template slot-scope="{ size }" slot="menuLeft">
        <!--        <el-button type="danger"-->
        <!--                   size="mini"-->
        <!--                   icon="el-icon-delete"-->
        <!--                   plain-->
        <!--                   v-if="permission.notice_delete"-->
        <!--                   @click="handleDelete">删 除-->
        <!--        </el-button>-->
      </template>
      <template slot-scope="{ row, index, size }" slot="menu">
        <el-button
          type="text"
          icon="el-icon-edit"
          :size="size"
          v-if="permission.notice_edit && [0, 2].includes(row.status)"
          @click="$refs.crud.rowEdit(row, index)"
          >编 辑
        </el-button>
        <el-button
          type="text"
          icon="el-icon-s-promotion"
          :size="size"
          v-if="[0, 2].includes(row.status)"
          @click="rowPublish(row, index)"
          >发 布
        </el-button>
        <el-button
          type="text"
          icon="el-icon-refresh-left"
          :size="size"
          v-if="permission.notice_delete && [1].includes(row.status)"
          @click="rowWithdraw(row, index)"
          >撤 销
        </el-button>
        <el-button
          type="text"
          :size="size"
          icon="el-icon-delete"
          v-if="permission.notice_delete && [0, 2].includes(row.status)"
          @click="$refs.crud.rowDel(row, index)"
          >删 除
        </el-button>
        <el-button
          type="text"
          icon="el-icon-download"
          :size="size"
          v-if="permission.notice_edit && [1].includes(row.status)"
          @click="handleAttach(row)"
          >附件管理
        </el-button>
      </template>
      <template slot-scope="{ size, type }" slot="menuForm">
        <el-button
          type="primary"
          icon="el-icon-s-promotion"
          :size="size"
          v-if="['edit', 'add'].includes(type)"
          @click="handleSubmit(type)"
          >发 布
        </el-button>
      </template>
    </avue-crud>
    <attach-dialog ref="attachDialogRef" code="private" />
    <!-- 人员选择弹窗 -->
    <wf-user-select
      ref="user-select"
      :check-type="checkType"
      :default-checked="defaultChecked"
      @onConfirm="handleReceiveUsers"
    ></wf-user-select>
  </basic-container>
</template>

<script>
import {
  getList,
  remove,
  update,
  add,
  getNotice,
  getUserMessage,
} from "@/api/desk/notice";
import { mapGetters } from "vuex";
import WfUserSelect from "@/views/plugin/workflow/process/components/user-select";
import { dateFormat } from "@/util/date";
import { tagType } from "@/api/ni/pa/pa-common";
import AttachDialog from "@/components/attach-dialog";
import { getDetail as getAttachDetail } from "@/api/resource/attach";
import { handleUploadPreview } from "@/util/util";

export default {
  components: {
    WfUserSelect,
    AttachDialog,
  },
  data() {
    return {
      checkType: "checkbox",
      defaultChecked: "",
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        size: "mini",
        searchSize: "mini",
        searchIcon: true, // 搜索栏能否收缩
        searchIndex: 2, // 搜索按钮索引,超出的搜索栏收缩
        height: "auto",
        calcHeight: 30,
        dialogWidth: 950,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        selection: true,
        excelBtn: true,
        dialogClickModal: false,
        // 操作栏
        menu: true,
        // menuWidth: 300,
        // menuFixed: false, // 取消冻结
        menuAlign: "center",
        addBtn: true,
        editBtn: false,
        viewBtn: true,
        // copyBtn: true,
        delBtn: false,
        column: [
          {
            label: "状态",
            prop: "status",
            type: "select",
            dicData: [
              {
                label: "待发布",
                value: 0,
              },
              {
                label: "已发布",
                value: 1,
              },
              {
                label: "撤销",
                value: 2,
              },
            ],
            search: true,
            dataType: "number",
            display: false,
          },
          {
            label: "创建人",
            prop: "createUser",
            type: "tree",
            dicUrl: `/api/blade-user/user-list`,
            props: {
              label: "name",
              value: "id",
            },
            search: true,
            addDisplay: false,
            editDisplay: false,
            viewDisplay: true,
          },
          {
            label: "通知日期",
            prop: "releaseTime",
            type: "datetime",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
            search: true,
            searchRange: true,
            rules: [
              {
                required: true,
                message: "请输入通知日期",
                trigger: "blur",
              },
            ],
            addDisplay: false,
            editDisplay: false,
            viewDisplay: true,
          },
          {
            label: "通知标题",
            prop: "title",
            span: 24,
            row: true,
            search: true,
            maxlength: 20,
            rules: [
              {
                required: true,
                message: "请输入通知标题",
                trigger: "blur",
              },
            ],
          },
          {
            label: "通知编码",
            placeholder: "请选择中文编码",
            prop: "noticeChineseCode",
            type: "select",
            span: 8,
            dicUrl:
              "/api/blade-system/dict/dictionary?code=notice_chinese_code",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
          },
          {
            labelWidth: 0,
            placeholder: "请输入数字编码",
            prop: "noticeNumberCode",
            type: "input",
            hide: true,
            span: 16,
          },
          {
            label: "是否需要回复",
            prop: "isManagerResponse",
            type: "radio",
            dicData: [
              {
                label: "否",
                value: 0,
              },
              {
                label: "是",
                value: 1,
              },
            ],
            span: 12,
            rules: [
              {
                required: true,
                message: "请选择是否回复",
                trigger: "blur",
              },
            ],
          },
          {
            label: "通知对象",
            prop: "releaseTarget",
            type: "radio",
            dicData: [
              {
                label: "全体对象",
                value: 0,
              },
              {
                label: "指定对象",
                value: 1,
              },
            ],
            value: 0,
            search: true,
            span: 24,
            rules: [
              {
                required: true,
                message: "请选择通知对象",
                trigger: "blur",
              },
            ],
            control: (val) => {
              if (val === 0) {
                return {
                  $receiveUsers: {
                    addDisplay: false,
                    editDisplay: false,
                    viewDisplay: false,
                  },
                  userChoose: {
                    addDisplay: false,
                    editDisplay: false,
                    viewDisplay: false,
                  },
                };
              } else if (val === 1) {
                return {
                  $receiveUsers: {
                    addDisplay: true,
                    editDisplay: true,
                    viewDisplay: true,
                    rules: [
                      {
                        required: true,
                        message: "请选择指定用户",
                        trigger: "blur",
                      },
                    ],
                  },
                  userChoose: {
                    addDisplay: true,
                    editDisplay: true,
                    viewDisplay: false,
                  },
                };
              }
            },
          },
          {
            label: "指定用户",
            prop: "$receiveUsers",
            span: 24,
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            type: "tree",
            dicUrl: `/api/blade-user/user-list`,
            props: {
              label: "name",
              value: "id",
            },
            multiple: true,
            disabled: true,
            hide: true,
          },
          {
            label: "",
            prop: "userChoose",
            formslot: true,
            span: 12,
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "通知内容",
            prop: "content",
            component: "AvueUeditor",
            options: {
              action: "/api/blade-resource/oss/endpoint/put-file",
              props: {
                res: "data",
                url: "link",
              },
            },
            hide: true,
            minRows: 6,
            span: 24,
          },
          {
            label: "附件",
            type: "upload",
            propsHttp: {
              res: "data",
              url: "attachId",
              name: "originalName",
            },
            action: "/api/blade-resource/oss/endpoint/put-file-attach",
            addDisplay: true,
            editDisplay: true,
            viewDisplay: false,
            hide: true,
            showColumn: false,
            span: 24,
            showFileList: true,
            multiple: true,
            limit: 10,
            prop: "attachment",
          },
          {
            label: "未读",
            prop: "unreadUsers",
            type: "textarea",
            span: 24,
            addDisplay: false,
            editDisplay: false,
            viewDisplay: true,
            hide: true,
          },
          {
            label: "已读",
            prop: "readUsers",
            type: "textarea",
            span: 24,
            addDisplay: false,
            editDisplay: false,
            viewDisplay: true,
            hide: true,
          },
          {
            label: "回复意见",
            hide: true,
            showColumn: false,
            labelPosition: "top",
            prop: "comments",
            span: 24,
            type: "dynamic",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: true,
            children: {
              align: "center",
              headerAlign: "center",
              index: false,
              column: [
                {
                  label: "回复人",
                  prop: "receiveUser",
                },
                {
                  label: "部门意见",
                  prop: "comment",
                  type: "textarea",
                },
              ],
            },
          },
        ],
      },
      data: [],
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.notice_add, false),
        viewBtn: this.vaildData(this.permission.notice_view, false),
        delBtn: this.vaildData(this.permission.notice_delete, false),
        editBtn: this.vaildData(this.permission.notice_edit, false),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  methods: {
    handleUploadPreview,
    rowSave(row, done, loading) {
      add(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          window.console.log(error);
          loading();
        }
      );
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          window.console.log(error);
          loading();
        }
      );
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen(done, type) {
      if ("add" == type) {
        this.form.status = 0;
        this.defaultChecked = "";
      }
      if (["edit", "view"].includes(type)) {
        getNotice(this.form.id).then((res) => {
          this.form = res.data.data;
          const receivers = this.form.receiveUsers;
          this.form.$receiveUsers = receivers;
          this.defaultChecked = receivers;
        });
      }
      if ("view" === type) {
        if (this.form.status === 1) {
          getUserMessage(this.form.id)
            .then((res) => {
              const users = res.data.data;
              if (users) {
                let arr = users.split("{^sp$}");
                this.form.readUsers = arr[0];
                this.form.unreadUsers = arr[1];
                let com = arr[2].split("{^com$}");
                let rows = new Array();
                com.forEach((o) => {
                  let name = o.split("{^name$}");
                  rows.push({ receiveUser: name[0], comment: name[1] });
                });
                this.form.comments = rows;
              }
            })
            .catch(() => {});
          this.setReadVisible(true, this.form.isManagerResponse === 1);
        } else {
          this.setReadVisible(false, false);
        }
      }
      done();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      const query = Object.assign(params, this.query);
      if (query.releaseTime && query.releaseTime.length == 2) {
        query.releaseTime_datege = query.releaseTime[0];
        query.releaseTime_datelt = query.releaseTime[1];
        query.releaseTime = null;
      }
      this.loading = true;
      getList(page.currentPage, page.pageSize, query).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
    handleUserChoose() {
      this.$refs["user-select"].visible = true;
    },
    handleUserChooseClear() {
      this.form.receiveUsers = undefined;
      this.form.$receiveUsers = [];
    },
    handleReceiveUsers(id, name) {
      // this.form.receiveUsers = id;
      // this.form.$receiveUsers = name;
      let beforeUsers = this.form.receiveUsers;

      let selectUserId;

      if (beforeUsers) {
        selectUserId = beforeUsers + "," + id;
      } else {
        selectUserId = id;
      }

      let ids = selectUserId.split(",");

      const uniqueArray = [...new Set(ids)];

      this.form.receiveUsers = uniqueArray.join(",");

      this.form.$receiveUsers = uniqueArray;
    },
    handleSubmit(type) {
      this.$refs.crud.validate((valid, done) => {
        if (valid) {
          this.$confirm("此操作将发布该公告，是否继续?", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
            center: true,
          }).then(
            () => {
              this.form.status = 1;
              this.form.releaseTime = dateFormat(new Date());
              if (type === "add") {
                this.$refs.crud.rowSave();
              } else if (type === "edit") {
                this.$refs.crud.rowUpdate();
              }
            },
            (error) => {
              if (error === "cancel") {
                this.$message({
                  type: "success",
                  message: "取消成功",
                });
              }
            }
          );
        }
        done();
      });
    },
    rowPublish(row, index) {
      let form = { ...row };
      form.status = 1;
      form.releaseTime = dateFormat(new Date());
      this.rowUpdate(form, index);
    },
    rowWithdraw(row, index) {
      let form = { ...row };
      form.status = 2;
      this.rowUpdate(form, index);
    },
    handleAttach(row) {
      return this.$refs.attachDialogRef.init(row.id, "blade_desk_notice");
    },
    setReadVisible(flag, isNeedComment) {
      const unreadUsers = this.findObject(this.option.column, "unreadUsers");
      unreadUsers.viewDisplay = flag;
      const readUsers = this.findObject(this.option.column, "readUsers");
      readUsers.viewDisplay = flag;
      const comments = this.findObject(this.option.column, "comments");
      comments.viewDisplay = isNeedComment;
    },
    uploadPreview(file, column, done) {
      getAttachDetail(file.url).then((res) => {
        const { link } = res.data.data;
        window.open(link);
      });
    },
    rowTagType(status) {
      return tagType(status);
    },
  },
};
</script>

<style></style>

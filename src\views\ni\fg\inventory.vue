<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page.sync="page"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @refresh-change="refreshChange"
               :cell-style="cellStyle"
               @cell-click="cellClick"
               @on-load="onLoad">
      <template #menuLeft>
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          v-if="permission.ni_fg_inventory_out"
          @click="handleOutbound">出 库
        </el-button>
        <el-button
          type="warning"
          icon="el-icon-shopping-cart-full"
          size="mini"
          v-if="permission.ni_fg_inventory_restocking"
          @click="handleRestocking">倒 箱
        </el-button>
        <!--导出-->
        <el-button
          type="info"
          icon="el-icon-download"
          size="mini"
          v-if="permission.ni_fg_inventory_export"
          @click="handleExport">导 出
        </el-button>
      </template>
    </avue-crud>
    <outbound-form-dialog ref="outboundFormDialogRef" @confirm="onLoad(page)"/>
    <restocking-form-dialog ref="restockingFormDialogRef" @confirm="handleRestockingConfirm"/>
  </basic-container>
</template>

<script>
import {changeRemark, getDetail, getList} from "@/api/ni/fg/fgInventory";
import {mapGetters} from "vuex";
import OutboundFormDialog from "@/views/ni/fg/components/OutboundFormDialog.vue";
import RestockingFormDialog from "@/views/ni/fg/components/RestockingFormDialog.vue";

export default {
  components: {OutboundFormDialog, RestockingFormDialog},
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      selectionList: [],
      option: {
        showSummary: true,
        sumColumnList: [
          {
            name: 'num',
            type: 'sum',
          },
          {
            name: 'weight',
            type: 'sum',
          }
        ],
        menu: false,
        menuWidth: 150,
        addBtn: false,
        delBtn: false,
        editBtn: false,
        size: "mini",
        searchSize: "mini",
        align: "center",
        searchIndex: 3,
        searchIcon: true,
        searchEnter: true,
        height: 'auto',
        calcHeight: 30,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        selection: true,
        dialogClickModal: false,
        reserveSelection: true,
        column: [
          {
            label: "仓库",
            prop: "depotId",
            type: "select",
            dicUrl: "/api/ni/base/depot/info/list?status=2&type=1",
            props: {
              label: "name",
              value: "id",
              desc: "code",
            },
            search: true,
            minWidth: 100,
            overHidden: true
          },
          {
            label: '品名',
            prop: "skuText",
            placeholder: " ",
            overHidden: true,
            minWidth: 150,
            search: true,
          },
          {
            label: '规格',
            prop: 'specText',
            placeholder: " ",
            display: false,
            overHidden: true,
            width: 150,
            search: true
          },
          {
            label: '质量',
            prop: 'qualityLevel',
            type: 'select',
            dicUrl: "/api/blade-system/dict-biz/dictionary?code=ni_product_sku_quality_level",
            props: {
              label: "dictValue",
              value: "dictKey",
              desc: 'dictKey'
            },
            placeholder: " ",
            width: 100,
          },
          {
            label: '外包装',
            prop: 'packageText',
            placeholder: " ",
            display: false,
            overHidden: true,
            width: 115,
            search: true
          },
          {
            label: '内包装',
            prop: 'innerPackageText',
            placeholder: " ",
            display: false,
            overHidden: true,
            width: 115,
            search: true
          },
          {
            label: '唛头',
            prop: 'currentMarkText',
            placeholder: " ",
            overHidden: true,
            hide: true,
            width: 110
          },
          {
            label: '批号',
            prop: 'batchNo',
            placeholder: " ",
            minWidth: 110,
            overHidden: true,
            search: true
          },
          {
            label: '数量',
            prop: 'num',
            placeholder: " ",
            type: 'number',
            minWidth: 80,
          },
          {
            label: '在途数量',
            prop: 'inTransitQuantity',
            placeholder: " ",
            type: 'number',
            minWidth: 80,
          },
          {
            label: '重量',
            prop: 'weight',
            placeholder: " ",
            type: 'number',
            minWidth: 80,
          },
          {
            label: "单位",
            prop: "unit",
            type: "select",
            filterable: true,
            width: 80,
            dicUrl: "/api/blade-system/dict-biz/dictionary?code=ni_material_unit",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            slot: true,
            placeholder: " ",
          },
          {
            label: '存货编码',
            prop: 'materialCode',
            placeholder: " ",
            width: 110,
            hide: true,
            overHidden: true,
            search: true
          },
          {
            label: "生产日期",
            prop: "productionDate",
            type: "date",
            placeholder: " ",
            minWidth: 100,
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            search: true,
            searchRange: true
          },
          {
            label: "备注",
            prop: "remark",
            type: "textarea",
            minWidth: 100,
            span: 24,
            search: true,
          },
          {
            label: "状态",
            prop: "status",
            type: 'select',
            dicUrl: "/api/blade-system/dict-biz/dictionary?code=ni_fg_inventory_status",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            dataType: "number",
            minWidth: 100,
            search: true,
          },
        ]
      },
      data: [],
      exportColumn: [
        {
          label: "仓库",
          prop: "depotName",
        },
        {
          label: "库位",
          prop: "location",
        },
        {
          label: "品名",
          prop: "skuText",
        },
        {
          label: "规格",
          prop: "specText",
        },
        {
          label: "质量",
          prop: "qualityLevel",
        },
        {
          label: "外包装",
          prop: "packageText",
        },
        {
          label: "内包装",
          prop: "innerPackageText",
        },
        {
          label: "唛头",
          prop: "currentMarkText",
        },
        {
          label: "批号",
          prop: "batchNo",
        },
        {
          label: "数量",
          prop: "num",
        },
        {
          label: "单位",
          prop: "unit",
        },
        {
          label: "生产日期",
          prop: "productionDate",
        },
        {
          label: "备注",
          prop: "remark",
        },
        {
          label: "状态",
          prop: "status",
        },
        {
          label: "存货编码",
          prop: "materialCode",
        },
      ],
      depotKeyValue: {},
      statusDictKeyValue: {},
      unitKeyValue: {},
      qualityLevelDictKeyValue: {},
      qualityLevelColorMap: {
        'A': '#67C23A', // 高吸附 - 绿色
        'P': '#409EFF', // 优等品 - 蓝色
        'Q': '#E6A23C', // 合格品 - 橙色
      },
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    ids() {
      let ids = [];
      this.selectionList.forEach(ele => {
        ids.push(ele.id);
      });
      return ids.join(",");
    }
  },
  created() {
    this.dictInit()
  },
  methods: {
    dictInit() {
      this.$http.get("/api/ni/base/depot/info/list?status=2&type=1")
        .then((res) => {
          this.depotKeyValue = res.data.data.reduce((acc, cur) => {
            acc[cur.id] = cur.name;
            return acc;
          }, {});
        });
      this.$http
        .get("/api/blade-system/dict-biz/dictionary?code=ni_product_sku_quality_level")
        .then((res) => {
          const dict = res.data.data;
          this.qualityLevelDictKeyValue = dict.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });
      this.$http.get("/api/blade-system/dict-biz/dictionary?code=ni_fg_inventory_status")
        .then((res) => {
          this.statusDictKeyValue = res.data.data.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });
      this.$http.get("/api/blade-system/dict-biz/dictionary?code=ni_material_unit")
        .then((res) => {
          this.unitKeyValue = res.data.data.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });
    },
    handleExport() {
      let msg = "是否导出当前选取的数据？"
      if (this.selectionList.length === 0) {
        msg = "是否导出全部数据？"
      }
      this.$confirm(msg, {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        let data = this.selectionList
        if (this.selectionList.length === 0) {
          const res = await getList(1, 100000, this.query);
          data = res.data.data.records
        }
        this.$Export.excel({
          title: "当前库存",
          columns: this.exportColumn,
          data: data.map((item) => {
            return {
              ...item,
              depotName: this.depotKeyValue[item.depotId],
              status: this.statusDictKeyValue[item.status],
              unit: this.unitKeyValue[item.unit],
              qualityLevel: this.qualityLevelDictKeyValue[item.qualityLevel]
            }
          })
        })
      });
    },
    handleRestocking() {
      let depotId;
      if (this.selectionList && this.selectionList.length > 0) {
        const depotIds = new Set()
        this.selectionList.forEach((item) => {
          depotIds.add(item.deptId)
        })
        if (depotIds.size > 1) {
          this.$message({
            type: "warning",
            message: "请选择同一仓库的数据进行出库!"
          })
          return
        }
        const freeze = this.selectionList.some((item) => item.status === 2)
        if (freeze) {
          this.$message({
            type: "warning",
            message: "请选择非冻结的数据进行出库!"
          })
          return
        }
        depotId = this.selectionList[0].depotId
      }
      this.$refs.restockingFormDialogRef.onAdd(depotId, this.selectionList)
    },
    handleRestockingConfirm() {
      this.onLoad(this.page)
      const path = "/ni/fg/inventory"
      if (path) this.$router.push(path);
    },
    handleOutbound() {
      let depotId;
      if (this.selectionList && this.selectionList.length > 0) {
        const depotIds = new Set()
        this.selectionList.forEach((item) => {
          depotIds.add(item.deptId)
        })
        if (depotIds.size > 1) {
          this.$message({
            type: "warning",
            message: "请选择同一仓库的数据进行出库!"
          })
          return
        }
        const freeze = this.selectionList.some((item) => item.status === 2)
        if (freeze) {
          this.$message({
            type: "warning",
            message: "请选择非冻结的数据进行出库!"
          })
          return
        }
        depotId = this.selectionList[0].depotId
      }
      this.$refs.outboundFormDialogRef.onAdd(depotId, this.selectionList)
    },
    beforeOpen(done, type) {
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then(res => {
          this.form = res.data.data;
        });
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      const q = {
        ...params,
        ...this.query
      }
      if (q.productionDate && q.productionDate.length === 2) {
        q.startProductionDate = q.productionDate
        q.endProductionDate = q.productionDate
        q.productionDate = null
      }
      q.descs = "id"
      getList(page.currentPage, page.pageSize, q).then(res => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
      });
    },
    cellStyle({row, column}) {
      if ("status" === column.columnKey && row.status === 2) {
        return {
          backgroundColor: '#409EFF',
          color: "#fff",
        }
      }
      if ("qualityLevel" === column.columnKey) {
        const color = this.qualityLevelColorMap[row.qualityLevel];
        return {
          backgroundColor: color || "#909399", // 默认颜色为灰色
          color: "#fff",
        }
      }
      if ("remark" === column.columnKey && this.permission.fgInventory_change_remark) {
        return {
          textDecoration: 'underline',
          cursor: 'pointer',
          color: '#909399'
        }
      }
    },
    cellClick(row, column, cell) {
      console.log(row, column, cell)
      if (column.property === "remark" && this.permission.fgInventory_change_remark) {
        this.handleChangeRemark(row)
      }
    },
    handleChangeRemark(row) {
      this.$prompt('请输入备注', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValue: row.remark,
        inputPlaceholder: '请输入备注',
        inputPattern: /^.{0,100}$/,
        inputErrorMessage: '备注不能超过100个字符'
      }).then(({value}) => {
        changeRemark(row.id, value).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: 'success',
            message: '操作成功!'
          });
          this.$refs.crud.toggleSelection();
        })
      })
    }
  }
};
</script>

<style>
</style>

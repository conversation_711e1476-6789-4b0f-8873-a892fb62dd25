```java
// 定义包路径，属于问题反馈模块的数据传输对象包
package com.natergy.ni.feedback.dto;

// 导入Lombok的@Data注解，用于自动生成JavaBean的常用方法
import lombok.Data;

/**
 * <AUTHOR>  // 作者标识
 */
// @Data：Lombok注解，自动生成该类所有字段的getter、setter方法，
// 同时生成toString()、equals()、hashCode()等方法，简化代码编写
public class QdrantResponseDTO {
    // 操作相关的唯一标识ID（可能对应向量数据库中的点ID或操作记录ID）
    private Integer id;
    // 操作结果的消息描述（如成功提示、错误原因等）
    private String message;
}
```

### 类功能说明

该类是用于接收与**Qdrant 向量数据库**交互后返回结果的数据传输对象（DTO），主要作用是封装对 Qdrant 进行操作（如数据同步、删除、查询等）后的响应信息：

- `id`：通常表示操作涉及的资源唯一标识（例如同步到 Qdrant 的点 ID、被删除的记录 ID 等），便于后续追踪或关联业务数据。
- `message`：用于描述操作结果的文本信息（如 "同步成功"、"删除失败：ID 不存在" 等），为调用方提供清晰的操作反馈。

通过 Lombok 的`@Data`注解简化了代码，自动生成字段的访问方法和其他基础方法，使该类能高效地作为 Qdrant 操作的响应载体。
<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page.sync="page"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @refresh-change="refreshChange"
               :cell-style="cellStyle"
               @cell-click="cellClick"
               @on-load="onLoad">
      <template #menuLeft>
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          v-if="permission.ni_fg_inventory_summary_out"
          @click="handleOutbound">出 库
        </el-button>
        <el-button
          type="warning"
          icon="el-icon-shopping-cart-full"
          size="mini"
          v-if="permission.ni_fg_inventory_summary_restocking"
          @click="handleRestocking">倒 箱
        </el-button>
        <el-button
          type="warning"
          icon="el-icon-truck"
          size="mini"
          plain
          v-if="permission.ni_fg_inventory_summary_transfer"
          @click="handleTransfer">调 拨
        </el-button>
        <el-button
          type="danger"
          icon="el-icon-turn-off-microphone"
          size="mini"
          v-if="permission.ni_fg_inventory_summary_isolation"
          @click="handleIsolation">标记冻结
        </el-button>
        <el-button
          type="warning"
          icon="el-icon-refresh-left"
          size="mini"
          v-if="permission.ni_fg_inventory_summary_back_production"
          @click="handleBackProduction">退生产
        </el-button>
        <el-button
          type="danger"
          icon="el-icon-lollipop"
          size="mini"
          v-if="userInfo.role_name.includes('admin')"
          @click="handleCheck">库存校验
        </el-button>
        <!--导出-->
        <el-button
          type="info"
          icon="el-icon-download"
          size="mini"
          v-if="permission.ni_fg_inventory_summary_export"
          @click="handleExport">导 出
        </el-button>
        <el-divider direction="vertical"></el-divider>
        <el-radio-group v-model="area" size="mini" @input="onLoad(page)">
          <el-radio-button label="ALL">全部</el-radio-button>
          <el-radio-button label="CN">国内</el-radio-button>
          <el-radio-button label="OS">国外</el-radio-button>
        </el-radio-group>
      </template>
      <template #menu="{row,index}">
        <el-button
          type="text"
          icon="el-icon-more"
          size="mini"
          @click="rowBatchNo(row,index)">
        </el-button>
      </template>
    </avue-crud>
    <outbound-form-dialog ref="outboundFormDialogRef" @confirm="onLoad(page)"/>
    <restocking-form-dialog ref="restockingFormDialogRef" @confirm="handleRestockingConfirm"/>
    <transfer-form-dialog ref="transferFormDialogRef" @confirm="handleTransferConfirm"/>
    <inventory-drawer ref="inventoryDrawerRef"/>
    <inventory-select-dialog ref="inventorySelectDialogRef" multiple @confirm="handleInventorySelectConfirm"/>
    <freeze-form-dialog ref="freezeFormDialogRef" @confirm="onLoad(page)"/>
    <el-dialog
      title="库存校验"
      append-to-body
      :visible.sync="check.visible"
      width="455px"
    >
      <avue-form
        :option="check.option"
        v-model="check.form"
        @submit="handleCheckSubmit"
      >
      </avue-form>
    </el-dialog>
  </basic-container>
</template>

<script>
import {getDetail, getSummaryByDepotSkuList} from "@/api/ni/fg/fgInventory";
import {mapGetters} from "vuex";
import OutboundFormDialog from "@/views/ni/fg/components/OutboundFormDialog.vue";
import RestockingFormDialog from "@/views/ni/fg/components/RestockingFormDialog.vue";
import InventoryDrawer from "@/views/ni/fg/components/InventoryDrawer.vue";
import InventorySelectDialog from "@/views/ni/fg/components/InventorySelectDialog.vue";
import TransferFormDialog from "@/views/ni/fg/components/TransferFormDialog.vue";
import FreezeFormDialog from "@/views/ni/fg/components/FreezeFormDialog.vue";

export default {
  components: {
    TransferFormDialog,
    InventoryDrawer,
    OutboundFormDialog,
    RestockingFormDialog,
    InventorySelectDialog,
    FreezeFormDialog
  },
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      selectionList: [],
      option: {
        showSummary: true,
        sumColumnList: [
          {
            name: 'num',
            type: 'sum',
          },
          {
            name: 'freezeNum',
            type: 'sum',
          },
          {
            name: 'inTransitQuantity',
            type: 'sum',
          },
          {
            name: 'availableKg',
            type: 'sum',
          },
          {
            name: 'freezeKg',
            type: 'sum',
          },
          {
            name: 'inTransitKg',
            type: 'sum',
          }
        ],
        menuWidth: 80,
        addBtn: false,
        delBtn: false,
        editBtn: false,
        size: "mini",
        searchSize: "mini",
        align: "center",
        searchIndex: 3,
        searchIcon: true,
        searchEnter: true,
        height: 'auto',
        calcHeight: 30,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        selection: true,
        dialogClickModal: false,
        reserveSelection: true,
        column: [
          {
            label: "仓库",
            prop: "depotId",
            type: "select",
            dicUrl: "/api/ni/base/depot/info/list?status=2&type=1",
            props: {
              label: "name",
              value: "id",
              desc: "code",
            },
            search: true,
            minWidth: 100,
            overHidden: true,
            searchOrder: 99
          },
          {
            label: '品名',
            prop: "skuText",
            placeholder: " ",
            overHidden: true,

            minWidth: 150,
            search: true,
            hide: true,
          },
          {
            label: '规格',
            prop: 'specText',
            placeholder: " ",
            display: false,
            overHidden: true,
            minWidth: 150,
            search: true,
            searchOrder: 98
          },
          {
            label: '质量',
            prop: 'qualityLevel',
            type: 'select',
            dicUrl: "/api/blade-system/dict-biz/dictionary?code=ni_product_sku_quality_level",
            props: {
              label: "dictValue",
              value: "dictKey",
              desc: 'dictKey'
            },
            placeholder: " ",
            width: 100,
          },
          {
            label: '外包装',
            prop: 'packageText',
            placeholder: " ",
            display: false,
            overHidden: true,
            minWidth: 115,
            search: true,
            searchOrder: 97
          },
          {
            label: '内包装',
            prop: 'innerPackageText',
            placeholder: " ",
            display: false,
            overHidden: true,
            minWidth: 115,
            search: true
          },
          {
            label: "可用箱数",
            prop: "num",
            minWidth: 80,
            type: 'number',
          },
          {
            label: "冻结箱数",
            prop: "freezeNum",
            minWidth: 80,
            type: 'number',
          },
          {
            label: "在途箱数",
            prop: "inTransitQuantity",
            minWidth: 80,
            type: 'number',
            hide: true,
          },
          {
            label: '可用重量',
            prop: 'availableKg',
            placeholder: " ",
            type: 'number',
            minWidth: 80,
          },
          {
            label: '冻结重量',
            prop: 'freezeKg',
            placeholder: " ",
            type: 'number',
            minWidth: 80,
          },
          {
            label: '在途重量',
            prop: 'inTransitKg',
            placeholder: " ",
            type: 'number',
            minWidth: 80,
            hide: true,
          },
          {
            label: "单位",
            prop: "unit",
            type: "select",
            filterable: true,
            minWidth: 80,
            dicUrl: "/api/blade-system/dict-biz/dictionary?code=ni_material_unit",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            slot: true,
            placeholder: " ",
          },
          {
            label: '存货编码',
            prop: 'materialCode',
            placeholder: " ",
            minWidth: 110,
            overHidden: true,
            search: true
          },
        ]
      },
      data: [],
      exportColumn: [
        {
          label: "仓库",
          prop: "depotName",
        },
        {
          label: "品名",
          prop: "skuText",
        },
        {
          label: "规格",
          prop: "specText",
        },
        {
          label: '质量',
          prop: 'qualityLevel',
        },
        {
          label: "外包装",
          prop: "packageText",
        },
        {
          label: "内包装",
          prop: "innerPackageText",
        },
        {
          label: "唛头",
          prop: "currentMarkText",
        },
        {
          label: "批号",
          prop: "batchNo",
        },
        {
          label: "数量",
          prop: "num",
        },
        {
          label: "重量",
          prop: "weight",
        },
        {
          label: "单位",
          prop: "unit",
        },
        {
          label: "生产日期",
          prop: "productionDate",
        },
        {
          label: "备注",
          prop: "remark",
        },
        {
          label: "状态",
          prop: "status",
        },
        {
          label: "存货编码",
          prop: "materialCode",
        },
      ],
      depotKeyValue: {},
      statusDictKeyValue: {},
      unitKeyValue: {},
      action: null,
      area: 'ALL',
      qualityLevelDictKeyValue: {},
      qualityLevelColorMap: {
        'A': '#67C23A', // 高吸附 - 绿色
        'P': '#409EFF', // 优等品 - 蓝色
        'Q': '#E6A23C', // 合格品 - 橙色
      },
      check: {
        visible: false,
        option: {
          size: "mini",
          span: 24,
          emptyBtn: false,
          column: [
            {
              label: "仓库",
              prop: "depotId",
              type: "select",
              dicUrl: "/api/ni/base/depot/info/list?status=2&type=1",
              props: {
                label: "name",
                value: "id",
                desc: "code",
              },
              minWidth: 100,
              overHidden: true,
              rules: [
                {
                  required: true,
                  message: "请选择仓库",
                  trigger: "blur",
                },
              ]
            },

          ]
        },
        form: {}
      }
    };
  },
  computed: {
    ...mapGetters(["permission", "userInfo"]),
    ids() {
      let ids = [];
      this.selectionList.forEach(ele => {
        ids.push(ele.id);
      });
      return ids.join(",");
    }
  },
  created() {
    this.dictInit()
  },
  methods: {
    rowBatchNo(row) {
      this.$refs.inventoryDrawerRef.onShow(row)
    },
    dictInit() {
      this.$http.get("/api/ni/base/depot/info/list?status=2&type=1")
        .then((res) => {
          this.depotKeyValue = res.data.data.reduce((acc, cur) => {
            acc[cur.id] = cur.name;
            return acc;
          }, {});
        });
      this.$http
        .get("/api/blade-system/dict-biz/dictionary?code=ni_product_sku_quality_level")
        .then((res) => {
          const dict = res.data.data;
          this.qualityLevelDictKeyValue = dict.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });
      this.$http.get("/api/blade-system/dict-biz/dictionary?code=ni_fg_inventory_status")
        .then((res) => {
          this.statusDictKeyValue = res.data.data.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });
      this.$http.get("/api/blade-system/dict-biz/dictionary?code=ni_material_unit")
        .then((res) => {
          this.unitKeyValue = res.data.data.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });
    },
    handleExport() {
      let msg = "是否导出当前选取的数据？"
      if (this.selectionList.length === 0) {
        msg = "是否导出全部数据？"
      }
      this.$confirm(msg, {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        let data = this.selectionList
        if (this.selectionList.length === 0) {
          const res = await getSummaryByDepotSkuList(1, 100000, this.query);
          data = res.data.data.records
        }
        this.$Export.excel({
          title: "当前库存",
          columns: this.exportColumn,
          data: data.map((item) => {
            return {
              ...item,
              depotName: this.depotKeyValue[item.depotId],
              status: this.statusDictKeyValue[item.status],
              unit: this.unitKeyValue[item.unit],
              qualityLevel: this.qualityLevelDictKeyValue[item.qualityLevel]
            }
          })
        })
      });
    },
    handleCheck() {
      this.check.visible = true
    },
    handleCheckSubmit(form, done) {
      this.$http.post("/api/ni/fg/inventory/check/" + form.depotId).then((res) => {
        if (res.data.msg)
          this.$alert(res.data.msg, '异常信息', {
            dangerouslyUseHTMLString: true,
            type: 'warning',
            center: true
          });
        else
          this.$message({
            type: "success",
            message: "校验完成，暂未发现问题!",
          });
      }).finally(() => {
        done()
      })
    },
    handleBackProduction() {
      if (this.selectionList && this.selectionList.length > 0) {
        const depotIds = new Set()
        this.selectionList.forEach((item) => {
          depotIds.add(item.deptId)
        })
        if (depotIds.size > 1) {
          this.$message({
            type: "warning",
            message: "请选择同一仓库的数据进行出库!"
          })
          return
        }
      }
      this.action = "backProduction"
      this.$refs.inventorySelectDialogRef.onShow({
        depotId: this.selectionList[0].depotId,
        skuIds: this.selectionList.map((item) => item.skuId).join(','),
        status: 1
      });
    },
    handleIsolation() {
      const depotIds = new Set()
      this.selectionList.forEach((item) => {
        depotIds.add(item.deptId)
      })
      if (depotIds.size > 1) {
        this.$message({
          type: "warning",
          message: "请选择同一仓库的数据进行出库!"
        })
        return
      }
      this.action = "freeze"
      this.$refs.inventorySelectDialogRef.onShow({
        depotId: this.selectionList[0].depotId,
        skuIds: this.selectionList.map((item) => item.skuId).join(','),
        status: 1
      });
    },
    handleTransfer() {
      const depotIds = new Set()
      this.selectionList.forEach((item) => {
        depotIds.add(item.deptId)
      })
      if (depotIds.size > 1) {
        this.$message({
          type: "warning",
          message: "请选择同一仓库的数据进行出库!"
        })
        return
      }
      this.action = "transfer"
      this.$refs.inventorySelectDialogRef.onShow({
        depotId: this.selectionList[0].depotId,
        skuIds: this.selectionList.map((item) => item.skuId).join(','),
      });
    },
    handleRestocking() {
      const depotIds = new Set()
      this.selectionList.forEach((item) => {
        depotIds.add(item.deptId)
      })
      if (depotIds.size > 1) {
        this.$message({
          type: "warning",
          message: "请选择同一仓库的数据进行出库!"
        })
        return
      }
      this.action = "restocking"
      this.$refs.inventorySelectDialogRef.onShow({
        depotId: this.selectionList[0].depotId,
        skuIds: this.selectionList.map((item) => item.skuId).join(','),
      });
    },
    handleInventorySelectConfirm(selectionList) {
      const depotIds = new Set()
      selectionList.forEach((item) => {
        depotIds.add(item.deptId)
      })
      if (depotIds.size > 1) {
        this.$message({
          type: "warning",
          message: "请选择同一仓库的数据进行出库!"
        })
        return
      }
      const depotId = selectionList[0].depotId
      if (this.action === 'restocking') {
        this.$refs.restockingFormDialogRef.onAdd(depotId, selectionList)
      } else if (this.action === 'outbound') {
        const freeze = selectionList.some((item) => item.status !== 1)
        if (freeze) {
          this.$message({
            type: "warning",
            message: "请选择非冻结的数据进行出库!"
          })
          return
        }
        this.$refs.outboundFormDialogRef.onAdd(depotId, selectionList)
      } else if (this.action === 'transfer') {
        this.$refs.transferFormDialogRef.onAdd(depotId, selectionList)
      } else if (this.action === 'backProduction') {
        this.$refs.outboundFormDialogRef.onAdd(depotId, selectionList, {type: '0207'})
      } else if (this.action === 'freeze') {
        this.$refs.freezeFormDialogRef.onFreeze(depotId, selectionList)
      }
    },
    handleTransferConfirm() {
      this.onLoad(this.page)
      const path = "/ni/fg/transfer"
      if (path) this.$router.push(path);
    },
    handleRestockingConfirm() {
      this.onLoad(this.page)
      const path = "/ni/fg/restocking"
      if (path) this.$router.push(path);
    },
    handleOutbound() {
      if (this.selectionList && this.selectionList.length > 0) {
        const depotIds = new Set()
        this.selectionList.forEach((item) => {
          depotIds.add(item.deptId)
        })
        if (depotIds.size > 1) {
          this.$message({
            type: "warning",
            message: "请选择同一仓库的数据进行出库!"
          })
          return
        }
      }
      this.action = "outbound"
      this.$refs.inventorySelectDialogRef.onShow({
        depotId: this.selectionList[0].depotId,
        skuIds: this.selectionList.map((item) => item.skuId).join(','),
        status: 1
      });
    },
    beforeOpen(done, type) {
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then(res => {
          this.form = res.data.data;
        });
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      const q = {
        ...params,
        ...this.query
      }
      q.area = this.area === "ALL" ? null : this.area
      getSummaryByDepotSkuList(page.currentPage, page.pageSize, q).then(res => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
      });
    },
    cellStyle({row, column}) {
      if ('freezeNum' === column.columnKey && row.freezeNum > 0) {
        return {
          backgroundColor: '#409EFF',
          color: "#fff",
          textDecoration: 'underline',
          cursor: 'pointer',
        }
      }
      if ("qualityLevel" === column.columnKey) {
        const color = this.qualityLevelColorMap[row.qualityLevel];
        return {
          backgroundColor: color || "#909399", // 默认颜色为灰色
          color: "#fff",
        }
      }
    },
    cellClick(row, column, cell) {
      if (column.property === "freezeNum") {
        this.rowFreezeShow(row)
      }
    },
    rowFreezeShow(row) {
    }
  }
};
</script>

<style>
</style>

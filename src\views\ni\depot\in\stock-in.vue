<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      :search.sync="query"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
      :cell-style="cellStyle"
      :row-style="rowStyle"
    >
      <template #stockState="{ row }">
        <span v-if="row.stockState"
          :style="{
            fontWeight: 'bold',
          }"
          >
          是
        </span>
        <span v-else
        :style="{
            color: '#FF0000',
            fontWeight: 'bold',
          }">否</span>
      </template>
      <template #total="{ row, index }">
        <span
          :style="{
            color: row.red ? '#F56C6C' : '',
            fontWeight: row.red ? 'bold' : '',
          }"
        >
          {{ row.total }}
        </span>
      </template>
      <template #pv="{ row, index }">
        <el-tag size="mini" v-if="row.pv" type="danger" effect="dark">
          是
        </el-tag>
        <el-tag size="mini" v-else type="info" effect="plain">否</el-tag>
      </template>
      <template #yonyouSync="{ row, size }">
        <span v-for="(item, index) in row.yonyouSync" :key="index">
          {{ yonyouSyncSequenceDictKeyValue[item.sequence] }}:
          <el-tag :size="size">
            {{ yonyouSyncStateDictKeyValue[item.value] }}
          </el-tag>
          ;
          <template v-if="item.id">
            <span style="font-weight: bolder">id:</span>
            {{ item.id }}
          </template>
          <template v-if="item.errMsg"> :{{ item.errMsg }} </template>
          <br />
        </span>
      </template>
      <template #brand="{ row, disabled, size, index }">
        <el-tag
          v-if="row.brand"
          :size="size"
          :effect="row.brand === '1' ? 'dark ' : 'plain'"
        >
          {{ row.$brand }}
        </el-tag>
      </template>
      <template #status="{ row, index }">
        <template v-if="row.status">
          <el-tag
            v-if="row.status === 1"
            size="mini"
            type="info"
            effect="plain"
          >
            {{ row.$status }}
          </el-tag>
          <el-tag
            v-else-if="row.status === 2"
            size="mini"
            type="success"
            effect="plain"
          >
            {{ row.$status }}
          </el-tag>
          <el-tag
            v-else-if="row.status === 3"
            size="mini"
            type="warning"
            effect="plain"
          >
            {{ row.$status }}
          </el-tag>
        </template>
      </template>
      <template #title="{ row, index }">
        <span v-if="row.red">(红字)</span>
        <span>
          {{ row.title }}
        </span>
      </template>
      <template #serialNo="{ row, index }">
        <span
          :style="{
            color: colorName,
            fontWeight: 'bold',
            cursor: 'pointer',
            textDecoration: 'underline',
          }"
          @click="$refs.crud.rowView(row, index)"
        >
          {{ row.serialNo }}
        </span>
      </template>
      <template #subType="{ row, size, index, dic }">
        <dict-tag
          v-if="row.subType"
          :size="size"
          v-model="row.subType"
          :dict="dic"
          :dict-props="{
            label: 'dictValue',
            value: 'dictKey',
          }"
        />
      </template>
      <!--      <template #subType="{ row }">-->
      <!--        <dict-tag-->
      <!--        <el-tag v-if="row.red" effect="dark" type="danger" size="mini"-->
      <!--          >{{ row.$subType }}-->
      <!--        </el-tag>-->
      <!--        <el-tag v-else effect="plain" size="mini">{{ row.$subType }}</el-tag>-->
      <!--      </template>-->
      <template #itemsForm="{ row, disabled, size, index }">
        <stock-in-item-form
          v-model="form.items"
          :size="size"
          :amount.sync="form.amount"
          :disabled="disabled"
        />
      </template>
      <template #menuForm="{ row, index, type }">
        <el-button
          type="primary"
          icon="el-icon-circle-plus-outline"
          size="mini"
          v-if="type == 'add'"
          @click="$refs.crud.rowSave()"
          >新增
        </el-button>
        <el-button
          type="primary"
          icon="el-icon-circle-plus-outline"
          size="mini"
          v-if="type == 'edit'"
          @click="$refs.crud.rowUpdate()"
          >修改
        </el-button>
        <el-button
          type="primary"
          icon="el-icon-s-promotion"
          size="mini"
          v-if="type == 'add'"
          @click="rowSaveAndApply"
          >提交
        </el-button>
        <el-button
          type="primary"
          icon="el-icon-s-promotion"
          size="mini"
          v-if="type == 'edit'"
          @click="rowEditAndApply"
          >提交
        </el-button>
        <el-button
          icon="el-icon-circle-close"
          size="mini"
          @click="$refs.crud.closeDialog()"
          >取消
        </el-button>
      </template>
      <template #menuLeft>
        <el-button
          type="primary"
          size="mini"
          icon="el-icon-plus"
          plain
          v-if="permission.storage_add_other"
          @click="handleOtherIn"
          >其他入库
        </el-button>
        <el-button
          type="danger"
          size="mini"
          icon="el-icon-delete"
          plain
          v-if="permission.storage_delete"
          :disabled="selectionList.length < 1"
          @click="handleDelete"
          >删 除
        </el-button>
        <el-dropdown @command="handleImport" v-if="permission.storage_add">
          <el-button type="primary" size="mini" icon="el-icon-upload2" plain>
            导入<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="199">其他入库</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-button
          type="warning"
          size="mini"
          icon="el-icon-printer"
          plain
          @click="handlePrint"
          >打 印
        </el-button>
        <el-button
          type="primary"
          size="mini"
          icon="el-icon-printer"
          v-if="permission.storage_print"
          @click="handlePrintStockIn"
          >入库打印
        </el-button>
        <el-button
          type="primary"
          size="mini"
          icon="el-icon-printer"
          @click="handlePrintSign"
        >打印标记
        </el-button>
        <el-button
          type="warning"
          size="mini"
          icon="el-icon-refresh"
          plain
          v-if="permission.storage_yonyou_sync"
          @click="handleYonyouSync"
          >用友同步
        </el-button>
        <el-divider direction="vertical"></el-divider>
        <el-checkbox v-model="pv">压力容器</el-checkbox>
      </template>
      <template #menu="{ row, index, type, size }">
        <el-button
          type="text"
          icon="el-icon-edit"
          :size="size"
          v-if="permission.storage_edit && row.status == 1"
          @click="$refs.crud.rowEdit(row, index)"
          >编 辑
        </el-button>
        <el-button
          type="text"
          icon="el-icon-s-promotion"
          :size="size"
          v-if="row.status == 1"
          @click="rowSubmit(row)"
          >提交
        </el-button>
        <el-button
          type="text"
          icon="el-icon-download"
          :size="size"
          @click="rowAttach(row)"
          >附件管理
        </el-button>
        <el-button
          type="text"
          icon="el-icon-refresh"
          :size="size"
          v-if="permission.storage_yonyou_sync && row.subType !== '109'"
          @click="rowYonyouSync(row)"
        >
          同步用友
        </el-button>
      </template>
      <template #menuRight="{ size }">
        <el-button
          icon="el-icon-time"
          circle
          :size="size"
          @click="handleLog"
        ></el-button>
      </template>
    </avue-crud>
    <attach-dialog ref="attachDialogRef" code="private" />
    <log-opt-dialog ref="logOptDialogRef" :module="module" />
    <stock-in-form ref="stockInFormRef" @submit="onLoad(page)" />
    <material-select-dialog
      ref="materialSelectDialogRef"
      multiple
      @submit="handleItemAddSubmit"
    />
    <el-dialog
      title="用友同步"
      append-to-body
      :visible.sync="yonyou.visible"
      width="355px"
    >
      <avue-form
        ref="yonyouSyncRef"
        :option="yonyou.option"
        v-model="yonyou.form"
        @submit="handleYonyouSyncSubmit"
      ></avue-form>
    </el-dialog>
    <el-dialog
      title="入库数据导入"
      append-to-body
      :visible.sync="importBox"
      width="555px"
    >
      <avue-form
        v-if="importBox"
        :option="importOption"
        v-model="importForm"
        :upload-after="uploadAfter"
      >
        <template slot="importTemplate">
          <el-button size="mini" type="primary" @click="handleTemplate">
            点击下载<i class="el-icon-download el-icon--right"></i>
          </el-button>
        </template>
      </avue-form>
    </el-dialog>
  </basic-container>
</template>

<script>
import {
  add,
  getDetail,
  getList,
  remove,
  submit,
  sync,
  syncRange,
  update,
  getPrintData,
  printSign,
} from "@/api/ni/depot/stockIn";
import { mapGetters } from "vuex";
import { dateNow1 } from "@/util/date";
import { exportBlob } from "@/api/common";
import { getToken } from "@/util/auth";
import { downloadXls } from "@/util/util";
import "nprogress/nprogress.css";
import AttachDialog from "@/components/attach-dialog";
import LogOptDialog from "@/components/log-opt-dialog";
import { detail as getOrderArrivalDetail } from "@/api/ni/por/order-arrival";
import MaterialSelectDialog from "@/views/ni/base/components/MaterialSelectDialog";
import StockInItemForm from "@/views/ni/depot/components/StockInItemForm";
import { loadPrintTemplate } from "@/api/system/printTemplate";
import { hiprint } from "vue-plugin-hiprint";
import StockInForm from "@/views/ni/depot/components/StockInSNForm";

export default {
  components: {
    StockInItemForm,
    AttachDialog,
    LogOptDialog,
    MaterialSelectDialog,
    StockInForm,
  },
  data() {
    return {
      module: "ni_depot_stock_in",
      itemDialogShow: false,
      transferIn: false,
      materialCode: "",
      form: {
        items: [],
      },
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      materialLoading: true,
      materialQuery: {},
      selectionList: [],
      option: {
        addBtn: false,
        menuWidth: 200,
        searchEnter: true,
        searchLabelWidth: 110,
        saveBtn: false,
        updateBtn: false,
        cancelBtn: false,
        span: 6,
        size: "mini",
        searchSize: "mini",
        delBtn: false,
        editBtn: false,
        dialogFullscreen: true,
        searchIndex: 3,
        searchIcon: true,
        height: "auto",
        calcHeight: 30,
        tip: false,
        labelWidth: 110,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: false,
        align: "center",
        // addBtn: false,
        viewBtn: false,
        selection: true,
        dialogClickModal: false,
        column: [
          {
            label: "状态",
            prop: "status",
            type: "select",
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_por_order_status",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            dataType: "number",
            fixed: "left",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            search: true,
          },
          {
            label: "单据主题",
            prop: "title",
            search: true,
            overHidden: true,
            rules: [
              {
                required: true,
                message: "请输入",
                trigger: "blur",
              },
            ],
          },
          {
            label: "单据编号",
            prop: "serialNo",
            disabled: true,
            search: true,
            placeholder: "系统自动生成",
            overHidden: true,
            width: 110,
          },
          {
            label: "采购人",
            prop: "personName",
            overHidden: true,
            display: false,
            width: 70,
          },
          {
            label: "采购人",
            prop: "personId",
            type: "select",
            remote: true,
            dicUrl: `/api/blade-user/search/otj/user?name={{key}}`,
            props: {
              label: "realName",
              value: "id",
            },
            dicFormatter: (data) => {
              return data.data.records;
            },
            filterable: true,
            overHidden: true,
            search: true,
            hide: true,
            showColumn: false,
          },
          {
            label: "供应商",
            prop: "supplierName",
            overHidden: true,
            width: 110,
          },
          {
            label: "供应商",
            prop: "supplierId",
            type: "select",
            remote: true,
            placeholder: " ",
            dicUrl: `/api/ni/base/supplier/info/page?status=2&keyword={{key}}`,
            props: {
              label: "name",
              value: "id",
              desc: "code",
            },
            dicFormatter: (data) => {
              return data.data.records;
            },
            search: true,
            hide: true,
            showColumn: false,
          },
          {
            label: "入库类型",
            prop: "subType",
            search: true,
            searchLabelWidth: 90,
            type: "select",
            width: 90,
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_depot_storage_type",
            props: {
              label: "dictValue",
              value: "dictKey",
              desc: "dictKey",
            },
            rules: [
              {
                required: true,
                message: "请输入",
                trigger: "blur",
              },
            ],
          },
          {
            label: "申购人",
            prop: "applyUserId",
            type: "select",
            remote: true,
            dicUrl: `/api/blade-user/search/otj/user?name={{key}}`,
            props: {
              label: "realName",
              value: "id",
            },
            dicFormatter: (data) => {
              return data.data.records;
            },
            filterable: true,
            overHidden: true,
            search: true,
            hide: true,
            showColumn: false,
          },
          {
            label: "申购人",
            prop: "applyUserName",
            display: false,
            overHidden: true,
          },
          {
            label: "采购申请",
            prop: "porApplySerialNo",
            display: false,
            hide: true,
            overHidden: true,
          },
          {
            label: "账套",
            prop: "brand",
            type: "radio",
            minWidth: 90,
            value: "1",
            dicUrl: "/api/blade-system/dict-biz/dictionary?code=ni_brand",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            search: true,
          },
          {
            label: "压力容器",
            prop: "pv",
            type: "radio",
            value: 0,
            dicData: [
              {
                label: "是",
                value: 1,
              },
              {
                label: "否",
                value: 0,
              },
            ],
            rules: [
              {
                required: true,
                message: "请选择压力容器",
                trigger: "blur",
              },
            ],
          },
          {
            label: "仓库",
            prop: "depotId",
            type: "select",
            width: 90,
            overHidden: true,
            search: true,
            placeholder: " ",
            dicUrl: "/api/ni/base/depot/info/list?type=7,8&status=2",
            props: {
              label: "name",
              value: "id",
              desc: "code",
            },
            rules: [
              {
                required: true,
                message: "请选择",
                trigger: "blur",
              },
            ],
          },
          {
            label: "库管人员",
            prop: "keeperId",
            type: "select",
            remote: true,
            dicUrl: `/api/blade-user/search/otj/user?name={{key}}`,
            props: {
              label: "realName",
              value: "id",
            },
            dicFormatter: (data) => {
              return data.data.records;
            },
            filterable: true,
            search: true,
            hide: true,
            showColumn: false,
            rules: [
              {
                required: true,
                message: "请选择",
                trigger: "blur",
              },
            ],
          },
          {
            label: "库管人员",
            prop: "keeperName",
            display: false,
          },
          {
            label: "入库时间",
            prop: "opTime",
            minWidth: 130,
            searchRange: true,
            type: "datetime",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
            rules: [
              {
                required: true,
                message: "请输入",
                trigger: "blur",
              },
            ],
          },
          {
            label: "入库时间",
            prop: "searchOpTime",
            search: true,
            minWidth: 130,
            searchRange: true,
            type: "date",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            showColumn: false,
            hide: true,
            display: false,
          },
          {
            label: "数量合计",
            prop: "total",
            type: "number",
            display: false,
          },
          {
            label: "全部到货",
            prop: "stockState",
            display: false,
          },
          {
            label: "金额合计",
            prop: "amount",
            type: "number",
            precision: 2,
            placeholder: " ",
            disabled: true,
            hide: true,
          },
          {
            label: "备注",
            prop: "remark",
            hide: true,
            type: "textarea",
            span: 24,
            minRows: 3,
          },
          {
            label: "用友同步",
            prop: "yonyouSync",
            minWidth: 220,
            addDisplay: false,
            editDisplay: false,
          },
          {
            label: "用友同步",
            prop: "yonyou",
            minWidth: 220,
            type: "select",
            dicData: [
              {
                label: "已同步",
                value: true,
              },
              {
                label: "未同步",
                value: false,
              },
            ],
            search: true,
            hide: true,
            showColumn: false,
            display: false,
          },
          {
            size: "mini",
            labelWidth: 0,
            label: "",
            prop: "items",
            span: 24,
            hide: true,
            showColumn: false,
          },
        ],
      },
      data: [],
      importBox: false,
      importForm: {},
      importOption: {
        submitBtn: false,
        emptyBtn: false,
        column: [
          {
            label: "模板上传",
            prop: "importFile",
            type: "upload",
            drag: true,
            loadText: "模板上传中，请稍等",
            span: 24,
            propsHttp: {
              res: "data",
            },
            dragFile: true,
            tip: "请上传 .xls,.xlsx 标准格式文件",
            action: "/api/ni/depot/stock/in/importStockIn",
          },
          {
            label: "模板下载",
            prop: "importTemplate",
            formslot: true,
            span: 24,
          },
        ],
      },
      unitDict: [],
      unitDictKeyValue: {},
      subTypeDict: [],
      subTypeDictKeyValue: {},
      yonyouSyncSequenceDict: [],
      yonyouSyncSequenceDictKeyValue: {},
      yonyouSyncStateDict: [],
      yonyouSyncStateDictKeyValue: {},
      yonyou: {
        visible: false,
        option: {
          span: 24,
          size: "mini",
          emptyBtn: false,
          column: [
            {
              prop: "date",
              label: "选择日期",
              type: "daterange",
              format: "yyyy-MM-dd",
              valueFormat: "yyyy-MM-dd",
              rules: [
                {
                  required: true,
                  message: "请选择日期范围",
                  trigger: "blur",
                },
              ],
            },
          ],
        },
        form: {},
      },
      pv: false,
      printTemplate: null,
      stockInPrintTemplate: null,
    };
  },
  watch: {
    pv: {
      handler(val) {
        if (val) {
          this.query.pv = 1;
        } else {
          this.query.pv = null;
        }
        this.page.currentPage = 1;
        this.onLoad(this.page);
      },
      immediate: false,
    },
  },
  computed: {
    ...mapGetters(["permission", "userInfo", "colorName"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.storage_add, false),
        viewBtn: this.vaildData(this.permission.storage_view, false),
        delBtn: this.vaildData(this.permission.storage_delete, false),
        editBtn: this.vaildData(this.permission.storage_edit, false),
      };
    },
    infoData() {
      return this.form.items || [];
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  mounted() {
    this.dictInit();
    loadPrintTemplate("ni_stock_in").then((res) => {
      this.printTemplate = JSON.parse(res.data.data.content);
    });
  },
  created() {
    loadPrintTemplate("ni_order_arrival_stock_in").then((res) => {
      this.stockInPrintTemplate = JSON.parse(res.data.data.content);
    });
  },
  methods: {
    rowStyle({ row }) {
      if (row.printSign) {
        return {
          backgroundColor: "#DCDCDC",
        };
      }
    },
    handleOtherIn() {
      this.$refs.stockInFormRef.onAddOther({});
    },
    handleYonyouSyncSubmit(form, done) {
      this.$refs.yonyouSyncRef.validate((valid) => {
        if (valid) {
          syncRange(form.date[0], form.date[1])
            .then(() => {
              this.yonyou.visible = false;
              this.yonyou.form = {};
              this.onLoad(this.page);
              this.$message({
                type: "success",
                message: "操作成功!",
              });
            })
            .finally(() => {
              done();
            });
        }
      });
    },
    handleItemAddSubmit(selectList) {
      if (selectList) {
        selectList.forEach((item) => {
          this.form.items.push({
            materialTypeName: item.typeName,
            materialCode: `${item.name}(${item.code})`,
            materialTypeId: item.typeId,
            materialId: item.id,
            specification: item.specification,
            gb: item.gb,
            unit: item.unit,
          });
        });
      }
    },
    rowYonyouSync(row) {
      let msg = "确定将选择数据同步?";
      if (row.yonyouSync && row.yonyouSync.length > 0) {
        msg = "数据已经同步过，是否继续?";
      }
      this.$confirm(msg, {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        sync(row.id).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
      });
    },
    handleLog() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择要查看的数据");
        return;
      } else if (this.selectionList.length > 1) {
        this.$message.warning("只能选择一条数据");
        return;
      }
      this.$refs.logOptDialogRef.init(this.selectionList[0].id);
    },
    rowAttach(row) {
      this.$refs.attachDialogRef.init(row.id, this.module);
    },
    rowSubmit(row) {
      this.$confirm("确定将提交该入库单?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return submit(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    rowEditAndApply() {
      this.form.status = 2;
      this.$refs.crud.rowUpdate();
    },
    rowSaveAndApply() {
      this.form.status = 2;
      this.$refs.crud.rowSave();
    },
    dictInit() {
      this.$http
        .get("/api/blade-system/dict-biz/dictionary?code=ni_material_unit")
        .then((res) => {
          this.unitDict = res.data.data;
          this.unitDictKeyValue = this.unitDict.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });
      this.$http
        .get("/api/blade-system/dict/dictionary?code=yonyou_sync_sequence")
        .then((res) => {
          this.yonyouSyncSequenceDict = res.data.data;
          this.yonyouSyncSequenceDictKeyValue =
            this.yonyouSyncSequenceDict.reduce((acc, cur) => {
              acc[cur.dictKey] = cur.dictValue;
              return acc;
            }, {});
        });
      this.$http
        .get("/api/blade-system/dict/dictionary?code=yonyou_sync_state")
        .then((res) => {
          this.yonyouSyncStateDict = res.data.data;
          this.yonyouSyncStateDictKeyValue = this.yonyouSyncStateDict.reduce(
            (acc, cur) => {
              acc[cur.dictKey] = cur.dictValue;
              return acc;
            },
            {}
          );
        });
      this.$http
        .get("/api/blade-system/dict-biz/dictionary?code=ni_depot_storage_type")
        .then((res) => {
          this.subTypeDict = res.data.data;
          this.subTypeDictKeyValue = this.subTypeDict.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });
    },
    handleTemplate() {
      exportBlob(
        `/api/ni/depot/stock/in/export-template?${
          this.website.tokenHeader
        }=${getToken()}`
      ).then((res) => {
        downloadXls(res.data, "入库导入模板.xlsx");
      });
    },
    uploadAfter(res, done, loading, column) {
      window.console.log(column);
      this.importBox = false;
      this.refreshChange();
      done();
    },
    rowSave(row, done, loading) {
      const form = { ...row };
      form.recordType = 1;
      add(form).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          window.console.log(error);
        }
      );
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          console.log(error);
        }
      );
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    handleYonyouSync() {
      this.yonyou.visible = true;
    },
    async handlePrint() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择数据");
        return;
      }

      let printDataList = [];
      let hiprintTemplate;

      if (!this.printTemplate) {
        this.$message.error("打印模板加载失败，请联系管理员");
        return;
      }

      for (const selectedItem of this.selectionList) {
        const res = await getDetail(selectedItem.id);
        let printData = res.data.data;
        printData.title = "山东能特异能源科技有限公司入库单";

        if (printData.brand === "4") {
          printData.title = "演绎智能设备（山东）有限公司入库单";
        } else if (printData.brand === "2") {
          printData.title = "淄博至简工贸入库单";
        }

        printData.subType =
          printData.subType != null
            ? this.subTypeDictKeyValue[printData.subType]
            : "";

        printData.purchaseUserName =
          printData.items.length > 0 ? printData.items[0].purchaseUserName : "";

        printData.opTime =
          printData.opTime != null ? printData.opTime.slice(0, 10) : "";

        printData.items = printData.items.map((item) => {
          return {
            ...item,
            unit: this.unitDictKeyValue[item.unit],
            price: (
              (Number(item.amount) * 10000) /
              Number(item.num) /
              10000
            ).toLocaleString("zh-CN", {
              minimumFractionDigits: 2,
            }),
          };
        });

        //存储分组后的结果
        const groupedItems = new Map();
        // 遍历items, 根据porOrderArrivalSerialNo进行分组
        printData.items.forEach((item) => {
          const { porOrderArrivalSerialNo } = item;
          if (!groupedItems.has(porOrderArrivalSerialNo)) {
            groupedItems.set(porOrderArrivalSerialNo, []);
          }
          groupedItems.get(porOrderArrivalSerialNo).push(item);
        });
        //分组结果存储在数组中
        const groupedItemsArray = Array.from(groupedItems.values());
        const result = groupedItemsArray.map((item) => {
          printData.arrivalSerialNo =
            item.length > 0 && item[0].porOrderArrivalSerialNo != null
              ? item[0].porOrderArrivalSerialNo
              : "";
          return {
            ...printData,
            items: item,
          };
        });

        printDataList = printDataList.concat(result);
      }

      hiprintTemplate = new hiprint.PrintTemplate({
        template: this.printTemplate,
      });

      hiprintTemplate.print(printDataList);
    },
    async handlePrintStockIn(withCost = true) {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择要打印的数据");
        return;
      }
      const res = await getPrintData(this.ids);
      const printData = res.data.data;
      for (const data of printData) {
        if (data.items.some((item) => !["4", "3"].includes(item.stockState))) {
          this.$message.warning("请选择已全部入库的数据");
          return;
        }
      }

      const hasBack = printData.some((item) => item.backNum > 0);
      if (hasBack) {
        this.$confirm("选择的数据中存在退换货的数据，是否全部打印?", {
          distinguishCancelAndClose: true,
          confirmButtonText: "全部打印",
          cancelButtonText: "只打印未退换货数据",
          type: "warning",
        })
          .then(() => {
            this.handlePrintStockInNext(withCost, printData);
          })
          .catch((action) => {
            this.$message({
              type: "info",
              message:
                action === "cancel" ? "放弃保存并离开页面" : "停留在当前页面",
            });
          });
      } else {
        this.handlePrintStockInNext(withCost, printData);
      }
    },
    async handlePrintStockInNext(withCost = true, data) {
      let printDataList = [];
      let hiprintTemplate;
      if (!this.stockInPrintTemplate) {
        this.$message.error("打印模板加载失败，请刷新页面后重试");
        return;
      }
      for (const item of data) {
        const res = await getOrderArrivalDetail(item.id);
        let printData = res.data.data;
        printData.title = "山东能特异能源科技有限公司入库单";

        if (printData.brand === "4") {
          printData.title = "演绎智能设备（山东）有限公司入库单";
        } else if (printData.brand === "2") {
          printData.title = "淄博至简工贸入库单";
        }
        printData.depotName = "";
        printData.keeperName = this.userInfo.user_name;
        printData.items = printData.items.filter(
          (item) => withCost || !item.cost
        );
        if (printData.items && printData.items.length > 0)
          printDataList.push(printData);
      }
      if (printDataList.length === 0) {
        this.$message.warning("没有可打印的数据,请重新选择");
        return;
      }
      console.log(printDataList);
      hiprintTemplate = new hiprint.PrintTemplate({
        template: this.stockInPrintTemplate,
      });
      hiprintTemplate.print(printDataList);
      setTimeout(() => {
        this.handlePrintSign();
      }, 1000);
    },
    handlePrintSign() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择要标记的数据");
        return;
      }
      let msg = `是否将选中的<span style="color: #F56C6C;font-weight: bold">${this.selectionList.length}条数据</span>标记为已打印?`;
      this.$confirm(msg, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        dangerouslyUseHTMLString: true,
        type: "warning",
      }).then(() => {
        printSign(this.ids).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
      });
    },
    handleImport(command) {
      const column = this.findObject(this.importOption.column, "importFile");
      column.action = `/api/ni/depot/stock/in/importStockIn?subType=${command}`;
      this.importBox = true;
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen(done, type) {
      if (["add"].includes(type)) {
        this.form.keeperId = this.userInfo.user_id;
        this.form.opTime = dateNow1();
      }
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then((res) => {
          this.form = res.data.data;
        });
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.pv = false;
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;

      const query = Object.assign(params, this.query);
      query.unAdjust = true;
      query.descs = "id";
      if (query.searchOpTime && query.searchOpTime.length > 1) {
        query.startOpTime = query.searchOpTime[0] + " 00:00:00";
        query.endOpTime = query.searchOpTime[1] + " 23:59:59";
      }
      getList(page.currentPage, page.pageSize, query).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
    cellStyle({ row, column }) {
      if ("title" === column.columnKey && row.red) {
        return {
          backgroundColor: "#F56C6C",
          color: "#fff",
        };
      }
    },
  },
};
</script>

<style></style>

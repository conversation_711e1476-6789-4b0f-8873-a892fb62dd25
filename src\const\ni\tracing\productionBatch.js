export default {
  align: "center",
  size: "mini",
  searchSize: "mini",
  height: "auto",
  calcHeight: 30,
  tip: false,
  searchIcon: true,
  searchShow: true,
  indexLabel: "序号",
  searchMenuSpan: 6,
  searchIndex: 3,
  border: true,
  index: false,
  addBtn: true,
  editBtn: false,
  delBtn: false,
  selection: true,
  dialogClickModal: false,
  menuWidth: 220,
  labelWidth: 150,
  showSummary: true,
  sumColumnList: [
    {
      name: "planBoxQuantity",
      type: "sum",
      decimals: 0,
    },
    {
      name: "actualBoxQuantity",
      type: "sum",
      decimals: 0,
    },
    {
      name: "actualTotalCapacity",
      type: "sum",
      decimals: 2,
    },
  ],
  column: [
    {
      label: "日期",
      prop: "date",
      type: "date",
      format: "yyyy-MM-dd",
      valueFormat: "yyyy-MM-dd",
      searchRange: true,
      width: 100,
      hide: true,
      rules: [
        {
          required: true,
          message: "日期为必填项",
          trigger: ["blur", "change"],
        },
      ],
      html: true,
    },
    {
      label: "批次时间",
      prop: "batchEndTime",
      type: "datetime",
      format: "yyyy-MM-dd HH:mm:ss",
      valueFormat: "yyyy-MM-dd HH:mm:ss",
      search: true,
      searchRange: true,
      width: 140,
      rules: [
        {
          required: true,
          message: "批次时间为必填项",
          trigger: ["blur", "change"],
        },
      ],
      html: true,
    },
    {
      label: "起始批次",
      prop: "startBatchCode",
      type: "input",
      hide: true,
      disabled: true,
      width: 60,
      search: true,
      viewDisplay: false,
      addDisplay: false,
    },
    {
      label: "终止批次",
      prop: "endBatchCode",
      type: "input",
      hide: true,
      disabled: true,
      width: 60,
      search: true,
      viewDisplay: false,
      addDisplay: false,
    },

    {
      label: "批次号",
      prop: "batchCode",
      type: "input",
      width: 120,
      overHidden: true,
      addDisplay: false,
    },
    {
      label: "起始批号",
      prop: "startBatchCode",
      type: "input",
      width: 180,
      hide: true,
      viewDisplay: false,
      editDisplay: false,
      rules: [
        {
          required: true,
          message: "起始批号为必填项",
          trigger: ["blur", "change"],
        },
        {
          pattern: /^\d{8}[A-H]$/,
          message: "批号格式错误（例：25063201A）",
          trigger: ["blur", "change"],
        },
      ],
      html: true,
    },
    {
      label: "终止批号",
      prop: "endBatchCode",
      type: "input",
      width: 180,
      hide: true,
      viewDisplay: false,
      editDisplay: false,
      rules: [
        {
          required: true,
          message: "终止批号为必填项",
          trigger: ["blur", "change"],
        },
        {
          pattern: /^\d{8}[A-H]$/,
          message: "批号格式错误（例：25063201A）",
          trigger: ["blur", "change"],
        },
      ],
      html: true,
    },

    {
      label: "发货编号",
      prop: "shipCode",
      type: "input",
      width: 180,
      hide: true,
      editDisplay: false,
    },
    {
      label: "备注",
      prop: "remark",
      type: "input",
      width: 180,
      hide: true,
      viewDisplay: false,
      editDisplay: false,
    },
    {
      label: "质检报告",
      prop: "inspectionStatus",
      type: "select",
      width: 100,
      display: false,
      hide: true,
      showColumn: false,
      html: true,
      search: true,
      searchType: "select",
      dicData: [
        { label: "未关联", value: 0 },
        { label: "已关联", value: 1 },
      ],
    },
    {
      label: "质检报告",
      prop: "inspectionCode",
      width: 110,
      overHidden: true,
      display: false,
    },
    {
      label: "国内/外",
      prop: "area",
      type: "select",
      width: 70,
      addDisplay: false,
      dicData: [
        {
          label: "国内",
          value: "CN",
        },
        {
          label: "国外",
          value: "OS",
        },
      ],
    },
    {
      label: "码垛号",
      prop: "palletNumber",
      type: "input",
      hide: true,
      width: 60,
      rules: [
        {
          required: true,
          message: "码垛号为必填项",
          trigger: ["blur", "change"],
        },
      ],
      html: true,
      addDisplay: false,
    },
    {
      label: "品牌",
      prop: "brandId",
      type: "select",
      dicUrl: "/api/blade-system/dict-biz/dictionary?code=ni_product_brand",
      props: {
        label: "dictValue",
        value: "id",
      },
      hide: true,
      showColumn: false,
      viewDisplay: false,
      allowCreate: true,
      filterable: true,
      overHidden: true,
      minWidth: 70,
      search: true,
      rules: [
        {
          required: true,
          message: "品牌为必填项",
          trigger: ["blur", "change"],
        },
      ],
      html: true,
    },
    {
      label: "品牌",
      prop: "brandName",
      type: "input",
      width: 70,
      disabled: true,
      editDisplay: false,
      addDisplay: false,
    },
    {
      label: "存货编码",
      prop: "materialCode",
      width: 100,
      overHidden: true,
    },
    {
      label: "外包装",
      prop: "outerPackagingId",
      type: "select",
      dicUrl: `/api/ni/product/packaging/list?innerPark=0&current=1&size=20&status=1&&name={{key}}`,
      props: {
        label: "name",
        value: "id",
        desc: "type",
      },
      search: true,
      remote: true,
      viewDisplay: false,
      addDisplay: false,
      dicFormatter: (data) => {
        return data.data.records;
      },
      filterable: true,
      hide: true,
      showColumn: false,
    },
    {
      label: "外包装",
      prop: "outerPackagingId",
      type: "select",
      dicUrl: `/api/ni/product/packaging/list?innerPark=0&current=1&size=20&status=1&&name={{key}}&&type=D`,
      props: {
        label: "name",
        value: "id",
        desc: "type",
      },
      remote: true,
      viewDisplay: false,
      dicFormatter: (data) => {
        return data.data.records;
      },
      filterable: true,
      hide: true,
      showColumn: false,
      rules: [
        {
          required: true,
          message: "外包装为必填项",
          trigger: ["blur", "change"],
        },
      ],
      html: true,
    },
    {
      label: "外包装",
      prop: "outerPackagingName",
      type: "input",
      width: 110,
      overHidden: true,
      disabled: true,
      editDisplay: false,
      addDisplay: false,
      html: true,
      formatter: function (row, column, cellValue, index) {
        const displayValue = cellValue || column.outerPackagingName || "";
        if (row.sku) {
          return `<span style="color:green">${displayValue}</span>`;
        } else {
          return `<span style="color:red">${displayValue}</span>`;
        }
      },
    },
    {
      label: "内包装",
      prop: "innerPackagingId",
      type: "select",
      dicUrl:
        "/api/ni/product/packaging/list?innerPark=1&current=1&size=20&status=1&&name={{key}}",
      props: {
        label: "name",
        value: "id",
        desc: "type",
      },
      search: true,
      viewDisplay: false,
      dicFormatter: (data) => {
        return data.data.records;
      },
      filterable: true,
      change: ({ value }) => {
        if (value) {
          console.log("选择");
        }
      },
      hide: true,
      showColumn: false,
      addDisplay: false,
    },
    {
      label: "内包装",
      prop: "innerPackagingName",
      type: "input",
      width: 110,
      overHidden: true,
      disabled: true,
      editDisplay: false,
      addDisplay: false,
    },

    {
      label: "规格",
      prop: "specificationId",
      type: "select",
      dicUrl: "/api/ni/product/spec/list",
      props: {
        label: "name",
        value: "id",
        desc: "type",
      },
      hide: true,
      showColumn: false,
      viewDisplay: false,
      allowCreate: true,
      filterable: true,
      overHidden: true,
      minWidth: 120,
      search: true,
      rules: [
        {
          required: true,
          message: "规格为必填项",
          trigger: ["blur", "change"],
        },
      ],
      html: true,
    },
    {
      label: "规格",
      prop: "specificationName",
      type: "input",
      width: 90,
      overHidden: true,
      disabled: true,
      editDisplay: false,
      addDisplay: false,
    },
    {
      label: "质量",
      prop: "qualityId",
      type: "select",
      dicUrl:
        "/api/blade-system/dict-biz/dictionary?code=ni_product_sku_quality_level",
      props: {
        label: "dictValue",
        value: "id",
        desc: "type",
      },
      hide: true,
      showColumn: false,
      viewDisplay: false,
      allowCreate: true,
      filterable: true,
      overHidden: true,
      minWidth: 100,
      search: true,
      rules: [
        {
          required: true,
          message: "质量为必填项",
          trigger: ["blur", "change"],
        },
      ],
      html: true,
    },
    {
      label: "质量",
      prop: "qualityName",
      type: "input",
      width: 80,
      overHidden: true,
      disabled: true,
      editDisplay: false,
      addDisplay: false,
    },

    {
      label: "计划箱数",
      prop: "planBoxQuantity",
      type: "input",
      width: 90,
      addDisplay: false,
    },
    {
      label: "实际箱数",
      prop: "actualBoxQuantity",
      type: "input",
      width: 90,
      addDisplay: false,
    },
    {
      label: "实际总重量",
      prop: "actualTotalCapacity",
      type: "input",
      width: 90,
      addDisplay: false,
    },
    {
      label: "允许发货",
      prop: "allowDelivery",
      type: "select",
      value: true,
      width: 80,
      dicData: [
        {
          label: "允许",
          value: true,
        },
        {
          label: "不允许",
          value: false,
        },
      ],
      html: true,
      formatter: function (val, value, label) {
        if (value === false) {
          return `<span style="color:red">${label}</span>`;
        } else {
          return `<span style="color:green">${label}</span>`;
        }
      },
      search: true,
      rules: [
        {
          required: true,
          message: "允许发货为必填项",
          trigger: ["blur", "change"],
        },
      ],
    },
    {
      label: "备货计划",
      prop: "inventoryPlanId",
      search: true,
      width: 80,
    },
    {
      label: "二维码样式",
      prop: "qrCodeStyle",
      type: "select",
      dicUrl:
        "/api/blade-system/dict-biz/dictionary?code=ni_tracing_qr_code_style",
      props: {
        label: "dictValue",
        value: "dictKey",
      },
      width: 90,
      display: false,
    },
  ],
};

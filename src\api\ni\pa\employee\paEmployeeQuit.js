import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/ni/pa/paEmployeeQuit/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/ni/pa/paEmployeeQuit/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/ni/pa/paEmployeeQuit/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/ni/pa/paEmployeeQuit/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/ni/pa/paEmployeeQuit/submit',
    method: 'post',
    data: row
  })
}

export const getDetailByProcessInsId = (processInsId) => {
  return request({
    url: '/api/ni/pa/paEmployeeQuit/detail',
    method: 'get',
    params: {
      processInsId
    }
  })
}

export const start = (row) => {
  return request({
    url: '/api/ni/pa/paEmployeeQuit/start',
    method: 'post',
    data: row
  })
}

export const saveAndStart = (processDefKey, row) => {
  return request({
    url: '/api/ni/pa/paEmployeeQuit/saveAndStart',
    method: 'post',
    params: {
      processDefKey
    },
    data: row
  })
}


import request from "@/router/axios";

export const getDetail = (id) => {
  return request({
    url: "/api/ni/project/dispatch/detail",
    method: "get",
    params: {
      id,
    },
  });
};
export const getList = (params) => {
  return request({
    url: "/api/ni/project/dispatch/list",
    method: "get",
    params,
  });
};

export const getPage = (current, size, params) => {
  return request({
    url: "/api/ni/project/dispatch/page",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};
/**
 * 指派人员
 */
export const assign = (
  employId,
  workDate,
  assignedTo,
  cancel,
  workingHours
) => {
  return request({
    url: "/api/ni/project/dispatch/assign",
    method: "post",
    params: {
      employId,
      workDate,
      assignedTo,
      cancel,
      workingHours,
    },
  });
};

export const assignHours = (id, workingHours) => {
  return request({
    url: "/api/ni/project/dispatch/assignHours",
    method: "post",
    params: {
      id,
      workingHours,
    },
  });
};

export const changeWorkDate = (ids, workDate) => {
  return request({
    url: "/api/ni/project/dispatch/changeWorkDate",
    method: "post",
    params: {
      ids,
      workDate,
    },
  });
};
export const overrule = (ids) => {
  return request({
    url: "/api/ni/project/dispatch/overrule",
    method: "post",
    params: {
      ids,
    },
  });
};

export const overruleCancel = (ids) => {
  return request({
    url: "/api/ni/project/dispatch/overruleCancel",
    method: "post",
    params: {
      ids,
    },
  });
};
export const publish = (ids, processDefKey) => {
  return request({
    url: "/api/ni/project/dispatch/publish",
    method: "post",
    params: {
      ids,
      processDefKey,
    },
  });
};
export const publishCancel = (ids) => {
  return request({
    url: "/api/ni/project/dispatch/publishCancel",
    method: "post",
    params: {
      ids,
    },
  });
};

export const finish = (data) => {
  return request({
    url: "/api/ni/project/dispatch/finish",
    method: "post",
    data,
  });
};
export const changeRemark = (id, remark) => {
  return request({
    url: "/api/ni/project/dispatch/changeRemark",
    method: "post",
    params: {
      id,
      remark,
    },
  });
};

export const referenceDispatch = (dispatchDate, employIds) => {
  return request({
    url: "/api/ni/project/dispatch/referenceDispatch",
    method: "post",
    params: {
      dispatchDate,
      employIds,
    },
  });
};
export const referenceDispatch1 = (dispatchDate, ids) => {
  return request({
    url: "/api/ni/project/dispatch/v1/referenceDispatch",
    method: "post",
    params: {
      dispatchDate,
      ids,
    },
  });
};
export const getItemList = (dispatchId) => {
  return request({
    url: "/api/ni/project/dispatch/getItemList",
    method: "get",
    params: {
      dispatchId,
    },
  });
};
export const changeWorkingHours = (data) => {
  return request({
    url: "/api/ni/project/dispatch/changeWorkingHours",
    method: "post",
    data,
  });
};

export const getPersonStateSummary = (dispatchDate, opType, deptCode) => {
  return request({
    url: "/api/ni/project/dispatch/getPersonStateSummary",
    method: "get",
    params: {
      dispatchDate,
      opType,
      deptCode,
    },
  });
};

export const changeOtherPersons = (userIds) => {
  return request({
    url: "/api/ni/project/dispatch/changeOtherPersons",
    method: "post",
    params: {
      userIds,
    },
  });
};
export const addPersonLeave = (data) => {
  return request({
    url: "/api/ni/project/dispatch/addPersonLeave",
    method: "post",
    data,
  });
};

export const deletePersonLeave = (ids) => {
  return request({
    url: "/api/ni/project/dispatch/deletePersonLeave",
    method: "post",
    params: {
      ids,
    },
  });
};
export const getPersonLeaveList = (current, size, deptCode,isLeaveY,params) => {
  return request({
    url: "/api/ni/project/dispatch/getPersonLeaveListByDept",
    method: "get",
    params: {
      ...params,
      current,
      size,
      deptCode,
      isLeaveY,
    },
  });
};

export const checkPersonIsAssign = (personId, startDate, endDate) => {
  return request({
    url: "/api/ni/project/dispatch/checkPersonIsAssign",
    method: "get",
    params: {
      personId,
      startDate,
      endDate,
    },
  });
};


export const getFooterSetting = (opType) => {
  return request({
    url: "/api/ni/project/dispatch/footerSetting",
    method: "get",
    params: {
      opType,
    },
  });
};


export const updateFooterSetting = (opType, data) => {
  return request({
    url: "/api/ni/project/dispatch/updateFooterSetting",
    method: "post",
    params: {
      opType,
    },
    data
  });
};

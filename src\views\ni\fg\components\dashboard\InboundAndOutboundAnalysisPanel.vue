<script>
import { dateFormat, getMonthFirst } from "@/util/date";

export default {
  props: {
    value: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      resizeTime: 1,
      currentMonth: this.getThisMonth(),
      inOutBar: null,
      dateRange: [],
      inOutBarOption: {
        grid: {
          left: 0,
          containLabel: true,
          bottom: 30,
          top: 10,
          right: 30,
        },
        legend: {
          y: "bottom",
          x: "center",
          type: "scroll",
          data: ["本月", "上月", "去年同期"],
        },
        xAxis: {
          type: "category",
          data: ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"],
        },
        yAxis: {
          type: "value",
          min: 0,
        },
        series: [],
      },
    };
  },
  beforeDestroy() {
    if (this.inOutBar) {
      this.inOutBar.dispose();
      this.inOutBar = null;
    }
  },
  mounted() {
    this.dateRange = [
      getMonthFirst(new Date()),
      dateFormat(new Date(), "yyyy-MM-dd"),
    ];
    this.$nextTick(() => {
      this.getTransactionItemLine(this.dateRange);
    });
    window.addEventListener("resize", this.updateContainer, false);
  },
  watch: {
    value: {
      deep: true,
      handler(newVal) {
        if (newVal) {
          this.$nextTick(() => {
            this.renderCharts();
          });
        }
      },
    },
  },
  methods: {
    reload() {
      this.getTransactionItemLine(this.dateRange);
    },
    getTransactionItemLine(dateRange) {
      const [start, end] = dateRange;
      this.$emit("on-load", start, end);
    },
    getThisMonth(checkedValues = "") {
      let date;
      if (!checkedValues || checkedValues.length === 0) {
        date = new Date();
        return `${date.getFullYear()}-${date.getMonth() + 1}`;
      }
      date = new Date(checkedValues[0]);
      const date2 = new Date(checkedValues[1]);
      const startMonth =
        date.getMonth() + 1 > 9
          ? date.getMonth() + 1
          : `0${date.getMonth() + 1}`;
      const endMonth =
        date2.getMonth() + 1 > 9
          ? date2.getMonth() + 1
          : `0${date2.getMonth() + 1}`;

      return `${date.getFullYear()}-${startMonth}  至  ${date2.getFullYear()}-${endMonth}`;
    },
    renderCharts() {
      if (!this.inOutBarContainer) {
        this.inOutBarContainer = document.getElementById("inOutBarContainer");
      }
      if (!this.inOutBar) {
        this.inOutBar = this.$echarts.init(this.inOutBarContainer);
      }
      // 提取日期作为 X 轴数据
      let xAxisData = [];
      if (this.value.length > 0) {
        const uniqueDates = [...new Set(this.value.map((item) => item.date))];
        xAxisData = uniqueDates.sort((a, b) => a.localeCompare(b));
      }

      // 构建数据结构：按 typeText 分组，保存 weight 和 stack
      const seriesData = {};
      this.value.forEach((item) => {
        const { date, typeText, stack, weight } = item;

        // 初始化系列
        if (!seriesData[typeText]) {
          seriesData[typeText] = {
            weight: {}, // 日期 -> weight
            stack: stack, // 该 typeText 所有数据的 stack（应一致）
          };
        }
        // 填充 weight 数据
        seriesData[typeText].weight[date] = weight;
      });

      // 构建 ECharts 系列
      const series = Object.keys(seriesData).map((typeText) => {
        const { weight, stack } = seriesData[typeText];
        return {
          name: typeText,
          type: "bar",
          stack: stack, // 使用该 typeText 的 stack 分组
          barWidth: stack === "in" ? 5 : null,
          yAxisIndex: 0, // 重量在第一个 Y 轴
          data: xAxisData.map((date) => weight[date] || 0), // 按日期填充数据
          label: {
            show: false,
          },
        };
      });

      // 构建配置
      const option = {
        tooltip: {
          trigger: "axis",
          formatter: (params) => {
            const xAxisValue = params[0].axisValue;
            const validParams = params.filter((item) => item.value !== 0);
            if (validParams.length === 0) {
              return `<div style="padding: 4px 8px; background: #fff; border: 1px solid #ccc;">无有效数据</div>`;
            }

            const rows = validParams.map((item) => {
              return `<span style="display: inline-block; margin: 4px 0;">${
                item.marker
              } ${item.seriesName}: ${Number(item.value).toLocaleString(
                "zh-CN"
              )} kg</span>`;
            });

            return `      <div style="padding: 4px 8px; background: #fff; border: 1px solid #ccc;">
        <strong>日期：${xAxisValue}</strong><br/>
        ${rows.join("<br/>")}      </div>
    `;
          },
        },
        grid: this.inOutBarOption.grid,
        legend: {
          y: "bottom",
          x: "center",
          type: "scroll",
          data: Object.keys(seriesData).filter((typeText) => {
            const weight = seriesData[typeText].weight;
            return xAxisData.some((date) => Number(weight[date] || 0) !== 0);
          }),
        },
        xAxis: {
          type: "category",
          data: xAxisData,
        },
        yAxis: [
          {
            type: "value",
            name: "kg",
            min: 0,
          },
        ],
        series: series,
      };
      // 渲染图表
      this.inOutBar.setOption(option, true);
    },
    updateContainer() {
      if (
        document.documentElement.clientWidth >= 1400 &&
        document.documentElement.clientWidth < 1920
      ) {
        this.resizeTime = (document.documentElement.clientWidth / 2080).toFixed(
          2
        );
      } else if (document.documentElement.clientWidth < 1080) {
        this.resizeTime = (document.documentElement.clientWidth / 1080).toFixed(
          2
        );
      } else {
        this.resizeTime = 1;
      }
      this.inOutBar.resize({
        // 根据父容器的大小设置大小
        width: this.inOutBarContainer.clientWidth,
        height: `${this.resizeTime * 526}px`,
      });
    },
  },
};
</script>

<template>
  <el-card
    shadow="never"
    :body-style="{ padding: '10px 20px' }"
    :class="{
      'board-item': true,
    }"
  >
    <div slot="header" class="clearfix">
      <span>出入库概览</span>
      <el-date-picker
        style="float: right; padding: 3px 0"
        v-model="dateRange"
        size="mini"
        type="daterange"
        value-format="yyyy-MM-dd"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        @change="getTransactionItemLine"
      >
      </el-date-picker>
    </div>
    <div
      id="inOutBarContainer"
      :style="{ width: '100%', height: `${resizeTime * 526}px` }"
    ></div>
  </el-card>
</template>

<style lang="scss" scoped>
.board-item {
  padding: 8px;

  /deep/ .el-card__header {
    color: #303133;
    font-size: 20px;
    font-weight: 500;
    border-bottom: 0;
    padding: 16px 18px !important;
  }
}
</style>

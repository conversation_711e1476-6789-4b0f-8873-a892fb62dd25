<template>
  <basic-container>
    <avue-skeleton :loading="waiting" avatar :rows="8">
      <div style="display: flex;">
        <avue-title
          style="margin-bottom: 20px"
          :styles="{ fontSize: '20px' }"
          :value="process.name"
        ></avue-title>
        <el-badge v-if="permission.wf_process_draft&&draftCount > 0" :value="draftCount"
                  style="margin-top: 5px;  margin-right: 40px;" type="warning">
          <el-button
            size="mini"
            v-loading="loading"
            @click="handleDraftBox"
          >草稿箱
          </el-button>
        </el-badge>
      </div>
      <el-card shadow="never" style="margin-top: 20px">
        <!-- 自定义表单区域 -->
        <avue-form
          v-if="
            option &&
            ((option.column && option.column.length > 0) ||
              (option.group && option.group.length > 0))
          "
          v-model="form"
          ref="form"
          :option="option"
          :defaults.sync="defaults"
          @submit="handleSubmit"
          @error="loading = false"
          :upload-preview="handleUploadPreview"
        >
          <!-- 自定义表单区域 -->
          <template #items="{ row, disabled }">
            <div style="overflow: hidden">
              <avue-crud
                style="margin-left: 1px"
                :option="item.option"
                :data="form.items"
                @selection-change="itemSelectionChange"
                ref="itemCrud"
                @cell-click="itemCellClickChange"
                :upload-preview="handleUploadPreview"
              >
                <template #pv="{ row, index }">
                  <el-tag size="mini" type="danger" effect="dark" v-if="row.pv">
                    是
                  </el-tag>
                  <el-tag size="mini" type="info" effect="plain" v-else>
                    否
                  </el-tag>
                </template>
                <template #fa="{ row, size, index, dic }">
                  <el-dropdown
                    trigger="click"
                    @command="rowFaChange($event, row, index)"
                  >
                    <el-tag
                      size="mini"
                      v-if="row.fa"
                      type="danger"
                      effect="dark"
                      style="cursor: pointer"
                    >
                      是
                    </el-tag>
                    <el-tag
                      size="mini"
                      v-else
                      type="info"
                      effect="plain"
                      style="cursor: pointer"
                      >否
                    </el-tag>
                    <i class="el-icon-arrow-down el-icon--right"></i>
                    <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item
                        v-for="item in dic"
                        :key="item.value"
                        :disabled="item.value === row.fa"
                        :command="item.value"
                        >{{ item.label }}
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </el-dropdown>
                </template>
                <template #attach="{ row, index }">
                  <el-link v-if="row.attach" type="primary" target="_blank">
                    <i class="el-icon-circle-plus-outline" />
                    附件({{ row.attach ? row.attach.length : 0 }})
                  </el-link>
                </template>
              </avue-crud>
            </div>
          </template>
        </avue-form>
      </el-card>
      <el-card shadow="never" style="margin-top: 20px" v-if="showExamForm">
        <wf-examine-form
          ref="examineForm"
          :process="process"
          @user-select="handleUserSelect"
        ></wf-examine-form>
      </el-card>
      <div style="height: 120px"></div>
      <el-row
        class="foot-item avue-affix"
        :style="{
          width: isCollapse ? 'calc(100% - 80px)' : 'calc(100% - 260px)',
        }"
        id="avue-view"
      >
        <el-button
          v-no-more-click
          type="primary"
          size="mini"
          v-loading="loading"
          @click="handleSubmit"
          >发起
        </el-button>
        <el-button
          v-if="permission.wf_process_draft"
          type="success"
          size="mini"
          v-loading="loading"
          @click="handleDraftNotClose(process.id, process.formKey, form, process.key)"
          >存为草稿
        </el-button>
        <el-button
          v-if="permission.wf_process_draft"
          type="success"
          size="mini"
          v-loading="loading"
          @click="handleDraft(process.id, process.formKey, form, process.key)"
          >存为草稿并关闭
        </el-button>
      </el-row>
    </avue-skeleton>

    <!-- 人员选择弹窗 -->
    <wf-user-select
      ref="user-select"
      :check-type="checkType"
      :default-checked="defaultChecked"
      @onConfirm="handleUserSelectConfirm"
    ></wf-user-select>
    <!-- 草稿弹窗 -->
    <draft-popup
      :visible.sync="isDraftPopupVisible"
      :draftList="draftList"
      @select="handleDraftSelect"
      @delete="handleDraftDelete"
    ></draft-popup>
  </basic-container>
</template>

<script>
import WfExamineForm from "@/views/plugin/workflow/process/components/examForm.vue";
import WfUserSelect from "@/views/plugin/workflow/process/components/user-select";
import exForm from "@/views/plugin/workflow/mixins/ex-form";
import draft from "@/views/plugin/workflow/mixins/draft";
import UserSelect from "@/components/user-select";
import { changeFa } from "@/api/ni/por/apply-item";
import DraftPopup from "@/views/plugin/workflow/process/components/draftPopup.vue";
import debounce from "@/util/debounce";

export default {
  components: {
    WfUserSelect,
    WfExamineForm,
    UserSelect,
    DraftPopup
  },
  mixins: [exForm, draft],
  activated() {
    let val = this.$route.query.p;
    if (val) {
      let text = Buffer.from(val, "base64").toString();
      text = text.replace(/[\r|\n|\t]/g, "");
      const param = JSON.parse(text);
      const { processId, processDefKey, form } = param;
      if (form) {
        const f = JSON.parse(
          new Buffer(decodeURIComponent(form), "utf8").toString("utf8")
        );
        this.form = Object.assign(this.form, f);
      }
      if (processId) {
        this.getForm(processId);
      } else if (processDefKey) {
        this.getFormByProcessDefKey(processDefKey);
      }
    }
  },
  computed: {
    showExamForm() {
      const { hideComment, hideCopy, hideExamine } = this.process;
      return !hideComment || !hideCopy || !hideExamine;
    },
  },
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      //  这里的vm指的就是vue实例，可以用来当做this使用
      vm.fromPath = from.path; //获取上一级路由的路径
    });
  },
  data() {
    return {
      orderSelectShow: false,
      fromPath: "",
      defaults: {},
      form: {
        items: [],
      },
      option: {
        header: false,
        cellBtn: true,
        addBtn: false,
        refreshBtn: false,
        columnBtn: false,
        menu: false,
        saveBtn: false,
        updateBtn: false,
        cancelBtn: false,
        editBtn: false,
        delBtn: false,
        submitBtn: false,
        emptyBtn: false,
        dialogFullscreen: true,
        size: "mini",
        align: "center",
        calcHeight: 30,
        tip: false,
        border: true,
        viewBtn: false,
        dialogClickModal: false,
        showSummary: true,
        selection: false,
        sumColumnList: [
          {
            name: "num",
            type: "sum",
            decimals: 1,
          },
          {
            name: "amount",
            type: "sum",
          },
        ],
        column: [
          {
            label: "",
            prop: "items",
            labelPosition: "top",
            span: 24,
          },
        ],
      },
      item: {
        selectionList: [],
        option: {
          header: false,
          cellBtn: true,
          addBtn: false,
          refreshBtn: false,
          columnBtn: false,
          menu: false,
          saveBtn: false,
          updateBtn: false,
          cancelBtn: false,
          editBtn: false,
          delBtn: false,
          dialogFullscreen: true,
          size: "mini",
          align: "center",
          calcHeight: 30,
          tip: false,
          border: true,
          viewBtn: false,
          dialogClickModal: false,
          selection: false,
          showSummary: true,
          sumColumnList: [
            {
              name: "num",
              type: "sum",
              decimals: 1,
            },
            {
              name: "amount",
              type: "sum",
            },
          ],
          column: [
            {
              label: "固定资产",
              prop: "fa",
              type: "radio",
              width: 110,
              dicData: [
                {
                  label: "是",
                  value: 1,
                },
                {
                  label: "否",
                  value: 0,
                },
              ],
            },
            {
              label: "品名",
              prop: "materialName",
              //   type: "textarea",
              //   minRows: 1,
              //   placeholder: " ",
              overHidden: true,
              cell: true,
            },
            {
              label: "账套",
              prop: "brand",
              overHidden: true,
              cell: true,
              dicUrl: "/api/blade-system/dict-biz/dictionary?code=ni_brand",
              disabled: true,
              props: {
                label: "dictValue",
                value: "dictKey",
              },
            },
            {
              label: "用途",
              prop: "purpose",
              //   type: "textarea",
              //   minRows: 1,
              //   placeholder: " ",
              overHidden: true,
              cell: true,
            },
            {
              label: "编码",
              minWidth: 100,
              placeholder: " ",
              prop: "materialCode",
              overHidden: true,
              clearable: false,
              cell: true,
            },
            {
              label: "规格",
              prop: "specification",
              placeholder: " ",
              overHidden: true,
              disabled: true,
            },
            {
              label: "材质",
              prop: "quality",
              placeholder: " ",
              disabled: true,
              overHidden: true,
            },
            {
              label: "国标",
              prop: "gb",
              placeholder: " ",
              disabled: true,
              overHidden: true,
            },
            {
              label: "单位",
              prop: "unit",
              type: "select",
              dicUrl:
                "/api/blade-system/dict-biz/dictionary?code=ni_material_unit",
              props: {
                label: "dictValue",
                value: "dictKey",
              },
              placeholder: " ",
              slot: true,
            },
            {
              label: "数量",
              prop: "arrivalNum",
              type: "number",
              precision: 0,
              placeholder: " ",
              minWidth: 100,
              cell: true,
              rules: [
                {
                  required: true,
                  message: "请输入数量",
                  trigger: "blur",
                },
              ],
            },
            {
              label: "单价",
              prop: "price",
              type: "number",
              controls: false,
              disabled: true,
              precision: 2,
              cell: true,
              placeholder: " ",
            },
            {
              label: "金额",
              prop: "arrivalAmount",
              overHidden: true,
              type: "number",
              cell: true,
              minWidth: 100,
              precision: 2,
              placeholder: " ",
            },
            {
              label: "压力容器",
              prop: "pv",
              cell: true,
              type: "radio",
              width: 110,
              dicData: [
                {
                  label: "是",
                  value: 1,
                },
                {
                  label: "否",
                  value: 0,
                },
              ],
            },
            {
              label: "申请人",
              prop: "applyUserName",
              cell: true,
            },
            {
              label: "采购人",
              prop: "purchaseUserName",
              cell: true,
              display: true,
              disabled: true,
            },
            {
              label: "供应商",
              prop: "supplier",
              cell: true,
              display: true,
              disabled: true,
            },
            {
              label: "项目编号",
              prop: "projectSerialNo",
              cell: true,
              display: true,
              disabled: true,
            },
            {
              label: "采购申请编号",
              prop: "applySerialNo",
              cell: true,
              disabled: true,
              display: true,
            },
            {
              label: "备注",
              prop: "remark",
              type: "textarea",
              minRows: 1,
              placeholder: " ",
              cell: true,
            },
            {
              label: "附件",
              type: "upload",
              width: 94,
              propsHttp: {
                res: "data",
                url: "attachId",
                name: "originalName",
              },
              cell: true,
              action:
                "/api/blade-resource/oss/endpoint/put-file-attach?code=public",
              display: true,
              showFileList: true,
              multiple: true,
              limit: 10,
              prop: "attach",
            },
          ],
        },
      },
      process: {},
      loading: false,
      payment: 0,
      typeDict: [],
      typeDictKeyValue: {},
      isDraftPopupVisible: false,
      draftList: [],
      draftCount: 0,
      draftId: null
    };
  },
  methods: {
    rowFaChange(fa, row, index) {
      const ids = row.applyItemId != null ? row.applyItemId : row.porApplyItemId;
      console.log(ids);
      changeFa(ids, fa).then(() => {
        this.form.items[index].fa = fa;
        this.$message({
          type: "success",
          message: "操作成功!",
        });
      });
    },
    getForm(processId) {
      this.getStartForm(processId).then((res) => {
        const _this = this;
        let { process } = res;
        this.form.processId = process.id;
        const option = _this.option;
        const { column, group } = option;
        console.log(column)
        column.forEach((col) => {
          if (col.value) col.value = _this.getDefaultValues(col.value);
        });
        if (group && group.length > 0) {
          // 处理group
          group.forEach((gro) => {
            gro.column.forEach((col) => {
              if (col.value) col.value = _this.getDefaultValues(col.value);
            });
          });
        }
        if (_this.permission.wf_process_draft) {
          // 查询草稿箱
          this.initDraft(process.id,process.key).then((data) => {
          this.draftCount = data.length;
          this.draftList = data;
            if (data && Array.isArray(data) && data.length > 0 ) {
              _this.$confirm("是否引用之前保存的草稿？", "提示", {})
              .then(() => {
                this.isDraftPopupVisible = true; // 打开草稿弹窗
              });
            }
          });
        }
        _this.waiting = false;
      });
    },
    getFormByProcessDefKey(processDefKey) {
      this.getStartFormByProcessDefKey(processDefKey).then((res) => {
        const _this = this;
        let { process } = res;
        _this.form.processId = process.id;
        const option = _this.option;
        const { column, group } = option;
        column.forEach((col) => {
          if (col.value) col.value = _this.getDefaultValues(col.value);
        });
        if (group && group.length > 0) {
          // 处理group
          group.forEach((gro) => {
            gro.column.forEach((col) => {
              if (col.value) col.value = _this.getDefaultValues(col.value);
            });
          });
        }
        if (_this.permission.wf_process_draft) {
          // 查询草稿箱
          this.initDraft(process.id,process.key).then((data) => {
          this.draftCount = data.length;
          this.draftList = data;
            if (data && Array.isArray(data) && data.length > 0 ) {
              _this.$confirm("是否引用之前保存的草稿？", "提示", {})
              .then(() => {
                this.isDraftPopupVisible = true; // 打开草稿弹窗
              });
            }
          });
        }
        _this.waiting = false;
      });
    },
    handleSubmit:debounce(function () {
      this.loading = true;
      this.form.draftId = this.draftId;
      this.handleStartProcess(true)
        .then((done) => {
          this.$message.success("发起成功");
          if(this.draftId != null){
            this.draftCount = this.draftCount-1;
            this.draftList = this.draftList.filter(item => item.id !== this.draftId);
          }
          if (this.fromPath) {
            this.handleCloseTag(this.fromPath);
          } else this.handleCloseTag("/plugin/workflow/process/send");
          done();
        })
        .catch(() => {
          this.loading = false;
        });
    },1000),
    getDefaultValues(value) {
      let defaultValue = "";
      if (value.toString().includes("${") && value.toString().includes("}")) {
        try {
          defaultValue = eval("`" + value + "`");
        } catch (err) {
          defaultValue = value;
        }
      } else defaultValue = value;

      return defaultValue;
    },

    //选择草稿
    handleDraftSelect(selectedDraft) {
      //草稿版本与流程版本不一致
      if(!selectedDraft.sameVersion){
        this.$confirm("选中的草稿与当前流程版本不一致，是否继续引用？", "提示", {})
        .then(() => {
          this.draftId = selectedDraft.id;
          this.form = JSON.parse(selectedDraft.variables);
          this.isDraftPopupVisible = false;
        });
      } else {
        this.draftId = selectedDraft.id;
        this.form = JSON.parse(selectedDraft.variables);
      }
    },
    //删除草稿
    handleDraftDelete(draftId) {
      this.$confirm("是否删除选中的草稿箱数据？", "提示", {})
        .then(() => {
          this.$axios.post(`/api/blade-workflow/process/draft/delete/${draftId}`)
          .then(response => {
            this.$message.success('草稿删除成功');
            this.draftCount = this.draftCount-1;
            this.draftList = this.draftList.filter(item => item.id !== draftId);
          })
          .catch(error => {
            this.$message.error('草稿删除失败，请重试');
          });
      })
    },
    handleDraftBox() {
      if (this.draftList.length > 0) {
        this.isDraftPopupVisible = true;
      } else {
        // 重新获取草稿数据
        this.initDraft(this.form.processId).then((data) => {
          this.draftCount = data.length;
          this.draftList = data;
          if (data && Array.isArray(data) && data.length > 0) {
            this.isDraftPopupVisible = true;
          }
        });
      }
    },

  },
};
</script>

<style lang="scss" scoped>
.foot-item {
  position: fixed;
  bottom: 0;
  margin-left: -20px;
  // right: 0;
  z-index: 101;
  height: 66px;
  background-color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  -webkit-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
</style>

import request from '@/router/axios';

export const getDetail = (id) => {
  return request({
    url: '/api/EmployeeResumeInfo/niEmployeeResumeInfo/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/EmployeeResumeInfo/niEmployeeResumeInfo/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const batchUpload = (row) => {
  return request({
    url: '/api/EmployeeResumeInfo/niEmployeeResumeInfo/batch',
    method: 'post',
    data: row
  })
}
/**
 * 分页查询
 * @param current
 * @param size
 * @param params
 * @returns {*}
 */
export const page = (current, size, params) => {
  return request({
    url: '/api/EmployeeResumeInfo/niEmployeeResumeInfo/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}
/**
 * 重新分析
 * @param params
 * @returns {*}
 */
export const reanalysis = (params) => {
  return request({
    url: '/api/EmployeeResumeInfo/niEmployeeResumeInfo/reanalysis',
    method: 'post',
    params: {
      ...params,
    }
  })
}
/**
 * 用人部门感兴趣
 * @param params
 * @returns {*}
 */
export const interested = (params) => {
  return request({
    url: '/api/EmployeeResumeInfo/niEmployeeResumeInfo/interested',
    method: 'post',
    params: {
      ...params,
    }
  })
}

/**
 * HR录入邀请面试信息
 * @param params
 * @returns {*}
 */
export const invite = (params) => {
  return request({
    url: '/api/EmployeeResumeInfo/niEmployeeResumeInfo/invite',
    method: 'post',
    params: {
      ...params,
    }
  })
}
/**
 * 邀请记录
 * @param id
 * @returns {*}
 */
export const inviteRecord = (id) => {
  return request({
    url: '/api/EmployeeResumeInfo/niEmployeeResumeInfo/inviteRecord',
    method: 'get',
    params: {
      id,
    }
  })
}
/**
 * 面试评价
 * @param vo
 * @returns {*}
 */
export const evaluation = (vo) => {
  return request({
    url: '/api/EmployeeResumeInfo/niEmployeeResumeInfo/evaluation',
    method: 'post',
    data: vo
  })
}

/**
 * 过滤用户
 * @param name
 * @returns {AxiosPromise}
 */
export const filterUser = (name) => {
  return request({
    url: '/api/blade-user/search/otj/user',
    method: 'get',
    params: {
      name
    }
  })
}
/**
 * 岗位字典
 * @param deptIds
 * @returns {AxiosPromise}
 */
export const getJobs = (deptIds) => {
  return request({
    url: '/api/blade-system/post/select',
    method: 'get',
    params: {
      deptIds,
    }
  })
}
/**
 * 入职
 * @param id
 * @param phone
 * @returns {AxiosPromise}
 */
export const onboarding = (id,phone) => {
  return request({
    url: '/api/EmployeeResumeInfo/niEmployeeResumeInfo/onboarding',
    method: 'post',
    params: {
      id,
      phone
    }
  })
}





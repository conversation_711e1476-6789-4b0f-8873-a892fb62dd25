<script>
import {getBeforeBatchNo, getList} from "@/api/ni/fg/fgInventory";

export default {
  name: "SkuSelectDialog",
  props: {
    params: {
      type: Object,
      default: () => {
      },
    },
    checkBefore: {
      type: Boolean,
      default: true,
    },
    multiple: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      visible: false,
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      selectionList: [],
      option: {
        menu: false,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        size: "mini",
        searchSize: "mini",
        align: "center",
        searchIndex: 3,
        searchIcon: true,
        searchEnter: true,
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        selection: true,
        dialogClickModal: false,
        column: [
          {
            label: "",
            prop: "radio",
            type: "radio",
            width: 55,
            hide: true,
          },
          {
            label: "仓库",
            prop: "depotId",
            type: "select",
            dicUrl: "/api/ni/base/depot/info/list?status=2&type=1",
            props: {
              label: "name",
              value: "id",
              desc: "code",
            },
            search: true,
            minWidth: 100,
            overHidden: true
          },
          {
            label: '存货编码',
            prop: 'materialCode',
            placeholder: " ",
            width: 110,
            search: true,
            searchOrder: 99,
            overHidden: true
          },

          {
            label: '规格',
            prop: 'specText',
            placeholder: " ",
            display: false,
            overHidden: true,
            width: 120,
            search: true,
            searchOrder: 98
          },
          {
            label: '质量',
            prop: 'qualityLevel',
            type: 'select',
            dicUrl: "/api/blade-system/dict-biz/dictionary?code=ni_product_sku_quality_level",
            props: {
              label: "dictValue",
              value: "dictKey",
              desc: 'dictKey'
            },
            placeholder: " ",
            width: 100,
          },
          {
            label: '外包装',
            prop: 'packageText',
            placeholder: " ",
            display: false,
            overHidden: true,
            width: 115,
            search: true,
            searchOrder: 97
          },
          {
            label: '内包装',
            prop: 'innerPackageText',
            placeholder: " ",
            display: false,
            overHidden: true,
            width: 115
          },
          {
            label: '唛头',
            prop: 'currentMarkText',
            placeholder: " ",
            overHidden: true,
            hide: true,
            width: 110
          },
          {
            label: '批号',
            prop: 'batchNo',
            placeholder: " ",
            minWidth: 110,
            overHidden: true,
            search: true,
            searchOrder: 96
          },
          {
            label: "可用箱数",
            prop: "num",
            minWidth: 80,
            type: 'number',
          },
          {
            label: '重量',
            prop: 'weight',
            placeholder: " ",
            type: 'number',
            minWidth: 80,
          },
          {
            label: "单位",
            prop: "unit",
            type: "select",
            filterable: true,
            width: 80,
            dicUrl: "/api/blade-system/dict-biz/dictionary?code=ni_material_unit",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            slot: true,
            placeholder: " ",
          },
          {
            label: '品名',
            prop: "skuText",
            placeholder: " ",
            overHidden: true,
            minWidth: 150,
          },
          {
            label: "生产日期",
            prop: "productionDate",
            type: "date",
            placeholder: " ",
            minWidth: 100,
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            fixed: 'right',
            search: true,
            searchRange: true
          },
          {
            label: "备注",
            prop: "remark",
            type: "textarea",
            minWidth: 100,
            span: 24,
            search: true,
          },
          {
            label: "状态",
            prop: "status",
            type: 'select',
            dicUrl: "/api/blade-system/dict-biz/dictionary?code=ni_fg_inventory_status",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            fixed: 'right',
            dataType: "number",
            minWidth: 100,
            search: true,
          },
        ]
      },
      data: [],
      form: {},
      query: {},
      param: {},
      status: 'all',
      qualityLevelDictKeyValue: {},
      qualityLevelColorMap: {
        'A': '#67C23A', // 高吸附 - 绿色
        'P': '#409EFF', // 优等品 - 蓝色
        'Q': '#E6A23C', // 合格品 - 橙色
      },
    }
  },
  methods: {
    onShow(param) {
      if (!this.multiple) {
        this.$set(this.option, "selection", false);
        this.findObject(this.option.column, "radio").hide = false;
      } else {
        this.$set(this.option, "selection", true);
        this.findObject(this.option.column, "radio").hide = true;
      }
      this.param = param
      this.query = {}
      this.page.currentPage = 1
      this.visible = true
    },
    rowClick(row) {
      if (!this.multiple) {
        this.selectionList = [row];
        this.$set(this.form, "radio", row.id);
      } else this.$refs.crud.toggleSelection([row]);
    },
    handleClose(done) {
      this.visible = false;
      if (done && typeof done == "function") done();
    },
    async handleConfirm() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      const before = await this.checkBefore1(this.selectionList);
      if (this.checkBefore && before && before.length > 0) {
        this.$confirm('选择的库存中有更早的库存, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$emit("confirm", this.selectionList);
          this.handleClose();
        })
      } else {
        this.$emit("confirm", this.selectionList);
        this.handleClose();
      }
    },
    async checkBefore1(selectionList) {
      let ids = new Set();
      selectionList.forEach((ele) => {
        ids.add(ele.id);
      });
      const res = await getBeforeBatchNo(selectionList[0].depotId, Array.from(ids).join(","))
      return res.data.data
    },
    searchChange(params, done) {
      this.query = params
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    searchReset() {
      this.query = {}
      this.page.currentPage = 1;
      this.onLoad(this.page);
    },
    selectionClear() {
      this.selectionList = [];
      if (this.$refs.crud) this.$refs.crud.toggleSelection();
    },
    onLoad(page, params = {}) {
      this.loading = true;
      const q = {
        ...params,
        ...this.query,
        ...this.params,
        ...this.param
      }
      if (q.productionDate && q.productionDate.length === 2) {
        q.startProductionDate = q.productionDate[0]
        q.endProductionDate = q.productionDate[1]
        q.productionDate = null
      }
      if (this.status !== 'all') {
        q.status = this.status
      }
      if (!q.descs && !q.ascs)
        q.ascs = "production_date"
      getList(page.currentPage, page.pageSize, q).then(res => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
    cellStyle({row, column}) {
      if ("status" === column.columnKey && row.status === 2) {
        return {
          backgroundColor: '#409EFF',
          color: "#fff",
        }
      }
      if ("qualityLevel" === column.columnKey) {
        const color = this.qualityLevelColorMap[row.qualityLevel];
        return {
          backgroundColor: color || "#909399", // 默认颜色为灰色
          color: "#fff",
        }
      }
    },
  }
}
</script>

<template>
  <el-dialog v-dialogDrag :visible.sync="visible" append-to-body width="1100px">
    <avue-crud
      ref="crud"
      v-if="visible"
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      v-model="form"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionList = $event"
      @current-change="page.currentPage = $event"
      @size-change="page.pageSize = $event"
      @row-click="rowClick"
      :cell-style="cellStyle"
      @on-load="onLoad"
    >
      <template v-if="!multiple" #radio="{ row }">
        <el-radio v-model="form.radio" :label="row.id"><i></i></el-radio>
      </template>
      <template #menuLeft>
        <el-radio-group v-model="status" size="mini" @input="onLoad(page)">
          <el-radio-button label="all">全部</el-radio-button>
          <el-radio-button label="1">正常</el-radio-button>
          <el-radio-button label="2">冻结</el-radio-button>
        </el-radio-group>
      </template>
    </avue-crud>
    <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose" size="mini">取 消</el-button>
        <el-button type="primary" @click="handleConfirm" size="mini"
        >确 定</el-button
        >
      </span>
  </el-dialog>
</template>

<style scoped>

</style>

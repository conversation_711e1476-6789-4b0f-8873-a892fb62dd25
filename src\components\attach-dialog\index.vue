<template>
  <el-drawer
    title="附件信息"
    :visible.sync="visible"
    :direction="direction"
    size="65%"
    append-to-body
    :before-close="handleClose"
  >
    <basic-container>
      <avue-crud
        v-if="visible"
        :option="option"
        :table-loading="loading"
        :data="data"
        :page.sync="page"
        :before-open="beforeOpen"
        v-model="form"
        ref="crud"
        @row-update="rowUpdate"
        @row-save="rowSave"
        @row-del="rowDel"
        @search-change="searchChange"
        @search-reset="searchReset"
        @selection-change="selectionChange"
        @current-change="currentChange"
        @size-change="sizeChange"
        @refresh-change="refreshChange"
        @on-load="onLoad"
        :upload-preview="uploadPreview"
      >
        <template slot-scope="{ row }" slot="attachSize">
          <el-tag size="mini">{{ `${row.attachSize} KB` }}</el-tag>
        </template>
        <template slot="menuLeft">
          <el-button
            type="primary"
            size="mini"
            plain
            v-if="!detail"
            icon="el-icon-upload2"
            @click="handleUpload"
            >上 传
          </el-button>
          <el-button
            type="danger"
            size="mini"
            icon="el-icon-delete"
            v-if="!detail && option.delBtn"
            plain
            @click="handleDelete"
            >删 除
          </el-button>
        </template>
        <template slot-scope="scope" slot="menu">
          <el-button
            type="text"
            icon="el-icon-search"
            size="mini"
            @click="handleView(scope.row)"
            >预览
          </el-button>
          <el-button
            type="text"
            icon="el-icon-download"
            size="mini"
            @click="handleDownload(scope.row)"
            >下载
          </el-button>
        </template>
      </avue-crud>
    </basic-container>
    <el-dialog
      title="附件管理"
      append-to-body
      :visible.sync="attachBox"
      width="555px"
    >
      <avue-form
        v-if="attachBox"
        ref="form"
        :option="attachOption"
        v-model="attachForm"
        :upload-after="uploadAfter"
      >
      </avue-form>
    </el-dialog>
  </el-drawer>
</template>

<script>
import {
  add,
  fileLink,
  getDetail,
  getPage,
  remove,
  update,
} from "@/api/resource/attach";
import { dateNow1 } from "@/util/date";
import { genSn } from "@/api/ni/base/serialno";
import { downloadFileBlob, handleUploadPreview } from "@/util/util";
import { Base64 } from "js-base64";

export default {
  name: "attachDialog",
  props: {
    detail: {
      type: Boolean,
      default: false,
    },
    code: {
      type: String,
      default: "private",
    },
    delBtn: {
      type: Boolean,
      default: true,
    },
  },
  created() {
    const column = this.findObject(this.attachOption.column, "attachFile");
    column.action += this.code;
  },
  watch: {
    detail: function (val) {
      if (val) {
        this.option.editBtn = false;
        this.option.delBtn = false;
      } else {
        this.option.editBtn = true;
        this.option.delBtn = true;
      }
    },
    delBtn: {
      handler(val) {
        this.option.delBtn = val;
      },
      immediate: true,
    },
  },
  data() {
    return {
      attachBox: false,
      businessKey: null,
      businessName: null,
      visible: false,
      direction: "rtl",
      loading: false,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      form: {},
      option: {
        delBtn: true,
        addBtn: false,
        editBtn: false,
        height: "auto",
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        size: "mini",
        searchSize: "mini",
        align: "center",
        border: true,
        index: false,
        viewBtn: false,
        selection: true,
        dialogClickModal: false,
        column: [
          {
            label: "附件原名",
            prop: "originalName",
            rules: [
              {
                required: true,
                message: "请输入附件原名",
                trigger: "blur",
              },
            ],
          },
          {
            label: "附件大小",
            prop: "attachSize",
            slot: true,
            rules: [
              {
                required: true,
                message: "请输入附件大小",
                trigger: "blur",
              },
            ],
          },
          {
            label: "上传人",
            prop: "createUserName",
            slot: true,
            display: false,
          },
          {
            label: "上传时间",
            prop: "createTime",
            slot: true,
            display: false,
          },
        ],
      },
      data: [],
      attachForm: {},
      attachOption: {
        editBtn: false,
        size: "mini",
        searchSize: "mini",
        submitBtn: false,
        emptyBtn: false,
        column: [
          {
            label: "附件上传",
            prop: "attachFile",
            type: "upload",
            drag: true,
            dragFile: true,
            multiple: true,
            loadText: "模板上传中，请稍等",
            span: 24,
            propsHttp: {
              res: "data",
            },
            action:
              "/api/blade-resource/oss/endpoint/put-file-attach-business?code=",
          },
        ],
      },
    };
  },
  methods: {
    handleClose(done) {
      //将附件数量传出去
      this.$emit("close", this.page.total);
      done();
    },
    uploadAfter(res, done, loading, column) {
      this.attachBox = false;
      this.refreshChange();
      this.$emit("updateAfter");
      done();
    },
    handleView(row) {
      const url = "/kkf/onlinePreview?url=";
      if (row.accessPolicy && row.accessPolicy === "Private") {
        //请求后端获取下载Url
        fileLink(row.id).then((res) => {
          window.open(url + encodeURIComponent(Base64.encode(res.data.data)));
        });
      } else window.open(url + encodeURIComponent(Base64.encode(row.link)));
    },
    async handleDownload(row) {
      if (row.accessPolicy && row.accessPolicy === "Private") {
        //请求后端获取下载Url
        const r = await fileLink(row.id);
        downloadFileBlob(r.data.data, row.originalName);
      } else downloadFileBlob(row.link, row.originalName);
    },
    handleUpload() {
      this.attachForm = {};
      this.attachBox = true;
    },
    init(businessKey, businessName) {
      this.businessKey = businessKey;
      this.businessName = businessName;
      const attachFileColumn = this.findObject(
        this.attachOption.column,
        "attachFile"
      );
      attachFileColumn.data = {
        businessKey: this.businessKey,
        businessName: this.businessName,
      };
      this.visible = true;
      this.onLoad(this.page);
    },
    uploadPreview(file, column, done) {
      handleUploadPreview(file, column, done);
    },
    rowSave(row, done, loading) {
      if (row.attach) {
        row.attach = row.attach
          .map((item) => {
            return item.value;
          })
          .join(",");
      }
      add(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          window.console.log(error);
        }
      );
    },
    rowUpdate(row, index, done, loading) {
      if (row.attach) {
        row.attach = row.attach
          .map((item) => {
            return item.value;
          })
          .join(",");
      }
      update(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          console.log(error);
        }
      );
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen(done, type) {
      const that = this;
      if ("add" === type) {
        that.form = {
          purchaseTime: dateNow1(),
          qc: true,
          billType: "1",
          pays: [
            {
              paymentType: "1",
              paymentRate: 100,
              cycle: 30,
            },
          ],
        };
        genSn("por-order").then((res) => {
          that.form.serialNo = res.data.data;
        });
      } else if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then((res) => {
          that.form = res.data.data;
          if (this.contractForm.attaches) {
            this.contractForm.attach = this.contractForm.attaches.map(
              (item) => {
                return {
                  label: item.originalName,
                  value: item.id,
                };
              }
            );
          }
          if (that.form.items)
            that.form.items.forEach((item) => {
              const subNum = item.subNum;
              item.detail = subNum.map((i) => {
                return `申请单:${i.applyTitle}*${i.num};`;
              });
            });
        });
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      getPage(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query, {
          businessKey: this.businessKey,
          businessName: this.businessName,
        })
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
  },
};
</script>

<style scoped></style>

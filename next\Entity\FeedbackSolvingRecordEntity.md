```java
package com.natergy.ni.feedback.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDate;
import java.util.Date;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 问题各负责人解决记录 实体类
 *
 * <AUTHOR>
 * @since 2025-02-27
 */
@Data
@TableName("ni_feedback_solving_record")
@ApiModel(value = "FeedbackSolvingRecord对象", description = "问题各负责人解决记录")
@EqualsAndHashCode(callSuper = true)
public class FeedbackSolvingRecordEntity extends TenantEntity {

	/**
	 * 问题id
	 */
	@ApiModelProperty(value = "问题id")
	private Long feedbackId;
	/**
	 * 负责人id
	 */
	@ApiModelProperty(value = "负责人id")
	private Long responsibilityPersonId;
	/**
	 * 预估解决时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd")
	@ApiModelProperty(value = "预估解决时间")
	private LocalDate estimatedTime;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remarks;
	/**
	 * 问题原因
	 */
	@ApiModelProperty(value = "问题原因")
	private String problemCause;
	/**
	 * 解决方案
	 */
	@ApiModelProperty(value = "解决方案")
	private String solution;
	/**
	 * 流程实例id
	 */
	@ApiModelProperty(value = "流程实例id")
	private String processInstanceId;
	/**
	 * 负责人名称
	 */
	@ApiModelProperty(value = "负责人名称")
	private String responsibilityPersonName;

	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd")
	@ApiModelProperty(value = "实际解决日期")
	private LocalDate resolveDate;

}
```


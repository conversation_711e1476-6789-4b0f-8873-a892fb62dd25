<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :search.sync="query"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
      :cell-style="cellStyle"
    >
      <template #menu="{ row, index, size }">
        <el-button
          type="text"
          icon="el-icon-setting"
          size="mini"
          plain
          style="border: 0; background-color: transparent !important"
          @click.stop="handleBox(row)"
        >
          箱体数据
        </el-button>
      </template>

      <template #menuLeft>
        <el-checkbox v-model="unLink">未关联</el-checkbox>
        <template v-if="area !== 'CN'">
          <el-divider direction="vertical" />
          <el-radio-group v-model="ship" size="mini" @input="onLoad(page)">
            <el-radio-button label="Y">匹配发货编号</el-radio-button>
            <el-radio-button label="N">不匹配发货编号</el-radio-button>
          </el-radio-group>
        </template>
      </template>
    </avue-crud>

    <el-drawer
      :title="`[${scopePropertyName}] 箱体`"
      :visible.sync="boxDrawerVisible"
      :direction="direction"
      append-to-body
      :before-close="handleBoxDrawerClose"
      size="70%"
    >
      <div>
        <box-info v-if="boxDrawerVisible" :batch-id="batchId" />
      </div>
    </el-drawer>
  </basic-container>
</template>

<script>
import { getList, getDetail } from "@/api/ni/tracing/productionBatch";
import BoxInfo from "./boxInfo.vue";
import { mapGetters } from "vuex";
import { dateFormat } from "@/util/date";

export default {
  props: {
    area: {
      type: String,
    },
    shipCode: {
      type: String,
    },
  },
  components: {
    BoxInfo,
  },
  data() {
    return {
      unLink: false,
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 100,
        currentPage: 1,
        total: 0,
      },
      ship: "Y",
      selectionList: [],
      option: {
        showSummary: true,
        sumColumnList: [
          {
            name: "planBoxQuantity",
            type: "sum",
            decimals: 0,
          },
          {
            name: "actualBoxQuantity",
            type: "sum",
            decimals: 0,
          },
        ],
        height: "auto",
        calcHeight: 30,
        tip: false,
        searchIcon: true,
        searchShow: true,
        indexLabel: "序号",
        searchMenuSpan: 6,
        searchIndex: 3,
        border: true,
        index: false,
        viewBtn: true,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        selection: true,
        dialogClickModal: false,
        menuWidth: 200,
        labelWidth: 150,
        column: [
          {
            label: "日期",
            prop: "date",
            type: "date",
            searchRange: true,
            width: 100,
          },
          {
            label: "批次时间",
            prop: "batchEndTime",
            type: "datetime",
            search: true,
            searchRange: true,
            searchOrder: 40,
            width: 140,
            disabled: true,
          },
          {
            label: "质检报告",
            prop: "inspectionCode",
            type: "input",
            width: 120,
            disabled: true,
            search: true,
            viewDisplay: false,
          },
          {
            label: "起始批次",
            prop: "startBatchCode",
            type: "input",
            hide: true,
            disabled: true,
            width: 60,
            search: true,
            viewDisplay: false,
          },
          {
            label: "终止批次",
            prop: "endBatchCode",
            type: "input",
            hide: true,
            disabled: true,
            width: 60,
            search: true,
            viewDisplay: false,
          },
          {
            label: "批次号",
            prop: "batchCode",
            type: "input",
            overHidden: true,
            width: 122,
          },
          {
            label: "品牌",
            prop: "brandId",
            type: "select",
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_product_brand",
            props: {
              label: "dictValue",
              value: "id",
            },
            hide: true,
            showColumn: false,
            viewDisplay: false,
            allowCreate: true,
            filterable: true,
            overHidden: true,
            minWidth: 100,
            search: true,
          },
          {
            label: "品牌",
            prop: "brandName",
            type: "input",
            width: 80,
            disabled: true,
            editDisplay: false,
            addDisplay: false,
          },

          {
            label: "外包装",
            prop: "outerPackagingId",
            type: "select",
            dicUrl: `/api/ni/product/packaging/list?innerPark=0&current=1&size=20&status=1&&name={{key}}`,
            props: {
              label: "name",
              value: "id",
              desc: "type",
            },
            search: true,
            remote: true,
            dicFormatter: (data) => {
              return data.data.records;
            },
            filterable: true,
            change: ({ value }) => {
              if (value) {
                console.log("选择");
              }
            },
            hide: true,
            showColumn: false,
          },
          {
            label: "外包装",
            prop: "outerPackagingName",
            type: "input",
            width: 170,
          },
          {
            label: "内包装",
            prop: "innerPackagingId",
            type: "select",
            dicUrl:
              "/api/ni/product/packaging/list?innerPark=1&current=1&size=20&status=1&&name={{key}}",
            props: {
              label: "name",
              value: "id",
              desc: "type",
            },
            search: true,
            viewDisplay: false,
            dicFormatter: (data) => {
              return data.data.records;
            },
            filterable: true,
            change: ({ value }) => {
              if (value) {
                console.log("选择");
              }
            },
            hide: true,
            showColumn: false,
          },
          {
            label: "内包装",
            prop: "innerPackagingName",
            type: "input",
            width: 170,
            disabled: true,
            editDisplay: false,
            addDisplay: false,
          },

          {
            label: "规格",
            prop: "specificationId",
            type: "select",
            dicUrl: "/api/ni/product/spec/list",
            props: {
              label: "name",
              value: "id",
              desc: "type",
            },
            hide: true,
            showColumn: false,
            allowCreate: true,
            filterable: true,
            overHidden: true,
            minWidth: 120,
            search: true,
            rules: [
              {
                required: true,
                message: "请选择规格",
                trigger: "blur",
              },
            ],
          },
          {
            label: "规格",
            prop: "specificationName",
            type: "input",
            width: 100,
          },
          {
            label: "质量",
            prop: "qualityId",
            type: "select",
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_product_sku_quality_level",
            props: {
              label: "dictValue",
              value: "id",
              desc: "type",
            },
            hide: true,
            showColumn: false,
            viewDisplay: false,
            allowCreate: true,
            filterable: true,
            overHidden: true,
            minWidth: 100,
            search: true,
          },
          {
            label: "质量",
            prop: "qualityName",
            type: "input",
            width: 100,
            disabled: true,
            editDisplay: false,
            addDisplay: false,
          },
          {
            label: "容量",
            prop: "outerPackagingCapacity",
            type: "input",
            width: 100,
          },
          {
            label: "计划箱数",
            prop: "planBoxQuantity",
            type: "input",
            width: 100,
          },
          {
            label: "实际箱数",
            prop: "actualBoxQuantity",
            type: "input",
            width: 100,
          },
        ],
      },
      data: [],
      scopePropertyName: "批次",
      boxDrawerVisible: false,
      batchId: "",
      direction: "rtl",
    };
  },
  watch: {
    unLink: {
      handler() {
        this.page.currentPage = 1;
        this.onLoad(this.page);
      },
    },
  },
  computed: {
    ...mapGetters(["permission", "colorName"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.productionBatch_add, false),
        viewBtn: this.vaildData(this.permission.productionBatch_view, false),
        delBtn: this.vaildData(this.permission.productionBatch_delete, false),
        editBtn: this.vaildData(this.permission.productionBatch_edit, false),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      // return ids.join(",");
      return ids;
    },
  },
  methods: {
    handleBox(row) {
      this.scopePropertyName = row.batchCode;
      this.batchId = row.id;
      this.boxDrawerVisible = true;
    },
    handleBoxDrawerClose(hide) {
      hide();
    },
    beforeOpen(done, type) {
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then((res) => {
          this.form = res.data.data;
        });
      }
      done();
    },
    searchReset() {
      this.query = {
        startBatchCode: "",
        endBatchCode: "",
      };
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      const startBatchCode = this.query.startBatchCode;
      const endBatchCode = this.query.endBatchCode;
      this.query = params;
      this.query.startBatchCode = startBatchCode;
      this.query.endBatchCode = endBatchCode;
      this.page.currentPage = 1;
      this.onLoad(this.page, this.query);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    cellStyle({ row, column }) {
      if ("batchCode" === column.columnKey && row.isInout) {
        return {
          backgroundColor: "#909399",
          color: "#fff",
        };
      }
      if (
        "inspectionCode" === column.columnKey &&
        row.inspectionId &&
        row.inspectionCode &&
        row.inspectionCode !== "无编号"
      ) {
        return {
          backgroundColor: this.colorName,
          color: "#fff",
        };
      } else if (
        "inspectionCode" === column.columnKey &&
        row.inspectionId &&
        row.inspectionCode === "无编号"
      ) {
        return {
          backgroundColor: "#E6A23C",
          color: "#fff",
        };
      }
    },
    onLoad(page, params = {}) {
      this.loading = true;
      if (params.inspectionCode) {
        params.inspectionStatus = 1;
      }
      if (this.query.batchEndTime) {
        this.query.batchEndTime = this.query.batchEndTime.map((item) => {
          if (item && typeof item === "string") {
            item = new Date(item);
          }
          return item ? dateFormat(item, "yyyy-MM-dd hh:mm:ss") : null;
        });
      }
      this.query.area = this.area;
      if (this.area == "OS" && this.ship == "Y") {
        this.query.shipCode = this.shipCode;
      } else {
        this.query.shipCode = null;
      }
      const queryParams = {
        ...this.query,
        ...(params.inspectionId !== undefined
          ? { inspectionId: params.inspectionId }
          : {}),
      };
      if (this.unLink) {
        queryParams.inspectionStatus = 0;
      }
      getList(page.currentPage, page.pageSize, queryParams).then((res) => {
        const data = res.data.data;
        data.records.forEach((item) => {
          item.inspectionStatus = item.inspectionId != null ? 1 : 0;
          if (item.inspectionId && !item.inspectionCode) {
            item.inspectionCode = "无编号";
          }
        });
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
  },
};
</script>

<style scoped>
/* 新增样式：控制水平排列和间距 */
.search-range-container {
  display: flex;
  align-items: center;
  width: 100%;
}

/* 兼容 Avue 搜索栏布局（可选） */
.avue-crud .search-box {
  display: flex;
  flex-wrap: wrap; /* 允许搜索条件换行 */
  gap: 10px; /* 统一搜索条件间距 */
}
</style>

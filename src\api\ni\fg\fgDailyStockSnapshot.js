import request from "@/router/axios";

export const getList = (current, size, params) => {
  return request({
    url: "/api/ni/fg/daily-stock-snapshot/page",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const rebuild = () => {
  return request({
    url: "/api/ni/fg/daily-stock-snapshot/rebuild",
    method: "post",
  });
};

export const dailyInOutboundSum = (ids, type) => {
  return request({
    url: "/api/ni/fg/daily-stock-snapshot/dailyInOutboundSum",
    method: "get",
    params: {
      ids,
      type,
    },
  });
};

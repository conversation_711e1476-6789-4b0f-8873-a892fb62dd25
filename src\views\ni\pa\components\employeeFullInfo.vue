<template>
    <el-form :model="fullInfo"  :rules="rules" :ref="formRef"   label-width="120px">
      <el-tabs type="border-card">
        <!-- 基础信息 -->
        <el-tab-pane label="个人信息"  >
          <el-row gutter="20">
            <!-- 头像上传 -->
            <el-col :span="6" >
              <el-form-item label="头像"  >
                <el-upload
                  :disabled="disableRule.baseInfo"
                  class="avatar-uploader"
                  action="/api/blade-resource/oss/endpoint/put-file-attach"
                  :show-file-list="false"
                  :headers="getAuthorizationHeader()"
                  list-type="picture-card"
                  :on-success="(response, file, fileList) => handleSuccess(response, file, fileList,'baseInfo.avatar')"
                >
                  <img v-if="fullInfo.baseInfo.avatar" :src="fullInfo.baseInfo.avatar" class="avatar">
                  <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                </el-upload>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="银行卡">
                <el-upload
                  :disabled="disableRule.extInfo"
                  class="avatar-uploader"
                  action="/api/blade-resource/oss/endpoint/put-file-attach"
                  :headers="getAuthorizationHeader()"
                  limit="1"
                  :show-file-list="false"
                  list-type="picture-card"
                  :on-success="(response, file, fileList) => handleSuccess(response, file, fileList,'extInfo.bankCard')">
                  <img v-if="fullInfo.extInfo.bankCard" :src="fullInfo.extInfo.bankCard" class="fileItem">
                  <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                </el-upload>
              </el-form-item>
            </el-col>

            <el-col :span="6">
              <el-form-item label="身份证正面">
                <el-upload
                  :disabled="disableRule.extInfo"
                  class="avatar-uploader"
                  action="/api/blade-resource/oss/endpoint/put-file-attach"
                  :headers="getAuthorizationHeader()"
                  limit="1"
                  :show-file-list="false"
                  list-type="picture-card"
                  :on-success="(response, file, fileList) => handleSuccess(response, file, fileList,'extInfo.idCardPicture')">
                  <img v-if="fullInfo.extInfo.idCardPicture" :src="fullInfo.extInfo.idCardPicture" class="fileItem">
                  <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                </el-upload>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="身份证反面">
                <el-upload
                  :disabled="disableRule.extInfo"
                  class="avatar-uploader"
                  action="/api/blade-resource/oss/endpoint/put-file-attach"
                  :headers="getAuthorizationHeader()"
                  limit="1"
                  :show-file-list="false"
                  list-type="picture-card"
                  :on-success="(response, file, fileList) => handleSuccess(response, file, fileList,'extInfo.idCardBackPicture')">
                  <img v-if="fullInfo.extInfo.idCardBackPicture" :src="fullInfo.extInfo.idCardBackPicture" class="fileItem">
                  <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                </el-upload>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row gutter="20">
            <!-- 基础信息两列布局 -->
            <el-col :span="8">
              <el-form-item label="员工姓名"  required >
                <el-autocomplete
                  :disabled="disableRule.baseInfo"
                  v-if="autoComplete"
                  class="inline-input"
                  v-model="fullInfo.baseInfo.name"
                  :fetch-suggestions="queryEmployee"
                  placeholder="请输入内容"
                  @select="handleEmployeeSelect"
                ></el-autocomplete>
                <el-input  v-else :readonly="disableRule.baseInfo" v-model="fullInfo.baseInfo.name"/>
              </el-form-item>
              <el-form-item label="联系电话" required  >
                <el-input :readonly="disableRule.baseInfo"  v-model="fullInfo.baseInfo.phone"/>
              </el-form-item>

              <el-form-item label="部门" required>
                <el-select
                  :disabled="disableRule.baseInfo"
                  v-model="fullInfo.baseInfo.deptId">
                  <el-option
                    v-for="item in deptSelect"
                    :key="item.id"
                    :label="item.deptName"
                    :value="item.id"/>
                </el-select>
              </el-form-item>


              <el-form-item label="婚姻状况">
                <el-select
                  :disabled="disableRule.extInfo"
                  v-model="fullInfo.extInfo.marriage">
                  <el-option
                    v-for="item in yesOrNoSelect"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"/>
                </el-select>
              </el-form-item>
              <el-form-item label="研究的专业领域">
                <el-input :readonly="disableRule.extInfo" v-model="fullInfo.extInfo.researchField"/>
              </el-form-item>
              <el-form-item label="个人信仰">
                <el-input :readonly="disableRule.extInfo" v-model="fullInfo.extInfo.religion"/>
              </el-form-item>
              <el-form-item label="民族">
                <el-input :readonly="disableRule.baseInfo" v-model="fullInfo.baseInfo.ethnicity"/>
              </el-form-item>

            </el-col>

            <el-col :span="8">
              <el-form-item label="身份证号" required>
                <el-input :readonly="disableRule.baseInfo" v-model="fullInfo.baseInfo.idCard"/>
              </el-form-item>

              <el-form-item label="其他联系电话">
                <el-input :readonly="disableRule.baseInfo" v-model="fullInfo.baseInfo.otherPhone"/>
              </el-form-item>
              <el-form-item required label="岗位">
                <el-select
                  :disabled="disableRule.baseInfo"
                  v-model="fullInfo.baseInfo.positionId">
                  <el-option
                    v-for="item in postSelect"
                    :key="item.id"
                    :label="item.postName"
                    :value="item.id"/>
                </el-select>
              </el-form-item>
              <el-form-item label="政治面貌">
                <el-select
                  :disabled="disableRule.extInfo"
                  v-model="fullInfo.extInfo.politic">
                  <el-option
                    v-for="item in politicalSelect"
                    :key="item.dictKey"
                    :label="item.dictValue"
                    :value="item.dictKey"/>
                </el-select>
              </el-form-item>
              <el-form-item label="获得知识产权">
                <el-select
                  :disabled="disableRule.extInfo"
                  v-model="fullInfo.extInfo.hasIntellectualProperty">
                  <el-option
                    v-for="item in yesOrNoSelect"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"/>
                </el-select>
              </el-form-item>

              <el-form-item label="个人爱好">
                <el-input :readonly="disableRule.extInfo" v-model="fullInfo.extInfo.hobbies"/>
              </el-form-item>
              <el-form-item label="是否住宿舍"  >
                <el-radio-group  :disabled="disableRule.extInfo" v-model="fullInfo.extInfo.hostel">
                  <el-radio :label="1">是</el-radio>
                  <el-radio :label="0" >否</el-radio>
                </el-radio-group>
<!--                <el-checkbox v-model="fullInfo.extInfo.hostel"  true-label=1  false-label=0>是</el-checkbox>-->
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="银行卡号" required>
                <el-input :readonly="disableRule.extInfo" v-model="fullInfo.extInfo.bankCardNum"/>
              </el-form-item>
              <el-form-item label="员工编号">
                <el-input :readonly="disableRule.baseInfo" v-model="fullInfo.baseInfo.code"/>
              </el-form-item>

<!--              <el-form-item label="联系地址">-->
<!--                <el-input v-model="fullInfo.baseInfo.address"/>-->
<!--              </el-form-item>-->

              <el-form-item label="合同状态"  required>
                <el-select
                  :disabled="disableRule.baseInfo"
                  v-model="fullInfo.baseInfo.contractStatus">
                  <el-option
                    v-for="item in contractStatusSelect"
                    :key="item.label"
                    :label="item.label"
                    :value="item.value"/>
                </el-select>
              </el-form-item>
              <el-form-item label="职称">
                <el-input :readonly="disableRule.extInfo" v-model="fullInfo.extInfo.professionalTitle"/>
              </el-form-item>

              <el-form-item label="存在竞业协议">
                <el-select
                  :disabled="disableRule.extInfo"
                  v-model="fullInfo.extInfo.hasCompetitionRestriction">
                  <el-option
                    v-for="item in yesOrNoSelect"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"/>
                </el-select>
              </el-form-item>

              <el-form-item label="英语水平">
                <el-input :readonly="disableRule.extInfo" v-model="fullInfo.extInfo.englishProficiency"/>
              </el-form-item>
              <el-form-item label="是否实习">
                <el-radio-group  :disabled="disableRule.extInfo" v-model="fullInfo.extInfo.intern">
                  <el-radio :label="1">是</el-radio>
                  <el-radio :label="0" >否</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>

          </el-row>
          <el-row  >
            <el-col :span="24">
              <el-form-item label="户籍所在地">
                <el-cascader
                  :disabled="disableRule.extInfo"
                  :props="{emitPath:false}"
                  v-model="fullInfo.extInfo.householdRegisterCode"
                  :options="regionSelect"
                  filterable
                  clearable
                  separator=""
                ></el-cascader>
                <el-input style="width: 70%;" :readonly="disableRule.extInfo" v-model="fullInfo.extInfo.householdRegister"></el-input>
              </el-form-item>
              <el-form-item label="家庭地址">
                <el-cascader
                  :disabled="disableRule.extInfo"
                  :props="{emitPath:false}"
                  v-model="fullInfo.extInfo.familyAddressCode"
                  :options="regionSelect"
                  filterable
                  clearable
                  separator=""
                ></el-cascader>
                <el-input style="width: 70%;" :readonly="disableRule.extInfo" v-model="fullInfo.extInfo.familyAddress"></el-input>
              </el-form-item>
              <el-form-item label="当前居住地">
                <el-cascader
                  :disabled="disableRule.extInfo"
                  :props="{emitPath:false}"
                  v-model="fullInfo.extInfo.currentAddressCode"
                  :options="regionSelect"
                  filterable
                  clearable
                  separator=""
                ></el-cascader>
                <el-input style="width: 70%;" :readonly="disableRule.extInfo" v-model="fullInfo.extInfo.currentAddress"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row gutter="20">
            <el-col  :span="8">
              <el-form-item label="驾照">
                <el-input :readonly="disableRule.extInfo" v-model="fullInfo.extInfo.driverLicense"/>
              </el-form-item>
              <el-form-item label="入职时间">
                <el-date-picker
                  :readonly="disableRule.baseInfo"
                  v-model="fullInfo.baseInfo.enterDate"
                  type="date"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"/>
              </el-form-item>
            </el-col>
            <el-col  :span="8">
              <el-form-item label="驾龄">
                <el-input :readonly="disableRule.extInfo" v-model="fullInfo.extInfo.drivingExperienceYears"/>
              </el-form-item>
              <el-form-item label="填表时间">
                <el-date-picker
                  :readonly="disableRule.baseInfo"
                  v-model="fullInfo.baseInfo.fillTime"
                  type="date"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"/>
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item label="车牌号">
                <el-tag
                  :key="num"
                  v-for="num in carNumTags"
                  closable
                  :disable-transitions="false"
                  @close="carNumTagClose(num)">
                  {{num}}
                </el-tag>
                <el-input
                  class="input-new-tag"
                  v-if="carNumVisible"
                  ref="saveTagInput"
                  v-model="tagInputVal"
                  size="small"
                  @keyup.enter.native="carNumInputConfirm"
                  @blur="carNumInputConfirm"
                >
                </el-input>
                <el-button :disabled="disableRule.baseInfo"  @click="addCarNumTag">+</el-button>
                <!--                  <el-input v-model="fullInfo.extInfo.carNum"/>-->
              </el-form-item>
            </el-col>
          </el-row>
        </el-tab-pane>

        <!-- 扩展信息 -->
        <el-tab-pane label="其他信息">
          <el-collapse>
            <!-- 家庭成员 -->
            <el-collapse-item title="家庭成员">
              <div class="table-header">
                <el-button v-show="disableRule.familyMembers" @click="addFamilyMember" size="mini" type="primary">新增成员</el-button>
              </div>
              <el-table :data="fullInfo.familyMembers" border size="mini">
                <el-table-column prop="name" label="姓名">
                  <template slot-scope="scope">
                    <el-input :readonly="!disableRule.familyMembers" v-model="scope.row.employeeFamilyName"  size="mini"></el-input>
                  </template>
                </el-table-column>
                <el-table-column prop="employeeFamilyMember" label="关系">
                  <template slot-scope="scope">
                    <el-input :readonly="!disableRule.familyMembers" v-model="scope.row.employeeFamilyMember" size="mini"></el-input>
                  </template>
                </el-table-column>
                <el-table-column prop="employeeFamilyWork" label="工作单位">
                  <template slot-scope="scope">
                    <el-input :readonly="!disableRule.familyMembers"  v-model="scope.row.employeeFamilyWork" size="mini"></el-input>
                  </template>
                </el-table-column>
                <el-table-column prop="employeeFamilyContact" label="联系电话">
                  <template slot-scope="scope">
                    <el-input  :readonly="!disableRule.familyMembers" v-model="scope.row.employeeFamilyContact" size="mini"></el-input>
                  </template>
                </el-table-column>
                <el-table-column v-if="disableRule.familyMembers"  label="操作">
                  <template slot-scope="scope">
                    <el-button
                      @click="deleteFamilyMember(scope.$index)"
                      type="danger"
                      size="mini"
                      plain>删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-collapse-item>

            <!-- 教育经历 -->
            <el-collapse-item title="教育经历">
              <div class="table-header">
                <el-button v-show="disableRule.educations" @click="addEducation" size="mini" type="primary">新增经历</el-button>
              </div>
              <el-table :data="fullInfo.educations" border size="mini">
                <el-table-column prop="startDate" label="起始时间">
                  <template slot-scope="scope">
                    <el-date-picker
                      :readonly="!disableRule.educations"
                      v-model="scope.row.startDate"
                      type="date"
                      format="yyyy-MM-dd"
                      value-format="yyyy-MM-dd"
                      size="mini">
                    </el-date-picker>
                  </template>
                </el-table-column>
                <el-table-column prop="endDate" label="截止时间">
                  <template slot-scope="scope">
                    <el-date-picker
                      :readonly="!disableRule.educations"
                      v-model="scope.row.endDate"
                      type="date"
                      format="yyyy-MM-dd"
                      value-format="yyyy-MM-dd"
                      size="mini">
                    </el-date-picker>
                  </template>
                </el-table-column>
                <el-table-column prop="type" label="学历类型">
                  <template slot-scope="scope">
                    <el-select
                      size="mini"
                      :disabled="!disableRule.educations"
                      v-model="scope.row.type">
                      <el-option
                        v-for="item in eduTypeSelect"
                        :key="item.label"
                        :label="item.label"
                        :value="item.value"/>
                    </el-select>
                    <!--                    <el-input v-model="scope.row.type" size="mini"></el-input>-->
                  </template>
                </el-table-column>
                <el-table-column prop="school" label="学校">
                  <template slot-scope="scope">
                    <el-input :readonly="!disableRule.educations" v-model="scope.row.school" size="mini"></el-input>
                  </template>
                </el-table-column>
                <el-table-column prop="major" label="专业">
                  <template slot-scope="scope">
                    <el-input :readonly="!disableRule.educations" v-model="scope.row.major" size="mini"></el-input>
                  </template>
                </el-table-column>
                <el-table-column prop="education" label="学历">
                  <template slot-scope="scope">
                    <el-select v-model="scope.row.education"
                               :disabled="!disableRule.educations"
                               size="mini" placeholder="请选择">
                      <el-option
                        v-for="item in eduSelect"
                        :key="item.dictKey"
                        :label="item.dictValue"
                        :value="item.dictKey">
                      </el-option>
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column prop="graduation" label="毕业证">
                  <template slot-scope="scope">
                    <el-upload
                      :disabled="!disableRule.educations"
                      class="small-picture-card"
                      action="/api/blade-resource/oss/endpoint/put-file-attach"
                      :show-file-list="false"
                      :headers="getAuthorizationHeader()"
                      list-type="picture-card"
                      :on-success="(response, file, fileList) => handleDynamicSuccess(response, file, fileList, scope.row,'graduation')">
                      <img v-if="scope.row.graduation" :src="scope.row.graduation" class="avatar">
                      <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                    </el-upload>

                  </template>
                </el-table-column>
                <el-table-column prop="degree" label="学位">

                  <template slot-scope="scope">
                    <el-upload
                      :disabled="!disableRule.educations"
                      class="small-picture-card"
                      action="/api/blade-resource/oss/endpoint/put-file-attach"
                      :show-file-list="false"
                      :headers="getAuthorizationHeader()"
                      list-type="picture-card"
                      :on-success="(response, file, fileList) => handleDynamicSuccess(response, file, fileList, scope.row,'degree')" >
                      <img v-if="scope.row.degree" :src="scope.row.degree" class="avatar">
                      <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                    </el-upload>
                  </template>
                </el-table-column>
                <el-table-column v-if="disableRule.educations" label="操作">
                  <template slot-scope="scope">
                    <el-button
                      @click="deleteEducation(scope.$index)"
                      type="danger"
                      size="mini"
                      plain>删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-collapse-item>

            <!-- 工作经历 -->
            <el-collapse-item title="工作经历">
              <div class="table-header">
                <el-button v-show="disableRule.workExperiences" @click="addWork" size="mini" type="primary">新增经历</el-button>
              </div>
              <el-table :data="fullInfo.workExperiences" border size="mini">
                <el-table-column prop="startDate" label="起始时间">
                  <template slot-scope="scope">
                    <el-date-picker
                      :readonly="!disableRule.workExperiences"
                      v-model="scope.row.startDate"
                      type="date"
                      format="yyyy-MM-dd"
                      value-format="yyyy-MM-dd"
                      size="mini">
                    </el-date-picker>
                  </template>
                </el-table-column>
                <el-table-column prop="endDate" label="截止时间">
                  <template slot-scope="scope">
                    <el-date-picker
                      :readonly="!disableRule.workExperiences"
                      v-model="scope.row.endDate"
                      type="date"
                      format="yyyy-MM-dd"
                      value-format="yyyy-MM-dd"
                      size="mini">
                    </el-date-picker>
                  </template>
                </el-table-column>
                <el-table-column prop="company" label="公司">
                  <template slot-scope="scope">
                    <el-input  :readonly="!disableRule.workExperiences" v-model="scope.row.company" size="mini"></el-input>
                  </template>
                </el-table-column>
                <el-table-column prop="office" label="职位">
                  <template slot-scope="scope">
                    <el-input  :readonly="!disableRule.workExperiences" v-model="scope.row.office" size="mini"></el-input>
                  </template>
                </el-table-column>
                <el-table-column prop="workContent" label="职责描述">
                  <template slot-scope="scope">
                    <el-input  :readonly="!disableRule.workExperiences" v-model="scope.row.workContent" size="mini"></el-input>
                  </template>
                </el-table-column>
                <el-table-column v-if="disableRule.workExperiences" label="操作">
                  <template slot-scope="scope">
                    <el-button
                      @click="deleteWork(scope.$index)"
                      type="danger"
                      size="mini"
                      plain>删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-collapse-item>
          </el-collapse>
        </el-tab-pane>
        <el-tab-pane label="住宿信息" v-if="fullInfo.extInfo.hostel ===1">

          <el-col span="12">
            <el-form-item label="住宿申请书">
              <el-upload
                :disabled="disableRule.dormitory"
                class="avatar-uploader"
                action="/api/blade-resource/oss/endpoint/put-file-attach"
                :headers="getAuthorizationHeader()"
                :show-file-list="false"
                list-type="picture-card"
                :on-success="(response, file, fileList) => handleFilesSuccess(response, file, fileList,6)">
                <img v-if="employeeFiles.accommodation" :src="employeeFiles.accommodation" class="fileItem">
                <i v-else class="el-icon-plus avatar-uploader-icon"></i>
              </el-upload>
            </el-form-item>
            <el-form-item label="宿舍所属厂区">
              <el-input :readonly="disableRule.dormitory" v-model="fullInfo.dormitory.region"/>
            </el-form-item>
            <el-form-item label="宿舍编号">
              <el-input  :readonly="disableRule.dormitory" v-model="fullInfo.dormitory.code"/>
            </el-form-item>

          </el-col>
          <el-col span="12">
            <el-form-item label="住宿承诺书">
              <el-upload
                :disabled="disableRule.dormitory"
                class="avatar-uploader"
                action="/api/blade-resource/oss/endpoint/put-file-attach"
                :headers="getAuthorizationHeader()"
                :show-file-list="false"
                list-type="picture-card"
                :on-success="(response, file, fileList) => handleFilesSuccess(response, file, fileList,7)">
                <img v-if="employeeFiles.accommodationGuarantee" :src="employeeFiles.accommodationGuarantee" class="fileItem">
                <i v-else class="el-icon-plus avatar-uploader-icon"></i>
              </el-upload>
            </el-form-item>
            <el-form-item label="房间号">
              <el-input  :readonly="disableRule.dormitory" v-model="fullInfo.dormitory.roomCode"/>
            </el-form-item>
            <el-form-item label="床位号">
              <el-input  :readonly="disableRule.dormitory" v-model="fullInfo.dormitory.bedCode"/>
            </el-form-item>
          </el-col>
        </el-tab-pane>
        <el-tab-pane label="签署文件">
          <el-collapse>
            <el-collapse-item title="新人培训">
              <el-row>
                <el-col span="24">
                  <el-form-item label="电子签信息">
<!--                    <el-upload-->
<!--                      disabled-->
<!--                      class="avatar-uploader"-->
<!--                      action="/api/blade-resource/oss/endpoint/put-file-attach"-->
<!--                      :headers="getAuthorizationHeader()"-->
<!--                      :show-file-list="false"-->
<!--                      list-type="picture-card"-->
<!--                      :on-success="(response, file, fileList) => handleFilesSuccess(response, file, fileList,1)">-->
<!--&lt;!&ndash;                      <img v-if="employeeFiles.baseInfo" :src="employeeFiles.baseInfo" class="fileItem">&ndash;&gt;-->
<!--                      <i v-else class="el-icon-plus avatar-uploader-icon"></i>-->
<!--                    </el-upload>-->

                      <el-button v-if="employeeFiles.baseInfo" @click="viewFile(employeeFiles.baseInfo)" >预览文件</el-button>
                      <i v-else >未完成</i>
                  </el-form-item>
<!--                  <el-form-item label="保密协议">-->
<!--                    <el-upload-->
<!--                      :disabled="disableRule.files"-->
<!--                      class="avatar-uploader"-->
<!--                      action="/api/blade-resource/oss/endpoint/put-file-attach"-->
<!--                      :headers="getAuthorizationHeader()"-->
<!--                      :show-file-list="false"-->
<!--                      list-type="picture-card"-->
<!--                      :on-success="(response, file, fileList) => handleFilesSuccess(response, file, fileList,2)">-->
<!--                      <img v-if="employeeFiles.secret" :src="employeeFiles.secret" class="fileItem">-->
<!--                      <i v-else class="el-icon-plus avatar-uploader-icon"></i>-->
<!--                    </el-upload>-->
<!--                  </el-form-item>-->
<!--                  <el-form-item label="新员工告知书">-->
<!--                    <el-upload-->
<!--                      :disabled="disableRule.files"-->
<!--                      class="avatar-uploader"-->
<!--                      action="/api/blade-resource/oss/endpoint/put-file-attach"-->
<!--                      :headers="getAuthorizationHeader()"-->
<!--                      :show-file-list="false"-->
<!--                      list-type="picture-card"-->
<!--                      :on-success="(response, file, fileList) => handleFilesSuccess(response, file, fileList,3)">-->
<!--                      <img v-if="employeeFiles.notice" :src="employeeFiles.notice" class="fileItem">-->
<!--                      <i v-else class="el-icon-plus avatar-uploader-icon"></i>-->
<!--                    </el-upload>-->
<!--                  </el-form-item>-->
                </el-col>
<!--                <el-col span="12">-->
<!--                  <el-form-item label="软件使用承诺书">-->
<!--                    <el-upload-->
<!--                      :disabled="disableRule.files"-->
<!--                      class="avatar-uploader"-->
<!--                      action="/api/blade-resource/oss/endpoint/put-file-attach"-->
<!--                      :headers="getAuthorizationHeader()"-->
<!--                      :show-file-list="false"-->
<!--                      list-type="picture-card"-->
<!--                      :on-success="(response, file, fileList) => handleFilesSuccess(response, file, fileList,4)">-->
<!--                      <img v-if="employeeFiles.software" :src="employeeFiles.software" class="fileItem">-->
<!--                      <i v-else class="el-icon-plus avatar-uploader-icon"></i>-->
<!--                    </el-upload>-->
<!--                  </el-form-item>-->

<!--                  <el-form-item label="公司规则制度考试">-->
<!--                    <el-upload-->
<!--                      :disabled="disableRule.files"-->
<!--                      class="avatar-uploader"-->
<!--                      action="/api/blade-resource/oss/endpoint/put-file-attach"-->
<!--                      :headers="getAuthorizationHeader()"-->
<!--                      :show-file-list="false"-->
<!--                      list-type="picture-card"-->
<!--                      :on-success="(response, file, fileList) => handleFilesSuccess(response, file, fileList,5)">-->
<!--                      <img v-if="employeeFiles.exam" :src="employeeFiles.exam" class="fileItem">-->
<!--                      <i v-else class="el-icon-plus avatar-uploader-icon"></i>-->
<!--                    </el-upload>-->
<!--                  </el-form-item>-->
<!--                </el-col>-->
              </el-row>
            </el-collapse-item>
          </el-collapse>

        </el-tab-pane>
        <el-tab-pane label="培训记录">
          <el-table :data="fullInfo.trainingList" border size="mini">
            <el-table-column prop="trainingName" label="培训名称"/>

            <el-table-column prop="trainingDate" label="培训时间">
              <template slot-scope="scope">
                <el-date-picker
                  readonly
                  v-model="scope.row.trainingDate"
                  type="date"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                  size="mini">
                </el-date-picker>
              </template>
            </el-table-column>
            <el-table-column prop="trainingType" label="培训类型">
              <template slot-scope="scope">
                {{displayTrainingType(scope.row.trainingType)}}
<!--                <el-input  readonlyv-model="scope.row.trainingType" size="mini"></el-input>-->
              </template>
            </el-table-column>
            <el-table-column prop="trainerId" label="培训员">
              <template slot-scope="scope">
                <el-select disabled v-model="scope.row.trainerId">
                  <el-option
                    v-for="item in userSelect"
                    :key="item.id"
                    :label="item.realName"
                    :value="item.id"
                  />
                </el-select>
<!--                <el-input  readonly v-model="scope.row.trainerId" size="mini"></el-input>-->
              </template>
            </el-table-column>

          </el-table>
        </el-tab-pane>
        <!-- 其他标签页结构类似 -->
        <slot name="otherTabs"/>
        <slot name="buttons"></slot>
      </el-tabs>
    </el-form>
</template>

<script>

import {mapGetters} from "vuex";
import { get, set } from 'lodash';
import {getAuthorizationHeader} from "@/api/resource/fileManagement";
import { fullInfo, queryEmployeeByName} from "@/api/ni/emhire/niHrEmployeeBaseInfo";
import {getRegionLazyTree} from "@/api/base/region";
import {getJobs} from "@/api/ni/emhire/niEmployeeResumeInfo";
import {getDeptTree} from "@/api/system/dept";
import {getPostList} from "@/api/system/post";
import {getDeptItem, getEduItem, getPoliticalItem, getUserItem} from "@/api/selectUtils";
import {fileLink} from "@/api/resource/attach";
import {Base64} from "js-base64";

export default {
  name:'FullInfoForm',
  props:['fullInfo','autoComplete','formRef'],
  data() {
    return {
      defaults: {},
      form: {},
      carNumVisible:false,
      tagInputVal:'',
      carNumTags:[],

      loading: false,
      politicalSelect:[],
      eduSelect:[],
      regionSelect:[],
      contractStatusSelect:[
        {label:"未签约",value:1},
        {label:"未与上家解除",value:0},
        {label:"已签约",value:2},
        {label:"已解除",value:3},
      ],
      yesOrNoSelect: [
        {label:'否',value:0},
        {label: '是', value:1},
      ],
      eduTypeSelect: [
        {label:'第一学历',value:3},
        {label:'最高学历',value:2},
        {label:'在读',value:0},
        {label:'毕业',value:1}],
      trainingType:{
        0:'安全培训',
        2:'人事培训',
        1:'NI培训',
      },
      deptSelect:[],
      postSelect:[],
      userSelect:[],
      postTree:[],
      deptTree:[],
      deptMap:{},
      postMap:{},
      rules: {
        'fullInfo.baseInfo.name': [{ required: true, message: "请输入用户名" }],
        'fullInfoBaseInfoIdCard': [{ required: true, message: "请输入身份证号" }],
        'fullInfoExtInfoBankCardNum': [{ required: true, message: "请输入银行卡号" }],
        'fullInfoBaseInfoPhone': [{ required: true, message: "请输入手机号" }],
        'fullInfoBaseInfoDeptId': [{ required: true, message: "请选择部门" }],
        'fullInfoExtInfoHostel': [{ required: true, message: "请选择是否住宿" }],
        'fullInfoBaseInfoContractStatus': [{ required: true, message: "请选择合同状态" }]
      },
      auditForm:true,
      disableRule: {
        baseInfo: false,
        extInfo: false,
        familyMembers: true,
        educations: true,
        workExperiences: true,
        files: true,
        trainingList: false,
        dormitory: false,
      },
    };
  },
  watch: {
    fullInfo(newVal) {
      console.info('watch',newVal);
      this.$emit('info-update', newVal)
    }
  },
  computed: {
    ...mapGetters(["permission","userInfo"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.niHrEmployeeBaseInfo_add, false),
        viewBtn: this.vaildData(this.permission.niHrEmployeeBaseInfo_view, false),
        delBtn: this.vaildData(this.permission.niHrEmployeeBaseInfo_delete, false),
        editBtn: this.vaildData(this.permission.niHrEmployeeBaseInfo_edit, false)
      };
    },
    employeeFiles(){
      return {
        baseInfo: this.findFile(1),
        secret: this.findFile(2),
        notice: this.findFile(3),
        software: this.findFile(4),
        exam: this.findFile(5),
        accommodation: this.findFile(6),
        accommodationGuarantee: this.findFile(7),
        electrician: this.findFile(8),
        welder: this.findFile(9),
      }
    },

  },
  created() {
    this.initDic();
  },
  mounted() {
    console.info("mounted");
  },
  methods: {
    getAuthorizationHeader,
    // 家庭成员操作
    addFamilyMember() {
      this.fullInfo.familyMembers.push({ employeeFamilyName: '', employeeFamilyMember: '', employeeFamilyWork: '', employeeFamilyContact: '' });
    },
    deleteFamilyMember(index) {
      this.fullInfo.familyMembers.splice(index, 1);
    },
    // 教育经历操作
    addEducation() {
      this.fullInfo.educations.push({ startDate:'',endDate:'',school: '', type:'', major: '', degree: '', education: '' ,graduation:''});
    },
    deleteEducation(index) {
      this.fullInfo.educations.splice(index, 1);
    },
    // 工作经历操作
    addWork() {
      this.fullInfo.workExperiences.push({ startDate:'',endDate:'',company: '', office: '', workContent: '' });
    },
    deleteWork(index) {
      this.fullInfo.workExperiences.splice(index, 1);
    },
    carNumTagClose(tag){
      this.carNumTags.splice(this.carNumTags.indexOf(tag), 1);
    },
    carNumInputConfirm(){
      let inputValue = this.tagInputVal;
      if (inputValue) {
        this.carNumTags.push(inputValue);
      }
      this.inputVisible = false;
      this.tagInputVal = '';
    },
    addCarNumTag(){
      this.carNumVisible = true;
      this.$nextTick(_ => {
        this.$refs.saveTagInput.$refs.input.focus();
      });
    },
    getCarNum(){
      return this.carNumTags.join();
    },
    setCarNum(){
      if (this.fullInfo.baseInfo.carNum){
        this.carNumTags = this.fullInfo.baseInfo.carNum.split(",")
        console.info('carNumTags',this.carNumTags)
      }else{
        this.carNumTags = [];
      }
    },
    clearCarNum(){
      this.carNumTags = [];
      this.fullInfo.baseInfo.carNum = '';
    },
    viewFile(fileUrl){
      const url = "http://192.168.200.23/kkf/onlinePreview?url=";
      window.open(url + encodeURIComponent(Base64.encode(fileUrl)))
    },
    queryEmployee(queryString, cb){
      if (queryString === ''|| queryString.length<1) {
        cb([])
        return;
      }
      queryEmployeeByName(queryString).then(res=>{
        let result = res.data.data.map(item => { return {value:item.name+":"+item.idCard,id:item.id} });
        cb(result);
      }).catch(err=>{
        console.error(err);
      })
    },
    handleEmployeeSelect(item){
      fullInfo(item.id).then(res=>{
        let info = res.data.data;
        if (!info.dormitory){
          info.dormitory = Object.assign({}, this.fullInfo.dormitory);
        }
        if (!info.extInfo.hostel){
          info.extInfo.hostel = 0
        }
        if (!info.extInfo.intern){
          info.extInfo.intern = 0
        }
        this.fullInfo = info;
        if (!this.fullInfo.files){
          this.fullInfo.files = [];
        }
        this.setCarNum();
        this.$emit('info-update', this.fullInfo);
      }).catch(err=>{
        this.$message.error("拉取用户信息失败"+err);
        console.error(err);
      })
    },
    displayTrainingType(type){
      return this.trainingType[type]
    },
    initDic() {
      getUserItem().then((res) => {
        this.userSelect = res.data.data;
      });
      getPoliticalItem().then((res) => {
        this.politicalSelect = res.data.data;
      });
      getEduItem().then((res) => {
          this.eduSelect = res.data.data;
        });
      getRegionLazyTree().then((res) => {
        this.regionSelect = res.data.data;
      });
      getDeptItem().then((res) => {
        this.deptSelect = res.data.data;
        this.deptMap = new Map(this.deptSelect.map(item => [item.id,item.deptName]));
      });
      getJobs().then(res => {
        this.postSelect = res.data.data;
        this.postMap = new Map(
          this.postSelect.map(object => {
            return [object.id, object.postName];
          })
        );
      })
      getDeptTree(this.userInfo.tenantId).then((res) => {
        this.deptTree = res.data.data;
      });
      getPostList(this.userInfo.tenantId).then((res) => {
        this.postTree = res.data.data;
      });
    },
    findFile(type){
      if (this.fullInfo.files){
        return this.vaildData(this.fullInfo.files.findLast(f => f.type === type) , {}).fileUrl
      }else{
        return {}
      }
    },
    displayByProcess(rules){
      console.info('disabled',rules)
      this.disableRule = rules;
    },
    handleFilesSuccess(response, file, fileList,type){
      console.info(type)
      let exist = this.fullInfo.files.findLastIndex(f=>f.type === type)
      if (exist !== -1){
        this.fullInfo.files.splice(exist, 1)
      }
      this.fullInfo.files.push({
        type:type,
        fileUrl:response.data.link,
        fileId:response.data.attachId,
        name: response.data.originalName,
      })
    },
    handleSuccess(response, file, fileList,field) {
      set(this.fullInfo,field,response.data.link)
      set(this.fullInfo,field+'FileId',response.data.attachId)
    },
    handleDynamicSuccess(response, file, fileList, row,prop) {
      this.$set(row,prop, response.data.link)
      this.$set(row,prop+'FileId', response.data.attachId)
    },
  }
}
</script>

<style lang="scss" scoped>
.el-select{width: 100%;}
.small-picture-card {
  .el-upload--picture-card {
    width: 80px;
    height: 80px;
    line-height: 80px;
    font-size: 24px;
    padding: 0;
  }

  .el-upload-list--picture-card .el-upload-list__item {
    width: 80px;
    height: 80px;
    line-height: 80px;
  }

  .el-upload--picture-card .el-upload__input {
    display: none;
  }
}
.fileItem {
  width: 72px;
  height: 72px;
}
.avatar {
  width: 72px;
  height: 72px;
  border-radius: 50%;
}
.inline-input{
  width:100%;
}
</style>

import request from '@/router/axios';


export const genSn = (code,id) => {
  return request({
    url: '/api/ni/base/serialno/gen',
    method: 'get',
    params: {
      code,
      id,
    }
  })
}

export const getList = (current, size, params) => {
  return request({
    url: '/api/ni/base/serialno/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/ni/base/serialno/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/ni/base/serialno/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/ni/base/serialno/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/ni/base/serialno/submit',
    method: 'post',
    data: row
  })
}


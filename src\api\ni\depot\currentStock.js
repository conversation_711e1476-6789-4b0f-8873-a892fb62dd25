import request from "@/router/axios";

export const getList = (current, size, params) => {
  return request({
    url: "/api/ni/depot/currentStock/page",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (params) => {
  return request({
    url: "/api/ni/depot/currentStock/detail",
    method: "get",
    params,
  });
};

export const changeRemark = (ids, remark) => {
  return request({
    url: "/api/ni/depot/currentStock/changeRemark",
    method: "post",
    params: {
      ids,
      remark,
    },
  });
};
export const changeLocation = (params) => {
  return request({
    url: "/api/ni/depot/currentStock/changeLocation",
    method: "post",
    params,
  });
};

export const changeAmount = (params) => {
  return request({
    url: "/api/ni/depot/currentStock/changeAmount",
    method: "post",
    params,
  });
};
/**
 * 导出用，获取所有数据
 * @param params
 * @returns {*}
 */
export const getAllList = (params) => {
  return request({
    url: "/api/ni/depot/currentStock/list",
    method: "get",
    params,
  });
};
export const changePurpose = (ids, purpose) => {
  return request({
    url: "/api/ni/depot/currentStock/changePurpose",
    method: "post",
    params: {
      ids,
      purpose,
    },
  });
};

<template>
  <basic-container>
     <FullInfoForm :fullInfo="fullInfo" :autoComplete="false" ref="form">
       <template #otherTabs>
         <el-tab-pane label="NI培训" v-if="process.taskName === 'NI培训' || process.taskName === '人事确认'">
            <el-row>
              <el-col :span="12">
                <el-form-item label="分配角色">
                  <TreeSelect
                    :disabled="disableRule.niTraining"
                    v-model="niTrainingInfo.roles"
                    :data="roleTree"
                    :props="treeProps"
                    :multiple="true"
                    :check-strictly="true"
                    placeholder="请选择角色"
                    @change="handleStrictChange"
                  />
                </el-form-item>
                <el-form-item label="考勤号">
                  <el-select
                    :disabled="disableRule.niTraining"
                    filterable
                    @change="handleZkEccSelect"
                    v-model="fullInfo.baseInfo.signInCode">
                    <el-option
                      v-for="item in zkEccSelect"
                      :key="item.badgenumber"
                      :label="item.badgenumber"
                      :value="item.badgenumber">
                      {{item.badgenumber}}:{{ item.name}}
                    </el-option>
                  </el-select>
                </el-form-item>

<!--                <el-form-item label="培训记录">-->
<!--                  <el-upload-->
<!--                    :disabled="disableRule.niTraining"-->
<!--                    class="avatar-uploader"-->
<!--                    action="/api/blade-resource/oss/endpoint/put-file-attach"-->
<!--                    :headers="getAuthorizationHeader()"-->
<!--                    :show-file-list="false"-->
<!--                    list-type="picture-card"-->
<!--                    :on-success="(response, file, fileList) => customHandleSuccess(response,file,fileList,10)">-->
<!--                    <img v-if="niTrainingRecordFile.record" :src="niTrainingRecordFile.record" class="fileItem">-->
<!--                    <i v-else class="el-icon-plus avatar-uploader-icon"></i>-->
<!--                  </el-upload>-->
<!--                </el-form-item>-->
              </el-col>

              <el-col :span="12" >
                <el-form-item label="归属公司">
                  <el-select
                    :disabled="disableRule.niTraining"
                    v-model="niTrainingInfo.company">
                    <el-option
                      v-for="item in belongSelect"
                      :key="item.dictKey"
                      :label="item.dictValue"
                      :value="item.dictKey"/>
                  </el-select>
                </el-form-item>
                <el-form-item label="培训人员">
                  <el-select
                    :disabled="disableRule.niTraining"
                    filterable
                    v-model="niTrainingInfo.applyUser">
                    <el-option
                      v-for="item in userSelect"
                      :key="item.id"
                      :label="item.realName"
                      :value="item.id"/>
                  </el-select>
                </el-form-item>
<!--                <el-form-item label="考试结果">-->
<!--                  <el-upload-->
<!--                    :disabled="disableRule.niTraining"-->
<!--                    class="avatar-uploader"-->
<!--                    action="/api/blade-resource/oss/endpoint/put-file-attach"-->
<!--                    :headers="getAuthorizationHeader()"-->
<!--                    :show-file-list="false"-->
<!--                    list-type="picture-card"-->
<!--                    :on-success="(response, file, fileList) => customHandleSuccess(response, file, fileList,11)">-->
<!--                    <img v-if="niTrainingRecordFile.exam" :src="niTrainingRecordFile.exam" class="fileItem">-->
<!--                    <i v-else class="el-icon-plus avatar-uploader-icon"></i>-->
<!--                  </el-upload>-->
<!--                </el-form-item>-->
              </el-col>
            </el-row>
         </el-tab-pane>
         <el-tab-pane label="安环办培训" v-if="process.taskName === '安全培训公司级' || process.taskName ==='人事确认'" >
           <el-row>
             <el-col :span="12">
               <el-form-item label="培训时间">
                 <el-date-picker
                   :readonly="disableRule.safeTraining"
                   v-model="safeInfo.date"
                   type="date"
                   format="yyyy-MM-dd"
                   value-format="yyyy-MM-dd"
                   size="mini">
                 </el-date-picker>
               </el-form-item>
             </el-col>
             <el-col :span="12" >
               <el-form-item label="培训人员">
                 <el-select
                   :disabled="disableRule.safeTraining"
                   filterable
                   v-model="safeInfo.applyUser">
                   <el-option
                     v-for="item in userSelect"
                     :key="item.id"
                     :label="item.realName"
                     :value="item.id"/>
                 </el-select>
               </el-form-item>

             </el-col>
           </el-row>
           <el-collapse>
             <el-collapse-item title="特殊工种">
               <div class="table-header">
                 <el-button v-if="!disableRule.safeTraining"  @click="addSpecializedWork" size="mini" type="primary">新增证件</el-button>
               </div>
               <el-table :data="specializedWorkList" border size="mini">
                 <el-table-column prop="type" label="证件类型">
                   <template slot-scope="scope">
                     <el-input :readonly="disableRule.safeTraining"  v-model="scope.row.type" size="mini"></el-input>
                   </template>
                 </el-table-column>
                 <el-table-column prop="certificateNo" label="证件号">
                   <template slot-scope="scope">
                     <el-input :readonly="disableRule.safeTraining"  v-model="scope.row.certificateNo" size="mini"></el-input>
                   </template>
                 </el-table-column>
                 <el-table-column prop="startDate" label="起始时间">
                   <template slot-scope="scope">
                     <el-date-picker
                       :readonly="disableRule.safeTraining"
                       v-model="scope.row.startDate"
                       type="date"
                       format="yyyy-MM-dd"
                       value-format="yyyy-MM-dd"
                       size="mini">
                     </el-date-picker>
                   </template>
                 </el-table-column>
                 <el-table-column prop="endDate" label="到期时间">
                   <template slot-scope="scope">
                     <el-date-picker
                       :readonly="disableRule.safeTraining"
                       v-model="scope.row.endDate"
                       type="date"
                       format="yyyy-MM-dd"
                       value-format="yyyy-MM-dd"
                       size="mini">
                     </el-date-picker>
                   </template>
                 </el-table-column>

                 <el-table-column prop="fileUrl" label="附件">
                   <template slot-scope="scope">
                     <el-upload
                       :disabled="disableRule.safeTraining"
                       class="small-picture-card"
                       action="/api/blade-resource/oss/endpoint/put-file-attach"
                       :show-file-list="false"
                       :headers="getAuthorizationHeader()"
                       list-type="picture-card"
                       :on-success="(response, file, fileList) => handleSpecializedWorkFile(response, file, fileList, scope.row)">
                       <img v-if="scope.row.fileUrl" :src="scope.row.fileUrl" class="avatar">
                       <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                     </el-upload>

                   </template>
                 </el-table-column>

                 <el-table-column v-if="!disableRule.safeTraining"  label="操作">
                   <template slot-scope="scope">
                     <el-button
                       @click="deleteSpecializedWork(scope.$index)"
                       type="danger"
                       size="mini"
                       plain>删除</el-button>
                   </template>
                 </el-table-column>
               </el-table>
             </el-collapse-item>
           </el-collapse>
         </el-tab-pane>
         <el-tab-pane label="流转信息" name="second">
           <el-card shadow="never" style="margin-top: 5px">
             <wf-flow :flow="flow"></wf-flow>
           </el-card>
         </el-tab-pane>
         <el-tab-pane label="流程跟踪" name="third">
           <template v-if="activeName === 'third'" >
             <el-card shadow="never" style="margin-top: 5px">
               <wf-design ref="bpmn" style="height: 500px" :options="bpmnOption"></wf-design>
             </el-card>
           </template>
         </el-tab-pane>
       </template>
       <template #buttons>
         <el-card shadow="never" style="margin-top: 20px" v-if="process.status === 'todo'">
           <wf-examine-form ref="examineForm" :comment.sync="comment" :process="process"
                            @user-select="handleUserSelect"></wf-examine-form>
         </el-card>
       </template>
     </FullInfoForm>
    <!-- 底部按钮 -->
    <wf-button ref="wfButtonRef" :loading="submitLoading" :button-list="buttonList" :process="process"
               :comment="comment" @examine="handleExamine" @user-select="handleUserSelect" @print="handlePrint"
               @rollback="handleRollbackTask" @terminate="handleTerminateProcess" @withdraw="handleWithdrawTask"></wf-button>
    <!-- 人员选择弹窗 -->
    <user-select ref="user-select" :check-type="checkType" :default-checked="defaultChecked"
                 @onConfirm="handleUserSelectConfirm"></user-select>
<!--    <attach-dialog ref="attachRef" code="public" :delBtn="false" @close="handleRefreshAttach" />-->
  </basic-container>
</template>

<script>
import WfExamineForm from "@/views/plugin/workflow/process/components/examForm.vue";
import WfButton from "@/views/plugin/workflow/process/components/button.vue";
import WfFlow from "@/views/plugin/workflow/process/components/flow.vue";
import userSelect from "@/views/plugin/workflow/process/components/user-select";
import exForm from "@/views/plugin/workflow/mixins/ex-form";
import theme from "@/views/plugin/workflow/mixins/theme";
import AttachDialog from "@/components/attach-dialog";
import FullInfoForm from "@/views/ni/pa/components/employeeFullInfo.vue";
import {getRoleTree} from "@/api/system/role";
import TreeSelect from "@/views/ni/pa/components/TreeSelect.vue";
import {getCompanyItem, getUserItem, getZkEccItem} from "@/api/selectUtils";
import {getAuthorizationHeader} from "@/api/resource/fileManagement";
import request from "@/router/axios";

export default {
  mixins: [exForm, theme],
  components: {
    TreeSelect,
    FullInfoForm,
    userSelect,
    WfExamineForm,
    WfButton,
    WfFlow,
    AttachDialog,
  },
  watch: {
    '$route.query.p': {
      immediate: true,
      handler(val) {
        console.info('route')
        if (val) {
          const param = JSON.parse(Buffer.from(val, "base64").toString());
          const { taskId, processInsId } = param;
          if (processInsId) {
            //TODO 根据processInsId加载form数据
            this.getFormInfo(taskId, processInsId);
          }
        }
      },
    }
  },
  data() {
    return {
      fromPath:null,
      activeName: "first",
      defaults: {},
      form: {},
      carNumVisible:false,
      tagInputVal:'',
      carNumTags:[],
      visible:false,
      loading: false,
      politicalSelect:[],
      eduSelect:[],
      regionSelect:[],
      contractStatusSelect:[
        {label:"未签约",value:1},
        {label:"未与上家解除",value:0},
        {label:"已签约",value:2},
        {label:"已解除",value:3},
      ],
      yesOrNoSelect: [
        {label:'否',value:0},
        {label: '是', value:1},
      ],
      eduTypeSelect: [
        {label:'第一学历',value:3},
        {label:'最高学历',value:2},
        {label:'在读',value:0},
        {label:'毕业',value:1}],
      deptSelect:[],
      postSelect:[],
      belongSelect:[],
      userSelect:[],
      zkEccSelect:[],
      treeProps: {
        children: 'children',
        label: 'title',
        value: 'value'
      },
      postTree:[],
      deptTree:[],
      roleTree:[],
      deptMap:{},
      postMap:{},
      fullInfo:{
        esign:1,
        baseInfo:{
          avatar:'',
          avatarFileId:'',
          name:'',
          ethnicity:'',
          idCard:'',
          phone:'',
          address:'',
          enterDate:'',
          fillTime:'',
          otherPhone:'',
          deptId:'',
          positionId:'',
          resumeId:'',
          carNum:'',
          contractStatus:'',
          code:''
        },
        extInfo:{
          householdRegister:'',
          householdRegisterCode:'',
          marriage:'',
          politic:'',
          religion:'',
          currentAddress:'',
          currentAddressCode:'',
          professionalTitle:'',
          driverLicense:'',
          drivingExperienceYears:'',
          hobbies:'',
          englishProficiency:'',
          bankCard:'',
          bankCardFileId:'',
          bankCardNum:'',
          hasIntellectualProperty:'',
          hasCompetitionRestriction:'',
          researchField:'',
          familyAddress:'',
          familyAddressCode:'',
          hostel:0,
          intern:0,
          idCardBackPicture:'',
          idCardBackPictureFileId:'',
          idCardPicture:'',
          idCardPictureFileId:'',
        },
        familyMembers:[],
        educations:[],
        workExperiences:[],
        files:[],
        trainingList:[],
        dormitory:{
          region:'',
          code:'',
          roomCode:'',
          bedCode:''
        }
      },
      niTrainingInfo:{
        roles: [],
        company:'',
        signInCode:'',
        zkEccUserId:'',
        applyUser:'',
      },
      safeInfo:{
        applyUser:'',
        date:'',
      },
      specializedWorkList:[],
      vars: [], // 需要提交的字段
      submitLoading: false, // 提交时按钮loading
      disableRule: {
        niTraining:false,
        safeTraining:false,
        back:{
          baseInfo: false,
          extInfo: false,
          familyMembers: true,
          educations: true,
          workExperiences: true,
          files: true,
          trainingList: false,
          dormitory: false,
        },
        hr:{
          baseInfo: true,
          extInfo: true,
          familyMembers: false,
          educations: false,
          workExperiences: false,
          files: false,
          trainingList: false,
          dormitory: true
        },
        dormitory:{
          baseInfo: true,
          extInfo: true,
          familyMembers: false,
          educations: false,
          workExperiences: false,
          files: true,
          trainingList: false,
          dormitory: false
        },
        ni:{
          baseInfo: true,
          extInfo: true,
          familyMembers: false,
          educations: false,
          workExperiences: false,
          files: true,
          trainingList: false,
          dormitory: true
        },
        safe:{
          baseInfo: true,
          extInfo: true,
          familyMembers: false,
          educations: false,
          workExperiences: false,
          files: true,
          trainingList: false,
          dormitory: true
        }
      },
    };
  },
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      //  这里的vm指的就是vue实例，可以用来当做this使用
      vm.fromPath = from.path; //获取上一级路由的路径
    });
  },
  created() {
    getRoleTree('000000').then((res) => {
      this.roleTree = res.data.data;
    });
    getCompanyItem().then((res) => {
        this.belongSelect = res.data.data;
    });
    getUserItem().then((res) => {
        this.userSelect = res.data.data;
    });
    getZkEccItem().then((res) => {
        this.zkEccSelect = res.data.data;
    });
  },
  computed:{
    niTrainingRecordFile(){
      return{
        record: this.$refs.form.findFile(10),
        exam: this.$refs.form.findFile(11),
      }
    }
  },
  mounted() {
    // roleTree
    console.info('mounted')
  },
  methods: {
    getAuthorizationHeader,
    customHandleSuccess(response,file,fileList,type){
      this.$refs.form.handleFilesSuccess(response, file, fileList,type)
    },
    handleStrictChange(value, nodes, checkedInfo) {
      // this.roles.push(value)
      console.log('严格模式变化：', value, nodes, checkedInfo)
    },
    handleSpecializedWorkFile(response, file, fileList, row) {
      this.$set(row,'fileId', response.data.attachId)
      this.$set(row,'fileUrl', response.data.link)
    },
    handleZkEccSelect(val){
      let select = this.zkEccSelect.findLast(item => item.badgenumber === val)
      if (select){
        this.niTrainingInfo.signInCode = select.badgenumber;
        this.niTrainingInfo.zkEccUserId = select.userid;
      }
    },
    addSpecializedWork() {
      this.specializedWorkList.push({ startDate:'',endDate:'',certificateNo: '', type:'', fileId: '', fileUrl: ''});
    },
    deleteSpecializedWork(index) {
      this.specializedWorkList.splice(index, 1);
    },
    // 获取任务详情
    getFormInfo(taskId, processInsId) {

      this.getTaskDetail(taskId, processInsId).then((res) => {
        const { process } = res;
        const { variables, processIsFinished } = process;
        let info = variables;
        if (!info.dormitory){
           info.dormitory = Object.assign({}, this.fullInfo.dormitory);
        }
        if (info.safeInfo){
          this.safeInfo = Object.assign({}, info.safeInfo);
        }
        if (info.specializedWorkList){
          this.specializedWorkList = info.specializedWorkList;
        }

        this.fullInfo = info;
        this.fullInfo.wf_latest_task_assignee = process.assignee;
        this.niTrainingInfo = Object.assign({}, variables.niTrainingInfo);
        if (this.fullInfo.baseInfo.carNum){
          this.$refs.form.carNumTags = this.fullInfo.baseInfo.carNum.split(",");
        }
        this.handleZkEccSelect(this.fullInfo.baseInfo.signInCode)
        console.info('getDetails',process)
        if(process.taskName === '人事培训'){
          this.$refs.form.displayByProcess(this.disableRule.hr);
        }
        if(process.taskName === '办公室审批宿舍'){
          this.$refs.form.displayByProcess(this.disableRule.dormitory);
        }

        if(process.taskName === 'NI培训'){
          this.disableRule.niTraining = false
          this.$refs.form.displayByProcess(this.disableRule.ni);
        }
        if(process.taskName === '安全培训公司级'){
          this.disableRule.niTraining = true
          this.disableRule.safeTraining = false
          this.$refs.form.displayByProcess(this.disableRule.safe);
        }
      });
    },
    // 审核
    handleExamine(pass) {
      this.submitLoading = true;
      // zkEccSelect
      this.fullInfo.niTrainingInfo = this.niTrainingInfo
      this.fullInfo.safeInfo = this.safeInfo;
      this.fullInfo.specializedWorkList = this.specializedWorkList
      this.fullInfo.pass = pass;
      this.handleCompleteTask(pass, this.fullInfo)
        .then(() => {
          this.submitLoading = false;
          this.$message.success("处理成功");
          if (this.fromPath) {
            this.handleCloseTag(this.fromPath);
          } else
            this.handleCloseTag("/plugin/workflow/process/todo");
        })
        .catch(() => {
          this.submitLoading = false;
          this.$message.error("处理失败");
        });
    },
  },
};
</script>

<style lang="scss" scoped>
.header {
  width: 100%;
  height: 50px;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 10px 10px 0;
}
.fileItem {
  width: 72px;
  height: 72px;
}
.foot-item {
  position: fixed;
  bottom: 0;
  margin-left: -20px;
  // right: 0;
  z-index: 101;
  height: 66px;
  background-color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  -webkit-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
</style>

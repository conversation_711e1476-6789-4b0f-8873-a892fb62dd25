import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/ni/fin/material-allocation/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/ni/fin/material-allocation/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/ni/fin/material-allocation/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/ni/fin/material-allocation/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/ni/fin/material-allocation/update',
    method: 'post',
    data: row
  })
}

export const getMaterialPurpose = (orderItemIds) => {
  return request({
    url: '/api/ni/fin/material-allocation/getMaterialPurpose',
    method: 'get',
    params: {
      orderItemIds
    }
  })
}

export const exportBlob = (params) => {
  return request({
    url: `/api/ni/fin/material-allocation/export`,
    method: "get",
    responseType: "blob",
    params: {
      ...params
    }
  });
};

export const getDetailByParams = (params) => {
  return request({
    url: '/api/ni/fin/material-allocation/getDetailByParams',
    method: 'get',
    params: {
      ...params
    }
  })
}

//根据当前界面发票号获取默认的项目
export const selectProjectInfo = (serialNos) => {
  return request({
    url: '/api/ni/fin/material-allocation/selectProjectInfo',
    method: 'get',
    params: {
      serialNos
    }
  })
}

export const changeProjectInfo = (data) => {
  return request({
    url: '/api/ni/fin/material-allocation/changeProjectInfo',
    method: 'post', 
    data: data,
  });
}

export const getConfirmPage = (current, size, params) => {
  return request({
    url: '/api/ni/fin/material-allocation/getConfirmPage',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const confirmTag = (form) => {
  return request({
    url: '/api/ni/fin/material-allocation/confirmTag',
    method: 'post',
    data: form,
  })
}

export const changeProject = (selectionList,catalogId) => {
  return request({
    url: '/api/ni/fin/material-allocation/changeProject',
    method: 'post',
    data: selectionList,
    params:{
      catalogId,
    }
  })
}

export const confirmNotice = (month) => {
  return request({
    url: '/api/ni/fin/material-allocation/confirmNotice',
    method: 'post',
    params:{
      month,
    }
  })
}
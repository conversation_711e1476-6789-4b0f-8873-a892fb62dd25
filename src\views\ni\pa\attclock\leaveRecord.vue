<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
    <template slot="status" slot-scope="{ row }">
      <el-tag :type="getStatusType(row.status)">{{ getStatusText(row.status) }}</el-tag>
    </template>

    <template slot="reviewStatus" slot-scope="{ row }">
      <el-tag :type="getReviewStatusType(row.reviewStatus)">{{ getReviewStatusText(row.reviewStatus) }}</el-tag>
    </template>

      <template #menuLeft>
        <el-button
          v-if="permission.electron_leave_print"
          type="warning"
          size="mini"
          icon="el-icon-printer"
          plain
          @click="handlePrint"
          >打印
        </el-button>
      </template>

      <template slot-scope="{ row }" slot="menu">
        <el-button
          type="text"
          icon="el-icon-info"
          size="small"
          v-if="permission.flow_info&&row.processInstanceId"
          @click.stop="handleFlowInfo(row)"
          >流程详情
        </el-button>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import {
  getList,
  getDetail,
  add,
  update,
  remove,
} from "@/api/ni/pa/attendance/leaveRecord";
import option from "@/const/ni/pa/leaveRecord";
import { mapGetters } from "vuex";
import { hiprint } from "vue-plugin-hiprint";
import { loadPrintTemplate } from "@/api/system/printTemplate";
export default {
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: option,
      data: [],
      printTemplateJson: {},
      hiprintTemplate: null,
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.leaveRecord_add, false),
        viewBtn: this.vaildData(this.permission.leaveRecord_view, false),
        delBtn: this.vaildData(this.permission.leaveRecord_delete, false),
        editBtn: this.vaildData(this.permission.leaveRecord_edit, false),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  methods: {
    getStatusText(status) {
      const statusMap = {
        0: '已作废',
        1: '生效',
        2: ''
      };
      return statusMap[status] || ''; 
    },
    getStatusType(status) {
      const statusMap = {
        0: 'info',
        1: 'success',
        2: 'info'
      };
      return statusMap[status] || 'info'; 
    },
    
    getReviewStatusText(status) {
      const statusMap = {
        1: '已提交',
        2: '已审核',
        3: '已驳回',
        4: '已终止'
      };
      return statusMap[status] || '暂无';
    },
    
    getReviewStatusType(status) {
      const typeMap = {
        1: 'warning', 
        2: 'success', 
        3: 'danger',   
        4: 'info'
      };
      return typeMap[status] || 'info';
    },

    async getPrintTempJsonData() {
      const res = await loadPrintTemplate("ni_electron_leave");

      this.printTemplateJson = JSON.parse(res.data.data.content);

      this.hiprintTemplate = new hiprint.PrintTemplate({
        template: this.printTemplateJson
      });
    },
    async handlePrint() {

      const loading = this.$loading({
          lock: false,
          text: "正在准备打印中...",
          spinner: "el-icon-loading",
        });

      if (!this.hiprintTemplate) {
        try {
          await this.getPrintTempJsonData();
        } catch (error) {
            console.log(error)
        }
        
      }

      const STATUS_TEXT_MAP = {
        0: '已作废',
        1: '生效',
        2: ''
      };
      const REVIEW_STATUS_TEXT_MAP = {
        1: '已提交',
        2: '已审核',
        3: '已驳回',
        4: '已终止'
      };

      getList(1, 999999999, this.query).then((res) => {
        const data = res.data.data.records;
        let isPrinting = true;
        data.forEach(item => {
          //存在审核状态不为“已审核”、“终止”的记录给出提醒，即（已提交、已驳回）
          if(item.reviewStatus != 2 && item.reviewStatus != 4){
            isPrinting = false;
          }
        });

        const recordsToPrint = data.filter(item => 
          //打印 审核状态：已审核；状态：生效、已作废 
          item.reviewStatus === 2 && item.status !== 2
        );

        recordsToPrint.forEach(item => {
          item.statusText = STATUS_TEXT_MAP[item.status] || '';
          item.reviewStatusText = REVIEW_STATUS_TEXT_MAP[item.reviewStatus] || '';
        });

        if(recordsToPrint.length === 0){
          this.$message.warning('没有已审核的请假记录，请检查筛选条件！');
        }else if( !isPrinting){
          // 弹窗提醒用户是否继续打印
          this.$confirm('存在未完成审核的记录，是否继续打印？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
          }).then(() => {
            this.hiprintTemplate.print({data:recordsToPrint});
          }).catch(() => {
           //取消打印
          });
        }else{
          this.hiprintTemplate.print({data:recordsToPrint});
        }
      
      }).finally(()=>{
        loading.close();
      });
    },
    rowSave(row, done, loading) {
      add(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          window.console.log(error);
        }
      );
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          console.log(error);
        }
      );
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen(done, type) {
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then((res) => {
          this.form = res.data.data;
        });
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
    handleFlowInfo({processInstanceId}){
     const urlParam= Buffer.from(JSON.stringify({processInsId:processInstanceId})).toString('base64');
     this.$router.push(`/workflow/process/detail/${urlParam}`)
 
    }
  },
};
</script>

<style></style>

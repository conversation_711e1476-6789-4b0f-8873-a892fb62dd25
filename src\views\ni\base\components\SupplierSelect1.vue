<template>
  <div>
    <el-input
      v-model="name"
      :size="size"
      suffix-icon="el-icon-search"
      :disabled="disabled"
      readonly
      @click.native="handleSelect"
      @clear="handleClear"
    />
    <supplier-select-dialog
      ref="supplierSelectRef"
      :multiple="multiple"
      v-model="value"
      :params="params"
      @submit="handleSupplierSubmit"
    />
  </div>
</template>

<script>
import SupplierSelectDialog from "@/views/ni/base/components/SupplierSelectDialog";
import { getDetail } from "@/api/ni/base/supplier/supplierinfo";
import Emitter from "element-ui/src/mixins/emitter";

export default {
  name: "SupplierSelect",
  mixins: [Emitter],
  components: {
    SupplierSelectDialog,
  },
  props: {
    value: {
      type: String,
    },
    size: {
      type: String,
      default: "mini",
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    multiple: {
      type: Boolean,
      default: false,
    },
    params: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      name: "",
    };
  },
  watch: {
    value: {
      handler(val) {
        if (val) {
          const name = [];
          const checks = (val + "").split(",");
          const asyncList = [];
          checks.forEach((c) => {
            asyncList.push(getDetail(c));
          });
          Promise.all(asyncList).then((res) => {
            res.forEach((r) => {
              const data = r.data.data;
              if (data) name.push(data.name);
            });
            this.$set(this, "name", name.join(","));
          });
        } else this.$set(this, "name", "");
      },
      immediate: true,
    },
  },
  methods: {
    handleClear() {
      this.$emit("input", ""); // 触发 v-model 更新
      this.name = ""; // 清空显示名称
      this.dispatch("ElFormItem", "el.form.blur", [""]);
    },
    handleSelect() {
      if (this.readonly || this.disabled) return;
      else this.$refs.supplierSelectRef.init();
    },
    handleSupplierSubmit(selectList) {
      if (selectList) {
        const ids = selectList.map((item) => item.id);
        this.$emit("input", ids.join(","));
        this.$emit("submit", selectList);
        this.$nextTick(() => {
          this.dispatch("ElFormItem", "el.form.blur", [this.codes]);
        });
      }
    },
  },
};
</script>

<style scoped></style>

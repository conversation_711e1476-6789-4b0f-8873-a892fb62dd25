import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/ni/fg/transfer/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/ni/fg/transfer/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/ni/fg/transfer/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/ni/fg/transfer/save',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/ni/fg/transfer/update',
    method: 'post',
    data: row
  })
}

export const audit = (id) => {
  return request({
    url: '/api/ni/fg/transfer/audit/' + id,
    method: 'post',
  })
}

export const reverse = (id, quantity) => {
  return request({
    url: '/api/ni/fg/transfer/reverse',
    method: 'post',
    params: {
      id,
      quantity
    }
  })
}

export const finish = (row) => {
  return request({
    url: '/api/ni/fg/transfer/finish',
    method: 'post',
    data: row
  })
}

export const ship = (id) => {
  return request({
    url: '/api/ni/fg/transfer/ship/' + id,
    method: 'post'
  })
}

export const receive = (data) => {
  return request({
    url: '/api/ni/fg/transfer/receive',
    method: 'post',
    data
  })
}
export const red = (id, reason) => {
  return request({
    url: '/api/ni/fg/transfer/red/' + id,
    method: 'post',
    params: {
      reason,
    }
  })
}

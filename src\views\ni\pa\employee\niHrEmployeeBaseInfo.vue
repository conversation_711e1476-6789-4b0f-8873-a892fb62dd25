<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page.sync="page"
               :permission="permissionList"
               v-model="form"
               ref="crud"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @refresh-change="refreshChange"
               @on-load="onLoad">
      <template slot="deptId" slot-scope="{ row }">
        <el-tag v-if="row.deptId">{{ displayDept(row.deptId)}}</el-tag>
      </template>
      <template slot="positionId" slot-scope="{ row }">
        <el-tag v-if="row.positionId">{{ displayPosition(row.positionId)}}</el-tag>
      </template>

      <template slot="status" slot-scope="{ row }">
        <el-tag v-if="row.status == 0">入职前</el-tag>
        <el-tag v-if="row.status == 1">未审批</el-tag>
        <el-tag v-if="row.status == 2">审批中</el-tag>
        <el-tag v-if="row.status == 3">正常</el-tag>
        <el-tag v-if="row.status == 4" >离职</el-tag>
      </template>

      <template slot="contractStatus" slot-scope="{ row }">
        <el-tag v-if="row.contractStatus">{{ displayContractStatus(row.contractStatus) }}</el-tag>
      </template>

      <template slot="menuLeft">
        <el-button type="primary"
                   size="small"
                   icon="el-icon-plus"
                   plain
                   v-if="permission.niHrEmployeeBaseInfo_add"
                   @click="addDialogVisible = true">新增</el-button>

        <el-button type="primary"
                   size="small"
                   icon="el-icon-plus"
                   plain
                   v-if="permission.niHrEmployeeBaseInfo_add"
                   @click="inviteDialogVisible = true">邀请员工</el-button>

        <el-button type="danger"
                   size="small"
                   icon="el-icon-delete"
                   plain
                   v-if="permission.niHrEmployeeBaseInfo_delete"
                   @click="handleDelete">删 除
        </el-button>
      </template>
      <template #menu="{ size, row, index }">
        <el-button
          v-if="row.status === 1"
          size="small"  type="primary"
          @click="rewrite(row.id)"
          >员工填写</el-button>
<!--        <el-button-->
<!--        @click="startOnBound(row.id)"-->
<!--        >办理入职</el-button>-->
        <el-button
          v-if="row.status !== 2"
          size="small"  type="primary"
          @click="rowEdit(row)" >编辑</el-button>

        <el-button
          size="small"
          @click="viewFullInfo(row)" >查看详情</el-button>
        <el-button
          size="small"
          type="danger"
          v-if="row.status !==2"
          @click="rowDel(row)">删除</el-button>
      </template>

    </avue-crud>

    <el-dialog title="邀请" :visible.sync="inviteDialogVisible" width="50%" append-to-body   @closed="handleDialogClosed">
      <el-form>

      <el-row>
        <el-form-item label="姓名">
          <el-input  v-model="inviteName"/>
        </el-form-item>
        <el-form-item label="手机号">
          <el-input  v-model="invitePhone"/>
        </el-form-item>
      </el-row>

      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="inviteDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="addInviteInfo()">添加</el-button>
      </span>
    </el-dialog>
    <el-dialog title="编辑" :visible.sync="addDialogVisible" width="80%" append-to-body   @closed="handleDialogClosed">
      <FullInfoForm :fullInfo="fullInfo" :autoComplete="false" ref="editDialogRef"></FullInfoForm>

      <span slot="footer" class="dialog-footer">
        <el-button @click="addDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="edit()">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog title="详情" :visible.sync="detailDialogVisible" width="100%" append-to-body >
      <FullInfoForm :fullInfo="fullInfo" :autoComplete="false"  ref="detailsRef"></FullInfoForm>
    </el-dialog>
  </basic-container>
</template>

<script>
import {add, getList, remove, update, fullInfo, rewrite, invite} from "@/api/ni/emhire/niHrEmployeeBaseInfo";
import exForm from "@/views/plugin/workflow/mixins/ex-form";
import option from "@/const/ni/emhire/niHrEmployeeBaseInfo";
import {mapGetters} from "vuex";
import {getRegionLazyTree} from "@/api/base/region";
import {getDeptTree} from "@/api/system/dept";
import {getPostList} from "@/api/system/post";
import {getJobs} from "@/api/ni/emhire/niEmployeeResumeInfo";
import {getAuthorizationHeader} from "@/api/resource/fileManagement";
import FullInfoForm from "@/views/ni/pa/components/employeeFullInfo.vue";
import {getDeptItem, getEduItem, getPoliticalItem} from "@/api/selectUtils";

export default {
  mixins: [exForm],
  components: {FullInfoForm},
    data() {
      return {
        form: {},
        query: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        option: option,
        data: [],

        carNumVisible:false,
        tagInputVal:'',
        carNumTags:[],

        politicalSelect:[],
        eduSelect:[],
        regionSelect:[],
        contractStatusSelect:[
          {label:"未签约",value:1},
          {label:"未与上家解除",value:0},
          {label:"已签约",value:2},
          {label:"已解除",value:3},
        ],
        yesOrNoSelect: [
          {label:'否',value:0},
          {label: '是', value:1},
        ],
        eduTypeSelect: [
          {label:'第一学历',value:3},
          {label:'最高学历',value:2},
          {label:'在读',value:0},
          {label:'毕业',value:1}],
        deptSelect:[],
        postSelect:[],

        postTree:[],
        deptTree:[],
        deptMap:{},
        postMap:{},

        inviteDialogVisible:false,
        detailDialogVisible:false,
        addDialogVisible:false,
        inviteName:'',
        invitePhone:'',

        fullInfo:{
          baseInfo:{
            avatar:'',
            avatarFileId:'',
            name:'',
            ethnicity:'',
            idCard:'',
            phone:'',
            address:'',
            enterDate:'',
            fillTime:'',
            otherPhone:'',
            deptId:'',
            positionId:'',
            resumeId:'',
            carNum:'',
            contractStatus:'',
            code:''
          },
          extInfo:{
            householdRegister:'',
            householdRegisterCode:'',
            marriage:'',
            politic:'',
            religion:'',
            currentAddress:'',
            currentAddressCode:'',
            professionalTitle:'',
            driverLicense:'',
            drivingExperienceYears:'',
            hobbies:'',
            englishProficiency:'',
            bankCard:'',
            bankCardFileId:'',
            bankCardNum:'',
            hasIntellectualProperty:'',
            hasCompetitionRestriction:'',
            researchField:'',
            familyAddress:'',
            familyAddressCode:'',
            hostel:0,
            intern:0,
            idCardBackPicture:'',
            idCardBackPictureFileId:'',
            idCardPicture:'',
            idCardPictureFileId:'',
          },
          familyMembers:[],
          educations:[],
          workExperiences:[],
          files:[],
          trainingList:[],
          dormitory:{
            region:'',
            code:'',
            roomCode:'',
            bedCode:''
          }
        },
        displayRule:{
          baseInfo: true,
          extInfo: true,
          familyMembers: false,
          educations: false,
          workExperiences: false,
          files: false,
          trainingList: false,
          dormitory: true
        }
      };
    },
    computed: {
      ...mapGetters(["permission","userInfo"]),
      permissionList() {
        return {
          addBtn: this.vaildData(this.permission.niHrEmployeeBaseInfo_add, false),
          viewBtn: this.vaildData(this.permission.niHrEmployeeBaseInfo_view, false),
          delBtn: this.vaildData(this.permission.niHrEmployeeBaseInfo_delete, false),
          editBtn: this.vaildData(this.permission.niHrEmployeeBaseInfo_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    created() {
      this.initDic();
    },
    methods: {
      getAuthorizationHeader,
      startOnBound(id){
        this.dynamicRoute(
          {
            processDefKey: 'employee_entry',
            formKey: '',
            form: encodeURIComponent(
              Buffer.from(JSON.stringify({employee:id})).toString("utf8")
            ),
          },
          "start"
        );
      },
      addInviteInfo(){
        invite(this.inviteName.trim(),this.invitePhone.trim()).then((res) => {
          if (res.data.code == 200) {
            this.inviteDialogVisible = false;
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          }
        }).catch(err=>{
          console.log(err);
          this.$message({
            type: "error",
            message: "操作失败!"
          });
        })
      },
      handleDialogClosed(){
        this.inviteName = '';
        this.invitePhone = '';
        this.tagInputVal='';
        this.carNumTags=[];
        this.fullInfo = Object.assign({}, this.$options.data().fullInfo)
        this.$refs.editDialogRef.clearCarNum();
      },
      edit(){
        this.fullInfo.baseInfo.carNum = this.$refs.editDialogRef.getCarNum()
        this.fullInfo.extInfo.ext = JSON.stringify(this.fullInfo.dormitory)
        console.info(this.fullInfo)
        update(this.fullInfo).then(() => {
          this.onLoad(this.page);
          this.addDialogVisible = false;
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          window.console.log(error);
        });
      },
      rewrite(id){
        rewrite(id).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          this.$message({
            type: "error",
            message: "操作失败!"
          });
          window.console.log(error);
        });
      },
      displayPosition(positionId){
        if (positionId){
          return this.postMap.get(positionId)
        }
      },
      displayDept(deptId){
        if (deptId){
          return this.deptMap.get(deptId)
        }
      },
      displayContractStatus(contractStatus){
        return this.contractStatusSelect[contractStatus]? this.contractStatusSelect[contractStatus].label :'未填写'
      },
      initDic() {
        getPoliticalItem().then((res) => {
            this.politicalSelect = res.data.data;
          });
        getEduItem().then((res) => {
            this.eduSelect = res.data.data;
          });
        getRegionLazyTree().then((res) => {
          this.regionSelect = res.data.data;
        });
        getDeptItem().then((res) => {
          this.deptSelect = res.data.data;
          this.deptMap = new Map(this.deptSelect.map(item => [item.id,item.deptName]));
        });
         getJobs().then(res => {
           this.postSelect = res.data.data;
          this.postMap = new Map(
            this.postSelect.map(object => {
              return [object.id, object.postName];
            })
          );
        })
        getDeptTree(this.userInfo.tenantId).then((res) => {
          this.deptTree = res.data.data;
        });
        getPostList(this.userInfo.tenantId).then((res) => {
          this.postTree = res.data.data;
        });
      },
      rowEdit(row){
        fullInfo(row.id)
          .then(response => {
            this.fullInfo = response.data.data;
            this.addDialogVisible = true;
            if (this.fullInfo.extInfo.ext){
              this.fullInfo.dormitory = JSON.parse(this.fullInfo.extInfo.ext);
            }
            this.$nextTick(()=>{
              this.$refs.editDialogRef.setCarNum();
            })
          }).catch(error => {
            this.$message({
              type: "error",
              message: "操作失败!"
            });
            console.error(error);
          })
      },
      viewFullInfo(row){
        fullInfo(row.id)
          .then(response => {
            this.fullInfo = response.data.data;
            this.detailDialogVisible = true;

            if (this.fullInfo.extInfo.ext){
              this.fullInfo.dormitory = JSON.parse(this.fullInfo.extInfo.ext);
            }

            this.$nextTick(()=>{
              this.$refs.detailsRef.displayByProcess(this.displayRule)
              this.$refs.detailsRef.setCarNum();
            })
          }).catch(error => {
          this.$message({
            type: "error",
            message: "操作失败!"
          });
          console.error(error);
        })
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      handleSuccess(response) {
        this.fullInfo.baseInfo.photo = response.data.link
        console.info(this.fullInfo.baseInfo.photo)
      },
      handleDynamicSuccess(response, file, fileList, row,prop) {
        console.info(response);
        this.$set(row,prop, response.data.link)
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        console.info(params);
        this.query = params;
        this.page.currentPage = 1;
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        // this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      refreshChange() {
        this.onLoad(this.page, this.query);
      },
      onLoad(page, params = {}) {
        this.loading = true;
        getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      }
    }
  };
</script>

<style scoped>
.el-select{width: 100%;}
.small-picture-card {
  .el-upload--picture-card {
    width: 80px;
    height: 80px;
    line-height: 80px;
    font-size: 24px;
    padding: 0;
  }

  .el-upload-list--picture-card .el-upload-list__item {
    width: 80px;
    height: 80px;
    line-height: 80px;
  }

  .el-upload--picture-card .el-upload__input {
    display: none;
  }
}
</style>

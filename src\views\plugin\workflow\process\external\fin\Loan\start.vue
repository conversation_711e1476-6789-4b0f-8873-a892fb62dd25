<template>
  <basic-container>
    <avue-skeleton :loading="waiting" avatar :rows="8">
      <div style="display: flex;">
      <avue-title
        style="margin-bottom: 20px"
        :styles="{ fontSize: '20px' }"
        :value="process.name"
      ></avue-title>
        <el-badge v-if="permission.wf_process_draft&&draftCount > 0" :value="draftCount"
                  style="margin-top: 5px;  margin-right: 40px;" type="warning">
          <el-button
            size="mini"
            v-loading="loading"
            @click="handleDraftBox"
          >草稿箱
          </el-button>
        </el-badge>
      </div>
      <el-card shadow="never" style="margin-top: 20px">
        <!-- 自定义表单区域 -->
        <avue-form
          v-if="
            option &&
            ((option.column && option.column.length > 0) ||
              (option.group && option.group.length > 0))
          "
          v-model="form"
          ref="form"
          :option="option"
          :defaults.sync="defaults"
          @submit="handleSubmit"
          @error="loading = false"
          :upload-preview="handleUploadPreview"
        >
        </avue-form>
        <!-- 自定义表单区域 -->
      </el-card>

      <el-card shadow="never" style="margin-top: 20px" v-if="showExamForm">
        <wf-examine-form
          ref="examineForm"
          :process="process"
          @user-select="handleUserSelect"
        ></wf-examine-form>
      </el-card>
      <div style="height: 120px"></div>
      <el-row
        class="foot-item avue-affix"
        :style="{
          width: isCollapse ? 'calc(100% - 80px)' : 'calc(100% - 260px)',
        }"
        id="avue-view"
      >
        <el-button
          v-no-more-click
          type="primary"
          size="mini"
          v-loading="loading"
          @click="handleSubmit"
          >发起
        </el-button>
        <el-button
          v-if="permission.wf_process_draft"
          type="success"
          size="mini"
          v-loading="loading"
          @click="handleDraftNotClose(process.id, process.formKey, form, process.key)"
          >存为草稿
        </el-button>
        <el-button
          v-if="permission.wf_process_draft"
          type="success"
          size="mini"
          v-loading="loading"
          @click="handleDraft(process.id, process.formKey, form, process.key)"
          >存为草稿并关闭
        </el-button>
      </el-row>
    </avue-skeleton>

    <!-- 人员选择弹窗 -->
    <wf-user-select
      ref="user-select"
      :check-type="checkType"
      :default-checked="defaultChecked"
      @onConfirm="handleUserSelectConfirm"
    ></wf-user-select>
    <!-- 草稿弹窗 -->
    <draft-popup
      :visible.sync="isDraftPopupVisible"
      :draftList="draftList"
      @select="handleDraftSelect"
      @delete="handleDraftDelete"
    ></draft-popup>
  </basic-container>
</template>

<script>
import WfExamineForm from "@/views/plugin/workflow/process/components/examForm.vue";
import WfUserSelect from "@/views/plugin/workflow/process/components/user-select";
import MaterialSelect from "@/views/ni/base/components/MaterialSelect";
import ProjectSelect from "@/views/ni/project/components/MyProjectSelect";

import exForm from "@/views/plugin/workflow/mixins/ex-form";
import draft from "@/views/plugin/workflow/mixins/draft";
import { genSn } from "@/api/ni/base/serialno";
import DraftPopup from "@/views/plugin/workflow/process/components/draftPopup.vue";
import debounce from "@/util/debounce";

export default {
  components: {
    WfUserSelect,
    WfExamineForm,
    MaterialSelect,
    ProjectSelect,
    DraftPopup
  },
  mixins: [exForm, draft],
  watch: {
    "$route.query.p": {
      handler(val) {
        if (val) {
          const param = JSON.parse(Buffer.from(val, "base64").toString());
          const { processId, processDefKey, form } = param;
          if (processId) {
            this.getForm(processId);
          } else if (processDefKey) {
            this.getFormByProcessDefKey(processDefKey);
          }
          this.dictInit();
          genSn("por").then((res) => {
            this.form.serialNo = res.data.data;
          });
          if (form) {
            this.form = { ...form };
          }
        }
      },
      immediate: true,
    },
  },
  computed: {
    showExamForm() {
      const { hideComment, hideCopy, hideExamine } = this.process;
      return !hideComment || !hideCopy || !hideExamine;
    },
  },
  data() {
    return {
      defaults: {},
      form: {},
      option: {
        labelWidth: 120,
        calcHeight: 30,
        size: "mini",
        span: 12,
        menuBtn: false,
        column: [
          {
            type: "input",
            label: "申请人",
            display: true,
            prop: "creator",
            value: "${this.$store.getters.userInfo.nick_name}",
            readonly: true,
          },
          {
            type: "input",
            label: "申请部门",
            display: true,
            row: true,
            prop: "createDept",
            value: "${this.$store.getters.userInfo.dept_name}",
            readonly: true,
          },
          {
            label: "借款事由",
            prop: "reason",
            type: "textarea",
            minRows: 1,
            span: 24,
            search: true,
            rules: [
              {
                required: true,
                message: "请输入借款事由",
                trigger: "blur",
              },
            ],
          },
          {
            label: "借款类型",
            prop: "type",
            type: "select",
            placeholder: " ",
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_fin_loan_type",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            search: true,
            rules: [
              {
                required: true,
                message: "请输入类型",
                trigger: "blur",
              },
            ],
          },
          {
            label: "币种",
            prop: "currency",
            type: "select",
            search: true,
            dictData: [],
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            value: "RMB",
            placeholder: " ",
            rules: [
              {
                required: true,
                message: "请选择币种",
                trigger: "blur",
              },
            ],
            control: (val) => {
              if (val === "RMB") {
                return {
                  excRate: {
                    display: false,
                    value: 1,
                  },
                };
              } else {
                return {
                  excRate: {
                    display: true,
                  },
                };
              }
            },
          },
          {
            label: "汇率",
            prop: "excRate",
            labelTip:
              "汇率=本位币/原币.如本位币为人民币，原币为美元: 汇率为:0.1439.",
            type: "number",
            placeholder: " ",
            hide: true,
            display: false,
            rules: [
              {
                required: true,
                message: "请输入汇率",
                trigger: "blur",
              },
            ],
          },
          {
            label: "借款金额",
            prop: "amount",
            type: "number",
            precision: 2,
            rules: [
              {
                required: true,
                message: "请输入借款金额",
                trigger: "blur",
              },
            ],
          },
          {
            label: "备注",
            prop: "remark",
            type: "textarea",
            span: 24,
          },
          {
            label: "附件",
            type: "upload",
            propsHttp: {
              res: "data",
              url: "link",
              name: "originalName",
            },
            action: "/api/blade-resource/oss/endpoint/put-file",
            display: true,
            span: 24,
            showFileList: true,
            multiple: true,
            limit: 10,
            prop: "attachment",
          },
        ],
      },
      process: {},
      loading: false,
      payment: 0,
      isDraftPopupVisible: false,
      draftList: [],
      draftCount: 0,
      draftId: null,
      isConfirmShow: false
    };
  },
  methods: {
    dictInit() {
      this.$http
        .get("/api/blade-system/dict/dictionary?code=currency")
        .then((res) => {
          const column = this.findObject(this.option.column, "currency");
          column.dicData = res.data.data;
        });
    },
    getForm(processId) {
      this.getStartForm(processId).then((res) => {
        const _this = this;
        let { process } = res;
        this.form.processId = process.id;
        const option = _this.option;
        const { column, group } = option;
        column.forEach((col) => {
          if (col.value) col.value = _this.getDefaultValues(col.value);
        });
        if (group && group.length > 0) {
          // 处理group
          group.forEach((gro) => {
            gro.column.forEach((col) => {
              if (col.value) col.value = _this.getDefaultValues(col.value);
            });
          });
        }
        if (_this.permission.wf_process_draft && !this.isConfirmShow) {
          this.isConfirmShow = true;
          // 查询草稿箱
          this.initDraft(process.id,process.key).then((data) => {
          this.draftCount = data.length;
          this.draftList = data;
            if (data && Array.isArray(data) && data.length > 0 ) {
              _this.$confirm("是否引用之前保存的草稿？", "提示", {})
              .then(() => {
                this.isDraftPopupVisible = true; // 打开草稿弹窗
              });
            }
          });
        }
        _this.waiting = false;
      });
    },
    getFormByProcessDefKey(processDefKey) {
      this.getStartFormByProcessDefKey(processDefKey).then((res) => {
        const _this = this;
        let { process } = res;
        _this.form.processId = process.id;
        const option = _this.option;
        const { column, group } = option;
        column.forEach((col) => {
          if (col.value) col.value = _this.getDefaultValues(col.value);
        });
        if (group && group.length > 0) {
          // 处理group
          group.forEach((gro) => {
            gro.column.forEach((col) => {
              if (col.value) col.value = _this.getDefaultValues(col.value);
            });
          });
        }
        if (_this.permission.wf_process_draft && !this.isConfirmShow) {
          this.isConfirmShow = true;
          // 查询草稿箱
          this.initDraft(process.id,process.key).then((data) => {
          this.draftCount = data.length;
          this.draftList = data;
            if (data && Array.isArray(data) && data.length > 0 ) {
              _this.$confirm("是否引用之前保存的草稿？", "提示", {})
              .then(() => {
                this.isDraftPopupVisible = true; // 打开草稿弹窗
              });
            }
          });
        }
        _this.waiting = false;
      });
    },
    handleSubmit:debounce(function () {
      //保存再提交
      if (this.form.inquiry) {
        this.form.inquiryState = 1;
      }
      this.form.porState = 1;
      this.form.draftId = this.draftId;
      this.handleStartProcess(true)
        .then((done) => {
          this.$message.success("发起成功");
          if(this.draftId != null){
              this.draftCount = this.draftCount-1;
              this.draftList = this.draftList.filter(item => item.id !== this.draftId);
            }
          this.handleCloseTag("/ni/fin/loan");
          done();
        })
        .catch(() => {
          this.loading = false;
        });
    },1000),

        //选择草稿
        handleDraftSelect(selectedDraft) {
      //草稿版本与流程版本不一致
      if(!selectedDraft.sameVersion){
        this.$confirm("选中的草稿与当前流程版本不一致，是否继续引用？", "提示", {})
        .then(() => {
          this.draftId = selectedDraft.id;
          this.form = JSON.parse(selectedDraft.variables);
          this.isDraftPopupVisible = false;
        });
      } else {
        this.draftId = selectedDraft.id;
        this.form = JSON.parse(selectedDraft.variables);
      }
    },
    //删除草稿
    handleDraftDelete(draftId) {
      this.$confirm("是否删除选中的草稿箱数据？", "提示", {})
        .then(() => {
          this.$axios.post(`/api/blade-workflow/process/draft/delete/${draftId}`)
          .then(response => {
            this.$message.success('草稿删除成功');
            this.draftCount = this.draftCount-1;
            this.draftList = this.draftList.filter(item => item.id !== draftId);
          })
          .catch(error => {
            this.$message.error('草稿删除失败，请重试');
          });
      })
    },
    handleDraftBox() {
      if (this.draftList.length > 0) {
        this.isDraftPopupVisible = true;
      } else {
        // 重新获取草稿数据
        this.initDraft(this.form.processId).then((data) => {
          this.draftCount = data.length;
          this.draftList = data;
          if (data && Array.isArray(data) && data.length > 0) {
            this.isDraftPopupVisible = true;
          }
        });
      }
    },

  },
};
</script>

<style lang="scss" scoped>
.foot-item {
  position: fixed;
  bottom: 0;
  margin-left: -20px;
  // right: 0;
  z-index: 101;
  height: 66px;
  background-color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  -webkit-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
</style>

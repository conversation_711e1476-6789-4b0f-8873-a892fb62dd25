<template>
  <div>
    <el-input
      v-model="serialNo"
      :size="size"
      suffix-icon="el-icon-search"
      :placeholder="placeholder || ' '"
      readonly
      :disabled="disabled"
      @click.native="handleSelect"
    ></el-input>
    <!-- 人员选择弹窗 -->
    <fin-adjust-select-dialog
      ref="adjust-select"
      :multiple="multiple"
      :default-checked="value"
      :params="params"
      @onConfirm="handleAdjustSelectConfirm"
    ></fin-adjust-select-dialog>
  </div>
</template>
<script>
import { getDetail } from "@/api/ni/fin/adjust";

import FinAdjustSelectDialog from "./FinAdjustSelectDialog";
import Emitter from "element-ui/src/mixins/emitter";

export default {
  name: "fin-adjust-select",
  mixins: [Emitter],
  components: { FinAdjustSelectDialog },
  props: {
    value: [String, Number],
    multiple: {
      // radio单选 checkbox多选
      type: Boolean,
      default: false,
    },
    size: {
      type: String,
      default: () => {
        return "mini";
      },
    },
    readonly: {
      type: Boolean,
      default: false,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    placeholder: String,
    params: {
      type: Object,
      default: () => {},
    },
    beforeSelect: {
      type: Function,
    },
  },
  watch: {
    value: {
      handler(val) {
        if (val) {
          const serialNo = [];
          const checks = (val + "").split(",");
          const asyncList = [];
          checks.forEach((c) => {
            asyncList.push(getDetail(c));
          });
          Promise.all(asyncList).then((res) => {
            res.forEach((r) => {
              const data = r.data.data;
              if (data) serialNo.push(data.serialNo);
            });
            this.$set(this, "serialNo", serialNo.join(","));
          });
        } else this.$set(this, "serialNo", "");
      },
      immediate: true,
    },
  },
  data() {
    return {
      serialNo: "",
    };
  },
  methods: {
    handleSelect() {
      if (this.readonly || this.disabled) return;
      else {
        if (this.beforeSelect) {
          this.beforeSelect(() => {
            this.$refs["adjust-select"].visible = true;
          });
        } else {
          this.$refs["adjust-select"].visible = true;
        }
      }
    },
    handleAdjustSelectConfirm(selection, ids) {
      this.$emit("input", ids);
      this.$emit("change", selection);
      this.$nextTick(() => {
        this.dispatch("ElFormItem", "el.form.blur", [ids]);
      });
    },
  },
};
</script>
<style lang="scss"></style>

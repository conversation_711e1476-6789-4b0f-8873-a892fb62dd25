<template>
  <basic-container>
    <avue-skeleton :loading="waiting" avatar :rows="8">
      <avue-affix id="avue-view" :offset-top="114">
        <div class="header">
          <avue-title :value="process.processDefinitionName"></avue-title>
          <div v-if="process.status != 'todo'">
            主题：
            <avue-select
              v-model="theme"
              size="mini"
              :clearable="false"
              :dic="themeList"
            ></avue-select>
          </div>
        </div>
      </avue-affix>
      <el-tabs v-model="activeName">
        <el-tab-pane label="申请信息" name="first">
          <el-card shadow="never">
            <div
              id="printBody"
              :class="process.status !== 'todo' ? `wf-theme-${theme}` : ''"
            >
              <avue-form
                v-if="
                  option &&
                  ((option.column && option.column.length > 0) ||
                    (option.group && option.group.length > 0))
                "
                v-model="form"
                ref="form"
                :defaults.sync="defaults"
                :option="option"
                :upload-preview="handleUploadPreview"
              >
                <template #projectId="{ disabled, size, index, row }">
                  <project-select
                    v-model="form.projectId"
                    :size="size"
                    :params="{ status: 9 }"
                    :disabled="disabled"
                    @confirm="projectConfirm"
                    @clear="projectClear"
                  />
                </template>
                <template #budgetId="{ row, disabled, size, index }">
                  <un-finish-budget-select
                    v-model="form.budgetId"
                    :size="size"
                    :disabled="disabled"
                    @confirm="handleBudgetConfirm($event, form)"
                    @clear="handleBudgetClear"
                  />
                </template>
                <template #amount="{ disabled, size }">
                  <span
                    style="color: #f56c6c; font-weight: bolder; font-size: 16px"
                  >
                    {{ form.amount }}
                  </span>
                </template>
                <template #itemsLabel>
                  <span style="font-size: 16px; font-weight: 500">
                    采购明细
                  </span>
                  <template v-if="!option.detail">
                    <el-divider direction="vertical"></el-divider>
                    <el-button-group>
                      <el-button
                        type="primary"
                        size="mini"
                        icon="el-icon-plus"
                        plain
                        :disabled="!form.projectId && !form.budgetId"
                        @click="itemAdd"
                        >添加
                      </el-button>
                      <el-button
                        type="danger"
                        size="mini"
                        icon="el-icon-delete"
                        plain
                        :disabled="item.selectionList.length <= 0"
                        @click="itemDelete"
                        >删 除
                      </el-button>
                      <el-button
                        type="warning"
                        size="mini"
                        icon="el-icon-c-scale-to-original"
                        plain
                        :disabled="item.selectionList.length <= 0"
                        @click="handleItemInquiry"
                        >批量比价
                      </el-button>
                    </el-button-group>
                    <el-button
                      style="margin-left: 5px"
                      type="info"
                      icon="el-icon-user"
                      size="mini"
                      @click="handleDesignation"
                      :disabled="item.selectionList.length <= 0"
                    >指定验收人
                    </el-button>
                  </template>
                </template>
                <template #items="{ row, disabled }">
                  <div style="overflow: hidden;margin-top: 5px">
                    <avue-crud
                      style="margin-left: 1px"
                      :option="item.option"
                      :data="form.items"
                      @selection-change="itemSelectionChange"
                      ref="itemCrud"
                      @cell-click="itemCellClickChange"
                    >
                      <template #menu="{ row, index }">
                        <a
                          href="#"
                          v-if="!row.reject"
                          style="
                            text-decoration: underline;
                            font-weight: bold;
                            color: #f56c6c;
                          "
                          @click.p.prevent="rowReject(row, index)"
                        >
                          驳回
                        </a>
                        <a
                          href="#"
                          v-else-if="row.rejectUserId === userInfo.user_id"
                          style="text-decoration: underline; font-weight: bold"
                          @click.p.prevent="rowRejectBack(row, index)"
                        >
                          撤销
                        </a>
                      </template>
                      <template #reject="{ row, index }">
                        <el-tooltip
                          v-if="row.reject"
                          class="item"
                          effect="dark"
                          :content="`驳回原因：${row.rejectReason}`"
                          placement="top-start"
                        >
                          <el-tag size="mini" type="danger" effect="dark"
                            >是
                          </el-tag>
                        </el-tooltip>
                        <el-tag v-else size="mini" type="info" effect="plain"
                          >否
                        </el-tag>
                      </template>
                      <template #expand="{ row }">
                        <el-form label-position="left" inline size="mini">
                          <el-row>
                            <el-col :span="12">
                              采购原因:
                              <span style="font-weight: bolder">{{
                                row.reason
                              }}</span>
                            </el-col>
                            <el-col :span="12">
                              采购风险:
                              <span style="font-weight: bolder">{{
                                row.risk
                              }}</span>
                            </el-col>
                            <el-col :span="24">
                              采购带来的收益及效果:
                              <span style="font-weight: bolder">{{
                                row.profit
                              }}</span>
                            </el-col>
                            <el-col :span="24">
                              项目安全性分析及风险控制:
                              <span style="font-weight: bolder">{{
                                row.security
                              }}</span>
                            </el-col>
                            <el-col :span="24">
                              项目环保因素分析及风险控制:
                              <span style="font-weight: bolder">{{
                                row.ep
                              }}</span>
                            </el-col>
                            <el-col :span="24">
                              供应商比价:
                              <el-table
                                :data="row.inquiries"
                                size="mini"
                                border
                                style="width: 700px"
                              >
                                <el-table-column
                                  prop="supplier"
                                  label="供应商名称"
                                  show-overflow-tooltip
                                >
                                </el-table-column>
                                <el-table-column
                                  prop="supplierLinkman"
                                  label="联系方式"
                                  show-overflow-tooltip
                                >
                                </el-table-column>
                                <el-table-column
                                  prop="remark"
                                  label="比价详情"
                                  show-overflow-tooltip
                                >
                                </el-table-column>
                                <el-table-column
                                  prop="recommend"
                                  label="建议供应商"
                                  show-overflow-tooltip
                                >
                                  <template slot-scope="scope">
                                    <el-tag
                                      size="mini"
                                      v-if="scope.row.recommend"
                                      type="danger"
                                      effect="dark"
                                      >是
                                    </el-tag>
                                  </template>
                                </el-table-column>
                              </el-table>
                            </el-col>
                          </el-row>
                        </el-form>
                      </template>
                      <template #inquiries="{ row, size, index }">
                        <a
                          v-if="row.inquiries && row.inquiries.length > 0"
                          href="#"
                          style="text-decoration: underline; font-weight: bold"
                          @click.p.prevent="rowInquiry(row, index)"
                        >
                          比价
                          <span
                            v-if="row.inquiries && row.inquiries.length > 0"
                          >
                            [{{ row.inquiries.length }}]
                          </span>
                        </a>
                        <a
                          v-else
                          href="#"
                          style="text-decoration: underline; font-weight: bold"
                          @click.p.prevent="rowInquiry(row, index)"
                        >
                          未比价
                        </a>
                        <el-tag
                          v-if="
                            !row.reject &&
                            row.inquiries &&
                            row.inquiries.length > 0 &&
                            !row.inquiryAgree &&
                            option.detail &&
                            process.status === 'todo' &&
                            buttonList.find((b) => b.buttonKey === 'wf_pass')
                          "
                          size="mini"
                          type="danger"
                          effect="dark"
                          >未审核
                        </el-tag>
                      </template>
                      <template #cost="{ row, index }">
                        <i
                          :class="
                            row.inquiry && !row.inquiryExpand
                              ? 'el-icon-arrow-right'
                              : 'el-icon-arrow-down'
                          "
                          v-if="row.inquiry"
                          @click="itemRowExpand(row)"
                        ></i>
                        <el-tag
                          size="mini"
                          type="danger"
                          effect="dark"
                          v-if="row.cost"
                        >
                          费用
                        </el-tag>
                        <el-tag size="mini" type="info" effect="plain" v-else>
                          实物
                        </el-tag>
                      </template>
                      <template #pv="{ row, index }">
                        <el-tag
                          size="mini"
                          type="danger"
                          effect="dark"
                          v-if="row.pv"
                        >
                          是
                        </el-tag>
                        <el-tag size="mini" type="info" effect="plain" v-else>
                          否
                        </el-tag>
                      </template>
                      <template #fa="{ row, index }">
                        <el-tag
                          size="mini"
                          type="danger"
                          effect="dark"
                          v-if="row.fa"
                        >
                          是
                        </el-tag>
                        <el-tag size="mini" type="info" effect="plain" v-else>
                          否
                        </el-tag>
                      </template>
                      <template #materialCode="{ row, size, disabled, index }">
                        <span v-if="row.materialCode">
                          [{{ row.materialCode }}]{{ row.materialName }}
                        </span>
                      </template>
                      <template
                        #materialCodeForm="{ row, size, disabled, index }"
                      >
                        <span v-if="!row.cost && row.materialId">
                          [{{ row.materialCode }}]{{ row.materialName }}
                        </span>
                        <material-select
                          v-else
                          v-model="row.materialId"
                          :size="size"
                          :disabled="disabled"
                          @submit="handleMaterialSubmit($event, row)"
                        />
                      </template>
                      <template #numForm="{ row, disabled, size }">
                        <el-input-number
                          :size="size"
                          v-model="row.num"
                          :disabled="disabled"
                          :min="0"
                          :controls="false"
                          style="width: 100%"
                          @change="handleNumChange($event, row)"
                        />
                      </template>
                      <template #amountForm="{ row, disabled, size }">
                        <el-input-number
                          :size="size"
                          v-model="row.amount"
                          :disabled="disabled"
                          :min="0"
                          :controls="false"
                          style="width: 100%"
                          @change="handleAmountChange($event, row)"
                        />
                      </template>
                      <template #attach="{ row, index }">
                        <span
                          v-if="row.attach"
                          style="text-decoration: underline; cursor: pointer"
                          @click="rowAttach(row, index)"
                          ><i class="el-icon-circle-plus-outline" /> 附件({{
                            row.attach ? row.attach.length : 0
                          }})</span
                        >
                        <!--                        <el-link v-if="row.attach" type="primary" target="_blank" @click="rowAttach(row, index)">-->
                        <!--                          <i class="el-icon-circle-plus-outline" />-->
                        <!--                          附件({{ row.attach ? row.attach.length : 0 }})-->
                        <!--                        </el-link>-->
                      </template>
                      <template #remark="{ row, index }">
                        <span v-if="row.remark != null">
                          {{ htmlDecode(row.remark) }}
                        </span>
                      </template>
                    </avue-crud>
                  </div>
                </template>
              </avue-form>
            </div>
          </el-card>
          <el-card
            shadow="never"
            style="margin-top: 20px"
            v-if="process.status == 'todo'"
          >
            <wf-examine-form
              ref="examineForm"
              :comment.sync="comment"
              :process="process"
              @user-select="handleUserSelect"
            ></wf-examine-form>
          </el-card>
        </el-tab-pane>
        <el-tab-pane label="流转信息" name="second">
          <el-card shadow="never" style="margin-top: 5px">
            <wf-flow :flow="flow"></wf-flow>
          </el-card>
        </el-tab-pane>
        <el-tab-pane label="流程跟踪" name="third">
          <template v-if="activeName == 'third'">
            <el-card shadow="never" style="margin-top: 5px">
              <wf-design
                ref="bpmn"
                style="height: 500px"
                :options="bpmnOption"
              ></wf-design>
            </el-card>
          </template>
        </el-tab-pane>
      </el-tabs>
    </avue-skeleton>

    <!-- 底部按钮 -->
    <wf-button
      :loading="submitLoading"
      :button-list="buttonList"
      :process="process"
      :comment="comment"
      @examine="handleExamine"
      @user-select="handleUserSelect"
      @print="handlePrint"
      @rollback="handleRollbackTask"
      @terminate="handleTerminateProcess"
      @withdraw="handleWithdrawTask1"
    ></wf-button>
    <!-- 人员选择弹窗 -->
    <user-select
      ref="user-select"
      :check-type="checkType"
      :default-checked="defaultChecked"
      @onConfirm="handleUserSelectConfirm"
    ></user-select>
    <budget-item-dialog
      ref="porBudgetItemRef"
      multiple
      :params="{ used: false }"
      @confirm="handleItemSelect"
    />
    <material-select-dialog
      ref="materialSelectDialogRef"
      multiple
      @submit="handleItemAddSubmit"
    />
    <el-dialog
      title="比价"
      append-to-body
      :visible.sync="item.inquiryVisible"
      :close-on-click-modal="false"
      width="1000px"
    >
      <div slot="title" class="clearfix">
        <span>比价</span>
        <el-divider
          direction="vertical"
          v-if="!item.inquiryOpt.detail"
        ></el-divider>
        <el-button
          v-if="!item.inquiryOpt.detail"
          type="primary"
          size="mini"
          @click="handleInquiryHistory"
        >
          历史比价
        </el-button>
      </div>
      <avue-form
        v-if="item.inquiryVisible"
        :option="item.inquiryOpt"
        v-model="item.inquiryForm"
        @submit="handleInquirySubmit"
      >
      </avue-form>
      <avue-form
        v-if="
          item.inquiryVisible &&
          item.inquiryOpt.detail &&
          process.status === 'todo'
        "
        :option="item.inquiryApprovalOpt"
        v-model="item.inquiryApprovalForm"
        @submit="handleInquiryAgree"
      >
      </avue-form>
    </el-dialog>
    <apply-history-inquiry-dialog
      ref="historyInquiryRef"
      :material-id="itemSelectMaterialIds"
      :show.sync="item.inquiryHistoryShow"
      @submit="handleHistoryInquirySubmit"
    />
    <attach-dialog
      ref="attachRef"
      code="public"
      :delBtn="false"
      @close="handleRefreshAttach"
    />
    <el-dialog
      title="驳回"
      append-to-body
      :visible.sync="item.rejectVisible"
      width="400px"
    >
      <div slot="title" class="clearfix">
        <span>{{ item.rejectTitle }}</span>
      </div>
      <avue-form
        ref="rejectRef"
        v-if="item.rejectVisible"
        :option="item.rejectOpt"
        v-model="item.rejectForm"
        @submit="handleRejectSubmit"
      >
        <template #menuForm="{ size }">
          <el-button type="warning" :size="size" @click="handleRejectAndStop"
            >驳回并终止
          </el-button>
        </template>
      </avue-form>
    </el-dialog>
    <el-dialog
      title="指定验收人"
      append-to-body
      :visible.sync="designation.visible"
      width="400px"
      center
    >
      <avue-form
        :option="designation.option"
        v-model="designation.form"
        @submit="handleDesignationSubmit"
      ></avue-form>
    </el-dialog>
  </basic-container>
</template>

<script>
import WfExamineForm from "@/views/plugin/workflow/process/components/examForm.vue";
import WfButton from "@/views/plugin/workflow/process/components/button.vue";
import WfFlow from "@/views/plugin/workflow/process/components/flow.vue";
import userSelect from "@/views/plugin/workflow/process/components/user-select";

import exForm from "@/views/plugin/workflow/mixins/ex-form";
import theme from "@/views/plugin/workflow/mixins/theme";
import MaterialSelect from "@/views/ni/base/components/MaterialSelect";
import ProjectSelect from "@/views/ni/project/components/MyProjectSelect";
import BudgetItemDialog from "@/views/ni/por/components/BudgetItemDialog";
import MaterialSelectDialog from "@/views/ni/base/components/MaterialSelectDialog";
import UnFinishBudgetSelect from "@/views/ni/por/components/UnFinishBudgetSelect";
import ApplyHistoryInquiryDialog from "@/views/ni/por/components/ApplyHistoryInquiryDialog";
import { getList as getAttachList } from "@/api/resource/attach";
import AttachDialog from "@/components/attach-dialog";
import { getDetail as getMaterialDetail } from "@/api/ni/base/material/materialinfo";
import { dateNow1 } from "@/util/date";
import { mapGetters } from "vuex";
import { detail } from "@/api/system/param";
import {getUser} from "@/api/system/user";

export default {
  mixins: [exForm, theme],
  components: {
    AttachDialog,
    userSelect,
    WfExamineForm,
    WfButton,
    WfFlow,
    MaterialSelect,
    ProjectSelect,
    BudgetItemDialog,
    MaterialSelectDialog,
    UnFinishBudgetSelect,
    ApplyHistoryInquiryDialog,
  },
  watch: {
    "$route.query.p": {
      immediate: true,
      handler(val) {
        if (val) {
          this.submitLoading = true;
          Object.keys(this.form).forEach((key) => (this.form[key] = ""));
          const param = JSON.parse(Buffer.from(val, "base64").toString());
          const { taskId, processInsId } = param;
          detail({ paramKey: "ni.por.apply.inquiries.amount" }).then((res) => {
            this.inquiriesAmount = Number(res.data.data.paramValue);
            this.form.tip = `采购金额大于 ${this.inquiriesAmount}元，需比价!`;
          });
          detail({ paramKey: "por.pv.apply.user" }).then((res) => {
            const applyUser = res.data.data.paramValue;
            const pv = this.findObject(this.option.column, "pv");
            const pvItem = this.findObject(this.item.option.column, "pv");
            if (applyUser.includes(this.userInfo.user_name)) {
              pv.disabled = false;
              pvItem.cell = true;
              pvItem.disabled = false;
            } else {
              pv.disabled = true;
              pvItem.cell = false;
              pvItem.disabled = true;
            }
          });
          if (processInsId) {
            this.getDetail(taskId, processInsId);
          }
        }
      },
    },
  },
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      vm.fromPath = from.path; //获取上一级路由的路径
    });
  },
  computed: {
    ...mapGetters(["userInfo"]),
    showExamForm() {
      const { hideComment, hideCopy, hideExamine } = this.process;
      return !hideComment || !hideCopy || !hideExamine;
    },
    itemIds() {
      let items = new Set();
      this.form.items.forEach((ele) => {
        items.add(ele.budgetItemId);
      });
      return Array.from(items).join(",");
    },
    itemSelectMaterialIds() {
      let items = new Set();
      this.item.selectionList.forEach((ele) => {
        items.add(ele.materialId);
      });
      return Array.from(items).join(",");
    },
  },
  data() {
    return {
      fromPath: "",
      activeName: "first",
      defaults: {},
      form: {
        items: [],
      },
      option: {
        detail: true,
        labelWidth: 120,
        calcHeight: 30,
        size: "mini",
        span: 8,
        menuBtn: false,
        column: [
          {
            label: "申请人",
            display: true,
            span: 8,
            prop: "createUserName",
            readonly: true,
          },
          {
            label: "申请部门",
            display: true,
            row: true,
            span: 8,
            prop: "createDeptName",
            readonly: true,
          },
          {
            label: "采购编号",
            prop: "serialNo",
            overHidden: true,
            minWidth: 135,
            span: 8,
            search: true,
            disabled: true,
          },
          {
            label: "采购主题",
            prop: "title",
            overHidden: true,
            minWidth: 135,
            span: 8,
            rules: [
              {
                required: true,
                message: "请输入",
                trigger: "blur",
              },
            ],
          },
          {
            label: "采购类型",
            prop: "type",
            type: "select",
            span: 8,
            dicUrl: "/api/blade-system/dict-biz/dictionary?code=ni_por_type",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            disabled: true,
            rules: [
              {
                required: true,
                message: "请输入",
                trigger: "blur",
              },
            ],
          },
          {
            label: "关联项目",
            prop: "projectId",
            placeholder: " ",
            filterable: true,
            span: 8,
            display: false,
            rules: [
              {
                required: true,
                message: "请输入",
                trigger: "blur",
              },
            ],
          },
          {
            label: "关联项目",
            prop: "projectTitle",
            span: 8,
            placeholder: " ",
            disabled: true,
          },
          {
            label: "账套",
            prop: "brand",
            type: "radio",
            minWidth: 90,
            span: 8,
            value: "1",
            dicUrl: "/api/blade-system/dict-biz/dictionary?code=ni_brand",
            disabled: true,
            props: {
              label: "dictValue",
              value: "dictKey",
            },
          },
          {
            label: "年度预算",
            prop: "year",
            type: "radio",
            value: false,
            dicData: [
              {
                label: "是",
                value: true,
              },
              {
                label: "否",
                value: false,
              },
            ],
            display: false,
            placeholder: " ",
            rules: [
              {
                required: true,
                message: "请选择是否年度预算",
                trigger: "blur",
              },
            ],
            change: ({ value }) => {
              const budgetIdColumn = this.findObject(
                this.option.column,
                "budgetId"
              );
              const subTypeColumn = this.findObject(
                this.option.column,
                "subType"
              );
              const projectIdColumn = this.findObject(
                this.option.column,
                "projectId"
              );
              const projectTitleColumn = this.findObject(
                this.option.column,
                "projectTitle"
              );
              if (value) {
                budgetIdColumn.rules = [
                  {
                    required: true,
                    message: "请输入",
                    trigger: "blur",
                  },
                ];
                subTypeColumn.display = false;
                projectIdColumn.display = false;
                projectTitleColumn.display = true;
              } else {
                budgetIdColumn.rules = [
                  {
                    required: false,
                    message: "请输入",
                    trigger: "blur",
                  },
                ];
                subTypeColumn.display = true;
                projectIdColumn.display = true;
                projectTitleColumn.display = false;
              }
            },
          },
          {
            label: "关联预算",
            labelTip: "只关联已审核的预算",
            prop: "budgetId",
            placeholder: " ",
            dicData: [],
            type: "tree",
            props: {
              label: "title",
              value: "id",
            },
            filterable: true,
            rules: [
              {
                required: true,
                message: "请输入",
                trigger: "blur",
              },
            ],
          },
          {
            label: "预算类型",
            prop: "subType",
            placeholder: " ",
            type: "select",
            dicUrl: "/api/ni/por/type/listWithPermission",
            display: false,
            props: {
              label: "name",
              value: "code",
              desc: "code",
            },
            rules: [
              {
                required: true,
                message: "请选择预算类型",
                trigger: "blur",
              },
            ],
          },
          {
            label: "紧急申购",
            prop: "crash",
            type: "radio",
            placeholder: " ",
            value: false,
            dicData: [
              {
                label: "是",
                value: true,
              },
              {
                label: "否",
                value: false,
              },
            ],
            change: ({ value }) => {
              const yearColumn = this.findObject(this.option.column, "year");
              const budgetIdColumn = this.findObject(
                this.option.column,
                "budgetId"
              );
              const subTypeColumn = this.findObject(
                this.option.column,
                "subType"
              );
              const projectIdColumn = this.findObject(
                this.option.column,
                "projectId"
              );
              const projectTitleColumn = this.findObject(
                this.option.column,
                "projectTitle"
              );
              if (value) {
                yearColumn.display = true;
                budgetIdColumn.rules = [
                  {
                    required: false,
                    message: "请输入",
                    trigger: "blur",
                  },
                ];
                subTypeColumn.display = true;
                projectIdColumn.display = true;
                projectTitleColumn.display = false;
              } else {
                yearColumn.display = false;
                budgetIdColumn.rules = [
                  {
                    required: true,
                    message: "请输入",
                    trigger: "blur",
                  },
                ];
                subTypeColumn.display = false;
                projectIdColumn.display = false;
                projectTitleColumn.display = true;
              }
            },
          },
          {
            label: "压力容器",
            prop: "pv",
            type: "radio",
            placeholder: " ",
            value: 0,
            disabled: true,
            dicData: [
              {
                label: "是",
                value: 1,
              },
              {
                label: "否",
                value: 0,
              },
            ],
            change: ({ value }) => {
              if (!this.waiting && !this.option.detail)
                this.form.items.forEach((item) => {
                  item.pv = value;
                });
            },
          },
          {
            label: "需用日期",
            prop: "needDate",
            overHidden: true,
            minWidth: 90,
            type: "date",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            rules: [
              {
                required: true,
                message: "请输入",
                trigger: "blur",
              },
            ],
          },
          {
            label: "采购方式",
            labelTip: "由哪个部门进行采购",
            prop: "buyer",
            type: "radio",
            value: "1",
            dicUrl: "/api/blade-system/dict-biz/dictionary?code=ni_por_buyer",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            dataType: "number",
            rules: [
              {
                required: true,
                message: "请选择采购方式",
                trigger: "blur",
              },
            ],
          },
          {
            label: "收货地",
            prop: "receivingAddress",
            type: "radio",
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_por_receiving_address",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
          },
          {
            label: "采购金额",
            prop: "amount",
            type: "number",
            disabled: true,
            placeholder: " ",
          },
          {
            label: "备注",
            overHidden: true,
            prop: "remark",
            type: "textarea",
            display: false,
            span: 24,
            minRows: 3,
          },
          {
            label: "附件",
            type: "upload",
            propsHttp: {
              res: "data",
              url: "attachId",
              name: "originalName",
            },
            action: "/api/blade-resource/oss/endpoint/put-file",
            display: false,
            span: 24,
            showFileList: true,
            multiple: true,
            limit: 10,
            prop: "attach",
          },
          {
            label: "采购明细",
            prop: "items",
            labelPosition: "top",
            span: 24,
          },
        ],
      },
      item: {
        selectionList: [],
        option: {
          header: false,
          rowKey: "budgetItemId",
          expand: true,
          expandWidth: 1,
          expandRowKeys: [],
          cellBtn: true,
          addBtn: false,
          refreshBtn: false,
          columnBtn: false,
          menu: true,
          menuWidth: 70,
          saveBtn: false,
          updateBtn: false,
          cancelBtn: false,
          editBtn: false,
          delBtn: false,
          dialogFullscreen: true,
          size: "mini",
          align: "center",
          calcHeight: 30,
          tip: false,
          border: true,
          viewBtn: false,
          dialogClickModal: false,
          selection: true,
          showSummary: true,
          sumColumnList: [
            {
              name: "num",
              type: "sum",
              decimals: 1,
            },
            {
              name: "amount",
              type: "sum",
            },
          ],
          column: [
            {
              label: "序号",
              prop: "row",
              width: 55,
            },
            {
              label: "类型",
              prop: "cost",
              placeholder: " ",
              width: 80,
              disabled: true,
            },
            {
              label: "品名",
              prop: "materialName",
              type: "textarea",
              minRows: 1,
              placeholder: " ",
              overHidden: true,
              minWidth: 110,
              cell: true,
              rules: [
                {
                  required: true,
                  message: "请输入品名",
                  trigger: "blur",
                },
              ],
            },
            {
              label: "用途",
              prop: "purpose",
              type: "textarea",
              minWidth: 110,
              minRows: 1,
              placeholder: " ",
              overHidden: true,
              cell: true,
            },
            {
              label: "编码",
              minWidth: 100,
              placeholder: " ",
              prop: "materialCode",
              overHidden: true,
              clearable: false,
              rules: [
                {
                  required: false,
                  message: "请选择编码",
                  trigger: "blur",
                },
              ],
            },
            {
              label: "规格",
              prop: "specification",
              placeholder: " ",
              overHidden: true,
              disabled: true,
              minWidth: 110,
            },
            {
              label: "材质",
              prop: "quality",
              placeholder: " ",
              disabled: true,
              overHidden: true,
              minWidth: 110,
            },

            {
              label: "单位",
              prop: "unit",
              type: "select",
              dicUrl:
                "/api/blade-system/dict-biz/dictionary?code=ni_material_unit",
              props: {
                label: "dictValue",
                value: "dictKey",
              },
              minWidth: 80,
              placeholder: " ",
              slot: true,
              rules: [
                {
                  required: true,
                  message: "请选择单位",
                  trigger: "blur",
                },
              ],
            },
            {
              label: "数量",
              prop: "num",
              type: "number",
              precision: 0,
              placeholder: " ",
              minWidth: 100,
              cell: true,
              rules: [
                {
                  required: true,
                  message: "请输入数量",
                  trigger: "blur",
                },
              ],
            },
            {
              label: "单价",
              prop: "price",
              type: "number",
              controls: false,
              disabled: true,
              precision: 2,
              minWidth: 100,
              cell: true,
              placeholder: " ",
              change: ({ value, row }) => {
                if (row.num) {
                  row.amount = (Number(value) * Number(row.num)).toFixed(2);
                } else {
                  row.amount = 0;
                }
                this.sumAmount();
              },
            },
            {
              label: "金额",
              prop: "amount",
              overHidden: true,
              type: "number",
              cell: true,
              minWidth: 100,
              precision: 2,
              placeholder: " ",
              rules: [
                {
                  required: true,
                  message: "请输入金额",
                  trigger: "blur",
                },
              ],
            },
            {
              label: "压力容器",
              prop: "pv",
              cell: true,
              type: "radio",
              width: 90,
              dicData: [
                {
                  label: "是",
                  value: 1,
                },
                {
                  label: "否",
                  value: 0,
                },
              ],
            },
            {
              label: "固定资产",
              prop: "fa",
              type: "radio",
              placeholder: " ",
              value: 0,
              disabled: true,
              dicData: [
                {
                  label: "是",
                  value: 1,
                },
                {
                  label: "否",
                  value: 0,
                },
              ],
            },
            {
              label: "比价",
              prop: "inquiries",
              minWidth: 130,
            },
            {
              label: "驳回",
              prop: "reject",
              width: 65,
            },
            {
              label: "备注",
              prop: "remark",
              type: "textarea",
              minRows: 1,
              minWidth: 130,
              placeholder: " ",
              cell: true,
              overHidden: true,
            },{
              label: "验收人",
              prop: "designationInspectionUserName",
              width: 80,
              overHidden: true,
              cell: false,
            },
            {
              label: "附件",
              type: "upload",
              width: 94,
              propsHttp: {
                res: "data",
                url: "attachId",
                name: "originalName",
              },
              cell: true,
              action:
                "/api/blade-resource/oss/endpoint/put-file-attach?code=public",
              display: true,
              showFileList: true,
              multiple: true,
              limit: 10,
              prop: "attach",
              fixed: "right",
            },
            {
              label: "国标",
              prop: "gb",
              placeholder: " ",
              disabled: true,
              overHidden: true,
              minWidth: 90,
            },
          ],
        },
        inquiryVisible: false,
        inquiryOpt: {
          detail: false,
          size: "mini",
          emptyBtn: false,
          labelWidth: 120,
          column: [
            {
              label: "采购原因",
              prop: "reason",
              type: "textarea",
              minRows: 1,
              placeholder: " ",
              rules: [
                {
                  required: true,
                  message: "请输入采购原因",
                  trigger: "blur",
                },
              ],
            },
            {
              label: "采购风险",
              prop: "risk",
              type: "textarea",
              minRows: 1,
              placeholder: " ",
              rules: [
                {
                  required: true,
                  message: "请输入采购风险",
                  trigger: "blur",
                },
              ],
            },
            {
              label: "采购带来的收益及效果",
              prop: "profit",
              span: 24,
              type: "textarea",
              minRows: 2,
              placeholder: " ",
              rules: [
                {
                  required: true,
                  message: "请输入采购带来的收益及效果",
                  trigger: "blur",
                },
              ],
            },
            {
              label: "项目安全性分析及风险控制",
              prop: "security",
              span: 24,
              minRows: 2,
              type: "textarea",
              placeholder: " ",
              rules: [
                {
                  required: true,
                  message: "请输入项目安全性分析及风险控制",
                  trigger: "blur",
                },
              ],
            },
            {
              label: "项目环保因素分析及风险控制",
              prop: "ep",
              span: 24,
              minRows: 2,
              type: "textarea",
              placeholder: " ",
              rules: [
                {
                  required: true,
                  message: "请输入项目环保因素分析及风险控制",
                  trigger: "blur",
                },
              ],
            },
            {
              label: "验收标准",
              prop: "ac",
              span: 12,
              minRows: 1,
              type: "textarea",
              placeholder: " ",
            },
            {
              label: "技术参数",
              prop: "technology",
              span: 12,
              minRows: 1,
              type: "textarea",
              placeholder: " ",
            },
            {
              label: "三方比价",
              prop: "inquiries",
              span: 24,
              type: "dynamic",
              children: {
                size: "mini",
                span: 8,
                align: "center",
                headerAlign: "center",
                addBtn: false,
                delBtn: false,
                column: [
                  {
                    label: "供应商名称",
                    prop: "supplier",
                    placeholder: " ",
                    type: "textarea",
                    overHidden: true,
                    minRows: 1,
                    rules: [
                      {
                        required: true,
                        message: "请填写供应商名称",
                        trigger: "blur",
                      },
                    ],
                  },
                  {
                    label: "联系方式",
                    prop: "supplierLinkman",
                    placeholder: " ",
                    type: "textarea",
                    overHidden: true,
                    minRows: 1,
                    rules: [
                      {
                        required: true,
                        message: "请填写供应商联系人",
                        trigger: "blur",
                      },
                    ],
                  },

                  {
                    label: "比价详情",
                    prop: "remark",
                    type: "textarea",
                    minRows: 1,
                    placeholder: " ",
                    overHidden: true,
                    rules: [
                      {
                        required: true,
                        message: "请填写比价详情",
                        trigger: "blur",
                      },
                    ],
                  },
                  {
                    label: "建议供应商",
                    prop: "recommend",
                    type: "radio",
                    value: false,
                    dicData: [
                      {
                        label: "是",
                        value: true,
                      },
                      {
                        label: "否",
                        value: false,
                      },
                    ],
                    placeholder: " ",
                    rules: [
                      {
                        required: true,
                        message: "请选择建议供应商",
                        trigger: "blur",
                      },
                    ],
                    change: ({ value, index }) => {
                      if (value && this.item && this.item.inquiries)
                        this.item.inquiries.forEach((item, i) => {
                          if (i === index) {
                            return;
                          }
                          item.recommend = false;
                        });
                    },
                  },
                ],
              },
            },
            {
              label: "部门主管意见",
              prop: "comments1",
              minRows: 1,
              type: "textarea",
              placeholder: " ",
              disabled: true,
              span: 12,
            },
            {
              label: "工艺组意见",
              prop: "comments2",
              minRows: 1,
              type: "textarea",
              placeholder: " ",
              disabled: true,
              span: 12,
            },
            {
              label: "总经理意见",
              prop: "comments3",
              minRows: 1,
              type: "textarea",
              placeholder: " ",
              disabled: true,
              span: 12,
            },
            {
              label: "总经办意见",
              prop: "comments4",
              minRows: 1,
              type: "textarea",
              placeholder: " ",
              disabled: true,
              span: 12,
            },
          ],
        },
        inquiryForm: {},
        inquiryApprovalOpt: {
          span: 24,
          detail: false,
          size: "mini",
          emptyBtn: false,
          submitText: "审核",
          labelWidth: 120,
          column: [
            {
              label: "审核意见",
              prop: "comments",
              type: "textarea",
              minRows: 2,
              placeholder: " ",
              rules: [
                {
                  required: true,
                  message: "请输入审核意见",
                  trigger: "blur",
                },
              ],
            },
          ],
        },
        inquiryApprovalForm: {},
        inquiryHistoryShow: false,
        rejectVisible: false,
        rejectTitle: "",
        rejectOpt: {
          submitText: "驳回",
          emptyBtn: false,
          span: 24,
          detail: false,
          size: "mini",
          labelWidth: 120,
          column: [
            {
              label: "驳回原因",
              prop: "rejectReason",
              type: "textarea",
              labelPosition: "top",
              minRows: 2,
              placeholder: " ",
              rules: [
                {
                  required: true,
                  message: "请输入驳回原因",
                  trigger: "blur",
                },
              ],
            },
          ],
        },
        rejectForm: {},
      },
      vars: [], // 需要提交的字段
      submitLoading: false, // 提交时按钮loading
      inquiries: {},
      actionItem: {},
      actionItemIndex: null,
      designation: {
        visible: false,
        option: {
          span: 24,
          size: "mini",
          emptyBtn: false,
          column: [
            {
              label: "验收人",
              type: "select",
              prop: "designationInspectionUserId",
              remote: true,
              dicUrl: `/api/blade-user/search/otj/user?name={{key}}`,
              props: {
                label: "realName",
                value: "id",
              },
              dicFormatter: (data) => {
                return data.data.records;
              },
              placeholder: " ",
              filterable: true,
              change: ({value}) => {
                if (value){
                  getUser(value).then((res) => {
                    this.designation.form.designationInspectionUserName = res.data.data.realName;
                  });
                }
              },
              rules: [
                {
                  required: true,
                  message: "请选择指定验收人",
                  trigger: "blur",
                },
              ],
            },
          ],
        },
        form: {},
      },
    };
  },
  methods: {
    handleDesignation() {
      if (this.item.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.designation.form.indexList = this.item.selectionList.map(
        (item) => item.$index
      );
      this.designation.visible = true;
    },
    handleDesignationSubmit(form, done) {
      this.form.items.forEach((item, index) => {
        if (form.indexList && form.indexList.includes(index)) {
          this.$set(
            item,
            "designationInspectionUserId",
            form.designationInspectionUserId
          );
          this.$set(
            item,
            "designationInspectionUserName",
            form.designationInspectionUserName
          );
        }
      });
      done();
      this.designation.visible = false;
    },
    handleWithdrawTask1() {
      if (this.form.crash) {
        this.$message({
          type: "warning",
          message:
            "紧急申购撤销前请联系采购确认是否已购买，再联系综合服务部撤回!",
        });
        return;
      }
      this.handleWithdrawTask();
    },
    htmlDecode(input) {
      const doc = new DOMParser().parseFromString(input, "text/html");
      return doc.documentElement.textContent;
    },
    handleRefreshAttach() {
      getAttachList({
        businessName: "ni_por_apply_item",
        businessKey: this.actionItem.id,
      }).then((res) => {
        const data = res.data.data;
        this.actionItem.attach = data.map((item) => {
          return {
            label: item.originalName,
            value: item.id,
          };
        });
        this.form.items.splice(this.actionItemIndex, 1, { ...this.actionItem });
      });
    },
    rowAttach(row, index) {
      if (!this.option.detail) {
        return;
      }
      this.actionItem = row;
      this.actionItemIndex = index;
      this.$refs.attachRef.init(row.id, "ni_por_apply_item");
    },
    handleInquiryHistory() {
      this.item.inquiryHistoryShow = true;
    },
    handleRejectAndStop() {
      this.item.rejectForm.terminate = true;
      this.$refs.rejectRef.submit();
    },
    handleRejectSubmit(form, done) {
      if (form.terminate)
        this.$confirm("驳回后，将不允许再次发起，是否继续?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            this.form.items.forEach((item, index) => {
              if (
                this.item.rejectForm.id &&
                this.item.rejectForm.id === item.id
              ) {
                this.$set(item, "reject", true);
                this.$set(item, "terminate", true);
                this.$set(item, "rejectReason", form.rejectReason);
                this.$set(item, "rejectUserId", this.userInfo.user_id);
              }
            });
            this.item.rejectVisible = false;
          })
          .finally(() => {
            done();
          });
      else {
        this.form.items.forEach((item, index) => {
          if (this.item.rejectForm.id && this.item.rejectForm.id === item.id) {
            this.$set(item, "reject", true);
            this.$set(item, "terminate", false);
            this.$set(item, "rejectReason", form.rejectReason);
            this.$set(item, "rejectUserId", this.userInfo.user_id);
          }
        });
        done();
        this.item.rejectVisible = false;
      }
    },
    handleInquiryAgree(form, done) {
      this.form.items.forEach((item, index) => {
        if (this.item.inquiryForm.index === index) {
          item.comments = form.comments;
          if (this.process.taskName === "总经理") {
            item.comments3 = `[${dateNow1()}]-${this.userInfo.user_name}：${
              form.comments
            }`;
          } else if (this.process.taskName.indexOf("总经办") >= 0) {
            item.comments4 = `[${dateNow1()}]-${this.userInfo.user_name}：${
              form.comments
            }`;
          } else if (this.process.taskName === "工艺") {
            item.comments2 = `[${dateNow1()}]-${this.userInfo.user_name}：${
              form.comments
            }`;
          } else if (
            [
              "部门主管",
              "尹龙",
              "郭传旭",
              "李春海",
              "王圣林",
              "演绎总经理",
            ].includes(this.process.taskName)
          ) {
            item.comments1 = `[${dateNow1()}]-${this.userInfo.user_name}：${
              form.comments
            }`;
          }
          this.$set(item, "inquiryAgree", true);
        }
      });
      done();
      this.item.inquiryVisible = false;
    },
    isDeptManager() {
      return (
        this.process.taskName === "部门主管" ||
        this.process.taskName === "尹龙" ||
        this.process.taskName === "郭传旭" ||
        this.process.taskName === "李春海" ||
        this.process.taskName === "王圣林"
      );
    },
    handleHistoryInquirySubmit(row) {
      const form = {
        ...row,
        id: null,
      };
      form.inquiries.forEach((inquiry) => (inquiry.id = null));
      this.item.inquiryForm = form;
    },
    handleInquirySubmit(form, done) {
      if (form.inquiries.every((item) => !item.recommend)) {
        this.$message({
          type: "warning",
          message: "请选择推荐供应商!",
        });
        done();
        return;
      }
      if (form.inquiries.filter((item) => item.recommend).length > 1) {
        this.$message({
          type: "warning",
          message: "推荐供应商只能选择一家!",
        });
        done();
        return;
      }
      this.form.items.forEach((item, index) => {
        if (form.index !== null && form.index === index) {
          item.reason = form.reason;
          item.risk = form.risk;
          item.profit = form.profit;
          item.security = form.security;
          item.ep = form.ep;
          item.ac = form.ac;
          item.technology = form.technology;
          item.inquiries = form.inquiries;
          this.$set(item, "inquiry", true);
          this.$set(item, "inquiryExpand", false);
        }
      });
      this.item.inquiryVisible = false;
      done();
    },
    rowReject(row, index) {
      if (this.form.items.length === 1) {
        this.$message({
          type: "warning",
          message: "单条明细请直接驳回该申请!",
        });
        return;
      }
      this.item.rejectForm = {
        index,
        id: row.id,
        rejectReason: row.rejectReason,
      };
      this.item.rejectTitle = `确认要驳回[${row.materialName}]的采购申请？`;
      this.item.rejectVisible = true;
    },
    rowRejectBack(row, index) {
      this.$confirm("确定将选择数据的驳回撤销?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        row.reject = false;
        row.rejectReason = null;
      });
    },
    rowInquiry(row, index) {
      if (this.option.detail && !row.inquiries) {
        return;
      }
      Object.keys(this.item.inquiryForm).forEach(
        (key) => (this.item.inquiryForm[key] = "")
      );
      this.item.inquiryOpt.detail = this.option.detail;
      this.item.inquiryForm = {
        ...row,
        index,
      };
      if (
        !this.item.inquiryForm.inquiries ||
        this.item.inquiryForm.inquiries.length === 0
      ) {
        this.item.inquiryForm.inquiries = [
          {
            applyId: this.form.id,
            applyItemId: row.id,
            recommend: false,
          },
          { applyId: this.form.id, applyItemId: row.id, recommend: false },
          { applyId: this.form.id, applyItemId: row.id, recommend: false },
        ];
      }
      this.item.inquiryVisible = true;
    },
    itemRowExpand(row) {
      row.inquiryExpand = !row.inquiryExpand;
      this.$refs.itemCrud.toggleRowExpansion(row);
    },
    handleMaterialSubmit(selectList, row1) {
      if (selectList.length > 0) {
        const row = selectList[0];
        this.$nextTick(() => {
          this.$set(row1, "materialName", row.name);
          this.$set(row1, "materialCode", row.code);
          this.$set(row1, "specification", row.specification);
          this.$set(row1, "quality", row.quality);
          this.$set(row1, "gb", row.gb);
          this.$set(row1, "unit", row.unit);
          this.$set(row1, "cost", row.cost);
        });
      }
    },
    handleBudgetClear() {
      this.form.budgetId = null;
      this.form.items = [];
    },
    handleBudgetConfirm(selectList, row) {
      if (selectList && selectList.length > 0) {
        this.$nextTick(() => this.$refs.form.clearValidate());
        row.projectId = selectList[0].projectId;
        row.projectTitle = selectList[0].projectTitle;
        row.projectSerialNo = selectList[0].projectSerialNo;
        row.budgetSerialNo = selectList[0].serialNo;
        row.brand = selectList[0].brand;
        row.subType = selectList[0].type;
        const projectId = this.findObject(this.option.column, "projectId");

        if (selectList[0].year === "2" && this.form.crash) {
          //年度预算
          projectId.rules = [
            {
              required: false,
              message: "请输入",
              trigger: "blur",
            },
          ];
        } else {
          projectId.rules = [
            {
              required: true,
              message: "请选择项目",
              trigger: "blur",
            },
          ];
        }
      }
    },
    projectClear() {
      const column = this.findObject(this.option.column, "budgetId");
      column.dicData = [];
      this.form.budgetId = null;
    },
    projectConfirm(selectionList) {
      if (selectionList) {
        this.$nextTick(() => this.$refs.form.clearValidate());
        this.form.projectTitle = selectionList[0].title;
        this.buildTitle(this.form.type, this.form.projectTitle);
        const budgetId = this.findObject(this.option.column, "budgetId");
        if (this.form.crash) {
          budgetId.rules = [
            {
              required: false,
              message: "请输入",
              trigger: "blur",
            },
          ];
        } else {
          budgetId.rules = [
            {
              required: true,
              message: "请选择项目",
              trigger: "blur",
            },
          ];
        }
      }
    },
    itemCellClickChange(row) {
      if (!this.option.detail) {
        this.form.items.forEach((item) => (item.$cellEdit = false));
        row.$cellEdit = true;
        const materialCodeColumn = this.findObject(
          this.item.option.column,
          "materialCode"
        );
        if (!row.cost) {
          materialCodeColumn.rules = [
            {
              required: true,
              message: "请选择编码",
              trigger: "blur",
            },
          ];
        } else {
          materialCodeColumn.rules = [
            {
              required: false,
              message: "请选择编码",
              trigger: "blur",
            },
          ];
        }
      }
    },
    handleNumChange(num, row) {
      if (row.price) {
        row.amount = Number(row.price) * Number(row.num);
      }
      this.sumAmount();
    },
    handleAmountChange(amount, row) {
      if (row.num) {
        row.price = (Number(amount) / Number(row.num)).toFixed(2);
      }
      this.sumAmount();
    },
    sumAmount() {
      this.$nextTick(() => {
        const itemAmount = this.form.items.reduce((acc, cur) => {
          return Number(acc) + Number(cur.amount);
        }, 0);
        this.form.amount = Number(itemAmount.toFixed(2));
      });
    },
    itemSelectionChange(list) {
      this.item.selectionList = list;
    },
    itemSelectionClear() {
      this.item.selectionList = [];
      this.$refs.itemCrud.toggleSelection();
    },
    handleItemAddSubmit(selectList) {
      if (selectList) {
        selectList.forEach((item) => {
          const row = {
            typeId: item.typeId,
            materialCode: item.code,
            materialName: item.name,
            materialTypeId: item.typeId,
            materialId: item.id,
            specification: item.specification,
            quality: item.quality,
            gb: item.gb,
            unit: item.unit,
            cost: item.cost,
            pv: 0,
          };
          this.form.items.push(row);
        });
      }
      this.sumAmount();
    },
    handleItemSelect(selectionList) {
      selectionList.forEach((item) => {
        this.form.items.push({
          budgetItemId: item.id,
          purpose: item.purpose,
          materialCode: item.materialCode,
          materialName: item.materialName,
          materialId: item.materialId,
          specification: item.specification,
          quality: item.quality,
          gb: item.gb,
          unit: item.unit,
          num: item.num,
          budgetNum: item.num,
          usedNum: item.applyNum ? item.applyNum : 0,
          amount: item.amount,
          price: item.price,
          unitDicData: item.unitDicData,
          remark: item.remark,
          cost: item.cost,
          pv: item.pv,
        });
      });
      this.sumAmount();
    },
    itemDelete() {
      if (this.item.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      const indexList = this.item.selectionList.map((item) => item.$index);
      const [...items] = this.form.items.filter(
        (item, index) => !indexList.includes(index)
      );
      this.form.items = items;
      this.sumAmount();
    },
    handleItemInquiry() {
      if (this.item.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      const indexList = this.item.selectionList.map((item) => item.$index);
      Object.keys(this.item.inquiryForm).forEach(
        (key) => (this.form[key] = "")
      );
      if (this.item.selectionList.length === 1) {
        this.item.inquiryForm = {
          ...this.item.selectionList[0],
          index: indexList,
        };
      } else {
        this.item.inquiryForm = {
          index: indexList,
        };
      }
      if (
        !this.item.inquiryForm.inquiries ||
        this.item.inquiryForm.inquiries.length === 0
      ) {
        this.item.inquiryForm.inquiries = [
          { applyId: this.form.id, recommend: false },
          { applyId: this.form.id, recommend: false },
          { applyId: this.form.id, recommend: false },
        ];
      }
      this.item.inquiryVisible = true;
    },
    itemAdd() {
      if (!this.form.budgetId && !this.form.crash) {
        this.$message.warning("请选择预算");
        return;
      }
      if (this.form.crash && !this.form.budgetId) {
        this.$refs.materialSelectDialogRef.visible = true;
      } else {
        this.$refs.porBudgetItemRef.init(this.form.budgetId);
      }
    },
    // 获取任务详情
    getDetail(taskId, processInsId) {
      this.getTaskDetail(taskId, processInsId).then(async (res) => {
        const { process } = res;
        const { variables, status, assignee, processIsFinished } = process;

        const option = this.option;
        option.menuBtn = false;
        const { column, group } = option;
        option.detail =
          !["recall", "reject"].includes(processIsFinished) &&
          status !== "todo";
        if (status !== "todo") {
          // 已办，删除字段默认值
          option.detail = true;
          if (column && column.length > 0) {
            // 处理column
            column.forEach((col) => {
              if (col.type === "dynamic")
                col.children.column.forEach((cc) => {
                  delete cc.value;
                });
              delete col.value;
            });
          }
          if (group && group.length > 0) {
            // 处理group
            group.forEach((gro) => {
              if (gro.column && gro.column.length > 0) {
                gro.column.forEach((col) => {
                  if (col.type === "dynamic")
                    col.children.column.forEach((cc) => {
                      delete cc.value;
                    });
                  delete col.value;
                });
              }
            });
          }
        } else {
          let vars = column.vars || [];
          column.forEach((col) => {
            if (col.value) col.value = this.getDefaultValues(col.value);
          });
          if (group && group.length > 0) {
            // 处理group
            group.forEach((gro) => {
              gro.column.forEach((col) => {
                if (col.value) col.value = this.getDefaultValues(col.value);
              });
              vars = vars.concat(group.vars);
            });
          }
          this.vars = vars;
        }
        for (let key in variables) {
          if (!variables[key]) delete variables[key];
        }
        if (
          assignee === this.userInfo.user_id &&
          (["recall", "reject"].includes(this.process.processIsFinished) ||
            this.process.taskName === "发起人")
        ) {
          this.option.detail = false;
        } else {
          this.option.detail = true;
        }
        const auto = this.findObject(this.option.column, "auto");
        if (variables.auto && (!auto || auto === -1)) {
          option.column.unshift({
            label: "自动申购",
            prop: "auto",
            span: 8,
            type: "radio",
            dicData: [
              {
                label: "是",
                value: true,
              },
              {
                label: "否",
                value: false,
              },
            ],
            detail: true,
          });
        }
        const serialNumber = this.findObject(
          this.option.column,
          "serialNumber"
        );
        if (
          option.column &&
          process.variables &&
          process.variables.serialNumber &&
          (!serialNumber || serialNumber === -1)
        ) {
          option.column.unshift({
            label: "流水号",
            prop: "serialNumber",
            span: variables.auto ? 16 : 24,
            detail: true,
          });
        }

        this.option = option;
        for (const item of variables.items) {
          item.$cellEdit = false;
          if (!item.reject) {
            item.reject = false;
          }
          if (
            item.materialId &&
            (!item.materialCode ||
              ["recall", "reject"].includes(this.process.processIsFinished))
          ) {
            const res = await getMaterialDetail(item.materialId);
            if (!item.materialName) item.materialName = res.data.data.name;
            item.materialCode = res.data.data.code;
            item.specification = res.data.data.specification;
            item.quality = res.data.data.quality;
            item.unit = res.data.data.unit;
          }
        }
        variables.items.forEach((item) => {
          if (!item.price) {
            item.price = (Number(item.amount) / Number(item.num)).toFixed(2);
          }
          item.inquiryAgree = false;
        });
        this.form = variables;
        const remark = this.findObject(this.option.column, "remark");
        if (this.form.remark) {
          remark.display = true;
          remark.disabled = true;
        } else remark.display = false;
        if (
          this.form.crash &&
          !this.isDeptManager() &&
          !["recall", "reject"].includes(process.processIsFinished)
        ) {
          this.buttonList = this.buttonList.filter(
            (b) => b.buttonKey !== "wf_reject" && b.buttonKey !== "wf_terminate"
          );
          this.item.option.menu = false;
        }
        if (this.process.isOwner) {
          this.item.option.menu = false;
        }
        this.initAttach();
        this.waiting = false;
        this.submitLoading = false;
      });
    },
    initAttach() {
      if (this.form.items && this.form.items.length > 0) {
        const itemIds = this.form.items.map((item) => item.id).join(",");
        const attachMap = {};
        getAttachList({
          businessName: "ni_por_apply_item",
          businessKey: itemIds,
        }).then((res) => {
          const data = res.data.data;
          data.forEach((item) => {
            if (!attachMap[item.businessKey]) {
              attachMap[item.businessKey] = [];
            }
            attachMap[item.businessKey].push({
              label: item.originalName,
              value: item.id,
            });
          });
          this.form.items.forEach((item) => {
            item.attach = attachMap[item.id];
          });
        });
      }
    },
    // 审核
    handleExamine(pass) {
      const unAgreeInquiries = [];
      this.form.items.forEach((item) => {
        if (
          item.inquiries &&
          item.inquiries.length > 0 &&
          !item.inquiryAgree &&
          !item.reject
        )
          unAgreeInquiries.push(item);
      });
      if (
        this.option.detail &&
        pass &&
        unAgreeInquiries.length > 0 &&
        [
          "部门主管",
          "尹龙",
          "郭传旭",
          "李春海",
          "王圣林",
          "演绎总经理",
          "工艺",
          "总经理",
          "总经办",
          "总经办-杨工",
          "总经办-王总",
        ].includes(this.process.taskName)
      ) {
        this.$message.warning("有未审核的工作表，请全部审核后再操作");
        return;
      }
      this.submitLoading = true;

      this.$refs.form.validate((valid, done) => {
        if (valid) {
          const variables = {};
          this.sumAmount();
          this.option.column.forEach((v) => {
            if (this.form[v.prop]) variables[v.prop] = this.form[v.prop];
          });
          if (!variables.crash) {
            variables.year = false;
          }
          //清理节点的工作表审批标致
          if (variables.inquiryAuditing) {
            variables.inquiryAuditing = null;
          }
          if (variables.auto == null) {
            variables.auto = false;
          }
          if (pass) this.handleCompleteReject(variables);
          if (["recall", "reject"].includes(process.processIsFinished)) {
            variables.items.forEach((item) => (item.reject = false));
          }
          this.handleCompleteTask(pass, variables)
            .then(() => {
              this.$message.success("处理成功");
              if (this.fromPath) {
                this.handleCloseTag(this.fromPath);
              } else this.handleCloseTag("/plugin/workflow/process/todo");
            })
            .catch(() => {
              done();
              this.submitLoading = false;
            });
        } else {
          done();
          this.submitLoading = false;
        }
      });
    },
    handleCompleteReject(variables) {
      let msg = "";
      variables.items.forEach((item) => {
        if (item.reject && this.userInfo.user_id === item.rejectUserId) {
          msg += `驳回${item.materialName}/${item.num}/${item.amount},驳回原因:${item.rejectReason};\n`;
          item.rejectUserId = null;
        }
      });
      if (msg) {
        this.$refs.examineForm.examineForm.comment = msg;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
/deep/ .el-table__expand-icon {
  visibility: hidden;
}

/deep/ .el-link--inner {
  font-size: 12px;
}

/deep/ .el-upload-list__item-name {
  font-size: 12px;
}

.header {
  width: 100%;
  height: 50px;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 10px 10px 0;
}
</style>

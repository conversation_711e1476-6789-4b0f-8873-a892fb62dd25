<template>
  <el-popover placement="bottom" width="350" v-model="visible" trigger="click">
    <el-tabs v-model="activeName" stretch @tab-click="handleTabClick">
      <el-tab-pane
        v-for="({ label, name, num }, index) in noticeList"
        :key="index"
        :label="`${label}[${num}]`"
        :name="name"
      ></el-tab-pane>
    </el-tabs>
    <el-scrollbar style="height: 300px">
      <flow-todo-notice
        ref="flowTodoRef"
        v-if="activeName === 'flowTodo'"
        :option="option"
        :page="page"
        @toRoute="visible = false"
      />
      <notice-notice
        v-else-if="activeName === 'notice'"
        ref="noticeRef"
        :option="option"
        :page="page"
        @toRoute="visible = false"
      />
      <avue-notice
        v-else
        :data="data"
        :option="option"
        @click="handleClick"
        @page-change="pageChange"
      ></avue-notice>
    </el-scrollbar>
    <div slot="reference">
      <i :class="isDot ? 'notice-flicker' : ''" class="el-icon-bell"></i>
    </div>
  </el-popover>
</template>

<script>
import FlowTodoNotice from "@/page/index/top/components/FlowTodoNotice";
import request from "@/router/axios";
import NoticeNotice from "@/page/index/top/components/NoticeNotice";
import { connectWebsocket } from "@/util/websocket";
import store from "@/store";

const noticeList = [
  { label: "通知公告", name: "notice", path: "/desk/notice-user", num: 0 },
  {
    label: "待办事宜",
    name: "flowTodo",
    path: "/plugin/workflow/process/todo",
    num: 0,
  },
];

export default {
  name: "top-notice",
  components: {
    NoticeNotice,
    FlowTodoNotice,
  },
  data() {
    return {
      visible: false,
      activeName: "notice",
      activePath: "",
      option: {
        props: {
          img: "img",
          title: "title",
          subtitle: "subtitle",
          tag: "tag",
          status: "status",
        },
      },
      page: {
        pageSize: 5,
        currentPage: 1,
        total: 0,
      },
      data: [],
      noticeList,
      noticeNameLabel: {},
      noticeNamePath: {},
      isDot: false,
      notifyPromise: Promise.resolve(),
    };
  },
  created() {
    this.websocketConn();
    this.loadRemind();
    this.noticeNamePath = this.noticeList.reduce((acc, cur) => {
      acc[cur.name] = cur.path;
      return acc;
    }, {});
    this.noticeNameLabel = this.noticeList.reduce((acc, cur) => {
      acc[cur.name] = cur.label;
      return acc;
    }, {});
    this.activePath = this.noticeNamePath[this.activeName];
    store.dispatch("LoadLoginEvent");
  },
  methods: {
    websocketConn() {
      connectWebsocket(
        `ws://${window.location.host}/websocket/socket/message`,
        (message) => {
          //推送事件到待办事项页面
          this.$bus.$emit("todo_new_message_event");

          //推送事件到首页中的待办组件中
          this.$bus.$emit("index_todo_new_message_event");

          this.loadRemind();

          this.$refs[`${this.activeName}Ref`].loadData();

          const messageJson = JSON.parse(message);
          console.log(messageJson);
          this.$notify({
            title: messageJson.title,
            position: "top-right",
            type: ["success", "warning", "info", "error"].includes(
              messageJson.type
            )
              ? messageJson.type
              : "info",
            dangerouslyUseHTMLString: true,
            message: messageJson.content,
            duration: 10000,
            onClick: () => {
              this.notifyClick(messageJson.code);
            },
          });
        },
        (err) => {
          console.log(err);
        }
      );
    },
    handleClick(item) {
      this.$message.success(JSON.stringify(item));
    },
    loadNotice() {},
    loadStockApply() {},
    handleTabClick(tab, event) {
      console.log(tab, event);
      this.activePath = this.noticeNamePath[tab.name];
    },
    loadRemind() {
      request({
        url: "/api/blade-desk/top-notice/list",
        method: "get",
        NProgress: false,
      }).then((res) => {
        const data = res.data.data;
        const notifyesKV = data.reduce((acc, cur) => {
          acc[cur.code] = cur.num;
          return acc;
        }, {});

        this.noticeList.forEach((notice) => {
          notice.num = notifyesKV[notice.name];
          this.isDot = notice.num > 0;
        });
      });
    },
    notifyClick(name) {
      this.visible = true;
      if (name && this.noticeNamePath[name]) {
        this.$router.push(this.noticeNamePath[name]);
      } else if (name) {
        this.$router.push(name);
      }
    },
    pageChange(page, done) {
      done();
      this.visible = false;
      this.$router.push(this.activePath);
    },
  },
};
</script>

<style lang="scss" scoped>
.cursor {
  cursor: pointer;
}

@keyframes blink {
  50% {
    opacity: 0;
  }
}

.notice-flicker {
  color: red;
  animation: blink 1s infinite;
}
</style>

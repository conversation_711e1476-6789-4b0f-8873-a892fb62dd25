<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :search.sync="query"
      :page.sync="page"
      v-model="form"
      ref="crud"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      :cell-style="cellStyle"
      @on-load="onLoad"
    >
      <template #menuLeft>
        <el-button
          size="mini"
          type="danger"
          icon="el-icon-smoking"
          v-if="permission.ni_fg_daily_stock_snapshot_rebuild"
          @click="handleRebuildCurrentSnapshot"
        >
          重建当日快照
        </el-button>
        <!--导出-->
        <el-button
          type="info"
          icon="el-icon-download"
          size="mini"
          v-if="permission.ni_fg_daily_stock_snapshot_export"
          @click="handleExport"
          >导 出
        </el-button>
        <el-divider direction="vertical"></el-divider>
        <el-radio-group v-model="area" size="mini" @input="onLoad(page)">
          <el-radio-button label="ALL">全部</el-radio-button>
          <el-radio-button label="CN">国内</el-radio-button>
          <el-radio-button label="OS">国外</el-radio-button>
        </el-radio-group>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import {
  dailyInOutboundSum,
  getList,
  rebuild,
} from "@/api/ni/fg/fgDailyStockSnapshot";
import { mapGetters } from "vuex";
import { dateFormat } from "@/util/date";

export default {
  data() {
    return {
      form: {},
      query: {
        date: null,
      },
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        menu: false,
        showSummary: true,
        sumColumnList: [
          {
            name: "openingStock",
            type: "sum",
          },

          {
            name: "openingStockWeight",
            type: "sum",
          },
          {
            name: "closingStock",
            type: "sum",
          },
          {
            name: "closingStockWeight",
            type: "sum",
          },
        ],
        menuWidth: 80,
        addBtn: false,
        delBtn: false,
        editBtn: false,
        size: "mini",
        searchSize: "mini",
        align: "center",
        searchIndex: 3,
        searchIcon: true,
        searchEnter: true,
        height: "auto",
        calcHeight: 30,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        selection: true,
        dialogClickModal: false,
        reserveSelection: true,
        column: [
          {
            label: "日期",
            prop: "date",
            type: "date",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            search: true,
            searchOrder: 99,
            clearable: false,
            hide: true,
            showColumn: false,
          },
          {
            label: "仓库",
            prop: "depotId",
            type: "select",
            dicUrl: "/api/ni/base/depot/info/list?status=2&type=1",
            props: {
              label: "name",
              value: "id",
              desc: "code",
            },
            sortable: true,
            filters: true,
            search: true,
            minWidth: 100,
            overHidden: true,
            searchOrder: 98,
          },
          {
            label: "存货编码",
            prop: "materialCode",
            sortable: true,
            placeholder: " ",
            minWidth: 100,
            overHidden: true,
            filters: true,
            search: true,
            searchOrder: 95,
          },
          {
            label: "规格",
            prop: "spec",
            type: "select",
            dicUrl: "/api/ni/product/spec/list",
            props: {
              label: "name",
              value: "code",
              desc: "code",
            },
            hide: true,
            showColumn: false,
            filterable: true,
            search: true,
            searchOrder: 98,
          },
          {
            label: "规格",
            prop: "specText",
            placeholder: " ",
            display: false,
            overHidden: true,
            sortable: true,
            filters: true,
            minWidth: 80,
          },
          {
            label: "质量",
            prop: "qualityLevel",
            type: "select",
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_product_sku_quality_level",
            props: {
              label: "dictValue",
              value: "dictKey",
              desc: "dictKey",
            },
            placeholder: " ",
            sortable: true,
            filters: true,
            width: 100,
          },
          {
            label: "外包装",
            prop: "packageId",
            type: "select",
            dicUrl: `/api/ni/product/packaging/list?innerPark=0&current=1&size=20&status=1&&name={{key}}`,
            props: {
              label: "name",
              value: "id",
              desc: "type",
            },
            search: true,
            remote: true,
            dicFormatter: (data) => {
              return data.data.records;
            },
            filterable: true,
            hide: true,
            showColumn: false,
            searchOrder: 97,
          },
          {
            label: "外包装",
            prop: "packageText",
            placeholder: " ",
            display: false,
            overHidden: true,
            minWidth: 115,
            sortable: true,
            filters: true,
          },
          {
            label: "内包装",
            prop: "innerPackageText",
            placeholder: " ",
            display: false,
            overHidden: true,
            sortable: true,
            filters: true,
            minWidth: 115,
          },
          {
            label: "内包装",
            prop: "innerPackageId",
            type: "select",
            dicUrl:
              "/api/ni/product/packaging/list?innerPark=1&current=1&size=20&status=1&&name={{key}}",
            props: {
              label: "name",
              value: "id",
              desc: "type",
            },
            search: true,
            remote: true,
            dicFormatter: (data) => {
              return data.data.records;
            },
            hide: true,
            showColumn: false,
            filterable: true,
            searchOrder: 96,
          },
          {
            label: "期初件数",
            prop: "openingStock",
            minWidth: 80,
            sortable: true,
            type: "number",
          },
          {
            label: "期末件数",
            prop: "closingStock",
            minWidth: 80,
            sortable: true,
            type: "number",
          },
          {
            label: "期初重量",
            prop: "openingStockWeight",
            placeholder: " ",
            type: "number",
            sortable: true,
            minWidth: 80,
          },
          {
            label: "期末重量",
            prop: "closingStockWeight",
            placeholder: " ",
            type: "number",
            sortable: true,
            minWidth: 80,
          },
          {
            label: "入库",
            minWidth: 140,
            children: [
              {
                label: "件数",
                prop: "inboundNum",
                placeholder: " ",
                minWidth: 70,
              },
              {
                label: "重量",
                prop: "inboundWeight",
                placeholder: " ",
                minWidth: 70,
              },
            ],
          },
          {
            label: "出库",
            minWidth: 140,
            children: [
              {
                label: "件数",
                prop: "outboundNum",
                placeholder: " ",
                minWidth: 70,
              },
              {
                label: "重量",
                prop: "outboundWeight",
                placeholder: " ",
                minWidth: 70,
              },
            ],
          },
          {
            label: "国内/外",
            prop: "area",
            type: "radio",
            dicData: [
              {
                label: "国内",
                value: "CN",
              },
              {
                label: "国外",
                value: "OS",
              },
            ],
            width: 100,
            filters: true,
            sortable: true,
          },
        ],
      },
      data: [],
      exportColumn: [
        {
          label: "仓库",
          prop: "depotName",
        },
        {
          label: "存货编码",
          prop: "materialCode",
        },
        {
          label: "规格",
          prop: "specText",
        },
        {
          label: "质量",
          prop: "qualityLevel",
        },
        {
          label: "外包装",
          prop: "packageText",
        },
        {
          label: "内包装",
          prop: "innerPackageText",
        },
        {
          label: "数量",
          prop: "num",
        },
        {
          label: "重量",
          prop: "weight",
        },
        {
          label: "单位",
          prop: "unit",
        },
        {
          label: "国内/外",
          prop: "area",
        },
        {
          label: "备注",
          prop: "remark",
        },
        {
          label: "状态",
          prop: "status",
        },
      ],
      depotKeyValue: {},
      statusDictKeyValue: {},
      unitKeyValue: {},
      action: null,
      area: "ALL",
      qualityLevelDictKeyValue: {},
      qualityLevelColorMap: {
        A: "#67C23A", // 高吸附 - 绿色
        P: "#409EFF", // 优等品 - 蓝色
        Q: "#E6A23C", // 合格品 - 橙色
      },
    };
  },
  computed: {
    ...mapGetters(["permission", "userInfo", "colorName"]),
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  created() {
    this.query.date = this.getYesterday();
    this.dictInit();
  },
  methods: {
    getYesterday() {
      const today = new Date();
      const yesterday = new Date(today.getTime() - 86400000);
      return dateFormat(yesterday, "yyyy-MM-dd");
    },
    dictInit() {
      this.$http
        .get("/api/ni/base/depot/info/list?status=2&type=1")
        .then((res) => {
          this.depotKeyValue = res.data.data.reduce((acc, cur) => {
            acc[cur.id] = cur.name;
            return acc;
          }, {});
        });
      this.$http
        .get(
          "/api/blade-system/dict-biz/dictionary?code=ni_product_sku_quality_level"
        )
        .then((res) => {
          const dict = res.data.data;
          this.qualityLevelDictKeyValue = dict.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });
      this.$http
        .get(
          "/api/blade-system/dict-biz/dictionary?code=ni_fg_inventory_status"
        )
        .then((res) => {
          this.statusDictKeyValue = res.data.data.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });
      this.$http
        .get("/api/blade-system/dict-biz/dictionary?code=ni_material_unit")
        .then((res) => {
          this.unitKeyValue = res.data.data.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });
    },
    handleRebuildCurrentSnapshot() {
      this.$confirm("确定重建当日的快照?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return rebuild();
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    handleExport() {
      let msg = "是否导出当前选取的数据？";
      if (this.selectionList.length === 0) {
        msg = "是否导出全部数据？";
      }
      this.$confirm(msg, {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        let data = this.selectionList;
        if (this.selectionList.length === 0) {
          const q = { ...this.query };
          q.area = this.area === "ALL" ? null : this.area;
          const res = await getList(1, 100000, q);
          data = res.data.data.records;
        }
        this.$Export.excel({
          title: "当前库存",
          columns: this.exportColumn,
          data: data.map((item) => {
            return {
              ...item,
              depotName: this.depotKeyValue[item.depotId],
              status: this.statusDictKeyValue[item.status],
              unit: this.unitKeyValue[item.unit],
              qualityLevel: this.qualityLevelDictKeyValue[item.qualityLevel],
              area: item.area === "CN" ? "国内" : "国外",
            };
          }),
        });
      });
    },
    handleInventorySelectConfirm(selectionList) {
      const depotIds = new Set();
      selectionList.forEach((item) => {
        depotIds.add(item.deptId);
      });
      if (depotIds.size > 1) {
        this.$message({
          type: "warning",
          message: "请选择同一仓库的数据进行出库!",
        });
        return;
      }
      const depotId = selectionList[0].depotId;
      if (this.action === "restocking") {
        this.$refs.restockingFormDialogRef.onAdd(depotId, selectionList);
      } else if (this.action === "outbound") {
        const freeze = selectionList.some((item) => item.status !== 1);
        if (freeze) {
          this.$message({
            type: "warning",
            message: "请选择非冻结的数据进行出库!",
          });
          return;
        }
        this.$refs.outboundFormDialogRef.onAdd(depotId, selectionList);
      } else if (this.action === "transfer") {
        this.$refs.transferFormDialogRef.onAdd(depotId, selectionList);
      } else if (this.action === "backProduction") {
        this.$refs.outboundFormDialogRef.onAdd(depotId, selectionList, {
          type: "0207",
        });
      } else if (this.action === "freeze") {
        this.$refs.freezeFormDialogRef.onFreeze(depotId, selectionList);
      }
    },
    searchReset() {
      this.query = {
        date: this.getYesterday(),
      };
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      const q = {
        ...params,
        ...this.query,
      };
      q.area = this.area === "ALL" ? null : this.area;
      getList(page.currentPage, page.pageSize, q).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        data.records.forEach((item) => {
          item.inboundNum = "--";
          item.outboundNum = "--";
          item.inboundWeight = "--";
          item.outboundWeight = "--";
        });
        const ids = data.records.map((item) => item.id);
        if (ids.length > 0) {
          this.loadInboundSum(ids.join(","));
          this.loadOutboundSum(ids.join(","));
        }
        this.data = data.records;
        this.loading = false;
      });
    },
    loadInboundSum(ids) {
      dailyInOutboundSum(ids, "IN").then((res) => {
        const data = res.data.data;
        const dataMap = new Map(data.map((item) => [item.id + "", item]));
        this.data.forEach((item) => {
          if (dataMap.has(item.id + "")) {
            const target = dataMap.get(item.id + "");
            item.inboundNum = target.num;
            item.inboundWeight = target.weight;
          }
        });
        console.log(this.data);
      });
    },
    loadOutboundSum(ids) {
      dailyInOutboundSum(ids, "OUT").then((res) => {
        const data = res.data.data;
        const dataMap = new Map(data.map((item) => [item.id, item]));
        this.data.forEach((item) => {
          if (dataMap.has(Number(item.id))) {
            const target = dataMap.get(Number(item.id));
            item.outboundNum = target.num;
            item.outboundWeight = target.weight;
          }
        });
      });
    },
    cellStyle({ row, column }) {
      if ("area" === column.columnKey) {
        return {
          backgroundColor: row.area === "CN" ? "#F56C6C" : this.colorName,
          color: "#fff",
        };
      }
      if ("freezeNum" === column.columnKey && row.freezeNum > 0) {
        return {
          backgroundColor: "#409EFF",
          color: "#fff",
          textDecoration: "underline",
          cursor: "pointer",
        };
      }
      if ("qualityLevel" === column.columnKey) {
        const color = this.qualityLevelColorMap[row.qualityLevel];
        return {
          backgroundColor: color || "#909399", // 默认颜色为灰色
          color: "#fff",
        };
      }
    },
  },
};
</script>

<style></style>

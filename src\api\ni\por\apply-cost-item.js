import request from "@/router/axios";

export const getPage = (current, size, params) => {
  return request({
    url: "/api/ni/por/apply/cost/item/page",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};
export const getList = (params) => {
  return request({
    url: "/api/ni/por/apply/cost/item/list",
    method: "get",
    params: {
      ...params,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/ni/por/apply/cost/item/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const remove = (ids) => {
  return request({
    url: "/api/ni/por/apply/cost/item/remove",
    method: "post",
    params: {
      ids,
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/ni/por/apply/cost/item/submit",
    method: "post",
    data: row,
  });
};

export const update = (row) => {
  return request({
    url: "/api/ni/por/apply/cost/item/submit",
    method: "post",
    data: row,
  });
};

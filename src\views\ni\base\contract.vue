<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :search.sync="query"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      :before-close="beforeClose"
      :upload-preview="uploadPreview"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
      @tree-load="treeLoad"
      :cell-style="cellStyle"
      @cell-click="cellClick"
    >
      <template #archiveNo="{ row, index, size }">
        <span v-if="row.status !== 9"></span>
        <span
          v-else-if="row.archive == null"
          class="el-dropdown-link"
          style="font-size: 12px; color: #909399"
          @click="handleGenerateArchiveNo(row)"
        >
          未归档
        </span>
        <span v-else-if="row.archive == 0" @click="archiveDetail(row)">
          <el-tag type="warning" style="cursor: pointer">
            <span v-if="row.archiveSerialNo == null">
              {{ row.archiveNo }}-(待签收)
            </span>
            <span v-else>
              {{ row.archiveSerialNo }}
            </span>
            <!--  -->
          </el-tag>
        </span>
        <span v-else @click="archiveDetail(row)">
          <el-tag type="success" style="cursor: pointer">
            <!-- {{ row.archiveNo }}-(已归档) -->
            <span v-if="row.archiveSerialNo == null">
              {{ row.archiveNo }}-(已归档)
            </span>
            <span v-else>
              {{ row.archiveSerialNo }}
            </span>
          </el-tag>
        </span>
      </template>
      <template #attachPreview="{ row }">
        <span v-if="row.status !== 9"></span>
        <el-link
          v-else-if="row.attachPreview != null"
          type="success"
          @click="archiveAttachDialog(row)"
        >
          查看附件
        </el-link>
        <el-link v-else type="warning" @click="archiveAttachDialog(row)">
          上传附件
        </el-link>
      </template>
      <template #amount="{ row, index }">
        <span v-if="row.unPayAmount">{{
          Number(row.unPayAmount).toLocaleString("zh-CN", {
            minimumFractionDigits: 2,
          })
        }}</span>
        <span v-else-if="row.amount">{{
          Number(row.amount).toLocaleString("zh-CN", {
            minimumFractionDigits: 2,
          })
        }}</span>
      </template>
      <template #payApplyState="{ row, index, size }">
        <el-tag
          type="info"
          effect="plain"
          size="mini"
          v-if="row.payType === '2' && row.payApplyState === 0"
        >
          {{ row.$payApplyState }}
        </el-tag>
        <el-tag
          type="warning"
          effect="plain"
          size="mini"
          v-else-if="row.payType === '2' && row.payApplyState === 1"
        >
          {{ row.$payApplyState }}
        </el-tag>
        <el-tag
          effect="dark"
          size="mini"
          v-else-if="row.payType === '2' && row.payApplyState === 2"
        >
          {{ row.$payApplyState }}
        </el-tag>
      </template>
      <template #payType="{ row, index }">
        <el-tag v-if="row.payType == '1'" size="mini" effect="plain"
          >{{ payTypeDictKeyValue[row.payType] }}
        </el-tag>
        <el-tag
          v-else-if="row.payType == '2'"
          size="mini"
          type="danger"
          effect="plain"
        >
          {{ payTypeDictKeyValue[row.payType] }}
        </el-tag>
        <el-tag v-else size="mini" type="info" effect="plain"
          >{{ payTypeDictKeyValue[row.payType] }}
        </el-tag>
      </template>
      <template #orderIdForm="{ disabled, size, index }">
        <por-order-select
          v-model="form.orderId"
          :label.sync="form.order"
          :size="size"
          :multiple="false"
          :disabled="disabled"
          :params="{
            brand: form.brand,
            supplierId: form.b,
          }"
          :before-select="beforeOrderSelect"
          @clear="handleOrderClear"
          @submit="handleOrderSubmit"
        />
      </template>
      <template #template="{ row, index }">
        <el-tag v-if="row.template" size="mini" type="info" effect="plain"
          >是
        </el-tag>
        <el-tag v-else size="mini" type="danger" effect="dark">否</el-tag>
      </template>

      <template #serialNo="{ row, index, size }">
        <div @click="rowDetail(row)">
          <a
            href="javascript:void(0);"
            style="text-decoration: underline"
            onclick="return false;"
            >{{ row.serialNo }}</a
          >
        </div>
      </template>
      <template #brand="{ row, disabled, size, index }">
        <el-tag
          v-if="row.brand"
          :size="size"
          :effect="row.brand === '1' ? 'dark ' : 'plain'"
        >
          {{ brandDictKeyValue[row.brand] }}
        </el-tag>
      </template>
      <template #bForm="{ row, index, size, disabled }">
        <supplier-select
          v-model="form.b"
          :size="size"
          :multiple="false"
          :disabled="disabled"
          @clear="() => (form.b = null)"
          @submit="handleSupplierSubmit"
        />
      </template>
      <template #type="{ row, index }">
        <el-tag v-if="row.typeName" :key="index" size="mini" effect="plain">
          {{ row.typeName }}
        </el-tag>
      </template>
      <template #menuLeft>
        <el-dropdown
          v-if="permission.ni_base_contract_add & (1 !== 1)"
          @command="handleAdd"
        >
          <el-button type="primary" size="mini" icon="el-icon-plus"
            >新 增<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
              v-for="item in brandDict"
              :key="item.dictKey"
              :command="item.dictKey"
              >{{ item.dictValue }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-button
          type="primary"
          size="mini"
          icon="el-icon-s-promotion"
          plain
          v-if="permission.ni_base_contract_apply"
          @click="handleApply"
          >合同申请
        </el-button>
        <el-button
          type="warning"
          size="mini"
          icon="el-icon-money"
          plain
          v-if="permission.ni_base_contract_change_pay_state"
          @click="handleChangePayState"
          >修改付款状态
        </el-button>
        <el-button
          type="danger"
          size="mini"
          icon="el-icon-delete"
          plain
          v-if="permissionList.delBtn"
          @click="handleDelete"
          >删 除
        </el-button>
        <el-button
          type="warning"
          size="mini"
          icon="el-icon-camera"
          plain
          v-if="permission.ni_base_contract_apply"
          @click="handleContractScan"
          >合同扫描
        </el-button>
        <el-button
          type="success"
          size="mini"
          icon="el-icon-download"
          plain
          v-if="permission.ni_base_contract_export"
          @click="handleExport"
        >
          导出
        </el-button>
      </template>
      <template #menuRight="{ size }">
        <el-radio-group v-model="brand" size="mini" @input="onLoad(page)">
          <el-radio-button label="natergy">能特异</el-radio-button>
          <el-radio-button label="yy">演绎</el-radio-button>
        </el-radio-group>
        <el-divider direction="vertical"></el-divider>
        <el-button
          icon="el-icon-time"
          circle
          :size="size"
          @click="handleLog"
        ></el-button>
      </template>
      <template #menu="{ row, index, size }">
        <el-button
          v-if="
            (permission.ni_base_contract_edit && row.status === 0) ||
            userInfo.role_name.includes('admin')
          "
          type="text"
          icon="el-icon-edit"
          :size="size"
          @click="rowEdit(row, index)"
          >编辑
        </el-button>
        <el-button
          v-if="permission.ni_base_contract_apply && row.status === 0"
          type="text"
          icon="el-icon-s-promotion"
          :size="size"
          @click="rowApply(row)"
          >提交申请
        </el-button>
        <el-button
          v-if="
            (row.createUser === userInfo.user_id && row.status === 0) ||
            userInfo.role_name.includes('admin')
          "
          type="text"
          icon="el-icon-delete"
          :size="size"
          style="color: #f56c6c"
          @click="rowDel(row, index)"
          >删除
        </el-button>
        <el-button
          v-if="
            row.status === 9 &&
            !row.parentId &&
            (!row.archive || row.archive !== 1) &&
            permission.ni_base_contract_apply
          "
          type="text"
          style="color: #e6a23c"
          icon="el-icon-s-promotion"
          :size="size"
          @click="rowAdditional(row)"
          >补充合同
        </el-button>
        <el-button
          v-if="
            (!row.type.includes('11') &&
              row.amount &&
              Number(row.amount) !== 0 &&
              row.status === 9 &&
              row.unPayAmount &&
              Number(row.unPayAmount) > 0) ||
            ['3', '9'].includes(row.type)
          "
          type="text"
          icon="el-icon-s-finance"
          :size="size"
          @click="rowPay(row)"
          >付款申请
        </el-button>
        <el-button
          type="text"
          icon="el-icon-s-grid"
          :size="size"
          @click="rowItems(row)"
          >明细
        </el-button>
        <el-button
          type="text"
          icon="el-icon-download"
          :size="size"
          @click="rowAttach(row)"
          >附件
        </el-button>
        <el-button
          type="text"
          icon="el-icon-circle-close"
          :size="size"
          style="color: #f56c6c"
          v-if="permission.ni_base_contract_end && row.status === 9"
          @click="rowEnd(row)"
          >终止
        </el-button>
      </template>
      <template #billState="{ row, index }">
        <el-tag
          v-if="
            row.invoiceAmount &&
            row.invoiceAmount < row.amount &&
            row.invoiceAmount > 0
          "
          size="mini"
          type="warning"
        >
          部分开票
        </el-tag>
        <el-tag
          v-else-if="row.invoiceAmount && row.invoiceAmount >= row.amount"
          size="mini"
          effect="dark"
        >
          已开票
        </el-tag>
        <el-tag v-else size="mini" type="info" effect="plain"> 未开票</el-tag>
      </template>
    </avue-crud>
    <log-opt-dialog ref="logOptDialogRef" :module="module" />
    <por-order-list-dialog ref="orderListRef" />
    <contract-pay-build-drawer
      ref="contractPayBuildDialogRef"
      @submit="onLoad(page)"
    />
    <contract-pay-apply-build-drawer
      ref="contractPayApplyBuildDialogRef"
      @submit="onLoad(page)"
    />
    <el-drawer
      :visible.sync="detailVisible"
      :title="form.title"
      custom-class="wf-drawer"
      size="100%"
      append-to-body
    >
      <contract-por-detail
        v-if="detailVisible"
        :taskId="form.taskId"
        :businessKey="form.id"
        :processInstanceId="form.processInsId"
      />
    </el-drawer>
    <attach-dialog ref="attachDialogRef" code="private" />
    <order-item-drawer ref="payableItemRef" />
    <attach-dialog ref="archiveAttachRef" code="private" />
    <invoice-link-dialog
      ref="invoiceLinkDialogRef"
      :title="invoiceSelectTitle"
      :contract-id="contractId"
      :supplier-id="supplierId"
      type="contract"
    />
    <!--------------- 合同归档表单 --------------->
    <el-dialog
      title="合同归档"
      append-to-body
      :visible.sync="archiveVisible"
      width="50%"
    >
      <avue-form
        ref="archiveRef"
        :option="archiveOption"
        v-model="archiveForm"
        @submit="archiveSubmit"
        @reset-change="handleReset"
        :upload-preview="archivePreview"
        :upload-delete="archiveDelete"
        :upload-config="{
          showFileList: true,
          autoUpload: true,
          onPreview: null,
        }"
      >
      </avue-form>
    </el-dialog>
    <!----------------- 合同归档详情 ------------------>
    <el-dialog
      title="详情"
      append-to-body
      :visible.sync="archiveDetailVisible"
      width="50%"
    >
      <avue-form
        ref="archiveDetailRef"
        :option="archiveDetailOption"
        v-model="archiveDetailForm"
      >
        <template #archiveAttach="{ row, index }">
          <el-link
            v-if="archiveDetailForm.archiveAttach"
            type="primary"
            target="_blank"
            @click="archiveRowAttach(archiveDetailForm)"
          >
            <i class="el-icon-circle-plus-outline" />
            已签订附件({{
              archiveDetailForm.archiveAttach
                ? archiveDetailForm.archiveAttach.length
                : 0
            }})
          </el-link>
        </template>
      </avue-form>
      <el-button
        v-if="
          this.archiveDetailForm.archive == 0 &&
          permission.ni_base_contract_archive_receive
        "
        style="margin-left: 48%"
        icon="el-icon-check"
        type="success"
        size="mini"
        @click="handleReceiveBtn"
      >
        签收
      </el-button>
    </el-dialog>
    <contract-scan-dialog ref="contractScanDialog" />
    <contract-form-dialog ref="contractFormDialog" @submit="onLoad(page)" />
  </basic-container>
</template>

<script>
import {
  add,
  apply,
  archiveStart,
  end,
  generateArchiveNo,
  getDetail,
  getPage,
  getTaskIdByProcessInsId,
  remove,
  toVoid,
  update,
  getLazyList,
} from "@/api/ni/base/contract";
import { mapGetters } from "vuex";
import LogOptDialog from "@/components/log-opt-dialog";
import UserSelect from "@/components/user-select";
import {
  fileLink,
  getDetail as getAttachDetail,
  getList as getAttachList,
  remove as attachRemove,
} from "@/api/resource/attach";
import PorOrderListDialog from "@/views/ni/por/components/OrderListDialog";
import FinApplyPayableListDialog from "@/views/ni/fin/components/ApplyPayableListDialog";
import SupplierSelect from "@/views/ni/base/components/SupplierSelect1";
import exForm from "@/views/plugin/workflow/mixins/ex-form";
import draft from "@/views/plugin/workflow/mixins/draft";
import ContractPorDetail from "@/views/ni/base/contract-por-detail";
import OrderItemDrawer from "@/views/ni/por/components/OrderItemDrawer";
import ContractPayBuildDrawer from "@/views/ni/base/components/ContractPayBuildDrawer";

import PorOrderSelect from "@/views/ni/por/components/OrderSelect";
import AttachDialog from "@/components/attach-dialog";
import InvoiceLinkDialog from "@/views/ni/fin/components/InvoiceLinkDialog";
import archiveOption from "@/api/ni/base/archiveOption";
import { completeTask } from "@/api/plugin/workflow/process";
import { Base64 } from "js-base64";
import contractScanDialog from "@/views/ni/base/components/ContractScanDialog";
import ContractPayApplyBuildDrawer from "@/views/ni/base/components/ContractPayApplyBuildDrawer";
import ContractFormDialog from "@/views/ni/base/components/ContractFormDialog";

export default {
  components: {
    ContractFormDialog,
    InvoiceLinkDialog,
    AttachDialog,
    PorOrderSelect,
    ContractPorDetail,
    SupplierSelect,
    FinApplyPayableListDialog,
    PorOrderListDialog,
    UserSelect,
    LogOptDialog,
    ContractPayBuildDrawer,
    OrderItemDrawer,
    contractScanDialog,
    ContractPayApplyBuildDrawer,
  },
  mixins: [exForm, draft],
  data() {
    return {
      detailVisible: false,
      module: "ni_base_contract",
      processDefKey: "process_base_contract_por",
      formKey: "wf_ex_base/Contract/Por",
      form: {},
      archiveForm: {}, //合同归档表单
      archiveDetailForm: {},
      archiveVisible: false, //合同归档弹窗
      archiveDetailVisible: false,
      archiveOption: archiveOption,
      archiveDetailOption: {
        labelWidth: 120,
        calcHeight: 30,
        size: "mini",
        menuBtn: false,
        column: [
          {
            type: "input",
            label: "签收状态",
            span: 12,
            display: true,
            readonly: true,
            disabled: true,
            prop: "archiveStatus",
          },
          {
            type: "input",
            label: "归档编号",
            prop: "archiveNo",
            span: 12,
            display: false,
            readonly: true,
            disabled: true,
          },
          {
            type: "input",
            label: "归档编号",
            prop: "archiveSerialNo",
            span: 12,
            display: true,
            readonly: true,
            disabled: true,
          },
          {
            type: "input",
            label: "合同名称",
            span: 12,
            display: true,
            prop: "contractTitle",
            readonly: true,
            disabled: true,
          },
          {
            type: "input",
            label: "合同编号",
            span: 12,
            display: true,
            prop: "contractNo",
            readonly: true,
            disabled: true,
          },
          {
            type: "input",
            label: "流程实例ID",
            span: 12,
            display: false,
            prop: "archiveProcessInsId",
          },
          {
            label: "已签订附件",
            prop: "archiveAttach",
            display: true,
          },
        ],
      },
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        lazy: true,
        editBtn: false,
        delBtn: false,
        addBtn: false,
        searchEnter: true,
        menuWidth: 200,
        dialogDirection: "rtl",
        dialogType: "drawer",
        labelWidth: 110,
        searchLabelWidth: 110,
        size: "mini",
        searchSize: "mini",
        align: "center",
        searchIndex: 3,
        searchIcon: true,
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        selection: true,
        dialogClickModal: false,
        column: [
          {
            label: "审核状态",
            prop: "status",
            search: true,
            type: "select",
            dicUrl: "/api/blade-system/dict/dictionary?code=data_status",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            dataType: "number",
            addDisplay: false,
            editDisplay: false,
            overHidden: true,
            minWidth: 85,
            fixed: "left",
          },
          {
            label: "申请人",
            prop: "createUser",
            type: "select",
            remote: true,
            dicUrl: `/api/blade-user/search/otj/user?name={{key}}`,
            props: {
              label: "realName",
              value: "id",
            },
            dicFormatter: (data) => {
              return data.data.records;
            },
            display: false,
            hide: true,
            showColumn: false,
            search: true,
          },
          {
            label: "申请人",
            prop: "createUserName",
            minWidth: 80,
            display: false,
          },

          {
            label: "合同名称",
            prop: "name",
            type: "input",
            minWidth: 120,
            overHidden: true,
            fixed: "left",
            search: true,
            searchOrder: 98,
            span: 12,
            rules: [
              {
                required: true,
                message: "请输入合同名称",
                trigger: "blur",
              },
            ],
          },
          {
            label: "主合同",
            prop: "parentSerialNo",
            minWidth: 110,
            placeholder: " ",
            type: "input",
            disabled: true,
            search: true,
            overHidden: true,
            span: 12,
          },
          {
            label: "合同编号",
            prop: "serialNo",
            minWidth: 110,
            placeholder: "系统自动生成",
            type: "input",
            disabled: true,
            search: true,
            searchOrder: 99,
            overHidden: true,
            span: 12,
          },
          {
            label: "供应商",
            prop: "b",
            remote: true,
            type: "select",
            dicUrl:
              "/api/ni/base/supplier/info/page?status=2&blacklist=0&keyword={{key}}",
            props: {
              label: "name",
              value: "id",
              desc: "code",
            },
            dicFormatter: (data) => {
              return data.data.records;
            },
            search: true,
            searchOrder: 97,
            hide: true,
            showColumn: false,
            span: 12,
            rules: [
              {
                required: true,
                message: "请选择供应商",
                trigger: "blur",
              },
            ],
          },
          {
            label: "供应商",
            prop: "bname",
            display: false,
            minWidth: 120,
            overHidden: true,
            span: 12,
          },
          {
            label: "供应商负责人",
            prop: "bbPic",
            type: "input",
            hide: true,
            span: 12,
            rules: [
              {
                required: true,
                message: "请输入供应商负责人",
                trigger: "blur",
              },
            ],
          },
          {
            label: "合同类型",
            prop: "type",
            type: "cascader",
            dicData: [],
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            disabled: false,
            slot: true,
            search: true,
            minWidth: 150,
            row: false,
            span: 12,
            overHidden: true,
            rules: [
              {
                required: true,
                message: "请选择合同类型",
                trigger: "blur",
              },
            ],
            control: (val) => {
              if (val.includes("1")) {
                return {
                  orderId: {
                    display: true,
                  },
                  type: {
                    row: false,
                  },
                };
              } else {
                return {
                  orderId: {
                    display: false,
                  },
                  type: {
                    row: true,
                  },
                };
              }
            },
          },
          {
            label: "采购订单",
            prop: "orderId",
            search: true,
            display: false,
            hide: true,
            showColumn: false,
            span: 12,
            rules: [
              {
                required: true,
                message: "请选择采购订单",
                trigger: "blur",
              },
            ],
          },
          {
            label: "模板合同",
            prop: "template",
            type: "radio",
            value: true,
            span: 12,
            width: 60,
            dicData: [
              {
                label: "是",
                value: true,
              },
              {
                label: "否",
                value: false,
              },
            ],
            rules: [
              {
                required: true,
                message: "请选择是否模板合同",
                trigger: "blur",
              },
            ],
          },
          {
            label: "收付类型",
            prop: "payType",
            type: "radio",
            minWidth: 70,
            hide: true,
            order: 91,
            dicData: [],
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            span: 12,
            rules: [
              {
                required: true,
                message: "请选择收付类型",
                trigger: "blur",
              },
            ],
          },
          {
            label: "合同金额",
            prop: "amount",
            minWidth: 120,
            overHidden: true,
            type: "number",
            precision: 2,
            span: 12,
            rules: [
              {
                required: true,
                message: "请输入合同金额",
                trigger: "blur",
              },
            ],
          },
          {
            label: "币种",
            prop: "currency",
            type: "select",
            hide: true,
            search: true,
            dictData: [],
            span: 12,
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            value: "RMB",
            placeholder: " ",
            rules: [
              {
                required: true,
                message: "请选择币种",
                trigger: "blur",
              },
            ],
            control: (val) => {
              if (val === "RMB") {
                return {
                  excRate: {
                    display: false,
                    value: 1,
                  },
                };
              } else {
                return {
                  excRate: {
                    display: true,
                  },
                };
              }
            },
          },
          {
            label: "汇率",
            prop: "excRate",
            labelTip:
              "汇率=本位币/原币.如本位币为人民币，原币为美元: 汇率为:0.1439.",
            type: "number",
            placeholder: " ",
            hide: true,
            display: false,
            span: 12,
            rules: [
              {
                required: true,
                message: "请输入汇率",
                trigger: "blur",
              },
            ],
          },
          {
            label: "账套",
            prop: "brand",
            placeholder: " ",
            type: "radio",
            overHidden: true,
            display: false,
            span: 12,
            width: 60,
            dicData: [],
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            search: true,
            rules: [
              {
                required: true,
                message: "请选择账套",
                trigger: "blur",
              },
            ],
          },
          {
            label: "附件",
            type: "upload",
            propsHttp: {
              res: "data",
              url: "attachId",
              name: "originalName",
            },
            action: "/api/blade-resource/oss/endpoint/put-file-attach",
            display: true,
            hide: true,
            showColumn: false,
            span: 24,
            showFileList: true,
            multiple: true,
            limit: 10,
            prop: "attachment",
          },
          {
            label: "备注",
            prop: "remark",
            minWidth: 120,
            type: "textarea",
            overHidden: true,
            span: 24,
          },
          {
            label: "付款申请金额",
            prop: "payApplyAmount",
            placeholder: " ",
            overHidden: true,
            disabled: true,
            minWidth: 82,
            display: false,
          },
          {
            label: "付款申请",
            prop: "payApplyState",
            type: "select",
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_por_order_pay_apply_state",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            dataType: "number",
            placeholder: " ",
            overHidden: true,
            disabled: true,
            minWidth: 82,
            display: false,
            search: true,
          },
          {
            label: "归档编号",
            prop: "archiveNo",
            search: false,
            display: false,
            minWidth: 110,
          },
          {
            label: "归档编号",
            prop: "archiveSerialNo",
            search: true,
            hide: true,
            display: false,
          },
          {
            label: "是否归档",
            prop: "archive",
            type: "select",
            search: true,
            display: false,
            hide: true,
            dicData: [
              {
                label: "已归档",
                value: 1,
              },
              {
                label: "待签收",
                value: 0,
              },
              {
                label: "未归档",
                value: 2,
              },
            ],
          },

          {
            label: "已签订附件",
            prop: "attachPreview",
            display: false,
            overHidden: true,
          },
        ],
      },
      data: [],
      statusDictKeyValue: {},
      typeDict: [],
      typeDictKeyValue: {},
      currencyDict: [],
      currencyDictKeyValue: {},
      brandDict: [],
      brandDictKeyValue: {},
      payTypeDict: [],
      payTypeDictKeyValue: {},
      contractId: null,
      supplierId: null,
      invoiceSelectTitle: "",
      brand: "natergy",
      exportColumn: [
        {
          label: "审核状态",
          prop: "status",
        },
        {
          label: "申请人",
          prop: "createUserName",
        },
        {
          label: "合同名称",
          prop: "name",
        },
        {
          label: "合同编号",
          prop: "serialNo",
        },
        {
          label: "供应商",
          prop: "bname",
        },
        {
          label: "供应商负责人",
          prop: "bbPic",
        },
        {
          label: "合同类型",
          prop: "type",
        },
        {
          label: "模板合同",
          prop: "template",
        },
        {
          label: "合同金额",
          prop: "amount",
        },
        {
          label: "账套",
          prop: "brand",
        },
        {
          label: "备注",
          prop: "remark",
        },
        {
          label: "归档编号",
          prop: "archiveNo",
          search: false,
          display: false,
          minWidth: 110,
        },
        {
          label: "归档编号",
          prop: "archiveSerialNo",
          search: true,
          hide: true,
          display: false,
        },
        {
          label: "是否归档",
          prop: "archive",
        },
      ],
    };
  },
  computed: {
    ...mapGetters(["permission", "userInfo"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.ni_base_contract_add, false),
        viewBtn: this.vaildData(this.permission.ni_base_contract_view, false),
        delBtn: this.vaildData(this.permission.ni_base_contract_delete, false),
        editBtn: this.vaildData(this.permission.ni_base_contract_edit, false),
        applyBtn: this.vaildData(this.permission.ni_base_contract_apply, false),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  mounted() {
    this.dictInit();
  },
  methods: {
    handleExport() {
      let msg = "是否导出当前选取的数据？";
      if (this.selectionList.length === 0) {
        msg = "是否导出全部数据？";
      }
      this.$confirm(msg, {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        let data = this.selectionList;
        if (this.selectionList.length === 0) {
          const query = { ...this.query };
          if (this.brand === "natergy") {
            query.brand = "1,2";
          } else if (this.brand === "yy") {
            query.brand = "4";
          }
          query.descs = this.page.descs;
          query.bbType = "supplier";
          const res = await getPage(1, 100000, query);
          data = res.data.data.records;
        }
        this.$Export.excel({
          title: "当前合同",
          columns: this.exportColumn,
          data: data.map((item) => {
            return {
              ...item,
              amount: item.unPayAmount ? item.unPayAmount : item.amount,
              status: this.statusDictKeyValue[item.status],
              type: this.typeDictKeyValue[item.type],
              brand: this.brandDictKeyValue[item.brand],
              template: item.template ? "是" : "否",
              archive:
                item.archive === 1
                  ? "已归档"
                  : item.archive === 0
                  ? "待签收"
                  : "未归档",
            };
          }),
        });
      });
    },
    // 附件删除
    archiveDelete(file) {
      this.$confirm("确定将选择附件删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return attachRemove(file.url);
        })
        .then(() => {
          // 重新获取附件列表
          return getAttachList({
            businessName: "ni_base_contract_archive",
            businessKey: this.archiveForm.contractId,
          });
        })
        .then((res) => {
          // 更新表单的附件数据
          const data = res.data.data;
          this.archiveForm.attachment = data.map((item) => {
            return {
              label: item.originalName,
              value: item.id,
            };
          });
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    // 附件预览
    archivePreview(file) {
      if (this.archiveForm.attachment.length > 0) {
        const attachId = this.archiveForm.attachment[Number(file.uid)].value;
        const url = "/kkf/onlinePreview?url=";
        //请求后端获取下载Url
        // const id = row.attachId;
        fileLink(attachId).then((res) => {
          window.open(url + encodeURIComponent(Base64.encode(res.data.data)));
        });
      }
    },
    archiveRowAttach(row) {
      this.$refs.archiveAttachRef.init(
        row.contractId,
        "ni_base_contract_archive"
      );
      if (row.archive) {
        this.$refs.archiveAttachRef.delBtn = false;
        this.$refs.archiveAttachRef.detail = true;
      } else {
        this.$refs.archiveAttachRef.delBtn = true;
        this.$refs.archiveAttachRef.detail = false;
      }
    },
    //上传附件
    archiveAttachDialog(row) {
      if (row.archive) {
        this.$refs.archiveAttachRef.delBtn = false;
        this.$refs.archiveAttachRef.detail = true;
      } else {
        this.$refs.archiveAttachRef.delBtn = true;
        this.$refs.archiveAttachRef.detail = false;
      }
      this.$refs.archiveAttachRef.init(row.id, "ni_base_contract_archive");
    },
    //签收按钮
    handleReceiveBtn() {
      const processInsId = this.archiveDetailForm.archiveProcessInsId;
      getTaskIdByProcessInsId(processInsId).then((res) => {
        const taskId = res.data.data;
        const pass = true;
        completeTask({
          pass,
          taskId,
          processInstanceId: processInsId,
        }).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.archiveDetailVisible = false;
        });
      });
    },
    //发起流程
    archiveSubmit(row, done, loading) {
      const processDefKey = "process_base_contract_archive";
      this.$confirm("是否发起归档申请？", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        archiveStart(row, processDefKey).then(
          () => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!",
            });
            done();
          },
          (error) => {
            loading();
            window.console.log(error);
          }
        );
        this.archiveVisible = false;
      });
      done();
    },
    //打开表单
    handleGenerateArchiveNo(row) {
      generateArchiveNo(row.id).then((res) => {
        console.log(res);
        const data = res.data.data;
        const key = Object.keys(data);
        const value = Object.values(data);
        this.archiveForm = {
          archiveNo: key[0], //归档编号
          archiveSerialNo: value[0], //归档编号
          createUserName: this.userInfo.nick_name, //发起人
          createDeptName: this.userInfo.dept_name[0], //部门
          contractUserName: row.createUserName, //合同申请人
          supplierName: row.bname, //供应商
          contractTitle: row.name, //合同名称
          contractNo: row.serialNo, //合同编号
          contractAmount: row.amount, //合同金额
          remark: "", //备注
          contractId: row.id, //合同id
          // attachment: null,
          attachment: getAttachList({
            businessName: "ni_base_contract_archive",
            businessKey: row.id,
          }).then((res) => {
            const data = res.data.data;
            this.archiveForm.attachment = data.map((item) => {
              return {
                label: item.originalName,
                value: item.id,
              };
            });
          }),
        };
        this.archiveVisible = true;
      });
    },
    archiveDetail(row) {
      this.archiveDetailForm = {
        contractId: row.id,
        archiveNo: row.archiveNo, //归档编号
        archiveSerialNo: row.archiveSerialNo,
        contractTitle: row.name, //合同名称
        contractNo: row.serialNo, //合同编号
        archiveStatus: row.archive == 0 ? "待签收" : "已归档",
        archive: row.archive,
        archiveProcessInsId: row.archiveProcessInsId,
        archiveAttach: getAttachList({
          businessName: "ni_base_contract_archive",
          businessKey: row.id,
        }).then((res) => {
          const data = res.data.data;
          this.archiveDetailForm.archiveAttach = data.map((item) => {
            return {
              label: item.originalName,
              value: item.contractId,
            };
          });
        }),
      };
      this.archiveDetailVisible = true;
    },
    handleReset() {
      this.archiveForm = {};
    },
    handleAdd(brand) {
      this.form.brand = brand;
      this.option.addTitle = `新增【${this.brandDictKeyValue[brand]}】`;
      this.$refs.crud.rowAdd();
    },
    beforeOrderSelect(done) {
      if (!this.form.brand) {
        this.$message({
          type: "warning",
          message: "请选择账套!",
        });
        return;
      }
      if (!this.form.b) {
        this.$message({
          type: "warning",
          message: "请选择供应商!",
        });
        return;
      }
      done();
    },
    handleOrderClear() {
      this.form.amount = null;
    },
    handleOrderSubmit(selectList) {
      if (selectList) {
        this.form.amount = selectList[0].amount;
      }
    },
    rowEdit(row) {
      this.$refs.contractFormDialog.onEdit(row.id);
      // this.$refs.crud.rowEdit(row, index);
    },
    rowApply(row) {
      if (!row.billType) {
        this.$message({
          type: "warning",
          message: "发票类型还未设置!",
        });
        return;
      }
      this.$confirm(
        `确定将选择数据[<span style='color: #F56C6C;font-weight: bold'>${row.serialNo}</span>]发起申请？`,
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          dangerouslyUseHTMLString: true,
          type: "warning",
        }
      )
        .then(() => {
          return apply(row.id, this.processDefKey);
        })
        .then(() => {
          row.status = 2;
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    rowPay(row) {
      this.$refs.contractPayBuildDialogRef.init(row);
    },
    rowAttach(row) {
      this.$refs.attachDialogRef.init(row.id, this.module);
    },
    rowItems(row) {
      this.$refs.payableItemRef.initByContractId(row.id);
    },
    handleSupplierSubmit() {
      this.form.bbType = "supplier";
    },
    uploadPreview(file, column, done) {
      if (String(file.url).startWith("http")) {
        done();
      } else {
        getAttachDetail(file.url).then((res) => {
          const { link } = res.data.data;
          window.open(link);
        });
      }
    },
    rowDetail(row, index) {
      if (row.processInsId) {
        this.dynamicRoute(
          {
            processInstanceId: row.processInsId,
            processDefKey: this.processDefKey,
            formKey: this.formKey,
          },
          "detail"
        );
      } else {
        this.$refs.crud.rowView(row, index);
      }
    },
    rowToVoid(row) {
      this.$confirm("此操作作废提交的数据，是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        center: true,
      })
        .then(() => {
          return toVoid(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    rowEnd(row) {
      this.$confirm("此操作将终止选中的合同，是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        center: true,
      })
        .then(() => {
          return end(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    handleLog() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择要查看的数据");
        return;
      } else if (this.selectionList.length > 1) {
        this.$message.warning("只能选择一条数据");
        return;
      }
      this.$refs.logOptDialogRef.init(this.selectionList[0].id);
    },
    handleSubmit(type) {
      this.form.status = 2;
      if (type === "add") {
        this.$refs.crud.rowSave();
      } else if (type === "edit") {
        this.$refs.crud.rowUpdate();
      }
    },
    dictInit() {
      this.$http
        .get("/api/blade-system/dict/dictionary?code=data_status")
        .then((res) => {
          const column = this.findObject(this.option.column, "status");
          column.dicData = res.data.data;
          this.statusDictKeyValue = res.data.data.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });
      this.$http
        .get(
          "/api/blade-system/dict-biz/dictionary-tree?code=ni_base_contract_type"
        )
        .then((res) => {
          const column = this.findObject(this.option.column, "type");
          column.dicData = res.data.data;
          this.typeDictKeyValue = res.data.data.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });
      this.$http
        .get("/api/blade-system/dict-biz/dictionary?code=ni_base_contract_type")
        .then((res) => {
          this.typeDict = res.data.data;
          this.typeDictKeyValue = this.typeDict.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });
      this.$http
        .get("/api/blade-system/dict/dictionary?code=currency")
        .then((res) => {
          const column = this.findObject(this.option.column, "currency");
          column.dicData = res.data.data;
          this.currencyDict = res.data.data;
          this.currencyDictKeyValue = this.currencyDict.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });
      this.$http
        .get("/api/blade-system/dict-biz/dictionary?code=ni_brand")
        .then((res) => {
          const column = this.findObject(this.option.column, "brand");
          column.dicData = res.data.data;
          this.brandDict = res.data.data;
          this.brandDictKeyValue = this.brandDict.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });
      this.$http
        .get(
          "/api/blade-system/dict-biz/dictionary?code=ni_base_contract_pay_type"
        )
        .then((res) => {
          const column = this.findObject(this.option.column, "payType");
          column.dicData = res.data.data;
          this.payTypeDict = res.data.data;
          this.payTypeDictKeyValue = this.payTypeDict.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });
    },
    rowSave(row, done, loading) {
      if (row.type && row.type instanceof Array) {
        row.type = row.type.join(",");
      }
      add(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          window.console.log(error);
        }
      );
    },
    rowUpdate(row, index, done, loading) {
      if (row.type && row.type instanceof Array) {
        row.type = row.type.join(",");
      }
      update(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          console.log(error);
        }
      );
    },
    rowAdditional(row) {
      const form = {
        parentId: row.id,
        name: row.serialNo + "的补充合同",
        type: "4", // 补充合同
        brand: row.brand,
        a: 0,
        b: row.b,
        bbPic: row.bbPic,
        bbType: "supplier",
        payType: "2",
        billType: row.billType,
        taxRate: row.taxRate,
        contractState: "2",
        aaPic: this.userInfo.user_id,
      };
      //TODO 补充合同
      if (this.userInfo.user_id !== row.createUser) {
        this.$confirm(
          `该合同由<span style="color:#F56C6C;font-weight: bold">${row.createUserName}</span>创建，是否继续？`,
          {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            dangerouslyUseHTMLString: true,
            type: "warning",
          }
        ).then(() => {
          this.dynamicRoute(
            {
              processDefKey: "process_base_contract_por",
              formKey: "wf_ex_base/Contract/Por",
              form: encodeURIComponent(
                Buffer.from(JSON.stringify(form)).toString("utf8")
              ),
            },
            "start"
          );
        });
      } else {
        this.dynamicRoute(
          {
            processDefKey: "process_base_contract_por",
            formKey: "wf_ex_base/Contract/Por",
            form: encodeURIComponent(
              Buffer.from(JSON.stringify(form)).toString("utf8")
            ),
          },
          "start"
        );
      }
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除？", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },

    handleChangePayState() {},
    handleApply() {
      const form = {
        brand: "1",
        a: 0,
        bbType: "supplier",
        payType: "2",
        contractState: "2",
        aaPic: this.userInfo.user_id,
      };
      this.dynamicRoute(
        {
          processDefKey: "process_base_contract_por",
          formKey: "wf_ex_base/Contract/Por",
          form: encodeURIComponent(
            Buffer.from(JSON.stringify(form)).toString("utf8")
          ),
        },
        "start"
      );
    },

    /**
     * 合同扫描
     */
    handleContractScan() {
      if (this.selectionList.length != 1) {
        this.$message.warning("请选择一条数据！");
        return;
      }
      console.log(this.selectionList[0].id);
      this.$refs.contractScanDialog.setId(this.selectionList[0].id);
      this.$refs.contractScanDialog.visible = true;
    },

    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据！");
        return;
      }
      this.$confirm("确定将选择数据删除？", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen(done, type) {
      if ("add" === type) {
        this.form.a = 0;
        this.form.bbType = "supplier";
      } else if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then((res) => {
          this.form = res.data.data;
        });
      }
      done();
    },
    beforeClose(done) {
      const typeColumn = this.findObject(this.option.column, "type");
      typeColumn.disabled = false;
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      const query = Object.assign(params, this.query);
      if (this.brand === "natergy") {
        query.brand = "1,2";
      } else if (this.brand === "yy") {
        query.brand = "4";
      }
      query.bbType = "supplier";
      getPage(page.currentPage, page.pageSize, query).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
    treeLoad(tree, treeNode, resolve) {
      const parentId = tree.id;
      getLazyList(parentId).then((res) => {
        resolve(res.data.data);
      });
    },
    cellStyle({ row, column }) {
      if ("status" === column.columnKey && row.status === 0) {
        return {
          backgroundColor: "#909399",
          color: "#fff",
        };
      } else if ("status" === column.columnKey && row.status === 1) {
        return {
          backgroundColor: "#409EFF",
        };
      } else if ("status" === column.columnKey && row.status === 2) {
        return {
          backgroundColor: " #E6A23C",
          color: "#fff",
        };
      } else if ("status" === column.columnKey && row.status === 3) {
        return {
          backgroundColor: " #F56C6C",
        };
      } else if ("status" === column.columnKey && row.status === 6) {
        return {
          backgroundColor: " #F56C6C",
          color: "#fff",
        };
      } else if ("status" === column.columnKey && [4, 5].includes(row.status)) {
        return {
          backgroundColor: " #E6A23C",
        };
      } else if ("status" === column.columnKey && row.status === 9) {
        return {
          backgroundColor: " #67C23A",
          color: "#fff",
        };
      } else if ("status" === column.columnKey) {
        return {
          backgroundColor: " #E6A23C",
        };
      }
      if ("parentSerialNo" === column.columnKey && row.parentSerialNo) {
        return {
          cursor: "pointer",
          textDecoration: "underline",
        };
      }
    },
    cellClick(row, column) {
      if (column.property === "parentSerialNo" && row.parentSerialNo) {
        this.query.serialNo = row.parentSerialNo;
        this.page.currentPage = 1;
        this.onLoad(this.page);
      }
    },
  },
};
</script>

<style>
.attach-icon {
  font-size: 12px;
  color: #167c46;
  text-decoration: underline;
}

.attach-icon:hover {
  cursor: pointer;
}
</style>

<template>
  <div class="avue-top">
    <div class="top-bar__left">
      <div
        class="avue-breadcrumb"
        :class="[{ 'avue-breadcrumb--active': isCollapse }]"
        v-if="showCollapse"
      >
        <i class="icon-navicon" @click="setCollapse"></i>
      </div>
    </div>
    <div class="top-bar__title">
      <div class="top-bar__item" style="float: left">
        <h2 class="avue-logo_title tenant-title">{{ title }}</h2>
      </div>
      <div
        class="top-bar__item"
        style="float: left"
        v-if="version && version.id"
      >
        <el-tag
          class="version-tag"
          type="success"
          size="mini"
          effect="dark"
          @click="handleVersionShow"
        >
          {{ version.version }}
        </el-tag>
      </div>
      <div class="top-bar__item top-bar__item--show" v-if="showMenu">
        <top-menu ref="topMenu"></top-menu>
      </div>
      <span class="top-bar__item" v-if="showSearch">
        <top-search></top-search>
      </span>
    </div>
    <div class="top-bar__right">
      <div class="top-bar__item top-bar__item--show top-bar_scan">
        <top-ai></top-ai>
      </div>
      <el-tooltip effect="dark" content="全局扫码监听已开启" placement="bottom">
        <div class="top-bar__item top-bar__item--show top-bar_scan">
          <top-scan></top-scan>
        </div>
      </el-tooltip>
      <el-tooltip
        v-if="showColor"
        effect="dark"
        :content="$t('navbar.color')"
        placement="bottom"
      >
        <div class="top-bar__item">
          <top-color></top-color>
        </div>
      </el-tooltip>
      <el-tooltip
        v-if="showDoc"
        effect="dark"
        content="使用文档"
        placement="bottom"
      >
        <div class="top-bar__item">
          <top-doc></top-doc>
        </div>
      </el-tooltip>
      <el-tooltip
        v-if="showDebug"
        effect="dark"
        :content="logsFlag ? $t('navbar.bug') : logsLen + $t('navbar.bugs')"
        placement="bottom"
      >
        <div class="top-bar__item">
          <top-logs></top-logs>
        </div>
      </el-tooltip>
      <el-tooltip
        v-if="showLock"
        effect="dark"
        :content="$t('navbar.lock')"
        placement="bottom"
      >
        <div class="top-bar__item">
          <top-lock></top-lock>
        </div>
      </el-tooltip>
      <el-tooltip
        v-if="showTheme"
        effect="dark"
        :content="$t('navbar.theme')"
        placement="bottom"
      >
        <div class="top-bar__item top-bar__item--show">
          <top-theme></top-theme>
        </div>
      </el-tooltip>
      <el-tooltip
        effect="dark"
        :content="$t('navbar.notice')"
        placement="bottom"
      >
        <div class="top-bar__item top-bar__item--show">
          <top-notice></top-notice>
        </div>
      </el-tooltip>
      <el-tooltip
        v-if="showFullScren"
        effect="dark"
        :content="
          isFullScren ? $t('navbar.screenfullF') : $t('navbar.screenfull')
        "
        placement="bottom"
      >
        <div class="top-bar__item">
          <i
            :class="isFullScren ? 'icon-tuichuquanping' : 'icon-quanping'"
            @click="handleScreen"
          ></i>
        </div>
      </el-tooltip>
      <img class="top-bar__img" :src="avatar + userInfo.avatar"/>
      <el-dropdown>
        <span class="el-dropdown-link">
          {{ userInfo.user_name }}
          <i class="el-icon-arrow-down el-icon--right"></i>
        </span>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item>
            <router-link to="/">{{ $t("navbar.dashboard") }}</router-link>
          </el-dropdown-item>
          <el-dropdown-item>
            <router-link to="/info/index"
            >{{ $t("navbar.userinfo") }}
            </router-link>
          </el-dropdown-item>
          <el-dropdown-item
            v-if="website.switchMode"
            @click.native="switchDept"
          >{{ $t("navbar.switchDept") }}
          </el-dropdown-item>
          <el-dropdown-item @click.native="logout" divided
          >{{ $t("navbar.logOut") }}
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <el-dialog
        title="用户信息选择"
        append-to-body
        :visible.sync="userBox"
        width="350px"
      >
        <avue-form
          ref="form"
          :option="userOption"
          v-model="userForm"
          @submit="submitSwitch"
        />
      </el-dialog>
    </div>
    <version-dialog ref="versionDialog"></version-dialog>
  </div>
</template>
<script>
import {resetRouter} from "@/router/router";
import {mapGetters, mapState} from "vuex";
import {fullscreenToggel, listenfullscreen} from "@/util/util";
import topLock from "./top-lock";
import topMenu from "./top-menu";
import topSearch from "./top-search";
import topTheme from "./top-theme";
import topLogs from "./top-logs";
import topColor from "./top-color";
import topNotice from "./top-notice";
import topLang from "./top-lang";
import topScan from "./top-scan";
import topAi from "./top-ai";
import website from "@/config/website";
import {getToken} from "@/util/auth";
import topDoc from "@/page/index/top/top-doc";
import {closeWebsocket} from "@/util/websocket";
import store from "@/store";
import VersionDialog from "@/page/index/top/components/VersionDialog";

const keyName = website.key + "-token";

export default {
  components: {
    topLock,
    topMenu,
    topSearch,
    topTheme,
    topLogs,
    topColor,
    topNotice,
    topLang,
    topDoc,
    topScan,
    topAi,
    VersionDialog,
  },
  name: "top",
  data() {
    return {
      avatar: `/api/blade-resource/attach/download?${
        this.website.tokenHeader
      }=${getToken()}&id=`,
      title: website.fullTitle,
      userBox: false,
      userForm: {
        deptId: "",
        roleId: "",
      },
      userOption: {
        labelWidth: 70,
        submitBtn: true,
        emptyBtn: false,
        submitText: "切换",
        column: [
          {
            label: "部门",
            prop: "deptId",
            type: "select",
            props: {
              label: "deptName",
              value: "id",
            },
            dicUrl: "/api/blade-system/dept/select",
            span: 24,
            display: false,
            rules: [
              {
                required: true,
                message: "请选择部门",
                trigger: "blur",
              },
            ],
          },
          {
            label: "角色",
            prop: "roleId",
            type: "select",
            props: {
              label: "roleName",
              value: "id",
            },
            dicUrl: "/api/blade-system/role/select",
            span: 24,
            display: false,
            rules: [
              {
                required: true,
                message: "请选择角色",
                trigger: "blur",
              },
            ],
          },
        ],
      },
      wm: null,
    };
  },
  filters: {},
  created() {
  },
  mounted() {
    listenfullscreen(this.setScreen);
    store.dispatch("GetCurrentVersion").then((version) => {
      if (version && !version.read) {
        this.handleVersionShow();
      }
    });
    this.$store.dispatch("FlowRoutes").then(() => {
    });
    // this.wm = this.watermark();
    // this.wm.remove();
    // this.wm = this.watermark({
    //   fontSize: "14px",
    //   text:
    //     this.userInfo.user_name + " " + dateFormat(new Date(), "yyyy-MM-dd"),
    // });

    //监听用户退出登录，让所有标签页同时退出
    window.addEventListener("storage", (e) => {
      if (keyName === e.key) {
        let oldValue = JSON.parse(e.oldValue);
        let newValue = JSON.parse(e.newValue);
        //旧值拥有token，变更后的值没有token了，代表退出登录了
        if (oldValue.content && !newValue.content) {
          this.sureLogout();
        }
      }
    });
  },
  computed: {
    ...mapState({
      showDebug: (state) => state.common.showDebug,
      showTheme: (state) => state.common.showTheme,
      showLock: (state) => state.common.showLock,
      showDoc: (state) => state.common.showDoc,
      showFullScren: (state) => state.common.showFullScren,
      showCollapse: (state) => state.common.showCollapse,
      showSearch: (state) => state.common.showSearch,
      showMenu: (state) => state.common.showMenu,
      showColor: (state) => state.common.showColor,
      version: (state) => state.common.version,
    }),
    ...mapGetters([
      "userInfo",
      "isFullScren",
      "tagWel",
      "tagList",
      "isCollapse",
      "tag",
      "logsLen",
      "logsFlag",
    ]),
  },
  methods: {
    handleVersionShow() {
      this.$refs.versionDialog.onShow();
    },
    getTitle() {
      const code = this.$store.getters.tenantCode;
      const map = website.tenants.reduce((acc, cur) => {
        acc[cur.value] = cur.label;
        return acc;
      }, {});
      this.title = map[code];
    },
    handleScreen() {
      fullscreenToggel();
    },
    setCollapse() {
      this.$store.commit("SET_COLLAPSE");
      console.log("收缩");
    },
    setScreen() {
      this.$store.commit("SET_FULLSCREN");
    },
    switchDept() {
      const userId = this.userInfo.user_id;
      const deptColumn = this.findObject(this.userOption.column, "deptId");
      deptColumn.dicUrl = `/api/blade-system/dept/select?userId=${userId}`;
      deptColumn.display = true;
      const roleColumn = this.findObject(this.userOption.column, "roleId");
      roleColumn.dicUrl = `/api/blade-system/role/select?userId=${userId}`;
      roleColumn.display = true;
      this.userBox = true;
    },
    submitSwitch(form, done) {
      this.$store.dispatch("refreshToken", form).then(() => {
        this.userBox = false;
        this.$router.push({path: "/"});
      });
      done();
    },
    logout() {
      this.$confirm(this.$t("logoutTip"), this.$t("tip"), {
        confirmButtonText: this.$t("submitText"),
        cancelButtonText: this.$t("cancelText"),
        type: "warning",
      }).then(() => {
        this.sureLogout();
      });
    },

    sureLogout() {
      if (this.wm) {
        this.wm.remove();
      }
      this.$store.dispatch("LogOut").then(() => {
        closeWebsocket();
        resetRouter();
        this.$router.push({path: "/login"});
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.tenant-title {
  color: #1a1a6e;
  margin: 0 10px;
}

.version-tag {
  cursor: pointer;
}
</style>

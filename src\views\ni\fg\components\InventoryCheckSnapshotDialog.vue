<script>
import {
  build as buildShapshot,
  getList,
} from "@/api/ni/fg/fgInventoryCheckSnapshot";

export default {
  name: "InventoryCheckSnapshotDialog",
  data() {
    return {
      check: {},
      visible: false,
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        menu: false,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        size: "mini",
        searchSize: "mini",
        align: "center",
        searchIndex: 3,
        searchIcon: true,
        searchEnter: true,
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        selection: true,
        dialogClickModal: false,
        column: [
          {
            label: "仓库",
            prop: "depotId",
            type: "select",
            dicUrl: "/api/ni/base/depot/info/list?status=2&type=1",
            props: {
              label: "name",
              value: "id",
              desc: "code",
            },
            search: true,
            minWidth: 100,
            overHidden: true,
          },
          {
            label: "存货编码",
            prop: "materialCode",
            placeholder: " ",
            width: 110,
            overHidden: true,
          },
          {
            label: "存货编码",
            prop: "queryMaterialCodeOrSkuId",
            placeholder: " ",
            hide: true,
            showColumn: false,
            search: true,
            overHidden: true,
          },
          {
            label: "规格",
            prop: "spec",
            type: "select",
            dicUrl: "/api/ni/product/spec/list",
            props: {
              label: "name",
              value: "code",
              desc: "code",
            },
            hide: true,
            showColumn: false,
            filterable: true,
            minWidth: 120,
            search: true,
          },
          {
            label: "规格",
            prop: "specText",
            placeholder: " ",
            display: false,
            overHidden: true,
            width: 120,
          },
          {
            label: "外包装",
            prop: "packageText",
            placeholder: " ",
            display: false,
            overHidden: true,
            width: 115,
            search: true,
          },
          {
            label: "内包装",
            prop: "innerPackageText",
            placeholder: " ",
            display: false,
            overHidden: true,
            width: 115,
            search: true,
          },
          {
            label: "质量",
            prop: "qualityLevel",
            type: "select",
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_product_sku_quality_level",
            props: {
              label: "dictValue",
              value: "dictKey",
              desc: "dictKey",
            },
            placeholder: " ",
            width: 90,
            search: true,
          },
          {
            label: "箱数",
            prop: "num",
            minWidth: 80,
            type: "number",
          },

          {
            label: "重量",
            prop: "weight",
            placeholder: " ",
            type: "number",
            minWidth: 80,
          },
          {
            label: "单位",
            prop: "unit",
            type: "select",
            filterable: true,
            width: 80,
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_material_unit",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            slot: true,
            placeholder: " ",
          },
        ],
      },
      data: [],
      form: {},
      qualityLevelColorMap: {
        A: "#67C23A", // 高吸附 - 绿色
        P: "#409EFF", // 优等品 - 蓝色
        Q: "#E6A23C", // 合格品 - 橙色
      },
      exportColumn: [
        {
          label: "存货编码",
          prop: "materialCode",
          placeholder: " ",
          width: 110,
          overHidden: true,
        },
        {
          label: "规格",
          prop: "specText",
          placeholder: " ",
          display: false,
          overHidden: true,
          width: 120,
        },
        {
          label: "外包装",
          prop: "packageText",
          placeholder: " ",
          display: false,
          overHidden: true,
          width: 115,
        },
        {
          label: "内包装",
          prop: "innerPackageText",
          placeholder: " ",
          display: false,
          overHidden: true,
          width: 115,
        },
        {
          label: "质量",
          prop: "qualityLevel",
          width: 90,
        },
        {
          label: "箱数",
          prop: "num",
          minWidth: 80,
        },
        {
          label: "盘点数量",
          prop: "actualQty",
          minWidth: 80,
        },
        {
          label: "备注",
          prop: "remark1",
          minWidth: 80,
        },
      ],
      qualityLevelDictKeyValue: {},
    };
  },
  created() {
    this.$http
      .get(
        "/api/blade-system/dict-biz/dictionary?code=ni_product_sku_quality_level"
      )
      .then((res) => {
        this.qualityLevelDictKeyValue = res.data.data.reduce((acc, cur) => {
          acc[cur.dictKey] = cur.dictValue;
          return acc;
        }, {});
      });
  },
  methods: {
    onShow(check) {
      this.check = { ...check };
      this.visible = true;
    },
    handleSnapshot() {
      this.$confirm("确定要重建库存快照?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return buildShapshot(this.check.id);
        })
        .then((res) => {
          this.check.snapshotTime = res.data.data;
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    handleClose(done) {
      this.visible = false;
      if (done && typeof done == "function") done();
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    searchReset() {
      this.query = {};
      this.page.currentPage = 1;
      this.onLoad(this.page);
    },
    cellStyle({ row, column }) {
      if ("qualityLevel" === column.columnKey) {
        const color = this.qualityLevelColorMap[row.qualityLevel];
        return {
          backgroundColor: color || "#909399", // 默认颜色为灰色
          color: "#fff",
        };
      }
    },
    onLoad(page, params = {}) {
      this.loading = true;
      const q = {
        ...params,
        ...this.query,
        ...this.params,
      };
      q.checkId = this.check.id;
      getList(page.currentPage, page.pageSize, q).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
    handleExport() {
      this.$confirm("是否导出当前筛选的所有数据？", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const q = {
          ...this.query,
          checkId: this.check.id,
        };
        const res = await getList(1, 1000000, q);
        this.$Export.excel({
          title: this.check.snapshotTime + "-库存快照",
          columns: this.exportColumn,
          data: res.data.data.records.map((item) => {
            return {
              ...item,
              qualityLevel: this.qualityLevelDictKeyValue[item.qualityLevel],
            };
          }),
        });
      });
    },
  },
};
</script>

<template>
  <el-dialog v-dialogDrag :visible.sync="visible" append-to-body width="1150px">
    <template #title>
      库存快照 {{ check.snapshotTime }}
      <el-divider direction="vertical" />
      <el-button
        type="success"
        size="mini"
        icon="el-icon-download"
        plain
        v-if="check.status !== 9"
        @click="handleExport"
        >导出
      </el-button>
      <el-button
        size="mini"
        v-if="check.status !== 4"
        type="primary"
        @click="handleSnapshot"
        >重建快照
      </el-button>
    </template>
    <avue-crud
      ref="crud"
      v-if="visible"
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      v-model="form"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionList = $event"
      @current-change="page.currentPage = $event"
      @size-change="page.pageSize = $event"
      @on-load="onLoad"
      :cell-style="cellStyle"
    >
    </avue-crud>
  </el-dialog>
</template>

<style scoped></style>

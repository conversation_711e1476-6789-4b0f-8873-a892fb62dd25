<template>
  <el-row>
    <el-col :span="5">
      <div class="box">
        <el-scrollbar>
          <basic-container>
            <avue-tree :option="treeOption" :data="treeData" @node-click="nodeClick" />
          </basic-container>
        </el-scrollbar>
      </div>
    </el-col>
    <el-col :span="19">
      <basic-container>
        <avue-crud :option="option" :search.sync="search" :table-loading="loading" :data="data" ref="crud"
          v-model="form" :page.sync="page" @search-change="searchChange" @search-reset="searchReset"
          @selection-change="selectionChange" @current-change="currentChange" @size-change="sizeChange"
          @refresh-change="refreshChange" @on-load="onLoad">
          <template #realName="{ row }">
            <i class="el-icon-male" v-if="row.sex === 1" style="color: blue"></i>
            <i class="el-icon-female" v-else-if="row.sex === 2" style="color: red"></i>
            <span>{{ row.realName }}</span>
          </template>
          <template #roleName="{ row }">
            <el-tag size="mini">{{ row.roleName }}</el-tag>
          </template>
          <template #deptName="{ row }">
            <el-tag size="mini">{{ row.deptName }}</el-tag>
          </template>
          <template #postName="{ row }">
            <el-tag v-if="row.postName" size="mini" effect="dark">{{ row.postName }}
            </el-tag>
          </template>
          <!------------------- 年龄 ------------------->
          <template #age="{ row }">
            <span v-if="row.birthday || row.idCard"> {{ getAge(row) }} 岁 </span>
            <span v-else> </span>
          </template>
          <!------------------ 离职日期 ------------------------>
          <template #leaveTime="{ row }">
            <el-tag type="info" v-if="row.leaveTime">{{ row.leaveTime.split(" ")[0] }}
            </el-tag>
            <el-tag type="success" v-else-if="row.status == 1">在职</el-tag>
          </template>
          <!-- --------------------------------------------- -->
          <template slot="menuLeft">
            <el-button type="success" size="mini" icon="el-icon-download" plain v-if="permission.user_base_info_export"
              @click="handleExport">导出
            </el-button>
            <el-button
              type="info"
              size="mini"
              plain
              v-if="permission.user_base_info_export_qr"
              icon="el-icon-download"
              @click="handleExportQr"
            >导出二维码
            </el-button>
            <el-divider direction="vertical"></el-divider>
            <el-checkbox v-model="statusOn">在职</el-checkbox>
            <el-checkbox v-model="statusOff">离职</el-checkbox>
          </template>
        </avue-crud>
      </basic-container>
    </el-col>
  </el-row>
</template>

<script>
import { permission } from "vxe-pc-ui";
import { getDeptLazyTree } from "../../api/system/dept";
import { getUser, userBaseInfo } from "../../api/system/user";
import { mapGetters } from "vuex";
import func from "@/util/func";
import NProgress from "nprogress";
import {exportBlob} from "@/api/common";
import {getToken} from "@/util/auth";

export default {
  name: "user-base-info",
  data() {
    return {
      statusOn: true,
      statusOff: false,
      form: {},
      search: {},
      selectionList: [],
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      props: {
        label: "title",
        value: "key",
      },
      treeDeptId: null,
      treeData: [],
      treeOption: {
        size: "mini",
        searchSize: "mini",
        nodeKey: "id",
        lazy: true,
        treeLoad: function (node, resolve) {
          const parentId = node.level === 0 ? 0 : node.data.id;
          getDeptLazyTree(parentId).then((res) => {
            resolve(
              res.data.data.map((item) => {
                return {
                  ...item,
                  leaf: !item.hasChildren,
                };
              })
            );
          });
        },
        addBtn: false,
        menu: false,
        props: {
          labelText: "标题",
          label: "title",
          value: "value",
          children: "children",
        },
      },
      option: {
        menu: false,
        align: "center",
        size: "mini",
        searchSize: "mini",
        height: "auto",
        calcHeight: 80,
        tip: false,
        searchIcon: true, // 搜索栏能否收缩
        searchIndex: 3, // 搜索按钮索引,超出的搜索栏收缩
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        selection: false,
        menuWidth: 150,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        dialogType: "dialog",
        dialogClickModal: false,
        column: [
          {
            label: "姓名",
            prop: "realName",
            search: true,
            searchOrder: 100,
          },
          {
            label: "性别",
            prop: "sex",
            type: "select",
            minWidth: 60,
            search: true,
            dicData: [
              {
                label: "男",
                value: 1,
              },
              {
                label: "女",
                value: 2,
              },
              {
                label: "未知",
                value: 3,
              },
            ],
            overHidden: true,
          },
          {
            label: "角色",
            prop: "roleName",
            slot: true,
          },
          {
            label: "部门",
            prop: "deptName",
            slot: true,
          },
          {
            label: "岗位",
            prop: "postName",
            slot: true,
          },
          {
            label: "主手机号",
            prop: "phone",
            minWidth: 100,
            overHidden: true,
            search: true,
          },
          {
            label: "副手机号",
            prop: "secondaryPhones",
            minWidth: 100,
            overHidden: true,
          },
          {
            label: "身份证号",
            prop: "idCard",
            minWidth: 120,
            overHidden: true,
            hide: true,
            showColumn: false,
          },
          {
            label: "入职时间",
            prop: "entryTime",
            type: "date",
            minWidth: 90,
            searchRange: true,
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            search: true,
            overHidden: true,
            display: false,
            searchOrder: 99,
          },
          {
            label: "离职时间",
            prop: "leaveTime",
            type: "date",
            minWidth: 90,
            searchRange: true,
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            search: true,
            display: false,
            searchOrder: 98,
          },
          {
            label: "年龄",
            prop: "age",
            minWidth: 120,
            overHidden: true,
          },
          // {
          //   label: "户籍",
          //   prop: "address",
          //   minWidth: 120,
          //   overHidden: true,
          // },
          {
            label: "邮箱",
            prop: "email",
            minWidth: 100,
            hide: true,
            overHidden: true,
          },
          {
            label: "车牌号",
            prop: "licensePlate",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: true,
            readonly: true,
            placeholder: " ",
            overHidden: true,
            search: true,
            searchOrder: 97,
          },
        ],
      },
      data: [],
      exportData: [], //存储导出数据
    };
  },
  computed: {
    ...mapGetters(["permission", "userInfo"]),
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  watch: {
    statusOn: {
      handler() {
        this.page.currentPage = 1;
        this.onLoad(this.page);
      },
      immediate: true,
    },
    statusOff: {
      handler() {
        this.page.currentPage = 1;
        this.onLoad(this.page);
      },
      immediate: true,
    },
  },
  methods: {
    getAge(row) {
      const today = new Date(); //获取当前日期
      let birthDate;

      if (row.birthday && row.birthday !== '') {
        birthDate = new Date(row.birthday); //将生日转换为日期
      } else if (row.idCard != null && row.idCard.length >= 18) {
        const birthYear = row.idCard.substring(6, 10);
        const birthMonth = row.idCard.substring(10, 12);
        const birthDay = row.idCard.substring(12, 14);
        birthDate = new Date(birthYear, birthMonth - 1, birthDay); //将身份证中的出生日期转换为日期
      } else {
        return ''; // 如果既没有生日也没有身份证号，则返回空字符串
      }

      let age = today.getFullYear() - birthDate.getFullYear(); // 计算年份差异
      const monthDiff = today.getMonth() - birthDate.getMonth(); // 计算月份差异
      const dayDiff = today.getDate() - birthDate.getDate(); // 计算日期差异

      // 如果当前月份和日小于生日的月份和日，则年龄减1
      if (monthDiff < 0 || (monthDiff === 0 && dayDiff < 0)) {
        age--;
      }
      return age;
    },
    handleExport() {
      this.$confirm("是否导出当前筛选的所有数据？", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.handleExportData();
      });
    },
    handleExportQr() {
      const account = func.toStr(this.query.account);
      const realName = func.toStr(this.query.realName);
      let ids = "";
      if (this.selectionList && this.selectionList.length > 0)
        ids = this.selectionList
          .map((item) => {
            return item.id;
          })
          .join(",");
      this.$confirm("是否导出二维码?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        NProgress.start();
        exportBlob(
          `/api/blade-user/export-qr?${
            this.website.tokenHeader
          }=${getToken()}&account=${account}&realName=${realName}&ids=${ids}&deptId=${this.treeDeptId}`
        ).then((res) => {
          let blob = new Blob([res.data], {type: "application/zip"});
          let url = window.URL.createObjectURL(blob);
          const link = document.createElement("a"); // 创建a标签
          link.href = url;
          link.download = "二维码.zip"; // 重命名文件
          link.click();
          URL.revokeObjectURL(url); // 释放内存
          NProgress.done();
        });
      });
    },
    //获取搜索的打印数据
    async getExportData() {
      //每次获取必须初始化一下，不然会把之前缓存额数据一并带入
      this.exportData = [];
      const query = { ...this.params, ...this.query };
      if (query.entryTime && query.entryTime.length === 2) {
        query.entryTimeStart = query.entryTime[0];
        query.entryTimeEnd = query.entryTime[1];
        query.entryTime = null;
      }
      if (query.leaveTime && query.leaveTime.length === 2) {
        query.leaveTimeStart = query.leaveTime[0];
        query.leaveTimeEnd = query.leaveTime[1];
        query.leaveTime = null;
      }
      if (this.statusOn && !this.statusOff) {
        query.status = 1; //在职
      } else if (this.statusOff && !this.statusOn) {
        query.status = 2; //离职
      } else {
        query.status = null;
      }
      await userBaseInfo(1, 100000, query, null, this.treeDeptId).then(res => {
        this.exportData = res.data.data.records;
      });
    },
    // 数据导出
    async handleExportData() {
      let opt = {
        column: [
          {
            label: "姓名",
            prop: "realName",
          },
          {
            label: "性别",
            prop: "sex",
          },
          {
            label: "角色",
            prop: "roleName",
          },
          {
            label: "部门",
            prop: "deptName",
          },
          {
            label: "岗位",
            prop: "postName",
          },
          {
            label: "主手机号",
            prop: "phone",
          },
          {
            label: "副手机号",
            prop: "secondaryPhones",
          },
          {
            label: "身份证号",
            prop: "idCard",
          },
          {
            label: "入职时间",
            prop: "entryTime",
          },
          {
            label: "离职时间",
            prop: "leaveTime",
          },
          {
            label: "年龄",
            prop: "age",
          },
          {
            label: "车牌号",
            prop: "licensePlate",
          },
          {
            label: "邮箱",
            prop: "email",
          },
        ],
      };
      await this.getExportData().then(() => {
        this.$Export.excel({
          title: "花名册",
          columns: opt.column,
          data: this.exportData.map((item) => {
            return {
              ...item,
              sex: item.sex == 1 ? "男" : item.sex == 2 ? "女" : "未知",
              age: item.birthday ? this.getAge(item) : "",
              leaveTime: item.leaveTime
                ? item.leaveTime.split(" ")[0]
                : item.status == 1
                  ? "在职"
                  : "",
            };
          }),
        });
        this.exportData = [];
      });

    },
    nodeClick(data) {
      this.treeDeptId = data.id;
      this.page.currentPage = 1;
      this.onLoad(this.page);
    },
    searchReset() {
      this.query = {};
      this.treeDeptId = null;
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    beforeOpen(done, type) {
      if (["edit", "view"].includes(type)) {
        getUser(this.form.id).then((res) => {
          this.form = res.data.data;
        });
      }
      done();
    },
    onLoad(page, params = {}) {
      this.loading = true;
      if(this.permission.idcard_show){
          const field = this.findObject(this.option.column, `idCard`);
          field.hide = false;
          field.showColumn = true;
      }
      const query = Object.assign(params, this.query);
      if (query.entryTime && query.entryTime.length === 2) {
        query.entryTimeStart = query.entryTime[0];
        query.entryTimeEnd = query.entryTime[1];
        query.entryTime = null;
      }
      if (query.leaveTime && query.leaveTime.length === 2) {
        query.leaveTimeStart = query.leaveTime[0];
        query.leaveTimeEnd = query.leaveTime[1];
        query.leaveTime = null;
      }
      if (this.statusOn && !this.statusOff) {
        query.status = 1; //在职
      } else if (this.statusOff && !this.statusOn) {
        query.status = 2; //离职
      } else {
        query.status = null;
      }
      userBaseInfo(
        page.currentPage,
        page.pageSize,
        query,
        null,
        this.treeDeptId
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
  },
};
</script>

<style scoped></style>

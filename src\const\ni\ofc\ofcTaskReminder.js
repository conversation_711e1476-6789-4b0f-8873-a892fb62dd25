export default {
  height:'auto',
  calcHeight: 30,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: false,
  viewBtn: true,
  selection: true,
  dialogClickModal: false,
  menu: true,
  menuWidth: 280,
  menuAlign: "center",


  column: [
    {
      label: "编号id",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "租户id",
      prop: "tenantId",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },

    {
      label: "更新人",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "更新时间",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "是否删除",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "状态",
      prop: "status",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "用户ID",
      prop: "createUser",
      type: "select",
      dicUrl: `/api/blade-user/user-list`,
      props: {
        label: "name",
        value: "id",
      },
      hide: true,
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      sortable: true,
      rules: [{
        required: true,
        message: "请选择用户",
        trigger: "blur"
      }],
      // 用户选择后会自动将用户ID转换为用户名和部门
      // 具体实现在ofcTaskReminder.vue中的handleUserSelectConfirm方法
    },
    {
      label: "部门ID",
      prop: "createDept",
      type: "tree",
      dicUrl: `/api/blade-system/dept/list`,
      props: {
        label: "deptName",
        value: "id",
      },
      hide: true,
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
    },
    {
      label: "待办人",
      prop: "userName",
      type: "input",
      addDisplay: true,
      editDisplay: true,
      sortable: false,
      disabled: true,
      search: true,
      searchslot: true,
    },
    {
      label: "部门",
      prop: "department",
      type: "input",
      addDisplay: true,
      editDisplay: true,
      sortable: false,
      disabled: true,
      search: true,
    },
    {
      label: "待办事项",
      prop: "toDoTask",
      type: "input",
      rules: [{
        required: true,
        message: "请输入待办事项",
        trigger: "blur"
      }],
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "datetime",
      format: "yyyy-MM-dd HH:mm:ss",
      valueFormat: "yyyy-MM-dd HH:mm:ss",
      width: 160,
      addDisplay: true,
      editDisplay: true,
      viewDisplay: true,
      hide: false,
      sortable: false,
    },
    {
      label: "截止时间",
      prop: "endTime",
      type: "datetime",
      format: "yyyy-MM-dd HH:mm:ss",
      valueFormat: "yyyy-MM-dd HH:mm:ss",
      width: 160,
      rules: [{
        required: true,
        message: "请选择截止时间",
        trigger: "blur"
      }],
    },
    {
      label: "完成情况",
      prop: "isComplated",
      type: "select",
      dicData: [
        {
          label: "未完成",
          value: 0
        },
        {
          label: "已完成",
          value: 1
        },
        {
          label: "已逾期",
          value: 3
        }
      ],
      addDisplay: false,
      editDisplay: true,
      viewDisplay: true,
      hide: false,
      search: true,
    },
  ]
}

import request from "@/router/axios";

export const getPage = (current, size, params) => {
  return request({
    url: "/api/ni/fin/costApply/page",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/ni/fin/costApply/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const remove = (ids) => {
  return request({
    url: "/api/ni/fin/costApply/remove",
    method: "post",
    params: {
      ids,
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/ni/fin/costApply/save",
    method: "post",
    data: row,
  });
};

export const update = (row) => {
  return request({
    url: "/api/ni/fin/costApply/update",
    method: "post",
    data: row,
  });
};

export const roleRelation = (params) => {
  return request({
    url: "/api/ni/fin/costApply/roleRelation",
    method: "post",
    params,
  });
};

export const getTypeByRoleId = (roleId) => {
  return request({
    url: "/api/ni/fin/costApply/getTypeByRoleId",
    method: "get",
    params: {
      roleId,
    },
  });
};

import request from "@/router/axios";

export const getPage = (current, size, params) => {
  return request({
    url: "/api/ni/ehs/extinguisher/check/page",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/ni/ehs/extinguisher/check/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const add = (row) => {
    return request({
      url: "/api/ni/ehs/extinguisher/check/add",
      method: "post",
      data: row,
    });
  };

  export const remove = (ids) => {
    return request({
      url: "/api/ni/ehs/extinguisher/check/remove",
      method: "post",
      params: {
        ids,
      },
    });
  };
  
  export const update = (row) => {
    return request({
      url: "/api/ni/ehs/extinguisher/check/update",
      method: "post",
      data: row,
    });
  };


  export const save = (row) => {
    return request({
      url: "/api/ni/ehs/extinguisher/check/save",
      method: "post",
      data: row,
    });
  }; 

  export const massCheck = (ids) => {
    return request({
      url: "/api/ni/ehs/extinguisher/check/massCheck",
      method: "post",
      params: {
        ids,
      },
    });
  };
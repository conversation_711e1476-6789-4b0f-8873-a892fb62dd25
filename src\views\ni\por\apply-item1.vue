<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :search.sync="query"
      :page.sync="page"
      v-model="form"
      ref="crud"
      @search-change="searchChange"
      @search-reset="searchReset"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @sort-change="sortChange"
      @selection-change="selectionChange"
      @on-load="onLoad"
      :cell-style="cellStyle"
      @cell-click="cellClick"
    >
      <template #attaches="{ row, index }">
        <span>{{
          !row.attachNum || Number(row.attachNum) === 0
            ? "无附件"
            : `附件[${row.attachNum}]`
        }}</span>
      </template>
      <template #inquiries="{ row }">
        <span
          >{{
            row.inquiries && row.inquiries.length > 0
              ? `比价[${row.inquiries.length}]`
              : "无比价"
          }}
        </span>
      </template>
      <template #applySerialNo="{ row, index }">
        <flow-timeline-popover
          v-if="row.processInsId"
          :process-ins-id="row.processInsId"
          :form-key="formKey"
          :process-def-key="processDefKey"
          v-model="row.applySerialNo"
          :flow.sync="row.flow"
          trigger="click"
          lazy
        />
        <span v-else>{{ row.applySerialNo }}</span>
      </template>
      <template #menuLeft>
        <el-button
          type="success"
          size="mini"
          icon="el-icon-download"
          plain
          @click="handleExport"
          >导出
        </el-button>
        <el-button
          type="warning"
          size="mini"
          icon="el-icon-edit"
          @click="handleRemark"
          >修改备注
        </el-button>
        <!--                <el-button-->
        <!--                  type="primary"-->
        <!--                  size="mini"-->
        <!--                  icon="el-icon-edit"-->
        <!--                  @click="handleInquiry"-->
        <!--                  >修改比价-->
        <!--                </el-button>-->
        <el-button
          type="warning"
          size="mini"
          icon="el-icon-edit"
          v-if="permission.porapply_item_inquiry"
          @click="handleInquiryFlow"
          >修改比价
        </el-button>
        <el-button
          type="danger"
          size="mini"
          icon="el-icon-collection-tag"
          v-if="permission.porapply_item_year"
          @click="handleYearsAgo"
          >标记年前必须购回
        </el-button>
        <el-button
          style="margin-left: 5px"
          type="info"
          icon="el-icon-user"
          size="mini"
          @click="handleDesignation"
          >指定验收人
        </el-button>
        <el-divider direction="vertical"></el-divider>
        <el-radio-group v-model="brand" size="mini" @input="onLoad(page)">
          <el-radio-button label="natergy">能特异</el-radio-button>
          <el-radio-button label="yy">演绎</el-radio-button>
        </el-radio-group>
        <el-divider direction="vertical"></el-divider>
        <el-checkbox v-model="owner" @change="handleOwnerChange"
          >自己
        </el-checkbox>
        <el-checkbox v-model="auto">自动申购</el-checkbox>
        <el-checkbox v-model="pv">压力容器</el-checkbox>
      </template>
      <template #num="{ row, index }">
        <el-badge
          v-if="row.reject && row.rejectNum && row.rejectNum < row.num"
          :value="`驳回:${Number(row.rejectNum)}`"
          class="item"
          style="margin-top: 10px; margin-right: 40px"
        >
          <span>{{
            Number(row.num).toLocaleString("zh-CN", {
              minimumFractionDigits: 2,
            })
          }}</span>
        </el-badge>
        <span v-else>{{
          Number(row.num).toLocaleString("zh-CN", {
            minimumFractionDigits: 2,
          })
        }}</span>
      </template>
      <template #price="{ row, index }">
        <span>{{
          Number(row.price).toLocaleString("zh-CN", {
            minimumFractionDigits: 2,
          })
        }}</span>
      </template>
      <template #cost="{ row, index }">
        <el-tag size="mini" type="danger" effect="dark" v-if="row.cost">
          费用
        </el-tag>
        <el-tag size="mini" type="info" effect="plain" v-else> 实物</el-tag>
      </template>
      <template #applyStatus="{ row, index }">
        <el-tooltip
          v-if="
            row.reject &&
            (!row.rejectNum || row.rejectNum === 0 || row.rejectNum >= row.num)
          "
          class="item"
          effect="dark"
          :content="'驳回原因：' + row.rejectReason"
          placement="top-start"
        >
          <el-tag size="mini" type="danger" effect="plain"> 被驳回</el-tag>
        </el-tooltip>
        <template v-else>
          <el-tag v-if="row.applyStatus === 0" size="mini" type="info">
            {{ row.$applyStatus }}
          </el-tag>
          <el-tag v-else-if="row.applyStatus === 1" size="mini" effect="plain">
            {{ row.$applyStatus }}
          </el-tag>
          <el-tag v-else-if="row.applyStatus === 2" size="mini" effect="plain">
            {{ row.$applyStatus }}
          </el-tag>
          <el-tag v-else-if="row.applyStatus === 3" size="mini" type="danger">
            {{ row.$applyStatus }}
          </el-tag>
          <el-tag
            v-else-if="row.applyStatus === 4"
            size="mini"
            type="warning"
            effect="plain"
          >
            {{ row.$applyStatus }}
          </el-tag>
          <el-tag
            v-else-if="row.applyStatus === 5"
            size="mini"
            type="warning"
            effect="plain"
          >
            {{ row.$applyStatus }}
          </el-tag>
          <el-tag
            v-else-if="row.applyStatus === 6"
            size="mini"
            type="danger"
            effect="plain"
          >
            {{ row.$applyStatus }}
          </el-tag>
          <el-tag
            v-else-if="row.applyStatus === 9"
            size="mini"
            type="success"
            effect="plain"
          >
            {{ row.$applyStatus }}
          </el-tag>
          <el-tag v-else size="mini" type="warning" effect="dark">
            {{ row.$applyStatus }}
          </el-tag>
        </template>
      </template>
      <template #amount="{ row }">
        <span>{{
          Number(row.amount).toLocaleString("zh-CN", {
            minimumFractionDigits: 2,
          })
        }}</span>
      </template>
      <template #crash="{ row }">
        <el-tag v-if="row.crash" size="mini" type="danger" effect="dark">
          是
        </el-tag>
        <el-tag v-else size="mini" type="info">否</el-tag>
      </template>
    </avue-crud>
    <attach-dialog ref="attachRef" code="public" :delBtn="false" />
    <el-dialog
      title="比价"
      append-to-body
      :visible.sync="inquiry.visible"
      width="1000px"
    >
      <div slot="title" class="clearfix">
        <span>比价</span>
        <el-divider direction="vertical"></el-divider>
        <el-button
          type="warning"
          size="mini"
          icon="el-icon-print"
          @click="handleInquiryPrint"
        >
          打印
        </el-button>
      </div>
      <avue-form
        v-if="inquiry.visible"
        :option="inquiry.option"
        v-model="inquiry.form"
        @submit="handleInquirySubmit"
      >
      </avue-form>
    </el-dialog>
    <el-dialog
      title=""
      append-to-body
      :visible.sync="yearsAgo.visible"
      width="300px"
    >
      <div slot="title" class="clearfix"></div>
      <avue-form
        v-if="yearsAgo.visible"
        :option="yearsAgo.option"
        v-model="yearsAgo.form"
        @submit="handleYearsAgoSubmit"
        @reset-change="() => (yearsAgo.visible = false)"
      >
      </avue-form>
    </el-dialog>
    <el-dialog
      :title="order.title"
      append-to-body
      :visible.sync="order.visible"
      width="500"
    >
      <avue-crud :option="order.option" :data="order.data"></avue-crud>
    </el-dialog>
    <el-dialog
      :title="arrival.title"
      append-to-body
      :visible.sync="arrival.visible"
      width="500"
    >
      <avue-crud :option="arrival.option" :data="arrival.data"></avue-crud>
    </el-dialog>
    <el-dialog
      title="指定验收人"
      append-to-body
      :visible.sync="designation.visible"
      width="400px"
      center
    >
      <avue-form
        :option="designation.option"
        v-model="designation.form"
        @submit="handleDesignationSubmit"
      ></avue-form>
    </el-dialog>
  </basic-container>
</template>

<script>
import {
  changeDesignationUserId,
  changeInquiry,
  changePv,
  changeRemark,
  changeYearsAgo,
  getDepotLocation,
  getList,
  getPage1,
  getPaymentStatus,
} from "@/api/ni/por/apply-item";
import exForm from "@/views/plugin/workflow/mixins/ex-form";
import { getItemListByApplyItemId } from "@/api/ni/por/order-arrival";
import AttachDialog from "@/components/attach-dialog";
import FlowTimelinePopover from "@/components/wf-flow-timeline/popover";
import { mapGetters } from "vuex";
import { listByApplyItemId } from "@/api/ni/por/order-item";
import { loadPrintTemplate } from "@/api/system/printTemplate";
import { hiprint } from "vue-plugin-hiprint";
import {getUser} from "@/api/system/user";

export default {
  mixins: [exForm],
  name: "PorApplyItem",
  components: {
    AttachDialog,
    FlowTimelinePopover,
  },
  beforeRouteEnter(to, from, next) {
    to.meta.keepAlive = true;
    next();
  },
  data() {
    return {
      module: "ni_por_apply",
      processDefKey: "process_por_apply",
      formKey: "wf_ex_por/Apply",
      rejectProcessDefKey: "process_por_apply_reject",
      rejectFormKey: "wf_ex_por/ApplyReject",
      inquiryProcessDefKey: "process_ni_por_apply_inquiry",
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        searchEnter: true,
        index: false,
        align: "center",
        searchLabelWidth: 110,
        searchIndex: 3,
        searchIcon: true,
        addBtn: false,
        menu: false,
        size: "mini",
        searchSize: "mini",
        height: "auto",
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        viewBtn: true,
        selection: true,
        dialogClickModal: false,
        column: [
          {
            label: "序号",
            prop: "row",
            width: 55,
            search: true,
          },
          {
            label: "状态",
            prop: "applyStatus",
            dicData: [],
            search: true,
            minWidth: 80,
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            dataType: "number",
            display: false,
            type: "select",
          },
          {
            label: "品名",
            prop: "materialName",
            minWidth: 90,
            overHidden: true,
            search: true,
            searchOrder: 99,
          },
          {
            label: "规格型号",
            overHidden: true,
            prop: "specification",
            minWidth: 90,
            search: true,
            searchOrder: 98,
          },
          {
            label: "材质",
            prop: "quality",
            minWidth: 90,
            overHidden: true,
            search: true,
          },
          {
            label: "数量",
            prop: "num",
            type: "number",
            placeholder: " ",
            minWidth: 80,
            overHidden: true,
          },
          {
            label: "单位",
            prop: "unit",
            overHidden: true,
            type: "select",
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_material_unit",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            width: 55,
            slot: true,
          },
          {
            label: "单价",
            prop: "price",
            type: "number",
            disabled: true,
            minWidth: 100,
            controls: false,
            precision: 2,
            placeholder: " ",
            overHidden: true,
          },
          {
            label: "备注",
            prop: "remark",
            minWidth: 110,
            overHidden: true,
            search: true,
          },
          {
            label: "附件",
            prop: "attaches",
            minWidth: 60,
            overHidden: true,
          },
          {
            label: "工作表",
            prop: "inquiries",
            width: 60,
          },
          {
            label: "申请日期",
            prop: "createTime",
            type: "date",
            minWidth: 85,
            overHidden: true,
            display: false,
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            search: true,
            searchRange: true,
          },
          {
            label: "用途",
            prop: "purpose",
            type: "textarea",
            minRows: 1,
            placeholder: " ",
            overHidden: true,
            minWidth: 90,
            search: true,
          },
          {
            label: "编码",
            prop: "materialCode",
            minWidth: 120,
            overHidden: true,
            search: true,
          },

          {
            label: "申请人",
            prop: "createUserName",
            addDisplay: false,
            editDisplay: false,
            overHidden: true,
            minWidth: 60,
            search: true,
          },
          {
            label: "申请部门",
            prop: "createDeptName",
            addDisplay: false,
            editDisplay: false,
            overHidden: true,
            minWidth: 80,
          },
          {
            label: "申请部门",
            prop: "createDept",
            type: "tree",
            dicUrl: `/api/blade-system/dept/list`,
            props: { label: "deptName", value: "id" },
            search: true,
            viewDisplay: true,
            hide: true,
            showColumn: false,
            addDisplay: false,
            editDisplay: false,
            overHidden: true,
            minWidth: 80,
          },
          {
            label: "收货地",
            prop: "receivingAddress",
            type: "radio",
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_por_receiving_address",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            clearable: false,
            width: 90,
            overHidden: true,
          },
          {
            label: "国标",
            overHidden: true,
            prop: "gb",
            hide: true,
            minWidth: 80,
          },
          {
            label: "金额",
            prop: "amount",
            sortable: true,
            overHidden: true,
            controls: false,
            type: "number",
            minWidth: 90,
            precision: 2,
            placeholder: " ",
          },
          {
            label: "压力容器",
            prop: "pv",
            type: "radio",
            width: 69,
            dicData: [
              {
                label: "是",
                value: 1,
              },
              {
                label: "否",
                value: 0,
              },
            ],
            display: false,
          },
          {
            label: "申请编号",
            prop: "applySerialNo",
            search: true,
            overHidden: true,
            minWidth: 116,
          },
          {
            label: "采购申请",
            prop: "applyTitle",
            overHidden: true,
            minWidth: 110,
          },
          {
            label: "采购类型",
            prop: "type",
            type: "select",
            minWidth: 80,
            dicUrl: "/api/blade-system/dict-biz/dictionary?code=ni_por_type",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            overHidden: true,
            search: true,
          },
          {
            label: "需用日期",
            prop: "needDate",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            overHidden: true,
            width: 84,
            type: "date",
          },
          {
            label: "申请备注",
            prop: "applyRemark",
            overHidden: true,
            minWidth: 100,
            search: true,
          },

          {
            label: "预算编号",
            prop: "budgetSerialNo",
            search: true,
            overHidden: true,
            minWidth: 100,
          },
          {
            label: "项目编号",
            prop: "projectSerialNo",
            search: true,
            minWidth: 100,
            overHidden: true,
          },
          {
            label: "账套",
            prop: "brand",
            type: "radio",
            minWidth: 70,
            value: "1",
            dicData: [],
            disabled: true,
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            search: true,
          },
          {
            label: "类型",
            prop: "cost",
            type: "select",
            dicData: [
              {
                label: "费用",
                value: true,
              },
              {
                label: "实物",
                value: false,
              },
            ],
            search: true,
            placeholder: " ",
            width: 60,
            disabled: true,
          },
          {
            label: "紧急申购",
            prop: "crash",
            type: "radio",
            value: false,
            hide: true,
            minWidth: 70,
            dicData: [
              {
                label: "是",
                value: true,
              },
              {
                label: "否",
                value: false,
              },
            ],
            search: true,
          },
          {
            label: "验收人",
            prop: "designationInspectionUserName",
            width: 80,
            overHidden: true,
          },
          {
            label: "采购人",
            prop: "purchaseUserName",
            minWidth: 70,
            overHidden: true,
            fixed: "right",
          },
          {
            label: "订货数",
            prop: "purchaseNum",
            minWidth: 70,
            placeholder: " ",
            overHidden: true,
            fixed: "right",
          },
          {
            label: "到货数",
            prop: "arrivalNum",
            placeholder: " ",
            minWidth: 70,
            overHidden: true,
            fixed: "right",
          },
          {
            label: "付款状态",
            prop: "paymentStatus",
            type: "select",
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_fin_payment_status",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            dataType: "number",
            minWidth: 70,
            placeholder: " ",
            overHidden: true,
            fixed: "right",
          },
          {
            label: "仓库位置",
            prop: "depotLocation",
            minWidth: 70,
            overHidden: true,
            fixed: "right",
          },
          {
            label: "采购驳回",
            prop: "reject",
            type: "radio",
            minWidth: 70,
            hide: true,
            dicData: [
              {
                label: "是",
                value: true,
              },
              {
                label: "否",
                value: false,
              },
            ],
            search: true,
          },
        ],
      },
      data: [],
      statusDict: [],
      statusDictKeyValue: {},
      brandDict: [],
      brandDictKeyValue: {},
      auto: false,
      pv: false,
      owner: false,
      dataType: "all",
      brand: "natergy",
      exportData: [], //存储导出数据
      typeDictKeyValue: {}, //采购类型字典转化
      unitDictKeyValue: {}, //单位类型字典转化
      arrival: {
        title: "到货明细",
        visible: false,
        option: {
          showSummary: true,
          sumColumnList: [
            {
              name: "arrivalNum",
              type: "sum",
              decimals: 1,
            },
            {
              name: "qualifiedNum",
              type: "sum",
            },
          ],
          header: false,
          menu: false,
          addBtn: false,
          size: "mini",
          search: false,
          column: [
            {
              label: "暂存位置",
              prop: "depotLocation",
              overHidden: true,
              minWidth: 80,
            },
            {
              label: "到货日期",
              prop: "arrivalDate",
              minWidth: 85,
            },
            {
              label: "合格数",
              prop: "qualifiedNum",
              minWidth: 70,
            },
            {
              label: "到货数",
              prop: "arrivalNum",
              minWidth: 70,
            },
            {
              label: "验收人员",
              prop: "inspectionUserName",
              minWidth: 70,
              overHidden: true,
            },
            {
              label: "验收时间",
              prop: "inspectionDate",
              minWidth: 85,
            },
            {
              label: "验收结果",
              prop: "qualified",
              type: "select",
              fixed: "right",
              dicUrl:
                "/api/blade-system/dict-biz/dictionary?code=ni_por_order_arrival_inspection_state",
              placeholder: " ",
              props: {
                label: "dictValue",
                value: "dictKey",
              },
              minWidth: 70,
            },
            {
              label: "验收情况",
              prop: "inspectionRemark",
              minWidth: 70,
              overHidden: true,
            },
            {
              label: "登记人",
              prop: "createUserName",
              minWidth: 70,
              overHidden: true,
            },
            {
              label: "流水号",
              prop: "serialNo",
              minWidth: 80,
              overHidden: true,
            },
            {
              label: "入库状态",
              prop: "stockState",
              fixed: "right",
              type: "select",
              dicData: [
                {
                  label: "未入库",
                  value: "1",
                },
                {
                  label: "部分入库",
                  value: "5",
                },
                {
                  label: "已入库",
                  value: "3",
                },
                {
                  label: "无需入库",
                  value: "4",
                },
              ],
              minWidth: 90,
              slot: true,
              display: false,
              filters: true,
            },
          ],
        },
        data: [],
      },
      export1: {
        column: [
          {
            label: "序号",
            prop: "row",
          },
          {
            label: "状态",
            prop: "applyStatus",
          },
          {
            label: "品名",
            prop: "materialName",
          },
          {
            label: "编码",
            prop: "materialCode",
          },
          {
            label: "规格型号",
            prop: "specification",
          },
          {
            label: "材质",
            prop: "quality",
          },
          {
            label: "数量",
            prop: "num",
          },
          {
            label: "单位",
            prop: "unit",
          },
          {
            label: "单价",
            prop: "price",
          },
          {
            label: "申请时间",
            prop: "createTime",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
          },
          {
            label: "备注",
            prop: "remark",
          },
          {
            label: "到货数",
            prop: "arrivalNum",
          },
          {
            label: "入库数",
            prop: "stockNum",
          },
          {
            label: "退货数",
            prop: "backNum",
          },
          {
            label: "申请人",
            prop: "createUserName",
          },
          {
            label: "国标",
            prop: "gb",
          },
          {
            label: "申请金额",
            prop: "amount",
          },
          {
            label: "申请编号",
            prop: "applySerialNo",
          },
          {
            label: "采购申请",
            prop: "applyTitle",
          },
          {
            label: "采购类型",
            prop: "type",
          },
          {
            label: "需用日期",
            prop: "needDate",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
          },
          {
            label: "类型",
            prop: "cost",
          },
          {
            label: "预算名称",
            prop: "budgetTitle",
          },
          {
            label: "预算编号",
            prop: "budgetSerialNo",
          },
          {
            label: "项目名称",
            prop: "projectTitle",
          },
          {
            label: "项目编号",
            prop: "projectSerialNo",
          },
          {
            label: "账套",
            prop: "brand",
          },
          {
            label: "紧急申购",
            prop: "crash",
          },
          {
            label: "采购人",
            prop: "purchaseUserName",
          },
          {
            label: "暂存位置",
            prop: "depotLocation",
          },
        ],
      },
      inquiry: {
        visible: false,
        option: {
          submitText: "修改",
          detail: false,
          size: "mini",
          emptyBtn: false,
          labelWidth: 120,
          column: [
            {
              label: "采购原因",
              prop: "reason",
              type: "textarea",
              minRows: 1,
              placeholder: " ",
              rules: [
                {
                  required: true,
                  message: "请输入采购原因",
                  trigger: "blur",
                },
              ],
            },
            {
              label: "采购风险",
              prop: "risk",
              type: "textarea",
              minRows: 1,
              placeholder: " ",
              rules: [
                {
                  required: true,
                  message: "请输入采购风险",
                  trigger: "blur",
                },
              ],
            },
            {
              label: "采购带来的收益及效果",
              prop: "profit",
              span: 24,
              type: "textarea",
              minRows: 2,
              placeholder: " ",
              rules: [
                {
                  required: true,
                  message: "请输入采购带来的收益及效果",
                  trigger: "blur",
                },
              ],
            },
            {
              label: "项目安全性分析及风险控制",
              prop: "security",
              span: 24,
              minRows: 2,
              type: "textarea",
              placeholder: " ",
              rules: [
                {
                  required: true,
                  message: "请输入项目安全性分析及风险控制",
                  trigger: "blur",
                },
              ],
            },
            {
              label: "项目环保因素分析及风险控制",
              prop: "ep",
              span: 24,
              minRows: 2,
              type: "textarea",
              placeholder: " ",
              rules: [
                {
                  required: true,
                  message: "请输入项目环保因素分析及风险控制",
                  trigger: "blur",
                },
              ],
            },
            {
              label: "验收标准",
              prop: "ac",
              span: 12,
              minRows: 1,
              type: "textarea",
              placeholder: " ",
            },
            {
              label: "技术参数",
              prop: "technology",
              span: 12,
              minRows: 1,
              type: "textarea",
              placeholder: " ",
            },
            {
              label: "供应商比价",
              prop: "inquiries",
              span: 24,
              type: "dynamic",
              children: {
                span: 8,
                addBtn: false,
                delBtn: false,
                align: "center",
                headerAlign: "center",
                size: "mini",
                column: [
                  {
                    label: "供应商名称",
                    prop: "supplier",
                    placeholder: " ",
                    type: "textarea",
                    overHidden: true,
                    minRows: 1,
                    rules: [
                      {
                        required: true,
                        message: "请填写供应商名称",
                        trigger: "blur",
                      },
                    ],
                  },
                  {
                    label: "联系方式",
                    prop: "supplierLinkman",
                    placeholder: " ",
                    type: "textarea",
                    overHidden: true,
                    minRows: 1,
                    rules: [
                      {
                        required: true,
                        message: "请填写供应商联系人",
                        trigger: "blur",
                      },
                    ],
                  },

                  {
                    label: "比价详情",
                    prop: "remark",
                    type: "textarea",
                    minRows: 1,
                    placeholder: " ",
                    overHidden: true,
                    rules: [
                      {
                        required: true,
                        message: "请填写比价详情",
                        trigger: "blur",
                      },
                    ],
                  },
                  {
                    label: "建议供应商",
                    prop: "recommend",
                    type: "radio",
                    value: false,
                    dicData: [
                      {
                        label: "是",
                        value: true,
                      },
                      {
                        label: "否",
                        value: false,
                      },
                    ],
                    placeholder: " ",
                    rules: [
                      {
                        required: true,
                        message: "请选择建议供应商",
                        trigger: "blur",
                      },
                    ],
                    change: ({ value, index }) => {
                      if (value)
                        this.inquiry.form.inquiries.forEach((item, i) => {
                          if (i === index) {
                            return;
                          }
                          item.recommend = false;
                        });
                    },
                  },
                ],
              },
            },
            {
              label: "部门主管意见",
              prop: "comments1",
              minRows: 1,
              type: "textarea",
              placeholder: " ",
              disabled: true,
              span: 12,
            },
            {
              label: "工艺组意见",
              prop: "comments2",
              minRows: 1,
              type: "textarea",
              placeholder: " ",
              disabled: true,
              span: 12,
            },
            {
              label: "总经理意见",
              prop: "comments3",
              minRows: 1,
              type: "textarea",
              placeholder: " ",
              disabled: true,
              span: 12,
            },
            {
              label: "总经办意见",
              prop: "comments4",
              minRows: 1,
              type: "textarea",
              placeholder: " ",
              disabled: true,
              span: 12,
            },
          ],
        },
        form: {},
        historyShow: false,
        printTemplate: null,
      },
      order: {
        title: "订单明细",
        visible: false,
        option: {
          showSummary: true,
          sumColumnList: [
            {
              name: "num",
              type: "sum",
              decimals: 1,
            },
            {
              name: "amount",
              type: "sum",
            },
          ],
          header: false,
          menu: false,
          addBtn: false,
          size: "mini",
          search: false,
          column: [
            {
              label: "订单编号",
              prop: "serialNo",
              overHidden: true,
              minWidth: 80,
            },
            {
              label: "采购日期",
              prop: "purchaseTime",
              format: "yyyy-MM-dd",
              valueFormat: "yyyy-MM-dd",
              overHidden: true,
              minWidth: 80,
            },
            {
              label: "供应商",
              prop: "supplier",
              minWidth: 85,
              overHidden: true,
            },
            {
              label: "数量",
              prop: "num",
              minWidth: 70,
            },
            {
              label: "单价",
              prop: "price",
              minWidth: 70,
            },
            {
              label: "金额",
              prop: "amount",
              minWidth: 70,
              overHidden: true,
            },
            {
              label: "备注",
              prop: "remark",
              minWidth: 80,
              overHidden: true,
            },
            {
              label: "采购人",
              prop: "purchaseUserName",
              minWidth: 85,
            },
          ],
        },
        data: [],
      },
      yearsAgo: {
        visible: false,
        option: {
          labelWidth: 120,
          submitText: "确认",
          emptyText: "取消",
          detail: false,
          size: "mini",
          span: 24,
          column: [
            {
              label: "年前必须购回",
              prop: "yearsAgo",
              type: "switch",
              dicData: [
                {
                  label: "取消",
                  value: 0,
                },
                {
                  label: "标记",
                  value: 1,
                },
              ],
              placeholder: " ",
              rules: [
                {
                  required: true,
                  message: "请选择标记还是取消",
                  trigger: "blur",
                },
              ],
            },
          ],
        },
        form: {},
      },
      designation: {
        visible: false,
        option: {
          span: 24,
          size: "mini",
          emptyBtn: false,
          column: [
            {
              label: "验收人",
              type: "select",
              prop: "designationInspectionUserId",
              remote: true,
              dicUrl: `/api/blade-user/search/otj/user?name={{key}}`,
              props: {
                label: "realName",
                value: "id",
              },
              dicFormatter: (data) => {
                return data.data.records;
              },
              placeholder: " ",
              filterable: true,
              change: ({value}) => {
                if (value){
                  getUser(value).then((res) => {
                    this.designation.form.designationInspectionUserName = res.data.data.realName;
                  });
                }
              },
              rules: [
                {
                  required: true,
                  message: "请选择指定验收人",
                  trigger: "blur",
                },
              ],
            },
          ],
        },
        form: {},
      },
    };
  },
  created() {
    this.dictInit();
    loadPrintTemplate("ni_por_apply").then((res) => {
      this.inquiry.printTemplate = JSON.parse(res.data.data.content);
    });
  },
  computed: {
    ...mapGetters(["userInfo", "colorName"]),
    ids() {
      let ids = new Set();
      this.selectionList.forEach((ele) => {
        ids.add(ele.id);
      });
      return Array.from(ids).join(",");
    },
  },
  watch: {
    pv: {
      handler(val) {
        if (val) {
          this.query.pv = 1;
        } else {
          this.query.pv = null;
        }
        this.page.currentPage = 1;
        this.onLoad(this.page);
      },
      immediate: false,
    },
    auto: {
      handler(val) {
        if (val) {
          this.query.auto = 1;
        } else {
          this.query.auto = null;
        }
        this.page.currentPage = 1;
        this.onLoad(this.page);
      },
      immediate: false,
    },
  },
  methods: {
    handleInquiryPrint() {
      if (!this.inquiry.printTemplate) {
        this.$message.error("打印模板加载失败，请联系管理员");
        return;
      }
      const hiprintTemplate = new hiprint.PrintTemplate({
        template: this.inquiry.printTemplate,
      });
      const printData = this.inquiry.form;
      let printDataList = [];
      const item = this.inquiry.form;
      const items = [
        {
          ...item,
          cost: item.cost ? "费用" : "实物",
          unit: this.unitDictKeyValue[item.unit],
          remark:
            item.remark && item.remark.length > 50
              ? item.remark.substr(0, 50) + "..."
              : item.remark,
        },
      ];
      const data = {
        ...printData,
        type: this.typeDictKeyValue[printData.type],
        amountType: printData.amount < 100000 ? "十万元以下" : "十万元以上",
        security: item.security,
        environment: item.ep ? item.ep : "",
        reason: item.reason ? item.reason : "",
        profit: item.profit ? item.profit : "",
        risk: item.risk ? item.risk : "",
        ZGSuggestion: item.comments1 ? item.comments1 : "",
        GYSuggestion: item.comments2 ? item.comments2 : "",
        MaSuggestion: item.comments3 ? item.comments3 : "",
        ZJBSuggestion: item.comments4 ? item.comments4 : "",
        items,
      };
      item.inquiries.forEach((inquiry, index) => {
        data[`inquiries${index + 1}`] =
          inquiry.supplier +
          "<br>" +
          inquiry.supplierLinkman +
          "<br>" +
          inquiry.remark;
        if (inquiry.recommend) {
          data.recommend +=
            inquiry.supplier + "(" + inquiry.supplierLinkman + ")" + "<br>";
        }
      });
      printDataList.push(data);
      hiprintTemplate.print(printDataList);
    },
    handleOwnerChange(val) {
      this.query.createUser = val ? this.userInfo.user_id : null;
      this.page.currentPage = 1;
      this.onLoad(this.page, this.query);
    },
    handleDesignation() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请至少选择一条数据");
        return;
      }
      const unOwner = this.selectionList.some(
        (row) => row.createUser !== this.userInfo.user_id
      );
      if (unOwner) {
        this.$message.warning("只能修改本人的申请");
        return;
      }
      this.designation.visible = true;
    },
    handleDesignationSubmit(form, done) {
      changeDesignationUserId(this.ids,form.designationInspectionUserId).then((res)=>{
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        this.designation.visible = false;
        this.onLoad(this.page);
      }).finally(()=>done())
    },
    handleYearsAgo() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择要标记的数据");
        return;
      }
      const notOwner = this.selectionList.some(
        (item) =>
          item.createUser !== this.userInfo.user_id &&
          !this.userInfo.role_name.includes("admin")
      );
      if (notOwner) {
        this.$message.warning("请选择自己申购的数据");
        return;
      }
      this.yearsAgo.form.yearsAgo = 1;
      this.yearsAgo.form.ids = this.ids;
      this.yearsAgo.visible = true;
    },
    handleYearsAgoSubmit(form, done) {
      changeYearsAgo(form.ids, form.yearsAgo)
        .then(() => {
          this.yearsAgo.visible = false;
          const ids = this.selectionList.map((item) => item.id);
          this.data.forEach((item) => {
            if (ids.includes(item.id)) {
              item.yearsAgo = form.yearsAgo;
            }
          });
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        })
        .finally(() => {
          done();
        });
    },
    rowPvChange(pv, row, index) {
      changePv(row.id, pv).then(() => {
        this.data[index].pv = pv;
        this.$message({
          type: "success",
          message: "操作成功!",
        });
      });
    },
    rowInquiry(row, index) {
      Object.keys(this.inquiry.form).forEach(
        (key) => (this.inquiry.form[key] = "")
      );
      this.inquiry.form = {
        ...row,
        index: [index],
      };
      this.inquiry.option.detail = true;
      this.inquiry.visible = true;
    },
    handleInquirySubmit(form, done) {
      if (!form.inquiries || form.inquiries.length === 0) {
        this.$message({ type: "warning", message: "请添加比价明细" });
        done();
        return;
      }
      const recommend = form.inquiries.some((item) => item.recommend);
      if (!recommend) {
        this.$message({ type: "warning", message: "请选择建议供应商" });
        done();
        return;
      }
      changeInquiry(form)
        .then(() => {
          this.onLoad(this.page);
          this.inquiry.visible = false;
          this.$message({
            type: "success",
            message: "操作成功",
          });
        })
        .finally(() => {
          done();
        });
    },
    handleAutoChange() {
      this.page.currentPage = 1;
      this.onLoad(this.page);
    },
    showArrivalItem(row) {
      getItemListByApplyItemId(row.id).then((res) => {
        const { data } = res.data;
        if (data && data.length > 0) {
          this.arrival.data = data;
          this.arrival.title = `[${row.row}]${row.materialName}-到货明细`;
          this.arrival.visible = true;
        }
      });
    },
    showOrderItem(row) {
      listByApplyItemId(row.id).then((res) => {
        const { data } = res.data;
        if (data && data.length > 0) {
          this.order.data = data;
          this.order.title = `[${row.row}]${row.materialName}-订单明细`;
          this.order.visible = true;
        }
      });
    },
    rowDetail(row, index) {
      if (row.processInsId) {
        this.dynamicRoute(
          {
            processInstanceId: row.processInsId,
            formKey: row.status === 1 ? this.formKey : this.rejectFormKey,
            processDefKey:
              row.status === 1 ? this.processDefKey : this.rejectProcessDefKey,
          },
          "detail"
        );
      } else {
        this.$refs.crud.rowView(row, index);
      }
    },
    handleDataTypeChange() {
      this.onLoad(this.page);
    },
    dictInit() {
      this.$http
        .get("/api/blade-system/dict/dictionary?code=data_status")
        .then((res) => {
          const column = this.findObject(this.option.column, "applyStatus");
          column.dicData = res.data.data;
          this.statusDict = res.data.data;
          this.statusDictKeyValue = this.statusDict.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });
      this.$http
        .get("/api/blade-system/dict-biz/dictionary?code=ni_brand")
        .then((res) => {
          const column = this.findObject(this.option.column, "brand");
          column.dicData = res.data.data;
          this.brandDict = res.data.data;
          this.brandDictKeyValue = this.brandDict.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });
      //单位
      this.$http
        .get("/api/blade-system/dict-biz/dictionary?code=ni_material_unit")
        .then((res) => {
          const unitDict = res.data.data;
          this.unitDictKeyValue = unitDict.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });
      //采购类型数据转化
      this.$http
        .get("/api/blade-system/dict-biz/dictionary?code=ni_por_type")
        .then((res) => {
          const typeDict = res.data.data;
          this.typeDictKeyValue = typeDict.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });
    },
    searchReset() {
      this.query = {};
      this.owner = false;
      this.pv = null;
      this.auto = null;
      this.page.currentPage = 1;
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.query.createUser = this.owner ? this.userInfo.user_id : null;
      this.query.pv = this.pv ? 1 : null;
      this.query.auto = this.auto ? 1 : null;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    sortChange(val) {
      const { prop, order } = val;
      if (prop === "amount") {
        this.data.sort((a, b) => {
          const numA =
            typeof a.amount === "string"
              ? parseFloat(a.amount.replace(/[^\d.-]/g, ""))
              : a.amount;
          const numB =
            typeof b.amount === "string"
              ? parseFloat(b.amount.replace(/[^\d.-]/g, ""))
              : b.amount;
          return order === "ascending"
            ? (numA || 0) - (numB || 0)
            : (numB || 0) - (numA || 0);
        });
      }
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    onLoad(page, params = {}) {
      this.loading = true;
      if (this.dataType === "all") {
        this.query.unArrival = null;
        this.query.unStock = null;
      } else if (this.dataType === "unArrival") {
        this.query.unArrival = true;
        this.query.unStock = null;
      } else if (this.dataType === "unStock") {
        this.query.unStock = true;
        this.query.unArrival = null;
      }
      if (this.brand === "natergy") {
        this.query.brand = "1,2";
      } else if (this.brand === "yy") {
        this.query.brand = "4";
      }
      const query = {
        ...params,
        ...this.query,
        desc: "id",
      };
      if (query.createTime != null) {
        query.startCreateTime = query.createTime[0];
        query.endCreateTime = query.createTime[1];
        query.createTime = null;
      }
      getPage1(page.currentPage, page.pageSize, query).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records.map((item) => ({
          ...item,
          amount: Number(item.amount || 0),
          depotLocation: "",
          paymentStatus: -1,
        }));
        if (this.data.length > 0) {
          const ids = this.data.map((item) => item.id).join(",");
          this.loadDepotLocation(ids);
          this.loadPaymentStatus(ids);
        }
        this.loading = false;
        this.selectionClear();
      });
    },
    loadPaymentStatus(ids) {
      getPaymentStatus(ids).then((res) => {
        const data = res.data.data;
        const dataMap = data.reduce((acc, cur) => {
          acc[cur.id] = cur.paymentStatus;
          return acc;
        }, {});
        this.data.forEach((item) => {
          item.paymentStatus = dataMap[item.id];
        });
      });
    },
    loadDepotLocation(ids) {
      getDepotLocation(ids).then((res) => {
        const data = res.data.data;
        const dataMap = data.reduce((acc, cur) => {
          acc[cur.id] = cur.depotLocation;
          return acc;
        }, {});
        this.data.forEach((item) => {
          item.depotLocation = dataMap[item.id];
        });
      });
    },
    cellStyle({ row, column }) {
      if ("paymentStatus" === column.columnKey) {
        const status = row.paymentStatus;
        switch (status) {
          case 0: // 未付款
            return {
              backgroundColor: "#F56C6C", // 红色
              color: "#fff",
              fontWeight: "bold",
            };
          case 1: // 部分付款
            return {
              backgroundColor: "#E6A23C", // 橙色
              color: "#fff",
            };
          case 2: // 已付讫
            return {
              backgroundColor: "#67C23A", // 绿色
              color: "#fff",
            };
          case 3: // 已申请
            return {
              backgroundColor: "#409EFF", // 蓝色
              color: "#fff",
            };
          default:
            return {}; // 默认样式
        }
      }
      if ("createUserName" === column.columnKey && row.crash) {
        return {
          backgroundColor: "#F56C6C",
          color: "#fff",
        };
      }
      if ("pv" === column.columnKey) {
        return row.pv
          ? {
              backgroundColor: "#F56C6C",
              color: "#fff",
            }
          : {
              color: "#C0C4CC",
            };
      }
      if ("attaches" === column.columnKey) {
        return {
          color: row.attachNum ? this.colorName : "#C0C4CC",
          cursor: "pointer",
          textDecoration: "underline",
        };
      }
      if ("inquiries" === column.columnKey) {
        return row.inquiries && row.inquiries.length > 0
          ? {
              color: this.colorName,
              cursor: "pointer",
              textDecoration: "underline",
            }
          : { color: "#C0C4CC" };
      }
      if ("brand" === column.columnKey) {
        return {
          backgroundColor: row.brand === "1" ? this.colorName : "#7cb305",
          color: "#fff",
        };
      }
      if ("row" === column.columnKey && row.yearsAgo) {
        return {
          backgroundColor: "#F56C6C",
          color: "#fff",
        };
      }
      if ("materialName" === column.columnKey && row.type === "21") {
        return {
          background: "url(/img/yzdyzb.png) no-repeat left center / contain",
          backgroundColor: "#F56C6C",
          color: "#fff",
        };
      }
      if ("purchaseNum" === column.columnKey && row.purchaseNum > 0) {
        return {
          backgroundColor: "#5b8c00",
          color: "#fff",
          cursor: "pointer" /* 显示小手图标 */,
          textDecoration: "underline",
        };
      }
      if ("arrivalNum" === column.columnKey && row.arrivalNum > 0) {
        return {
          backgroundColor: "#10239e",
          color: "#fff",
          cursor: "pointer" /* 显示小手图标 */,
          textDecoration: "underline",
        };
      }
    },
    cellClick(row, column, cell) {
      console.log(row, column, cell);
      if (column.property === "purchaseNum") {
        //显示订单明细列表
        this.showOrderItem(row);
      } else if (column.property === "arrivalNum") {
        //显示到货明细列表
        this.showArrivalItem(row);
      } else if (column.property === "attaches") {
        this.rowAttach(row);
      } else if (
        column.property === "inquiries" &&
        row.inquiries &&
        row.inquiries.length > 0
      ) {
        this.rowInquiry(row, column.$index);
      }
    },
    handleInquiryFlow() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择要比价的数据");
        return;
      }
      const applyIds = new Set();
      const status = new Set();
      for (let i = 0; i < this.selectionList.length; i++) {
        applyIds.add(this.selectionList[i].applyId);
        if (this.selectionList[i].status !== "9")
          status.add(this.selectionList[i].status);
      }
      if (applyIds.size > 1) {
        this.$message.warning("请选择同一采购申请的数据");
        return;
      }
      if (status.size > 1) {
        this.$message.warning("请选择已审核的数据");
        return;
      }
      Object.keys(this.inquiry.form).forEach(
        (key) => (this.inquiry.form[key] = "")
      );
      const form = {
        applyId: this.selectionList[0].applyId,
        itemIds: this.selectionList.map((item) => item.id),
      };
      this.dynamicRoute(
        {
          processDefKey: this.inquiryProcessDefKey,
          form: encodeURIComponent(
            Buffer.from(JSON.stringify(form)).toString("utf8")
          ),
        },
        "start"
      );
    },
    handleRemark() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择要修改的数据");
        return;
      }
      const ids = this.selectionList.map((item) => item.id).join(",");
      this.$prompt("备注", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
      })
        .then(({ value }) => {
          return changeRemark(ids, value);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    async handleExport() {
      let msg =
        '是否导出<span style="color: #F56C6C;font-weight: bold">所有数据</span>?';
      if (this.selectionList.length > 0) {
        msg =
          '是否要导出<span style="color: #F56C6C;font-weight: bold">当前选中的数据</span>？';
      }
      let data = [];
      this.$confirm(msg, {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        dangerouslyUseHTMLString: true,
        type: "warning",
      }).then(async () => {
        if (this.selectionList.length > 0) {
          data = this.selectionList;
        } else {
          if (this.dataType === "all") {
            this.query.unArrival = null;
            this.query.unStock = null;
          } else if (this.dataType === "unArrival") {
            this.query.unArrival = true;
            this.query.unStock = null;
          } else if (this.dataType === "unStock") {
            this.query.unStock = true;
            this.query.unArrival = null;
          }
          if (this.brand === "natergy") {
            this.query.brand = "1,2";
          } else if (this.brand === "yy") {
            this.query.brand = "4";
          }
          const query = {
            ...this.query,
            desc: "id",
          };
          if (query.createTime != null) {
            query.startCreateTime = query.createTime[0];
            query.endCreateTime = query.createTime[1];
            query.createTime = null;
          }
          const res = await getList(query);
          data = res.data.data;
        }
        this.$Export.excel({
          title: "采购申请明细",
          columns: this.export1.column,
          data: data.map((item) => {
            return {
              ...item,
              applyStatus: this.statusDictKeyValue[item.applyStatus],
              cost: item.cost ? "费用" : "实物",
              unit: this.unitDictKeyValue[item.unit],
              brand: this.brandDictKeyValue[item.brand],
              crash: item.crash ? "是" : "否",
              type: this.typeDictKeyValue[item.type],
            };
          }),
        });
      });
    },
    rowAttach(row) {
      this.$refs.attachRef.init(row.id, "ni_por_apply_item");
    },
  },
};
</script>

<style></style>

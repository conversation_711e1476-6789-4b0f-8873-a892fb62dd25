import request from "@/router/axios";

export const getPage = (current, size, params) => {
  return request({
    url: "/api/ni/ofc/car/schedule/appointment/page",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/ni/ofc/car/schedule/detail",
    method: "get",
    params: {
      id,
    },
  });
};


  export const remove = (ids) => {
    return request({
      url: "/api/ni/ofc/car/schedule/appointment/remove",
      method: "post",
      params: {
        ids,
      },
    });
  };
  

  export const appointmentSave = (row) => {
    return request({
      url: "/api/ni/ofc/car/schedule/appointment/appointmentSave",
      method: "post",
      data: row,
    });
  };

  export const cancel = (id) => {
    return request({
      url: "/api/ni/ofc/car/schedule/appointment/cancelAppointment",
      method: "post",
      params:{
        id
      }
    });
  };

  export const cancelClock = (id) => {
    return request({
      url: "/api/ni/ofc/car/schedule/appointment/clockAppointment",
      method: "post",
      params:{
        id
      }
    });
  };

  export const update = (row) => {
    return request({
      url: "/api/ni/ofc/car/schedule/appointment/update",
      method: "post",
      data: row,
    });
  };
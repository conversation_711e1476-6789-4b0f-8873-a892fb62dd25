<script>
export default {
  props: {
    multiple: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      url: "/api/ni/old/huoWuZhuanLiu/list",
      detail: false,
      title: "货物流转明细",
      visible: false,
      form: {},
      query: {},
      loading: false,
      selectionList: [],
      data: [],
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      option: {
        showSummary: true,
        sumColumnList: [
          {
            name: "quantity",
            type: "sum",
          },
        ],
        menu: false,
        editBtn: false,
        delBtn: false,
        searchEnter: true,
        searchIndex: 3,
        searchIcon: true,
        size: "mini",
        searchSize: "mini",
        align: "center",
        addBtn: false,
        header: false,
        border: true,
        tip: false,
        reserveSelection: true,
        highlightCurrentRow: true,
        searchMenuSpan: 6,
        selection: true,
        column: [
          {
            label: "Id",
            prop: "id",
            search: true,
            width: 70,
          },
          {
            label: "状态",
            prop: "status",
            width: 70,
            overHidden: true,
            hide: true,
          },
          {
            label: "批号",
            prop: "batchNo",
            width: 100,
            search: true,
            overHidden: true,
          },
          {
            label: "项目",
            prop: "project",
            overHidden: true,
            width: 70,
          },
          {
            label: "方向",
            prop: "direction",
            placeholder: " ",
            overHidden: true,
            width: 65,
          },
          {
            label: "用友编码",
            prop: "youYongBianMa",
            placeholder: " ",
            overHidden: true,
            search: true,
            width: 90,
          },
          {
            label: "产品编号",
            prop: "productNo",
            placeholder: " ",
            overHidden: true,
            width: 80,
            hide: true,
          },
          {
            label: "规格",
            prop: "specification",
            placeholder: " ",
            overHidden: true,
            search: true,
            width: 85,
          },
          {
            label: "包装",
            prop: "packaging",
            placeholder: " ",
            overHidden: true,
            width: 85,
          },
          {
            label: "外包装",
            prop: "outerPackaging",
            placeholder: " ",
            overHidden: true,
            search: true,
            width: 100,
          },
          {
            label: "数量",
            prop: "quantity",
            placeholder: " ",
            overHidden: true,
            search: true,
            width: 90,
          },
          {
            label: "仓库",
            prop: "warehouse",
            placeholder: " ",
            overHidden: true,
            search: true,
            searchOrder: 99,
            width: 80,
          },
          {
            label: "备注",
            prop: "remark",
            placeholder: " ",
            overHidden: true,
            width: 80,
          },

          {
            label: "提货地",
            prop: "tiHuoDi",
            placeholder: " ",
            overHidden: true,
            fixed: "right",
            width: 80,
          },
          {
            label: "发货编号",
            prop: "shippingNo",
            placeholder: " ",
            overHidden: true,
            width: 120,
          },
        ],
      },
      direction: "all",
    };
  },
  methods: {
    onShow(params) {
      this.direction = "all";
      this.page.currentPage = 1;
      this.query = {};
      this.params = params;
      this.visible = true;
    },
    handleClose(done) {
      this.selectionClear();
      this.visible = false;
      if (done && typeof done == "function") done();
    },
    searchReset() {
      this.query = {};
      this.direction = "all";
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.onLoad(this.page, params);
      done();
    },
    selectionClear() {
      this.selectionList = [];
      if (this.$refs.crud) this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      const qu = { ...params, ...this.query, ...this.params };
      this.$http
        .get(this.url, {
          params: {
            ...qu,
            current: page.currentPage,
            size: page.pageSize,
          },
        })
        .then((res) => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records || [];
          this.loading = false;
        });
    },
    handleDataSub(row) {
      this.subVisible = true;
      this.transactionId = row.id;
      this.onLoadSub(this.pageSub);
    },
    onDirectionChange(direction) {
      if (direction === "all") {
        delete this.query.direction;
      } else {
        this.query.direction = direction;
      }
      this.onLoad(this.page, this.query);
    },
  },
};
</script>

<template>
  <el-dialog
    ref="ai-dialog"
    custom-class="ai-dialog"
    :visible.sync="visible"
    width="60%"
    append-to-body
    :before-close="handleClose"
  >
    <template #title>
      {{ title }}
      <el-divider direction="vertical" />
      <el-radio-group
        v-model="direction"
        size="mini"
        @change="onDirectionChange"
        style="margin-right: 20px"
      >
        <el-radio-button label="all">全部</el-radio-button>
        <el-radio-button label="出">出</el-radio-button>
        <el-radio-button label="入">入</el-radio-button>
      </el-radio-group>
    </template>
    <avue-crud
      v-if="visible"
      :option="option"
      :table-loading="loading"
      :data="data"
      v-model="form"
      :search.sync="query"
      :page.sync="page"
      ref="crud"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionList = $event"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
    </avue-crud>
  </el-dialog>
</template>

<style scoped></style>

import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/ni/base/supplier/info/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/ni/base/supplier/info/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/ni/base/supplier/info/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/ni/base/supplier/info/save',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/ni/base/supplier/info/update',
    method: 'post',
    data: row
  })
}

export const submit = (ids) => {
  return request({
    url: '/api/ni/base/supplier/info/submit',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const back = (ids) => {
  return request({
    url: '/api/ni/base/supplier/info/back',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const toVoid = (ids) => {
  return request({
    url: '/api/ni/base/supplier/info/toVoid',
    method: 'post',
    params: {
      ids,
    }
  })
}


export const updateBlacklistById = (id, blacklist) => {
  return request({
    url: '/api/ni/base/supplier/info/updateBlacklistById',
    method: 'post',
    params: {
      id,
      blacklist
    }
  })
}

export const updateAclById = (id, acl) => {
  return request({
    url: '/api/ni/base/supplier/info/updateAclById',
    method: 'post',
    params: {
      id,
      acl
    }
  })
}

export const userGrant = (supplierIds, userIds) => {
  return request({
    url: '/api/ni/base/supplier/info/userGrant',
    method: 'post',
    data: {
      supplierIds,
      userIds
    }
  })
}
export const getUserGrant = (id) => {
  return request({
    url: '/api/ni/base/supplier/info/userGrant',
    method: 'get',
    params: {
      id,
    }
  })
}
export const sync = (id, isCovered = false) => {
  return request({
    url: '/api/ni/base/supplier/info/sync',
    method: 'post',
    params: {
      id,
      isCovered
    }
  })
}
export const getDetail1 = (params) => {
  return request({
    url: '/api/ni/base/supplier/info/detail',
    method: 'get',
    params
  })
}
export const deptGrant = (data) => {
  return request({
    url: '/api/ni/base/supplier/info/deptGrant',
    method: 'post',
    data
  })
}
export const getDeptGrant = (id) => {
  return request({
    url: '/api/ni/base/supplier/info/deptGrant',
    method: 'get',
    params: {
      id,
    }
  })
}

//自动生成编号
export const generateHonestNo = () => {
  return request({
    url: '/api/ni/base/supplier/info/generateHonestNo',
    method: 'get',
  })
}

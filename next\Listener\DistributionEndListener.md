```java
// 定义包路径，属于问题反馈模块的监听器组件
package com.natergy.ni.feedback.listener;

// 导入MyBatis-Plus的查询条件构造器，用于数据库查询
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
// 导入问题反馈实体类，对应数据库表结构
import com.natergy.ni.feedback.entity.FeedbackEntity;
// 导入问题反馈状态枚举类，定义问题的各种状态
import com.natergy.ni.feedback.enums.FeedbackStatusEnum;
// 导入问题反馈服务接口，用于操作问题反馈数据
import com.natergy.ni.feedback.service.IFeedbackService;
// 导入Lombok的日志注解，自动生成日志对象
import lombok.extern.slf4j.Slf4j;
// 导入Apache的字符串工具类，用于字符串处理
import org.apache.commons.lang3.StringUtils;
// 导入Flowable工作流的执行监听器相关类
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.task.service.delegate.DelegateTask;
import org.flowable.task.service.delegate.TaskListener;
// 导入Blade框架的用户缓存工具类，用于获取用户信息
import org.springblade.common.cache.UserCache;
// 导入Spring的组件注解，将类标识为Spring管理的Bean
import org.springframework.stereotype.Component;
// 导入Spring的事务注解，用于声明事务管理
import org.springframework.transaction.annotation.Transactional;

// 导入Java工具类，用于集合处理和流操作
import java.util.Arrays;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>  // 作者标识
 * @since 2025-03-04  // 类创建日期
 */
// @Slf4j：Lombok注解，自动生成log日志对象，用于打印日志
@Slf4j
// @Component("DistributionEndListener")：Spring注解，将类注册为组件，名称为"DistributionEndListener"
// 供Flowable工作流引擎通过该名称调用此监听器
@Component("DistributionEndListener")
// 实现Flowable的ExecutionListener接口，作为流程执行监听器，监听流程执行到特定节点的事件
public class DistributionEndListener implements org.flowable.engine.delegate.ExecutionListener {

	// 注入问题反馈服务对象，用于数据库操作
	private final IFeedbackService feedbackService;

	// 构造函数注入，Spring会自动注入IFeedbackService的实现类
	public DistributionEndListener(IFeedbackService feedbackService) {
		this.feedbackService = feedbackService;
	}

	// @Transactional(rollbackFor = Exception.class)：声明事务，当方法中发生任何异常时，事务回滚
	@Transactional(rollbackFor = Exception.class)
	// 实现ExecutionListener接口的notify方法，当流程执行到该监听器绑定的节点时触发
	@Override
	public void notify(DelegateExecution execution) {
		// 从流程执行上下文（execution）中获取名为"feedbackId"的变量，该变量存储问题反馈的ID
		Object feedbackId = execution.getVariable("feedbackId");

		// 根据feedbackId查询对应的问题反馈实体
		FeedbackEntity feedbackEntity = feedbackService.getOne(
			new LambdaQueryWrapper<FeedbackEntity>()
				.eq(FeedbackEntity::getId, Long.valueOf(feedbackId.toString()))  // 条件：ID等于feedbackId
		);

		// 从流程执行上下文中获取名为"responsibility"的变量，该变量存储负责人的ID列表（字符串形式，用逗号分隔）
		Object responsibilityIds = execution.getVariable("responsibility");

		// 将responsibilityIds转换为字符串，若为null则默认空字符串
		// Optional.ofNullable避免空指针，map(String::valueOf)转换为字符串，orElse设置默认值
		String responsibilityIdsStr = Optional.ofNullable(responsibilityIds)
			.map(String::valueOf)
			.orElse(StringUtils.EMPTY);

		// 当负责人ID列表为空时（例如：问题被驳回到发起人后重新提交的场景），不执行后续操作，直接返回
		if (StringUtils.isBlank(responsibilityIdsStr)) {
			return;
		}

		// 将负责人ID列表转换为负责人姓名列表：
		// 1. 按逗号分割ID字符串为数组（如"1,2,3" → ["1","2","3"]）
		// 2. 遍历每个ID，通过UserCache.getUser(Long.valueOf(item))从缓存获取用户信息
		// 3. 过滤掉空用户信息（filter(Objects::nonNull)）
		// 4. 提取用户的账号（item.getAccount()）作为负责人姓名
		// 5. 用逗号拼接所有姓名为字符串（如"张三,李四"）
		String responsibilityNames = Arrays.stream(responsibilityIdsStr.split(","))
			.map(item -> UserCache.getUser(Long.valueOf(item)))
			.filter(Objects::nonNull)
			.map(item -> String.valueOf(item.getAccount()))
			.collect(Collectors.joining(","));

		// 更新问题反馈实体的负责人ID列表
		feedbackEntity.setResponsibility(responsibilityIdsStr);
		// 更新问题反馈实体的负责人姓名列表
		feedbackEntity.setResponsibilityName(responsibilityNames);
		// 更新问题反馈的状态为"等待认领"（从枚举类获取对应值）
		feedbackEntity.setStatus(FeedbackStatusEnum.WAITING_FOR_CLAIM.getValue());

		// 保存或更新问题反馈实体（若数据库中已存在则更新，否则新增）
		feedbackService.saveOrUpdate(feedbackEntity);
	}
}
```

### 类功能说明

该类是基于**Flowable 工作流引擎**的流程执行监听器，命名为`DistributionEndListener`，主要作用是在流程执行到 “分配结束” 节点时，自动更新问题反馈的负责人信息和状态，实现工作流与业务数据的联动。

#### 核心业务场景

当问题反馈流程完成分配环节后（例如：管理员将问题分配给具体负责人），监听器被触发，同步更新问题的负责人 ID、负责人姓名，并将状态设置为 “等待认领”，确保流程状态与业务数据一致。

#### 关键逻辑解析

1. **获取流程变量**：从工作流上下文（`DelegateExecution`）中获取`feedbackId`（问题 ID）和`responsibility`（负责人 ID 列表），这两个变量是流程与业务数据关联的关键。
2. **查询业务实体**：根据`feedbackId`查询对应的`FeedbackEntity`，准备进行数据更新。
3. **处理负责人信息**：将负责人 ID 列表转换为姓名列表（通过用户缓存获取），便于前端展示。
4. **更新业务状态**：设置问题状态为 “等待认领”，并保存更新后的实体，完成流程与业务的同步。

#### 技术亮点

- 采用**声明式事务**（`@Transactional`）确保数据更新的原子性，避免流程与业务数据不一致。
- 使用`Optional`和 Stream API 处理集合数据，代码简洁且避免空指针异常。
- 通过`UserCache`缓存获取用户信息，减少数据库查询，提高性能。
- 适配特殊场景（如流程驳回后重新提交），通过判断负责人 ID 是否为空进行逻辑分支处理。

该监听器是工作流与业务系统集成的典型实现，通过监听流程节点事件，自动完成业务数据更新，减少手动操作，提升系统自动化程度
<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template #honestNoForm="{ disabled, size, index }">
        <el-col :span="18">
          <el-input v-model="form.honestNo" :size="size"></el-input>
        </el-col>
        <el-col :span="6">
          <el-button
            size="mini"
            icon="el-icon-plus"
            @click="handleGenerateHonestNo()"
          />
        </el-col>
      </template>

      <template #tags="{ row, size }">
        <el-tag
          v-if="item"
          size="mini"
          v-for="(item, index) in row.tags && row.tags.split(',')"
          :key="index"
          effect="dark"
        >
          {{ item }}
        </el-tag>
      </template>
      <template #yonyouSync="{ row, size }">
        <span v-for="(item, index) in row.yonyouSync" :key="index">
          {{ yonyouSyncSequenceDictKeyValue[item.sequence] }}:
          <el-tag :size="size">
            {{ yonyouSyncStateDictKeyValue[item.value] }}
          </el-tag>
          <template v-if="item.errMsg"> :{{ item.errMsg }} </template>
          <br />
        </span>
      </template>
      <template #typeId="{ row, index }">
        <el-tag v-if="row.typeId" size="mini" effect="plain"
          >{{ typeDictKeyValue[row.typeId] }}
        </el-tag>
      </template>
      <template #status="{ row, index }">
        <el-tag v-if="row.status === 1" size="mini" type="info">
          {{ row.$status }}
        </el-tag>
        <el-tag v-else-if="row.status === 2" size="mini">
          {{ row.$status }}
        </el-tag>
        <el-tag v-else-if="row.status === 3" size="mini" type="warning">
          {{ row.$status }}
        </el-tag>
        <el-tag v-else size="mini" type="danger">
          {{ row.$status }}
        </el-tag>
      </template>
      <template #blacklist="{ row, index }">
        <el-switch
          size="mini"
          v-model="row.blacklist"
          active-color="red"
          @change="rowBlacklistChange($event, row)"
        />
      </template>
      <template #acl="{ row, index }">
        <el-tooltip
          :content="row.acl === '1' ? '私有' : '公有'"
          placement="top"
        >
          <el-switch
            size="mini"
            v-model="row.acl"
            active-value="2"
            inactive-value="1"
            inactive-color="#ff4949"
            @change="rowAclChange($event, row)"
          />
        </el-tooltip>
      </template>
      <template #honestStatus="{ row, index }">
        <el-tag size="mini" :type="row.honestStatus ? 'success' : 'info'">
          {{ row.honestStatus ? "已签订" : "没有签订" }}
        </el-tag>
      </template>
      <template #name="{ row, index }">
        <span
          :style="{
            color: colorName,
            cursor: 'pointer',
            textDecoration: 'underline',
          }"
          @click="$refs.crud.rowView(row, index)"
        >
          {{ row.name }}
        </span>
      </template>
      <template #financeAccountForm="{ row, index,size,disabled, type }">
        <span v-if="type === 'view'" style="font-weight: bolder">{{
          row.financeAccount | formatBackNo(row.financeAccount)
        }}</span>
        <bank-card-input
          v-model="row.financeAccount"
          :size="size"
          :disabled="disabled"
        />
      </template>
      <template #menuLeft>
        <el-button
          type="danger"
          size="mini"
          icon="el-icon-delete"
          plain
          v-if="permission.supplierinfo_delete"
          @click="handleDelete"
          >删 除
        </el-button>
        <el-button
          size="mini"
          icon="el-icon-setting"
          plain
          v-if="userInfo.role_name.includes('admin')"
          @click="handleRole"
          >权限设置
        </el-button>
        <el-button
          type="success"
          size="mini"
          icon="el-icon-download"
          plain
          @click="handleExport"
          >导出
        </el-button>
        <el-dropdown>
          <el-button
            v-if="
              permission.supplierinfo_import ||
              userInfo.role_name.includes('admin')
            "
            icon="el-icon-upload"
            type="warning"
            size="mini"
            plain
          >
            导 入<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item @click.native="() => (excelBox = true)">
              数据导入
            </el-dropdown-item>
            <el-dropdown-item @click.native="() => (excelBox1 = true)"
              >银行账号导入
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </template>
      <template #menuRight="{ size }">
        <el-button
          icon="el-icon-time"
          circle
          :size="size"
          @click="handleLog"
        ></el-button>
      </template>
      <template #menu="{ row, index, size }">
        <el-button
          type="text"
          icon="el-icon-s-promotion"
          :size="size"
          v-if="[1, 3].includes(row.status)"
          @click="rowSubmit(row)"
          >提交
        </el-button>
        <el-button
          type="text"
          icon="el-icon-delete"
          :size="size"
          v-if="row.status === 2 && permission.supplierinfo_tovoid"
          @click="rowToVoid(row)"
          >作废
        </el-button>
        <el-divider direction="vertical" />
        <el-dropdown>
          <el-button type="text" :size="size">
            更多<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
              v-if="
                userInfo.role_name.includes('admin') &&
                row.status === 2 &&
                row.acl === '1'
              "
              @click.native="rowAcl(row)"
            >
              <i class="el-icon-circle-plus-outline"></i>权限设置
            </el-dropdown-item>
            <el-dropdown-item @click.native="rowAttach(row)">
              <i class="el-icon-time"></i>附件管理
            </el-dropdown-item>
            <el-dropdown-item @click.native="rowYonyouSync(row)">
              <i class="el-icon-refresh"></i>同步用友
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </template>
      <template #menuForm="{ row, index, type }">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          v-if="type === 'add'"
          @click="$refs.crud.rowSave()"
        >
          新增
        </el-button>
        <el-button
          type="primary"
          icon="el-icon-circle-check"
          size="mini"
          v-if="type === 'edit'"
          @click="$refs.crud.rowUpdate()"
        >
          修改
        </el-button>
        <el-button
          type="primary"
          icon="el-icon-s-promotion"
          size="mini"
          v-if="['edit', 'add'].includes(type)"
          @click="handleSubmit(type)"
        >
          提交
        </el-button>
        <el-button
          icon="el-icon-check"
          size="mini"
          v-if="['edit', 'add'].includes(type)"
          @click="$refs.crud.closeDialog()"
        >
          取消
        </el-button>
      </template>
    </avue-crud>
    <attach-dialog ref="attachDialogRef" code="private" />
    <log-opt-dialog ref="logOptDialogRef" :module="module" />
    <el-dialog
      title="供应商数据导入"
      append-to-body
      :visible.sync="excelBox"
      width="555px"
    >
      <avue-form
        :option="excelOption"
        v-model="excelForm"
        :upload-after="uploadAfter"
      >
        <template slot="excelTemplate">
          <el-button type="primary" @click="handleTemplate">
            点击下载<i class="el-icon-download el-icon--right"></i>
          </el-button>
        </template>
      </avue-form>
    </el-dialog>
    <el-dialog
      title="供应商银行账号导入"
      append-to-body
      :visible.sync="excelBox1"
      width="555px"
    >
      <avue-form
        :option="excelOption1"
        v-model="excelForm1"
        :upload-after="uploadAfter1"
      >
        <template slot="excelTemplate">
          <el-button type="primary" @click="handleTemplate1">
            点击下载<i class="el-icon-download el-icon--right"></i>
          </el-button>
        </template>
      </avue-form>
    </el-dialog>
    <el-dialog
      title="权限设置"
      append-to-body
      :visible.sync="authority.visible"
      width="555px"
    >
      <avue-form
        :option="authority.option"
        v-model="authority.form"
        @submit="deptGrantSubmit"
      >
      </avue-form>
    </el-dialog>
  </basic-container>
</template>

<script>
import {
  add,
  getDetail,
  getList,
  remove,
  submit,
  sync,
  toVoid,
  update,
  updateAclById,
  updateBlacklistById,
  deptGrant,
  getDeptGrant,
  generateHonestNo,
} from "@/api/ni/base/supplier/supplierinfo";
import { mapGetters } from "vuex";
import { getDeptTree } from "@/api/system/dept";

import { getDetail as getAttachDetail } from "@/api/resource/attach";
import AttachDialog from "@/components/attach-dialog";
import LogOptDialog from "@/components/log-opt-dialog";
import { exportBlob } from "@/api/common";
import { getToken } from "@/util/auth";
import { downloadXls } from "@/util/util";
import website from "@/config/website";
import { dateNow1 } from "@/util/date";
import BankCardInput from "@/components/bank-card-input/index.vue";

export default {
  components: {
    BankCardInput,
    AttachDialog,
    LogOptDialog,
  },
  data() {
    return {
      module: "ni_base_supplier",
      attachDetail: false,
      typeDict: [],
      typeDict1: [],
      typeDictKeyValue: {},
      typeDictKeyValue1: {},
      statusDict: [],
      statusDictKeyValue: {},
      honestVisible: false,
      form: {
        contacts: [{}],
      },
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        searchEnter: true,
        searchLabelWidth: 110,
        reserveSelection: true,
        delBtn: false,
        saveBtn: false,
        updateBtn: false,
        cancelBtn: false,
        span: 8,
        searchIndex: 3,
        searchIcon: true,
        size: "mini",
        searchSize: "mini",
        align: "center",
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 8,
        border: true,
        index: false,
        selection: true,
        labelWidth: 135,
        dialogClickModal: false,
        column: [
          {
            label: "状态",
            prop: "status",
            type: "select",
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_por_order_status",
            search: true,
            searchOrder: 1,
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            dataType: "number",
            display: false,
          },
          {
            label: "供应商编号",
            labelTip: "同步用友后会将用友的编码带入进来",
            prop: "code",
            search: true,
            searchOrder: 99,
            display: false,
            width: 85,
          },
          {
            label: "供应商名称",
            prop: "name",
            search: true,
            searchOrder: 98,
            overHidden: true,
            display: false,
            width: 100,
          },
          {
            label: "统一社会信用代码",
            searchLabel: "usci",
            searchLabelTip: "统一社会信用代码",
            prop: "usci",
            search: true,
            searchOrder: 97,
            overHidden: true,
            display: false,
            hide: true,
          },
          {
            label: "经营范围",
            prop: "scope",
            overHidden: true,
            display: false,
          },
          {
            label: "供应商等级",
            prop: "level",
            display: false,
            type: "select",
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_supplier_level",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            dataType: "number",
            minWidth: 90,
            slot: true,
            search: true,
          },
          {
            label: "供应商类别",
            prop: "type",
            search: true,
            display: false,
            type: "select",
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_supplier_type",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            overHidden: true,
            minWidth: 90,
            slot: true,
          },
          {
            label: "所在省份",
            prop: "provinceCode",
            display: false,
            hide: true,
            type: "select",
            cascader: ["cityCode"],
            dicUrl: "/api/blade-system/region/province-list",
            props: {
              label: "name",
              value: "code",
            },
            slot: true,
          },
          {
            label: "所在省份",
            prop: "province",
            display: false,
            hide: true,
            showColumn: false,
            minWidth: 120,
            slot: true,
          },
          {
            label: "所在市区",
            prop: "cityCode",
            display: false,
            hide: true,
            showColumn: false,
            type: "select",
            dicUrl: `/api/blade-system/region/city-list?provinceCode={{key}}`,
            props: {
              label: "name",
              value: "code",
            },
            minWidth: 120,
            slot: true,
          },
          {
            label: "所在市区",
            prop: "city",
            hide: true,
            display: false,
            minWidth: 120,
            slot: true,
          },
          {
            label: "供应商地址",
            prop: "address",
            display: false,
            hide: true,
          },
          {
            label: "法人",
            prop: "corporation",
            display: false,
            hide: true,
          },
          {
            label: "注册资本(万元)",
            prop: "registeredCapital",
            display: false,
            hide: true,
          },
          {
            label: "联系人",
            prop: "linkman",
            display: false,
            overHidden: true,
            search: true,
          },
          {
            label: "手机号",
            prop: "linkPhone",
            labelTip: "供应商手机号不能重复",
            display: false,
            disabled: true,
            overHidden: true,
            search: true,
          },
          {
            label: "人员规模",
            prop: "scale",
            display: false,
            hide: true,
          },
          {
            label: "标签",
            prop: "tags",
            search: true,
            display: false,
          },
          {
            label: "访问控制",
            prop: "acl",
            search: true,
            type: "radio",
            value: "2",
            dicData: [
              {
                label: "私有",
                value: "1",
              },
              {
                label: "公开",
                value: "2",
              },
            ],
            display: false,
          },
          {
            label: "备注",
            prop: "remark",
            hide: true,
            type: "textarea",
            display: false,
            span: 24,
            minRows: 3,
          },
          {
            label: "廉洁协议",
            prop: "honestStatus",
            type: "radio",
            fixed: "right",
            search: true,
            dicData: [
              {
                label: "没有签订",
                value: false,
              },
              {
                label: "已签订",
                value: true,
              },
            ],
            display: false,
          },
          {
            label: "黑名单",
            prop: "blacklist",
            type: "radio",
            search: true,
            fixed: "right",
            dicData: [
              {
                label: "否",
                value: false,
              },
              {
                label: "是",
                value: true,
              },
            ],
            display: false,
          },
          {
            label: "是否同步用友",
            prop: "sync",
            type: "radio",
            search: true,
            hide: true,
            dicData: [
              {
                label: "否",
                value: false,
              },
              {
                label: "是",
                value: true,
              },
            ],
            display: false,
          },
          {
            label: "用友同步",
            prop: "yonyouSync",
            minWidth: 220,
            addDisplay: false,
            editDisplay: false,
            hide: true,
          },
        ],
        group: [
          {
            label: "基本信息",
            arrow: false,
            column: [
              {
                label: "供应商编号",
                prop: "code",
                placeholder: "同步用友编号",
                disabled: true,
              },
              {
                label: "供应商名称",
                prop: "name",
                searchLabelWidth: 90,
                search: true,
                rules: [
                  {
                    required: true,
                    message: "请输入",
                    trigger: "blur",
                  },
                ],
              },
              {
                label: "经营范围",
                prop: "scope",
              },
              {
                label: "统一社会信用代码",
                prop: "usci",
              },
              {
                label: "供应商等级",
                prop: "level",
                type: "select",
                dicUrl:
                  "/api/blade-system/dict-biz/dictionary?code=ni_supplier_level",
                props: {
                  label: "dictValue",
                  value: "dictKey",
                },
                dataType: "number",
                slot: true,
                search: true,
              },
              {
                label: "供应商类别",
                prop: "type",
                type: "select",
                dicUrl:
                  "/api/blade-system/dict-biz/dictionary?code=ni_supplier_type",
                props: {
                  label: "dictValue",
                  value: "dictKey",
                  desc: "dictKey",
                },
                slot: true,
                rules: [
                  {
                    required: true,
                    message: "请选择供应商类别",
                    trigger: "blur",
                  },
                ],
              },
              {
                label: "所在省份",
                prop: "provinceCode",
                searchLabelWidth: 90,
                search: true,
                type: "select",
                cascader: ["cityCode"],
                dicUrl: "/api/blade-system/region/province-list",
                props: {
                  label: "name",
                  value: "code",
                },
                width: 120,
                slot: true,
              },
              {
                label: "所在市区",
                prop: "cityCode",
                searchLabelWidth: 90,
                search: true,
                type: "select",
                dicUrl: `/api/blade-system/region/city-list?provinceCode={{provinceCode}}`,
                props: {
                  label: "name",
                  value: "code",
                },
                width: 120,
                slot: true,
              },
              {
                label: "供应商地址",
                prop: "address",
                hide: true,
              },
              {
                label: "法人",
                prop: "corporation",
                hide: true,
              },
              {
                label: "注册资本(万元)",
                prop: "registeredCapital",
                hide: true,
              },

              {
                label: "人员规模",
                prop: "scale",
                hide: true,
              },
              {
                label: "主要联系人",
                prop: "linkman",
                rules: [
                  {
                    required: true,
                    message: "请输入主要联系人",
                    trigger: "blur",
                  },
                ],
                change: (value) => {
                  this.handleMainLinkName(value);
                },
              },
              {
                label: "手机号",
                prop: "linkPhone",
                rules: [
                  {
                    required: true,
                    message: "请输入主要联系人手机号",
                    trigger: "blur",
                  },
                ],
                change: (value) => {
                  this.handleMainLinkPhone(value);
                },
              },
              {
                label: "标签",
                prop: "tags",
              },
              {
                label: "访问控制",
                prop: "acl",
                type: "radio",
                value: "2",
                dicData: [
                  {
                    label: "私有",
                    value: "1",
                  },
                  {
                    label: "公开",
                    value: "2",
                  },
                ],
                rules: [
                  {
                    required: true,
                    message: "请选择",
                    trigger: "blur",
                  },
                ],
              },
              {
                label: "是否同步用友",
                prop: "sync",
                type: "radio",
                value: true,
                dicData: [
                  {
                    label: "否",
                    value: false,
                  },
                  {
                    label: "是",
                    value: true,
                  },
                ],
              },
              {
                label: "备注",
                prop: "remark",
                hide: true,
                type: "textarea",
                span: 24,
                minRows: 3,
              },
            ],
          },
          {
            label: "联系人信息",
            arrow: false,
            column: [
              {
                size: "mini",
                labelWidth: 0,
                label: "",
                prop: "contacts",
                span: 24,
                type: "dynamic",
                children: {
                  size: "mini",
                  align: "center",
                  headerAlign: "center",
                  rowAdd: (done) => {
                    this.$message.success("新增回调");
                    done();
                  },
                  column: [
                    {
                      label: "姓名",
                      prop: "name",
                      placeholder: " ",
                      rules: [
                        {
                          required: true,
                          message: "请输入姓名",
                          trigger: "blur",
                        },
                      ],
                    },
                    {
                      label: "职位",
                      prop: "post",
                      placeholder: " ",
                    },
                    {
                      label: "性别",
                      prop: "gender",
                      type: "radio",
                      placeholder: " ",
                      dicUrl: "/api/blade-system/dict/dictionary?code=sex",
                      props: {
                        label: "dictValue",
                        value: "dictKey",
                      },
                    },
                    {
                      label: "联系电话",
                      prop: "phone",
                      placeholder: " ",
                    },
                    {
                      label: "备注",
                      prop: "remark",
                      placeholder: " ",
                    },
                    {
                      label: "主要联系人",
                      prop: "main",
                      type: "radio",
                      dicData: [
                        {
                          label: "是",
                          value: true,
                        },
                        {
                          label: "否",
                          value: false,
                        },
                      ],
                      rules: [
                        {
                          required: true,
                          message: "请选择主要联系人",
                          trigger: "blur",
                        },
                      ],
                      change: ({ row, value, index }) => {
                        if (value) {
                          this.form.contacts.forEach((item, i) => {
                            if (i === index) {
                              return;
                            }
                            item.main = false;
                          });
                          this.form.linkman = row.name;
                          this.form.linkPhone = row.phone;
                        }
                      },
                    },
                  ],
                },
              },
            ],
          },
          {
            label: "诚信廉洁协议",
            arrow: false,
            column: [
              {
                label: "是否签订",
                prop: "honestStatus",
                type: "switch",
                dicData: [
                  {
                    label: "否",
                    value: false,
                  },
                  {
                    label: "是",
                    value: true,
                  },
                ],
                value: true,
                rules: [
                  {
                    required: true,
                    message: "请输入协议编号",
                    trigger: "blur",
                  },
                ],
                change: ({ value }) => {
                  if (value) {
                    this.findObject(
                      this.option.group[2].column,
                      "honestNo"
                    ).rules = [
                      {
                        required: true,
                        message: "请输入协议编号",
                        trigger: "blur",
                      },
                    ];
                    this.findObject(
                      this.option.group[2].column,
                      "honestStartDate"
                    ).rules = [
                      {
                        required: true,
                        message: "请输入协议签订日期",
                        trigger: "blur",
                      },
                    ];
                  } else {
                    this.findObject(
                      this.option.group[2].column,
                      "honestNo"
                    ).rules = [
                      {
                        required: false,
                        message: "请输入协议编号",
                        trigger: "blur",
                      },
                    ];
                    this.findObject(
                      this.option.group[2].column,
                      "honestStartDate"
                    ).rules = [
                      {
                        required: false,
                        message: "请输入协议签订日期",
                        trigger: "blur",
                      },
                    ];
                  }
                },
              },
              {
                label: "协议编号",
                prop: "honestNo",
                rules: [
                  {
                    required: true,
                    message: "请输入协议编号",
                    trigger: "blur",
                  },
                ],
              },
              {
                label: "签订日期",
                prop: "honestStartDate",
                type: "date",
                format: "yyyy-MM-dd",
                valueFormat: "yyyy-MM-dd",
                rules: [
                  {
                    required: true,
                    message: "请输入协议签订日期",
                    trigger: "blur",
                  },
                ],
              },
              {
                label: "有效期至",
                prop: "honestEndDate",
                type: "date",
                format: "yyyy-MM-dd",
                valueFormat: "yyyy-MM-dd",
              },
            ],
          },
          {
            label: "财务信息",
            arrow: false,
            column: [
              {
                size: "mini",
                labelWidth: 0,
                label: "",
                prop: "finance",
                span: 24,
                type: "dynamic",
                children: {
                  size: "mini",
                  align: "center",
                  headerAlign: "center",
                  column: [
                    {
                      label: "开户银行",
                      prop: "financeBank",
                      minWidth: 110,
                      placeholder: " ",
                    },
                    {
                      label: "收款人全称",
                      prop: "financeName",
                      placeholder: " ",
                    },
                    {
                      label: "银行账号",
                      prop: "financeAccount",
                      placeholder: " ",
                      minWidth: 180,
                      rules: [
                        {
                          required: true,
                          message: "请输入银行账号",
                          trigger: "blur",
                        },
                      ],
                    },
                    {
                      label: "付款方式",
                      prop: "payType",
                      placeholder: " ",
                      minWidth: 120,
                    },
                    {
                      label: "发票类型",
                      prop: "bill",
                      placeholder: " ",
                      minWidth: 120,
                    },
                  ],
                },
              },
            ],
          },
        ],
      },
      data: [],
      honestOption: {
        size: "mini",
        labelWidth: 110,
        submitBtn: false,
        emptyBtn: false,
        column: [
          {
            label: "是否签订",
            prop: "honestStatus",
            type: "switch",
            dicData: [
              {
                label: "否",
                value: false,
              },
              {
                label: "是",
                value: true,
              },
            ],
            value: true,
            row: true,
            rules: [
              {
                required: true,
                message: "请输入协议编号",
                trigger: "blur",
              },
            ],
            control: (val) => {
              if (val) {
                return {
                  honestNo: {
                    rules: [
                      {
                        required: true,
                      },
                    ],
                  },
                  honestStartDate: {
                    rules: [
                      {
                        required: true,
                      },
                    ],
                  },
                  honestAttach: {
                    rules: [
                      {
                        required: true,
                      },
                    ],
                  },
                };
              } else {
                return {
                  honestNo: {
                    rules: [
                      {
                        required: false,
                      },
                    ],
                  },
                  honestStartDate: {
                    rules: [
                      {
                        required: false,
                      },
                    ],
                  },
                  honestAttach: {
                    rules: [
                      {
                        required: false,
                      },
                    ],
                  },
                };
              }
            },
          },
          {
            label: "协议编号",
            prop: "honestNo",
            row: true,
            rules: [
              {
                required: true,
                message: "请输入协议编号",
                trigger: "blur",
              },
            ],
          },
          {
            label: "签订日期",
            prop: "honestStartDate",
            type: "date",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            row: true,
            rules: [
              {
                required: true,
                message: "请输入协议签订日期",
                trigger: "blur",
              },
            ],
          },
          {
            label: "有效期至",
            prop: "honestEndDate",
            type: "date",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
          },
          {
            label: "附件上传",
            prop: "honestAttach",
            type: "upload",
            loadText: "附件上传中，请稍等",
            span: 24,
            propsHttp: {
              res: "data",
              url: "attachId",
              name: "originalName",
            },
            action: "/api/blade-resource/oss/endpoint/put-file-attach",
            rules: [
              {
                required: true,
                message: "请上传附件",
                trigger: "blur",
              },
            ],
          },
        ],
      },
      yonyouSyncSequenceDict: [],
      yonyouSyncSequenceDictKeyValue: {},
      yonyouSyncStateDict: [],
      yonyouSyncStateDictKeyValue: {},
      excelBox: false,
      excelOption: {
        size: "mini",
        searchSize: "mini",
        submitBtn: false,
        emptyBtn: false,
        column: [
          {
            label: "数据上传",
            prop: "excelFile",
            type: "upload",
            drag: true,
            loadText: "模板上传中，请稍等",
            span: 24,
            data: {},
            propsHttp: {
              res: "data",
            },
            tip: "请上传 .xls,.xlsx 标准格式文件",
            action: "/api/ni/base/supplier/info/import",
          },
          {
            label: "数据覆盖",
            prop: "isCovered",
            type: "switch",
            align: "center",
            width: 80,
            dicData: [
              {
                label: "否",
                value: 0,
              },
              {
                label: "是",
                value: 1,
              },
            ],
            value: 0,
            slot: true,
            rules: [
              {
                required: true,
                message: "请选择是否覆盖",
                trigger: "blur",
              },
            ],
          },
          {
            label: "模板下载",
            prop: "excelTemplate",
            formslot: true,
            span: 24,
          },
        ],
      },
      excelForm: {},
      excelBox1: false,
      excelOption1: {
        size: "mini",
        searchSize: "mini",
        submitBtn: false,
        emptyBtn: false,
        column: [
          {
            label: "数据上传",
            prop: "excelFile",
            type: "upload",
            drag: true,
            loadText: "模板上传中，请稍等",
            span: 24,
            data: {},
            propsHttp: {
              res: "data",
            },
            tip: "请上传 .xls,.xlsx 标准格式文件",
            action: "/api/ni/base/supplier/info/import-account",
          },
          {
            label: "数据覆盖",
            prop: "isCovered",
            type: "switch",
            align: "center",
            width: 80,
            dicData: [
              {
                label: "否",
                value: 0,
              },
              {
                label: "是",
                value: 1,
              },
            ],
            value: 0,
            slot: true,
            rules: [
              {
                required: true,
                message: "请选择是否覆盖",
                trigger: "blur",
              },
            ],
          },
          {
            label: "模板下载",
            prop: "excelTemplate",
            formslot: true,
            span: 24,
          },
        ],
      },
      excelForm1: {},
      authority: {
        visible: false,
        option: {
          span: 24,
          size: "mini",
          searchSize: "mini",
          emptyBtn: false,
          column: [
            {
              label: "选择部门",
              prop: "deptId",
              type: "tree",
              multiple: true,
              dicData: [],
              props: {
                label: "title",
              },
              slot: true,
              rules: [
                {
                  required: true,
                  message: "请选择部门",
                  trigger: "click",
                },
              ],
            },
          ],
        },
        form: {},
      },
    };
  },
  computed: {
    ...mapGetters(["userInfo", "permission", "colorName"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.supplierinfo_add, false),
        viewBtn: this.vaildData(this.permission.supplierinfo_view, false),
        delBtn: this.vaildData(this.permission.supplierinfo_delete, false),
        editBtn: this.vaildData(this.permission.supplierinfo_edit, false),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  mounted() {
    this.$nextTick(() => {
      this.$refs.crud.dicInit("cascader");
      this.dictInit();
    });
  },
  created() {
    getDeptTree(website.tenantId).then((res) => {
      const column = this.findObject(this.authority.option.column, "deptId");
      column.dicData = res.data.data;
    });
  },
  watch: {
    "excelForm.isCovered"() {
      if (this.excelForm.isCovered !== "") {
        const column = this.findObject(this.excelOption.column, "excelFile");
        column.action = `/api/ni/base/supplier/info/import?isCovered=${this.excelForm.isCovered}`;
      }
    },
    "excelForm1.isCovered"() {
      if (this.excelForm1.isCovered !== "") {
        const column = this.findObject(this.excelOption1.column, "excelFile");
        column.action = `/api/ni/base/supplier/info/import-account?isCovered=${this.excelForm1.isCovered}`;
      }
    },
  },
  methods: {
    //数据导出
    handleExport() {
      this.$confirm("是否导出当前筛选的所有数据？", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const q = {
          ...this.query,
        };
        const res = await getList(1, 10000, q);
        this.$Export.excel({
          title: "供应商信息",
          columns: this.option.column,
          data: res.data.data.records.map((item) => {
            return {
              ...item,
              status:
                item.status == 2
                  ? "已提交"
                  : item.status == 4
                  ? "已作废"
                  : item.status,
              honestStatus: item.honestStatus == true ? "已签订" : "没有签订",
              type: this.typeDictKeyValue1[item.type],
              blacklist: item.blacklist == true ? "是" : "否",
            };
          }),
        });
      });
    },
    //主要联系人联动
    handleMainLinkName(value) {
      if (this.form.contacts.length == 0) {
        this.form.contacts.push({
          name: value.value,
          main: true,
        });
      }
      this.form.contacts[0].name = value.value;
      this.form.contacts[0].main = true;
    },
    //手机号联动
    handleMainLinkPhone(value) {
      if (this.form.contacts.length == 0) {
        this.form.contacts.push({
          phone: value.value,
          main: true,
        });
      }
      this.form.contacts[0].phone = value.value;
      this.form.contacts[0].main = true;
    },
    //自动生成协议编号
    handleGenerateHonestNo() {
      generateHonestNo().then((res) => {
        this.form.honestNo = res.data.data;
      });
    },
    formatBackNo(data) {
      return data
        .replace(/\s/g, "")
        .replace(/[^\d]/g, "")
        .replace(/(\d{4})(?=\d)/g, "$1 ");
    },
    handleTemplate1() {
      exportBlob(
        `/api/ni/base/supplier/info/export-account-template?${
          this.website.tokenHeader
        }=${getToken()}`
      ).then((res) => {
        downloadXls(res.data, "供应商帐号导入模板.xlsx");
      });
    },
    uploadAfter1(res, done, loading, column) {
      console.log(res);
      window.console.log(column);
      this.excelBox1 = false;
      this.refreshChange();
      done();
      if (res) {
        this.$confirm(res, "提示", {
          confirmButtonText: "确定",
          type: "warning",
          center: true,
        });
      }
    },
    handleTemplate() {
      exportBlob(
        `/api/ni/base/supplier/info/export-template?${
          this.website.tokenHeader
        }=${getToken()}`
      ).then((res) => {
        downloadXls(res.data, "供应商导入模板.xlsx");
      });
    },
    uploadAfter(res, done, loading, column) {
      console.log(res);
      window.console.log(column);
      this.excelBox = false;
      this.refreshChange();
      done();
      if (res) {
        this.$confirm(res, "提示", {
          confirmButtonText: "确定",
          type: "warning",
          center: true,
        });
      }
    },
    rowYonyouSync(row) {
      let msg = "确定将选择数据同步?";
      if (row.yonyouSync && row.yonyouSync.length > 0) {
        msg = "数据已经同步过，是否继续?";
      }
      this.$confirm(msg, {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        sync(row.id).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
      });
    },
    handleLog() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择要查看的数据");
        return;
      } else if (this.selectionList.length > 1) {
        this.$message.warning("只能选择一条数据");
        return;
      }
      this.$refs.logOptDialogRef.init(this.selectionList[0].id);
    },
    rowAcl(row) {
      this.authority.form = {
        supplierIds: row.id,
      };
      getDeptGrant(row.id).then((res) => {
        this.authority.form = res.data.data;
        this.authority.visible = true;
      });
    },
    deptGrantSubmit(form, done) {
      deptGrant(form)
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        })
        .finally(() => {
          done();
          this.authority.form = {};
          this.authority.visible = false;
        });
    },
    handleRole() {
      if (this.selectionList.length < 1) {
        this.$message.warning("请选择数据！");
        return;
      }
      this.authority.form = {
        supplierIds: this.selectionList.map((item) => item.id).join(","),
      };
      this.authority.visible = true;
    },
    rowAttach(row) {
      if (row.status === 2) {
        this.attachDetail = true;
      }
      this.$refs.attachDialogRef.init(row.id, this.module);
    },
    rowAclChange(val, row) {
      const opt = val == "1" ? "私有" : "公开";
      this.$confirm(
        `此操作将修改供应商=>${row.name}的访问控制为${opt}，是否继续?`,
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
          center: true,
        }
      )
        .then(() => {
          updateAclById(row.id, val).then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!",
            });
          });
        })
        .catch(() => {
          row.blacklist = !val;
        });
    },
    rowBlacklistChange(val, row) {
      this.$confirm(
        `此操作将更改供应商=>${row.name}的黑名单状态，是否继续?`,
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
          center: true,
        }
      )
        .then(() => {
          updateBlacklistById(row.id, val).then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!",
            });
          });
        })
        .catch(() => {
          row.blacklist = !val;
        });
    },
    rowToVoid(row) {
      this.$confirm("此操作将作废该数据，是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        center: true,
      })
        .then(() => {
          return toVoid(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    rowSubmit(row) {
      this.$confirm("此操作将提交该数据，是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        center: true,
      })
        .then(() => {
          return submit(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    handleSubmit(type) {
      this.form.status = 2;
      if (type === "add") {
        this.$refs.crud.rowSave();
      } else if (type === "edit") {
        this.$refs.crud.rowUpdate();
      }
    },
    dictInit() {
      this.$http.get("/api/ni/base/supplier/type/list").then((res) => {
        this.typeDict = res.data.data;
        this.typeDictKeyValue = this.typeDict.reduce((acc, cur) => {
          acc[cur.id] = cur.name;
          return acc;
        }, {});
        this.typeDict1 = res.data.data;
        this.typeDictKeyValue1 = this.typeDict1.reduce((acc, cur) => {
          acc[cur.code] = cur.name;
          return acc;
        }, {});
      });
      this.$http
        .get("/api/blade-system/dict/dictionary?code=yonyou_sync_sequence")
        .then((res) => {
          this.yonyouSyncSequenceDict = res.data.data;
          this.yonyouSyncSequenceDictKeyValue =
            this.yonyouSyncSequenceDict.reduce((acc, cur) => {
              acc[cur.dictKey] = cur.dictValue;
              return acc;
            }, {});
        });
      this.$http
        .get("/api/blade-system/dict/dictionary?code=yonyou_sync_state")
        .then((res) => {
          this.yonyouSyncStateDict = res.data.data;
          this.yonyouSyncStateDictKeyValue = this.yonyouSyncStateDict.reduce(
            (acc, cur) => {
              acc[cur.dictKey] = cur.dictValue;
              return acc;
            },
            {}
          );
        });
    },
    uploadPreview(file) {
      console.log(file);
      getAttachDetail(file.url).then((res) => {
        const { link } = res.data.data;
        window.open(link);
      });
    },
    handleHonestSubmit(form, done) {
      const detail = { ...form };
      if (detail.honestAttach) {
        console.log(detail.honestAttach);
        detail.honestAttach = detail.honestAttach.map((item) => {
          return { id: item.value };
        });
      }
      update(detail)
        .then(
          () => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!",
            });
            done();
            this.honestVisible = false;
          },
          (error) => {
            console.log(error);
          }
        )
        .finally(() => {});
    },
    rowHonest(row) {
      getDetail(row.id).then((res) => {
        this.form = res.data.data;
        if (this.form.honestAttach) {
          this.form.honestAttach = this.form.honestAttach.map((item) => {
            return {
              label: item.originalName,
              value: item.link,
            };
          });
        }
        this.honestVisible = true;
      });
    },
    initData() {
      getDeptTree().then((res) => {
        const column = this.findObject(this.option.group, "deptId");
        column.dicData = res.data.data;
      });
    },
    rowSave(row, done, loading) {
      const detail = { ...row };
      if (detail.attach) {
        detail.attach = detail.attach.map((item) => {
          return {
            id: item.value,
          };
        });
      }
      if (detail.honestAttach) {
        detail.honestAttach = detail.honestAttach.map((item) => {
          return {
            id: item.value,
          };
        });
      }
      if (detail.finance) {
        detail.finance.forEach((item) => {
          if (item.financeAccount)
            item.financeAccount = item.financeAccount.split(" ").join("");
        });
      }
      add(detail).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          window.console.log(error);
        }
      );
    },
    rowUpdate(row, index, done, loading) {
      console.log(row.contacts);
      const detail = { ...row };
      if (detail.attach) {
        detail.attach = detail.attach.map((item) => {
          return {
            id: item.value,
          };
        });
      }
      if (detail.honestAttach) {
        detail.honestAttach = detail.honestAttach.map((item) => {
          return {
            id: item.value,
          };
        });
      }
      if (detail.finance) {
        detail.finance.forEach((item) => {
          if (item.financeAccount)
            item.financeAccount = item.financeAccount.split(" ").join("");
        });
      }
      update(detail).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          console.log(error);
        }
      );
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen(done, type) {
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then((res) => {
          this.form = res.data.data;
          if (this.form.attach) {
            this.form.attach = this.form.attach.map((item) => {
              return {
                label: item.originalName,
                value: item.link,
              };
            });
          }
          if (this.form.honestAttach) {
            this.form.honestAttach = this.form.honestAttach.map((item) => {
              return {
                label: item.originalName,
                value: item.link,
              };
            });
          }
          if (this.form.finance) {
            this.form.finance.forEach((item) => {
              if (item.financeAccount)
                item.financeAccount = this.formatBackNo(item.financeAccount);
            });
          }
        });
      } else if (type === "add") {
        this.form.honestStartDate = dateNow1();
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
  },
};
</script>

<style>
.el-switch__core {
  width: 30px !important;
  height: 16px;
}

.el-switch__core::after {
  width: 14px;
  height: 14px;
  margin-top: -1px;
}

.el-switch.is-checked .el-switch__core::after {
  margin-left: -15px;
}
</style>

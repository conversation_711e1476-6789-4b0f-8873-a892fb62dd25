<script>
import InventorySummarySelectDialog from "@/views/ni/fg/components/InventorySummarySelectDialog.vue";
import {mapGetters} from "vuex";
import {dateFormat} from "@/util/date";
import {add, getDetail, update} from "@/api/ni/fg/fgRestocking";
import {getDetail as getSkuDetail} from "@/api/ni/product/sku"
import SkuSelect from "@/views/ni/product/components/SkuSelect.vue";
import {getList as getBatchNos} from "@/api/ni/fg/fgInventory";

export default {
  name: "OutboundFormDialog",
  components: {SkuSelect, InventorySummarySelectDialog},
  data() {
    return {
      status: "add",
      statusMap: {
        add: "新增",
        edit: "编辑",
        view: "查看",
      },
      visible: false,
      step: 0,
      option: {
        submitText: '下一步',
        emptyBtn: false,
        size: "mini",
        span: 8,
        labelWidth: 110,
        column: [
          {
            label: "倒箱编号",
            prop: "serialNo",
            placeholder: "系统自动生成",
            overHeight: true,
            search: true,
          },
          {
            label: "倒箱主题",
            prop: "title",
            overHeight: true,
            search: true,
            rules: [
              {
                required: true,
                message: "请输入倒箱主题",
                trigger: "blur",
              },
            ],
          },
          {
            label: "仓库",
            prop: "depotId",
            type: "select",
            dicUrl: "/api/ni/base/depot/info/list?status=2&type=1",
            props: {
              label: "name",
              value: "id",
              desc: "code",
            },
            search: true,
            rules: [
              {
                required: true,
                message: "请选择仓库",
                trigger: "blur",
              },
            ],
          },
          {
            label: "操作人",
            prop: "opUserId",
            type: "select",
            remote: true,
            dicUrl: `/api/blade-user/search/otj/user?name={{key}}`,
            props: {
              label: "realName",
              value: "id",
            },
            dicFormatter: (data) => {
              return data.data.records;
            },
            placeholder: " ",
            filterable: true,
            overHidden: true,
            rules: [
              {
                required: true,
                message: "请选择操作人",
                trigger: "blur",
              },
            ],
            search: true,
            hide: true,
            showColumn: false,
          },
          {
            label: "操作人",
            prop: "opUserName",
            display: false,
            minWidth: 80
          },
          {
            label: "操作时间",
            prop: "opDate",
            search: true,
            type: "date",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            searchRange: true,
            placeholder: " ",
            clearable: false,
            minWidth: 120,
            rules: [
              {
                required: true,
                message: "请选择操作时间",
                trigger: "blur",
              },
            ],
          },
          {
            label: "库存明细",
            prop: "items",
            span: 24,
            type: "dynamic",
            hide: true,
            showColumn: false,
            children: {
              rowAdd: () => {
                this.handleInventorySelect()
              },
              size: "mini",
              align: "center",
              headerAlign: "center",
              showSummary: true,
              sumColumnList: [
                {
                  name: 'fromNum',
                  type: 'sum',
                  decimals: 1
                },
                {
                  name: 'fromWeight',
                  type: 'sum',
                  decimals: 1
                },
              ],
              column: [
                {
                  label: '存货编码',
                  prop: "fromMaterialCode",
                  placeholder: " ",
                  overHidden: true,
                  minWidth: 120,
                  cell: false,
                },
                {
                  label: '规格',
                  prop: 'fromSpecText',
                  placeholder: " ",
                  display: false,
                  overHidden: true,
                  cell: false,
                  minWidth: 100
                },

                {
                  label: '外包装',
                  prop: 'fromPackageText',
                  placeholder: " ",
                  display: false,
                  overHidden: true,
                  cell: false,
                  minWidth: 115
                },
                {
                  label: '内包装',
                  prop: 'fromInnerPackageText',
                  placeholder: " ",
                  display: false,
                  overHidden: true,
                  cell: false,
                  minWidth: 115
                },
                {
                  label: '质量',
                  prop: 'qualityLevel',
                  type: 'select',
                  dicUrl: "/api/blade-system/dict-biz/dictionary?code=ni_product_sku_quality_level",
                  props: {
                    label: "dictValue",
                    value: "dictKey",
                    desc: 'dictKey'
                  },
                  placeholder: " ",
                  width: 100,
                  cell: false,
                },
                {
                  label: '批号',
                  prop: 'fromBatchNo',
                  placeholder: " ",
                  minWidth: 150,
                },
                {
                  label: '当前库存',
                  prop: 'currentStock',
                  placeholder: " ",
                  minWidth: 100,
                  cell: false,
                },
                {
                  label: '数量',
                  prop: 'fromNum',
                  placeholder: " ",
                  type: 'number',
                  minWidth: 110,
                  rules: [
                    {
                      required: true,
                      message: "请输入数量",
                      trigger: "blur",
                    },
                  ],
                },
                {
                  label: '重量',
                  prop: 'fromWeight',
                  placeholder: " ",
                  type: 'number',
                  minWidth: 110,
                  cell: false
                },
                {
                  label: "单位",
                  prop: "fromUnit",
                  type: "select",
                  filterable: true,
                  width: 55,
                  cell: false,
                  dicUrl: "/api/blade-system/dict-biz/dictionary?code=ni_material_unit",
                  props: {
                    label: "dictValue",
                    value: "dictKey",
                  },
                  rules: [{
                    required: true,
                    message: "请选择单位",
                    trigger: "blur"
                  }],
                  slot: true,
                  placeholder: " ",
                },
                {
                  label: "备注",
                  prop: "remark",
                  type: "textarea",
                  placeholder: " ",
                  minRows: 1,
                  overHidden: true,
                  minWidth: 120,
                },
                {
                  label: "#",
                  prop: "action",
                  width: 60,
                  fixed: 'right'
                }
              ]
            }
          },
        ]
      },
      form: {},
      option2: {
        submitBtn: false,
        emptyBtn: false,
        emptyText: '上一步',
        size: "mini",
        span: 24,
        labelWidth: 110,
        column: [
          {
            label: "产品名称",
            prop: "toSkuId",
            placeholder: " ",
            overHidden: true,
            minWidth: 170,
            rules: [{
              required: true,
              message: "请选择产品名称",
              trigger: "blur"
            }],
          },
          {
            label: "规格",
            prop: "toSpecText",
            placeholder: " ",
            disabled: true,
          },
          {
            label: '质量',
            prop: 'qualityLevel',
            type: 'select',
            dicUrl: "/api/blade-system/dict-biz/dictionary?code=ni_product_sku_quality_level",
            props: {
              label: "dictValue",
              value: "dictKey",
              desc: 'dictKey'
            },
            placeholder: " ",
            disabled: true
          },
          {
            label: "外包装",
            prop: "toPackageText",
            placeholder: " ",
            disabled: true,
          },
          {
            label: "内包装",
            prop: "toInnerPackageText",
            placeholder: " ",
            disabled: true,
          },
          {
            label: '存货编码',
            prop: 'toMaterialCode',
            placeholder: " ",
            disabled: true
          },
          {
            label: "备注",
            prop: "remark",
            type: "textarea",
            placeholder: " ",
            minRows: 3,
            overHidden: true,
            minWidth: 120,
          },

        ]
      },
    }
  },
  computed: {
    ...mapGetters(['userInfo']),
    items() {
      return this.form.items || [];
    },
  },
  methods: {
    init() {
      this.step = 0
      Object.keys(this.form).forEach(key => this.form[key] = null);
      this.form.items = []
    },
    onShow(id) {
      this.status = 'view'
      this.init()
      getDetail(id).then((res) => {
        this.form = res.data.data
        this.visible = true
      })
    },
    onEdit(id) {
      this.status = 'edit'
      this.option.detail = false
      this.option2.detail = false
      getDetail(id).then((res) => {
        this.init()
        this.form = res.data.data
        this.visible = true
      })
    },
    onAdd(depotId, inventories) {
      this.status = 'add'
      this.option.detail = false
      this.option2.detail = false
      this.init()
      if (inventories) {
        this.handleInventorySelectConfirm(inventories)
      }
      this.form.depotId = depotId
      this.form.opUserId = this.userInfo.user_id
      this.form.opDate = dateFormat(new Date(), 'yyyy-MM-dd')
      this.visible = true
    },
    handleInventorySelect() {
      if (!this.form.depotId) {
        this.$message({
          type: "warning",
          message: "请选择仓库!"
        })
        return
      }
      this.$refs.inventorySelectDialog.onShow()
    },
    handleInventorySelectConfirm(selectionList) {
      selectionList.forEach((item) => {
        this.items.push({
          fromSkuId: item.skuId,
          fromSkuText: item.skuText,
          fromSpecText: item.specText,
          fromPackageText: item.packageText,
          fromInnerPackageText: item.innerPackageText,
          fromMaterialId: item.materialId,
          fromMaterialCode: item.materialCode,
          currentStock: item.num,
          qualityLevel: item.qualityLevel,
          fromUnit: item.unit,
          fromCapacity: item.capacity,
        })
      })
    },
    rowBoxCountChange(value, row) {
      row.fromWeight = value * row.fromCapacity
    },
    handleNext(form, done) {
      done()
      this.form.toNum = this.form.items.reduce((total, item) => {
        return total + item.fromNum
      }, 0)
      this.form.toWeight = this.form.items.reduce((total, item) => {
        return total + item.fromWeight
      }, 0)
      this.step = 1
    },
    handlePrevious() {
      this.step = 0
    },
    handleSkuSelectChange(skuId) {
      getSkuDetail(skuId).then((res) => {
        const {data} = res.data
        this.form.toSpecText = data.specText
        this.form.toPackageText = data.packageText
        this.form.toInnerPackageText = data.innerPackageText
        this.form.toMaterialCode = data.materialCode
        this.form.toMaterialId = data.materialId
        this.form.toUnit = data.unit
        this.form.qualityLevel = data.qualityLevel
      })
    },
    handleConfirm(form, done) {
      let r = null;
      if (this.status === 'add') {
        r = add(form)
      } else if (this.status === 'edit') {
        r = update(form)
      }
      if (r != null)
        r.then(() => {
          this.step = 2
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }).finally(() => {
          done()
        });
    },
    handleBack() {
      this.visible = false
      this.init()
      this.$emit('confirm', this.form)
    },
    batchNoRemoteMethod(query, row) {
      if (query !== '') {
        row.batchNoLoading = true;
        getBatchNos(1, 20, {batchNo: query, skuId: row.skuId, status: 1}).then((res) => {
          row.batchNoLoading = false;
          const data = res.data.data;
          data.records.forEach((item) => {
            item.label = `${item.batchNo}(${item.depotName})`
          })
          row.batchNoOptions = data.records.filter(item => {
            return item.batchNo.toLowerCase()
              .indexOf(query.toLowerCase()) > -1
          });
        })
      } else {
        row.batchNoOptions = [];
      }
    },
    rowBatchNoChange(val, row) {
      if (row.batchNoOptions && row.batchNoOptions.length > 0) {
        const selectedItem = row.batchNoOptions.find(item => item.batchNo === val);
        if (selectedItem) {
          row.fromNum = selectedItem.num; // 将批号对应数量赋值
          this.rowBoxCountChange(row.fromNum, row)
        }
      }
    },
    rowCopy(row, index) {
      const copy = {
        ...row,
        id: null,
        fromBatchNo: null
      }
      this.form.items.splice(index + 1, 0, copy)
    },
  }
}
</script>

<template>
  <el-dialog
    :visible.sync="visible"
    :title="`${statusMap[status]}-倒箱`"
    fullscreen
    append-to-body
  >
    <el-steps :active="step" align-center finish-status="success">
      <el-step title="选择库存"></el-step>
      <el-step title="填写倒箱信息"></el-step>
      <el-step v-if="status!=='view'" title="完成"></el-step>
    </el-steps>
    <div class="start-step" v-if="visible&&step===0">
      <avue-form
        :option="option"
        v-model="form"
        @submit="handleNext"
      >
        <template #action="{row,index}">
          <el-button type="text" icon="el-icon-copy-document" @click="rowCopy(row,index)"></el-button>
        </template>
        <template #fromBatchNo="{row,size,disabled,index}">
          <el-select
            :size="size"
            :disabled="disabled"
            v-model="row.fromBatchNo"
            filterable
            remote
            allow-create
            placeholder=" "
            :remote-method="((query)=>{batchNoRemoteMethod(query,row)})"
            :loading="row.batchNoLoading"
            @change="rowBatchNoChange($event,row)"
          >
            <el-option
              v-for="item in row.batchNoOptions"
              :key="item.id"
              :label="item.batchNo"
              :value="item.batchNo">
              <span style="float: left;">{{ `${item.batchNo}(${item.num})` }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">{{ item.depotName }}</span>
            </el-option>
          </el-select>
        </template>
        <template #fromNum="{row,size,disabled}">
          <el-input-number
            v-model="row.fromNum"
            :min="1"
            :max="!option.detail?row.currentStock:-1"
            :size="size"
            :step="1"
            :controls="false"
            :disabled="disabled"
            @change="rowBoxCountChange($event,row)"
          />
        </template>
      </avue-form>
    </div>
    <div class="next-step" v-else-if="visible&&step===1">
      <div class="next-step-wrapper">
        <avue-form
          :option="option2"
          ref="form"
          v-model="form"
          @submit="handleConfirm"
        >
          <template #toSkuId="{size}">
            <sku-select v-model="form.toSkuId" :size="size" :multiple="false" placeholder=" "
                        @change="handleSkuSelectChange"/>
          </template>
          <template #menuForm="{size}">
            <el-button :size="size" @click="handlePrevious">上一步</el-button>
            <el-button type="primary" :size="size" v-if="status!=='view'" @click="$refs.form.submit()">提交</el-button>
          </template>
        </avue-form>
      </div>
    </div>
    <div class="finish-step" v-else-if="visible&&step===2">
      <el-result icon="success" title="操作成功" subTitle="请在倒箱列表中点击审核完成倒箱操作">
        <template slot="extra">
          <el-button size="mini" @click="handleBack">返回</el-button>
        </template>
      </el-result>
    </div>
    <inventory-summary-select-dialog ref="inventorySelectDialog" :params="{depotId:form.depotId,status:1}" multiple
                                     @confirm="handleInventorySelectConfirm"/>
  </el-dialog>
</template>

<style scoped lang="scss">
.start-step {
  margin-top: 20px;
}

.next-step {
  margin-top: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;

  .next-step-wrapper {
    width: 500px;
  }
}
</style>


<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <!-- ---------------------分类-------------------------- -->
      <template #category="{ row, size, index, dic }">
        <el-dropdown
          trigger="click"
          @command="rowCategoryChange($event, row, index)"
        >
          <el-tag v-if="!row.category" size="mini" type="info"> 未设置 </el-tag>
          <el-tag
            v-else
            size="mini"
            type="warning"
            effect="dark"
            style="cursor: pointer"
          >
            {{ categoryDictKeyValue[row.category] }}
          </el-tag>
          <i class="el-icon-arrow-down el-icon--right"></i>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
              v-for="item in dic"
              :key="item.dictKey"
              :disabled="item.dictKey === row.category"
              :command="item.dictKey"
              >{{ item.dictValue }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </template>
      <!-- --------------------------使用状态------------------------------ -->
      <template #useStatus="{ row, size, index, dic }">
        <el-dropdown
          trigger="click"
          @command="rowUseStatusChange($event, row, index)"
        >
          <el-tag v-if="!row.useStatus" size="mini" type="info">
            未设置
          </el-tag>
          <el-tag
            v-else-if="row.useStatus == 1"
            size="mini"
            type="warning"
            effect="dark"
            style="cursor: pointer"
          >
            {{ useStatusDictKeyValue[row.useStatus] }}
          </el-tag>
          <el-tag
            v-else-if="row.useStatus == 2"
            size="mini"
            type="success"
            effect="dark"
            style="cursor: pointer"
          >
            {{ useStatusDictKeyValue[row.useStatus] }}
          </el-tag>
          <i class="el-icon-arrow-down el-icon--right"></i>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
              v-for="item in dic"
              :key="item.dictKey"
              :disabled="item.dictKey === row.useStatus"
              :command="item.dictKey"
            >
              {{ item.dictValue }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </template>
      <!-- --------------------------资产状态------------------------------ -->
      <template #assetStatus="{ row, size, index, dic }">
        <el-dropdown
          trigger="click"
          @command="rowAssetStatusChange($event, row, index)"
        >
          <el-tag v-if="!row.assetStatus" size="mini" type="info">
            未设置
          </el-tag>
          <el-tag
            v-else-if="row.assetStatus == 1"
            size="mini"
            type="success"
            effect="dark"
            style="cursor: pointer"
          >
            {{ assetStatusDicttKeyValue[row.assetStatus] }}
          </el-tag>
          <el-tag
            v-else-if="row.assetStatus == 2"
            size="mini"
            type="danger"
            effect="dark"
            style="cursor: pointer"
          >
            {{ assetStatusDicttKeyValue[row.assetStatus] }}
          </el-tag>
          <el-tag
            v-else-if="row.assetStatus == 3"
            size="mini"
            type="warning"
            effect="dark"
            style="cursor: pointer"
          >
            {{ assetStatusDicttKeyValue[row.assetStatus] }}
          </el-tag>
          <el-tag
            v-else-if="row.assetStatus == 4"
            size="mini"
            type="info"
            effect="dark"
            style="cursor: pointer"
          >
            {{ assetStatusDicttKeyValue[row.assetStatus] }}
          </el-tag>
          <i class="el-icon-arrow-down el-icon--right"></i>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
              v-for="item in dic"
              :key="item.dictKey"
              :disabled="item.dictKey === row.assetStatus"
              :command="item.dictKey"
            >
              {{ item.dictValue }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </template>
      <!----------------------------名称----------------------------->
      <template #name="{ row, index }">
        <el-button
          size="mini"
          @click="$refs.crud.rowView(row, index)"
          type="text"
          >{{ row.name }}</el-button
        >
      </template>
      <template #brand="{ row, size, index }">
        <el-tag v-if="row.brand == 1" :size="size" effect="dark">
          {{ brandDictKeyValue[row.brand] }}
        </el-tag>
        <el-tag
          v-else-if="row.brand == 2"
          :size="size"
          effect="dark"
          type="warning"
        >
          {{ brandDictKeyValue[row.brand] }}
        </el-tag>
        <el-tag v-else-if="row.brand" :size="size" effect="plain">
          {{ brandDictKeyValue[row.brand] }}
        </el-tag>
      </template>
      <!-- -----------------------领用状态-------------------------- -->
      <template #receive="{ row, size, index }">
        <span v-if="row.receive == 1" style="color: green;">
           已领取
        </span>
        <span v-else style="color: red;"> 未领取 </span>
      </template>
      <!-- -----------------------存放地点------------------------ -->
      <template #deposit="{ row, index }">
        <el-input
          type="textarea"
          :autosize="{ minRows: 1 }"
          placeholder="存放地点"
          v-model="row.deposit"
          @blur="rowDepositChange(row)"
          style="width: 100%"
        />
      </template>
      <!-- ------------------------- 质保期 ---------------------- -->
      <template #warranty="{ row, index }">
        <el-input
          type="textarea"
          :autosize="{ minRows: 1 }"
          placeholder="质保年限"
          v-model="row.warranty"
          @blur="rowWarrantyChange(row)"
          style="width: 100%"
        />
      </template>

      <template #menuLeft>
        <el-button
          type="danger"
          size="mini"
          icon="el-icon-delete"
          plain
          v-if="permission.fixed - asset_delete"
          @click="handleDelete"
          >删 除
        </el-button>

        <el-button
          type="danger"
          size="mini"
          icon="el-icon-edit-outline"
          plain
          @click="handleReceiveRegistrater"
          >领用登记
        </el-button>

        <el-button
          type="warning"
          size="mini"
          icon="el-icon-printer"
          plain
          @click="handlePrint"
          >设备验收单打印
        </el-button>
        <!-- <el-button
          type="warning"
          size="mini"
          icon="el-icon-upload2"
          plain
          @click="handleImport"
          >导入
        </el-button> -->

        <el-button
          size="mini"
          icon="el-icon-download"
          type="success"
          @click="handleExport"
          >导出
        </el-button>
        <!-- <el-divider direction="vertical"></el-divider>
        <el-checkbox v-model="JM">**********</el-checkbox> -->
      </template>
      <template #menuRight="{ size }">
        <el-button
          icon="el-icon-time"
          circle
          :size="size"
          @click="handleLog"
        ></el-button>
      </template>

      <!-------------------------------------------------- 操作栏------------------------------------------ -->
      <template #menu="{ row, index, size }">
        <el-button
          type="text"
          icon="el-icon-edit"
          :size="size"
          v-if="
            permission.fixed_asset_edit ||
            userInfo.role_name.includes('admin')
          "
          @click="$refs.crud.rowEdit(row, index)"
          >编 辑
        </el-button>

        <el-divider direction="vertical" />
        <el-dropdown>
          <el-button type="text" :size="size">
            更多<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
              type="text"
              icon="el-icon-download"
              :size="size"
              @click.native="rowAttach(row)"
            >
              上传图片
            </el-dropdown-item>

            <el-dropdown-item
              type="text"
              icon="el-icon-time"
              :size="size"
              @click.native="rowLog(row, index)"
            >
              操作日志
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </template>
      <!--------------------------------------------------- 弹框 ------------------------------------------------->
      <template #menuForm="{ row, index, type }">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          v-if="type === 'add'"
          @click="$refs.crud.rowSave()"
        >
          新增
        </el-button>
        <el-button
          icon="el-icon-check"
          size="mini"
          v-if="['edit', 'add'].includes(type)"
          @click="$refs.crud.closeDialog()"
        >
          取消
        </el-button>
      </template>

      <!-- -------------------------- --------------------------------------------------------->

      <!-- ------------------------------------------------------------------------------- -->
    </avue-crud>
    <attach-dialog ref="attachDialogRef" />
    <log-opt-dialog ref="logOptDialogRef" :module="module" />
    <!-- ////////////////////////////////////数据导入////////////////////////////////////// -->
    <el-dialog
      title="固定资产数据导入"
      append-to-body
      :visible.sync="excelBox"
      width="555px"
    >
      <avue-form
        :option="excelOption"
        v-model="excelForm"
        :upload-after="uploadAfter"
      >
        <template slot="excelTemplate">
          <el-button type="primary" @click="handleTemplate">
            点击下载<i class="el-icon-download el-icon--right"></i>
          </el-button>
        </template>
      </avue-form>
    </el-dialog>
    <!-- /////////////////////////////////////固定资产领用登记///////////////////////////////////////// -->
    <fixed-asset-register-form ref="fixedAssetRegisterFormRef" @submit="handleRegisterSubmit" />
  </basic-container>
</template>

<script>
import {
  getDetail,
  getList,
  remove,
  update,
  save,
  changeCategory,
  changeAssetStatus,
  changeUseStatus,
  changeDeposit,
  changeWarranty,
} from "@/api/ni/fin/fixed-asset";
import { mapGetters } from "vuex";
import LogOptDialog from "@/components/log-opt-dialog";
import AttachDialog from "@/components/attach-dialog";
import { exportBlob } from "@/api/common";
import { getToken } from "@/util/auth";
import { downloadXls } from "@/util/util";
import { loadPrintTemplate } from "@/api/system/printTemplate";
import FixedAssetRegisterForm from "./components/FixedAssetRegisterForm";

export default {
  components: {
    LogOptDialog,
    AttachDialog,
    FixedAssetRegisterForm,
  },
  data() {
    return {
      excelBox: false,
      excelOption: {
        size: "mini",
        searchSize: "mini",
        submitBtn: false,
        emptyBtn: false,
        tempDeposit: "",

        column: [
          {
            label: "数据上传",
            prop: "excelFile",
            type: "upload",
            drag: true,
            loadText: "数据上传中，请稍等",
            span: 24,
            data: {},
            propsHttp: {
              res: "data",
            },
            tip: "请上传 .xls,.xlsx 标准格式文件",
            action: "/api/ni/fin/fixedAsset/import",
          },
          {
            label: "模板下载",
            prop: "excelTemplate",
            formslot: true,
            span: 24,
          },
        ],
      },
      excelForm: {},
      JM: null,
      module: "ni_fin_fixed_asset",
      row: {},
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],

      option: {
        labelWidth: 110,
        span: 8,
        addBtn: true,
        saveBtn: false,
        cancelBtn: false,
        delBtn: false,
        editBtn: false,
        updateBtn: true,
        searchIndex: 3,
        searchIcon: true,
        size: "mini",
        searchSize: "mini",
        align: "center",
        rowKey: "id",
        defaultExpandAll: true,
        height: "auto",
        calcHeight: -30, //表格高度
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        viewBtn: false,
        selection: true,
        dialogClickModal: false,
        column: [
          {
            label: "分类",
            prop: "category",
            type: "select",
            width: 110,
            search: true,
            dicData: [],
            dataType: "number",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            rules: [
              {
                required: true,
                message: "请输入",
                trigger: "blur",
              },
            ],
            allowCreate: true,
            filterable: true,
            overHidden: true,
          },
          {
            label: "序号",
            prop: "row",
            type: "input",
            search: true,
            width: 60,
          },
          {
            label: "采购申请",
            prop: "porApplySerialNo",
            type: "input",
            search: true,
            overHidden: true,
            width: 120,
          },
          {
            label: "资产编号",
            prop: "serialNo",
            width: 110,
            placeholder: "系统自动生成",
            search: true,
            disabled: true,
            overHidden: true,
          },
          {
            label: "物料编码",
            prop: "materialCode",
            hide: true,
            // rules: [
            //   {
            //     required: true,
            //     message: "请输入",
            //     trigger: "blur",
            //   },
            // ],
          },
          {
            label: "名称",
            prop: "name",
            type: "input",
            search: true,
            width: 120,
          },
          {
            label: "规格",
            prop: "specification",
            type: "input",
            search: true,
            overHidden: true,
          },
          {
            label: "项目/预算",
            prop: "pOrb",
            type: "radio",
            value: 0,
            hide: true,
            display: false,
            dicData: [
              {
                label: "项目",
                value: 0,
              },
              {
                label: "预算",
                value: 1,
              },
            ],
            change: ({ value }) => {
              const projectId = this.findObject(
                this.option.column,
                "projectId"
              );
              const budgetId = this.findObject(this.option.column, "budgetId");
              if (value == 1) {
                projectId.display = false;
                budgetId.display = true;
              } else {
                projectId.display = true;
                budgetId.display = false;
              }
            },
            rules: [
              {
                required: true,
                message: "请选择",
                trigger: "blur",
              },
            ],
          },
          {
            label: "项目/预算编号",
            prop: "projectNo",
            type: "input",
            width: 110,
            overHidden: true,
            search: true,
          },
          {
            label: "项目名称",
            prop: "projectId",
            type: "tree",
            hide: true,
            display: false,
          },
          {
            label: "预算名称",
            prop: "budgetId",
            type: "tree",
            hide: true,
            display: false,
          },
          {
            label: "数量",
            prop: "num",
            type: "number",
            rules: [
              {
                required: true,
                message: "请输入",
                trigger: "blur",
              },
            ],
          },
          {
            label: "单位",
            prop: "unit",
            type: "tree",
            dataType: "number",
            dicData: [],
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            rules: [
              {
                required: true,
                message: "请输入",
                trigger: "blur",
              },
            ],
          },
          {
            label: "单价",
            prop: "price",
            type: "number",
          },
          {
            label: "总价",
            prop: "amount",
            type: "number",
            disabled: true,
            display: false,
            hide: true,
            showColumn: false,
          },
          {
            label: "存放地点",
            prop: "deposit",
            search: true,
            minWidth: 120,
            overHidden: true,
            type: "textarea",
            minRows: 1,
          },

          {
            label: "使用状态",
            prop: "useStatus",
            type: "select",
            search: true,
            dicData: [],
            dataType: "number",
            width: 100,
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            rules: [
              {
                required: true,
                message: "请输入",
                trigger: "blur",
              },
            ],
          },
          {
            label: "资产状态",
            prop: "assetStatus",
            type: "select",
            search: true,
            width: 100,
            dicData: [],
            dataType: "number",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            rules: [
              {
                required: true,
                message: "请输入",
                trigger: "blur",
              },
            ],
          },
          {
            label: "账套",
            prop: "brand",
            type: "select",
            search: true,
            dicData: [],
            dataType: "number",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            rules: [
              {
                required: true,
                message: "请输入",
                trigger: "blur",
              },
            ],
          },
          {
            label: "负责人",
            prop: "headId",
            type: "tree",
            // dicUrl: "/api/blade-user/user-vo-list",
            dicUrl: `/api/blade-user/user-list`,
            props: {
              label: "name",
              value: "id",
            },
            rules: [
              {
                required: true,
                message: "请输入",
                trigger: "blur",
              },
            ],
            allowCreate: true,
            filterable: true,
            overHidden: true,
            search: true,
            // hide:true
          },
          {
            label: "负责人部门",
            prop: "headDeptName",
            display: false,
            minWidth: 100,
            overHidden: true,
          },
          {
            label: "负责人部门",
            prop: "headDeptIds",
            type: "tree",
            hide: true,
            display: false,
            overHidden: true,

            dicUrl: `/api/blade-system/dept/list`,
            props: {
              label: "deptName",
              value: "id",
            },
            search: true,
          },

          {
            label: "购置日期",
            prop: "buyDate",
            type: "date",
            minWidth: 100,
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
            rules: [
              {
                required: true,
                message: "请输入",
                trigger: "blur",
              },
            ],
            overHidden: true,
          },
          {
            label: "购置时间范围",
            prop: "daterange",
            type: "daterange",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
            // valueFormat:"yyyy-MM-dd",
            startPlaceholder: "日期开始范围",
            endPlaceholder: "日期结束范围",
            search: true,
            searchRange: true,
            hide: true,
            display: false,
          },
          // {
          //   label:"出库编号",
          //   prop:"stockOutNo",
          //   width: 130,
          //   overHidden: true,
          // },
          {
            label: "供应商",
            prop: "supplierName",
            overHidden: true,
          },
          {
            label: "供应商电话",
            prop: "supplierPhone",
            overHidden: true,
          },
          {
            label: "质保期",
            prop: "warranty",
            minWidth: 120,
            overHidden: true,
            type: "textarea",
            minRows: 1,
          },
          {
            label: "验收人",
            prop: "inspectionUserId",
            type: "tree",
            dicUrl: `/api/blade-user/user-list`,
            props: {
              label: "name",
              value: "id",
            },
            // rules: [{
            //   required: true,
            //   message: "请输入",
            //   trigger: "blur"
            // }],
          },
          {
            label: "验收日期",
            prop: "inspectionDate",
            type: "date",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
            overHidden: true,
          },
          {
            label: "验收结果",
            prop: "qualified",
            type: "select",
            fixed: "right",
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_por_order_arrival_inspection_state",
            placeholder: " ",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            minWidth: 70,
          },
          {
            label: "是否领用",
            prop: "receive",
            fixed: "right",
            type: "select",
            dicData: [
              {
                label: "是",
                value: true,
              },
              {
                label: "否",
                value: false,
              },
            ],
          },
          {
            label: "创建人",
            prop: "createUser",
            hide: true,
            overHidden: true,
            display: false,
          },
          {
            label: "创建部门",
            prop: "createDept",
            hide: true,
            overHidden: true,
            display: false,
          },
          {
            label: "创建时间",
            prop: "createTime",
            hide: true,
            type: "datetime",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
            overHidden: true,
            display: false,
          },
          {
            label: "更新人",
            prop: "updateUser",
            hide: true,
            overHidden: true,
            display: false,
          },
          {
            label: "更新时间",
            prop: "updateTime",
            hide: true,
            type: "datetime",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
            overHidden: true,
            display: false,
          },
          {
            label: "状态",
            prop: "status",
            hide: true,
            overHidden: true,
            display: false,
          },
        ],
      },
      data: [],
      statusDict: [],
      statusDictKeyValue: {},
      typeDict: [],
      typeDictKeyValue: {},
      categoryDict: [],
      categoryDictKeyValue: {},
      assetStatusDict: [],
      assetStatusDicttKeyValue: {},
      useStatusDict: [],
      useStatusDictKeyValue: {},
      unitDict: [],
      unitDictKeyValue: {},
      brandDict: [],
      brandDictKeyValue: {},
      printTemplate: null,
      exportList: "", //数据导出列表
      exportData: [], //数据导出存储
    };
  },
  computed: {
    ...mapGetters(["permission", "userInfo"]),

    ////////////////////////////////

    permissionList() {
      return {
        // addBtn: this.vaildData(this.permission.fixed-asset_add, false),
        // viewBtn: this.vaildData(this.permission.fixed-asset_view, false),
        // delBtn: this.vaildData(this.permission.fixed-asset_delete, false),
        // editBtn: this.vaildData(this.permission.fixed-asset_edit, false)
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  mounted() {},
  ///////////////////////////////////////////////////////

  created() {
    this.dictInit();
    loadPrintTemplate(this.module).then((res) => {
      this.printTemplate = JSON.parse(res.data.data.content);
    });
  },

  methods: {
    //登记回传
    handleRegisterSubmit() {
      this.onLoad(this.page);
    },
    //领用登记
    handleReceiveRegistrater() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择要登记的数据");
        return;
      } else if (this.selectionList.length > 1) {
        this.$message.warning("只能选择一条数据");
        return;
      }
      this.$refs.fixedAssetRegisterFormRef.init(this.selectionList[0]);
    },
    rowDepositChange(row) {
      changeDeposit(row.id, row.deposit).then(() => {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "修改成功!",
        });
      });
    },
    rowWarrantyChange(row) {
      changeWarranty(row.id, row.warranty).then(() => {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "修改成功!",
        });
      });
    },
    rowAssetStatusChange(assetStatus, row, index) {
      changeAssetStatus(row.id, assetStatus).then(() => {
        this.data[index].assetStatus = assetStatus;
        this.$message({
          type: "success",
          message: "操作成功!",
        });
      });
    },
    rowUseStatusChange(useStatus, row, index) {
      changeUseStatus(row.id, useStatus).then(() => {
        this.data[index].useStatus = useStatus;
        this.$message({
          type: "success",
          message: "操作成功!",
        });
      });
    },
    rowCategoryChange(category, row, index) {
      changeCategory(row.id, category).then(() => {
        this.data[index].category = category;
        this.$message({
          type: "success",
          message: "操作成功!",
        });
      });
    },
    async handlePrint() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择要打印的数据");
        return;
      } else if (this.selectionList.length > 1) {
        this.$message.warning("只能选择一条数据");
        return;
      }
      let printData;
      let hiprintTemplate;
      if (!this.printTemplate) {
        this.$message.error("打印模板加载失败，请联系管理员");
        return;
      }
      const data = this.selectionList[0];
      printData = {
        ...data,
        brand: this.brandDictKeyValue[data.brand],
      };
      hiprintTemplate = new hiprint.PrintTemplate({
        template: this.printTemplate,
      });
      hiprintTemplate.print(printData);
    },
    rowLog(row) {
      this.$refs.logOptDialogRef.init(row.id);
    },
    handleLog() {
      this.$refs.logOptDialogRef.init();
    },

    dictInit() {
      this.$http
        .get(
          "/api/blade-system/dict-biz/dictionary?code=ni_fin_fixed_asset_category"
        )
        .then((res) => {
          const column = this.findObject(this.option.column, "category");
          column.dicData = res.data.data;
          this.categoryDict = res.data.data;
          this.categoryDictKeyValue = this.categoryDict.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });

      this.$http
        .get(
          "/api/blade-system/dict-biz/dictionary?code=ni_fin_fixed_asset_use_status"
        )
        .then((res) => {
          const column = this.findObject(this.option.column, "useStatus");
          column.dicData = res.data.data;
          this.useStatusDict = res.data.data;
          this.useStatusDictKeyValue = this.useStatusDict.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });

      this.$http
        .get(
          "/api/blade-system/dict-biz/dictionary?code=ni_fin_fixed_asset_asset_status"
        )
        .then((res) => {
          const column = this.findObject(this.option.column, "assetStatus");
          column.dicData = res.data.data;
          this.assetStatusDict = res.data.data;
          this.assetStatusDicttKeyValue = this.assetStatusDict.reduce(
            (acc, cur) => {
              acc[cur.dictKey] = cur.dictValue;
              return acc;
            },
            {}
          );
        });

      //单位
      this.$http
        .get("/api/blade-system/dict-biz/dictionary?code=ni_material_unit")
        .then((res) => {
          const unit = this.findObject(this.option.column, "unit");
          unit.dicData = res.data.data;
          this.unitDict = res.data.data;
          this.unitDictKeyValue = this.unitDict.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });

      this.$http
        .get("/api/blade-system/dict-biz/dictionary?code=ni_brand")
        .then((res) => {
          const brand = this.findObject(this.option.column, "brand");
          brand.dicData = res.data.data;
          this.brandDict = res.data.data;
          this.brandDictKeyValue = this.brandDict.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });
    },

    /////////////////////////////////////////////////////////
    handleTemplate() {
      exportBlob(
        `/api/ni/fin/fixedAsset/export-template?${
          this.website.tokenHeader
        }=${getToken()}`
      ).then((res) => {
        downloadXls(res.data, "固定资产导入模板.xlsx");
      });
    },
    handleImport() {
      this.excelBox = true;
    },
    //////////////////////////////////////////////////////////////
    //获取搜索的导出数据
    async getExportData() {
      this.exportList = "";
      const promises = [];
      this.exportData = [];
      for (var i = 1; i <= this.page.total / this.page.pageSize + 1; i++) {
        const promise = getList(i, this.page.pageSize, {
          ...this.params,
          ...this.query,
        }).then((res) => {
          const data = res.data.data.records;
          this.exportData = this.exportData.concat(data);
        });
        promises.push(promise);
      }
      // 等待所有异步请求完成
      await Promise.all(promises);
      return this.exportData;
    },
    async handleExportData() {
      this.exportList = "";
      let opt = {
        column: [
          {
            label: "分类",
            prop: "category",
          },
          {
            label: "资产编号",
            prop: "serialNo",
          },
          {
            label: "名称",
            prop: "name",
          },
          {
            label: "规格",
            prop: "specification",
          },
          {
            label: "数量",
            prop: "num",
          },
          {
            label: "单位",
            prop: "unit",
          },
          {
            label: "单价",
            prop: "price",
          },
          // {
          //   label: "总价",
          //   prop: "amount",
          // },
          {
            label: "存放地点",
            prop: "deposit",
          },
          {
            label: "使用状态",
            prop: "useStatus",
          },
          {
            label: "资产状态",
            prop: "assetStatus",
          },
          {
            label: "账套",
            prop: "brand",
          },
          {
            label: "负责人部门",
            prop: "headDeptName",
          },
          {
            label: "购置日期",
            prop: "buyDate",
            format: "yyyy-MM-dd",
          },
          {
            label: "供应商",
            prop: "supplierName",
          },
          {
            label: "供应商电话",
            prop: "supplierPhone",
          },
          {
            label: "质保期",
            prop: "warranty",
          },
          {
            label: "验收人",
            prop: "inspectionUserName",
          },
          {
            label: "验收日期",
            prop: "inspectionDate",
            format: "yyyy-MM-dd",
          },
          {
            label: "验收结果",
            prop: "qualified",
          },
        ],
      };

      let data = await this.getExportData();
      opt.column.map((item, index) => {
        if (index < opt.column.length - 1) {
          this.exportList += item.label + "\t";
        } else {
          this.exportList += item.label += "\r\n";
        }
      });
      const items = data.map((item) => {
        return {
          ...item,
          category: this.categoryDictKeyValue[item.category],
          unit: this.unitDictKeyValue[item.unit],
          useStatus: this.useStatusDictKeyValue[item.useStatus],
          assetStatus: this.assetStatusDicttKeyValue[item.assetStatus],
          brand: this.brandDictKeyValue[item.brand],
          qualified: item.qualified ? "合格" : "不合格",
        };
      });
      items.forEach((ele) => {
        let values = [];
        opt.column.forEach((item) => {
          values.push(
            ele[item.prop] !== undefined ? this.getField(ele[item.prop]) : ""
          );
        });
        this.exportList += values.join("\t") + "\r\n";
      });
      this.$Clipboard({
        text: this.exportList,
      })
        .then(() => {
          this.$message.success("数据导出完成，请复制到excel中");
        })
        .catch(() => {
          this.$message({ type: "waning", message: "该浏览器不支持自动复制" });
        })
        .finally(() => {
          //  操作完成后清空 exportList
          this.exportList = "";
        });
    },
    getField(value) {
      return value ? `${value}`.replace(/[\r\n\s]+/g, "") : "";
    },
    handleExport() {
      this.$confirm("是否导出当前筛选的所有数据？", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.handleExportData();
      });
    },
    /////////////////////////////////////////////////////////////
    rowSave(row, done, loading) {
      save(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          window.console.log(error);
        }
      );
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          console.log(error);
        }
      );
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },

    rowAttach(row) {
      this.$refs.attachDialogRef.init(row.id, this.module);
    },

    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen(done, type) {
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then((res) => {
          this.form = res.data.data;
        });
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    //////////////////////////////////////////////搜索按钮
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;

      if (params.daterange && params.daterange.length === 2) {
        params.startTime = params.daterange[0]; // 添加 startTime 参数
        params.endTime = params.daterange[1];
      }

      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    /////////刷新按钮的单击事件
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
  },
};
</script>

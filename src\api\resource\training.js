import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/ni/resource/training/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getPage = (current, size, params) => {
  return request({
    url: '/api/ni/resource/training/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/ni/resource/training/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (id) => {
  return request({
    url: '/api/ni/resource/training/remove',
    method: 'post',
    params: {
      id,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/ni/resource/training/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/ni/resource/training/update',
    method: 'post',
    data: row
  })
}

// 创建文件夹
export const buildFileSave = (row) => {
  return request({
    url: '/api/ni/resource/training/buildFileSave',
    method: 'post',
    data: row
  })
}

export const getParentIdTree = (pageId) => {
  return request({
    url: '/api/ni/resource/training/getParentIdTree',
    method: 'get',
    params: {
      pageId,
    }
  })
}

export const getParentId = (id) => {
  return request({
    url: '/api/ni/resource/training/getParentId',
    method: 'get',
    params: {
      id,
    }
  })
}

import { getToken } from '@/util/auth';
import website from '@/config/website';
import { Base64 } from 'js-base64';

export function getAuthorizationHeader() {
  return {
    'Authorization': `Basic ${Base64.encode(`${website.clientId}:${website.clientSecret}`)}`,
    'Blade-Auth': 'bearer ' + getToken(),
  };
}

import request from "@/router/axios";

export const getList = (current, size, params) => {
  return request({
    url: "/api/ni/depot/stock/steel/out/list",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/ni/depot/stock/steel/out/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const remove = (ids,needUpdateCurrentStock=true) => {
  return request({
    url: "/api/ni/depot/stock/steel/out/remove",
    method: "post",
    params: {
      ids,
      needUpdateCurrentStock
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/ni/depot/stock/steel/out/submit",
    method: "post",
    data: row,
  });
};

export const update = (row) => {
  return request({
    url: "/api/ni/depot/stock/steel/out/submit",
    method: "post",
    data: row,
  });
};
export const adjust = (ids,status) => {
  return request({
    url: "/api/ni/depot/stock/steel/out/adjust",
    method: "post",
    params: {
      ids,
      status
    },
  });
};

export const buildDepotSteelOut = (ids) => {
  return request({
    url: "/api/ni/depot/stock/steel/out/buildDepotSteelOut",
    method: "post",
    params: {
      ids,
    },
  });
};

<template>
  <basic-container>
    <avue-crud :option="option"
               :columns="mergedColumns"
               :table-loading="loading"
               :data="data"
               :page.sync="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @refresh-change="refreshChange"
               @on-load="onLoad"
               :summary-method="summaryMethod"
              >

      <template slot ="btnAddressForm">
        <el-button size="mini" @click="btnAddress" icon="el-icon-more" ></el-button>
      </template>


      <template #paymentType="{ row, index }">
        <el-tag v-if="row.paymentType === 1" size="mini">
          {{ paymentTypeDictKeyValue[row.paymentType] }}
        </el-tag>
        <el-tag v-if="row.paymentType === 2" size="mini" type="warning" >
          {{ paymentTypeDictKeyValue[row.paymentType] }}
        </el-tag>
        <el-tag v-if="row.paymentType === 3" size="mini" type="danger" >
          {{ paymentTypeDictKeyValue[row.paymentType] }}
        </el-tag>
        <el-tag v-if="![1,2,3].includes(row.paymentType)" size="mini" type="danger">
          {{ paymentTypeDictKeyValue[row.paymentType] }}
        </el-tag>

      </template>


      <template #expressType="{ row, index }">
        <el-tag v-if="row.expressType === 1" size="mini">
          {{ expressTypeDictKeyValue[row.expressType] }}
        </el-tag>
        <el-tag v-if="row.expressType === 2" size="mini" type="danger" >
          {{ expressTypeDictKeyValue[row.expressType] }}
        </el-tag>
      </template>



      <template #status="{ row, index }">
        <el-tag v-if="row.status === 1" size="mini" type="info">
          {{ statusDictKeyValue[row.status] }}
        </el-tag>
        <el-tag v-if="row.status === 2" size="mini" type="success">
          {{ statusDictKeyValue[row.status] }}
        </el-tag>
        <el-tag v-if="row.status === 3" size="mini" type="warning">
          {{ statusDictKeyValue[row.status] }}
        </el-tag>
        <el-tag v-if="row.status === 4" size="mini" type="danger">
          {{ statusDictKeyValue[row.status] }}
        </el-tag>
        <el-tag v-if="row.status === 5" size="mini" type="">
          {{ statusDictKeyValue[row.status] }}
        </el-tag>
        <el-tag v-if="row.status === 6" size="mini" type="danger">
          {{ statusDictKeyValue[row.status] }}
        </el-tag>
      </template>

<!--      <template #site="{ row, index }">-->
<!--        {{row.province}}{{row.city}}-->
<!--      </template>-->

        <template #applyCode="{row,index}">
          <el-button size="mini" @click="$refs.crud.rowView(row,index)" type="text">{{ row.applyCode }}</el-button>
        </template>
      <!-- -------------------------------------- -->
      <template #menuLeft>
        <el-button type="danger"
                   size="mini"
                   icon="el-icon-document-checked"
                   plain
                   v-if="permission.expressManage_Verify"
                   @click="handleVerify">对账
        </el-button>

<!--        <el-button type="success"-->
<!--                   size="mini"-->
<!--                   icon="el-icon-view"-->
<!--                   plain-->
<!--                   v-if="permission.expressManage_Settlement"-->
<!--                   @click="handleDelete">审核-->
<!--        </el-button>-->


<!--        <el-button-->
<!--        type="warning"-->
<!--        size="mini"-->
<!--        icon="el-icon-upload2"-->
<!--        plain-->
<!--        @click="handleImport"-->
<!--        >导入-->
<!--      </el-button>-->

      <el-button type="danger"
                 size="mini"
                 icon="el-icon-delete"
                 plain
                 @click="handleDelete">删 除
      </el-button>

      <el-button
        size="mini"
        icon="el-icon-download"
        type="success"
        @click="handleExport"
        >导出
      </el-button>

      <el-button
          type="warning"
          size="mini"
          icon="el-icon-printer"
          plain
          @click="handlePrintConfirm"
          >打印
      </el-button>
      <el-button
        type="warning"
        size="mini"
        icon="el-icon-s-check"
        plain
        v-if="permission.expressManage_load_verify"
        @click="handleImport"
      >导入验证
      </el-button>
        <el-button
          type="warning"
          size="mini"
          icon="el-icon-s-check"
          plain
          v-if="permission.expressManage_load_batch"
          @click="handleImport1"
        >快递导入
        </el-button>

      </template>
      <template #menuRight="{size}">
        <el-button icon="el-icon-time" circle :size="size" @click="handleLog"></el-button>
      </template>

      <template #cost="{ row }">
        <el-tooltip effect="dark" placement="top-start">
          <div slot="content" >
<!--            运&emsp;费:{{ row.freightCosts }}<br />-->
<!--            包装费:{{ row.packingCosts }}<br />-->
<!--            保价费:{{ row.insuredCosts }}-->
            {{ row.costDetails }}
          </div>
          <span v-if="row.status===2 && row.payableAmount===null " style="color: darkorange">{{ row.cost }}</span>
          <span v-if="row.status===2 && row.payableAmount!==null && row.costDescription===null && row.cost!==row.payableAmount" style="color: red">{{ row.cost }}</span>
          <span v-else>{{ row.cost }}</span>
        </el-tooltip>
      </template>

      <!-------------------------------------------------- 操作栏------------------------------------------ -->
      <template #menu="{row,index,size}">
        <el-button type="text"
                   icon="el-icon-edit"
                   :size="size"
                   v-if="([1].includes(row.status) && permission.expressManage_Edit && row.headId === userInfo.user_id)
                   || ([2].includes(row.status) && permission.expressManage_Edit_Manage)"
                   @click="$refs.crud.rowEdit(row,index)">编 辑
        </el-button>
        <el-button type="text"
                   icon="el-icon-s-promotion"
                   :size="size"
                   v-if="[1].includes(row.status) && permission.expressManage_Submit && row.headId === userInfo.user_id"
                   @click="rowSubmit(row)">提交
        </el-button>
<!--        <el-button type="text"-->
<!--                   icon="el-icon-delete"-->
<!--                   :size="size"-->
<!--                   v-if="row.status === 2 && permission.expressManage_Back && row.createUser === userInfo.user_id"-->
<!--                   @click="rowBack(row)">撤回-->
<!--        </el-button>-->
<!--        <el-button type="text"-->
<!--                   icon="el-icon-document-checked"-->
<!--                   :size="size"-->
<!--                   v-if="[2].includes(row.status) && permission.expressManage_Settlement"-->
<!--                   @click="rowSettlement(row)">对账-->
<!--        </el-button>-->
<!--        <el-button type="text"-->
<!--                   icon="el-icon-document-checked"-->
<!--                   :size="size"-->
<!--                   v-if="[3].includes(row.status) && permission.expressManage_Settlement"-->
<!--                   @click="rowSettlement(row)">审核-->
<!--        </el-button>-->



        <el-button type="text"
                   icon="el-icon-refresh-right"
                   :size="size"
                   v-if="row.status === 2 && permission.expressManage_Reject"
                   @click="rowReject(row)">驳回
        </el-button>
        <el-button type="text"
                   icon="el-icon-edit"
                   :size="size"
                   v-if="[5].includes(row.status) && permission.expressManage_Draft"
                   @click="rowDraft(row)">草稿
        </el-button>


        <el-divider direction="vertical" />
        <el-dropdown>
        <el-button type="text" :size="size">
         更多<i class="el-icon-arrow-down el-icon--right"></i>
        </el-button>
        <el-dropdown-menu slot="dropdown">
<!--          <el-dropdown-item-->
<!--          type="text"-->
<!--                   icon="el-icon-download"-->
<!--                   :size="size"-->
<!--                   @click.native="rowAttach(row)"-->
<!--          >-->
<!--            上传图片-->
<!--          </el-dropdown-item>-->

          <el-dropdown-item type="text"
                   icon="el-icon-time"
                   :size="size"
                   @click.native="rowLog(row,index)">
            操作日志
          </el-dropdown-item>
          <el-dropdown-item type="text"
                            icon="el-icon-delete"
                            :size="size"
                            v-if="permission.expressManage_Del && [1,5].includes(row.status)
                            && (row.createUser === userInfo.user_id || row.headId === userInfo.user_id)"
                            @click.native="rowDel(row)">
            删除
          </el-dropdown-item>
          <el-dropdown-item type="text"
                            icon="el-icon-delete"
                            :size="size"
                            v-if="row.status===2 && row.payableAmount!==null && row.cost!==row.payableAmount && row.headId === userInfo.user_id"
                            @click.native="rowCostDesc(row)">
            填写费用差异原因
          </el-dropdown-item>

        </el-dropdown-menu>
      </el-dropdown>

      </template>
      <!--------------------------------------------------- 弹框 ------------------------------------------------->
      <template #menuForm="{row,index,type}">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          v-if="type==='add'"
          @click="$refs.crud.rowSave()"
        >
          新增
        </el-button>

        <el-button
          type="primary"
          icon="el-icon-s-promotion"
          size="mini"
          v-if="['edit', 'add'].includes(type)"
          @click="handleSubmit(type)"
        >
          提交
        </el-button>
        <el-button
          icon="el-icon-check"
          size="mini"
          v-if="['edit', 'add'].includes(type)"
          @click="$refs.crud.closeDialog()"
        >
          取消
        </el-button>
      </template>

      <template slot="headIdForm" slot-scope="{disabled,size,index}">
        <user-select
          v-model="form.headId"
          :size="size"
          :disabled="disabled"
        />
      </template>
<!-- ----------------------------------------------------------------------------------->
<!-- ------------------------------------------------------------------------------- -->
    </avue-crud>
    <attach-dialog ref="attachDialogRef" />
    <log-opt-dialog ref="logOptDialogRef" :module="module"/>
<!-- ////////////////////////////////////数据导入////////////////////////////////////// -->
<!--    <el-dialog-->
<!--    title="快递登记数据导入"-->
<!--    append-to-body-->
<!--    :visible.sync="excelBox"-->
<!--    width="555px"-->
<!--  >-->
<!--    <avue-form-->
<!--      :option="excelOption"-->
<!--      v-model="excelForm"-->
<!--      :upload-after="uploadAfter"-->
<!--    >-->
<!--      <template slot="excelTemplate">-->
<!--        <el-button type="primary" @click="handleTemplate">-->
<!--          点击下载<i class="el-icon-download el-icon&#45;&#45;right"></i>-->
<!--        </el-button>-->
<!--      </template>-->
<!--    </avue-form>-->
<!--  </el-dialog>-->
<!-- /////////////////////////////////////////////////////////////////////////////////// -->
<!--    <el-dialog-->
<!--      title="导入验证数据"-->
<!--      :visible.sync="centerDialogVisible"-->
<!--      width="30%"-->
<!--      center-->
<!--      append-to-body>-->
<!--      <el-form :model="form">-->
<!--        <el-form-item >-->
<!--           <el-input type="textarea"-->
<!--                     :rows="3"-->
<!--                     placeholder="请输入内容"-->
<!--                     v-model="textarea">-->
<!--           </el-input>-->
<!--        </el-form-item>-->
<!--      </el-form>-->
<!--      <span slot="footer" class="dialog-footer">-->
<!--        <el-button @click="textarea = ''">清 空</el-button>-->
<!--        <el-button @click="centerDialogVisible = false">取 消</el-button>-->
<!--        <el-button type="primary" @click="handleCheck">确 定</el-button>-->
<!--     </span>-->
<!--    </el-dialog>-->
<!--    <el-dialog-->
<!--      title="批量导入快递数据"-->
<!--      :visible.sync="batchDialogVisible"-->
<!--      width="30%"-->
<!--      center-->
<!--      append-to-body>-->
<!--      <el-form :model="form">-->
<!--        <el-form-item >-->
<!--          <el-input type="textarea"-->
<!--                    :rows="3"-->
<!--                    placeholder="请输入内容"-->
<!--                    v-model="batchTextarea">-->
<!--          </el-input>-->
<!--        </el-form-item>-->
<!--      </el-form>-->
<!--      <span slot="footer" class="dialog-footer">-->
<!--        <el-button @click="batchTextarea = ''">清 空</el-button>-->
<!--        <el-button @click="batchDialogVisible = false">取 消</el-button>-->
<!--        <el-button type="primary" @click="handleBatchLoad">确 定</el-button>-->
<!--     </span>-->
<!--    </el-dialog>-->
    <el-dialog
      title="导入验证数据"
      append-to-body
      :visible.sync="excelBox"
      width="555px"
    >
      <avue-form
        :option="excelOption"
        v-model="excelForm"
        :upload-after="uploadAfter"
      >
        <template slot="excelTemplate">
          <el-button type="primary" @click="handleTemplate">
            点击下载<i class="el-icon-download el-icon--right"></i>
          </el-button>
        </template>
      </avue-form>
    </el-dialog>
    <el-dialog
      title="快递导入数据"
      append-to-body
      :visible.sync="excelBox1"
      width="555px"
    >
      <avue-form
        :option="excelOption1"
        v-model="excelForm1"
        :upload-after="uploadAfter1"
      >
        <template slot="excelTemplate">
          <el-button type="primary" @click="handleTemplate1">
            点击下载<i class="el-icon-download el-icon--right"></i>
          </el-button>
        </template>
      </avue-form>
    </el-dialog>
  </basic-container>
</template>

<script>
// 导入js包下的方法
import {
  getDetail,
  getList,
  remove,
  update,
  submit,
  back,
  save,
  draft,
  reject,
  verify, verifyExpress
} from "@/api/ni/ofc/ofcExpressManage";
import {mapGetters} from "vuex";
import LogOptDialog from "@/components/log-opt-dialog";
/////////////////////////////////////////////////////////
import UserSelect from "@/components/user-select";
import AttachDialog from "@/components/attach-dialog";//添加附件
import {dateNow1} from "@/util/date";//默认当前时间
import { exportBlob } from "@/api/common";
import { getToken } from "@/util/auth";
import { downloadXls } from "@/util/util";
import {hiprint} from "vue-plugin-hiprint";
import {loadPrintTemplate} from "@/api/system/printTemplate";
///////////////////////////////////////////////////////
/////////////////////////////////////////////////////
export default {
components: {
  UserSelect,
  LogOptDialog,
  AttachDialog,//添加附件
},
data() {
  return {
    textarea: "",
    batchTextarea: "",
    // /////////////////////////////////////////////////////////////
    centerDialogVisible: false,
    centerDialogVisible1: false,
    batchDialogVisible: false,
    excelBox: false,
    excelOption: {
      size: "mini",
      searchSize: "mini",
      submitBtn: false,
      emptyBtn: false,
      column: [
        {
          label: "数据上传",
          prop: "excelFile",
          type: "upload",
          drag: true,
          loadText: "数据上传中，请稍等",
          span: 24,
          data: {},
          propsHttp: {
            res: "data",
          },
          tip: "请上传 .xls,.xlsx 标准格式文件",
          action: "/api/ni/ofc/expressManage/imporLoadVerify",
        },
        {
          label: "模板下载",
          prop: "excelTemplate",
          formslot: true,
          span: 24,
        },
      ],
    },
    excelForm: {},
    excelBox1: false,
    excelOption1: {
      size: "mini",
      searchSize: "mini",
      submitBtn: false,
      emptyBtn: false,
      column: [
        {
          label: "数据上传",
          prop: "excelFile",
          type: "upload",
          drag: true,
          loadText: "数据上传中，请稍等",
          span: 24,
          data: {},
          propsHttp: {
            res: "data",
          },
          tip: "请上传 .xls,.xlsx 标准格式文件",
          action: "/api/ni/ofc/expressManage/importLoadBatch",
        },
        {
          label: "模板下载",
          prop: "excelTemplate",
          formslot: true,
          span: 24,
        },
      ],
    },
    excelForm1: {},

    parentId: 0,
    /////////////////////////////////////////////////////////////////////
    module: 'ni_ofc_express_manage',
    row: {},
    form: {},
    query: {},
    loading: true,
    page: {
      pageSize: 10,
      currentPage: 1,
      total: 0,
      pageSizes:[10, 20, 60, 100, 200, 300],
    },
    selectionList: [],
    option:{
      searchEnter: true,
      showSummary: true,
      menuWidth: 200,
      saveBtn: false,
      updateBtn: false,
      cancelBtn: false,
      editBtn: false,
      delBtn: false,
      labelWidth: 110,
      dialogFullscreen: true,
      size: 'mini',
      searchSize: 'mini',
      align: 'center',
      span: 6,
      searchIndex: 3,
      searchIcon: true,
      height: 'auto',
      calcHeight: 30,
      tip: false,
      searchShow: true,
      searchMenuSpan: 6,
      border: true,
      selection: true,
      dialogClickModal: false,
      //表格显示
          column: [
            {
              label: "状态",
              prop: "status",
              dicData: [],
              props: {
                label: "dictValue",
                value: "dictKey",
              },
              dataType: "number",
              display: false,

              search:true,
              overHidden:true,
              type:"select",
              // display:true,
              allowCreate:true,
              filterable:true,
              rules:[{
                required:true,
                message:"请选择状态",
                trigger:"blur"
              }],
            },
            {
              label: '经手人',
              prop: 'headName',
              display:false,
              // search:true,
              overHidden:true,
              hide:true,
            },
            {
              label:"经手人",
              prop:"headId",
              type: "tree",
              search:true,
              display:false,
              dicUrl:`/api/blade-user/user-list`,
              props: {
                label: "name",
                value: "id",
              },
            },
            {
              label: "快递日期",
              prop: "registerDaterange",
              type: "datetimerange",
              format:'yyyy-MM-dd HH:mm:ss',
              valueFormat: "yyyy-MM-dd HH:mm:ss",
              // valueFormat:"yyyy-MM-dd",
              startPlaceholder: '日期开始范围',
              endPlaceholder: '日期结束范围',
              search:true,
              searchRange: true,
              hide:true,
              display:false,
            },
          {
            label: '快递信息',
            display:false,
            children: [
              {
                label:"运单号",
                prop:"waybillNo",
                display:false,
                search:true,
                overHidden:true,
                minWidth:126,
              },
              {
              label: '收/发件人',
              prop:"recipient",
              display:false,
              search:true,
              overHidden:true,
            },
            {
              label:"对方地区",
              prop:"targetArea",
              display:false,
              search:true,
              overHidden:true,
              minWidth:126,
            },
            {
              label:"快递公司",
              prop:"expressCompany",
              display:false,
              search:true,
              overHidden:true,


              type:"select",
              // display:true,
              allowCreate:true,
              filterable:true,
              dicUrl:"/api/blade-system/dict-biz/dictionary?code=ni_ofc_express_company",
              dataType: "number",
              props:{
                label:"dictValue",
                value:"dictKey"
              },
              rules:[{
                required:true,
                message:"请选择快递公司",
                trigger:"blur"
              }],
            },

             {
              label:"快递费用",
              prop:"cost",
              display:false,
              search:false,
              overHidden:true,
            },
              {
                label:"应付金额",
                prop:"payableAmount",
                display:false,
                search:false,
                overHidden:true,
              },
            {
              label:"付款方式",
              prop:"paymentType",
              display:false,
              search:true,
              overHidden:true,

              dicData: [],
              type: "select",
              dataType: "number",
              props: {
                label: "dictValue",
                value: "dictKey",
              },

            },
            {
              label:"快递事由",
              prop:"reason",
              display:false,
              overHidden:true,
            },
              {
                label:"快递类型",
                prop:"expressType",
                search:true,
                display:false,
                overHidden:true,
                dicData: [],
                type: "select",
                dataType: "number",
                props: {
                  label: "dictValue",
                  value: "dictKey",
                },

              },
          ]

          },
            {
              label:'登记部门',
              prop:'headDeptName',
              display:false,
              search:false,
              overHidden:true,
            },

            {
              label:"登记部门",
              prop:"headDept",
              search:true,
              display:false,
              hide:true,
              type: "tree",
              dicUrl:`/api/blade-system/dept/list`,
              props: {
                label: "deptName",
                value: "id"
              },
            },
            {
              label:'快递日期',
              prop:'registerDate',
              type:"datetime",
              display:false,
              search:false,
              overHidden:true,
              format: "yyyy-MM-dd HH:mm:ss",
              valueFormat: "yyyy-MM-dd HH:mm:ss",
            },
          {
            label:"对账日期",
            prop:"verifyDate",
            type:"datetime",
            display:false,
            search:false,
            overHidden:true,
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
          },
            {
              label:"预算编号",
              prop:"budgetNo",
              search:true,
              display:false,
              overHidden:true,
              allowCreate:true,
              filterable:true,
              minWidth:95,
              dicData: [],
              type: "select",
              // dataType: "number",
              props: {
                label: "dictValue",
                value: "dictKey",
              },
            },
            {
              label:"对账流水号",
              prop:"serialNo",
              display:false,
              overHidden:true,
              minWidth:95,
            },
            {
              label:"费用差异原因",
              prop:"costDescription",
              display:false,
              overHidden:true,
              search:false,
            },
            {
              label:"验证日期",
              prop:"loadDate",
              type:"datetime",
              display:false,
              search:true,
              overHidden:true,
              minWidth:100,
              format: "yyyy-MM-dd HH:mm:ss",
              valueFormat: "yyyy-MM-dd HH:mm:ss",
              startPlaceholder: '日期开始范围',
              endPlaceholder: '日期结束范围',
              searchRange: true,
              // hide:true,
            },
            {
              label:'登记时间',
              prop:'createTime',
              type:"datetime",
              display:false,
              search:false,
              overHidden:true,
              format: "yyyy-MM-dd HH:mm:ss",
              valueFormat: "yyyy-MM-dd HH:mm:ss",
            },
        ],
        //弹框中填写
          group: [
            {
            label: '信息登记',
            prop: 'person',
            icon: 'el-icon-edit-outline',
              arrow: false,
              display:true,
              isFold:true,
            column: [
               {
                label:"预算编号",
                prop:"budgetNo",
                type:"select",
                display:true,
                allowCreate:true,
                filterable:true,
                dicData:[],
                // dicUrl:"/api/blade-system/dict-biz/dictionary?code=ni_ofc_express_budget",
                // dataType: "number",
                props:{
                  label:"dictValue",
                  value:"dictKey"
                },
                rules:[{
                  required:true,
                  message:"请选择预算编号",
                  trigger:"blur"
                }],
              },

              {
                label:"快递类型",
                prop:"expressType",
                type:"select",
                display:true,
                allowCreate:true,
                filterable:true,
                dicUrl:"/api/blade-system/dict-biz/dictionary?code=ni_ofc_express_type",
                dataType: "number",
                props:{
                  label:"dictValue",
                  value:"dictKey"
                },
                rules:[{
                  required:true,
                  message:"请选择快递类型",
                  trigger:"blur"
                }],
              },

              {
                label:"日期",
                prop:"registerDate",
                type:"datetime",
                search:true,
                overHidden:true,
                display:true,
                format: "yyyy-MM-dd HH:mm:ss",
                valueFormat: "yyyy-MM-dd HH:mm:ss",
                rules:[{
                  required:true,
                  message:"请输入日期",
                  trigger:"blur"
                }],
              },
            ]
          },
          {
            label: '快递登记',
            prop: 'express',
            icon: 'el-icon-edit-outline',
            arrow: false,
            column: [
              {
                label:"收/发件人",
                prop:"recipient",
                type:"input",
                display:true,
                span:5,
                rules:[{
                  required:true,
                  message:"请输入快递信息",
                  trigger:"blur"
                }],
              },
              {
                labelWidth: 0,
                label: "",
                prop: "btnAddress",
                span:1,
                display: true,
                hide: true,
                // tip: "根据复制的快递信息，自动提取收件人和地址信息",
              },
// //////////////////////////////////////////////////////////
            {
              label: '省份',
              prop: 'province',
              type: 'select',
              props: {
                label: 'name',
                value: 'code'
              },
              span:6,
              cascader: ['city'],
              dicUrl: '/api/blade-system/region/select',
              rules:[{
                  required:true,
                  message:"请输入对方省份",
                  trigger:"blur"
                }],
            },
            {
              label: '地市',
              prop: 'city',
              type: 'select',
              span: 6,
              props: {
                label: 'name',
                value: 'code'
              },
              cascader: ['district'],
              dicFlag: false,
              dicUrl: '/api/blade-system/region/select?code={{province}}',
              rules:[{
                  required:true,
                  message:"请输入对方地市",
                  trigger:"blur"
                }],
            },
            {
              label: '区县',
              prop: 'district',
              type: 'select',
              span: 6,
              // allowCreate: true,
              // filterable: true,
              props: {
                label: 'name',
                value: 'code'
              },
              dicFlag: false,
              dicUrl: '/api/blade-system/region/select?code={{city}}',
             },
// ////////////////////////////////////////////////////////////////
              {
                label:"快递公司",
                prop:"expressCompany",
                // type:"input",
                display:true,
                // rules:[{
                //   required:true,
                //   message:"请输入快递公司",
                //   trigger:"blur"
                // }],

                type:"select",
                // display:true,
                allowCreate:true,
                filterable:true,
                // dicUrl:"/api/blade-system/dict-biz/dictionary?code=ni_ofc_express_company",
                dataType: "number",
                props:{
                  label:"dictValue",
                  value:"dictKey"
                },
                rules:[{
                  required:true,
                  message:"请选择快递公司",
                  trigger:"blur"
                }],


              },

              {
                label:"运单号",
                prop:"waybillNo",
                type:"input",
                display:true,
                rules:[{
                  required:true,
                  message:"请输入运单号",
                  trigger:"blur"
                }],
              },

              {
                label:"运费",
                prop:"freightCosts",
                type:"number",
                controls: false,
                display:true,
                rules:[{
                  required:true,
                  message:"请输入运费",
                  trigger:"blur"
                }],
              },
              {
                label:"包装费",
                prop:"packingCosts",
                type:"number",
                value:0,
                controls: false,
                display:true,
                rules:[{
                  required:true,
                  message:"请输入包装费",
                  trigger:"blur"

                }],
              },
              {
                label:"保价费",
                prop:"insuredCosts",
                type:"number",
                value:0,
                controls: false,
                display:true,
                rules:[{
                  required:true,
                  message:"请输入保价费",
                  trigger:"blur"
                }],
              },
              {
                label:"付款方式",
                prop:"paymentType",
                type:"select",
                display:true,
                allowCreate:true,
                filterable:true,
                dicUrl:"/api/blade-system/dict-biz/dictionary?code=ni_ofc_express_payment_type",
                dataType: "number",
                props:{
                label:"dictValue",
                value:"dictKey"
                },
                rules:[{
                  required:true,
                   message:"请选择付款方式",
                   trigger:"blur"
                }],
               },

               {
                 label:"快递事由",
                 prop:"reason",
                 type:"textarea",
                 display:true,
                 span:12,
                 rules:[{
                   required:true,
                   message:"请输入快递事由",
                   trigger:"blur"
                 }],
              },
            ]
          }]
        },

    data: [],
    paymentTypeDict:[],
    paymentTypeDictKeyValue:{},
    expressTypeDict:[],
    expressTypeDictKeyValue:{},
    expressCompanyDict:{},
    expressCompanyDictKeyValue:{},

    ergedColumns: [], // 用于存放合并后的列配置
    statusDictKeyValue: {},
    budgetItemsPrintTemplate: null,

    itemPrintTemplate: null,
  };
},
computed: {
  ...mapGetters(["permission", "userInfo"]),

////////////////////////////////
  permissionList() {
    return {
      addBtn: this.vaildData(this.permission.expressManage_Add, false),
      viewBtn: this.vaildData(this.permission.projecttask_view, false),
      delBtn: this.vaildData(this.permission.projecttask_delete, false),
      editBtn: this.vaildData(this.permission.projecttask_edit, false)
    };
  },
  ids() {
    let ids = [];
    this.selectionList.forEach(ele => {
      ids.push(ele.id);
    });
    return ids.join(",");
  },

  //查询状态不是提交的数量
  getStatus() {
    let status = [];
    this.selectionList.forEach(ele => {
      // status.push(ele.status);
      if(ele.status !== 2){
        status.push(ele.status);
      }

    });
    // return status.join(",");
    return status.length
  },

  //应付金额异常
  getCostDesc() {
    let status = [];

    this.selectionList.forEach(ele => {
      console.log('ele:', ele)
      if(ele.payableAmount === null || (ele.payableAmount !== null && ele.cost !== ele.payableAmount && ele.costDescription == null)              ){
        status.push(ele.payableAmount);
      }
    });
    // return status.join(",");
    return status.length
  },
},
mounted() {
  this.mergeColumns();
  this.dictInit();
},
  created() {

    // loadPrintTemplate("ni_ofc_express_items").then((res) => {
    //   this.budgetItemsPrintTemplate = JSON.parse(res.data.data.content);
    // });

    loadPrintTemplate("ni_ofc_express_items").then((res) => {
      this.itemPrintTemplate = JSON.parse(res.data.data.content);
    });
  },


methods: {

  uploadAfter(res, done){
    if (res === '' ) {
      this.$message.success("全部导入成功。");
    }else{
      const err = (res+'').replace("Error: ", "")

      // 检查浏览器兼容性
      if (!navigator.clipboard) {
        this.$message.warning('该浏览器不支持自动复制');
        return;
      }

      // 确保错误信息是字符串类型
      const errorMessage = typeof err === 'string'
        ? err
        : err.message || JSON.stringify(err, null, 2);

      // 使用现代剪贴板API
      navigator.clipboard.writeText(errorMessage)
        .then(() => {
          this.$message.success('错误提示已复制到剪贴板');
        })
        .catch((clipboardErr) => {
          console.error('复制失败:', clipboardErr);
          this.$message.warning('复制失败，请手动复制');
        });



      //   this.$Clipboard({
      //     text: err
      //   }).then(() => {
      //     this.$message.error("错误提示导出到剪切板完成。");
      //   }).catch(() => {
      //     this.$message({type: 'warning', message: '该浏览器不支持自动复制'})
      //   })
      //
      // this.$alert(err, '错误提示', {
      //   confirmButtonText: '确定',
      // });
    }
    this.excelBox = false
    this.onLoad(this.page);
    done();
  },

  uploadAfter1(res, done){
    if (res === '' ) {
      this.$message.success("全部导入成功。");
    }else{
      const err = (res+'').replace("Error: ", "")
      this.$Clipboard({
        text: err
      }).then(() => {
        this.$message.error("错误提示导出到剪切板完成。");
      }).catch(() => {
        this.$message({ type: 'warning', message: '该浏览器不支持自动复制' })
      })
    }
    this.excelBox1 = false
    this.onLoad(this.page);
    done();
  },

  /**
   * 自动提取快递信息中的姓名、省、市、区
   */
  async btnAddress() {
    this.$prompt('请粘贴快递信息：（自动提取快递信息中的姓名、省、市、区）----功能开发中。。。',
      '', {
        inputValue: '',
        inputType: 'textarea',
        type: 'warning',
      }).then(({value}) => {
      // 处理输入值
      console.log(value)
    }).catch(() => {
      // 取消操作
      return;
    });
  },

  // /**
  //  * 精确计算
  //  */
  // preciseAdd(a, b) {
  //   let aStr = a.toString();
  //   let bStr = b.toString();
  //   const aDecimals = (aStr.split('.')[1] || '').length;
  //   const bDecimals = (bStr.split('.')[1] || '').length;
  //   const maxDecimals = Math.max(aDecimals, bDecimals);
  //   const factor = 10 ** maxDecimals;
  //   return (a * factor + b * factor) / factor;
  // },

  /**
   * 精确加法计算，避免浮点数精度丢失
   * @param {...number} numbers - 需要相加的数值
   * @returns {number} 计算结果
   */
  preciseAdd(...numbers) {
    if (numbers.length === 0) return 0;

    // 检查所有参数是否为有效数字
    if (!numbers.every(num => typeof num === 'number' && !isNaN(num))) {
      throw new Error('所有输入必须是有效的数字');
    }

    // 获取所有数字的小数位数，并找出最大的小数位数
    const decimalLengths = numbers.map(num => {
      const str = num.toString();
      return str.includes('.') ? str.split('.')[1].length : 0;
    });

    const maxDecimals = Math.max(...decimalLengths);
    const factor = Math.pow(10, maxDecimals);

    // 放大计算，避免精度丢失
    const sum = numbers.reduce((acc, num) => acc + Math.round(num * factor), 0);

    return sum / factor;
  },

  /**
   * 下面合计列计算
   */
  summaryMethod({ columns, data }) {
    const sums = [];
    if (columns.length > 0) {
      columns.forEach((column, index) => {
        if ([0].includes(index)) {
          sums[index] = '合计'
        } else if (!['cost', 'payableAmount',].includes(column.property)) {//过滤某些字段不参与计算
          sums[index] = ''
        } else {
          let values = data.map(item => Number(item[column.property]));
          if (!values.every((value) => isNaN(value))) {
            sums[index] = values.reduce((prev, curr) => {
              const value = Number(curr)
              if (!isNaN(value)) {
                // return prev + curr
                return this.preciseAdd(prev, curr)
              } else {
                return prev
              }
            }, 0);
          }
        }
      });
    }
    return sums;
  },

  mergeColumns() {
      // 合并 option 和 group 中的列配置
      this.mergedColumns = [
        ...this.option.column,
        ...this.option.group.flatMap((g) => g.column),
      ];
    },

  rowLog(row) {
    this.$refs.logOptDialogRef.init(row.id)
  },
  handleLog() {
    this.$refs.logOptDialogRef.init()
  },

  dictInit() {
    //预算编号
    this.$http
      .get("/api/blade-system/dict-biz/dictionary?code=ni_ofc_express_budget")
      .then((res) => {
        // console.log(res);
        const budgetNo = this.findObject(this.option.group, "budgetNo");
        const budgetNo1 = this.findObject(this.option.column, "budgetNo");
        budgetNo.dicData = res.data.data;
        budgetNo1.dicData = res.data.data;
      });

    this.$http
      .get("/api/blade-system/dict-biz/dictionary?code=ni_ofc_express_status")
      .then((res) => {
        // console.log(res);
        const column = this.findObject(this.option.column, "status");
        column.dicData = res.data.data;
        this.statusDict = res.data.data;
        this.statusDictKeyValue = this.statusDict.reduce((acc, cur) => {
          acc[cur.dictKey] = cur.dictValue;
          return acc;
        }, {});
      });

    this.$http
      .get("/api/blade-system/dict-biz/dictionary?code=ni_ofc_express_payment_type")
      .then((res) => {
        const column = this.findObject(this.option.column, "paymentType");
        column.dicData = res.data.data;
        this.paymentTypeDict = res.data.data;
        this.paymentTypeDictKeyValue = this.paymentTypeDict.reduce((acc, cur) => {
          acc[cur.dictKey] = cur.dictValue;
          return acc;
        }, {});
      });

    this.$http
      .get("/api/blade-system/dict-biz/dictionary?code=ni_ofc_express_type")
      .then((res) => {
        const column = this.findObject(this.option.column, "expressType");
        column.dicData = res.data.data;
        this.expressTypeDict = res.data.data;
        this.expressTypeDictKeyValue = this.expressTypeDict.reduce((acc, cur) => {
          acc[cur.dictKey] = cur.dictValue;
          return acc;
        }, {});
      });

    this.$http
      .get("/api/blade-system/dict-biz/dictionary?code=ni_ofc_express_company")
      .then((res) => {
        const column = this.findObject(this.option.column, "expressCompany");
        column.dicData = res.data.data;
        this.expressCompanyDict = res.data.data;
        this.expressCompanyDictKeyValue = this.expressCompanyDict.reduce((acc, cur) => {
          acc[cur.dictKey] = cur.dictValue;
          return acc;
        }, {});
      });

  },

  handleTaskLog(row) {
    this.row = row
    this.taskLogShow = true
  },

  handleFinishTask(row) {
    this.row = row
    this.finishTaskShow = true
  },
  handleStartTask(row) {
    this.row = row
    this.taskShow = true
  },

/////////////////////////////////////////////////////////
  handleTemplate() {
    exportBlob(
      `/api/ni/ofc/expressManage/export-template?${
        this.website.tokenHeader
      }=${getToken()}`
    ).then((res) => {
      downloadXls(res.data, "导入验证模板.xlsx");
    });
  },

  handleTemplate1() {
    exportBlob(
      `/api/ni/ofc/expressManage/export-template1?${
        this.website.tokenHeader
      }=${getToken()}`
    ).then((res) => {
      downloadXls(res.data, "批量导入模板.xlsx");
    });
  },

  handleImport() {
    this.excelBox = true;
  },

  handleImport1() {
    this.excelBox1 = true;
  },

  handleExport() {
    this.$confirm("是否导出当前筛选的所有数据？", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    }).then(() => {
      this.handleExportData();
    });
  },
  //获取搜索的打印数据
  async getExportData() {
    const promises = [];
    this.exportData = [];
    for (var i = 1; i <= this.page.total / this.page.pageSize + 1; i++) {
      const promise = getList(i, this.page.pageSize, {
        ...this.params,
        ...this.query,
      }).then((res) => {
        const data = res.data.data.records;
        this.exportData = this.exportData.concat(data);
      });

      promises.push(promise);
    }

    // 等待所有异步请求完成
    await Promise.all(promises);
    return this.exportData;
  },
  // 数据导出
  async handleExportData() {
    let opt = {
      column: [
        {
          label: "状态",
          prop: "statusName",
        },
        {
          label: "经手人",
          prop: "headName",
        },
        {
          label: "登记部门",
          prop: "headDeptName",
        },
        {
          label: "运单号",
          prop: "waybillNo",
        },
        {
          label: "收/发件人",
          prop: "recipient",
        },
        {
          label: "对方地区",
          prop: "targetArea",
        },
        {
          label: "快递公司",
          prop: "expressCompanyName",
        },
        {
          label: "快递费用",
          prop: "cost",
        },
        {
          label: "付款方式",
          prop: "paymentTypeName",
        },
        {
          label: "快递事由",
          prop: "reason",
        },
        {
          label: "快递类型",
          prop: "expressTypeName",
        },
        {
          label: "登记时间",
          prop: "registerDate",
        },
        {
          label: "预算编号",
          prop: "budgetNo",
        },
        {
          label: "对账流水号",
          prop: "serialNo",
        },
        {
          label: "对账日期",
          prop: "verifyDate",
        },

      ],
    };

    await this.getExportData();
    this.$Export.excel({
      title: "快递登记表",
      columns: opt.column,
      data: this.exportData.map((item) => {
        return {
          ...item,
          statusName: this.statusDictKeyValue[item.status],
          inspectorName: this.expressTypeDictKeyValue[item.inspector],
          expressCompanyName: this.expressCompanyDictKeyValue[item.expressCompany],
          paymentTypeDictName: this.paymentTypeDictKeyValue[item.paymentType],
          expressTypeName: this.expressTypeDictKeyValue[item.expressType],
        };
      }),
    });
    this.exportData = [];
  },

//////////////////////////////////////////////////////////////
  handleExport1() {
    this.$confirm("是否导出快递登记表数据?", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    }).then(() => {
      // NProgress.start();
      exportBlob(
        `/api/ni/ofc/expressManage/expressManageExport?${
          this.website.tokenHeader
        }=${getToken()}`
      ).then((res) => {
        downloadXls(res.data, `快递登记表.xlsx`);
        // NProgress.done();
      });

    });
  },

  showLoadDialog(){
    // this.centerDialogVisible = true
    this.excelBox = true
  },

  showBatchLoadDialog(){
    this.batchDialogVisible = true
  },

  // /**
  //  * 批量导入快递
  //  */
  // async handleBatchLoad() {
  //   if(this.batchTextarea.length === 0){
  //     this.$message.warning("数据不能为空。")
  //     return;
  //   }
  //
  //   this.$confirm("确定将粘贴板数据导入吗?", {
  //     confirmButtonText: "确定",
  //     cancelButtonText: "取消",
  //     type: "warning"
  //   }).then(async () => {
  //     let text = this.batchTextarea
  //     // 将字符串按行拆分成数组，并去掉空行
  //     // const shipments = text.trim().split("\n").map(line => line.split(/\s+/));
  //     // console.log(shipments);
  //
  //     batchLoad(shipments).then(() => {
  //       this.onLoad(this.page);
  //       this.$message({
  //         type: "success",
  //         message: "操作成功!"
  //       });
  //     }, error => {
  //       this.onLoad(this.page);
  //       error = String(error).replace("Error: ", "")
  //
  //       this.$Clipboard({
  //         text: error
  //       }).then(() => {
  //         this.$message.success("错误提示导出到剪切板完成。");
  //       }).catch(() => {
  //         this.$message({ type: 'waning', message: '该浏览器不支持自动复制' })
  //       })
  //
  //     });
  //   }).catch(() => {
  //     // 取消操作
  //     this.$message({
  //       type: 'info',
  //       message: '已取消'
  //     })
  //   })
  //   this.batchDialogVisible = false
  // },
  /**
   * 数据验证
   */
  async handleCheck() {
    if(this.textarea.length === 0){
      this.$message.warning("数据不能为空。")
      return;
    }

    this.$confirm("确定将粘贴板数据导入进行验证吗?", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    }).then(async () => {
      // const text = await navigator.clipboard.readText();
      const text = this.textarea
      verifyExpress(text).then(() => {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
      }, error => {
        // loading();
        this.onLoad(this.page);
        window.console.log(error);
        error = String(error).replace("Error: ", "")

        this.$Clipboard({
          text: error
        }).then(() => {
          this.$message.success("错误提示导出到剪切板完成。");
        }).catch(() => {
          this.$message({ type: 'waning', message: '该浏览器不支持自动复制' })
        })

      });
    }).catch(() => {
        // 取消操作
        this.$message({
          type: 'info',
          message: '已取消'
        })
    })
    this.centerDialogVisible = false
  },
/////////////////////////////////////////////////////////////
  handlePrintConfirm() {
    this.$confirm("是否打印当前筛选的所有数据？", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    })
      .then(() => {
        this.handlePrint();
      })
  },
  //数据打印
  async handlePrint() {
    let printData;
    let hiprintTemplate;

    if (!this.itemPrintTemplate) {
      this.$message.error("打印模板加载失败，请联系管理员");
      return;
    }
    //获取打印数据
    let data = await this.getExportData();

    //字典赋值
    let data1 = data.map((item) => {
      return {
        ...item,
        statusName: this.statusDictKeyValue[item.status],
        inspectorName: this.expressTypeDictKeyValue[item.inspector],
        expressCompany: this.expressCompanyDictKeyValue[item.expressCompany],
        paymentTypeName: this.paymentTypeDictKeyValue[item.paymentType],
        expressTypeName: this.expressTypeDictKeyValue[item.expressType],
      };
    });
    //合计金额
    const total = data1.reduce(function (prev, cur) {
      return Number(prev) + Number(cur.cost);
    }, 0);
    printData = {
      items: data1,
      printDate: dateNow1(),
      total: total,
    };
    hiprintTemplate = new hiprint.PrintTemplate({
      template: this.itemPrintTemplate,
    });
    hiprintTemplate.print(printData);
  },


//   async handlePrint() {
//   let printData;
//   let hiprintTemplate;
//     if (!this.budgetItemsPrintTemplate) {
//       this.$message.error("打印模板加载失败，请联系管理员");
//       return;
//     }``
//
//     const data = await getItemsExpressPrintData();
//     const total = data.data.data.reduce(function (prev, cur) {
//       return Number(prev) + Number(cur.cost);
//     }, 0);
//
//     if (this.data.length == 0) {
//       this.$message.error("查询数据为空，不能打印！");
//       return;
//     }
//     printData = {
//       items: data.data.data,
//       printDate: dateNow1(),
//       total: total.toLocaleString(),
//     };
//
//     hiprintTemplate = new hiprint.PrintTemplate({
//       template: this.budgetItemsPrintTemplate,
//     });
//
//       hiprintTemplate.print(printData);
//
//     },
/////////////////////////////////////////////////////////////
  rowSave(row, done, loading) {
    const pos = new Map();
    pos.set("province",row.province);
    pos.set("city",row.city);
    pos.set("district",row.district);
    pos.set("detailArea","");//row.detailArea
      save(row , pos).then(() => {
      this.onLoad(this.page);
      this.$message({
        type: "success",
        message: "操作成功!"
      });
      done();
    }, error => {
      loading();
      window.console.log(error);
    });
  },
  rowUpdate(row, index, done, loading) {
    const pos = new Map();
    pos.set("province",row.province);
    pos.set("city",row.city);
    pos.set("district",row.district);
    pos.set("detailArea",row.detailArea);
    update(row , pos).then(() => {
      this.onLoad(this.page);
      this.$message({
        type: "success",
        message: "操作成功!"
      });
      done();
    }, error => {
      loading();
      console.log(error);
    });
  },
  /**
   * 填写费用差异原因
   * @param row
   */ async rowCostDesc(row, pos) {
    // console.log(pos)
    let vt = row.costDescription
    if (vt === null) {
      vt = ""
    }
    // var desc = prompt("请输入费用差异原因：", vt);
    let desc = ""
    await this.$prompt('请输入费用差异原因：',
      '', {
      inputValue: vt,
      inputType: 'textarea',
        type: 'warning',
    }).then(({value}) => {
      // 处理输入值
        desc = value
    }).catch(() => {
      // 取消操作
        return;
    });

    if (desc === null || desc === '') {
      return
    }
    row.costDescription = desc
    update(row, pos).then(() => {
      this.onLoad(this.page);
      this.$message({
        type: "success",
        message: "操作成功!"
      });
      // done();
    }, error => {
      // loading();
      console.log(error);
    });
  },
  rowDel(row) {
    this.$confirm("确定将选择数据删除?", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    })
      .then(() => {
        return remove(row.id);
      })
      .then(() => {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
      });
  },
// ////////////////////////////////////////////////提交数据
  rowSubmit(row) {
    this.$confirm("此操作将提交该数据，是否继续?", '提示', {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
      center: true
    })
      .then(() => {
        return submit(row.id)
        // return save(row);
      })
      .then(() => {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
      });
  },

  ////////////////////////////////////////////撤回
  rowBack(row) {
    this.$confirm("此操作撤回提交的数据，是否继续?", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
      center: true,
    })
      .then(() => {
        return back(row.id);
      })
      .then(() => {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!",
        });
      });
  },

  //////////////////////////////////////////////////驳回数据
  rowReject(row) {
    this.$confirm("此操作将驳回该数据，是否继续?", '提示', {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
      center: true
    })
      .then(() => {
        return reject(row.id)
        // return save(row);
      })
      .then(() => {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
      });
  },
  //////////////////////////////////////////////////草稿数据
  rowDraft(row) {
    this.$confirm("此操作将草稿该数据，是否继续?", '提示', {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
      center: true
    })
      .then(() => {
        return draft(row.id)
      })
      .then(() => {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
      });
  },

  rowAttach(row) {
    this.$refs.attachDialogRef.init(row.id, this.module);
  },

  handleSubmit(type) {
  this.form.status = 2
    if (type === 'add') {
      this.$refs.crud.rowSave()
    } else if (type === 'edit') {
      this.$refs.crud.rowUpdate()
    }
  },

  handleDelete() {
    if (this.selectionList.length === 0) {
      this.$message.warning("请选择至少一条数据");
      return;
    }
    const status = this.selectionList.some((item) => {
      return item.status === 2 || item.status === 3 || item.status === 3;
    });
    if (status) {
      this.$message.warning("选择的数据中存在已提交或已对账或已审核的数据，请重新选择");
      return;
    }

    this.$confirm("确定将选择数据删除?", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    })
      .then(() => {
        return remove(this.ids);
      })
      .then(() => {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
        this.$refs.crud.toggleSelection();
      });
  },

  ////批量对账//////////////////////////////////////////
  handleVerify() {
    if (this.selectionList.length === 0) {
      this.$message.warning("请选择至少一条数据");
      return;
    }

    let temp;
    temp = this.getStatus
    if (temp !== 0){
      this.$message.warning("有 " + temp + " 条数据的状态不是<已提交>，对账操作失败！！！");
      return;
    }

    temp = this.getCostDesc
    if (temp !== 0){
      this.$message.warning("有 " + temp + " 条数据的应付金额异常未处理，对账操作失败！！！");
      return;
    }

    this.$confirm("确定将选择数据进行对账?", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    })
      .then(() => {
        return verify(this.ids);
      })
      .then(() => {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
        this.$refs.crud.toggleSelection();
      });
  },


  beforeOpen(done, type) {
    /////////////////////////////默认当前时间
    if ("add" == type) {
      // this.form.headId = this.userInfo.user_id
      this.form = {
        registerDate: dateNow1(),
        headId: this.userInfo.user_id,
      }
    }

    if (["edit", "view"].includes(type)) {
      getDetail(this.form.id).then((res) => {
        if(res.data.data !== null)
          this.form = res.data.data;
      });
    }

    done();
  }
  ,
  searchReset() {
    this.query = {};
    this.onLoad(this.page);
  }
  ,
  //////////////////////////////////////////////搜索按钮
  searchChange(params, done) {
    this.query = params;
    this.page.currentPage = 1;

    //前端传值时添加参数
    if (params.registerDaterange && params.registerDaterange.length === 2){
        params.startTime1 = params.registerDaterange[0]; // 添加 startTime1 参数
        params.endTime1 = params.registerDaterange[1];
      }

    if (params.loadDate && params.loadDate.length === 2){
      params.startTime2 = params.loadDate[0]; // 添加 startTime2 参数
      params.endTime2 = params.loadDate[1];
    }

    this.onLoad(this.page, params);

    done();
  }
  ,
  selectionChange(list) {
    this.selectionList = list;
  }
  ,
  selectionClear() {
    this.selectionList = [];
    this.$refs.crud.toggleSelection();
  }
  ,
  currentChange(currentPage) {
    this.page.currentPage = currentPage;
  }
  ,
  sizeChange(pageSize) {
    this.page.pageSize = pageSize;
  }
  ,
  /////////刷新按钮的单击事件
  refreshChange() {
    this.onLoad(this.page, this.query);
  }
  ,
  onLoad(page, params = {}) {
    this.loading = true;
    getList(page.currentPage, page.pageSize, Object.assign(params, this.query,{descs:'id'})).then(res => {
    const data = res.data.data;
    this.page.total = data.total;/////////////记录总条数
    this.data = data.records;
    this.loading = false;
    this.selectionClear();
    });
  },

},

}
;
</script>

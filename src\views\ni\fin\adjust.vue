<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
      :cell-style="cellStyle"
    >
      <template #serialNo="{ row, index }">
        <flow-timeline-popover
          v-if="row.processInsId"
          :process-ins-id="row.processInsId"
          :process-def-key="processDefKey"
          v-model="row.serialNo"
          :flow.sync="row.flow"
          trigger="click"
          lazy
        />
        <span v-else>{{ row.serialNo }}</span>
      </template>
      <template #amount="{ row, index }">
        <span>{{
            Number(row.amount).toLocaleString("zh-CN", {
              minimumFractionDigits: 2,
            })
          }}</span>
      </template>
      <template #currency="{ row, size, index, dic }">
        <dict-tag
          v-if="row.currency"
          :size="size"
          v-model="row.currency"
          :dict="dic"
          :dict-props="{
            label: 'dictValue',
            value: 'dictKey',
          }"
        />
      </template>
      <template #status="{ row, size, index, dic }">
        <dict-tag
          v-if="row.status"
          :size="size"
          v-model="row.status"
          :dict="dic"
          :dict-props="{
            label: 'dictValue',
            value: 'dictKey',
          }"
        />
      </template>
      <template #brand="{ row, size, index, dic }">
        <dict-tag
          v-if="row.brand"
          :size="size"
          v-model="row.brand"
          :dict="dic"
          :dict-props="{
            label: 'dictValue',
            value: 'dictKey',
          }"
        />
      </template>
      <template #subType="{ row, size, index, dic }">
        <dict-tag
          v-if="row.subType"
          :size="size"
          v-model="row.subType"
          :dict="dic"
          :dict-props="{
            label: 'dictValue',
            value: 'dictKey',
          }"
        />
      </template>
      <template #menuLeft>
        <el-button
          type="warning"
          size="mini"
          icon="el-icon-position"
          plain
          v-if="permission.ni_fin_adjust_apply"
          @click="handleApply"
        >调账申请
        </el-button>
        <el-button
          type="danger"
          size="mini"
          icon="el-icon-delete"
          plain
          v-if="permission.ni_fin_adjust_delete"
          @click="handleDelete"
        >删 除
        </el-button>
        <el-button
          type="warning"
          size="mini"
          icon="el-icon-s-open"
          plain
          v-if="permission.ni_fin_adjust_used"
          @click="handleUsed"
        >标记已用
        </el-button>
        <!-- 打印 -->
        <el-button
          type="warning"
          size="mini"
          icon="el-icon-printer"
          plain
          v-if="permission.ni_fin_adjust_print"
          @click="handlePrint"
        >打 印
        </el-button>
      </template>
      <template #menu="{ row, size, index }">
        <el-button
          type="text"
          icon="el-icon-s-open"
          :size="size"
          v-if="9 === row.status && !row.used"
          @click="rowUsed(row, index)"
        >已用
        </el-button>
        <el-button
          type="text"
          icon="el-icon-refresh-left"
          :size="size"
          :style="{ color: 'red' }"
          v-if="9 === row.status && row.used"
          @click="rowUnUsed(row, index)"
        >未用
        </el-button>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import {
  getList,
  getDetail,
  add,
  update,
  remove,
  used,
} from "@/api/ni/fin/adjust";
import option from "@/const/ni/fin/finAdjust";
import {mapGetters} from "vuex";
import exForm from "@/views/plugin/workflow/mixins/ex-form";
import FlowTimelinePopover from "@/components/wf-flow-timeline/popover";
import {loadPrintTemplate} from "@/api/system/printTemplate";
import { hiprint } from "vue-plugin-hiprint";
import {numToCapital} from "@/util/util";

export default {
  mixins: [exForm],
  components: {FlowTimelinePopover},
  data() {
    return {
      processDefKey: "process_fin_adjust",
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: option,
      data: [],
      printTemplate:null
    };
  },
  computed: {
    ...mapGetters(["permission", "colorName"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.ni_fin_adjust_add, false),
        viewBtn: this.vaildData(this.permission.ni_fin_adjust_view, false),
        delBtn: this.vaildData(this.permission.ni_fin_adjust_delete, false),
        editBtn: this.vaildData(this.permission.ni_fin_adjust_edit, false),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  created() {
    loadPrintTemplate("ni_fin_adjust").then((res) => {
      this.printTemplate = JSON.parse(res.data.data.content);
    });
  },
  methods: {
    handleApply() {
      this.dynamicRoute(
        {
          processDefKey: this.processDefKey,
        },
        "start"
      );
    },
    rowSave(row, done, loading) {
      add(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          window.console.log(error);
        }
      );
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          console.log(error);
        }
      );
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    rowUnUsed(row, index) {
      this.$confirm("确定将选择数据标记为未用?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return used(row.id, false);
        })
        .then(() => {
          this.data[index].used = false;
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    rowUsed(row, index) {
      this.$confirm("确定将选择数据标记为已用?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return used(row.id, true);
        })
        .then(() => {
          this.data[index].used = true;
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    handlePrint() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      } else if (this.selectionList.length > 1) {
        this.$message.warning("只能选择一条数据");
        return;
      }
      let hiprintTemplate;
      if (!this.printTemplate) {
        this.$message.error("打印模板加载失败，请联系管理员");
        return;
      }
      getDetail(this.selectionList[0].id).then((res) => {
        const printData = res.data.data;
        if(printData.brand==='1'){
          printData.title = "能特异调账申请单"
        }else if(printData.brand==='2'){
          printData.title = "至简调账申请单"
        }else if(printData.brand==='3'){
          printData.title = "演绎调账申请单"
        }
        printData.upperAmount=""
        printData.upperAmount = numToCapital(
          Number(printData.amount).toFixed(2)
        );
        printData.items=[{
          supplierName: printData.supplierName,
          subTypeName: printData.subTypeName,
          currencyName: printData.currencyName,
          amount: printData.amount,
          remark: printData.remark
        }]
        hiprintTemplate = new hiprint.PrintTemplate({
          template: this.printTemplate,
        });
        hiprintTemplate.print(printData);
      });
    },
    handleUsed() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据标记为已用?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return used(this.ids, true);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen(done, type) {
      if ("add" === type) {
        this.form.type = "1";
        this.form.currency = "RMB";
        this.form.brand = "1";
      }
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then((res) => {
          this.form = res.data.data;
        });
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query, {type: "1", descs: "id"})
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
    cellStyle({row, column}) {
      if ("createUserName" === column.columnKey && row.used) {
        return {
          backgroundColor: this.colorName,
          color: "#fff",
          fontWeight: "bold",
        };
      }
    },
  },
};
</script>

<style></style>

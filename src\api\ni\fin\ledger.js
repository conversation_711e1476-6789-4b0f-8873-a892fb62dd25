import request from '@/router/axios';

export const getPage = (current, size, params) => {
  return request({
    url: '/api/ni/fin/ledger/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/ni/fin/ledger/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/ni/fin/ledger/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/ni/fin/ledger/save',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/ni/fin/ledger/update',
    method: 'post',
    data: row
  })
}

export const submit = (ids) => {
  return request({
    url: '/api/ni/fin/ledger/submit',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const back = (ids) => {
  return request({
    url: '/api/ni/fin/ledger/back',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const toVoid = (ids) => {
  return request({
    url: '/api/ni/fin/ledger/toVoid',
    method: 'post',
    params: {
      ids,
    }
  })
}

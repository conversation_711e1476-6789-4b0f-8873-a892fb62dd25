<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
      :cell-style="cellStyle"
    >
      <template #menuLeft>
        <el-button
          type="danger"
          size="mini"
          icon="el-icon-delete"
          plain
          v-if="permission.niMealTakeoutDeliveryRules_delete"
          @click="handleDelete"
          >删 除
        </el-button>
      </template>
      <template #menu="{ row, size, index }">
        <el-button
          type="text"
          icon="el-icon-edit"
          size="mini"
          v-if="
            permissionList.editBtn &&
            1 === row.status &&
            (row.mandatory === 2 || userInfo.role_name.includes('admin'))
          "
          @click="$refs.crud.rowEdit(row, index)"
          >编 辑
        </el-button>
        <el-button
          type="text"
          icon="el-icon-delete"
          :size="size"
          v-if="
            permissionList.delBtn &&
            1 === row.status &&
            (row.mandatory === 2 || userInfo.role_name.includes('admin'))
          "
          @click="$refs.crud.rowDel(row, index)"
          >删 除
        </el-button>
        <el-button
          type="text"
          icon="el-icon-video-play"
          size="mini"
          :style="{ color: colorName }"
          v-if="
            row.status === 2 &&
            (row.mandatory === 2 || userInfo.role_name.includes('admin'))
          "
          @click="rowPlay(row)"
          >启用
        </el-button>
        <el-button
          type="text"
          icon="el-icon-video-pause"
          size="mini"
          style="color: #f56c6c"
          v-if="
            row.status === 1 &&
            (row.mandatory === 2 || userInfo.role_name.includes('admin'))
          "
          @click="rowPause(row)"
          >停用
        </el-button>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import {
  add,
  getDetail,
  getList,
  pause,
  play,
  remove,
  update,
} from "@/api/ni/meal/takeout-delivery-rules";
import { mapGetters } from "vuex";

export default {
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        editBtn: false,
        delBtn: false,
        span: 12,
        dialogDrag: true,
        size: "mini",
        searchSize: "mini",
        align: "center",
        searchIndex: 3,
        searchIcon: true,
        searchEnter: true,
        height: "auto",
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        selection: true,
        dialogClickModal: false,
        column: [
          {
            label: "规则名称",
            prop: "name",
            type: "input",
            search: true,
            minWidth: 120,
          },
          {
            label: "优先级",
            prop: "priority",
            type: "number",
            search: true,
            minWidth: 80,
          },
          {
            label: "对象类型",
            prop: "targetType",
            type: "select",
            search: true,
            minWidth: 90,
            dicData: [
              {
                label: "部门",
                value: "1",
              },
              {
                label: "人员",
                value: "2",
              },
            ],
            rules: [
              {
                required: true,
                message: "请选择对象类型",
                trigger: "blur",
              },
            ],
            control: (val) => {
              if (val === "1") {
                return {
                  deptId: {
                    display: true,
                  },
                  targetId: {
                    display: false,
                  },
                };
              } else {
                return {
                  deptId: {
                    display: false,
                  },
                  targetId: {
                    display: true,
                  },
                };
              }
            },
          },
          {
            label: "对象",
            prop: "targetId",
            type: "select",
            dicUrl: "/api/ni/meal/takeout/user-groups/list",
            props: {
              label: "name",
              value: "id",
            },
            display: true,
            dicFormatter: (data) => {
              return data.data.records;
            },
            hide: true,
            showColumn: false,
            rules: [
              {
                required: true,
                message: "请选择用户组",
                trigger: "blur",
              },
            ],
          },
          {
            label: "部门选择",
            prop: "deptId",
            type: "tree",
            dicUrl: "/api/blade-system/dept/tree",
            props: {
              label: "title",
              value: "id",
            },
            display: false,
            hide: true,
            showColumn: false,
            rules: [
              {
                required: true,
                message: "请选择部门",
                trigger: "blur",
              },
            ],
          },
          {
            label: "对象名称",
            prop: "targetName",
            minWidth: 120,
            overHidden: true,
            display: false,
          },
          {
            label: "送餐地",
            prop: "locationId",
            type: "select",
            dicUrl: "/api/ni/meal/takeout/locations/list?status=1&size=1000",
            props: {
              label: "name",
              value: "id",
            },
            dicFormatter: (data) => {
              return data.data.records;
            },
            search: true,
            minWidth: 100,
          },
          {
            label: "强制规则",
            labelTip: "强制规则不允许修改",
            prop: "mandatory",
            type: "radio",
            dicUrl: "/api/blade-system/dict/dictionary?code=yes_no",
            value: "1",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            dataType: "number",
            minWidth: 100,
          },
          {
            label: "备注",
            prop: "remark",
            type: "textarea",
            span: 24,
            minRows: 2,
            minWidth: 130,
          },
          {
            label: "状态",
            prop: "status",
            type: "select",
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_meal_takeout_status",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            dataType: "number",
            search: true,
            addDisplay: false,
            editDisplay: false,
            minWidth: 110,
          },
        ],
      },
      data: [],
    };
  },
  computed: {
    ...mapGetters(["permission", "colorName", "userInfo"]),
    permissionList() {
      return {
        addBtn: this.vaildData(
          this.permission.niMealTakeoutDeliveryRules_add,
          false
        ),
        viewBtn: this.vaildData(
          this.permission.niMealTakeoutDeliveryRules_view,
          false
        ),
        delBtn: this.vaildData(
          this.permission.niMealTakeoutDeliveryRules_delete,
          false
        ),
        editBtn: this.vaildData(
          this.permission.niMealTakeoutDeliveryRules_edit,
          false
        ),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  methods: {
    rowPause(row) {
      this.$confirm("确定停用选择数据?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return pause(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    rowPlay(row) {
      this.$confirm("确定启用选择数据?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return play(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    rowSave(row, done, loading) {
      add(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          window.console.log(error);
        }
      );
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          console.log(error);
        }
      );
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen(done, type) {
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then((res) => {
          this.form = res.data.data;
        });
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
    cellStyle({ row, column }) {
      if ("status" === column.columnKey) {
        return {
          backgroundColor: row.status === 2 ? "#F56C6C" : this.colorName,
          color: "#fff",
        };
      }
    },
  },
};
</script>

<style></style>

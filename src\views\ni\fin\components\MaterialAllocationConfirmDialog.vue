<template>
  <el-dialog
    ref="invoice-dialog"
    v-dialogdrag
    custom-class="invoice-dialog"
    :visible.sync="visible"
    width="60%"
    :before-close="handleClose"
    append-to-body
  >
    <avue-crud
      v-if="visible"
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      v-model="form"
      ref="crud"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionList = $event"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <!-- <template #catalogId="{ row }">
        <span
          v-if="row.catalogNo"
          :style="{
            backgroundColor: getColor(row.catalogNo),
            borderRadius: '50%',
            width: '20px',
            height: '20px',
            display: 'inline-block',
            textAlign: 'center',
            lineHeight: '20px',
            color: 'white',
          }"
        >
          {{ row.catalogNo }}
        </span>
        {{ row.catalogName }}
      </template> -->
      <template #catalogId="{ row }">
        <div v-if="row.catalogName">
          <div
            v-for="(name, index) in row.catalogName.split(',')"
            :key="index"
            class="catalog-item"
          >
            <span>{{ name.trim() }}</span>
          </div>
        </div>
        <span v-else>无</span>
      </template>
      <template #projectTitle="{ row }">
          <div v-for="(title, index) in row.projectTitle.split(',')" :key="index">
            <span v-if="title">{{ title }}</span>
          </div>
      </template>
      <template #projectSerialNo="{ row }">
          <div v-for="(serialNo, index) in row.projectSerialNo.split(',')" :key="index">
            <span v-if="serialNo">{{ serialNo }}</span>
          </div>
      </template>
      <template #notice="{ row }">
        <span v-if="row.notice">
          {{row.leaderName}}
        </span>
        <span v-else>
          未通知
        </span>
      </template>
      <template #menuLeft>
        <el-button
          type="success"
          size="mini"
          icon="el-icon-download"
          plain
          @click="handleExport"
          >导出
        </el-button>
        <el-button
          type="warning"
          size="mini"
          icon="el-icon-bell"
          @click="handleConfirmNotice"
          >一键通知
          </el-button>
      </template>
    </avue-crud>
  </el-dialog>
</template>
<script>
import { getConfirmPage, confirmNotice } from "@/api/ni/fin/material-allocation";
import { dateFormat } from "@/util/date";
import NProgress from "nprogress";
import { exportBlob } from "@/api/common";
import { downloadXls } from "@/util/util";

export default {
  data() {
    return {
      isInit: false,
      visible: false,
      form: {},
      query: {},
      loading: false,
      selectionList: [],
      data: [],
      page: {
        currentPage: 1, // 当前页
        pageSize: 10, // 每页条数
        total: 0, // 总条数
      },
      // props: {
      //   id: "id",
      //   records: "data.data.records",
      //   total: "data.data.total",
      // },
      option: {
        index: true,
        menu: false,
        addBtn: false,
        saveBtn: true,
        updateBtn: true,
        cancelBtn: false,
        editBtn: true,
        delBtn: false,
        viewBtn: false,
        labelWidth: 110,
        span: 8,
        searchIndex: 3,
        searchIcon: true,
        size: "mini",
        searchSize: "mini",
        align: "center",
        height: "auto",
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        dialogClickModal: false,
        showSummary: true,
        sumColumnList: [
          { label: "金额总计:", name: "amount", type: "sum", decimals: 2 },
          { label: "不含税金额总计:", name: "unTax", type: "sum", decimals: 2 },
        ],
        column: [
          {
            label: "确认月份",
            prop: "month",
            type: "month",
            format:"yyyy-MM",
            valueFormat:"yyyy-MM",
            search:true,
            hide:true,
            showColumn:false,
            display: true,
          },
          {
            label: "发票日期",
            prop: "date",
            overHidden: true,
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
          },
          {
            label: "确认日期",
            prop: "confirmDate",
            overHidden: true,
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
          },
          {
            label: "确认日期范围",
            prop: "confirmDateRange",
            type: "daterange",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            startPlaceholder: "日期开始范围",
            endPlaceholder: "日期结束范围",
            search: true,
            searchRange: true,
            showColumn: false,
            hide: true,
            display: false,
          },
          {
            label: "发票日期范围",
            prop: "dateRange",
            type: "daterange",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            startPlaceholder: "日期开始范围",
            endPlaceholder: "日期结束范围",
            search: true,
            searchRange: true,
            showColumn: false,
            hide: true,
            display: false,
          },
          {
            label: "物料名称",
            prop: "materialName",
            minWidth: 120,
            overHidden: true,
            search: true,
            disabled: true,
          },
          {
            label: "规格型号",
            prop: "specification",
            overHidden: true,
            disabled: true,
          },
          {
            label: "数量",
            prop: "num",
            overHidden: true,
          },
          {
            label: "单位",
            prop: "unit",
            overHidden: true,
            type: "select",
            dicData: [],
            props: {
              label: "dictValue",
              value: "dictKey",
            },
          },
          {
            label: "单价",
            prop: "price",
            overHidden: true,
          },
          {
            label: "总价",
            prop: "amount",
            overHidden: true,
          },
          {
            label: "不含税",
            prop: "unTax",
            overHidden: true,
          },
          {
            label: "发票号",
            prop: "billSerialNo",
            minWidth: 120,
            overHidden: true,
            search: true,
          },
          {
            label: "项目名称（大类）",
            prop: "catalogId",
            minWidth: 120,
            overHidden: true,
            type: "select",
            // search: true,
            multiple: true,
            collapseTags: true,
            width: 220,
            dicUrl: "/api/ni/fin/material-allocation/getProjectCatalog",
            props: {
              label: "name",
              value: "id",
              desc: "no",
            },
            display: false,
          },
          {
            label: "项目名称（大类）",
            prop: "catalogName",
            minWidth: 120,
            overHidden: true,
            // search: true,
            hide: true,
            showColumn: true,
          },
          {
            label: "项目名称（小类）",
            prop: "projectTitle",
            minWidth: 120,
            overHidden: true,
            disabled: true,
            hide:true,
          },
          {
            label: "项目编号",
            prop: "projectSerialNo",
            minWidth: 120,
            overHidden: true,
            disabled: true,
            hide:true,
          },
          {
            label: "物料用途",
            prop: "purpose",
            overHidden: true,
            disabled: true,
          },
          {
            label:"通知",
            prop: "notice",
          }
        ],
      },
    };
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        // 对话框打开时重置状态
        const currentMonth = dateFormat(new Date(), "yyyy-MM");
        const monthColumn = this.findObject(this.option.column, "month");
        monthColumn.searchValue = currentMonth; // 重置搜索框显示值
        this.query = { month: currentMonth }; // 重置查询条件
        this.page.currentPage = 1; // 重置分页
        this.onLoad(this.page, this.query); // 重新加载数据
      }
    }
  },
  mounted() {},
  computed: {
    invoiceIds() {
      let ids = [];
      this.data.forEach((ele) => {
        ids.push(ele.invoiceId);
      });
      return ids.join(",");
    },
  },
  created() {
    const month = this.findObject(this.option.column, "month");
    month.searchValue = dateFormat(new Date(), "yyyy-MM");
    this.query.month = dateFormat(new Date(), "yyyy-MM");
  },
  methods: {
    //数据导出
    handleExport() {
      // if (!this.query.month) {
      //   this.$message({
      //     type: "warning",
      //     message: "请选择月份,并搜索当月数据",
      //   });
      //   return;
      // }
      this.$confirm("是否导出当前筛选的数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        NProgress.start();
        const q = this.query;
        q.currentPage = 1;
        q.size = 10000;
        q.confirmData = true;
        exportBlob(`/api/ni/fin/material-allocation/export`, q).then((res) => {
          if (q.startDate && q.endDate) {
            downloadXls(
              res.data,
              `${q.startDate.split(' ')[0]} —— ${ q.endDate.split(' ')[0] } 发票货物明细.xlsx`
            );
          } else {
            downloadXls(res.data, `${ q.month }发票货物明细.xlsx`);
          }
          NProgress.done();
        });
      });
    },
    // 发送通知
    handleConfirmNotice(){
      if (!this.query.month) {
        this.$message({
          type: "warning",
          message: "请选择月份,并搜索当月数据",
        });
        return;
      }
      this.$confirm("是否将本月已确认内容通知到项目设计负责人?（已通知后将不再发送通知）", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        const month = this.query.month;
        confirmNotice(month).then(() => {
          this.onLoad(this.page, this.query);
          this.$message({
            type: "success",
            message: "通知成功",
          });
        });
      });
    },
    //大项目编号获取颜色
    getColor(catalogNo) {
      const colorMap = {
        1: "#FF0000",
        2: "#FF7F00",
        3: "#B3EE3A",
        4: "#FFD700",
        5: "#96CDCD",
        6: "#7A378B",
        7: "#BC8F8F",
        8: "#BABABA",
        9: "#404040",
        10: "#A0522D",
        11: "#1874CD",
      };
      return colorMap[catalogNo] || "transparent";
    },
    handleClose(done) {
      this.visible = false;
      if (done && typeof done == "function") done();
    },
    searchReset() {
      this.query = {};
      this.onLoad();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    searchChange(params, done) {
      this.query = params;
      if (params.dateRange && params.dateRange.length === 2) {
        const startDate = new Date(params.dateRange[0]);
        const endDate = new Date(params.dateRange[1]);

        params.startDate = dateFormat(startDate, "yyyy-MM-dd") + " 00:00:00";
        params.endDate = dateFormat(endDate, "yyyy-MM-dd") + " 23:59:59";
      }
      if(params.confirmDateRange && params.confirmDateRange.length === 2) {
        const confirmStartDate = new Date(params.confirmDateRange[0]);
        const confirmEndDate = new Date(params.confirmDateRange[1]);

        params.confirmStartDate = dateFormat(confirmStartDate, "yyyy-MM-dd") + " 00:00:00";
        params.confirmEndDate = dateFormat(confirmEndDate, "yyyy-MM-dd") + " 23:59:59";

      }
      this.onLoad(params);
      done();
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    onLoad(page, params = {}) {
      this.loading = true;
      //   const q = {

      //     ...Object.assign(params, this.query),
      //     ...this.params,
      //   };
      getConfirmPage(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
      });
    },
  },
};
</script>
<style lang="scss">
.dialog-title {
  text-align: left;
  font-size: 14px;
  font-weight: bolder;
  overflow: hidden;
}

.invoice-dialog {
  display: flex;
  flex-direction: column;
  margin: 0 !important;
  position: absolute;
  top: 40%;
  left: 50%;
  transform: translate(-50%, -40%);
  max-height: calc(100% - 30px);
  max-width: calc(100% - 30px);

  .el-dialog__body {
    flex: 1;
    overflow: auto;
  }
}
</style>

<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
      @cell-click="cellClick"
      :cell-style="cellStyle"
    >
      <template #serialNo="{ row }">
        <flow-timeline-popover
          v-if="row.processInsId"
          :process-ins-id="row.processInsId"
          :process-def-key="processDefKey"
          v-model="row.serialNo"
          :flow.sync="row.flow"
          trigger="click"
          lazy
        />
        <span v-else>{{ row.serialNo }}</span>
      </template>
      <template #workDate="{ row, index }">
        <span>{{ row.workDateStart }}</span>
        <span v-if="row.workDateEnd && row.workDateEnd !== row.workDateStart"
          >~{{ row.workDateEnd }}
        </span>
      </template>
      <template #communication="{ row, index }">
        <el-tag
          v-if="row.communication"
          size="mini"
          type="danger"
          effect="dark"
        >
          是
        </el-tag>
        <el-tag v-else size="mini" type="info" effect="plain">否</el-tag>
      </template>
      <template #night="{ row, index }">
        <el-tag v-if="row.night" size="mini" type="danger" effect="dark">
          是
        </el-tag>
        <el-tag v-else size="mini" type="info" effect="plain">否</el-tag>
      </template>
      <template #picking="{ row, index }">
        <el-tag v-if="row.picking" size="mini" type="danger" effect="dark">
          是
        </el-tag>
        <el-tag v-else size="mini" type="info" effect="plain">否</el-tag>
      </template>
      <template #drawing="{ row, index }">
        <el-tag v-if="row.drawing" size="mini" type="danger" effect="dark">
          是
        </el-tag>
        <el-tag v-else size="mini" type="info" effect="plain">否</el-tag>
      </template>
      <template #assignedToForm="{ disabled, size, index, row }">
        <user-select
          v-model="form.assignedTo"
          :size="size"
          multiple
          :disabled="disabled"
        ></user-select>
      </template>
      <template #person="{ row, index }">
        <el-tag v-if="row.person" size="mini" type="danger" effect="dark">
          是
        </el-tag>
        <el-tag v-else size="mini" type="info" effect="plain">否</el-tag>
      </template>
      <template #appointUserName="{ row, index }">
        <span v-if="row.person">{{ row.appointUserName }}</span>
      </template>
      <template #status="{ row }">
        <el-tag v-if="row.status === 1" size="mini" effect="info">
          {{ row.$status }}
        </el-tag>
        <el-tag v-else-if="row.status === 2" size="mini" effect="plain">
          {{ row.$status }}
        </el-tag>
        <el-tag
          v-else-if="row.status === 91"
          size="mini"
          type="warning"
          effect="plain"
        >
          {{ row.$status }}
        </el-tag>
        <el-tag
          v-else-if="row.status === 92"
          size="mini"
          type="warning"
          effect="dark"
        >
          {{ row.$status }}
        </el-tag>
        <el-tag v-else-if="row.status === 3" size="mini" type="danger">
          {{ row.$status }}
        </el-tag>
        <el-tag
          v-else-if="row.status === 4"
          size="mini"
          type="warning"
          effect="plain"
        >
          {{ row.$status }}
        </el-tag>
        <el-tag
          v-else-if="row.status === 5"
          size="mini"
          type="warning"
          effect="plain"
        >
          {{ row.$status }}
        </el-tag>
        <el-tag
          v-else-if="row.status === 6"
          size="mini"
          type="danger"
          effect="plain"
        >
          {{ row.$status }}
        </el-tag>
        <el-tag
          v-else-if="row.status === 9"
          size="mini"
          type="success"
          effect="plain"
        >
          {{ row.$status }}
        </el-tag>
      </template>
      <template #dispatchDate="{ row, size, index }">
        <el-tag v-if="row.dispatchDate" :size="size" type="danger" effect="dark"
          >{{ row.dispatchDate }}
        </el-tag>
      </template>
      <template #applyWorkingHours="{ row, index }">
        <span v-if="row.applyWorkingHours">{{
          row.applyWorkingHours + "H"
        }}</span>
      </template>
      <template #projectIdForm="{ disabled, size, index, row }">
        <project-select
          v-model="form.projectId"
          :size="size"
          :params="{ status: 9 }"
          :disabled="disabled"
          @confirm="projectConfirm"
        />
      </template>
      <template #menuLeft>
        <!--        <el-button-->
        <!--          type="primary"-->
        <!--          v-if="permissionList.applyBtn"-->
        <!--          size="mini"-->
        <!--          icon="el-icon-s-promotion"-->
        <!--          @click="handleApply"-->
        <!--          >用工申请-->
        <!--        </el-button>-->
        <el-button
          type="primary"
          size="mini"
          v-if="permissionList.applyBtn"
          icon="el-icon-s-promotion"
          @click="handleApply1"
          >用工申请
        </el-button>
        <el-button
          type="danger"
          size="mini"
          icon="el-icon-delete"
          plain
          v-if="permissionList.delBtn"
          @click="handleDelete"
          >删 除
        </el-button>
      </template>
      <template #menuRight="{ size }">
        <el-button
          icon="el-icon-time"
          circle
          :size="size"
          @click="handleLog"
        ></el-button>
      </template>
      <template #menu="{ row, index, size }">
        <el-button
          type="text"
          icon="el-icon-edit"
          :size="size"
          v-if="
            [1, 4].includes(row.status) &&
            (row.createUser === userInfo.user_id ||
              userInfo.role_name.includes('admin'))
          "
          @click="rowEdit(row)"
          >编 辑
        </el-button>
        <el-button
          type="text"
          :size="size"
          icon="el-icon-delete"
          v-if="
            [1, 4].includes(row.status) &&
            (row.createUser === userInfo.user_id ||
              userInfo.role_name.includes('admin'))
          "
          @click="$refs.crud.rowDel(row, index)"
          >删 除
        </el-button>
        <el-button
          type="text"
          :size="size"
          icon="el-icon-refresh-left"
          v-if="
            row.status === 2 &&
            (row.createUser === userInfo.user_id ||
              userInfo.role_name.includes('admin'))
          "
          @click="rowQuash(row)"
          >撤销
        </el-button>
        <el-button
          type="text"
          :size="size"
          v-if="
            row.status === 1 &&
            (row.createUser === userInfo.user_id ||
              userInfo.role_name.includes('admin'))
          "
          icon="el-icon-s-promotion"
          @click="rowPublish(row)"
          >发布
        </el-button>
        <el-button
          type="text"
          size="mini"
          icon="el-icon-info"
          @click="rowDispatch(row)"
          >派工明细
        </el-button>
        <el-button
          type="text"
          size="mini"
          icon="el-icon-s-promotion"
          v-if="
            permissionList.applyBtn &&
            (!row.parentId || row.parentId === 0) &&
            [2, 9, 91].includes(row.status)
          "
          @click="rowApply(row)"
          >用工申请
        </el-button>
      </template>
    </avue-crud>
    <dispatch-item-drawer ref="dispatchItemDrawerRef" />
    <project-employ-form-dialog
      ref="projectEmployFormDialogRef"
      @confirm="onLoad(page)"
    />
    <el-dialog
      title="修改用工类型"
      append-to-body
      :visible.sync="opType.visible"
      width="350px"
    >
      <avue-form
        :option="opType.option"
        v-model="opType.form"
        @submit="handleOpTypeChangeSubmit"
      >
      </avue-form>
    </el-dialog>
    <log-opt-dialog ref="logOptDialogRef" :module="module" />
  </basic-container>
</template>

<script>
import {
  add,
  changeOpType,
  getDetail,
  getPage,
  publish,
  remove,
  update,
  revokePublish,
  publish1,
  revokePublish1,
} from "@/api/ni/project/employ";
import { mapGetters } from "vuex";
import ProjectSelect from "@/views/ni/project/components/MyProjectSelect";
import exForm from "@/views/plugin/workflow/mixins/ex-form";
import DispatchItemDrawer from "@/views/ni/project/components/DispatchItemDrawer";
import FlowTimelinePopover from "@/components/wf-flow-timeline/popover.vue";
import UserSelect from "@/views/plugin/workflow/process/components/user-select.vue";
import ProjectEmployFormDialog from "./components/ProjectEmployFormDialog";
import LogOptDialog from "@/components/log-opt-dialog/index.vue";

export default {
  mixins: [exForm],
  components: {
    LogOptDialog,
    UserSelect,
    FlowTimelinePopover,
    ProjectSelect,
    DispatchItemDrawer,
    ProjectEmployFormDialog,
  },
  data() {
    return {
      module: "ni_project_employ",
      processDefKey: "process_ni_project_employ",
      startShow: false,
      finishShow: false,
      row: {},
      dispatchShow: false,
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        menuWidth: 180,
        searchEnter: true,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        searchIndex: 3,
        searchIcon: true,
        labelWidth: 135,
        searchLabelWidth: 135,
        size: "mini",
        searchSize: "mini",
        align: "center",
        height: "auto",
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: false,
        viewBtn: false,
        selection: true,
        dialogClickModal: false,
        column: [
          {
            label: "状态",
            prop: "status",
            type: "select",
            search: true,
            width: 82,
            dicData: [],
            span: 8,
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            dataType: "number",
            addDisplay: false,
            editDisplay: false,
          },
          {
            label: "申请人",
            prop: "createUser",
            type: "select",
            remote: true,
            dicUrl: `/api/blade-user/search/otj/user?name={{key}}`,
            props: {
              label: "realName",
              value: "id",
            },
            dicFormatter: (data) => {
              return data.data.records;
            },
            placeholder: " ",
            filterable: true,
            display: false,
            hide: true,
            showColumn: false,
            search: true,
          },
          {
            label: "申请人",
            prop: "createUserName",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            disabled: true,
            span: 8,
            width: 80,
          },
          {
            label: "申请部门",
            prop: "createDeptName",
            display: false,
            span: 8,
          },
          {
            label: "申请时间",
            width: 100,
            overHidden: true,
            display: false,
            prop: "createTime",
            type: "datetime",
            format: "yyyy-MM-dd hh:mm:ss",
            valueFormat: "yyyy-MM-dd hh:mm:ss",
            span: 8,
          },
          {
            label: "用工单号",
            prop: "serialNo",
            placeholder: "系统自动生成",
            searchPlaceholder: " ",
            overHidden: true,
            width: 110,
            disabled: true,
            span: 8,
            search: true,
          },
          {
            label: "关联单号",
            prop: "parentSerialNo",
            overHidden: true,
            width: 110,
            span: 8,
          },
          {
            label: "选择项目",
            prop: "projectId",
            hide: true,
            showColumn: false,
            span: 8,
            rules: [
              {
                required: true,
                message: "请选择项目",
                trigger: "blur",
              },
            ],
          },
          {
            label: "项目编号",
            searchLabel: "用工项目",
            searchLabelTip: "名称/编号",
            prop: "projectSerialNo",
            overHidden: true,
            addDisplay: false,
            editDisplay: false,
            width: 95,
            search: true,
            span: 8,
          },
          {
            label: "负责人",
            prop: "chargerId",
            type: "select",
            remote: true,
            dicUrl: `/api/blade-user/search/otj/user?name={{key}}`,
            props: {
              label: "realName",
              value: "id",
            },
            dicFormatter: (data) => {
              return data.data.records;
            },
            placeholder: " ",
            filterable: true,
            hide: true,
            showColumn: false,
            search: true,
          },
          {
            label: "负责人",
            prop: "chargerName",
            display: false,
            overHidden: true,
            width: 80,
          },
          {
            label: "用工类型",
            prop: "opType",
            type: "select",
            dicData: [],
            width: 80,
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            search: true,
            span: 8,
            rules: [
              {
                required: true,
                message: "请选择用工类型",
                trigger: "blur",
              },
            ],
          },
          {
            label: "用工日期",
            prop: "workDate",
            search: true,
            searchRange: true,
            overHidden: true,
            span: 8,
            width: 165,
            type: "date",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            rules: [
              {
                required: true,
                message: "请输入用工日期",
                trigger: "blur",
              },
            ],
          },
          {
            label: "派工内容",
            prop: "content",
            type: "textarea",
            search: true,
            overHidden: true,
            span: 24,
            minRows: 2,
            minWidth: 120,
            rules: [
              {
                required: true,
                message: "请输入派工内容",
                trigger: "blur",
              },
            ],
          },
          {
            label: "用工数",
            prop: "personNum",
            type: "number",
            span: 8,
            width: 80,
            rules: [
              {
                required: true,
                message: "请输入用工数",
                trigger: "blur",
              },
            ],
          },
          {
            label: "预计工时",
            prop: "applyWorkingHours",
            type: "number",
            width: 70,
            span: 8,
            rules: [
              {
                required: true,
                message: "请输入 预计工时",
                trigger: "blur",
              },
            ],
            dataType: "number",
            min: 0,
            max: 12,
            row: true,
            minWidth: 90,
          },
          {
            label: "夜班",
            prop: "night",
            value: true,
            search: true,
            span: 8,
            type: "radio",
            width: 60,
            dicData: [
              {
                label: "否",
                value: 0,
              },
              {
                label: "是",
                value: 1,
              },
            ],
            placeholder: " ",
            rules: [
              {
                required: true,
                message: "请选择是否夜班",
                trigger: "blur",
              },
            ],
          },
          {
            label: "材料备齐",
            prop: "ready",
            value: true,
            span: 8,
            type: "radio",
            minWidth: 70,
            hide: true,
            dicData: [
              {
                label: "否",
                value: 0,
              },
              {
                label: "是",
                value: 1,
              },
            ],
            placeholder: " ",
            rules: [
              {
                required: true,
                message: "请选择材料是否备齐",
                trigger: "blur",
              },
            ],
          },
          {
            label: "是否指定人员",
            prop: "person",
            value: 0,
            span: 8,
            type: "radio",
            minWidth: 70,
            hide: true,
            row: true,
            dicData: [
              {
                label: "否",
                value: 0,
              },
              {
                label: "是",
                value: 1,
              },
            ],
            placeholder: " ",
            rules: [
              {
                required: true,
                message: "请选择指定人员",
                trigger: "blur",
              },
            ],
          },
          {
            label: "指定人员",
            prop: "appointUserId",
            placeholder: " ",
            span: 8,
            row: true,
            hide: true,
            showColumn: false,
            rules: [
              {
                required: true,
                message: "请选择指定人员",
                trigger: "blur",
              },
            ],
          },
          {
            label: "指定人员",
            prop: "appointUserName",
            display: false,
            overHidden: true,
            width: 100,
            span: 8,
          },
          {
            label: "是否落实图纸",
            prop: "drawing",
            value: 0,
            hide: true,
            span: 8,
            type: "radio",
            minWidth: 70,
            dicData: [
              {
                value: 0,
                label: "不需要",
              },
              {
                label: "否",
                value: -1,
              },
              {
                label: "是",
                value: 1,
              },
            ],
            placeholder: " ",
            rules: [
              {
                required: true,
                message: "请选择是否需落实图纸",
                trigger: "blur",
              },
            ],
          },
          {
            type: "radio",
            label: "是否与生产沟通",
            hide: true,
            dicData: [
              {
                value: 0,
                label: "不需要",
              },
              {
                label: "是",
                value: 1,
              },
              {
                value: -1,
                label: "否",
              },
            ],
            span: 8,
            display: true,
            props: {
              label: "label",
              value: "value",
            },
            prop: "communication",
            value: 0,
            required: true,
            rules: [
              {
                required: true,
                message: "是否于生产沟通必须填写",
              },
            ],
          },
          {
            label: "是否需领料",
            prop: "picking",
            value: false,
            span: 8,
            hide: true,
            type: "radio",
            minWidth: 70,
            dicData: [
              {
                label: "否",
                value: false,
              },
              {
                label: "是",
                value: true,
              },
            ],
            placeholder: " ",
            rules: [
              {
                required: true,
                message: "请选择是否需领料",
                trigger: "blur",
              },
            ],
            control: (val) => {
              if (val) {
                return {
                  materials: {
                    display: true,
                  },
                };
              } else {
                return {
                  materials: {
                    display: false,
                  },
                };
              }
            },
          },
          {
            label: "领料明细",
            prop: "materials",
            labelPosition: "top",
            display: false,
            span: 24,
            hide: true,
            showColumn: false,
          },
          {
            label: "指派工时",
            prop: "dispatchHours",
            type: "number",
            span: 8,
            width: 70,
          },
          {
            label: "完工工时",
            prop: "dispatchFinishHours",
            type: "number",
            span: 8,
            width: 70,
          },
        ],
      },
      data: [],
      opTypeDict: [],
      opTypeDictKeyValue: {},
      detailVisible: false,
      opType: {
        visible: false,
        form: {},
        option: {
          span: 24,
          size: "mini",
          searchSize: "mini",
          emptyBtn: false,
          column: [
            {
              label: "用工类型",
              prop: "opType",
              type: "select",
              dicUrl:
                "/api/blade-system/dict-biz/dictionary?code=ni_project_dispatch_type",
              props: {
                label: "dictValue",
                value: "dictKey",
              },
              span: 24,
              rules: [
                {
                  required: true,
                  message: "请选择用工类型",
                  trigger: "blur",
                },
              ],
            },
          ],
        },
      },
    };
  },
  computed: {
    ...mapGetters(["permission", "userInfo", "colorName"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.projectdispatch_add, false),
        viewBtn: this.vaildData(this.permission.projectdispatch_view, false),
        delBtn: this.vaildData(this.permission.projectdispatch_delete, false),
        editBtn: this.vaildData(this.permission.projectdispatch_edit, false),
        applyBtn: this.vaildData(this.permission.projectdispatch_apply, false),
        dispatchBtn: this.vaildData(
          this.permission.projectdispatch_dispatch,
          false
        ),
        confirmBtn: this.vaildData(
          this.permission.projectdispatch_audit,
          false
        ),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  created() {
    this.dictInit();
  },
  methods: {
    rowEdit(row) {
      this.$refs.projectEmployFormDialogRef.onEdit(row.id);
    },
    rowPublish(row) {
      this.$confirm("确定将要发布该用工申请?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return publish1(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    rowDispatch(row) {
      this.$refs.dispatchItemDrawerRef.onShow(row.id);
    },
    rowQuash(row) {
      this.$confirm("确定将要取消该用工申请?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return revokePublish1(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    handleLog() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择要查看的数据");
        return;
      } else if (this.selectionList.length > 1) {
        this.$message.warning("只能选择一条数据");
        return;
      }
      this.$refs.logOptDialogRef.init(this.selectionList[0].id);
    },
    projectConfirm(selectionList) {
      if (selectionList) {
        if (
          this.$refs.crud &&
          "[object Function]" === toString.call(this.$refs.crud.clearValidate)
        ) {
          this.$refs.crud.clearValidate(["projectId"]);
        }
      }
    },
    dictInit() {
      this.$http
        .get(
          "/api/blade-system/dict-biz/dictionary?code=ni_project_employ_status"
        )
        .then((res) => {
          const column = this.findObject(this.option.column, "status");
          column.dicData = res.data.data;
        });
      this.$http
        .get(
          "/api/blade-system/dict-biz/dictionary?code=ni_project_dispatch_type"
        )
        .then((res) => {
          const column = this.findObject(this.option.column, "opType");
          column.dicData = res.data.data;
          this.opTypeDict = res.data.data;
          this.opTypeDictKeyValue = this.opTypeDict.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });
    },
    rowSave(row, done, loading) {
      if (row.assignedTo) {
        row.assignedTo = row.assignedTo.join(",");
      }
      add(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          window.console.log(error);
        }
      );
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          console.log(error);
        }
      );
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    handleApply() {
      this.dynamicRoute(
        {
          processDefKey: this.processDefKey,
        },
        "start"
      );
    },
    handleApply1() {
      this.$refs.projectEmployFormDialogRef.onApply();
    },
    rowApply(row) {
      const form = {
        parentId: row.id,
        parentSerialNo: row.serialNo,
        projectId: row.projectId,
      };
      this.$refs.projectEmployFormDialogRef.onApply(form);
      // this.dynamicRoute(
      //   {
      //     processDefKey: this.processDefKey,
      //     form: encodeURIComponent(
      //       Buffer.from(JSON.stringify(form)).toString("utf8")
      //     ),
      //   },
      //   "start"
      // );
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen(done, type) {
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then((res) => {
          this.form = res.data.data;
          if (this.form.projectId) {
            this.projectConfirm([{ id: this.form.projectId }]);
          }
        });
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      const q = Object.assign(params, this.query);
      if (q.workDate != null && q.workDate.length > 1) {
        q.workDateStart = q.workDate[0];
        q.workDateEnd = q.workDate[1];
        q.workDate = null;
      }
      q.descs = "id";
      getPage(page.currentPage, page.pageSize, q).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
    cellStyle({ row, column }) {
      if ("serialNo" === column.columnKey) {
        return {
          color: this.colorName,
          textDecoration: "underline",
          cursor: "pointer",
        };
      }
      if ("opType" === column.columnKey) {
        return {
          color: "#fff",
          backgroundColor: row.opType === "1" ? "#F56C6C" : this.colorName,
          textDecoration: row.status !== 2 ? "" : "underline",
          cursor: row.status !== 2 ? "" : "pointer",
        };
      }
    },
    cellClick(row, column) {
      if (column.property === "serialNo") {
        this.$refs.projectEmployFormDialogRef.onShow(row.id);
      }
      if (column.property === "opType" && row.status === 2) {
        this.handleOpTypeChange(row);
      }
    },
    handleOpTypeChange(row) {
      //TODO 判断是否已经派工，如果已经派工，则不能修改 如果还未派工则记录日志，进行修改
      if (row.status >= 9) {
        this.$message.warning("该条数据已经派工，无法修改");
        return;
      }
      this.opType.visible = true;
      this.opType.form = {
        id: row.id,
        opType: row.opType,
      };
    },
    handleOpTypeChangeSubmit(form, done) {
      changeOpType({ ids: form.id, opType: form.opType })
        .then((res) => {
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.opType.visible = false;
          this.onLoad(this.page);
        })
        .finally(() => {
          done();
        });
    },
  },
};
</script>

<style></style>

<script>
import InventorySummarySelectDialog from "@/views/ni/fg/components/InventorySummarySelectDialog.vue";
import {mapGetters} from "vuex";
import {dateFormat} from "@/util/date";
import {add, getDetail, receive, update, finish} from "@/api/ni/fg/fgTransfer";
import SkuSelect from "@/views/ni/product/components/SkuSelect.vue";
import {getList as getBatchNos} from "@/api/ni/fg/fgInventory";

export default {
  name: "TransferFormDialog",
  components: {SkuSelect, InventorySummarySelectDialog},
  data() {
    return {
      status: "add",
      statusMap: {
        add: "新增",
        edit: "编辑",
        view: "查看",
        receive: '收获'
      },
      visible: false,
      option: {
        submitBtn: false,
        emptyBtn: false,
        size: "mini",
        span: 8,
        labelWidth: 110,
        column: [
          {
            label: "调拨编号",
            prop: "serialNo",
            placeholder: "系统自动生成",
            overHeight: true,
            search: true,
          },
          {
            label: "调拨主题",
            prop: "title",
            overHeight: true,
            search: true,
            rules: [
              {
                required: true,
                message: "请输入调拨主题",
                trigger: "blur",
              },
            ],
          },
          {
            label: "调出仓库",
            prop: "fromDepotId",
            type: "select",
            dicUrl: "/api/ni/base/depot/info/list?status=2&type=1",
            props: {
              label: "name",
              value: "id",
              desc: "code",
            },
            rules: [
              {
                required: true,
                message: "请选择调出仓库",
                trigger: "blur",
              },
            ],
          },
          {
            label: "调入仓库",
            prop: "toDepotId",
            type: "select",
            dicUrl: "/api/ni/base/depot/info/list?status=2&type=1",
            props: {
              label: "name",
              value: "id",
              desc: "code",
            },
            search: true,
            rules: [
              {
                required: true,
                message: "请选择调入仓库",
                trigger: "blur",
              },
            ],
          },
          {
            label: "操作人",
            prop: "operator",
            type: "select",
            remote: true,
            dicUrl: `/api/blade-user/search/otj/user?name={{key}}`,
            props: {
              label: "realName",
              value: "id",
            },
            dicFormatter: (data) => {
              return data.data.records;
            },
            placeholder: " ",
            filterable: true,
            overHidden: true,
            rules: [
              {
                required: true,
                message: "请选择操作人",
                trigger: "blur",
              },
            ],
            search: true,
            hide: true,
            showColumn: false,
          },
          {
            label: "操作时间",
            prop: "transferDate",
            search: true,
            type: "date",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            searchRange: true,
            placeholder: " ",
            clearable: false,
            minWidth: 120,
            rules: [
              {
                required: true,
                message: "请选择操作时间",
                trigger: "blur",
              },
            ],
          },
          {
            label: '备注',
            prop: 'remark',
            type: 'textarea',
            placeholder: ' ',
            span: 24,
            minRows: 2,
          },
          {
            label: "调拨状态",
            prop: "status",
            type: "select",
            dicUrl: "/api/blade-system/dict-biz/dictionary?code=ni_fg_transfer_status",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            display: false,
          },
          {
            label: "调拨明细",
            prop: "items",
            span: 24,
            type: "dynamic",
            hide: true,
            showColumn: false,
            children: {
              rowAdd: () => {
                this.handleInventorySelect()
              },
              addBtn: true,
              delBtn: true,
              size: "mini",
              align: "center",
              headerAlign: "center",
              showSummary: true,
              sumColumnList: [
                {
                  name: 'fromNum',
                  type: 'sum',
                  decimals: 1
                },
                {
                  name: 'fromWeight',
                  type: 'sum',
                  decimals: 1
                },
              ],
              column: [
                {
                  label: '存货编码',
                  prop: 'materialCode',
                  placeholder: " ",
                  cell: false,
                  minWidth: 120
                },
                {
                  label: '规格',
                  prop: 'specText',
                  placeholder: " ",
                  display: false,
                  overHidden: true,
                  cell: false,
                  minWidth: 100
                },

                {
                  label: '外包装',
                  prop: 'packageText',
                  placeholder: " ",
                  display: false,
                  overHidden: true,
                  cell: false,
                  minWidth: 115
                },
                {
                  label: '内包装',
                  prop: 'innerPackageText',
                  placeholder: " ",
                  display: false,
                  overHidden: true,
                  cell: false,
                  minWidth: 115
                },
                {
                  label: '质量',
                  prop: 'qualityLevel',
                  type: 'select',
                  dicUrl: "/api/blade-system/dict-biz/dictionary?code=ni_product_sku_quality_level",
                  props: {
                    label: "dictValue",
                    value: "dictKey",
                    desc: 'dictKey'
                  },
                  placeholder: " ",
                  width: 100,
                  cell: false
                },
                {
                  label: '批号',
                  prop: 'batchNo',
                  placeholder: " ",
                  minWidth: 100,
                },
                {
                  label: '生产日期',
                  prop: 'productionDate',
                  placeholder: " ",
                  minWidth: 110,
                  overHidden: true,
                  cell: false,
                },
                {
                  label: '当前库存',
                  prop: 'currentStock',
                  placeholder: " ",
                  minWidth: 100,
                  cell: false,
                },
                {
                  label: '调拨数量',
                  prop: 'fromNum',
                  placeholder: " ",
                  type: 'number',
                  minWidth: 110,
                  rules: [
                    {
                      required: true,
                      message: "请输入数量",
                      trigger: "blur",
                    },
                  ],
                },
                {
                  label: '调拨重量',
                  prop: 'fromWeight',
                  placeholder: " ",
                  minWidth: 110,
                  type: 'number',
                  cell: false
                },
                {
                  label: '收货数',
                  prop: 'toNum',
                  placeholder: " ",
                  type: 'number',
                  minWidth: 110,
                  rules: [
                    {
                      required: true,
                      message: "请输入收货数",
                      trigger: "blur",
                    },
                  ],
                },
                {
                  label: '收货重量',
                  prop: 'toWeight',
                  placeholder: " ",
                  minWidth: 110,
                  type: 'number',
                },
                {
                  label: "备注",
                  prop: "remark",
                  type: "textarea",
                  placeholder: " ",
                  minRows: 1,
                  overHidden: true,
                  minWidth: 120,
                },
                {
                  label: "#",
                  prop: "action",
                  width: 60,
                  fixed: 'right'
                }
              ]
            }
          },
        ]
      },
      form: {},
    }
  },
  computed: {
    ...mapGetters(['userInfo']),
    items() {
      return this.form.items || [];
    },
  },
  methods: {
    init() {
      this.option.detail = false
      const items = this.findObject(this.option.column, 'items')
      const currentStock = this.findObject(items.children.column, 'currentStock')
      currentStock.hide = false
      const serialNo = this.findObject(this.option.column, 'serialNo')
      serialNo.disabled = false
      const title = this.findObject(this.option.column, 'title')
      title.disabled = false
      const fromDepotId = this.findObject(this.option.column, 'fromDepotId')
      fromDepotId.disabled = false
      const toDepotId = this.findObject(this.option.column, 'toDepotId')
      toDepotId.disabled = false
      const operator = this.findObject(this.option.column, 'operator')
      operator.disabled = false
      const transferDate = this.findObject(this.option.column, 'transferDate')
      transferDate.disabled = false
      const remark = this.findObject(this.option.column, 'remark')
      remark.disabled = false
      items.children.addBtn = true
      items.children.delBtn = true
      const fromNum = this.findObject(items.children.column, 'fromNum')
      fromNum.cell = true
      const productionDate = this.findObject(items.children.column, 'productionDate')
      productionDate.hide = false
      const toNum = this.findObject(items.children.column, 'toNum')
      toNum.hide = true
      const toWeight = this.findObject(items.children.column, 'toWeight')
      toWeight.hide = true
      Object.keys(this.form).forEach(key => this.form[key] = null);
      this.form.items = []
    },
    onShow(id) {
      this.status = 'view'
      this.init()
      this.option.detail = true
      const items = this.findObject(this.option.column, 'items')
      const currentStock = this.findObject(items.children.column, 'currentStock')
      currentStock.hide = true
      const productionDate = this.findObject(items.children.column, 'productionDate')
      productionDate.hide = true
      const toNum = this.findObject(items.children.column, 'toNum')
      toNum.hide = false
      getDetail(id).then((res) => {
        this.form = res.data.data
        this.visible = true
      })
    },
    onEdit(id) {
      this.status = 'edit'
      this.init()
      this.option.detail = false
      const items = this.findObject(this.option.column, 'items')
      const currentStock = this.findObject(items.children.column, 'currentStock')
      currentStock.hide = false
      const productionDate = this.findObject(items.children.column, 'productionDate')
      productionDate.hide = false
      getDetail(id).then((res) => {
        this.form = res.data.data
        this.visible = true
      })
    },
    onAdd(depotId, inventories) {
      this.status = 'add'
      this.init()
      if (inventories) {
        this.handleInventorySelectConfirm(inventories)
      }
      this.form.fromDepotId = depotId
      this.form.operator = this.userInfo.user_id
      this.form.transferDate = dateFormat(new Date(), 'yyyy-MM-dd')
      this.visible = true
    },
    onReceive(id) {
      this.status = 'receive'
      this.init()
      const items = this.findObject(this.option.column, 'items')
      const currentStock = this.findObject(items.children.column, 'currentStock')
      currentStock.hide = true
      const serialNo = this.findObject(this.option.column, 'serialNo')
      serialNo.disabled = true
      const title = this.findObject(this.option.column, 'title')
      title.disabled = true
      const fromDepotId = this.findObject(this.option.column, 'fromDepotId')
      fromDepotId.disabled = true
      const toDepotId = this.findObject(this.option.column, 'toDepotId')
      toDepotId.disabled = true
      const operator = this.findObject(this.option.column, 'operator')
      operator.disabled = true
      const transferDate = this.findObject(this.option.column, 'transferDate')
      transferDate.disabled = true
      const remark = this.findObject(this.option.column, 'remark')
      remark.disabled = true
      items.children.addBtn = false
      items.children.delBtn = false
      const fromNum = this.findObject(items.children.column, 'fromNum')
      fromNum.cell = false
      const toNum = this.findObject(items.children.column, 'toNum')
      toNum.hide = false
      const toWeight = this.findObject(items.children.column, 'toWeight')
      toWeight.hide = false
      getDetail(id).then((res) => {
        this.form = res.data.data
        this.visible = true
      })
    },
    rowFromNumChange(value, row) {
      row.fromWeight = Number(value) * Number(row.capacity)
    },
    rowToNumChange(value, row) {
      row.toWeight = Number(value) * Number(row.capacity)
    },
    handleInventorySelect() {
      if (!this.form.fromDepotId) {
        this.$message({
          type: "warning",
          message: "请选择仓库!"
        })
        return
      }
      this.$refs.inventorySelectDialog.onShow()
    },
    handleInventorySelectConfirm(selectionList) {
      selectionList.forEach((item) => {
        this.items.push({
          fromInventoryId: item.id,
          skuId: item.skuId,
          skuText: item.skuText,
          specText: item.specText,
          packageText: item.packageText,
          innerPackageText: item.innerPackageText,
          materialId: item.materialId,
          materialCode: item.materialCode,
          currentStock: item.num,
          qualityLevel: item.qualityLevel,
          unit: item.unit,
          capacity: item.capacity,
        })
      })
    },
    handleFinish() {
      //如果存在验证不通过，msg为错误信息
      this.$refs.form.validate((valid, done,) => {
        if (valid) {
          this.$confirm("直接完成将会立即更新当前库存，是否继续?", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning"
          })
            .then(() => {
              return finish(this.form)
            })
            .then(() => {
              this.visible = false
              this.init()
              this.$emit('confirm', this.form)
              this.$message({
                type: "success",
                message: "操作成功!"
              });
            }).finally(() => {
            done()
          });
        } else {
          return false;
        }
      })
    },
    async handleConfirm(form, done) {
      if (!form.items || form.items.length === 0) {
        this.$message({
          type: "warning",
          message: "请添加明细!"
        })
        done()
        return;
      }
      let r = null;
      if (this.status === 'add') {
        //提醒未登记批号
        const hasNoBatchNo = this.items.some(item => !item.batchNo);
        if (hasNoBatchNo) {
          try {
            await this.$confirm("明细中存在未登记批号的数据，是否继续？", '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            })
            r = add(form)
          } catch (err) {
            done()
            return;
          }
        } else
          r = add(form)
      } else if (this.status === 'edit') {
        r = update(form)
      } else if (this.status === 'receive') {
        //校验items内部的toNum是否为0,如果有0的值，则弹确认窗
        const hasZero = this.items.some(item => item.toNum === 0)
        //校验是否存在收货数和数量不同的明细，弹窗提示
        const hasDiff = this.items.some(item => Number(item.toNum) !== Number(item.fromNum))
        let msg = ''
        if (hasZero) {
          msg = '明细中存在收货数是0的条目，确认收货？'
        } else if (hasDiff) {
          msg = '明细中存在收货数和调拨数量不同的条目，确认收货？'
        }
        if (msg) {
          try {
            await this.$confirm(msg, '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            })
            r = receive(form)
          } catch (err) {
            done()
            return;
          }
        } else {
          r = receive(form)
        }
      }
      if (r != null)
        r.then(() => {
          this.visible = false
          this.init()
          this.$emit('confirm', this.form)
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }).finally(() => {
          done()
        });
    },
    batchNoRemoteMethod(query, row) {
      if (query !== '') {
        row.batchNoLoading = true;
        getBatchNos(1, 20, {batchNo: query, skuId: row.skuId, status: 1}).then((res) => {
          row.batchNoLoading = false;
          const data = res.data.data;
          data.records.forEach((item) => {
            item.label = `${item.batchNo}(${item.depotName})`
          })
          row.batchNoOptions = data.records.filter(item => {
            return item.batchNo.toLowerCase()
              .indexOf(query.toLowerCase()) > -1
          });
        })
      } else {
        row.batchNoOptions = [];
      }
    },
    rowBatchNoChange(val, row) {
      if (row.batchNoOptions && row.batchNoOptions.length > 0) {
        const selectedItem = row.batchNoOptions.find(item => item.batchNo === val);
        if (selectedItem) {
          row.fromNum = selectedItem.num; // 将批号对应数量赋值
          row.productionDate = selectedItem.productionDate
          this.rowFromNumChange(row.fromNum, row)
        }
      }
    },
    rowCopy(row, index) {
      const copy = {
        ...row,
        id: null,
        productionDate: null,
        batchNo: null
      }
      this.form.items.splice(index + 1, 0, copy)
    },
  }
}
</script>

<template>
  <el-dialog :visible.sync="visible" :title="`${statusMap[status]}-调拨单`" fullscreen append-to-body>
    <avue-form v-if="visible" ref="form" :option="option" v-model="form" @submit="handleConfirm">
      <template #action="{row,index}">
        <el-button type="text" icon="el-icon-copy-document" @click="rowCopy(row,index)"></el-button>
      </template>
      <template #batchNo="{row,size,disabled}">
        <el-select
          :size="size"
          :disabled="disabled"
          v-model="row.batchNo"
          filterable
          remote
          allow-create
          placeholder=" "
          :remote-method="((query)=>{batchNoRemoteMethod(query,row)})"
          :loading="row.batchNoLoading"
          @change="rowBatchNoChange($event,row)"
        >
          <el-option
            v-for="item in row.batchNoOptions"
            :key="item.id"
            :label="item.batchNo"
            :value="item.batchNo">
            <span style="float: left;">{{ `${item.batchNo}(${item.num})` }}</span>
            <span style="float: right; color: #8492a6; font-size: 13px">{{ item.depotName }}</span>
          </el-option>
        </el-select>
      </template>
      <template #fromNum="{row,size,disabled}">
        <el-input-number
          v-model="row.fromNum"
          :min="1"
          :size="size"
          :step="1"
          :controls="false"
          :disabled="disabled"
          @change="rowFromNumChange($event,row)"
        />
      </template>
      <template #toNum="{row,size,disabled}">
        <el-input-number
          v-model="row.toNum"
          :min="1"
          :size="size"
          :step="1"
          :controls="false"
          :disabled="disabled"
          @change="rowToNumChange($event,row)"
        />
      </template>
      <template #menuForm="{size,disabled}">
        <el-tooltip v-if="status==='add'" class="item" effect="dark" content="直接完成该调拨，并更新库存"
                    placement="top-start">
          <el-button type="warning" :size="size" :disabled="disabled" @click="handleFinish">立即完成</el-button>
        </el-tooltip>
      </template>
    </avue-form>
    <inventory-summary-select-dialog ref="inventorySelectDialog" :params="{ depotId: form.fromDepotId, status: 1 }"
                                     @confirm="handleInventorySelectConfirm"/>
  </el-dialog>
</template>

<style scoped lang="scss"></style>

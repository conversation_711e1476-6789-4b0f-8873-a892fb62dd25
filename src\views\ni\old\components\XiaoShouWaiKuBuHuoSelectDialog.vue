<script>


import HuoWuLiuZhuanMingxiListDialog from "@/views/ni/old/components/HuoWuLiuZhuanJiLuListDialog.vue";

export default {
  components: {HuoWuLiuZhuanMingxiListDialog},
  props: {
    multiple: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      multiple1: false,
      url: '/api/ni/old/xiaoShouWaiKuBuHuo/list',
      detail: false,
      title: "销售外库补货",
      params: {},
      visible: false,
      form: {},
      query: {},
      loading: false,
      selectionList: [],
      data: [],
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      option: {
        delBtn: false,
        editBtn: false,
        menuWidth: 100,
        searchEnter: true,
        searchIndex: 3,
        searchIcon: true,
        size: "mini",
        searchSize: "mini",
        align: "center",
        addBtn: false,
        header: false,
        border: true,
        tip: false,
        reserveSelection: true,
        highlightCurrentRow: true,
        searchMenuSpan: 6,
        selection: true,
        column: [
          {
            label: "",
            prop: "radio",
            type: "radio",
            width: 55,
            hide: true,
          },
          {
            label: "Id",
            prop: "id",
            search: true,
            width: 70,
          },
          {
            label: "状态",
            prop: "zhuangTai",
            width: 70,
            overHidden: true
          },
          {
            label: "状态",
            prop: "queryZhuangTai",
            hide: true,
            showColumn: false,
            search: true,
          },
          {
            label: "订单号",
            prop: "dingDanHao",
            width: 100, search: true,
            overHidden: true
          },
          {
            label: "发货日期",
            prop: "faHuoRiQi",
            overHidden: true,
            width: 110,
          },
          {
            label: "发货日期",
            prop: "faHuoRiQi1",
            search: true,
            searchRange: true,
            valueFormat: 'yyyy-MM-dd',
            format: 'yyyy-MM-dd',
            type: 'date',
            overHidden: true,
            hide: true,
            showColumn: false,
          },
          {
            label: "发货编号",
            prop: "faHuoBianHao",
            width: 100, search: true,
            overHidden: true
          },
          {
            label: "收货地址",
            prop: "shouHuoDiZhi",
            placeholder: " ",
            overHidden: true, search: true,
            width: 110,
          },
          {
            label: "外库名称",
            prop: "waiKuMingCheng",
            width: 100,
            search: true,
            overHidden: true
          },
          {
            label: "净重Kg",
            prop: "jingZhongKg",
            placeholder: " ",
            overHidden: true,
            width: 100,
          },
          {
            label: "发货地",
            prop: "faHuoDi",
            placeholder: " ",
            overHidden: true, search: true,
            width: 90,
          }, {
            label: "提货车号司机电话",
            prop: "tiCheHaoShiJiDianHua",
            placeholder: " ",
            overHidden: true, search: true,
            width: 100,
          },
          {
            label: "批号",
            prop: "piHao",
            width: 100,
            search: true,
            overHidden: true
          },
          {
            label: "发货附件",
            prop: "faHuoFuJian",
            placeholder: " ",
            overHidden: true,
            width: 80,
          }, {
            label: "质检附件",
            prop: "zhiJianFuJian",
            placeholder: " ",
            overHidden: true,
            width: 80,
          },
        ],
      },
    }
  },
  methods: {
    handleDataSub(row) {
      this.$refs.huoWuLiuZhuanMingxiListDialogRef.onShow({shippingNo: row.faHuoBianHao})
    },
    onShow(params, url) {
      if (!this.multiple) {
        this.$set(this.option, "selection", false);
        this.findObject(this.option.column, "radio").hide = false;
      } else {
        this.$set(this.option, "selection", true);
        this.findObject(this.option.column, "radio").hide = true;
      }
      if (url) {
        this.url = url
      }
      this.selectionClear()
      this.detail = true
      this.page.currentPage = 1
      this.query = {}
      this.params = params
      this.visible = true
    },
    onSelect(params, url, multiple) {
      this.multiple1 = this.multiple || multiple
      if (!this.multiple1) {
        this.$set(this.option, "selection", false);
        this.findObject(this.option.column, "radio").hide = false;
      } else {
        this.$set(this.option, "selection", true);
        this.findObject(this.option.column, "radio").hide = true;
      }
      if (url) {
        this.url = url
      }
      this.selectionClear()
      this.detail = false
      this.page.currentPage = 1
      this.query = {}
      this.params = params
      this.visible = true
    },
    handleConfirm() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$emit("confirm", this.selectionList);
      this.handleClose();
    },
    handleClose(done) {
      this.selectionClear()
      this.visible = false;
      if (done && typeof done == "function") done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.onLoad(this.page, params);
      done();
    },
    selectionClear() {
      this.selectionList = [];
      if (this.$refs.crud) this.$refs.crud.toggleSelection();
    },
    rowClick(row) {
      if (!this.multiple1) {
        this.selectionList = [row];
        this.$set(this.form, "radio", row.id);
      } else this.$refs.crud.toggleSelection([row]);
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      const qu = {...params, ...this.query, ...this.params};
      if (qu.faHuoRiQi1 && qu.faHuoRiQi1.length === 2) {
        qu.startFaHuoRiQi = qu.faHuoRiQi1[0]
        qu.endFaHuoRiQi = qu.faHuoRiQi1[1]
        qu.faHuoRiQi1 = null
      }
      this.$http.get(this.url, {
        params: {
          ...qu,
          current: page.currentPage,
          size: page.pageSize,
        },
      }).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records || [];
        this.loading = false;
      });
    },
  }
}
</script>

<template>
  <el-dialog
    ref="ai-dialog"
    custom-class="ai-dialog"
    :visible.sync="visible"
    :title="title"
    width="60%"
    append-to-body
    :before-close="handleClose"
  >
    <avue-crud
      v-if="visible"
      :option="option"
      :table-loading="loading"
      :data="data"
      v-model="form"
      :search.sync="query"
      :page.sync="page"
      ref="crud"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionList = $event"
      @row-click="rowClick"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template v-if="!multiple1" #radio="{ row }">
        <el-radio v-model="form.radio" :label="row.id"><i></i></el-radio>
      </template>
      <template #menu="{row,index}">
        <el-button type="text"
                   icon="el-icon-reading"
                   size="mini"
                   @click.stop="handleDataSub(row)">明细
        </el-button>
      </template>
    </avue-crud>
    <span slot="footer" class="dialog-footer" v-if="!detail">
      <el-button @click="handleClose" size="mini">取 消</el-button>
      <el-button type="primary" @click="handleConfirm" size="mini"
      >确 定</el-button
      >
    </span>
    <HuoWuLiuZhuanMingxiListDialog ref="huoWuLiuZhuanMingxiListDialogRef"/>
  </el-dialog>
</template>

<style scoped>

</style>

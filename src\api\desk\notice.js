import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/blade-desk/notice/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/blade-desk/notice/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/blade-desk/notice/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/blade-desk/notice/submit',
    method: 'post',
    data: row
  })
}

export const getNotice = (id) => {
  return request({
    url: '/api/blade-desk/notice/detail',
    method: 'get',
    params: {
      id,
    }
  })
}

export const getUnReadPage = (current, size, params) => {
  return request({
    url: '/api/ni/desk/userNotice/user-notice-page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
      isRead:0
    },
  })
}
export const getUserMessage = (id) => {
  return request({
    url: '/api/blade-desk/notice/userMessage',
    method: 'get',
    params: {
      id,
    }
  })
}

export const getUserNotice = (id) => {
  return request({
    url: '/api/blade-desk/notice/user-detail',
    method: 'get',
    params: {
      id,
    }
  })
}

export const getUserNoticePage = (current, size, params) => {
  return request({
    url: '/api/ni/desk/userNotice/user-notice-page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}


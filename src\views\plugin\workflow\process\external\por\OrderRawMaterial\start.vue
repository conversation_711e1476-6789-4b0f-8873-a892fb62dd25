<template>
  <basic-container>
    <avue-skeleton :loading="waiting" avatar :rows="8">
      <div style="display: flex;">
        <avue-title
          style="margin-bottom: 20px"
          :styles="{ fontSize: '20px' }"
          :value="process.name"
        ></avue-title>
        <el-badge v-if="permission.wf_process_draft&&draftCount > 0" :value="draftCount"
                  style="margin-top: 5px;  margin-right: 40px;" type="warning">
          <el-button
            size="mini"
            v-loading="loading"
            @click="handleDraftBox"
          >草稿箱
          </el-button>
        </el-badge>
      </div>
      <el-card shadow="never" style="margin-top: 20px">
        <!-- 自定义表单区域 -->
        <avue-form
          v-if="
            option &&
            ((option.column && option.column.length > 0) ||
              (option.group && option.group.length > 0))
          "
          v-model="form"
          ref="form"
          :option="option"
          :defaults.sync="defaults"
          @submit="handleSubmit"
          @error="loading = false"
          :upload-preview="handleUploadPreview"
        >
          <template #contractId="{ disabled, size, index }">
            <contract-select
              v-model="form.contractId"
              pay-type="2"
              :disabled="disabled"
              :size="size"
              @confirm="handleContractIdConfirm"
            />
          </template>
          <template #supplier="{ disabled, size, index }">
            <supplier-multiple-select
              v-model="form.supplier"
              :size="size"
              :multiple="false"
              :params="{ status: 2, blacklist: false, honestStatus: true }"
              :disabled="disabled"
              @submit="handleSupplierSubmit"
            />
          </template>
          <template #materialCode="{ row, type, disabled, size, index }">
            <material-select
              v-model="row.materialCode"
              :size="size"
              @submit="handleMaterialSubmit($event, row)"
            />
          </template>
          <template #unit="{ row, type, disabled, size, index }">
            <el-select
              v-model="row.unit"
              filterable
              size="mini"
              placeholder=" "
            >
              <el-option
                v-for="item in row.unitDicData"
                :key="item.dictKey"
                :label="item.dictValue"
                :value="item.dictKey"
              />
            </el-select>
          </template>
        </avue-form>
        <!-- 自定义表单区域 -->
      </el-card>

      <el-card shadow="never" style="margin-top: 20px" v-if="showExamForm">
        <wf-examine-form
          ref="examineForm"
          :process="process"
          @user-select="handleUserSelect"
        ></wf-examine-form>
      </el-card>
      <div style="height: 120px"></div>
      <el-row
        class="foot-item avue-affix"
        :style="{
          width: isCollapse ? 'calc(100% - 80px)' : 'calc(100% - 260px)',
        }"
        id="avue-view"
      >
        <el-button
            v-no-more-click
          type="primary"
          size="mini"
          v-loading="loading"
          @click="handleSubmit"
          >发起
        </el-button>
        <el-button
            v-if="permission.wf_process_draft"
            type="success"
            size="mini"
            v-loading="loading"
            @click="handleDraftNotClose(process.id, process.formKey, form, process.key)"
        >存为草稿
        </el-button>
        <el-button
            v-if="permission.wf_process_draft"
            type="success"
            size="mini"
            v-loading="loading"
            @click="handleDraft(process.id, process.formKey, form, process.key)"
        >存为草稿并关闭
        </el-button>
      </el-row>
    </avue-skeleton>

    <!-- 人员选择弹窗 -->
    <wf-user-select
      ref="user-select"
      :check-type="checkType"
      :default-checked="defaultChecked"
      @onConfirm="handleUserSelectConfirm"
    ></wf-user-select>
     <!-- 草稿弹窗 -->
    <draft-popup
      :visible.sync="isDraftPopupVisible"
      :draftList="draftList"
      @select="handleDraftSelect"
      @delete="handleDraftDelete"
    ></draft-popup>
  </basic-container>
</template>

<script>
import WfExamineForm from "@/views/plugin/workflow/process/components/examForm.vue";
import WfUserSelect from "@/views/plugin/workflow/process/components/user-select";

import exForm from "@/views/plugin/workflow/mixins/ex-form";
import draft from "@/views/plugin/workflow/mixins/draft";
import UserSelect from "@/components/user-select";
import { getDetail as getSupplierDetail } from "@/api/ni/base/supplier/supplierinfo";
import { dateFormat } from "@/util/date";
import { mapGetters } from "vuex";
import ContractSelect from "@/views/ni/base/components/ContractSelect";
import SupplierMultipleSelect from "@/views/ni/base/components/SupplierSelect";
import MaterialSelect from "@/views/ni/base/components/MaterialSelect";
import DraftPopup from "@/views/plugin/workflow/process/components/draftPopup.vue";
import debounce from "@/util/debounce";

export default {
  components: {
    WfUserSelect,
    WfExamineForm,
    UserSelect,
    ContractSelect,
    SupplierMultipleSelect,
    MaterialSelect,
    DraftPopup
  },
  mixins: [exForm, draft],
  watch: {
    "$route.query.p": {
      handler(val) {
        if (val) {
          const param = JSON.parse(Buffer.from(val, "base64").toString());
          const { processId, processDefKey, form } = param;
          if (processId) {
            this.getForm(processId);
          } else if (processDefKey) {
            this.getFormByProcessDefKey(processDefKey);
          }
          if (form) {
            this.form = { ...form };
          }
        }
      },
      immediate: true,
    },
  },
  activated() {
    let val=this.$route.query.p
    if (val) {
      const param = JSON.parse(Buffer.from(val, "base64").toString());
      const { processId, processDefKey, form } = param;
      if (processId) {
        this.getForm(processId);
      } else if (processDefKey) {
        this.getFormByProcessDefKey(processDefKey);
      }
      if (form) {
        this.form = { ...form };
      }
    }
  },
  computed: {
    ...mapGetters(["userInfo"]),
    showExamForm() {
      const { hideComment, hideCopy, hideExamine } = this.process;
      return !hideComment || !hideCopy || !hideExamine;
    },
  },
  data() {
    return {
      defaults: {},
      form: {},
      option: {
        labelWidth: 120,
        calcHeight: 30,
        size: "mini",
        span: 6,
        menuBtn: false,
        column: [
          {
            type: "input",
            label: "申请人",
            display: true,
            prop: "creator",
            value: "${this.$store.getters.userInfo.nick_name}",
            readonly: true,
          },
          {
            type: "input",
            label: "申请部门",
            display: true,
            row: true,
            prop: "createDept",
            value: "${this.$store.getters.userInfo.dept_name}",
            readonly: true,
          },
          {
            label: "订单主题",
            prop: "title",
            overHidden: true,
            minWidth: 110,
            rules: [
              {
                required: true,
                message: "请输入",
                trigger: "blur",
              },
            ],
          },
          {
            label: "订单编号",
            prop: "serialNo",
            placeholder: "系统自动生成",
            disabled: true,
            minWidth: 125,
          },
          {
            label: "订单类别",
            prop: "type",
            minWidth: 90,
            type: "select",
            disabled: true,
            dicUrl: "/api/blade-system/dict-biz/dictionary?code=ni_por_type",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            value: "1",
            rules: [
              {
                required: true,
                message: "请输入",
                trigger: "blur",
              },
            ],
          },
          {
            label: "关联合同",
            prop: "contractId",
            minWidth: 120,
            search: true,
            hide: true,
            showColumn: false,
          },
          {
            label: "供应商",
            prop: "supplier",
            minWidth: 110,
            overHidden: true,
            placeholder: "请选择供应商",
            prefixIcon: "el-icon-search",
            rules: [
              {
                required: true,
                message: "请输入",
                trigger: "blur",
              },
            ],
          },
          {
            label: "联系人",
            prop: "supplierLinkman",
            placeholder: " ",
            showColumn: false,
            hide: true,
            rules: [
              {
                required: true,
                message: "请输入",
                trigger: "blur",
              },
            ],
          },
          {
            label: "联系电话",
            prop: "supplierLinkPhone",
            placeholder: " ",
            showColumn: false,
            hide: true,
            minWidth: 95,
            rules: [
              {
                required: true,
                message: "请输入",
                trigger: "blur",
              },
            ],
          },
          {
            label: "发票类型",
            prop: "billType",
            minWidth: 93,
            type: "select",
            dicUrl: "/api/blade-system/dict-biz/dictionary?code=ni_bill_type",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            rules: [
              {
                required: true,
                message: "请输入",
                trigger: "blur",
              },
            ],
          },
          {
            label: "税率(%)",
            prop: "taxRate",
            hide: true,
            showColumn: false,
            type: "number",
            precision: 2,
            controls: false,
          },
          {
            label: "付款方式",
            prop: "payType",
            minWidth: 100,
            type: "select",
            disabled: true,
            value: "1",
            dicData: [
              {
                label: "财务付款",
                value: "1",
              },
              {
                label: "冲抵借款",
                value: "2",
              },
            ],
            rules: [
              {
                required: true,
                message: "请选择",
                trigger: "blur",
              },
            ],
          },
          {
            label: "备注",
            prop: "remark",
            hide: true,
            type: "textarea",
            span: 24,
            minRows: 3,
          },
        ],
        group: [
          {
            label: "详情",
            arrow: false,
            column: [
              {
                size: "mini",
                labelWidth: 0,
                label: "",
                prop: "items",
                span: 24,
                icon: "el-icon-arrow-right",
                type: "dynamic",
                children: {
                  showSummary: true,
                  sumColumnList: [
                    {
                      name: "num",
                      type: "sum",
                      decimals: 1,
                    },
                    {
                      name: "amount",
                      type: "sum",
                    },
                    {
                      name: "backNum",
                      type: "sum",
                    },
                    {
                      name: "backAmount",
                      type: "sum",
                    },
                  ],
                  align: "center",
                  headerAlign: "center",
                  column: [
                    {
                      label: "编码",
                      placeholder: " ",
                      prop: "materialCode",
                      cell: true,
                      clearable: false,
                      rules: [
                        {
                          required: true,
                          message: "请输入",
                          trigger: "blur",
                        },
                      ],
                      click: () => {
                        this.handleItemAdd();
                      },
                    },
                    {
                      label: "物料名称",
                      prop: "materialName",
                      placeholder: " ",
                      clearable: false,
                      disabled: true,
                    },
                    {
                      label: "规格",
                      prop: "specification",
                      slot: true,
                      placeholder: " ",
                      disabled: true,
                    },
                    {
                      label: "国标",
                      prop: "gb",
                      slot: true,
                      placeholder: " ",
                      disabled: true,
                    },
                    {
                      label: "单位",
                      prop: "unit",
                      slot: true,
                    },
                    {
                      label: "订购数",
                      prop: "num",
                      type: "number",
                      precision: 0,
                      controls: false,
                      placeholder: " ",
                      rules: [
                        {
                          required: true,
                          message: "请输入",
                          trigger: "blur",
                        },
                      ],
                      change: (data) => {
                        const that = this;
                        const { row, value } = data;
                        if (row.amount) {
                          row.price = row.amount / value;
                        } else {
                          row.price = 0;
                        }
                        let amount = 0;
                        that.form.items.forEach((item) => {
                          amount += item.amount;
                        });
                        that.form.amount = amount;
                      },
                    },
                    {
                      label: "单价",
                      prop: "price",
                      type: "number",
                      controls: false,
                      precision: 2,
                      placeholder: " ",
                      disabled: true,
                      cell: true,
                    },
                    {
                      label: "金额",
                      prop: "amount",
                      type: "number",
                      precision: 2,
                      controls: false,
                      placeholder: " ",
                      rules: [
                        {
                          required: true,
                          message: "请输入",
                          trigger: "blur",
                        },
                      ],
                      change: ({ row, value }) => {
                        const that = this;
                        if (row.num) {
                          row.price = value / row.num;
                        } else {
                          row.price = 0;
                        }
                        let amount = 0;
                        that.form.items.forEach((item) => {
                          amount += item.amount;
                        });
                        that.form.amount = amount;
                      },
                    },
                    {
                      label: "到货日期",
                      prop: "arrivalDate",
                      type: "date",
                      format: "yyyy-MM-dd",
                      valueFormat: "yyyy-MM-dd",
                    },
                    {
                      label: "备注",
                      prop: "remark",
                      type: "textarea",
                      minRows: 1,
                      placeholder: " ",
                    },
                  ],
                },
              },
            ],
          },
        ],
      },
      process: {},
      loading: false,
      payment: 0,
      isDraftPopupVisible: false,
      draftList: [],
      draftCount: 0,
      draftId: null,
      isConfirmShow: false
    };
  },
  methods: {
    getForm(processId) {
      this.getStartForm(processId).then((res) => {
        const _this = this;
        let { process } = res;
        this.form.processId = process.id;
        const option = _this.option;
        const { column, group } = option;
        column.forEach((col) => {
          if (col.value) col.value = _this.getDefaultValues(col.value);
        });
        if (group && group.length > 0) {
          // 处理group
          group.forEach((gro) => {
            gro.column.forEach((col) => {
              if (col.value) col.value = _this.getDefaultValues(col.value);
            });
          });
        }
        if (this.permission.wf_process_draft && !this.isConfirmShow) {
          this.isConfirmShow = true;
          // 查询草稿箱
          this.initDraft(process.id,process.key).then((data) => {
          this.draftCount = data.length;
          this.draftList = data;
            if (data && Array.isArray(data) && data.length > 0 ) {
              _this.$confirm("是否引用之前保存的草稿？", "提示", {})
              .then(() => {
                this.isDraftPopupVisible = true; // 打开草稿弹窗
              });
            }
          });
        }
        _this.waiting = false;
      });
    },
    getFormByProcessDefKey(processDefKey) {
      this.getStartFormByProcessDefKey(processDefKey).then((res) => {
        const _this = this;
        let { process } = res;
        _this.form.processId = process.id;
        const option = _this.option;
        const { column, group } = option;
        column.forEach((col) => {
          if (col.value) col.value = _this.getDefaultValues(col.value);
        });
        if (group && group.length > 0) {
          // 处理group
          group.forEach((gro) => {
            gro.column.forEach((col) => {
              if (col.value) col.value = _this.getDefaultValues(col.value);
            });
          });
        }
        if (this.permission.wf_process_draft && !this.isConfirmShow) {
          this.isConfirmShow = true;
          // 查询草稿箱
          this.initDraft(process.id,process.key).then((data) => {
          this.draftCount = data.length;
          this.draftList = data;
            if (data && Array.isArray(data) && data.length > 0 ) {
              _this.$confirm("是否引用之前保存的草稿？", "提示", {})
              .then(() => {
                this.isDraftPopupVisible = true; // 打开草稿弹窗
              });
            }
          });
        }
        _this.waiting = false;
      });
    },
    handleSubmit:debounce(function () {
      //保存再提交
      if (this.form.inquiry) {
        this.form.inquiryState = 1;
      }
      this.form.porState = 1;
      this.form.draftId = this.draftId;
      this.handleStartProcess(true)
        .then((done) => {
          this.$message.success("发起成功");
          if(this.draftId != null){
            this.draftCount = this.draftCount-1;
            this.draftList = this.draftList.filter(item => item.id !== this.draftId);
          }
          this.handleCloseTag("/ni/por/order-raw-material");
          done();
        })
        .catch(() => {
          this.loading = false;
        });
    },1000),
    handleContractIdConfirm(selectList) {
      if (selectList != null && selectList.length > 0) {
        const contract = selectList[0];
        console.log(contract);
        if (contract.bbType === "supplier") {
          this.form.supplier = contract.bname;
          this.form.supplierId = contract.b;
          getSupplierDetail(contract.b).then((res) => {
            const data = res.data.data;
            const selectList = [data];
            this.handleSupplierSubmit(selectList);
          });
        }
      }
    },
    handleSupplierSubmit(selectList) {
      this.form.supplierId = selectList
        .map((item) => {
          return item.id;
        })
        .join(",");
      this.form.supplierLinkman = selectList[0].linkman;
      this.form.supplierLinkPhone = selectList[0].linkPhone;
      this.form.title =
        dateFormat(new Date(), "yyyy-MM-dd") +
        "[" +
        this.form.supplier +
        "]采购订单";
    },
    handleMaterialSubmit(row, row1) {
      row1.materialName = row.name;
      row1.materialId = row.id;
      row1.specification = row.specification;
      row1.gb = row.gb;
      row1.unit = row.unit;
      row1.unitDicData = [];
      this.$http
        .get("/api/ni/base/material/unit/dict?id=" + row.id)
        .then((res) => {
          row1.unitDicData = res.data.data;
        });
    },
    //选择草稿
    handleDraftSelect(selectedDraft) {
      //草稿版本与流程版本不一致
      if(!selectedDraft.sameVersion){
        this.$confirm("选中的草稿与当前流程版本不一致，是否继续引用？", "提示", {})
        .then(() => {
          this.draftId = selectedDraft.id;
          this.form = JSON.parse(selectedDraft.variables);
          this.isDraftPopupVisible = false;
        });
      } else {
        this.draftId = selectedDraft.id;
        this.form = JSON.parse(selectedDraft.variables);
      }
    },
    //删除草稿
    handleDraftDelete(draftId) {
      this.$confirm("是否删除选中的草稿箱数据？", "提示", {})
        .then(() => {
          this.$axios.post(`/api/blade-workflow/process/draft/delete/${draftId}`)
          .then(response => {
            this.$message.success('草稿删除成功');
            this.draftCount = this.draftCount-1;
            this.draftList = this.draftList.filter(item => item.id !== draftId);
          })
          .catch(error => {
            this.$message.error('草稿删除失败，请重试');
          });
      })
    },
    handleDraftBox() {
      if (this.draftList.length > 0) {
        this.isDraftPopupVisible = true;
      } else {
        // 重新获取草稿数据
        this.initDraft(this.form.processId).then((data) => {
          this.draftCount = data.length;
          this.draftList = data;
          if (data && Array.isArray(data) && data.length > 0) {
            this.isDraftPopupVisible = true;
          }
        });
      }
    },

  },
};
</script>

<style lang="scss" scoped>
.foot-item {
  position: fixed;
  bottom: 0;
  margin-left: -20px;
  // right: 0;
  z-index: 101;
  height: 66px;
  background-color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  -webkit-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
</style>

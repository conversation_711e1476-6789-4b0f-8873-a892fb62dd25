<template>
  <basic-container>
    <avue-skeleton :loading="waiting" avatar :rows="8">
      <div style="display: flex">
        <avue-title
          style="margin-bottom: 20px"
          :styles="{ fontSize: '20px' }"
          :value="process.name"
        ></avue-title>
        <el-badge
          v-if="permission.wf_process_draft && draftCount > 0"
          :value="draftCount"
          style="margin-top: 5px; margin-right: 40px"
          type="warning"
        >
          <el-button size="mini" v-loading="loading" @click="handleDraftBox"
            >草稿箱
          </el-button>
        </el-badge>
      </div>
      <el-card shadow="never" style="margin-top: 20px">
        <avue-form
          v-if="
            option &&
            ((option.column && option.column.length > 0) ||
              (option.group && option.group.length > 0))
          "
          v-model="form"
          ref="form"
          :option="option"
          :defaults.sync="defaults"
          :upload-preview="handleUploadPreview"
        >
        </avue-form>
      </el-card>

      <el-card shadow="never" style="margin-top: 20px" v-if="showExamForm">
        <wf-examine-form
          ref="examineForm"
          :process="process"
          @user-select="handleUserSelect"
        ></wf-examine-form>
      </el-card>
      <div style="height: 120px"></div>
      <el-row
        class="foot-item avue-affix"
        :style="{
          width: isCollapse ? 'calc(100% - 80px)' : 'calc(100% - 260px)',
        }"
        id="avue-view"
      >
        <el-button
          type="primary"
          size="medium"
          v-loading="loading"
          @click="handleSubmit"
          >发起
        </el-button>
        <el-button
          v-if="permission.wf_process_draft"
          type="success"
          size="medium"
          v-loading="loading"
          @click="handleDraft(process.id, process.formKey, form, process.key)"
          >存为草稿
        </el-button>
      </el-row>
    </avue-skeleton>

    <!-- 人员选择弹窗 -->
    <wf-user-select
      ref="user-select"
      :check-type="checkType"
      :default-checked="defaultChecked"
      @onConfirm="handleUserSelectConfirm"
    ></wf-user-select>

    <!-- 草稿弹窗 -->
    <draft-popup
      :visible.sync="isDraftPopupVisible"
      :draftList="draftList"
      @select="handleDraftSelect"
      @delete="handleDraftDelete"
    ></draft-popup>
  </basic-container>
</template>

<script>
import WfExamineForm from "./examForm.vue";
import WfUserSelect from "./user-select";
import exForm from "../../mixins/ex-form";
import draft from "../../mixins/draft";
import DraftPopup from "./draftPopup.vue";
import debounce from "@/util/debounce";

export default {
  components: {
    WfUserSelect,
    WfExamineForm,
    DraftPopup,
  },
  mixins: [exForm, draft],
  /**
   * 发起流程受到keepalive影响，无法通过watch来监听，改用activated
   */
  activated() {
    let val = this.$route.params.params;
    const param = JSON.parse(Buffer.from(val, "base64").toString());
    console.log(param);
    const { processId, processDefKey, form } = param;
    if (form) {
      this.form = JSON.parse(decodeURIComponent(form));
    }
    if (processId || processDefKey) {
      this.getForm(processId, processDefKey);
    }
  },
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      //  这里的vm指的就是vue实例，可以用来当做this使用
      vm.fromPath = from.path; //获取上一级路由的路径
    });
  },
  data() {
    return {
      fromPath: null,
      defaults: {},
      form: {},
      option: {},
      process: {},
      loading: false,
      isDraftPopupVisible: false,
      draftList: [],
      draftCount: 0,
      draftId: null,
    };
  },
  methods: {
    getForm(processId, processDefKey) {
      let param;
      let method;
      if (processId) {
        param = processId;
        method = "getStartForm";
      } else if (processDefKey) {
        param = processDefKey;
        method = "getStartFormByProcessDefKey";
      }

      this[method](param).then((res) => {
        let { process, form, startForm } = res;
        this.form.processId = process.id;
        if (form) {
          const option = { ...eval("(" + form + ")"), menuBtn: false };
          const { column, group } = option;

          const groupArr = [];
          const columnArr = this.filterAvueColumn(column, startForm).column;
          if (group && group.length > 0) {
            // 处理group
            group.forEach((gro) => {
              gro.column = this.filterAvueColumn(gro.column, startForm).column;
              if (gro.column.length > 0) groupArr.push(gro);
            });
          }
          option.column = columnArr;
          option.group = groupArr;
          this.option = option;
          if (this.permission.wf_process_draft) {
            // 查询草稿箱
            this.initDraft(process.id, process.key).then((data) => {
              this.draftCount = data.length;
              this.draftList = data;
              if (data && Array.isArray(data) && data.length > 0) {
                this.$confirm("是否引用之前保存的草稿？", "提示", {}).then(
                  () => {
                    this.isDraftPopupVisible = true; // 打开草稿弹窗
                  }
                );
              }
            });
          }
        }
        this.waiting = false;
      });
    },
    handleDraftSelect(selectedDraft) {
      //草稿版本与流程版本不一致
      if (!selectedDraft.sameVersion) {
        this.$confirm(
          "选中的草稿与当前流程版本不一致，是否继续引用？",
          "提示",
          {}
        ).then(() => {
          this.draftId = selectedDraft.id;
          this.form = JSON.parse(selectedDraft.variables);
          this.isDraftPopupVisible = false;
        });
      } else {
        this.draftId = selectedDraft.id;
        this.form = JSON.parse(selectedDraft.variables);
      }
    },
    handleDraftBox() {
      if (this.draftList.length > 0) {
        this.isDraftPopupVisible = true;
      } else {
        // 重新获取草稿数据
        this.initDraft(this.form.processId).then((data) => {
          this.draftCount = data.length;
          this.draftList = data;
          if (data && Array.isArray(data) && data.length > 0) {
            this.isDraftPopupVisible = true;
          }
        });
      }
    },
    handleDraftDelete(draftId) {
      this.$confirm("是否删除选中的草稿箱数据？", "提示", {}).then(() => {
        this.$axios
          .post(`/api/blade-workflow/process/draft/delete/${draftId}`)
          .then((response) => {
            this.$message.success("草稿删除成功");
            this.draftCount = this.draftCount - 1;
            this.draftList = this.draftList.filter(
              (item) => item.id !== draftId
            );
          })
          .catch((error) => {
            this.$message.error("草稿删除失败，请重试");
          });
      });
    },
    handleSubmit: debounce(function () {
      this.handleStartProcess(true)
        .then((done) => {
          this.$message.success("流程已发起");
          if (this.draftId != null) {
            this.draftCount = this.draftCount - 1;
            this.draftList = this.draftList.filter(
              (item) => item.id !== this.draftId
            );
          }
          if (this.fromPath) {
            this.handleCloseTag(this.fromPath);
          } else this.handleCloseTag("/plugin/workflow/process/todo");
          done();
        })
        .catch(() => {
          this.loading = false;
        })
        .finally(() => {
          done();
        });
    }, 1000),
  },
};
</script>

<style lang="scss" scoped>
.foot-item {
  position: fixed;
  bottom: 0;
  margin-left: -20px;
  // right: 0;
  z-index: 101;
  height: 66px;
  background-color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  -webkit-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
</style>

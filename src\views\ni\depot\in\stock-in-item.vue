<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      v-model="form"
      :search.sync="query"
      ref="crud"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @sort-change="sortChange"
      @on-load="onLoad"
      :cell-style="cellStyle"
      @row-click="rowClick"
    >
      <template #status="{ row, index }">
        <template v-if="row.status">
          <el-tag
            v-if="row.status === 1"
            size="mini"
            type="info"
            effect="plain"
          >
            {{ row.$status }}
          </el-tag>
          <el-tag
            v-else-if="row.status === 2"
            size="mini"
            type="success"
            effect="plain"
          >
            {{ row.$status }}
          </el-tag>
          <el-tag
            v-else-if="row.status === 3"
            size="mini"
            type="warning"
            effect="plain"
          >
            {{ row.$status }}
          </el-tag>
        </template>
      </template>
      <template #arrivalStates="{ row }">
        <span
          v-if="row.arrivalStates === 3"
          :style="{
            fontWeight: 'bold',
          }"
        >
          全部到货
        </span>
        <span
          v-else
          :style="{
            color: '#FF0000',
            fontWeight: 'bold',
          }"
        >
          部分到货
        </span>
      </template>
      <template #pv="{ row, index }">
        <a
          v-if="row.pv"
          style="text-decoration: underline; font-weight: bold; color: #f56c6c"
          href="#"
          @click.p.prevent="rowPvShow(row)"
          >是
        </a>
        <el-tag size="mini" v-else type="info" effect="plain">否</el-tag>
      </template>
      <template #fa="{ row, index }">
        <a v-if="row.fa" style="font-weight: bold; color: #f56c6c">是 </a>
        <el-tag size="mini" v-else type="info" effect="plain">否</el-tag>
      </template>
      <template #qualityCertificate="{ row, index }">
        <a
          v-if="row.pv"
          style="text-decoration: underline"
          href="#"
          @click.p.prevent="rowQualityCertificate(row)"
        >
          {{
            row.qualityCertificate && row.qualityCertificate.length > 0
              ? `证明【${row.qualityCertificate.length}】`
              : "未上传"
          }}
        </a>
      </template>
      <template #yonyouSync="{ row, size }">
        <span v-for="(item, index) in row.yonyouSync" :key="index">
          {{ yonyouSyncSequenceDictKeyValue[item.sequence] }}:
          <el-tag :size="size">
            {{ yonyouSyncStateDictKeyValue[item.value] }}
          </el-tag>
          ;
          <template v-if="item.id">
            <span style="font-weight: bolder">id:</span>
            {{ item.id }}
          </template>
          <template v-if="item.errMsg"> :{{ item.errMsg }} </template>
          <br />
        </span>
      </template>
      <template #projectSerialNo="{ row, index }">
        <span v-if="row.projectId">{{ row.projectSerialNo }}</span>
        <el-tag
          size="mini"
          v-if="row.budgetYear === 2"
          type="warning"
          effect="plain"
        >
          年度预算
        </el-tag>
      </template>
      <template #depotLocation="{ row, index }">
        <el-tag size="mini" type="warning" effect="dark" v-if="row.temp"
          >暂存
        </el-tag>
        <span>{{ row.depotLocation }}</span>
      </template>
      <template #soa="{ row, index }">
        <el-tag size="mini" v-if="row.soa" effect="dark">{{ row.$soa }}</el-tag>
        <el-tag size="mini" v-else effect="plain" type="danger">否</el-tag>
      </template>
      <template #subType="{ row, index }">
        <el-tag v-if="row.subType === '101'" size="mini" effect="plain">
          {{ row.$subType }}
        </el-tag>
        <el-tag
          v-else-if="row.subType === '102'"
          size="mini"
          type="success"
          effect="plain"
        >
          {{ row.$subType }}
        </el-tag>
        <el-tag
          v-else-if="row.subType === '103'"
          size="mini"
          type="warning"
          effect="plain"
        >
          {{ row.$subType }}
        </el-tag>
        <el-tag
          v-else-if="row.subType === '104'"
          size="mini"
          type="danger"
          effect="plain"
        >
          {{ row.$subType }}
        </el-tag>
        <el-tag v-else-if="row.subType === '105'" size="mini">
          {{ row.$subType }}
        </el-tag>
        <el-tag v-else-if="row.subType === '106'" size="mini" type="success">
          {{ row.$subType }}
        </el-tag>
        <el-tag v-else-if="row.subType === '107'" size="mini" type="warning">
          {{ row.$subType }}
        </el-tag>
        <el-tag v-else-if="row.subType === '108'" size="mini" type="danger">
          {{ row.$subType }}
        </el-tag>
        <el-tag
          v-else-if="row.subType === '199'"
          size="mini"
          type="info"
          effect="dark"
        >
          {{ row.$subType }}
        </el-tag>
        <el-tag v-else-if="row.subType === '110'" size="mini" type="danger">
          {{ row.$subType }}
        </el-tag>
        <el-tag v-else size="mini" effect="dark">
          {{ row.$subType }}
        </el-tag>
      </template>
      <template #serialNo="{ row, index }">
        <span
          :style="{
            color: colorName,
            fontWeight: 'bold',
            cursor: 'pointer',
            textDecoration: 'underline',
          }"
          @click="$refs.stockInFormRef.onShow(row.billId)"
        >
          {{ row.serialNo }}
        </span>
      </template>
      <template #num="{ row }">
        <span v-if="row.num >= 0 && row.returnNum == 0">
          {{ row.num }}
        </span>
        <span
          v-else-if="row.num >= 0 && row.num <= row.returnNum"
          style="
            color: #909399;
            font-weight: bolder;
            text-decoration: line-through;
          "
        >
          {{ row.num }}
        </span>
        <span v-else style="font-weight: bolder; color: #f56c6c">
          {{ row.num }}
        </span>
        <el-tag
          v-if="row.returnNum > 0"
          size="mini"
          type="danger"
          effect="plain"
        >
          已退库:{{ row.returnNum }}
        </el-tag>
      </template>
      <template #menuLeft>
        <el-button
          type="success"
          size="mini"
          icon="el-icon-download"
          plain
          v-if="permission.stock_in_item_export"
          @click="handleExport"
          >导出
        </el-button>
        <el-button
          type="primary"
          size="mini"
          plain
          icon="el-icon-printer"
          v-if="permission.stock_in_item_print"
          @click="handlePrintStockIn1"
          >打印
        </el-button>
        <el-button
          type="danger"
          size="mini"
          icon="el-icon-refresh-left"
          v-if="permission.stock_in_item_red"
          @click="handleRed"
          >冲红
        </el-button>
        <el-button
          type="warning"
          size="mini"
          icon="el-icon-house"
          v-if="permission.stock_in_item_fa"
          @click="handleSetFixedAsset"
          >转固定资产
        </el-button>
        <el-button
          v-if="permission.stock_in_item_print && selectionList.length > 0"
          type="info"
          size="mini"
          icon="el-icon-price-tag"
          @click="handleMarkPrint"
          >打印标记
        </el-button>
        <el-divider direction="vertical" />
        <el-checkbox v-model="pv">压力容器</el-checkbox>
        <el-checkbox v-model="yzdyzb" @change="handleYzdyzbChange"
          >易制毒、易制爆化学品
        </el-checkbox>
        <el-checkbox v-model="auto">自动申购</el-checkbox>
        <el-divider direction="vertical" />
        <el-tag>
          当前表格已选择
          <span style="font-weight: bolder">{{ selectionList.length }}</span> 项
          <el-button type="text" size="mini" @click="selectionClear">
            清空
          </el-button>
        </el-tag>
      </template>
      <template #menuRight>
        <el-button
          icon="el-icon-time"
          circle
          size="mini"
          @click="handleLog"
        ></el-button>
      </template>
    </avue-crud>
    <set-fixed-asset-dialog
      ref="setFixedAssetRef"
      @setFixedAssetSubmit="handleSetFixedAssetBack"
    />
    <stock-in-order-arrival-link-dialog ref="arrivalLinkRef" />
    <stock-in-return-dialog ref="outReturnRef" @confirm="onLoad(page)" />
    <pv-register-dialog ref="pvRegisterRef" />
    <attach-dialog
      ref="attachDialogRef"
      @updateAfter="onLoad(page)"
      :del-btn="userInfo.role_name.includes('admin')"
    />
    <stock-in-form ref="stockInFormRef" :sn="false" @submit="onLoad(page)" />
    <el-dialog
      title="修改打印状态"
      append-to-body
      :visible.sync="print.visible"
      width="355px"
    >
      <avue-form
        ref="printRef"
        :option="print.option"
        v-model="print.form"
        @submit="handleMarkPrintSubmit"
      ></avue-form>
    </el-dialog>
    <log-opt-dialog ref="logOptDialogRef" :module="module" />
  </basic-container>
</template>
<script>
import { getPrintData as getStockPrintData } from "@/api/ni/depot/stockIn";
import { mapGetters } from "vuex";
import {
  getPage,
  getPrintData,
  markPrint,
  printCount,
  printSign,
} from "@/api/ni/depot/stockInItem";
import {
  detail as getOrderArrivalDetail,
  getDetail as getArrivalDetail,
} from "@/api/ni/por/order-arrival";
import "nprogress/nprogress.css";
import UserSelect from "@/components/user-select";
import StockInOrderArrivalLinkDialog from "@/views/ni/depot/components/StockInOrderArrivalLinkDialog";
import StockInReturnDialog from "@/views/ni/depot/components/StockInReturnDialog";
import PvRegisterDialog from "@/views/ni/depot/components/PvRegisterDialog";
import AttachDialog from "@/components/attach-dialog";
import StockInForm from "@/views/ni/depot/components/StockInForm";
import { loadPrintTemplate } from "@/api/system/printTemplate";
import { hiprint } from "vue-plugin-hiprint";
import LogOptDialog from "@/components/log-opt-dialog";
import { changeFa } from "@/api/ni/por/apply-item";
import SetFixedAssetDialog from "@/views/ni/depot/components/SetFixedAssetDialog";
import { dateFormat } from "@/util/date";

export default {
  name: "stockInItem",
  components: {
    StockInForm,
    SetFixedAssetDialog,
    StockInOrderArrivalLinkDialog,
    UserSelect,
    StockInReturnDialog,
    PvRegisterDialog,
    AttachDialog,
    LogOptDialog,
  },
  computed: {
    ...mapGetters(["permission", "userInfo", "colorName"]),
    items() {
      return this.form.items || [];
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
    billIds() {
      let billIds = [];
      this.selectionList.forEach((ele) => {
        billIds.push(ele.billId);
      });
      return billIds.join(",");
    },
  },
  watch: {
    pv: {
      handler(val) {
        if (val) this.query.pv = 1;
        else this.query.pv = null;
        this.page.currentPage = 1;
        this.onLoad(this.page);
      },
      immediate: false,
    },
    auto: {
      handler(val) {
        if (val) this.query.auto = 1;
        else this.query.auto = null;
        this.page.currentPage = 1;
        this.onLoad(this.page);
      },
      immediate: false,
    },
  },
  data() {
    return {
      allData: true,
      module: "",
      form: {
        items: [],
      },
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        stockInPrintTemplate: null,
        highlightCurrentRow: true,
        searchEnter: true,
        selection: true,
        menu: false,
        addBtn: false,
        saveBtn: false,
        updateBtn: false,
        cancelBtn: false,
        span: 6,
        size: "mini",
        searchSize: "mini",
        delBtn: false,
        editBtn: false,
        dialogFullscreen: true,
        searchIndex: 3,
        searchIcon: true,
        height: "auto",
        calcHeight: 30,
        tip: false,
        searchLabelWidth: 110,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: false,
        align: "center",
        viewBtn: false,
        dialogClickModal: false,
        column: [
          {
            label: "入库时间",
            prop: "opTime",
            search: true,
            searchRange: true,
            minWidth: 120,
            overHidden: true,
            type: "datetime",
            format: "yyyy-MM-dd HH:mm",
            valueFormat: "yyyy-MM-dd",
          },
          {
            label: "库管人员",
            prop: "keeperName",
            minWidth: 80,
            overHidden: true,
            display: false,
          },
          {
            label: "库管人员",
            prop: "keeperId",
            dicUrl: "/api/blade-user/user-list?deptCodes=010801",
            type: "select",
            props: {
              label: "realName",
              value: "id",
            },
            placeholder: " ",
            search: true,
            hide: true,
            showColumn: false,
          },
          {
            label: "入库编号",
            prop: "serialNo",
            search: true,
            minWidth: 140,
            overHidden: true,
            disabled: true,
          },
          {
            label: "仓库",
            prop: "depotId",
            minWidth: 100,
            overHidden: true,
            search: true,
            type: "select",
            placeholder: " ",
            dicUrl: "/api/ni/base/depot/info/list?type=7,8&status=2",
            props: {
              label: "name",
              value: "id",
              desc: "code",
            },
          },
          {
            label: "库位",
            prop: "depotLocation",
            minWidth: 100,
            overHidden: true,
            search: true,
            placeholder: " ",
          },
          {
            label: "到货日期",
            prop: "porOrderArrivalDate",
            placeholder: " ",
            search: true,
            searchRange: true,
            minWidth: 90,
            overHidden: true,
            type: "date",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
          },
          {
            label: "到货单号",
            prop: "porOrderArrivalSerialNo",
            minWidth: 120,
            overHidden: true,
            search: true,
            placeholder: " ",
            sortable: true,
          },
          {
            label: "订单编号",
            prop: "porOrderSerialNo",
            minWidth: 100,
            overHidden: true,
            search: true,
            placeholder: " ",
            sortable: true,
          },
          {
            label: "采购人",
            prop: "purchaseUserName",
            minWidth: 75,
            overHidden: true,
            placeholder: " ",
          },
          {
            label: "采购人",
            prop: "purchaseUserId",
            search: true,
            hide: true,
            showColumn: false,
            remote: true,
            type: "select",
            dicUrl: `/api/blade-user/search/otj/user?name={{key}}`,
            props: {
              label: "realName",
              value: "id",
            },
            dicFormatter: (data) => {
              return data.data.records;
            },
          },
          {
            label: "关联项目",
            prop: "projectSerialNo",
            minWidth: 100,
            overHidden: true,
          },
          {
            label: "关联预算",
            prop: "budgetSerialNo",
            minWidth: 100,
            overHidden: true,
          },
          {
            label: "请购人",
            prop: "personName",
            minWidth: 80,
            overHidden: true,
          },
          {
            label: "请购人",
            prop: "personId",
            type: "select",
            remote: true,
            dicUrl: `/api/blade-user/search/otj/user?name={{key}}`,
            props: {
              label: "realName",
              value: "id",
            },
            dicFormatter: (data) => {
              return data.data.records;
            },
            placeholder: " ",
            filterable: true,
            showColumn: false,
            hide: true,
            display: false,
            search: true,
          },
          {
            label: "序号",
            prop: "row",
            placeholder: " ",
            search: true,
            overHidden: true,
            width: 60,
            searchOrder: 97,
          },
          {
            label: "品名",
            placeholder: " ",
            search: true,
            prop: "materialName",
            minWidth: 100,
            overHidden: true,
            searchOrder: 99,
          },
          {
            label: "规格型号",
            placeholder: " ",
            minWidth: 110,
            overHidden: true,
            prop: "specification",
            search: true,
            searchOrder: 98,
          },
          {
            label: "材质",
            prop: "quality",
            minWidth: 110,
            overHidden: true,
          },
          {
            label: "数量",
            prop: "num",
            minWidth: 90,
            overHidden: true,
            placeholder: " ",
            type: "number",
          },
          {
            label: "单位",
            prop: "unit",
            minWidth: 80,
            overHidden: true,
            type: "select",
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_material_unit",
            placeholder: " ",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
          },
          {
            label: "用途",
            prop: "purpose",
            type: "textarea",
            minRows: 1,
            placeholder: " ",
            overHidden: true,
            minWidth: 80,
          },
          {
            label: "单价",
            prop: "price",
            minWidth: 80,
            overHidden: true,
            placeholder: " ",
            type: "number",
          },
          {
            label: "金额",
            prop: "amount",
            minWidth: 90,
            overHidden: true,
            placeholder: " ",
            type: "number",
          },
          {
            label: "压力容器",
            prop: "pv",
            type: "radio",
            width: 70,
            dicData: [
              {
                label: "是",
                value: 1,
              },
              {
                label: "否",
                value: 0,
              },
            ],
            display: false,
          },
          {
            label: "固定资产",
            prop: "fa",
            type: "radio",
            width: 70,
            dicData: [
              {
                label: "是",
                value: 1,
              },
              {
                label: "否",
                value: 0,
              },
            ],
            display: false,
          },
          {
            label: "易制毒、易制爆",
            prop: "yzdyzb",
            type: "radio",
            width: 70,
            hide: true,
            dicData: [
              {
                label: "是",
                value: 1,
              },
              {
                label: "否",
                value: 0,
              },
            ],
            display: false,
          },
          {
            label: "质量证明",
            prop: "qualityCertificate",
            width: 80,
            overHidden: true,
          },
          {
            label: "炉号",
            prop: "heatNo",
            width: 100,
            overHidden: true,
          },
          {
            label: "材料批号",
            prop: "materialBatchNo",
            width: 100,
            overHidden: true,
          },
          {
            label: "材料编号",
            prop: "materialNo",
            width: 100,
            overHidden: true,
          },
          {
            label: "材料牌号",
            prop: "materialBrand",
            width: 100,
            overHidden: true,
          },
          {
            label: "执行标准",
            prop: "executionStandards",
            width: 100,
            overHidden: true,
          },
          {
            label: "出厂日期",
            prop: "factoryDate",
            type: "date",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            width: 100,
            overHidden: true,
          },
          {
            label: "制造商",
            prop: "manufacturer",
            overHidden: true,
            width: 80,
          },
          {
            label: "账套",
            prop: "brand",
            type: "radio",
            minWidth: 70,
            dicUrl: "/api/blade-system/dict-biz/dictionary?code=ni_brand",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            search: true,
          },
          {
            label: "入库类型",
            prop: "subType",
            type: "select",
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_depot_storage_type",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            placeholder: " ",
            search: true,
            minWidth: 100,
            overHidden: true,
          },
          {
            label: "存货编码",
            prop: "materialCode",
            search: true,
            minWidth: 120,
            overHidden: true,
            placeholder: " ",
          },
          {
            label: "到货状态",
            prop: "arrivalStates",
            type: "select",
            dicData: [
              {
                label: "部分到货",
                value: 2,
              },
              {
                label: "全部到货",
                value: 3,
              },
            ],
            dataType: "number",
            fixed: "right",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            search: true,
          },
          {
            label: "打印",
            prop: "printCount",
            type: "number",
            fixed: "right",
            width: 60,
          },
          {
            label: "入库主题",
            prop: "title",
            minWidth: 120,
            overHidden: true,
            search: true,
          },
          {
            label: "供应商",
            prop: "supplierId",
            type: "select",
            remote: true,
            placeholder: " ",
            dicUrl: `/api/ni/base/supplier/info/page?status=2&keyword={{key}}`,
            props: {
              label: "name",
              value: "id",
              desc: "code",
            },
            dicFormatter: (data) => {
              return data.data.records;
            },
            search: true,
            hide: true,
            showColumn: false,
          },
          {
            label: "供应商",
            prop: "supplierName",
            minWidth: 120,
            overHidden: true,
          },
          {
            label: "SN",
            prop: "sn",
            minWidth: 70,
            overHidden: true,
          },
          {
            label: "备注",
            placeholder: " ",
            type: "textarea",
            minWidth: 120,
            overHidden: true,
            minRows: 1,
            prop: "remark",
          },
          {
            label: "自动申购",
            prop: "auto",
            type: "select",
            dicData: [
              {
                label: "是",
                value: 1,
              },
              {
                label: "否",
                value: 0,
              },
            ],
            display: false,
            width: 70,
            placeholder: " ",
            overHidden: true,
          },
          {
            label: "用友同步",
            prop: "yonyou",
            type: "select",
            dicData: [
              {
                label: "已同步",
                value: true,
              },
              {
                label: "未同步",
                value: false,
              },
            ],
            display: false,
            hide: true,
            showColumn: false,
          },
          {
            label: "用友同步",
            prop: "yonyouSync",
            overHidden: true,
            minWidth: 220,
            addDisplay: false,
            editDisplay: false,
            hide: true,
          },
          {
            label: "是否打印",
            prop: "stockInPrint",
            display: false,
            search: true,
            hide: true,
            type: "select",
            dicData: [
              {
                label: "是",
                value: true,
              },
              {
                label: "否",
                value: false,
              },
            ],
          },
        ],
      },
      data: [],
      yonyouSyncSequenceDict: [],
      yonyouSyncSequenceDictKeyValue: {},
      yonyouSyncStateDict: [],
      yonyouSyncStateDictKeyValue: {},
      export1: {
        column: [
          {
            label: "入库时间",
            prop: "opTime",
          },
          {
            label: "库管人员",
            prop: "keeperName",
          },
          {
            label: "入库编号",
            prop: "serialNo",
          },
          {
            label: "仓库",
            prop: "depotName",
          },
          {
            label: "库位",
            prop: "depotLocation",
          },
          {
            label: "到货单号",
            prop: "porOrderArrivalSerialNo",
          },
          {
            label: "采购人",
            prop: "purchaseUserName",
          },
          {
            label: "关联项目",
            prop: "projectSerialNo",
          },
          {
            label: "关联预算",
            prop: "budgetSerialNo",
          },
          {
            label: "请购人",
            prop: "personName",
          },
          {
            label: "序号",
            prop: "row",
          },
          {
            label: "品名",
            prop: "materialName",
          },
          {
            label: "编码",
            prop: "materialCode",
          },
          {
            label: "规格型号",
            prop: "specification",
          },
          {
            label: "材质",
            prop: "quality",
          },
          {
            label: "数量",
            prop: "num",
          },
          {
            label: "单位",
            prop: "unit",
          },

          {
            label: "用途",
            prop: "purpose",
          },
          {
            label: "单价",
            prop: "price",
          },
          {
            label: "金额",
            prop: "amount",
          },
          {
            label: "供应商",
            prop: "supplierName",
          },
          {
            label: "备注",
            prop: "remark",
          },
          {
            label: "国标",
            prop: "gb",
          },

          {
            label: "采购申请",
            prop: "porApplySerialNo",
          },
          {
            label: "入库类型",
            prop: "subType",
          },
        ],
      },
      pv: false,
      yzdyzb: false,
      auto: false,
      printTemplate: null,
      print: {
        visible: false,
        option: {
          size: "mini",
          span: 24,
          emptyBtn: false,
          column: [
            {
              label: "打印状态",
              prop: "dataPrint",
              type: "radio",
              value: true,
              dicData: [
                {
                  label: "已打印",
                  value: true,
                },
                {
                  label: "未打印",
                  value: false,
                },
              ],
              rules: [
                {
                  required: true,
                  message: "请选择打印状态",
                  trigger: "blur",
                },
              ],
            },
          ],
        },
        form: {},
      },
    };
  },
  created() {
    this.dictInit();
    loadPrintTemplate("ni_stock_in").then((res) => {
      this.printTemplate = JSON.parse(res.data.data.content);
    });
    loadPrintTemplate("ni_order_arrival_stock_in").then((res) => {
      this.stockInPrintTemplate = JSON.parse(res.data.data.content);
    });
  },
  beforeRouteEnter(to, from, next) {
    to.meta.keepAlive = true;
    next();
  },
  methods: {
    async handlePrintStockIn1() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择要打印的数据");
        return;
      }

      const unStockInAll = this.selectionList.some(
        (item) => item.arrivalStates !== 3
      );
      if (unStockInAll) {
        this.$message.warning("请选择已全部入库的数据");
        return;
      }
      if (!this.stockInPrintTemplate) {
        this.$message.error("打印模板加载失败，请刷新页面后重试");
        return;
      }
      const query = {
        type: "in",
        porOrderArrivalSerialNos: this.selectionList.map(
          (item) => item.porOrderArrivalSerialNo
        ),
      };
      const res = await getPage(1, 100000, query);
      const data = res.data.data;
      const printData = data.records;
      const grouped = printData.reduce((acc, item) => {
        const key = item.porOrderArrivalSerialNo;
        if (!acc[key]) {
          acc[key] = [];
        }
        acc[key].push(item);
        return acc;
      }, {});
      let printDataList = [];
      let hiprintTemplate;
      for (const key in grouped) {
        if (grouped.hasOwnProperty(key)) {
          const items = grouped[key];
          const printData = {
            title: "山东能特异能源科技有限公司入库单",
            serialNo: items[0].porOrderArrivalSerialNo,
            supplierName: items[0].supplierName,
            arrivalDate: items[0].porOrderArrivalDate,
            purchaseUserName: items[0].purchaseUserName,
            depotName: items[0].depotName,
            keeperName: this.userInfo.user_name,
            printDate: dateFormat(new Date(), "yyyy-MM-dd"),
            items: items.map((item) => {
              return {
                ...item,
                stockInSerialNo: item.serialNo,
                unitText: this.unitDictKeyValue[item.unit],
                arrivalNum: item.num,
                arrivalAmount: item.amount,
              };
            }),
          };
          if (items[0].brand === "4") {
            printData.title = "演绎智能设备（山东）有限公司入库单";
          } else if (items[0].brand === "2") {
            printData.title = "淄博至简工贸入库单";
          }
          printDataList.push(printData);
        }
      }
      if (printDataList.length === 0) {
        this.$message.warning("没有可打印的数据,请重新选择");
        return;
      }
      hiprintTemplate = new hiprint.PrintTemplate({
        template: this.stockInPrintTemplate,
      });
      hiprintTemplate.print(printDataList);
      const ids = printData.map((i) => i.id);
      printCount(ids.join(",")).then(() => {
        this.onLoad(this.page);
      });
    },
    async handlePrintStockIn(withCost = true) {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择要打印的数据");
        return;
      }
      const unStockInAll = this.selectionList.some(
        (item) => item.arrivalStates !== 3
      );
      if (unStockInAll) {
        this.$message.warning("请选择已全部入库的数据");
        return;
      }
      const res = await getStockPrintData(this.billIds);
      const printData = res.data.data;
      const hasBack = printData.some((item) => item.backNum > 0);
      if (hasBack) {
        this.$confirm("选择的数据中存在退换货的数据，是否全部打印?", {
          distinguishCancelAndClose: true,
          confirmButtonText: "全部打印",
          cancelButtonText: "只打印未退换货数据",
          type: "warning",
        })
          .then(() => {
            this.handlePrintStockInNext(withCost, printData);
          })
          .catch((action) => {
            this.$message({
              type: "info",
              message:
                action === "cancel" ? "放弃保存并离开页面" : "停留在当前页面",
            });
          });
      } else {
        this.handlePrintStockInNext(withCost, printData);
      }
    },
    async handlePrintStockInNext(withCost = true, data) {
      let printDataList = [];
      let hiprintTemplate;
      if (!this.stockInPrintTemplate) {
        this.$message.error("打印模板加载失败，请刷新页面后重试");
        return;
      }
      for (const item of data) {
        const res = await getOrderArrivalDetail(item.id);
        let printData = res.data.data;
        printData.title = "山东能特异能源科技有限公司入库单";

        if (printData.brand === "4") {
          printData.title = "演绎智能设备（山东）有限公司入库单";
        } else if (printData.brand === "2") {
          printData.title = "淄博至简工贸入库单";
        }
        printData.depotName = "";
        printData.keeperName = this.userInfo.user_name;
        printData.printDate = dateFormat(new Date(), "yyyy-MM-dd");
        printData.items = printData.items.filter(
          (item) => withCost || !item.cost
        );
        if (printData.items && printData.items.length > 0)
          printDataList.push(printData);
      }
      if (printDataList.length === 0) {
        this.$message.warning("没有可打印的数据,请重新选择");
        return;
      }
      hiprintTemplate = new hiprint.PrintTemplate({
        template: this.stockInPrintTemplate,
      });
      hiprintTemplate.print(printDataList);
      setTimeout(() => {
        this.handlePrintSign(printDataList);
      }, 1000);
    },
    handlePrintSign(printDataList) {
      let msg = `是否将到货单号的入库明细标记为已打印?`;
      this.$confirm(msg, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        dangerouslyUseHTMLString: true,
        type: "warning",
      }).then(() => {
        let arrivalSerialNos = [];
        printDataList.forEach((item) => {
          arrivalSerialNos.push(item.serialNo);
        });
        arrivalSerialNos = arrivalSerialNos.join(",");
        // return;
        printSign(arrivalSerialNos).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
      });
    },
    handleYzdyzbChange(val) {
      if (val) this.query.yzdyzb = 1;
      else this.query.yzdyzb = null;
      this.page.currentPage = 1;
      this.onLoad(this.page);
    },
    handleLog() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择要查看的数据");
        return;
      } else if (this.selectionList.length > 1) {
        this.$message.warning("只能选择一条数据");
        return;
      }
      if (this.selectionList[0].porOrderArrivalId) {
        getArrivalDetail(this.selectionList[0].porOrderArrivalId).then(
          (res) => {
            const { id, orderItemId } = res.data.data;
            this.module = "ni_por_order_arrival,ni_por_order";
            this.$refs.logOptDialogRef.init(id + "," + orderItemId);
          }
        );
      } else {
        this.module = "ni_por_order_arrival,ni_por_order";
        this.$refs.logOptDialogRef.init(this.selectionList[0].id);
      }
    },
    rowClick(row) {
      this.$refs.crud.toggleSelection([row]);
    },
    cellStyle({ row, column }) {
      if ("opTime" === column.columnKey && row.red) {
        return {
          backgroundColor: "#F56C6C",
          color: "#fff",
        };
      }
      if ("keeperName" === column.columnKey && row.dataPrint) {
        return {
          backgroundColor: "#909399",
          color: "#fff",
        };
      }
      if ("porOrderArrivalSerialNo" === column.columnKey && row.stockInPrint) {
        return {
          backgroundColor: "#2E8B57",
          color: "#fff",
        };
      }
      if ("materialName" === column.columnKey && row.yzdyzb === 1) {
        return {
          background:
            "#F56C6C url(/img/yzdyzb.png) no-repeat left center / contain",
          color: "#fff",
        };
      }
    },
    async handlePrint1() {
      if (this.page.total > 100) {
        this.$message.warning("单次最多打印100条数据，请重新筛选");
        return;
      }

      if (!this.printTemplate) {
        this.$message.error("打印模板加载失败，请联系管理员");
        return;
      }
      let hiprintTemplate;
      let printedList = []; //存储明细打印id
      const groupedItems = new Map(); //存储分组后的结果

      getPrintData(this.allQuery).then((res) => {
        this.printData = res.data.data;

        for (let printDataItem of this.printData) {
          printDataItem.opTime =
            printDataItem.opTime != null
              ? printDataItem.opTime.slice(0, 10)
              : "";
          printDataItem.subType =
            printDataItem.subType != null
              ? this.subTypeDictKeyValue[printDataItem.subType]
              : "";
          printDataItem.depotName = this.depotKeyValue[printDataItem.depotId];
          printDataItem.arrivalSerialNo = printDataItem.porOrderArrivalSerialNo;
          let key = `${printDataItem.opTime}&${printDataItem.porOrderArrivalSerialNo}&${printDataItem.supplierId}`;
          if (!groupedItems.has(key)) {
            groupedItems.set(key, []);
          }
          groupedItems.get(key).push(printDataItem);
        }
        //分组结果存储在数组中
        const groupedItemsArray = Array.from(groupedItems.values());
        // console.log(groupedItemsArray);
        const result = groupedItemsArray.map((items) => {
          items.arrivalSerialNo =
            items.length > 0 && items[0].porOrderArrivalSerialNo != null
              ? items[0].porOrderArrivalSerialNo
              : "";
          items = items.map((item) => {
            printedList.push(item.id);
            return {
              ...item,
              unit: this.unitDictKeyValue[item.unit],
              price: (
                (Number(item.amount) * 10000) /
                Number(item.num) /
                10000
              ).toLocaleString("zh-CN", {
                minimumFractionDigits: 2,
              }),
            };
          });
          return {
            ...items[0],
            items: items,
          };
        });
        hiprintTemplate = new hiprint.PrintTemplate({
          template: this.printTemplate,
        });
        hiprintTemplate.print(result);
        markPrint(printedList, true).then(() => {
          this.onLoad(this.page, this.query);
        });
      });
    },
    //数据打印
    async handlePrint() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择数据");
        return;
      }

      if (!this.printTemplate) {
        this.$message.error("打印模板加载失败，请联系管理员");
        return;
      }
      let hiprintTemplate;
      let printedList = []; //存储明细打印id
      const groupedItems = new Map(); //存储分组后的结果

      for (let printDataItem of this.selectionList) {
        printDataItem.opTime =
          printDataItem.opTime != null ? printDataItem.opTime.slice(0, 10) : "";
        printDataItem.subType =
          printDataItem.subType != null
            ? this.subTypeDictKeyValue[printDataItem.subType]
            : "";
        printDataItem.depotName = this.depotKeyValue[printDataItem.depotId];
        printDataItem.arrivalSerialNo = printDataItem.porOrderArrivalSerialNo;
        let key = `${printDataItem.opTime}&${printDataItem.porOrderArrivalSerialNo}&${printDataItem.supplierId}`;
        if (!groupedItems.has(key)) {
          groupedItems.set(key, []);
        }
        groupedItems.get(key).push(printDataItem);
      }
      //分组结果存储在数组中
      const groupedItemsArray = Array.from(groupedItems.values());
      // console.log(groupedItemsArray);
      const result = groupedItemsArray.map((items) => {
        items.arrivalSerialNo =
          items.length > 0 && items[0].porOrderArrivalSerialNo != null
            ? items[0].porOrderArrivalSerialNo
            : "";
        items = items.map((item) => {
          printedList.push(item.id);
          return {
            ...item,
            unit: this.unitDictKeyValue[item.unit],
            price: (
              (Number(item.amount) * 10000) /
              Number(item.num) /
              10000
            ).toLocaleString("zh-CN", {
              minimumFractionDigits: 2,
            }),
          };
        });
        return {
          ...items[0],
          items: items,
        };
      });
      hiprintTemplate = new hiprint.PrintTemplate({
        template: this.printTemplate,
      });
      hiprintTemplate.print(result);
      markPrint(printedList.join(","), true).then(() => {
        this.onLoad(this.page, this.query);
      });
    },
    rowPvShow(row) {
      this.$refs.pvRegisterRef.onDetail(row.porOrderArrivalId);
    },
    rowQualityCertificate(row) {
      this.$refs.attachDialogRef.init(
        row.porOrderArrivalId,
        "ni_por_pv_quality"
      );
    },
    handleMarkPrint() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择要标记的数据");
        return;
      }
      this.print.form.dataPrint = true;
      this.print.visible = true;
    },
    handleMarkPrintSubmit(form, done) {
      markPrint(this.ids, form.dataPrint)
        .then(() => {
          this.onLoad(this.page, this.query);
          this.print.visible = false;
          this.$message.success("修改成功！");
        })
        .finally(() => {
          done();
        });
    },
    handleSetFixedAsset() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择要转换的数据");
        return;
      }

      const returnItem = this.selectionList.some((item) => item.num <= 0);
      if (returnItem) {
        this.$message.warning("选中的数据中存在红字单据，请重新选择");
        return;
      }

      const subType = this.selectionList.some((item) => item.subType === "109");
      if (subType) {
        this.$message.warning("选中的数据中存在库位调整的数据，请重新选择");
        return;
      }

      const brands = new Set();
      const returnF = new Set();
      const con = new Set();
      this.selectionList.forEach((item) => {
        if (item.brand) brands.add(item.brand);
        if (item.returnNum >= item.num) returnF.add(item.id);
        if (item.outConfirmState !== 2) con.add(item.id);
      });

      if (brands.size > 1) {
        this.$message.warning("选中的数据中存在不同账套的数据，请重新选择");
        return;
      }

      if (returnF.size > 0) {
        this.$message.warning("选中的数据中存在已经退库完成的数据，请重新选择");
        return;
      }
      // 通过所有验证后执行转固定资产操作
      const fa = true;
      const ids = this.selectionList
        .map((item) => item.porApplyItemId)
        .join(",");
      changeFa(ids, fa).then(() => {
        const selectionList = this.selectionList.map((item) => {
          return {
            ...item,
            fa: true,
          };
        });
        this.$refs.setFixedAssetRef.init(selectionList);
      });
    },

    //固定资产回调
    handleSetFixedAssetBack() {
      this.onLoad(this.page);
      this.$message({
        type: "success",
        message: "操作成功!",
      }).catch((error) => {
        this.$message.error("转固定资产失败: " + error.message);
      });
    },
    handleRed() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择要退库的数据");
        return;
      }
      const returnItem = this.selectionList.some((item) => item.num <= 0);
      if (returnItem) {
        this.$message.warning("选中的数据中存在红字单据，请重新选择");
        return;
      }
      const subType = this.selectionList.some((item) => item.subType === "109");
      if (subType) {
        this.$message.warning("选中的数据中存在库位调整的数据，请重新选择");
        return;
      }
      const brands = new Set();
      const returnF = new Set();
      const con = new Set();
      this.selectionList.forEach((item) => {
        if (item.brand) brands.add(item.brand);
        if (item.returnNum >= item.num) returnF.add(item.id);
        if (item.outConfirmState !== 2) con.add(item.id);
      });
      if (brands.size > 1) {
        this.$message.warning("选中的数据中存在不同账套的数据，请重新选择");
        return;
      }
      if (returnF.size > 0) {
        this.$message.warning("选中的数据中存在已经退库完成的数据，请重新选择");
        return;
      }
      this.$refs.outReturnRef.init(this.selectionList);
    },
    handleExport() {
      let msg =
        '是否导出<span style="color: #F56C6C;font-weight: bold">所有数据</span>?';
      if (this.selectionList.length > 0) {
        msg =
          '是否要导出<span style="color: #F56C6C;font-weight: bold">当前选中的数据</span>？';
      }
      let data = [];
      this.$confirm(msg, {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        dangerouslyUseHTMLString: true,
        type: "warning",
      }).then(async () => {
        if (this.selectionList.length > 0) {
          data = this.selectionList;
        } else {
          const query = {
            ...this.params,
            ...this.query,
          };
          if (
            query.porOrderArrivalDate &&
            query.porOrderArrivalDate.length === 2
          ) {
            query.startArrivalDate = query.porOrderArrivalDate[0];
            query.endArrivalDate = query.porOrderArrivalDate[1];
            query.porOrderArrivalDate = null;
          }
          if (query.opTime && query.opTime.length === 2) {
            query.startOpTime = query.opTime[0];
            query.endOpTime = query.opTime[1];
            query.opTime = null;
          }
          if (query.subType && query.subType.length === 2) {
            query.type = query.subType[0];
            query.subType = query.subType[1];
          }
          query.type = "in";
          query.unAdjust = true;
          const res = await getPage(1, 999999, query);
          data = res.data.data.records;
        }
        this.$Export.excel({
          title: "入库明细",
          columns: this.export1.column,
          data: data.map((item) => {
            return {
              ...item,
              unit: this.unitDictKeyValue[item.unit],
              brand: this.brandDictKeyValue[item.brand],
              subType: this.subTypeDictKeyValue[item.subType],
              depotName: this.depotKeyValue[item.depotId],
            };
          }),
        });
      });
    },
    dictInit() {
      this.$http
        .get("/api/ni/base/depot/info/list?type=7,8&status=2")
        .then((res) => {
          this.depotKeyValue = res.data.data.reduce((acc, cur) => {
            acc[cur.id] = cur.name;
            return acc;
          }, {});
        });
      this.$http
        .get("/api/blade-system/dict-biz/dictionary?code=ni_brand")
        .then((res) => {
          this.brandDictKeyValue = res.data.data.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });

      this.$http
        .get("/api/blade-system/dict/dictionary?code=yonyou_sync_sequence")
        .then((res) => {
          this.yonyouSyncSequenceDict = res.data.data;
          this.yonyouSyncSequenceDictKeyValue =
            this.yonyouSyncSequenceDict.reduce((acc, cur) => {
              acc[cur.dictKey] = cur.dictValue;
              return acc;
            }, {});
        });
      this.$http
        .get("/api/blade-system/dict/dictionary?code=yonyou_sync_state")
        .then((res) => {
          this.yonyouSyncStateDict = res.data.data;
          this.yonyouSyncStateDictKeyValue = this.yonyouSyncStateDict.reduce(
            (acc, cur) => {
              acc[cur.dictKey] = cur.dictValue;
              return acc;
            },
            {}
          );
        });
      this.$http
        .get("/api/blade-system/dict-biz/dictionary?code=ni_depot_storage_type")
        .then((res) => {
          this.subTypeDict = res.data.data;
          this.subTypeDictKeyValue = this.subTypeDict.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });
      this.$http
        .get("/api/blade-system/dict-biz/dictionary?code=ni_material_unit")
        .then((res) => {
          this.unitDict = res.data.data;
          this.unitDictKeyValue = this.unitDict.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });
    },
    searchReset() {
      this.query = {};
      this.pv = false;
      this.yzdyzb = false;
      this.auto = false;
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    sortChange({ prop, order }) {
      console.log(prop, order);
      if (prop === "porOrderArrivalSerialNo") {
        this.query.prop = "por_order_arrival_serial_no";
      } else if (prop === "porOrderSerialNo") {
        this.query.prop = "por_order_serial_no";
      } else {
        this.query.prop = null;
      }
      if (order) this.query.order = order;
      else {
        this.query.order = null;
        this.query.prop = null;
      }
      this.onLoad(this.page);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      const query = {
        ...params,
        ...this.query,
      };
      if (query.porOrderArrivalDate && query.porOrderArrivalDate.length === 2) {
        query.startArrivalDate = query.porOrderArrivalDate[0];
        query.endArrivalDate = query.porOrderArrivalDate[1];
        query.porOrderArrivalDate = null;
      }
      if (query.opTime && query.opTime.length === 2) {
        query.startOpTime = query.opTime[0];
        query.endOpTime = query.opTime[1];
        query.opTime = null;
      }
      if (query.subType && query.subType.length === 2) {
        query.type = query.subType[0];
        query.subType = query.subType[1];
      }
      query.type = "in";
      query.unAdjust = true;
      this.allQuery = query;
      getPage(page.currentPage, page.pageSize, query).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
  },
};
</script>

<style scoped></style>

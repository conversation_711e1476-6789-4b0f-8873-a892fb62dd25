import { shallowRef } from "vue";

export default {
  height: "auto",
  calcHeight: 30,
  tip: false,
  searchIcon: true,
  searchShow: true,
  indexLabel: "序号",
  searchMenuSpan: 6,
  border: true,
  index: false,
  viewBtn: true,
  addBtn: true,
  editBtn: true,
  delBtn: true,
  selection: true,
  dialogClickModal: false,
  menuWidth: 220,
  labelWidth: 150,
  column: [
    {
      label: "批次号",
      prop: "batchCode",
      type: "input",
      search: true,
      disabled: true,
      searchOrder: 30,
      width: 150,
    },
    {
      label: "批次时间",
      prop: "batchEndTime",
      type: "datetime",
      search: true,
      searchRange: true,
      searchOrder: 40,
      width: 180,
      disabled: true,
    },
    {
      label: "国内/外",
      prop: "area",
      type: "select",
      search: true,
      width: 100,
      addDisplay: false,
      dicData: [
        {
          label: "国内",
          value: "CN",
        },
        {
          label: "国外",
          value: "OS",
        }
        ],
    },
    {
      label: "规格",
      prop: "spec",
      type: "input",
      disabled: true,
      searchOrder: 30,
      width: 90,
    },
    {
      label: "内包装",
      prop: "innerPackage",
      type: "input",
      width: 150,
      disabled: true,
    },
    {
      label: "外包装",
      prop: "outerPackaging",
      type: "input",
      width: 150,
      disabled: true,
    },

    {
      label: "质量",
      prop: "qualityName",
      type: "input",
      width: 180,
      disabled: true,
    },

    {
      label: "质检报告",
      prop: "code",
      type: "input",
      width: 180,
      disabled: true,
      search: true,
    },
    {
      label: "质检日期",
      prop: "date",
      type: "date",
      format: "yyyy-MM-dd",
      valueFormat: "yyyy-MM-dd",
      width: 150,
    },
    {
      label: "堆积密度",
      prop: "bulkDensity",
      type: "input",
      width: 100,
    },
    {
      label: "烧后LiCl",
      prop: "afterBurningLiCl",
      type: "input",
      width: 100,
    },
    {
      label: "粒度合格率",
      prop: "particleSizeQualifiedRate",
      type: "input",
      width: 100,
    },
    {
      label: "含水",
      prop: "waterContent",
      type: "input",
      width: 100,
    },
    {
      label: "气体解吸量",
      prop: "gasDesorption",
      type: "input",
      width: 100,
    },
    {
      label: "温升20g",
      prop: "temperatureRise20",
      type: "input",
      width: 100,
    },
    {
      label: "磨前落粉",
      prop: "fallingPowderBeforeGrinding",
      type: "input",
      width: 100,
    },
    {
      label: "烧后NaCl",
      prop: "afterBurningNaCl",
      type: "input",
      width: 100,
    },
    {
      label: "有效水吸附",
      prop: "effectiveWaterAdsorption",
      type: "input",
      width: 100,
    },
    {
      label: "强度",
      prop: "strength",
      type: "input",
      width: 100,
    },
    {
      label: "变异系数",
      prop: "coefficientOfVariation",
      type: "input",
      width: 100,
    },
    {
      label: "粒度",
      prop: "particleSize",
      type: "input",
      width: 100,
    },
    {
      label: "温升10g",
      prop: "temperatureRise10",
      type: "input",
      width: 100,
    },
    {
      label: "温升30g",
      prop: "temperatureRise30",
      type: "input",
      width: 100,
    },
    {
      label: "磨后落粉",
      prop: "fallingPowderAfterGrinding",
      type: "input",
      width: 100,
    },
    {
      label: "渣子",
      prop: "waste",
      type: "input",
      width: 100,
    },
    {
      label: "170度吸气量",
      prop: "inspiratoryVolume170t",
      type: "input",
      width: 100,
    },
    {
      label: "70度吸气量",
      prop: "inspiratoryVolume70t",
      type: "input",
      width: 100,
    },
    {
      label: "96H吸气量",
      prop: "inspiratoryVolume96h",
      type: "input",
      width: 100,
    },
    {
      label: "敦实堆积密度",
      prop: "solidBulkDensity",
      type: "input",
      width: 100,
    },
    {
      label: "欧标堆积密度",
      prop: "enBulkDensity",
      type: "input",
      width: 100,
    },
    {
      label: "温升50g",
      prop: "temperatureRise50",
      type: "input",
      width: 100,
    },
    {
      label: "5g落粉",
      prop: "fallingPowder5g",
      type: "input",
      width: 100,
    },
    {
      label: "PH值",
      prop: "ph",
      type: "input",
      width: 100,
    },
    {
      label: "KOH",
      prop: "koh",
      type: "input",
      width: 100,
    },
    {
      label: "欧标吸附",
      prop: "adsorptionEN",
      type: "input",
      width: 100,
    },
  ],
};

import request from "@/router/axios";

export const getList = (current, size, params) => {
  return request({
    url: "/api/ni/product/sku/list",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/ni/product/sku/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const remove = (ids) => {
  return request({
    url: "/api/ni/product/sku/remove",
    method: "post",
    params: {
      ids,
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/ni/product/sku/submit",
    method: "post",
    data: row,
  });
};

export const update = (row) => {
  return request({
    url: "/api/ni/product/sku/submit",
    method: "post",
    data: row,
  });
};
export const play = (ids) => {
  return request({
    url: "/api/ni/product/sku/play",
    method: "post",
    params: {
      ids,
    },
  });
};

export const pause = (ids) => {
  return request({
    url: "/api/ni/product/sku/pause",
    method: "post",
    params: {
      ids,
    },
  });
};

export const changeQualityLevel = (ids, qualityLevel) => {
  return request({
    url: "/api/ni/product/sku/changeQualityLevel",
    method: "post",
    params: {
      ids,
      qualityLevel,
    },
  });
};

export const changeTaishanBatch = (ids, taishan) => {
  return request({
    url: "/api/ni/product/sku/changeTaishanBatch",
    method: "post",
    params: {
      ids,
      taishan,
    },
  });
};
export const getDetailByMaterialCode = (materialCode) => {
  return request({
    url: "/api/ni/product/sku/detailByMaterialCode",
    method: "get",
    params: {
      materialCode,
    },
  });
};

```java
// 定义包路径，属于问题反馈模块的数据传输对象包
package com.natergy.ni.feedback.dto;

// 导入Lombok的@Data注解，用于自动生成JavaBean的常用方法
import lombok.Data;

/**
 * <AUTHOR>  // 作者标识
 */
// @Data：Lombok注解，自动生成该类所有字段的getter、setter方法，
// 同时生成toString()、equals()、hashCode()等方法，简化代码编写
public class DeleteResponseDTO {
    // 表示删除操作是否成功的标志位
    private Boolean success;
    // 存储删除操作的结果信息（如成功提示或错误原因）
    private String message;
}
```

### 类功能说明

该类是一个用于封装删除操作响应结果的数据传输对象（DTO），主要用于：

1. 作为删除接口的返回载体，向调用方传递操作结果
2. 通过`success`字段明确标识操作是否成功
3. 通过`message`字段提供详细的结果描述（成功时返回提示信息，失败时返回错误原因）

通常与`DeletePostDTO`配合使用，前者作为删除请求的参数，后者作为删除响应的结果。这种设计符合 RESTful API 的规范，使接口的输入输出更加清晰可维护。
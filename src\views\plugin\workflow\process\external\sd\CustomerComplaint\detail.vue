<template>
  <basic-container>
    <avue-skeleton :loading="waiting" avatar :rows="8">
      <avue-affix id="avue-view" :offset-top="114">
        <div class="header">
          <avue-title :value="process.processDefinitionName"></avue-title>
          <div v-if="process.status != 'todo'">
            主题：
            <avue-select
              v-model="theme"
              size="mini"
              :clearable="false"
              :dic="themeList"
            ></avue-select>
          </div>
        </div>
      </avue-affix>
      <el-tabs v-model="activeName">
        <el-tab-pane label="申请信息" name="first">
          <el-card shadow="never">
            <div
              id="printBody"
              :class="process.status != 'todo' ? `wf-theme-${theme}` : ''"
            >
              <avue-form
                v-if="
                  option &&
                  ((option.column && option.column.length > 0) ||
                    (option.group && option.group.length > 0))
                "
                v-model="form"
                ref="form"
                :defaults.sync="defaults"
                :option="option"
                :upload-preview="handleUploadPreview"
              >
                <template #divider>
                  <div class="form-divider"></div>
                </template>
              </avue-form>
            </div>
          </el-card>
<!--              <el-card shadow="never">-->
<!--                <div-->
<!--                  id="printBody"-->
<!--                  :class="process.status != 'todo' ? `wf-theme-${theme}` : ''"-->
<!--                >-->
<!--              <avue-form-->
<!--                v-if="-->
<!--                process.status == 'todo' && process.processIsFinished != 'reject' &&-->
<!--                  option1 &&-->
<!--                  ((option1.column && option1.column.length > 0) ||-->
<!--                    (option1.group && option1.group.length > 0))-->
<!--                "-->
<!--                v-model="form1"-->
<!--                ref="form1"-->
<!--                :defaults.sync="defaults1"-->
<!--                :option="option1"-->
<!--                :upload-preview="handleUploadPreview"-->
<!--              >-->
<!--              </avue-form>-->
<!--            </div>-->
<!--          </el-card>-->
          <el-card
            shadow="never"
            style="margin-top: 20px"
            v-if="process.status == 'todo'"
          >
            <wf-examine-form
              ref="examineForm"
              :comment.sync="comment"
              :process="process"
              @user-select="handleUserSelect"
            ></wf-examine-form>
          </el-card>
        </el-tab-pane>
        <el-tab-pane label="流转信息" name="second">
          <el-card shadow="never" style="margin-top: 5px">
            <wf-flow :flow="flow"></wf-flow>
          </el-card>
        </el-tab-pane>
        <el-tab-pane label="流程跟踪" name="third">
          <template v-if="activeName == 'third'">
            <el-card shadow="never" style="margin-top: 5px">
              <wf-design
                ref="bpmn"
                style="height: 500px"
                :options="bpmnOption"
              ></wf-design>
            </el-card>
          </template>
        </el-tab-pane>
      </el-tabs>
    </avue-skeleton>

    <!-- 底部按钮 -->
    <wf-button
      :loading="submitLoading"
      :button-list="buttonList"
      :process="process"
      :comment="comment"
      @examine="handleExamine"
      @user-select="handleUserSelect"
      @print="handlePrint"
      @rollback="handleRollbackTask"
      @terminate="handleTerminateProcess"
      @withdraw="handleWithdrawTask"
    ></wf-button>
    <!-- 人员选择弹窗 -->
    <user-select
      ref="user-select"
      :check-type="checkType"
      :default-checked="defaultChecked"
      @onConfirm="handleUserSelectConfirm"
    ></user-select>
  </basic-container>
</template>

<script>
import WfExamineForm from "@/views/plugin/workflow/process/components/examForm.vue";
import WfButton from "@/views/plugin/workflow/process/components/button.vue";
import WfFlow from "@/views/plugin/workflow/process/components/flow.vue";
import userSelect from "@/views/plugin/workflow/process/components/user-select";
import { changeFa } from "@/api/ni/por/apply-item";
import exForm from "@/views/plugin/workflow/mixins/ex-form";
import theme from "@/views/plugin/workflow/mixins/theme";
import UserSelect1 from "@/components/user-select";
import FinLoanSelect from "@/views/ni/fin/components/FinLoanSelect";

export default {
  mixins: [exForm, theme],
  components: {
    userSelect,
    WfExamineForm,
    WfButton,
    WfFlow,
    UserSelect1,
    FinLoanSelect,
  },
  activated() {
    let val = this.$route.query.p;
    if (val) {
      this.submitLoading = false;
      Object.keys(this.form).forEach((key) => (this.form[key] = ""));
      const param = JSON.parse(Buffer.from(val, "base64").toString());
      const { taskId, processInsId } = param;
      if (processInsId) {
        this.getDetail(taskId, processInsId);
      }
    }
  },
  data() {
    return {
      processStatus: false,
      // taskStatus: "done",
      // taskDefinitionKey: "",
      activeName: "first",
      // defaults1: {},
      // form1: {
      //   inChargePerson: "",
      //   chargeDept: "",
      //   solution: "",
      //   processTime: "",
      //   attach1: [],
      //   // items: [],
      // },
      // option1: {
      //   span: 8,
      //   cancelBtn: false,
      //   editBtn: false,
      //   delBtn: false,
      //   submitBtn: false,
      //   clearBtn: false,
      //   emptyBtn: false,
      //   column: [
      //     {
      //       label: "负责部门",
      //       prop: "chargeDept",
      //       type: "tree",
      //       disabled: true,
      //       dicUrl: `/api/blade-system/dept/list`,
      //       props: {
      //         label: 'deptName',
      //         value: 'id',
      //       },
      //     },
      //     {
      //       label: "负责人",
      //       prop: "inChargePerson",
      //       type: "select",
      //       disabled: true,
      //       dicUrl:`/api/blade-user/user-list`,
      //       props: {
      //         label: "name",
      //         value: "id",
      //       },
      //     },
      //     {
      //       label: "处理时间",
      //       prop: "processTime",
      //       type: "date",
      //       format: "yyyy-MM-dd hh:mm:ss",
      //       valueFormat: "yyyy-MM-dd hh:mm:ss",
      //       search: true,
      //       overHidden: true,
      //       minWidth: 100,
      //       rules: [{
      //         required: true,
      //         message: "请输入投诉时间",
      //         trigger: "blur"
      //       }],
      //     },
      //     {
      //       label: "解决方案",
      //       prop: "solution",
      //       type: "textarea",
      //       span: 24,
      //       rules: [{
      //         required: true,
      //         message: "请输入解决方案",
      //         trigger: "blur"
      //       }],
      //     },
      //     {
      //       label: "附件",
      //       type: "upload",
      //       propsHttp: {
      //         res: "data",
      //         url: "attachId",
      //         name: "originalName",
      //       },
      //       action: "/api/blade-resource/oss/endpoint/put-file-attach",
      //       display: true,
      //       span: 24,
      //       showFileList: true,
      //       multiple: true,
      //       limit: 10,
      //       prop: "attach1",
      //     },
      // ]
      // },
      defaults: {},
      form: {
        // items: [],
          inChargePerson: "",
          chargeDept: "",
          solution: "",
          processTime: "",
          attach1: [],
      },
      option: {
        span: 8,
        // detail: true,
        header: false,
        cellBtn: true,
        addBtn: false,
        refreshBtn: false,
        columnBtn: false,
        menu: false,
        saveBtn: false,
        updateBtn: false,
        cancelBtn: false,
        editBtn: false,
        delBtn: false,
        submitBtn: false,
        emptyBtn: false,
        dialogFullscreen: true,
        size: "mini",
        align: "center",
        calcHeight: 30,
        tip: false,
        border: true,
        viewBtn: false,
        dialogClickModal: false,
        selection: false,
        // sumColumnList: [
        //   {
        //     name: "num",
        //     type: "sum",
        //     decimals: 1,
        //   },
        //   {
        //     name: "amount",
        //     type: "sum",
        //   },
        // ],
        column: [
          {
            label: "投诉编号",
            prop: "complaintNumber",
            type: "input",
            placeholder: "投诉编号自动生成",
            disabled: true,
          },
          {
            label: "销售区域",
            prop: "salesArea",
            type: "select",
            display: true,
            disabled: true,
            dicData: [
              { label: '国内', value: '1' },
              { label: '国外', value: '2' }
            ],
            rules: [
              {
                required: true,
                message: "请输入销售区域",
                trigger: "blur",
              },
            ],
          },
          {
            type: "select",
            label: "投诉类型",
            display: true,
            prop: "complaintType",
            dicUrl: "/api/blade-system/dict-biz/dictionary?code=ni_sd_customer_complaint_type",
            dataType: "number",
            disabled: true,
            props: {
              label: "dictValue",
              value: "dictKey"
            },
            rules: [{
              required: true,
              message: "请输入投诉类型",
              trigger: "blur"
            }],
          },
          {
            label: "分类",
            prop: "classification",
            type: "select",
            dicUrl: "/api/blade-system/dict-biz/dictionary?code=ni_sd_customer_complaint_classification",
            dataType: "number",
            disabled: true,
            props: {
              label: "dictValue",
              value: "dictKey"
            },
            rules: [{
              required: true,
              message: "请输入分类",
              trigger: "blur"
            }],
          },
          {
            label: "产品批号",
            prop: "prodBatchNum",
            type: "input",
            // search: true,
            disabled: true,
            rules: [{
              required: true,
              message: "请输入产品批号",
              trigger: "blur"
            }],
          },
          {
            label: "投诉时间",
            prop: "complaintTime",
            type: "date",
            format: "yyyy-MM-dd hh:mm:ss",
            valueFormat: "yyyy-MM-dd hh:mm:ss",
            // search: true,
            // overHidden: true,
            // minWidth: 100,
            disabled: true,
            rules: [{
              required: true,
              message: "请输入投诉时间",
              trigger: "blur"
            }],
          },
          {
            label: "业务经理",
            prop: "businessManager",
            type: "select",
            dicData: [],
            disabled: true,
            // props: {
            //   label: "dictValue",
            //   value: "dictKey",
            // },
            // dicUrl: "/api/blade-user/user-list?deptIds=1558250198611173378",//1648593234170556437
            props: {
              label: "account",
              value: "id",
            },
            rules: [{
              required: true,
              message: "请输入业务经理",
              trigger: "blur"
            }],
          },
          {
            label: "客户编码",
            prop: "customerCode",
            type: "input",
            disabled: true,
            // rules: [{
            //   required: true,
            //   message: "请输入客户编码",
            //   trigger: "blur"
            // }],
          },
          {
            label: "客户名称",
            prop: "customerName",
            type: "input",
            disabled: true,
            rules: [{
              required: true,
              message: "请输入客户名称",
              trigger: "blur"
            }],
          },
          {
            label: "投诉内容",
            prop: "complaintContent",
            type: "textarea",
            disabled: true,
            span: 24,
            rules: [{
              required: true,
              message: "请输入投诉内容",
              trigger: "blur"
            }],
          },
          {
            label: "附件",
            type: "upload",
            disabled: true,
            propsHttp: {
              res: "data",
              url: "attachId",
              name: "originalName",
            },
            action: "/api/blade-resource/oss/endpoint/put-file-attach",
            display: true,
            span: 24,
            showFileList: true,
            multiple: true,
            limit: 10,
            prop: "attach",
          },
          {
            // label: '', // 标签会被自定义内容覆盖
            prop: 'divider',
            type: 'slot', // 使用插槽自定义内容
            slot: 'divider', // 对应 template 中的 slot 名称
            display: false, // 确保显示
            span: 24 // 占满一行
          },
          {
            label: "处理时间",
            prop: "processTime",
            type: "date",
            format: "yyyy-MM-dd hh:mm:ss",
            valueFormat: "yyyy-MM-dd hh:mm:ss",
            // search: true,
            overHidden: true,
            minWidth: 100,
            display: false,
            rules: [{
              required: true,
              message: "请输入投诉时间",
              trigger: "blur"
            }],
          },
          {
            label: "负责人",
            prop: "inChargePerson",
            type: "select",
            disabled: true,
            display: false,
            dicUrl:`/api/blade-user/user-list`,
            props: {
              label: "name",
              value: "id",
            },
          },
          {
            label: "负责部门",
            prop: "chargeDept",
            type: "tree",
            disabled: true,
            display: false,
            dicUrl: `/api/blade-system/dept/list`,
            props: {
              label: 'deptName',
              value: 'id',
            },
          },
          {
            label: "解决方案",
            prop: "solution",
            type: "textarea",
            span: 24,
            display: false,
            // hide: true,
            rules: [{
              required: true,
              message: "请输入解决方案",
              trigger: "blur"
            }],
          },
          {
            label: "附件",
            type: "upload",
            propsHttp: {
              res: "data",
              url: "attachId",
              name: "originalName",
            },
            action: "/api/blade-resource/oss/endpoint/put-file-attach",
            display: false,
            span: 24,
            showFileList: true,
            multiple: true,
            limit: 10,
            prop: "attach1",
          },

        ],
      },
      vars: [], // 需要提交的字段
      submitLoading: false, // 提交时按钮loading
      typeDict: [],
      typeDictKeyValue: {},
      inquiries: {},
    };
  },
  computed: {
    solutionDisplay() {
      // 反转逻辑，使 processStatus 为 false 时隐藏
      return this.processStatus;
    }
  },
  watch: {
    processStatus: {
      handler(val) {
        console.log("watch:" + val);
        // 找到解决方案字段并修改其 display 属性
        const solutionField = this.option.column.find(item => item.prop === 'chargeDept');
        if (solutionField) {
          solutionField.display = val;
        }
        const solutionField1 = this.option.column.find(item => item.prop === 'inChargePerson');
        if (solutionField1) {
          solutionField1.display = val;
        }
        const solutionField2 = this.option.column.find(item => item.prop === 'solution');
        if (solutionField2) {
          solutionField2.display = val;
        }
        const solutionField3 = this.option.column.find(item => item.prop === 'attach1');
        if (solutionField3) {
          solutionField3.display = val;
        }
        const solutionField4 = this.option.column.find(item => item.prop === 'processTime');
        if (solutionField4) {
          solutionField4.display = val;
        }
        const solutionField5 = this.option.column.find(item => item.prop === 'divider');
        if (solutionField5) {
          solutionField5.display = val;
        }
      },
      // immediate: true,
    },
  },
  created() {
    this.dictInit();
    // this.form.inChargePerson = this.userInfo.user_id;
    // this.form.chargeDept = this.userInfo.dept_id;
    // this.processStatus = true
  },
  methods: {
    dictInit() {
      this.$http
        //国内销售部和国外销售部
        .get("/api/blade-user/user-list?deptIds=1558250198611173378,1558250278609133570")
        .then((res) => {
          const column = this.findObject(this.option.column, "businessManager");
          let dt1 = res.data.data;
          let dt2=[];
          for (let i = 0; i < dt1.length; i++) {
            //岗位是国内销售经理和国外销售经理
            if (dt1[i].postId === '1648593234170556437' || dt1[i].postId === '1648593234170556441') {
              dt2.push(dt1[i])
            }
          }
          column.dicData = dt2
        });
    },
    // rowFaChange(fa, row, index) {
    //   changeFa(row.applyItemId, fa).then(() => {
    //     this.form.items[index].fa = fa;
    //     this.$message({
    //       type: "success",
    //       message: "操作成功!",
    //     });
    //   });
    // },
    // handleLoanSelect(selectList) {
    //   const row = selectList[0];
    //   this.form.loanAmount = row.amount;
    //   this.form.loanType = row.type;
    // },
    // 获取任务详情
    getDetail(taskId, processInsId) {
      this.getTaskDetail(taskId, processInsId).then((res) => {
        const { process } = res;
        const { variables, status } = process;
        // console.log(process)
        if (status === 'todo') {
          this.processStatus = true;
        }

        const option = this.option;
        option.menuBtn = false;
        const { column, group } = option;
        if (status != "todo") {
          // 已办，删除字段默认值
          option.detail = true;
          if (column && column.length > 0) {
            // 处理column
            column.forEach((col) => {
              if (col.type == "dynamic")
                col.children.column.forEach((cc) => {
                  delete cc.value;
                });
              delete col.value;
            });
          }

          if (group && group.length > 0) {
            // 处理group
            group.forEach((gro) => {
              if (gro.column && gro.column.length > 0) {
                gro.column.forEach((col) => {
                  if (col.type == "dynamic")
                    col.children.column.forEach((cc) => {
                      delete cc.value;
                    });
                  delete col.value;
                });
              }
            });
          }
        } else {
          let vars = column.vars || [];
          column.forEach((col) => {
            if (col.value) col.value = this.getDefaultValues(col.value);
          });
          if (group && group.length > 0) {
            // 处理group
            group.forEach((gro) => {
              gro.column.forEach((col) => {
                if (col.value) col.value = this.getDefaultValues(col.value);
              });
              vars = vars.concat(group.vars);
            });
          }
          this.vars = vars;
        }
        for (let key in variables) {
          if (!variables[key]) delete variables[key];
        }
        if (
          option.column &&
          process.variables &&
          process.variables.serialNumber
        ) {
          option.column.unshift({
            label: "流水号",
            prop: "serialNumber",
            span: 24,
            detail: true,
          });
        }
        this.option = option;
        this.form = variables;

        //初始化
        this.form.inChargePerson = this.userInfo.user_id;
        this.form.chargeDept = this.userInfo.dept_id;
        this.form.processTime = null;
        this.form.solution = null;
        this.form.attach1 = [];

        this.waiting = false;
      });
    },
    // 审核
    handleExamine(pass) {
      this.submitLoading = true;
      this.$refs.form.validate((valid, done) => {
        if (valid) {
          const variables = {};

          this.vars.forEach((v) => {
            if (v != "comment" && this.form[v]) variables[v] = this.form[v];
          });

          variables["solution"] = this.form.solution;
          variables["processTime"] = this.form.processTime;
          variables["inChargePerson"] = this.form.inChargePerson;
          variables["chargeDept"] = this.form.chargeDept;
          variables["attach1"] = this.form.attach1;
          this.handleCompleteTask(pass, variables)
            .then(() => {
              this.$message.success("处理成功");
              this.handleCloseTag("/plugin/workflow/process/todo");
            })
            .catch(() => {
              done();
              this.submitLoading = false;
            });
        } else {
          done();
          this.submitLoading = false;
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.header {
  width: 100%;
  height: 50px;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 10px 10px 0;
}
.form-divider {
  border-top: 1px solid #e5e7eb;
  margin: 10px 0;
}
</style>

<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
      @row-click="rowClick"
      @cell-dblclick="cellDblClick"
      :cell-style="cellStyle"
    >
      <template #personName="{row,index}">
        <span>{{ row.personName }}
         <template v-if="row.userStatus===2">(离)</template>
        </span>
      </template>
      <template #remark="{ row, index }">
        <el-link
          slot="reference"
          :type="row.remark ? '' : 'info'"
          style="font-size: 12px"
          @click="rowChangeRemark(row)"
        >
          {{ row.remark ? row.remark : "暂无" }}
        </el-link>
      </template>
      <template #remarkHeader="{ column }">
        <span>{{ column.label }}</span>
        <i class="el-icon-edit-outline"></i>
      </template>
      <template #brand="{ row, size, index }">
        <el-tag
          v-if="row.brand"
          :size="size"
          :effect="row.brand === '1' ? 'dark ' : 'plain'"
        >
          {{ row.$brand }}
        </el-tag>
      </template>
      <template #num="{ row, index }">
        <span :style="{ color: colorName, fontWeight: 'bold' }">
          {{ Number(row.num).toLocaleString("ZH") }}
        </span>
      </template>
      <template #projectSerialNo="{ row, index }">
        <span v-if="row.projectId">{{ row.projectSerialNo }}</span>
        <span v-else>{{ row.budgetSerialNo }}</span>
      </template>
      <template #pv="{ row, index }">
        <el-tag size="mini" type="danger" effect="dark" v-if="row.pv">
          是
        </el-tag>
        <el-tag size="mini" type="info" effect="plain" v-else> 否</el-tag>
      </template>
      <template #menuLeft>
        <el-button
          type="primary"
          size="mini"
          icon="el-icon-truck"
          v-if="permission.depot_stock_out"
          @click="handleOut"
        >出 库
        </el-button>
        <el-button
          type="warning"
          size="mini"
          icon="el-icon-truck"
          v-if="permission.depot_location_change"
          @click="handleLocationChange"
        >调整库位
        </el-button>
        <el-button
          type="warning"
          size="mini"
          icon="el-icon-setting"
          v-if="permission.depot_remark_change"
          @click="handleRemark"
        >修改备注
        </el-button>
        <el-button
          type="success"
          size="mini"
          icon="el-icon-download"
          plain
          v-if="permission.depot_export"
          @click="handleExport"
        >导出
        </el-button>
        <el-dropdown
          v-if="permission.depot_tag_print"
          split-button
          icon="el-icon-printer"
          type="danger"
          size="mini"
          plain
          @click="handleTagPrint"
          @command="handleTagPrint"
        >
          标签打印
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="location">打印库位</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-divider direction="vertical"></el-divider>
        <el-checkbox v-model="pv">压力容器</el-checkbox>
        <el-checkbox v-model="yzdyzb" @change="handleYzdyzbChange">易制毒、易制爆化学品</el-checkbox>
        <el-checkbox v-model="remarkUnEmpty" @change="handleRemarkUnEmptyChange">备注非空</el-checkbox>
        <el-checkbox v-model="dimission" @change="handleDimissionChange">已离职</el-checkbox>
        <el-checkbox v-model="crash" @change="handleCrashChange">紧急申购</el-checkbox>
        <el-checkbox v-model="three" @change="handleThreeChange">三个月前</el-checkbox>
      </template>
      <template #menuRight>
        <el-radio-group v-model="status" size="mini" @input="onLoad(page)">
          <el-radio-button label="all">全部</el-radio-button>
          <el-radio-button label="1">未冻结</el-radio-button>
          <el-radio-button label="-1">已冻结</el-radio-button>
        </el-radio-group>
      </template>
      <template #menu="{ type, size, row }">
        <el-button
          icon="el-icon-menu"
          :size="size"
          :type="type"
          v-if="permission.depot_stock_detail"
          @click="rowDetail(row)"
        >详情
        </el-button>
        <el-button
          icon="el-icon-printer"
          type="text"
          :size="size"
          v-if="row.pv"
          @click="rowPrint(row)"
        >
          打印
        </el-button>
      </template>
    </avue-crud>
    <stock-detail ref="stockDetailRef"/>
    <stock-out-form ref="stockOutRef" @confirm="refreshChange"/>
    <el-dialog
      title="库位调整"
      append-to-body
      :visible.sync="locationChange.visible"
      width="355px"
    >
      <avue-form
        v-if="locationChange.visible"
        :option="locationChange.option"
        v-model="locationChange.form"
        @submit="handleLocationChangeSubmit"
      >
      </avue-form>
    </el-dialog>
    <el-dialog
      title="修改金额"
      append-to-body
      :visible.sync="amountChange.visible"
      width="355px"
    >
      <avue-form
        v-if="amountChange.visible"
        :option="amountChange.option"
        v-model="amountChange.form"
        @submit="handleAmountChangeSubmit"
      >
      </avue-form>
    </el-dialog>
    <el-dialog
      title="台账打印"
      append-to-body
      :visible.sync="print.visible"
      width="355px"
    >
      <avue-form
        v-if="print.visible"
        :option="print.option"
        v-model="print.form"
        @submit="handlePrintSubmit"
      >
      </avue-form>
    </el-dialog>
  </basic-container>
</template>

<script>
import {
  changeAmount,
  changeLocation,
  changePurpose,
  changeRemark,
  getAllList,
  getDetail,
  getList,
} from "@/api/ni/depot/currentStock";
import {mapGetters} from "vuex";
import {earliestListBySn} from '@/api/ni/depot/stockInItem'
import StockDetail from "@/views/ni/depot/stock-detail";
import {dateNow1} from "@/util/date";
import StockOutForm from "@/views/ni/depot/components/StockOutForm";
import {loadPrintTemplate} from "@/api/system/printTemplate";
import {hiprint} from "vue-plugin-hiprint";
import {getStockPvHistory} from "@/api/ni/depot/stock";

export default {
  components: {
    StockDetail,
    StockOutForm,
  },
  data() {
    const depotCode = "06";
    return {
      depotCode, //备件库code,方便管理，如果仓库以非06开始的，则无法在此显示
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        showSummary: true,
        sumColumnList: [
          {
            name: "num",
            type: "sum",
            decimals: 2,
          },
        ],
        stripe: true,
        highlightCurrentRow: true,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        menuWidth: 150,
        searchEnter: true,
        selection: true,
        searchLabelWidth: 110,
        rowKey: "id",
        size: "mini",
        searchSize: "mini",
        searchIndex: 3,
        searchIcon: true,
        height: "auto",
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        align: "center",
        dialogClickModal: false,
        column: [
          {
            label: "账套",
            prop: "brand",
            type: "radio",
            dicData: [],
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            search: true,
            width: 80,
          },
          {
            label: "仓库",
            prop: "depotId",
            type: "select",
            dicUrl: "/api/ni/base/depot/info/list?status=2&code=" + depotCode,
            props: {
              label: "name",
              value: "id",
              desc: "code",
            },
            placeholder: " ",
            search: true,
            hide: true,
            showColumn: false,
          },
          {
            label: "仓库",
            prop: "depotName",
            minWidth: 110,
          },
          {
            label: "库位",
            prop: "depotLocation",
            search: true,
            searchOrder: 99,
            span: 8,
            minWidth: 110,
            overHidden: true,
            placeholder: " ",
          },
          {
            label: "项目/预算",
            searchLabel: "项目/预算编号",
            prop: "projectSerialNo",
            minWidth: 110,
            search: true,
            overHidden: true,
            disabled: true,
            filterable: true,
            placeholder: " ",
            searchOrder: 94,
          },
          {
            label: "请购人",
            prop: "personId",
            type: "select",
            remote: true,
            dicUrl: `/api/blade-user/search/otj/user?name={{key}}`,
            props: {
              label: "realName",
              value: "id",
            },
            dicFormatter: (data) => {
              return data.data.records;
            },
            placeholder: " ",
            search: true,
            hide: true,
            showColumn: false,
            searchOrder: 95,
          },
          {
            label: "请购人",
            prop: "personName",
            minWidth: 90,
            overHidden: true,
          },

          {
            label: "品名",
            prop: "materialName",
            overHidden: true,
            width: 100,
            search: true,
            searchOrder: 98,
            placeholder: " ",
          },
          {
            label: "规格",
            searchLabel: "规格型号",
            prop: "specification",
            placeholder: " ",
            overHidden: true,
            disabled: true,
            width: 100,
            search: true,
            searchOrder: 97,
          },
          {
            label: "材质",
            prop: "quality",
            placeholder: " ",
            disabled: true,
            overHidden: true,
            width: 90,
            search: true,
            searchOrder: 96,
          },
          {
            label: "单位",
            prop: "unit",
            minWidth: 75,
            overHidden: true,
            type: "select",
            dicData: [],
            placeholder: " ",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
          },
          {
            label: "数量",
            prop: "num",
            type: "input",
            minWidth: 75,
            overHidden: true,
          },
          {
            label: "用途",
            prop: "purpose",
            display: false,
            search: true,
            minWidth: 100,
            overHidden: true,
          },
          {
            label: "备注",
            prop: "remark",
            type: "input",
            minWidth: 110,
            overHidden: true,
            search: true,
          },
          {
            label: "入库日期",
            prop: "inDate",
            minWidth: 110,
          },
          {
            label: "上次盘点日期",
            prop: "checkDate",
            type: "date",
            format: "yyyy-MM-dd",
            minWidth: 100,
            overHidden: true,
          },
          {
            label: "盘点编号",
            prop: "checkSerialNo",
            minWidth: 110,
            overHidden: true,
          },
          {
            label: "编码",
            prop: "materialCode",
            overHidden: true,
            width: 110,
            search: true,
          },
          {
            label: "炉号",
            prop: "heatNo",
            width: 100,
            overHidden: true,
          },
          {
            label: "材料批号",
            prop: "sn",
            width: 100,
            overHidden: true,
            search: true,
          },
          {
            label: "材料编号",
            prop: "materialNo",
            width: 100,
            overHidden: true,
          },
          {
            label: "材料牌号",
            prop: "materialBrand",
            width: 100,
            overHidden: true,
          },
          {
            label: "执行标准",
            prop: "executionStandards",
            width: 100,
            overHidden: true,
          },
          {
            label: "出厂日期",
            prop: "pd",
            type: "date",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            width: 100,
            overHidden: true,
          },
          {
            label: "制造商",
            prop: "manufacturer",
            overHidden: true,
            width: 80,
          },
          {
            label: "压力容器",
            prop: "pv",
            type: "input",
            width: 70,
          },
          {
            label: "单价",
            prop: "price",
            type: "number",
            controls: false,
            precision: 2,
            placeholder: " ",
          },
          {
            label: "金额",
            prop: "amount",
            type: "number",
            controls: false,
            precision: 2,
            placeholder: " ",
          },
        ],
      },
      data: [],
      status: "all",
      pv: false,
      yzdyzb: false,
      unZero: true,
      remarkUnEmpty: false,
      dimission: false,
      crash: false,
      three: false,
      brandDict: [],
      brandDictKeyValue: {},
      unitDictKeyValue: [],
      locationChange: {
        visible: false,
        option: {
          size: "mini",
          span: 24,
          emptyBtn: false,
          column: [
            {
              label: "库位",
              prop: "depotLocation",
              remote: true,
              placeholder: " ",
            },
            {
              label: "数量",
              prop: "num",
              display: false,
              remote: true,
              placeholder: " ",
            },
            {
              label: "备注",
              prop: "remark",
              type: "textarea",
              remote: true,
              placeholder: " ",
            },
          ],
        },
        form: {},
      },
      amountChange: {
        visible: false,
        option: {
          size: "mini",
          span: 24,
          emptyBtn: false,
          enter: true,
          column: [
            {
              label: "修改金额",
              prop: "amount",
              remote: true,
              placeholder: " ",
            },
          ],
        },
        form: {},
      },
      print: {
        visible: false,
        option: {
          size: "mini",
          span: 24,
          emptyBtn: false,
          submitText: "打印",
          column: [
            {
              label: "选择台账",
              prop: "type",
              type: "select",
              dicData: [
                {
                  label: "板材材料台账",
                  value: "bc1",
                },
                {
                  label: "法兰台账",
                  value: "fl1",
                },
                {
                  label: "管材材料台账",
                  value: "gc1",
                },
                {
                  label: "板材入库单",
                  value: "bc",
                },
                {
                  label: "不锈钢管材入库单",
                  value: "bxg",
                },
                {
                  label: "锻件法兰入库单",
                  value: "fl",
                },
                {
                  label: "碳钢管材入库单",
                  value: "tg",
                },
              ],
              placeholder: " ",
              rules: [
                {
                  required: true,
                  message: "请选择台账",
                  trigger: "blur",
                },
              ],
            },
          ],
        },
        form: {},
        template: {},
      },
      export: {
        column: [
          {
            label: "账套",
            prop: "brand",
            width: 70,
          },
          {
            label: "仓库名称",
            prop: "depotName",
            width: 100,
          },
          {
            label: "库位",
            prop: "depotLocation",
            width: 100,
          },
          {
            label: "项目/预算",
            prop: "projectSerialNo",
          },
          {
            label: "请购人",
            prop: "personName",
          },
          {
            label: "品名",
            prop: "materialName",
            width: 100,
          },
          {
            label: "编码",
            prop: "materialCode",
          },
          {
            label: "规格型号",
            prop: "specification",
            width: 130,
          },
          {
            label: "材质",
            prop: "quality",
            width: 130,
          },
          {
            label: "单位",
            prop: "unit",
            width: 130,
          },
          {
            label: "备注",
            prop: "remark",
            width: 130,
          },
          {
            label: "库存数量",
            prop: "num",
          },
          {
            label: "炉号",
            prop: "heatNo",
            width: 100,
            overHidden: true,
          },
          {
            label: "材料批号",
            prop: "sn",
            width: 100,
            overHidden: true,
          },
          {
            label: "材料编号",
            prop: "materialNo",
            width: 100,
            overHidden: true,
          },
          {
            label: "材料牌号",
            prop: "materialBrand",
            width: 100,
            overHidden: true,
          },
          {
            label: "执行标准",
            prop: "executionStandards",
            width: 100,
            overHidden: true,
          },
          {
            label: "出厂日期",
            prop: "pd",
            type: "date",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            width: 100,
            overHidden: true,
          },
          {
            label: "制造商",
            prop: "manufacturer",
            overHidden: true,
            width: 80,
          },
          {
            label: "压力容器",
            prop: "pv",
            type: "input",
            width: 70,
          },
          {
            label: "单价",
            prop: "price",
            type: "number",
            controls: false,
            precision: 2,
            placeholder: " ",
          },
          {
            label: "金额",
            prop: "amount",
            type: "number",
            controls: false,
            precision: 2,
            placeholder: " ",
          },
          {
            label: "用途",
            prop: "purpose",
            placeholder: " ",
          },
        ],
      },
      tagPrintTemplate: null,
      tagLocationPrintTemplate: null,
    };
  },
  computed: {
    ...mapGetters(["permission", "userInfo", "colorName"]),
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  watch: {
    pv: {
      handler(val) {
        this.query.pv = val ? 1 : null;
        this.page.currentPage = 1;
        this.onLoad(this.page);
      },
      immediate: false,
    },
    unZero: {
      handler(val) {
        this.query.unZero = val ? true : null;
        this.page.currentPage = 1;
        this.onLoad(this.page);
      },
      immediate: false,
    },
  },
  created() {
    this.dictInit();
    const printType = this.findObject(this.print.option.column, "type");
    const dictData = printType.dicData;
    dictData.forEach((item) => {
      loadPrintTemplate(`ni_depot_current-stock-pv-${item.value}`).then(
        (res) => {
          this.print.template[item.value] = JSON.parse(res.data.data.content);
        }
      );
    });
    loadPrintTemplate("depot_tag").then((res) => {
      this.tagPrintTemplate = JSON.parse(res.data.data.content);
    });
    loadPrintTemplate("depot_location_tag").then((res) => {
      this.tagLocationPrintTemplate = JSON.parse(res.data.data.content);
    });
  },
  methods: {
    handleThreeChange(val) {
      this.three = val
      this.page.currentPage = 1;
      this.onLoad(this.page);
    },
    handleCrashChange(val) {
      if (val) {
        this.query.crash = true;
      } else {
        this.query.crash = null
      }
      this.page.currentPage = 1;
      this.onLoad(this.page);
    },
    handleDimissionChange(val) {
      if (val) {
        this.query.userStatus = 2;
      } else {
        this.query.userStatus = null
      }
      this.page.currentPage = 1;
      this.onLoad(this.page, this.query);
    },
    handleRemarkUnEmptyChange(val) {
      if (val) {
        this.query.remarkUnEmpty = 1;
      } else {
        this.query.remarkUnEmpty = null
      }
      this.page.currentPage = 1;
      this.onLoad(this.page);
    },
    handleYzdyzbChange(val) {
      if (val)
        this.query.yzdyzb = 1;
      else this.query.yzdyzb = null;
      this.page.currentPage = 1;
      this.onLoad(this.page);
    },
    handleLocationChange() {
      if (this.selectionList.length <= 0) {
        this.$message.warning("请至少选择一条数据");
        return;
      }
      const status = this.selectionList.some((item) => item.status < 0);
      if (status) {
        this.$message.warning("冻结的数据无法操作，请重新选择");
        return;
      }
      this.locationChange.form = {
        ids: this.ids,
      };
      const numC = this.findObject(this.locationChange.option.column, "num");
      if (this.selectionList.length === 1) {
        this.locationChange.form.depotLocation =
          this.selectionList[0].depotLocation;
        this.locationChange.form.remark = this.selectionList[0].remark;
        this.locationChange.form.num = this.selectionList[0].num;
        numC.display = true
      }else {
        numC.display = false
      }
      this.locationChange.visible = true;
    },
    handleAmountChangeSubmit(form, done) {
      changeAmount(form)
        .then((res) => {
          this.amountChange.visible = false;
          this.$message({
            type: res.data.code === 200 ? "success" : "warning",
            message: res.data.msg,
          });
          this.amountChange.form = {};
          this.onLoad(this.page);
        })
        .finally(() => {
          done();
        });
    },
    handleLocationChangeSubmit(form, done) {
      changeLocation(this.locationChange.form)
        .then((res) => {
          this.locationChange.visible = false;
          this.$message({
            type: res.data.code === 200 ? "success" : "warning",
            message: res.data.msg,
          });
          this.locationChange.form = {};
          this.onLoad(this.page);
        })
        .finally(() => {
          done();
        });
    },
    async handlePrintSubmit(form, done) {
      if (!this.print.template[form.type]) {
        this.$message.error("打印模板加载失败，请联系管理员");
        return;
      }
      let printData = {
        ...form,
        factoryDate: form.pd,
        materialBatchNo: form.sn,
      };
      if (form.type === "bc1") {
        let items = await getStockPvHistory(1, 100, {
          porArrivalItemId: form.arrivalItemId,
        });
        let dataItems = items.data.data.records;
        // 过滤出type等于out的数据
        let filteredDataItems = dataItems.filter((item) => item.type === "out");
        //当前库存
        let stockNum = form.num;
        let result = filteredDataItems.map((item, index) => {
          return {
            opTime: item.opTime,
            // depotNum:
            num: item.num >= 0 ? item.num : null,
            returnNum: item.num < 0 ? -item.num : null,
            materialName: item.materialName,
            materialNo: form.materialNo,
            personName: item.personName,
            remark: item.remark,
            stockNum: index === 0 ? stockNum : (stockNum -= item.num),
          };
        });
        printData.items = result;
      } else if (form.type === "fl1") {
        let items = await getStockPvHistory(1, 100, {
          porArrivalItemId: form.arrivalItemId,
        });
        let dataItems = items.data.data.records;
        let result = dataItems.map((item) => {
          return {
            outOpTime: item.type === "out" ? item.opTime : null,
            inOpTime: item.type === "in" ? item.opTime : null,
            materialName: item.materialName,
            executionStandards: form.executionStandards,
            specification: item.specification,
            quality: item.quality,
            unit: this.unitDictKeyValue[item.unit],
            remark: item.remark,
            outNum: item.type === "out" ? item.num : null,
            inNum: item.type === "in" ? item.num : null,
          };
        });
        printData.items = result;
      } else if (form.type === "gc1") {
        let items = await getStockPvHistory(1, 100, {
          porArrivalItemId: form.arrivalItemId,
        });
        let dataItems = items.data.data.records;
        let result = dataItems.map((item) => {
          return {
            outOpTime: item.type == "out" ? item.opTime : null,
            inOpTime: item.type == "in" ? item.opTime : null,
            outNum: item.type == "out" ? item.num : null,
            inNum: item.type == "in" ? item.num : null,
            specification: item.specification,
            heatNo: form.heatNo,
            materialBatchNo: printData.materialBatchNo,
            materialNo: printData.materialNo,
            serialNo: this.selectionList[0].serialNo,
          };
        });
        printData.items = result;
      }
      const hiprintTemplate = new hiprint.PrintTemplate({
        template: this.print.template[form.type],
      });
      hiprintTemplate.print(printData);
      done();
      this.print.visible = false;
    },
    handleOutNextStep() {
      const items = [];
      const form = {
        keeperId: this.userInfo.user_id,
        opTime: dateNow1(),
        subType: "202",
        pv: 0,
        items,
      };
      const depotIds = new Set();
      const projectIds = new Set();
      const budgetIds = new Set();
      const brands = new Set();
      const pv = new Set();
      this.selectionList.forEach((item) => {
        depotIds.add(item.depotId);
        projectIds.add(item.projectId);
        budgetIds.add(item.budgetId);
        brands.add(item.brand);
        if (item.pv) pv.add(item.pv);
      });
      if (brands.size > 1) {
        this.$message.warning("请选择同一账套的数据");
        return;
      }
      if (depotIds.size > 1) {
        this.$message.warning("请选择同一仓库的数据");
        return;
      }
      if (pv.size === this.selectionList.length) {
        form.pv = 1;
      } else if (pv.size === 0) {
        form.pv = 0;
      } else {
        this.$message.warning("压力容器请分别出库");
        return;
      }
      form.depotId = this.selectionList[0].depotId;
      form.brand = this.selectionList[0].brand;
      if (this.selectionList.length === 1 || projectIds.size === 1 || budgetIds.size === 1) {
        form.pbId = this.selectionList[0].projectId
          ? this.selectionList[0].projectId
          : this.selectionList[0].budgetId;
        form.projectId = this.selectionList[0].projectId;
        form.budgetId = this.selectionList[0].budgetId;
      }
      this.selectionList.forEach((item) => {
        items.push({
          brand: item.brand,
          depotId: item.depotId,
          depotLocation: item.depotLocation,
          materialCode: item.materialCode,
          materialName: item.materialName,
          materialId: item.materialId,
          specification: item.specification,
          gb: item.gb,
          unit: item.unit,
          bp: item.bp,
          stockNum: item.num,
          stockAmount: item.amount,
          personId: item.personId,
          personName: item.personName,
          projectId: item.projectId,
          budgetId: item.budgetId,
          budgetSerialNo: item.budgetSerialNo,
          projectSerialNo: item.projectSerialNo,
          auditorId: item.personId,
          outProjectAuditUser: item.personId,
          pv: item.pv ? item.pv : 0,
          yzdyzb: item.yzdyzb ? item.yzdyzb : 0,
          pvInfo: {
            heatNo: item.heatNo,
            materialBatchNo: item.sn,
            materialNo: item.materialNo,
            materialBrand: item.materialBrand,
            executionStandards: item.executionStandards,
            factoryDate: item.pd,
          },
          porOrderArrivalId: item.arrivalItemId,
          sn: item.sn ? item.sn : "1",
          attaches: item.attaches,
        });
      });
      this.$refs.stockOutRef.init(form);
    },
    handleOut() {
      if (this.selectionList.length > 0) {
        const status = this.selectionList.some((item) => item.status < 0);
        if (status) {
          this.$confirm("选中的数据有正在盘点的数据, 是否继续?", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }).then(() => {
            this.handleOutNextStep();
          });
        } else this.handleOutNextStep();
      }
    },
    handleTagPrint(command) {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      const msg = "是否打印选中数据的标签";
      this.$confirm(msg, {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        if (command === "location") {
          this.handleLocationTagPrint();
        } else {
          this.handleMaterialTagPrint();
        }
      });
    },
    async handleMaterialTagPrint() {
      if (!this.tagPrintTemplate) {
        this.$message.error("打印模板加载失败，请联系管理员");
        return;
      }
      const printData = [];
      this.selectionList.forEach((data) => {
        printData.push({
          id: "stock:" + data.id,
          depotLocation: data.depotLocation,
          materialCode:
            data.materialCode + (data.sn === "1" ? "" : "/" + data.sn),
          material:
            data.materialName + "/" + data.specification + "/" + data.quality,
          project:
            (data.projectSerialNo
              ? data.projectSerialNo
              : data.budgetSerialNo) +
            "/" +
            data.personName,
          sn: data.serialNo,
        });
      });
      let hiprintTemplate = new hiprint.PrintTemplate({
        template: this.tagPrintTemplate,
      });
      hiprintTemplate.print(printData);
    },
    handleLocationTagPrint() {
      if (!this.tagLocationPrintTemplate) {
        this.$message.error("打印模板加载失败，请联系管理员");
        return;
      }
      const printData = new Set();
      this.selectionList.forEach((data) => {
        if (data.depotLocation)
          printData.add({
            depotName: data.depotName,
            depotLocation: data.depotLocation,
            qrCode: "location:" + data.depotLocation,
          });
      });
      let hiprintTemplate = new hiprint.PrintTemplate({
        template: this.tagLocationPrintTemplate,
      });
      hiprintTemplate.print(Array.from(printData));
    },
    handleExport() {
      this.$confirm("是否导出当前筛选的所有数据？", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const q = {
          ...this.query,
          depotCode: this.depotCode,
          unZero: this.unZero ? true : null,
          status: this.status === "all" ? null : this.status,
          pv: this.pv ? 1 : null,
        };
        const res = await getAllList(q);
        this.$Export.excel({
          title: "当前库存",
          columns: this.export.column,
          data: res.data.data.map((item) => {
            return {
              ...item,
              projectSerialNo: item.projectSerialNo
                ? item.projectSerialNo
                : item.budgetSerialNo,
              brand: this.brandDictKeyValue[item.brand],
              unit: this.unitDictKeyValue[item.unit],
              pv: item.pv === 1 ? "是" : "否",
            };
          }),
        });
      });
    },
    handleRemark() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      let inputValue = "";
      if (this.selectionList.length === 1) {
        inputValue = this.selectionList[0].remark;
      }
      this.$prompt("", "请输入备注", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        inputType: "textarea",
        inputValue,
        inputPlaceholder: " ",
      })
        .then(({value}) => {
          return changeRemark(this.ids, value);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    rowPrint(row) {
      this.print.form = {
        ...row,
      };
      this.print.visible = true;
    },
    rowDetail(row) {
      this.materialId = row.materialId;
      this.$refs.stockDetailRef.init(row, this.query);
    },
    dictInit() {
      this.$http
        .get("/api/blade-system/dict-biz/dictionary?code=ni_brand")
        .then((res) => {
          const column = this.findObject(this.option.column, "brand");
          column.dicData = res.data.data;
          this.brandDict = res.data.data;
          this.brandDictKeyValue = this.brandDict.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });
      this.$http
        .get("/api/blade-system/dict-biz/dictionary?code=ni_material_unit")
        .then((res) => {
          const column = this.findObject(this.option.column, "unit");
          column.dicData = res.data.data;
          this.unitDictKeyValue = res.data.data.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });
    },
    beforeOpen(done, type) {
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then((res) => {
          this.form = res.data.data;
        });
      }
      done();
    },
    searchReset() {
      this.query = {
        unZero: true
      };
      this.remarkUnEmpty = false;
      this.pv = false;
      this.yzdyzb = false;
      this.dimission = false;
      this.unZero = true;
      this.crash = false
      this.status = "all";
      this.page.currentPage = 1;
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.query.unZero = this.unZero ? true : null;
      this.query.remarkUnEmpty = this.remarkUnEmpty ? true : null;
      this.query.pv = this.pv ? 1 : null;
      this.query.yzdyzb = this.yzdyzb ? 1 : null;
      this.query.userStatus = this.dimission ? 2 : null;
      this.query.crash = this.crash ? true : null;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.query.unZero = this.unZero ? true : null;
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      const q = {
        ...params,
        ...this.query,
      };
      q.depotCode = this.depotCode;
      q.status = this.status === "all" ? null : this.status;
      q.three = this.three
      getList(page.currentPage, page.pageSize, q).then((res) => {
        const data = res.data.data;
        data.records.forEach((item) => {
          item.inDate = '加载中...'
        })
        this.page.total = data.total;
        this.data = data.records;
        const rows = Array.from(new Set(this.data.filter((item) => item.sn > 1).map((item) => item.sn)));
        this.loadInDate(rows)
        this.loading = false;
        this.selectionClear();
      });
    },
    //根据批号查询入库时间
    async loadInDate(rows) {
      let map = {}
      if (rows.length > 0) {
        const res = await earliestListBySn({sn: rows.join(',')})
        const data = res.data.data
        map = data.reduce((acc, cur) => {
          acc[cur.sn] = cur.opTime ? cur.opTime.slice(0, 10) : '';
          return acc
        }, {})
      }
      this.data.forEach((item) => {
        if (item.sn === '1') {
          item.inDate = '2015年前'
        } else
          item.inDate = map[item.sn] || '-'
      })
    },
    rowClick(row) {
      this.$refs.crud.toggleSelection([row]);
    },
    cellDblClick(row, column) {
      if (column.property === "amount" && this.userInfo.role_name.includes('admin')) {
        this.amountChange.visible = true;
        this.amountChange.form.ids = row.id;
        this.amountChange.form.amount = row.amount;
      }
    },
    cellStyle({row, column}) {
      if (
        ["depotId", "depotLocation"].includes(column.columnKey) &&
        row.status < 0
      ) {
        return {
          backgroundColor: "#409EFF",
          color: "#fff",
        };
      }
      if ("materialName" === column.columnKey && row.pv) {
        return {
          backgroundColor: "#F56C6C",
          color: "#fff",
        };
      }
      if ("materialName" === column.columnKey && row.yzdyzb == 1) {
        return {
          background: '#722ed1 url(/img/yzdyzb.png) no-repeat left center / contain',
          color: "#fff",
        };
      }
    },
    rowChangeRemark(row) {
      const msg = `[${row.depotName}${
        row.depotLocation ? "-" + row.depotLocation : ""
      }]${row.materialName}/${row.materialCode}/${row.specification}-${
        row.num
      }(${row.$unit})`;
      this.$prompt("请输入备注", msg, {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        inputType: "textarea",
        inputValue: row.remark,
        inputPlaceholder: " ",
      })
        .then(({value}) => {
          return changeRemark(row.id, value);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    rowChangePurpose(row) {
      const msg = `[${row.depotName}${
        row.depotLocation ? "-" + row.depotLocation : ""
      }]${row.materialName}/${row.materialCode}/${row.specification}-${
        row.num
      }(${row.$unit})`;
      this.$prompt("请输入用途", msg, {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        inputType: "textarea",
        inputValue: row.purpose,
        inputPlaceholder: " ",
      })
        .then(({value}) => {
          return changePurpose(row.id, value);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
  },
};
</script>

<style></style>

```java
// 定义包路径，属于问题反馈模块的数据DTO传输对象包
package com.natergy.ni.feedback.dto;

// 导入问题反馈的实体类（作为父类）
import com.natergy.ni.feedback.entity.FeedbackEntity;
// 导入Lombok注解：@Data生成常用方法，@EqualsAndHashCode用于用于处理equals和hashCode
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 问题反馈 数据传输传输对象实体类
 *
 * <AUTHOR>
 * @since 2025-02-26
 */
// @Data：Lombok注解，自动生成该类所有字段的getter、setter、toString、equals、hashCode等方法
@Data
// @EqualsAndHashCode(callSuper = true)：Lombok注解，生成equals和hashCode方法时包含父类的字段
// callSuper = true表示在计算equals和hashCode时会调用父类的相应方法，确保父类字段也参与比较
@EqualsAndHashCode(callSuper = true)
// 继承FeedbackEntity实体类，继承其所有字段和方法
public class FeedbackDTO extends FeedbackEntity {
    // 序列化版本号，用于对象序列化时的版本控制
    private static final long serialVersionUID = 1L;

}
```

### 类功能说明

该类是问题反馈模块的数据**数据传输对象（DTO）**，主要作用是：

1. **继承实体类**：通过继承`FeedbackEntity`，获得其所有字段和方法，无需重复定义与实体类相同的属性。
2. **扩展灵活性**：作为 DTO，可在不修改原实体类的情况下，根据传输需求添加额外字段或方法（当前代码未添加新字段，为基础扩展扩展预留）。
3. **适配分层架构**：在控制器与服务层、服务层与外部接口之间传输数据时使用，避免直接暴露实体类，便于维护数据传输规则。

通过 Lombok 的`@Data`和`@EqualsAndHashCode`注解，简化了 JavaBean 的样板代码，同时保证了 equals 和 hashCode 方法能正确处理父类字段，确保对象比较的准确性。
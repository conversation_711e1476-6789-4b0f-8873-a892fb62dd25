import {getDeptLazyTree} from "@/api/system/dept";
import {startProcess} from "@/api/plugin/workflow/process";

export const lazyTreeOption = {
  size: 'mini',
  searchSize: 'mini',
  nodeKey: 'id',
  lazy: true,
  treeLoad: function (node, resolve) {
    const parentId = (node.level === 0) ? 0 : node.data.id;
    getDeptLazyTree(parentId).then(res => {
      resolve(res.data.data.map(item => {
        return {
          ...item,
          leaf: !item.hasChildren
        }
      }))
    });
  },
  addBtn: false,
  menu: false,
  props: {
    labelText: '标题',
    label: 'title',
    value: 'value',
    children: 'children'
  }
}

export const statusTagType = (status) => {
  if (status === null) {
    return ''
  }
  if (status === 0) {
    //草稿
    return 'info'
  } else if ([1, 2].includes(status)) {
    //已提交,审核中
    return ''
  } else if (status === 3) {
    //被驳回
    return 'danger'
  } else if (status === 4) {
    //已撤销
    return 'warning'
  } else if (status === 5) {
    //已挂起
    return 'warning'
  } else if (status === 6) {
    //已终止
    return 'danger'
  } else if (status === 9) {
    //已审核
    return 'success'
  }
}

export const tagType = (status) => {
  if (status === null) {
    return ''
  }
  if (status === 0) return 'info'
  else if (status === 1) return 'success'
  else if (status === 3)  return 'primary'
  else if (status === 4) return 'warning'
  else if (status === 5)  return 'danger'
  else return ''
}

export const rowStart = (row, processDefKey, obj) => {
  const _this = obj;
  _this.$confirm("此操作将提交该数据，是否继续?", '提示', {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
    center: true
  }).then(() => {
    _this.getStartFormByProcessDefKey(processDefKey).then(res => {
      let {process} = res;
      let form = {id: row.id, status: 1, processId: process.id};
      startProcess(form).then(() => {
        _this.onLoad(_this.page);
        _this.$message({
          type: "success",
          message: "操作成功!"
        });
      });
    });
  })
}

export const getStartFormByProcessDefIdInPaCommon = (processDefId, obj) => {
  obj.getStartForm(processDefId).then(res => {
    const _this = obj
    let {process} = res
    _this.form.processId = process.id
    const option = _this.option
    const {column, group} = option
    column.forEach(col => {
      if (col.value && toString.call(col.value) === '[object String]') col.value = _this.getDefaultValues(col.value)
    })
    if (group && group.length > 0) { // 处理group
      group.forEach(gro => {
        gro.column.forEach(col => {
          if (col.value && toString.call(col.value) === '[object String]') col.value = _this.getDefaultValues(col.value)
        })
      })
    }
    // const groupArr = []
    // const columnArr = this.filterAvueColumn(column, startForm, true).column
    // if (group && group.length > 0) { // 处理group
    //   group.forEach(gro => {
    //     gro.column = this.filterAvueColumn(gro.column, startForm, true).column
    //     if (gro.column.length > 0) groupArr.push(gro)
    //   })
    // }
    //
    // option.column = columnArr
    // option.group = groupArr
    // this.option = option
    if (_this.permission.wf_process_draft) {
      // 查询是否有草稿箱
      _this.initDraft(process.id).then(data => {
        _this.$confirm('是否恢复之前保存的草稿？', '提示', {}).then(() => {
          _this.form = JSON.parse(data)
        }).catch(() => {
        })
      })
    }
    _this.waiting = false
  })
}

export const getStartFormByProcessDefKeyInPaCommon = (processDefKey, obj) => {
  obj.getStartFormByProcessDefKey(processDefKey).then(res => {
    const _this = obj
    let {process} = res
    _this.form.processId = process.id
    const option = _this.option
    const {column, group} = option
    column.forEach(col => {
      if (col.value && toString.call(col.value) === '[object String]') col.value = _this.getDefaultValues(col.value)
    })
    if (group && group.length > 0) { // 处理group
      group.forEach(gro => {
        gro.column.forEach(col => {
          if (col.value && toString.call(col.value) === '[object String]') col.value = _this.getDefaultValues(col.value)
        })
      })
    }
    if (_this.permission.wf_process_draft) {
      // 查询是否有草稿箱
      _this.initDraft(process.id).then(data => {
        _this.$confirm('是否恢复之前保存的草稿？', '提示', {}).then(() => {
          _this.form = JSON.parse(data)
        }).catch(() => {
        })
      })
    }
    _this.waiting = false
  })
}

export const getMaxDayOfMonth = (date) => {
  if (date && toString.call(date) === '[object Date]') {
    let month =  date.getMonth() + 1;
    date.setMonth(month);
    date.setDate(0);
    // console.log('getMaxDayOfMonth', date.getDate());
    return date.getDate();
  }
}

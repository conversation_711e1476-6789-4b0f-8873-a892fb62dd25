<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
      :cell-style="cellStyle"
    >
      <template #menuLeft>
        <el-dropdown v-if="permissionList.addBtn" @command="handleAdd">
          <el-button type="primary" size="mini" icon="el-icon-plus"
          >新 增<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
              v-for="item in brandDict"
              :key="item.dictKey"
              :command="item.dictKey"
            >{{ item.dictValue }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-button
          type="danger"
          size="mini"
          icon="el-icon-delete"
          plain
          v-if="permission.niFinPaySoa_delete"
          @click="handleDelete"
        >删 除
        </el-button>
        <el-button
          type="warning"
          icon="el-icon-s-check"
          size="mini"
          plain
          v-if="permissionList.confirmBtn"
          @click="handleConfirm"
        >审 核
        </el-button>
      </template>
      <template #menu="{ row, index, size }">
        <el-button
          type="text"
          icon="el-icon-edit"
          :size="size"
          v-if="permissionList.editBtn && !row.confirm"
          @click="$refs.crud.rowEdit(row, index)"
        >编 辑
        </el-button>
        <el-button
          type="text"
          icon="el-icon-delete"
          :size="size"
          v-if="permissionList.delBtn && !row.confirm"
          @click="$refs.crud.rowDel(row, index)"
        >删 除
        </el-button>
        <el-button
          type="text"
          icon="el-icon-s-check"
          :size="size"
          v-if="permissionList.confirmBtn && !row.confirm"
          @click="rowConfirm(row)"
        >审 核
        </el-button>
        <el-button
          type="text"
          icon="el-icon-s-check"
          size="mini"
          plain
          v-if="permissionList.confirmBtn && row.confirm"
          @click="rowConfirmBack(row)"
        >弃审
        </el-button>
        <el-button
          type="text"
          icon="el-icon-s-grid"
          :size="size"
          @click="rowItems(row)"
        >明细
        </el-button>
        <el-button
          type="text"
          icon="el-icon-s-check"
          :size="size"
          v-if="
            row.confirm &&
            permission.niFinPaySoa_payable_apply &&
            Number(row.amount) -
              Number(row.advanceAmount) -
              Number(row.payApplyAmount) >
              0 &&
            (!row.payApplyState || [0, 1].includes(row.payApplyState))
          "
          @click="rowPayableApply(row)"
        >付款申请
        </el-button>
      </template>
      <template #billSerialNo="{ row, index }">
        <template v-if="row.billSerialNo">
          <div
            v-for="(item, index) in row.billSerialNo.split(',')"
            :key="index"
          >
            {{ item }}
          </div>
        </template>
      </template>
      <template #supplierIdForm="{ disabled, size, index }">
        <supplier-select
          v-model="form.supplierId"
          :size="size"
          :params="{ status: 2 }"
          :disabled="disabled"
        />
      </template>
      <template #date="{ row, index }">
        <span style="color: #e6a23c">{{ row.startDate }}</span>
        至
        <span style="color: #f56c6c">{{ row.endDate }}</span>
      </template>
      <template #itemsLabel>
        <el-tabs v-model="itemsTab">
          <el-tab-pane
            v-if="form.type !== '2'"
            :label="
              form.items && form.items.length > 0
                ? `订单明细（${form.items.length}）`
                : '订单明细'
            "
            name="items"
          ></el-tab-pane>
          <el-tab-pane
            v-if="form.type === '2'"
            :label="
              form.items && form.items.length > 0
                ? `支付宝明细（${form.items.length}）`
                : '支付宝明细'
            "
            name="alipays"
          ></el-tab-pane>
        </el-tabs>
      </template>
      <template #itemsForm="{ disabled, size }">
        <pay-soa-item
          v-if="itemsTab === 'items'"
          v-model="form.items"
          :disabled="disabled"
          :before-open="beforeItemAddOpen"
          @change="sumAmount"
        />
        <pay-soa-alipay
          v-else-if="itemsTab === 'alipays'"
          v-model="form.items"
          :disabled="disabled"
          @change="sumAmount"
        />
      </template>
      <template #backItemsLabel>
        <el-tabs v-model="backItemsTab">
          <el-tab-pane
            :label="
              form.backItems && form.backItems.length > 0
                ? `退换明细（${form.backItems.length}）`
                : '退换明细'
            "
            name="backItems"
          ></el-tab-pane>
        </el-tabs>
      </template>
      <template #backItemsForm="{ disabled, size }">
        <pay-soa-back-item
          v-model="form.backItems"
          :disabled="disabled"
          :before-open="beforeItemAddOpen"
          @change="sumAmount"
        />
      </template>
      <template #confirm="{ row, index }">
        <el-tag v-if="row.confirm" size="mini" type="danger" effect="dark">
          {{ row.$confirm }}
        </el-tag>
        <el-tag v-else size="mini" type="warning" effect="plain">
          {{ row.$confirm }}
        </el-tag>
      </template>
      <template #billState="{ row, index }">
        <el-tag
          v-if="
            row.invoiceAmount &&
            row.invoiceAmount < row.amount &&
            row.invoiceAmount > 0
          "
          size="mini"
          type="warning"
        >
          部分开票
        </el-tag>
        <el-tag
          v-else-if="row.invoiceAmount && row.invoiceAmount >= row.amount"
          size="mini"
          effect="dark"
        >
          已开票
        </el-tag>
        <el-tag v-else size="mini" type="info" effect="plain"> 未开票</el-tag>
      </template>
      <template #payApplyState="{ row, index, size }">
        <el-tag
          type="info"
          effect="plain"
          size="mini"
          v-if="!row.payApplyState || row.payApplyState === 0"
        >
          未申请
        </el-tag>
        <el-tag
          type="warning"
          effect="plain"
          size="mini"
          v-else-if="row.payApplyState === 1"
        >
          {{ row.$payApplyState }}
        </el-tag>
        <el-tag effect="dark" size="mini" v-else-if="row.payApplyState === 2">
          {{ row.$payApplyState }}
        </el-tag>
      </template>
      <template #brand="{ row, disabled, size, index }">
        <el-tag
          v-if="row.brand && row.brand === '1'"
          :size="size"
          effect="dark"
        >
          {{ row.$brand }}
        </el-tag>
        <el-tag v-else :size="size" effect="plain">
          {{ row.$brand }}
        </el-tag>
      </template>
      <template #type="{ row, index }">
        <el-tag v-if="row.type === '1'" size="mini" type="danger" effect="plain"
        >{{ row.$type }}
        </el-tag>
        <el-tag v-else size="mini" effect="plain">{{ row.$type }}</el-tag>
      </template>
      <template #advanceAmount="{ row, index }">
        <span v-if="row.advanceAmount">{{ row.advanceAmount }}</span>
        <span v-else>0.00</span>
      </template>
    </avue-crud>
    <pay-bill-form-dialog ref="payBillFormDialogRef"/>
    <invoice-link-dialog
      type="soa"
      ref="paySoaInvoiceDialogRef"
      :title="invoiceSelectTitle"
      :soa-id="soaId"
      :supplier-id="supplierId"
    />
    <pay-soa-item-drawer ref="itemRef"/>
  </basic-container>
</template>

<script>
import {
  add,
  confirm,
  confirmBack,
  getDetail as getPaySoaDetail,
  getDetail,
  getList,
  remove,
  update,
} from "@/api/ni/fin/pay-soa";
import {getList as getPayableApplyList} from '@/api/ni/fin/payable-apply'
import {mapGetters} from "vuex";
import SupplierSelect from "@/views/ni/base/components/SupplierSelect1";
import PaySoaItem from "@/views/ni/fin/components/PaySoaItem";
import PaySoaBackItem from "@/views/ni/fin/components/PaySoaBackItem";

import request from "@/router/axios";
import PaySoaAlipay from "@/views/ni/fin/components/PaySoaAlipay";
import PayBillFormDialog from "@/views/ni/fin/components/PayBillFormDialog";
import InvoiceLinkDialog from "@/views/ni/fin/components/InvoiceLinkDialog";
import exForm from "@/views/plugin/workflow/mixins/ex-form";
import PorOrderArrivalSelect from "@/views/ni/por/components/OrderArrivalSelect";
import {numToCapital} from "@/util/util";
import PaySoaItemDrawer from "@/views/ni/fin/components/PaySoaItemDrawer";

export default {
  mixins: [exForm],
  components: {
    PaySoaItemDrawer,
    InvoiceLinkDialog,
    SupplierSelect,
    PaySoaItem,
    PaySoaAlipay,
    PayBillFormDialog,
    PorOrderArrivalSelect,
    PaySoaBackItem,
  },
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      data: [],
      selectionList: [],
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      option: {
        editBtn: false,
        delBtn: false,
        searchEnter: true,
        addBtn: false,
        labelWidth: 110,
        searchLabelWidth: 110,
        align: "center",
        span: 8,
        searchIndex: 3,
        searchIcon: true,
        size: "mini",
        searchSize: "mini",
        height: "auto",
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: false,
        selection: true,
        viewBtn: true,
        column: [
          {
            label: "创建人",
            prop: "createUserName",
            type: "input",
            span: 8,
            addDisplay: false,
            editDisplay: false,
            search: true,
            minWidth: 70,
          },
          {
            label: "创建时间",
            prop: "createTime",
            overHidden: true,
            addDisplay: false,
            editDisplay: false,
            minWidth: 95,
            type: "datetime",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd  HH:mm:ss",
            searchRange: true,
            search: true,
            span: 8,
            defaultTime: ["00:00:00", "23:59:59"],
          },
          {
            label: "对账单编号",
            prop: "serialNo",
            minWidth: 110,
            placeholder: "系统自动生成",
            searchPlaceholder: " ",
            span: 8,
            disabled: true,
            search: true,
            overHidden: true,
          },
          {
            label: "供应商名称",
            prop: "supplierId",
            type: "select",
            remote: true,
            placeholder: " ",
            dicUrl: `/api/ni/base/supplier/info/page?status=2&blacklist=0&keyword={{key}}`,
            props: {
              label: "name",
              value: "id",
              desc: "code",
            },
            dicFormatter: (data) => {
              return data.data.records;
            },
            searchOrder: 99,
            minWidth: 100,
            overHidden: true,
            span: 8,
            hide: true,
            showColumn: false,
            search: true,
            rules: [
              {
                required: true,
                message: "请选择供应商",
                trigger: "blur",
              },
            ],
          },
          {
            label: "供应商名称",
            prop: "supplierName",
            searchPlaceholder: " ",
            minWidth: 130,
            overHidden: true,
            span: 8,
            display: false,
          },
          {
            label: "账套",
            prop: "brand",
            placeholder: " ",
            type: "radio",
            overHidden: true,
            disabled: true,
            dicData: [],
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            search: true,
            span: 8,
            minWidth: 70,
            rules: [
              {
                required: true,
                message: "请选择账套",
                trigger: "blur",
              },
            ],
          },
          {
            label: "对账类型",
            prop: "type",
            type: "select",
            dicData: [],
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            minWidth: 92,
            overHidden: true,
            search: true,
            span: 8,
            rules: [
              {
                required: true,
                message: "请选择对账类型",
                trigger: "blur",
              },
            ],
            change: ({value}) => {
              this.form.advanceAmount = 0.0;
              if (value === "2") {
                this.itemsTab = "alipays";
              } else {
                this.itemsTab = "items";
              }
            },
          },
          {
            label: "对账日期",
            prop: "date",
            type: "daterange",
            valueFormat: "yyyy-MM-dd",
            format: "yyyy-MM-dd",
            minWidth: 175,
            startPlaceholder: "开始日期",
            endPlaceholder: "结束日期",
            span: 8,
            hide: true,
            change: ({value}) => {
              if (!value || value.length == 0) {
                this.$set(this.form, "startDate", undefined);
                this.$set(this.form, "endDate", undefined);
              } else {
                this.$set(this.form, "startDate", value[0]);
                this.$set(this.form, "endDate", value[1]);
              }
            },
          },
          {
            label: "采购明细",
            prop: "items",
            labelPosition: "top",
            hide: true,
            showColumn: false,
            span: 24,
            display: true,
          },
          {
            label: "退货明细",
            prop: "backItems",
            labelPosition: "top",
            hide: true,
            showColumn: false,
            span: 24,
            display: true,
          },
          {
            label: "应付金额",
            prop: "amount",
            type: "number",
            precision: 2,
            placeholder: " ",
            disabled: true,
            minWidth: 100,
            span: 8,
            rules: [
              {
                required: true,
                message: "请输入应付金额",
                trigger: "blur",
              },
            ],
          },
          {
            label: "预付金额",
            prop: "advanceAmount",
            type: "number",
            precision: 2,
            placeholder: " ",
            disabled: true,
            minWidth: 100,
            span: 8,
          },
          {
            label: "币种",
            prop: "currency",
            type: "select",
            search: true,
            dicUrl: "/api/blade-system/dict/dictionary?code=currency",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            value: "RMB",
            placeholder: " ",
            span: 8,
            row: true,
            rules: [
              {
                required: true,
                message: "请选择币种",
                trigger: "blur",
              },
            ],
            control: (val) => {
              if (val === "RMB") {
                return {
                  excRate: {
                    display: false,
                    value: 1,
                    row: false,
                  },
                  currency: {
                    row: true,
                  },
                };
              } else {
                return {
                  excRate: {
                    display: true,
                    row: true,
                  },
                  currency: {
                    row: false,
                  },
                };
              }
            },
          },
          {
            label: "汇率",
            prop: "excRate",
            labelTip:
              "汇率=本位币/原币.如本位币为人民币，原币为美元: 汇率为:0.1439.",
            type: "number",
            placeholder: " ",
            hide: true,
            display: false,
            span: 8,
            row: true,
            rules: [
              {
                required: true,
                message: "请输入汇率",
                trigger: "blur",
              },
            ],
          },
          {
            label: "发票类型",
            prop: "billType",
            minWidth: 93,
            type: "select",
            span: 8,
            dicUrl: "/api/blade-system/dict-biz/dictionary?code=ni_bill_type",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            search: true,
            rules: [
              {
                required: true,
                message: "请选择发票类型",
                trigger: "blur",
              },
            ],
            change: ({value}) => {
              if (value === "1") {
                this.form.taxRate = 13;
              } else if (value === "2") {
                this.form.taxRate = 0;
              }
            },
          },
          {
            label: "税率(%)",
            prop: "taxRate",
            hide: true,
            showColumn: false,
            type: "number",
            precision: 2,
            controls: false,
            span: 8,
          },
          {
            label: "登记发票",
            prop: "billSerialNo",
            display: false,
            minWidth: 110,
            overHidden: true,
          },
          {
            label: "备注",
            prop: "remark",
            minWidth: 110,
            type: "textarea",
            span: 24,
            overHidden: true,
          },
          {
            label: "审核",
            prop: "confirm",
            addDisplay: false,
            editDisplay: false,
            search: true,
            minWidth: 70,
            type: "radio",
            value: false,
            dicData: [
              {
                label: "是",
                value: true,
              },
              {
                label: "否",
                value: false,
              },
            ],
          },
          {
            label: "审核人",
            prop: "confirmUserName",
            addDisplay: false,
            editDisplay: false,
            minWidth: 70,
            hide: true,
          },
          {
            label: "审核时间",
            prop: "confirmTime",
            addDisplay: false,
            editDisplay: false,
            hide: true,
            minWidth: 55,
            type: "datetime",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
          },

          {
            label: "付款状态",
            prop: "payApplyState",
            type: "select",
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_por_order_pay_apply_state",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            minWidth: 70,
            dataType: "number",
            addDisplay: false,
            editDisplay: false,
            search: true,
          },
          {
            label: "付款申请",
            prop: "payApplySerialNo",
            display: false,
            minWidth: 110,
            overHidden: true,
          },
        ],
      },
      brandDict: [],
      brandDictKeyValue: {},
      soaId: null,
      supplierId: null,
      invoiceSelectTitle: "",
      itemsTab: "items",
      backItemsTab: "backItems",
    };
  },
  beforeRouteEnter(to, from, next) {
    to.meta.keepAlive = true;
    next();
  },
  activated() {
    this.onLoad(this.page);
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.niFinPaySoa_add, false),
        viewBtn: this.vaildData(this.permission.niFinPaySoa_view, false),
        delBtn: this.vaildData(this.permission.niFinPaySoa_delete, false),
        editBtn: this.vaildData(this.permission.niFinPaySoa_edit, false),
        confirmBtn: this.vaildData(this.permission.niFinPaySoa_confirm, false),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
    subIds() {
      let ids = [];
      this.selectionListSub.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  created() {
    this.dictInit();
  },
  methods: {
    sumAmount() {
      let amount = 0;
      let backAmount = 0;
      if (this.form.items) {
        amount += this.form.items.reduce((acc, cur) => {
          return Number(acc) + Number(cur.amount);
        }, 0);
      }
      if (this.form.backItems) {
        backAmount += this.form.backItems.reduce((acc, cur) => {
          return Number(acc) + Number(cur.amount);
        }, 0);
      }
      console.log(amount);
      console.log(backAmount);
      this.$set(this.form, "amount", Number(amount) - Number(backAmount));
      //查询预付金额
      // this.loadAdvanceAmount();
    },
    handleAdd(brand) {
      this.form.brand = brand;
      this.option.addTitle = `新增【${this.brandDictKeyValue[brand]}】`;
      this.$refs.crud.rowAdd();
    },
    dictInit() {
      request({
        url: "/api/blade-system/dict-biz/dictionary?code=ni_brand",
        method: "get",
      }).then((res) => {
        const column = this.findObject(this.option.column, "brand");
        column.dicData = res.data.data;
        this.brandDict = res.data.data;
        this.brandDictKeyValue = this.brandDict.reduce((acc, cur) => {
          acc[cur.dictKey] = cur.dictValue;
          return acc;
        }, {});
      });
      request({
        url: "/api/blade-system/dict-biz/dictionary?code=ni_fin_pay_soa_type",
        method: "get",
      }).then((res) => {
        const column = this.findObject(this.option.column, "type");
        column.dicData = res.data.data;
      });
    },
    // 主表模块
    rowSave(row, done, loading) {
      add(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          window.console.log(error);
          loading();
        }
      );
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          window.console.log(error);
          loading();
        }
      );
    },
    cellStyle({row, column}) {
      if ("serialNo" === column.columnKey && row.confirm) {
        return {
          backgroundColor: "#096dd9",
          color: "#fff",
        };
      }
      if ('supplierName' === column.columnKey && [1].includes(row.payApplyState)) {
        return {
          backgroundColor: "#52c41a",
          color: "#fff",
        };
      } else if ('supplierName' === column.columnKey && [2].includes(row.payApplyState)) {
        return {
          backgroundColor: "#237804",
          color: "#fff",
        };
      }
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    rowBilling(row) {
      this.soaId = row.id;
      this.supplierId = row.supplierId;
      this.invoiceSelectTitle = `[${row.supplierName}](金额：${row.amount})对账发票`;
      this.$refs.paySoaInvoiceDialogRef.visible = true;
    },
    async rowPayableApply(row) {
      const form = {
        supplierId: row.supplierId,
        type: "2",
        paySoaId: row.id,
        paySoaAmount: Number(row.amount),
        unPayAmount: Number(row.amount) - Number(row.payApplyAmount),
        amount: Number(row.amount) - Number(row.payApplyAmount),
        upperAmount: numToCapital(
          Number(row.amount) - Number(row.payApplyAmount)
        ),
        currency: row.currency,
        billType: row.billType,
        taxRate: row.taxRate,
        items: [],
      };
      const res = await getPaySoaDetail(row.id);
      const data = res.data.data;
      if (data.payApplyAmount && data.payApplyAmount >= row.amount) {
        this.$message({
          type: "warning",
          message: "该对账已申请付款，请勿重复申请!",
        });
        row.payApplyState = 2;
        return;
      }
      const items = [];
      if (data.items) {
        data.items.forEach((item) => {
          items.push({...item, cell: false});
        });
      }
      if (data.backItems) {
        data.backItems.forEach((item) => {
          items.push({
            ...item,
            num: -Math.abs(Number(item.num)),
            amount: -Math.abs(Number(item.amount)),
            cell: false,
          });
        });
      }
      // form.items = items;
      this.dynamicRoute(
        {
          processDefKey: "process_fin_payable_apply",
          formKey: "wf_ex_fin/PayableApply",
          form: encodeURIComponent(
            Buffer.from(JSON.stringify(form)).toString("utf8")
          ),
        },
        "start"
      );
    },
    rowItems(row) {
      this.$refs.itemRef.init(row);
    },
    async rowConfirmBack(row) {
      let msg = "确定弃审选择数据?"
      if (row.payApplyState && [1, 2].includes(row.payApplyState)) {
        const res = await getPayableApplyList({paySoaId: row.id, type: '2'})
        const {data} = res.data
        if (data.length > 0 && data.some(item => item.status === 9 || item.payState === '1')) {
          this.$message({
            type: "warning",
            message: "该对账已付款，无法弃审!",
          });
          return;
        }
        msg = "该数据<span style='color: #f5222d;font-weight: bold'>已提交付款申请</span>，是否<span style='color: #f5222d;font-weight: bold'>撤回该申请</span>并弃审？";
      }
      this.$confirm(msg, {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        dangerouslyUseHTMLString: true,
      })
        .then(() => {
          return confirmBack(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    rowConfirm(row) {
      this.$confirm("确定审核选择数据?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return confirm(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    handleConfirm() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定审核选择数据?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return confirm(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeItemAddOpen(done) {
      if (!this.form.supplierId) {
        this.$message({
          type: "warning",
          message: "请选择供应商!",
        });
        return;
      }
      if (!this.form.brand) {
        this.$message({
          type: "warning",
          message: "请选择账套!",
        });
        return;
      }
      if (this.form.type === '2') {
        done({
          brand: this.form.brand,
          startDate: this.form.startDate,
          endDate: this.form.endDate,
          porOrderArrivalId: this.form.porOrderArrivalId,
        });
      } else
        done({
          supplierId: this.form.supplierId,
          brand: this.form.brand,
          startDate: this.form.startDate,
          endDate: this.form.endDate,
          porOrderArrivalId: this.form.porOrderArrivalId,

        });
    },
    beforeOpen(done, type) {
      if ("add" === type) {
        this.form = {
          ...this.form,
          type: "1",
          amount: 0,
          advanceAmount: 0,
          billState: 0,
          payApplyState: 0,
          items: [],
          backItems: [],
        };
      }
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then((res) => {
          this.form = res.data.data;
          this.form.date = [this.form.startDate, this.form.endDate];
          if (this.form.items) {
            this.form.items = res.data.data.items.map((item) => ({
              ...item,
              orderNum: item.num,
              cell: false,
            }));
          }
          if (this.form.backItems) {
            this.form.backItems = res.data.data.backItems.map((item) => ({
              ...item,
              backNum: item.num,
              cell: false,
            }));
          }
        });
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      const q = Object.assign(params, this.query);
      if (q.createTime && q.createTime.length === 2) {
        q.startCreateTime = q.createTime[0];
        q.endCreateTime = q.createTime[1];
        q.createTime = null;
      }
      q.descs = "id";
      getList(page.currentPage, page.pageSize, q).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
  },
};
</script>

<style></style>

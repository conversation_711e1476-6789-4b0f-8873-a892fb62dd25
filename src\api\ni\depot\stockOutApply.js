import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/ni/depot/stock/apply/out/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/ni/depot/stock/apply/out/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/ni/depot/stock/apply/out/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/ni/depot/stock/apply/out/save',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/ni/depot/stock/apply/out/update',
    method: 'post',
    data: row
  })
}


export const submitApply = (ids) => {
  return request({
    url: '/api/ni/depot/stock/apply/out/submitApply',
    method: 'post',
    params: {
      ids
    }
  })
}

/**
 * 出库确认
 * @param ids
 * @returns {AxiosPromise}
 */
export const outSubmit = (row) => {
  return request({
    url: '/api/ni/depot/stock/apply/out/confirm',
    method: 'post',
    data: row
  })
}


export const getItemPage = (current, size, params) => {
  return request({
    url: '/api/ni/depot/stock/apply/out/item/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}
export const submit = (ids) => {
  return request({
    url: '/api/ni/depot/stock/apply/out/submit',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const back = (ids) => {
  return request({
    url: '/api/ni/depot/stock/apply/out/back',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const toVoid = (ids) => {
  return request({
    url: '/api/ni/depot/stock/apply/out/toVoid',
    method: 'post',
    params: {
      ids,
    }
  })
}

import request from "@/router/axios";

export const getList = (current, size, params) => {
  return request({
    url: "/api/ni/depot/check/page",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/ni/depot/check/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const remove = (ids) => {
  return request({
    url: "/api/ni/depot/check/remove",
    method: "post",
    params: {
      ids,
    },
  });
};

export const toVoid=(ids)=>{
  return request({
    url: "/api/ni/depot/check/toVoid",
    method: "post",
    params: {
      ids,
    },
  });
}
export const add = (row) => {
  return request({
    url: "/api/ni/depot/check/save",
    method: "post",
    data: row,
  });
};

export const update = (row) => {
  return request({
    url: "/api/ni/depot/check/update",
    method: "post",
    data: row,
  });
};
export const check=(id)=>{
  return request({
    url: '/api/ni/depot/check/check',
    method: 'post',
    params: {
      id,
    }
  })
}


export const checked = (row) => {
  return request({
    url: "/api/ni/depot/check/checked",
    method: "post",
    data: row,
  });
};

export const audit = (id) => {
  return request({
    url: "/api/ni/depot/check/audit",
    method: "post",
    params: {
      id,
    },
  });
};

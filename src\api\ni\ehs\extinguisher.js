import request from "@/router/axios";

export const getPage = (current, size, params) => {
  return request({
    url: "/api/ni/ehs/extinguisher/page",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/ni/ehs/extinguisher/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const add = (row) => {
    return request({
      url: "/api/ni/ehs/extinguisher/add",
      method: "post",
      data: row,
    });
  };

  export const remove = (ids) => {
    return request({
      url: "/api/ni/ehs/extinguisher/remove",
      method: "post",
      params: {
        ids,
      },
    });
  };
  
  export const update = (row) => {
    return request({
      url: "/api/ni/ehs/extinguisher/update",
      method: "post",
      data: row,
    });
  };

  export const submit = (ids) => {
    return request({
      url: '/api/ni/ehs/extinguisher/submit',
      method: 'post',
      params: {
        ids,
      }
    })
  }
  
  export const back = (ids) => {
    return request({
      url: '/api/ni/ehs/extinguisher/back',
      method: 'post',
      params: {
        ids,
      }
    })
  }

  export const save = (row) => {
    return request({
      url: "/api/ni/ehs/extinguisher/save",
      method: "post",
      data: row,
    });
  };

  export const notice = (row) => {
    return request({
      url: "/api/ni/ehs/extinguisher/notice",
      method: "post",
      data: row,
    });
  };

  export const getRummagerInfo = () => {
    return request({
      url: "/api/ni/ehs/extinguisher/rummagerInfo",
      method: "get",
    });
  };

//修改管理员
  // export const changeMananger = (selectedIds,managerIds) => {
  //   return request({
  //     url: "/api/ni/ehs/extinguisher/changeMananger",
  //     method: "post",
  //     params: {
  //       selectedIds,
  //       managerIds,
  //     }
  //   })
  // };

  //新增灭火器第二负责人
  export const addSecondUser = (extinguisherId, secondHeadUser) =>{
    return request({
      url:"/api/ni/ehs/extinguisher/addUser",
      method:"post",
      params:{
        extinguisherId,
        secondHeadUser,
      }
    })
  };
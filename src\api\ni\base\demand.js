import request from '@/router/axios';

export const getList = (current, size, params, createDept) => {
  return request({
    url: '/api/ni/base/demand/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
      createDept,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/ni/base/demand/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/ni/base/demand/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/ni/base/demand/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/ni/base/demand/submit',
    method: 'post',
    data: row
  })
}

export const getDetailByProcessInsId = (processInsId) => {
  return request({
    url: '/api/ni/base/demand/detail',
    method: 'get',
    params: {
      processInsId
    }
  })
}

export const start = (row) => {
  return request({
    url: '/api/ni/base/demand/start',
    method: 'post',
    data: row
  })
}

export const saveAndStart = (processDefKey, row) => {
  return request({
    url: '/api/ni/base/demand/saveAndStart',
    method: 'post',
    params: {
      processDefKey
    },
    data: row
  })
}

// 数据新增
export const getProcessIdByProcessKey = (processKey) => {
	return request({
		url: `/api/blade-workflow/process/getProcessIdByProcessKey`,
		method: 'GET',
		params:{
			processKey
		}
	})
}
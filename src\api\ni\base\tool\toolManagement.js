import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/ni/base/tool/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getPage = (current, size, params) => {
  return request({
    url: '/api/ni/base/tool/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/ni/base/tool/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/ni/base/tool/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/ni/base/tool/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/ni/base/tool/submit',
    method: 'post',
    data: row
  })
}

export const getMaterialInfo = (value) => {
  return request({
    url: '/api/ni/base/tool/getMaterialInfo',
    method: 'get',
    params: {
      materialCode: value
    }
  })
}
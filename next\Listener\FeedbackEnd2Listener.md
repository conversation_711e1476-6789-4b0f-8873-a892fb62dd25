```java
// 定义包路径，属于问题反馈模块的监听器组件
package com.natergy.ni.feedback.listener;

// 导入MyBatis-Plus的查询条件构造器
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
// 导入问题反馈相关实体类和VO
import com.natergy.ni.feedback.entity.FeedbackEntity;
import com.natergy.ni.feedback.entity.FeedbackSolvingRecordEntity;
import com.natergy.ni.feedback.enums.FeedbackStatusEnum;
import com.natergy.ni.feedback.service.IFeedbackService;
import com.natergy.ni.feedback.vo.FeedbackSolvingRecordVO;
// 导入Lombok日志注解
import lombok.extern.slf4j.Slf4j;
// 导入Apache字符串工具类
import org.apache.commons.lang3.StringUtils;
// 导入Flowable工作流执行监听器相关类
import org.flowable.engine.delegate.DelegateExecution;
// 导入Blade框架的缓存工具类和系统实体
import org.springblade.common.cache.SysCache;
import org.springblade.common.cache.UserCache;
import org.springblade.modules.system.entity.User;
// 导入Spring组件注解和事务注解
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

// 导入Java时间处理和集合工具类
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 问题负责人完成任务
 * （当问题负责人完成处理任务后触发的监听器）
 *
 * <AUTHOR>  // 作者标识
 * @since 2025-04-21  // 类创建日期
 */
// @Slf4j：Lombok注解，自动生成日志对象
@Slf4j
// @Component("FeedbackEnd2Listener")：Spring注解，将类注册为组件，名称为"FeedbackEnd2Listener"
// 供Flowable工作流引擎通过该名称调用
@Component("FeedbackEnd2Listener")
// 实现Flowable的ExecutionListener接口，作为流程执行监听器
public class FeedbackEnd2Listener implements org.flowable.engine.delegate.ExecutionListener {

	// 注入问题反馈服务，用于操作问题反馈数据
	private final IFeedbackService feedbackService;

	// 定义字符串分隔符（用于多值字段的拆分和拼接）
	private final static String SEPARATOR = ",";

	// 构造函数注入问题反馈服务
	public FeedbackEnd2Listener(IFeedbackService feedbackService) {
		this.feedbackService = feedbackService;
	}

	// @Transactional：声明事务，发生异常时回滚
	@Transactional(rollbackFor = Exception.class)
	// 实现监听器的notify方法，流程执行到该节点时触发
	@Override
	public void notify(DelegateExecution execution) {
		// 获取流程实例ID（用于关联问题反馈实体）
		String processInstanceId = execution.getProcessInstanceId();

		// 根据流程实例ID查询对应的问题反馈实体
		FeedbackEntity feedbackEntity = feedbackService.getOne(
			new LambdaQueryWrapper<FeedbackEntity>()
				.eq(FeedbackEntity::getProcessInstanceId, processInstanceId)
		);

		// 从流程变量中获取"claimList"（问题认领记录列表，可能是VO或Map类型）
		Object claimList = execution.getVariable("claimList");
		List<?> list = (List<?>) claimList;  // 转换为泛型列表

		// 检查列表元素类型，获取最新的解决日期（根据元素类型调用不同方法）
		LocalDate latestDate = checkGenericType(list) ? getLatestDateByVo(claimList) : getLatestDateByMap(claimList);

		// 如果存在最新解决日期，更新到问题反馈实体
		if (Objects.nonNull(latestDate)) {
			feedbackEntity.setFinalResolutionTime(latestDate);
		}

		// 从流程变量中获取"responsibility"（负责人ID列表字符串）
		Object responsibility = execution.getVariable("responsibility");
		String responsibilityStr = Optional.ofNullable(responsibility)
			.map(String::valueOf)
			.orElse(StringUtils.EMPTY);

		// 根据认领记录列表是否为空，设置问题状态：
		// - 列表为空 → "无需处理"
		// - 列表非空 → "待确认"
		feedbackEntity.setStatus(
			list.isEmpty() 
				? FeedbackStatusEnum.NO_NEED_TO_HANDLE.getValue() 
				: FeedbackStatusEnum.TO_BE_CONFIRMED.getValue()
		);

		// 处理负责人对应的部门名称列表（负责人ID不为空时）
		List<String> deptNameCollect;
		if (StringUtils.isNotBlank(responsibilityStr)) {
			// 1. 拆分负责人ID字符串为数组
			// 2. 遍历每个ID，获取用户信息 → 再获取用户所属部门名称
			// 3. 收集所有部门名称并去重（默认逻辑，实际可能需要去重）
			deptNameCollect = Arrays.stream(responsibilityStr.split(SEPARATOR))
				.map(item -> {
					User user = UserCache.getUser(Long.valueOf(item));  // 从缓存获取用户
					return SysCache.getDeptName(Long.valueOf(user.getDeptId()));  // 从缓存获取部门名称
				})
				.collect(Collectors.toList());

			// 将部门名称列表用分隔符拼接为字符串，更新到问题反馈实体
			feedbackEntity.setDeptNames(StringUtils.join(deptNameCollect, SEPARATOR));
		}

		// 保存更新后的问题反馈实体
		feedbackService.saveOrUpdate(feedbackEntity);
	}

	/**
	 * 检查列表元素的泛型类型
	 * @param list 待检查的列表
	 * @return 如果列表非空且第一个元素是FeedbackSolvingRecordVO类型，返回true；否则返回false
	 */
	private static boolean checkGenericType(List<?> list) {
		if (!list.isEmpty()) {
			Object firstElement = list.get(0);
			return firstElement instanceof FeedbackSolvingRecordVO;
		}
		return false;
	}

	/**
	 * 当列表元素是LinkedHashMap时，提取最新的解决日期
	 * （适用于流程变量序列化后可能转为Map的场景）
	 * @param claimList 认领记录列表（Map类型）
	 * @return 最新的解决日期（LocalDate）
	 */
	private static LocalDate getLatestDateByMap(Object claimList) {
		// 将claimList转换为LinkedHashMap列表（若为null则返回空列表）
		List<LinkedHashMap<String, Object>> list = Optional.ofNullable(claimList)
			.map(obj -> (List<LinkedHashMap<String, Object>>) obj)
			.orElse(Collections.EMPTY_LIST);

		// 定义日期格式化器（匹配"yyyy-MM-dd"格式）
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
		LocalDate latestDate = null;

		// 遍历每条记录，解析解决日期并找到最新的日期
		for (LinkedHashMap<String, Object> record : list) {
			Object dateObj = record.get("resolveDate");  // 获取"resolveDate"字段值
			if (dateObj != null) {
				try {
					LocalDate currentDate = LocalDate.parse(dateObj.toString(), formatter);
					// 比较并更新最新日期
					if (latestDate == null || currentDate.isAfter(latestDate)) {
						latestDate = currentDate;
					}
				} catch (Exception e) {
					// 处理日期解析异常（如格式不匹配）
					e.printStackTrace();
				}
			}
		}
		return latestDate;
	}

	/**
	 * 当列表元素是FeedbackSolvingRecordVO时，提取最新的解决日期
	 * @param claimList 认领记录列表（VO类型）
	 * @return 最新的解决日期（LocalDate）
	 */
	private static LocalDate getLatestDateByVo(Object claimList) {
		// 将claimList转换为FeedbackSolvingRecordVO列表（若为null则返回空列表）
		List<FeedbackSolvingRecordVO> list = Optional.ofNullable(claimList)
			.map(obj -> (List<FeedbackSolvingRecordVO>) obj)
			.orElse(Collections.EMPTY_LIST);

		// 筛选出解决日期不为空的记录，按日期倒序排序后取第一条（最新日期）
		Optional<FeedbackSolvingRecordVO> finalSolvingRecord = list.stream()
			.filter(item -> Objects.nonNull(item.getResolveDate()))  // 过滤空日期
			.min((r1, r2) -> r2.getResolveDate().compareTo(r1.getResolveDate()));  // 倒序排序取最小（实际是最大）

		// 返回最新日期（若存在）
		return finalSolvingRecord.map(FeedbackSolvingRecordEntity::getResolveDate).orElse(null);
	}
}
```

### 类功能说明

该类是基于**Flowable 工作流引擎**的流程执行监听器，命名为`FeedbackEnd2Listener`，主要作用是在**问题负责人完成处理任务后**触发，自动更新问题反馈的最终解决时间、状态和部门信息，实现流程结束与业务数据的同步。

#### 核心业务场景

当问题的所有负责人完成各自的处理任务后，工作流执行到该监听器关联的节点，自动汇总处理结果（如最新解决日期），更新问题状态，并记录相关部门信息，为后续流程（如确认环节）提供数据支持。

#### 关键逻辑解析

1. **关联业务实体**：通过流程实例 ID（`processInstanceId`）查询对应的`FeedbackEntity`，实现工作流与业务数据的关联。
2. **处理认领记录**：从流程变量`claimList`中获取负责人的处理记录，根据记录类型（`FeedbackSolvingRecordVO`或`LinkedHashMap`）提取最新的解决日期，更新为问题的最终解决时间。
3. **更新问题状态**：根据认领记录列表是否为空，设置问题状态为 “无需处理” 或 “待确认”。
4. **维护部门信息**：通过负责人 ID 列表，从缓存中获取对应的部门名称，更新到问题反馈实体，便于统计和展示。

#### 技术亮点

- **兼容多数据类型**：通过`checkGenericType`方法判断列表元素类型，分别处理`FeedbackSolvingRecordVO`和`LinkedHashMap`两种格式，适配不同的流程变量序列化场景。
- **日期处理健壮性**：使用`DateTimeFormatter`解析日期，并通过异常处理避免格式错误导致的崩溃。
- **缓存优化**：通过`UserCache`和`SysCache`获取用户和部门信息，减少数据库查询，提升性能。
- **事务保障**：使用`@Transactional`确保数据更新的原子性，避免流程与业务数据不一致。

该监听器是工作流结束阶段的关键组件，通过自动汇总和更新业务数据，减少手动操作，确保流程闭环的数据准确性。


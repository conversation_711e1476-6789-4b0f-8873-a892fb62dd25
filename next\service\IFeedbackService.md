```java
// 定义包路径，属于问题反馈模块的服务层接口
package com.natergy.ni.feedback.service;

// 导入MyBatis-Plus的分页接口
import com.baomidou.mybatisplus.core.metadata.IPage;
// 导入当前模块的DTO、实体、参数和VO类
import com.natergy.ni.feedback.dto.SearchResponseDTO;
import com.natergy.ni.feedback.entity.FeedbackEntity;
import com.natergy.ni.feedback.params.FeedbackParams;
import com.natergy.ni.feedback.vo.FeedbackVO;
// 导入BladeX框架的基础服务接口（封装了CRUD基础方法）
import org.springblade.core.mp.base.BaseService;
// 导入Spring的异步注解
import org.springframework.scheduling.annotation.Async;

// 导入Java工具类和并发相关类
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 问题反馈 服务类
 *
 * <AUTHOR>
 * @since 2025-02-26
 */
// 继承BaseService接口，泛型为FeedbackEntity，获得基础CRUD操作能力
public interface IFeedbackService extends BaseService<FeedbackEntity> {

	/**
	 * 条件搜索 客户列表（注：注释可能有误，实际应为“问题列表”）
	 *
	 * @param page           分页数据（包含页码、每页条数等）
	 * @param feedbackParams 查询参数对象（封装了各种查询条件）
	 * @param description    问题描述（用于模糊搜索或关键词匹配）
	 * @param userIdsStr     可查看的用户ID集合（用逗号分隔的字符串，用于数据权限控制）
	 * @return 分页的问题反馈VO列表（IPage<FeedbackVO>）
	 */
	IPage<FeedbackVO> selectFeedbackPage(IPage<FeedbackVO> page, FeedbackParams feedbackParams, String description, String userIdsStr);


	/**
	 * 异步同步到Qdrant向量数据库
	 *
	 * @param feedback 问题反馈实体（包含需要同步的数据）
	 * @return CompletableFuture<Void> 异步任务结果（无返回值）
	 */
	@Async  // 标识为异步方法，由Spring异步执行，不阻塞主线程
	CompletableFuture<Void> asyncQdrant(FeedbackEntity feedback);


	/**
	 * 同步数据到Qdrant向量数据库（异步执行）
	 *
	 * @param feedbackEntity 问题反馈实体（包含需要同步的数据）
	 */
	@Async  // 标识为异步方法
	void synchronizeData(FeedbackEntity feedbackEntity);

	/**
	 * 根据文本搜索相关问题
	 *
	 * @param queryText 搜索文本（用户输入的查询关键词）
	 * @return 搜索结果列表（包含匹配的问题信息）
	 */
	List<SearchResponseDTO> searchText(String queryText);
}
```

### 接口功能说明

该接口是问题反馈模块的核心服务接口，继承自 BladeX 框架的`BaseService`，除了基础的 CRUD 操作外，还定义了针对问题反馈的业务方法，主要功能包括：

1. **条件分页查询**：`selectFeedbackPage`方法支持多条件组合查询，结合分页参数和数据权限（`userIdsStr`），返回符合条件的问题反馈 VO 列表，用于前端展示。
2. **向量数据库同步**：
   - `asyncQdrant`：异步将问题反馈数据同步到 Qdrant 向量数据库，返回`CompletableFuture`便于异步任务管理（如等待完成、处理异常）。
   - `synchronizeData`：另一个异步同步方法，直接同步数据到 Qdrant，无返回值。
3. **文本搜索**：`searchText`方法根据输入的文本关键词，搜索相关的问题反馈记录，返回匹配结果（通常基于 Qdrant 向量数据库的相似性搜索实现）。

这些方法的具体实现由接口的实现类（如`FeedbackServiceImpl`）提供，服务层通过调用数据访问层（DAO）和外部服务（如 Qdrant）完成业务逻辑，是控制器与数据层之间的桥梁。
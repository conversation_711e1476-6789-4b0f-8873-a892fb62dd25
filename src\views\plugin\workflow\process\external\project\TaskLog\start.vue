<template>
    <basic-container>
      <avue-skeleton :loading="waiting" avatar :rows="8">
        <div style="display: flex;">
          <avue-title
            style="margin-bottom: 20px"
            :styles="{ fontSize: '20px' }"
            :value="process.name"
          ></avue-title>
          <el-badge v-if="permission.wf_process_draft&&draftCount > 0" :value="draftCount"
                    style="margin-top: 5px;  margin-right: 40px;" type="warning">
            <el-button
              size="mini"
              v-loading="loading"
              @click="handleDraftBox"
            >草稿箱
            </el-button>
          </el-badge>
        </div>
        <el-card shadow="never" style="margin-top: 20px">
          <!-- 自定义表单区域 -->
          <avue-form
            v-if="
              option &&
              ((option.column && option.column.length > 0) ||
                (option.group && option.group.length > 0))
            "
            v-model="form"
            ref="form"
            :option="option"
            :defaults.sync="defaults"
            @submit="handleSubmit"
            @error="loading = false"
            :upload-preview="handleUploadPreview"
          >
          </avue-form>
          <!-- 自定义表单区域 -->
        </el-card>
        <el-card shadow="never" style="margin-top: 20px" v-if="showExamForm">
          <wf-examine-form
            ref="examineForm"
            :process="process"
            @user-select="handleUserSelect"
          ></wf-examine-form>
        </el-card>
        <div style="height: 120px"></div>
        <el-row
          class="foot-item avue-affix"
          :style="{
            width: isCollapse ? 'calc(100% - 80px)' : 'calc(100% - 260px)',
          }"
          id="avue-view"
        >
          <el-button
            v-no-more-click
            type="primary"
            size="mini"
            v-loading="loading"
            @click="handleSubmit"
            >发起
          </el-button>
          <el-button
            v-if="permission.wf_process_draft"
            type="success"
            size="mini"
            v-loading="loading"
            @click="handleDraftNotClose(process.id, process.formKey, form, process.key)"
            >存为草稿
          </el-button>
          <el-button
            v-if="permission.wf_process_draft"
            type="success"
            size="mini"
            v-loading="loading"
            @click="handleDraft(process.id, process.formKey, form, process.key)"
            >存为草稿并关闭
          </el-button>
        </el-row>
      </avue-skeleton>

      <!-- 人员选择弹窗 -->
      <wf-user-select
        ref="user-select"
        :check-type="checkType"
        :default-checked="defaultChecked"
        @onConfirm="handleUserSelectConfirm"
      ></wf-user-select>
      <!-- 草稿弹窗 -->
      <draft-popup
        :visible.sync="isDraftPopupVisible"
        :draftList="draftList"
        @select="handleDraftSelect"
        @delete="handleDraftDelete"
      ></draft-popup>
    </basic-container>
  </template>

  <script>
  import WfExamineForm from "@/views/plugin/workflow/process/components/examForm.vue";
  import WfUserSelect from "@/views/plugin/workflow/process/components/user-select";
  import exForm from "@/views/plugin/workflow/mixins/ex-form";
  import draft from "@/views/plugin/workflow/mixins/draft";
  import UserSelect from "@/components/user-select";
  import DraftPopup from "@/views/plugin/workflow/process/components/draftPopup.vue";
  import debounce from "@/util/debounce";

  export default {
    components: {
      WfUserSelect,
      WfExamineForm,
      UserSelect,
      DraftPopup
    },
    mixins: [exForm, draft],
    activated() {
      let val=this.$route.query.p
      if (val) {
        let text = Buffer.from(val, "base64").toString();
        text = text.replace(/[\r|\n|\t]/g, "");
        const param = JSON.parse(text);
        const { processId, processDefKey, form } = param;
        if (form) {
          const f = JSON.parse(
              new Buffer(decodeURIComponent(form), "utf8").toString("utf8")
          );
          this.form = Object.assign(this.form, f);
        }
        if (processId) {
          this.getForm(processId);
        } else if (processDefKey) {
          this.getFormByProcessDefKey(processDefKey);
        }
      }
    },
    computed: {
      showExamForm() {
        const { hideComment, hideCopy, hideExamine } = this.process;
        return !hideComment || !hideCopy || !hideExamine;
      },
    },
    beforeRouteEnter(to, from, next) {
      next((vm) => {
        //  这里的vm指的就是vue实例，可以用来当做this使用
        vm.fromPath = from.path; //获取上一级路由的路径
      });
    },
    data() {
      return {
        orderSelectShow: false,
        fromPath: "",
        defaults: {},
        form: {},
        option: {
          labelWidth: 120,
          calcHeight: 30,
          size: "mini",
          menuBtn: false,
          column: [
          {
            label: "日期",
            prop: "day",
            type: "date",
            placeholder: " ",
            row: true,
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            pickerOptions: {
              disabledDate(time) {
                return time.getTime() > Date.now();
              },
            },
          },
          {
            label: "预计完成日期",
            placeholder: " ",
            type: "date",
            prop: "estimateEndDate",
            row: true,
            hide: true,
            disabled: true,
            showColumn: false,
          },
          {
            label: "剩余(天)",
            prop: "surplus",
            minWidth: 90,
            placeholder: " ",
            type: "number",
            disabled: true,
            row: true,
          },

          {
            label: "工作内容",
            prop: "content",
            type: "textarea",
            minWidth: 400,
            minRows: 3,
            span: 24,
          },
          {
            label: "抄送人",
            prop: "copyUser",

          },
          ],
        },
        process: {},
        loading: false,
        payment: 0,
        typeDict: [],
        typeDictKeyValue: {},
        isDraftPopupVisible: false,
        draftList: [],
        draftCount: 0,
        draftId: null
      };
    },
    methods: {
      getForm(processId) {
        this.getStartForm(processId).then((res) => {
          const _this = this;
          let { process } = res;
          this.form.processId = process.id;
          const option = _this.option;
          const { column, group } = option;
          column.forEach((col) => {
            if (col.value) col.value = _this.getDefaultValues(col.value);
          });
          if (group && group.length > 0) {
            // 处理group
            group.forEach((gro) => {
              gro.column.forEach((col) => {
                if (col.value) col.value = _this.getDefaultValues(col.value);
              });
            });
          }
          if (_this.permission.wf_process_draft) {
            // 查询草稿箱
            this.initDraft(process.id,process.key).then((data) => {
              this.draftCount = data.length;
              this.draftList = data;
              if (data && Array.isArray(data) && data.length > 0) {
                this.$confirm("是否引用之前保存的草稿？", "提示", {})
                .then(() => {
                  this.isDraftPopupVisible = true; // 打开草稿弹窗
                })
              }
            });
          }
          _this.waiting = false;
        });
      },
      getFormByProcessDefKey(processDefKey) {
        this.getStartFormByProcessDefKey(processDefKey).then((res) => {
          const _this = this;
          let { process } = res;
          _this.form.processId = process.id;
          const option = _this.option;
          const { column, group } = option;
          column.forEach((col) => {
            if (col.value) col.value = _this.getDefaultValues(col.value);
          });
          if (group && group.length > 0) {
            // 处理group
            group.forEach((gro) => {
              gro.column.forEach((col) => {
                if (col.value) col.value = _this.getDefaultValues(col.value);
              });
            });
          }
          if (_this.permission.wf_process_draft) {
            // 查询草稿箱
            this.initDraft(process.id,process.key).then((data) => {
              this.draftCount = data.length;
              this.draftList = data;
              if (data && Array.isArray(data) && data.length > 0) {
                this.$confirm("是否引用之前保存的草稿？", "提示", {})
                .then(() => {
                  this.isDraftPopupVisible = true; // 打开草稿弹窗
                })
              }
            });
          }
          _this.waiting = false;
        });
      },
      handleSubmit:debounce(function () {
        this.loading = true;
        this.form.draftId = this.draftId;
        this.handleStartProcess(true)
          .then((done) => {
            this.$message.success("发起成功");
            if(this.draftId != null){
              this.draftCount = this.draftCount-1;
              this.draftList = this.draftList.filter(item => item.id !== this.draftId);
            }
            if (this.fromPath) {
              this.handleCloseTag(this.fromPath);
            } else this.handleCloseTag("/ni/project/task-log");
            done();
          })
          .catch(() => {
            this.loading = false;
          });
      },1000),
      getDefaultValues(value) {
        let defaultValue = "";
        if (value.toString().includes("${") && value.toString().includes("}")) {
          try {
            defaultValue = eval("`" + value + "`");
          } catch (err) {
            defaultValue = value;
          }
        } else defaultValue = value;
        return defaultValue;
      },
      //选择草稿
      handleDraftSelect(selectedDraft) {
        //草稿版本与流程版本不一致
        if(!selectedDraft.sameVersion){
          this.$confirm("选中的草稿与当前流程版本不一致，是否继续引用？", "提示", {})
          .then(() => {
          this.draftId = selectedDraft.id;
          this.form = JSON.parse(selectedDraft.variables);
          this.isDraftPopupVisible = false;
          });
        } else {
          this.draftId = selectedDraft.id;
          this.form = JSON.parse(selectedDraft.variables);
        }
      },
      //删除草稿
      handleDraftDelete(draftId) {
        this.$confirm("是否删除选中的草稿箱数据？", "提示", {})
        .then(() => {
          this.$axios.post(`/api/blade-workflow/process/draft/delete/${draftId}`)
          .then(response => {
            this.$message.success('草稿删除成功');
            this.draftCount = this.draftCount-1;
            this.draftList = this.draftList.filter(item => item.id !== draftId);
          })
          .catch(error => {
            this.$message.error('草稿删除失败，请重试');
          });
        })
      },
      handleDraftBox() {
        if (this.draftList.length > 0) {
          this.isDraftPopupVisible = true;
        } else {
          // 重新获取草稿数据
          this.initDraft(this.form.processId).then((data) => {
          this.draftCount = data.length;
          this.draftList = data;
          if (data && Array.isArray(data) && data.length > 0) {
            this.isDraftPopupVisible = true;
          }
          });
        }
      },

    },
  };
  </script>

  <style lang="scss" scoped>
  .foot-item {
    position: fixed;
    bottom: 0;
    margin-left: -20px;
    // right: 0;
    z-index: 101;
    height: 66px;
    background-color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    -webkit-transition: 0.3s;
    transition: 0.3s;
    -webkit-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  }
  </style>

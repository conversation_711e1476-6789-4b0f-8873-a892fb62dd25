import request from "@/router/axios";

export const getList = (current, size, params) => {
  return request({
    url: "/api/ni/fin/voucher/list",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/ni/fin/voucher/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const remove = (ids) => {
  return request({
    url: "/api/ni/fin/voucher/remove",
    method: "post",
    params: {
      ids,
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/ni/fin/voucher/save",
    method: "post",
    data: row,
  });
};

export const update = (row) => {
  return request({
    url: "/api/ni/fin/voucher/update",
    method: "post",
    data: row,
  });
};
export const submit = (ids) => {
  return request({
    url: "/api/ni/fin/voucher/submit",
    method: "post",
    params: {
      ids,
    },
  });
};
export const audit = (ids) => {
  return request({
    url: "/api/ni/fin/voucher/audit",
    method: "post",
    params: {
      ids,
    },
  });
};

export const auditBack = (ids) => {
  return request({
    url: "/api/ni/fin/voucher/auditBack",
    method: "post",
    params: {
      ids,
    },
  });
};

export const overrule = (ids) => {
  return request({
    url: "/api/ni/fin/voucher/overrule",
    method: "post",
    params: {
      ids,
    },
  });
};

export const posting = (ids) => {
  return request({
    url: "/api/ni/fin/voucher/posting",
    method: "post",
    params: {
      ids,
    },
  });
};

export const removal = (id, remark) => {
  return request({
    url: "/api/ni/fin/voucher/removal",
    method: "post",
    params: {
      id,
      remark,
    },
  });
};
export const toVoid = (ids) => {
  return request({
    url: "/api/ni/fin/voucher/toVoid",
    method: "post",
    params: {
      ids,
    },
  });
};

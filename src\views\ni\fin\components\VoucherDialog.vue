<script>
import { dateFormat } from "@/util/date";
import { mapGetters } from "vuex";
import { add, update } from "@/api/ni/fin/voucher";

export default {
  name: "VoucherDialog",
  props: {},
  data() {
    return {
      visible: false,
      option: {
        detail: false,
        submitText: "保存草稿",
        size: "mini",
        span: 8,
        emptyBtn: false,
        column: [
          {
            label: "凭证字",
            prop: "type",
            type: "select",
            dicData: [
              {
                label: "记",
                value: "记",
              },
              {
                label: "收",
                value: "收",
              },
              {
                label: "付",
                value: "付",
              },
              {
                label: "转",
                value: "转",
              },
            ],
            clearable: false,
            rules: [
              {
                required: true,
                message: "请选择凭证字",
                trigger: "blur",
              },
            ],
          },
          {
            label: "凭证日期",
            prop: "date",
            type: "date",
            placeholder: " ",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            clearable: false,
            rules: [
              {
                required: true,
                message: "请选择凭证日期",
                trigger: "blur",
              },
            ],
          },
          {
            label: "凭证号",
            prop: "serialNo",
            placeholder: "系统自动生成",
          },
          {
            label: "制单人",
            prop: "createUserName",
            disabled: true,
          },
          {
            label: "状态",
            prop: "status",
            type: "select",
            dataType: "number",
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_fin_voucher_status",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            disabled: true,
          },
          {
            label: "摘要",
            prop: "summary",
            span: 24,
            type: "textarea",
            minRows: 2,
            rules: [
              {
                required: true,
                message: "请输入摘要",
                trigger: "blur",
              },
            ],
          },
          {
            label: "",
            labelWidth: 0,
            prop: "details",
            type: "dynamic",
            span: 24,
            children: {
              showSummary: true,
              sumColumnList: [
                {
                  name: "borrowAmount",
                  type: "sum",
                  decimals: 2,
                },
                {
                  name: "loanAmount",
                  type: "sum",
                  decimals: 2,
                },
              ],
              align: "center",
              headerAlign: "center",
              rowAdd: (done) => {
                done({ borrowAmount: 0, loanAmount: 0 });
              },
              rowDel: (row, done) => {
                done();
              },
              column: [
                {
                  label: "摘要",
                  prop: "summary",
                  type: "textarea",
                  placeholder: " ",
                  minRows: 1,
                  rules: [
                    {
                      required: true,
                      message: "请输入摘要",
                      trigger: "blur",
                    },
                  ],
                },
                {
                  label: "会计科目",
                  prop: "accountingId",
                  type: "tree",
                  parent: false,
                  clearable: true,
                  dicUrl: "/api/ni/fin/accounting/submitTree",
                  props: {
                    label: "name",
                    value: "id",
                    desc: "code",
                  },
                  placeholder: " ",
                  typeformat(item) {
                    return `${item.code}-${item.name}`;
                  },
                  rules: [
                    {
                      required: true,
                      message: "请选择会计科目",
                      trigger: "blur",
                    },
                  ],
                },
                {
                  label: "借方金额",
                  prop: "borrowAmount",
                  type: "number",
                  controls: false,
                  precision: 2,
                  placeholder: " ",
                  rules: [
                    {
                      required: true,
                      message: "请输入借方金额",
                      trigger: "blur",
                    },
                  ],
                },
                {
                  label: "贷方金额",
                  prop: "loanAmount",
                  type: "number",
                  controls: false,
                  precision: 2,
                  placeholder: " ",
                  rules: [
                    {
                      required: true,
                      message: "请输入贷方金额",
                      trigger: "blur",
                    },
                  ],
                },
              ],
            },
          },
        ],
      },
      form: {},
      title: "记账凭证",
      sourceSystemKeyValue: {},
      mode: "add",
    };
  },
  computed: {
    ...mapGetters(["userInfo"]),
  },
  created() {
    this.$http
      .get(
        "/api/blade-system/dict-biz/dictionary?code=ni_fin_voucher_source_system"
      )
      .then((res) => {
        this.sourceSystemKeyValue = res.data.data.reduce((acc, cur) => {
          acc[cur.dictKey] = cur.dictValue;
          return acc;
        }, {});
      });
  },
  methods: {
    onAdd() {
      this.option.detail = false;
      this.mode = "add";
      this.title = `${this.sourceSystemKeyValue["9"]}凭证`;
      this.form = {
        date: dateFormat(new Date(), "yyyy-MM-dd"),
        type: "付",
        attachNum: 0,
        sourceSystem: "9",
        createUserName: this.userInfo.user_name,
        status: 1,
        details: [{ borrowAmount: 0, loanAmount: 0 }],
      };
      this.visible = true;
    },
    onEdit(form) {
      this.option.detail = false;
      this.mode = "edit";
      this.title = `${this.sourceSystemKeyValue["9"]}凭证`;
      this.form = {
        ...form,
      };
      this.visible = true;
    },
    handleClose() {
      this.visible = false;
    },
    onShow(form) {
      this.mode = "view";
      this.title = `${this.sourceSystemKeyValue[form.sourceSystem]}凭证`;
      this.form = {
        ...form,
      };
      this.option.detail = true;
      this.visible = true;
    },
    handleSubmit(form, done) {
      let res;
      if (this.mode === "add") res = add(form);
      else if (this.mode === "edit") res = update(form);
      if (!res) return;
      res
        .then((res) => {
          this.$emit("confirm", res);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.handleClose();
        })
        .finally(() => {
          done();
        });
    },
    handleAudit() {
      this.form.status = 2;
      this.$refs.form.submit();
    },
  },
};
</script>

<template>
  <el-dialog
    :title="title"
    append-to-body
    :visible.sync="visible"
    width="1150px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <avue-form
      ref="form"
      :option="option"
      v-model="form"
      @submit="handleSubmit"
    >
      <template #menuForm="{ size }">
        <el-button type="warning" :size="size" @click="handleAudit"
          >提交审核
        </el-button>
      </template>
    </avue-form>
  </el-dialog>
</template>

<style scoped></style>

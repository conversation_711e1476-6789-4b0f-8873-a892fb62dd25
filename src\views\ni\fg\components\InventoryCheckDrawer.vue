<script>
import { getList as getSnapshotList } from "@/api/ni/fg/fgInventoryCheckSnapshot";
import {
  add,
  getList as getItemList,
  remove,
  update,
} from "@/api/ni/fg/fgInventoryCheckItem";
import { finish, getDetail } from "@/api/ni/fg/fgInventoryCheck";
import { mapGetters } from "vuex";

export default {
  name: "InventoryCheckDrawer",
  data() {
    return {
      check: {},
      title: "",
      visible: false,
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        cellBtn: true,
        showSummary: true,
        sumColumnList: [
          {
            name: "actualQty",
            type: "sum",
          },
        ],
        addBtn: false,
        height: "auto",
        size: "mini",
        searchSize: "mini",
        align: "center",
        searchIndex: 3,
        searchIcon: true,
        searchEnter: true,
        calcHeight: 30,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        selection: true,
        dialogClickModal: false,
        column: [
          {
            label: "存货编码",
            prop: "skuId",
            search: true,
            type: "select",
            remote: true,
            dicUrl: `/api/ni/fg/inventory-check-snapshot/list?queryMaterialCodeOrSkuId={{key}}`,
            props: {
              label: "materialCode",
              value: "skuId",
              desc: "specText",
            },
            dicFormatter: (data) => {
              return data.data.records;
            },
            clearable: true,
            filterable: true,
            placeholder: " ",
            minWidth: 100,
            overHidden: true,
            cell: true,
          },
          {
            label: "规格",
            prop: "specText",
            search: true,
            placeholder: " ",
            display: false,
            overHidden: true,
            minWidth: 100,
          },
          {
            label: "外包装",
            prop: "packageText",
            search: true,
            placeholder: " ",
            display: false,
            overHidden: true,
            minWidth: 115,
          },
          {
            label: "内包装",
            prop: "innerPackageText",
            placeholder: " ",
            display: false,
            overHidden: true,
            minWidth: 115,
          },
          {
            label: "快照库存",
            prop: "systemQty",
            placeholder: " ",
            minWidth: 100,
            overHidden: true,
          },
          {
            label: "盘点数量",
            prop: "actualQty",
            placeholder: " ",
            type: "number",
            minWidth: 100,
            cell: true,
            rules: [
              {
                required: true,
                message: "请输入数量",
                trigger: "blur",
              },
            ],
          },
          {
            label: "差值",
            prop: "variance",
            cell: false,
            width: 80,
          },
          {
            label: "备注",
            prop: "remark",
            type: "textarea",
            minRows: 1,
            minWidth: 100,
            span: 24,
            cell: true,
          },
        ],
      },
      data: [],
      query: {},
      form: {},
      row: {},
      status: "all",
      freeze: {
        visible: false,
        form: {},
        option: {
          size: "mini",
          span: 24,
          column: [
            {
              label: "冻结原因",
              prop: "reason",
              placeholder: " ",
            },
            {
              label: "冻结类型",
              prop: "type",
              type: "select",
              dicUrl:
                "/api/blade-system/dict-biz/dictionary?code=ni_fg_inventory_freeze_type",
              props: {
                label: "dictValue",
                value: "dictKey",
              },
              placeholder: " ",

              rules: [
                {
                  required: true,
                  message: "请选择冻结类型",
                  trigger: "blur",
                },
              ],
            },
            {
              label: "备注",
              prop: "remark",
              placeholder: " ",
              type: "textarea",
              minRows: 2,
            },
          ],
        },
      },
      excelBox: false,
      excelOption: {
        size: "mini",
        searchSize: "mini",
        submitBtn: false,
        emptyBtn: false,
        column: [
          {
            label: "数据上传",
            prop: "excelFile",
            type: "upload",
            drag: true,
            loadText: "模板上传中，请稍等",
            span: 24,
            data: {},
            propsHttp: {
              res: "data",
            },
            tip: "请上传 .xls,.xlsx 标准格式文件",
            action: "/api/ni/fg/inventory-check-item/import",
          },
          {
            label: "数据覆盖",
            prop: "isCovered",
            type: "switch",
            align: "center",
            width: 80,
            dicData: [
              {
                label: "否",
                value: 0,
              },
              {
                label: "是",
                value: 1,
              },
            ],
            value: 0,
            slot: true,
            rules: [
              {
                required: true,
                message: "请选择是否覆盖",
                trigger: "blur",
              },
            ],
          },
          {
            label: "模板下载",
            prop: "excelTemplate",
            formslot: true,
            span: 24,
          },
        ],
      },
      qualityLevelDictKeyValue: {},
      excelForm: {},
      exportColumn: [
        {
          label: "存货编码",
          prop: "materialCode",
          placeholder: " ",
          width: 110,
          overHidden: true,
        },
        {
          label: "规格",
          prop: "specText",
          placeholder: " ",
          display: false,
          overHidden: true,
          width: 120,
        },
        {
          label: "外包装",
          prop: "packageText",
          placeholder: " ",
          display: false,
          overHidden: true,
          width: 115,
        },
        {
          label: "内包装",
          prop: "innerPackageText",
          placeholder: " ",
          display: false,
          overHidden: true,
          width: 115,
        },
        {
          label: "质量",
          prop: "qualityLevel",
          width: 90,
        },
        {
          label: "箱数",
          prop: "systemQty",
          minWidth: 80,
        },
        {
          label: "盘点数量",
          prop: "actualQty",
          minWidth: 80,
        },
        {
          label: "备注",
          prop: "remark",
          minWidth: 80,
        },
      ],
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        delBtn: this.vaildData(this.check.status < 3, false),
        editBtn: this.vaildData(this.check.status < 3, false),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  watch: {
    "excelForm.isCovered"() {
      if (this.excelForm.isCovered !== "") {
        const column = this.findObject(this.excelOption.column, "excelFile");
        column.action = `/api/ni/fg/inventory-check-item/import-check?checkId=${this.check.id}&isCovered=${this.excelForm.isCovered}`;
      }
    },
  },
  created() {
    this.$http
      .get(
        "/api/blade-system/dict-biz/dictionary?code=ni_product_sku_quality_level"
      )
      .then((res) => {
        const dict = res.data.data;
        this.qualityLevelDictKeyValue = dict.reduce((acc, cur) => {
          acc[cur.dictKey] = cur.dictValue;
          return acc;
        }, {});
      });
  },
  methods: {
    onCheck(row) {
      // if (![1, 2, 3].includes(row.status)) {
      //   this.$message.error('盘点状态错误，请重新选择')
      //   return
      // }
      this.check = row;
      this.title = `[${row.serialNo}]${row.title}-${row.depotName}`;
      this.visible = true;
    },
    handleImport() {
      const column = this.findObject(this.excelOption.column, "excelFile");
      column.action = `/api/ni/fg/inventory-check-item/import-check?checkId=${this.check.id}`;
      this.excelBox = true;
    },
    handleTemplate() {
      this.$Export.excel({
        title: "盘点数据导入模板",
        columns: this.exportColumn,
        data: [],
      });
    },
    handleExport() {
      this.$confirm("是否导出当前筛选的所有数据？", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const q = {
          ...this.query,
          descs: "id",
          checkId: this.check.id,
        };
        const res = await getItemList(1, 1000000, q);
        this.$Export.excel({
          title: "盘点明细",
          columns: this.exportColumn,
          data: res.data.data.records.map((item) => {
            return {
              ...item,
              qualityLevel: this.qualityLevelDictKeyValue[item.qualityLevel],
            };
          }),
        });
      });
    },
    handleAdd() {
      this.data.splice(0, 0, {
        depotId: this.check.depotId,
        checkId: this.check.id,
        status: 1,
        skuIdOptions: [],
        skuIdLoading: false,
        $cellEdit: true,
      });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择要删除的数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    handleClose(done) {
      this.visible = false;
      if (done && typeof done == "function") done();
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    searchReset() {
      this.query = {
        depotId: this.row.depotId,
        skuId: this.row.skuId,
      };
      this.page.currentPage = 1;
      this.onLoad(this.page);
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      const q = {
        ...params,
        ...this.query,
        ...this.params,
        checkId: this.check.id,
      };
      if (!q.descs && !q.ascs) q.descs = "id";
      getItemList(page.currentPage, page.pageSize, q).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.selectionClear();
        this.loading = false;
      });
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    cellStyle({ row, column }) {
      if ("status" === column.columnKey && row.status === 2) {
        return {
          backgroundColor: "#409EFF",
          color: "#fff",
        };
      }
      if ("variance" === column.columnKey && row.variance < 0) {
        return {
          backgroundColor: "#F56C6C",
          color: "#fff",
        };
      } else if ("variance" === column.columnKey && row.variance > 0) {
        return {
          backgroundColor: "#67C23A",
          color: "#fff",
        };
      }
    },
    rowSave(form, done, loading) {
      add(form).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          console.log(error);
        }
      );
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    rowUpdate(form, index, done, loading) {
      update(form).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          console.log(error);
        }
      );
    },
    skuIdRemoteMethod(query, row) {
      if (query !== "") {
        row.skuIdLoading = true;
        getSnapshotList(1, 20, {
          queryMaterialCodeOrSkuId: query,
          depotId: this.check.depotId,
          checkId: this.check.id,
        }).then((res) => {
          row.skuIdLoading = false;
          const data = res.data.data;
          row.skuIdOptions = data.records;
        });
      } else {
        row.skuIdOptions = [];
      }
    },
    async rowSkuIdChange(val, row) {
      if (row.skuIdOptions && row.skuIdOptions.length > 0) {
        const selectedItem = row.skuIdOptions.find(
          (item) => item.skuId === val
        );
        if (selectedItem) {
          row.specText = selectedItem.specText;
          row.packageId = selectedItem.packageId;
          row.packageText = selectedItem.packageText;
          row.innerPackageId = selectedItem.innerPackageId;
          row.innerPackageText = selectedItem.innerPackageText;
          row.systemQty = selectedItem.num;
        }
      }
    },
    uploadAfter(res, done, loading, column) {
      this.excelBox = false;
      this.refreshChange();
      done();
      if (res) {
        this.$confirm(res, "提示", {
          confirmButtonText: "确定",
          type: "warning",
          center: true,
        });
      }
    },
    handleFinish() {
      this.$confirm("该操作将完成该盘点，完成后无法再调整盘点结果，是否继续?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return finish(this.check.id);
        })
        .then(() => {
          getDetail(this.check.id).then((res) => {
            this.check = res.data.data;
          });
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
  },
};
</script>

<template>
  <el-drawer
    :title="title"
    :visible.sync="visible"
    direction="rtl"
    size="80%"
    append-to-body
    destroy-on-close
  >
    <template #title>
      <div>
        <span>{{ title }}</span>
        -总箱数：{{ Number(check.systemQty).toLocaleString("zh-CN") }}/<span
          :style="
            check.systemQty !== check.actualQty ? { color: '#F56C6C' } : {}
          "
          >{{ Number(check.actualQty).toLocaleString("zh-CN") }}</span
        >
        <el-divider direction="vertical" />
        <el-button
          type="success"
          size="mini"
          icon="el-icon-success"
          plain
          v-if="[1, 2].includes(check.status)"
          @click="handleFinish"
          >完成盘点
        </el-button>
      </div>
    </template>
    <div style="margin: 20px 20px">
      <avue-crud
        ref="crud"
        v-if="visible"
        :option="option"
        :table-loading="loading"
        :data="data"
        :page.sync="page"
        v-model="form"
        :permission="permissionList"
        @row-update="rowUpdate"
        @row-save="rowSave"
        @row-del="rowDel"
        @search-change="searchChange"
        @search-reset="searchReset"
        @selection-change="selectionList = $event"
        @current-change="page.currentPage = $event"
        @size-change="page.pageSize = $event"
        @refresh-change="refreshChange"
        @on-load="onLoad"
        :cell-style="cellStyle"
      >
        <template #skuId="{ row }">
          <span>{{ row.materialCode }}</span>
        </template>
        <template #skuIdForm="{ row, size, disabled, index }">
          <el-select
            :size="size"
            :disabled="disabled"
            v-model="row.skuId"
            filterable
            remote
            placeholder=" "
            :remote-method="
              (query) => {
                skuIdRemoteMethod(query, row);
              }
            "
            :loading="row.skuIdLoading"
            @change="rowSkuIdChange($event, row)"
          >
            <el-option
              v-for="item in row.skuIdOptions"
              :key="item.id"
              :label="item.materialCode"
              :value="item.skuId"
            >
            </el-option>
          </el-select>
        </template>
        <template #menuLeft>
          <!-- 登记-->
          <el-button
            type="primary"
            icon="el-icon-plus"
            size="mini"
            v-if="check.status < 3"
            @click="handleAdd"
          >
            登记
          </el-button>
          <el-button
            type="warning"
            size="mini"
            icon="el-icon-upload2"
            plain
            v-if="check.status < 3"
            @click="handleImport"
            >导入
          </el-button>
          <el-button
            type="success"
            size="mini"
            icon="el-icon-download"
            plain
            @click="handleExport"
            >导出
          </el-button>
          <el-button
            type="danger"
            size="mini"
            icon="el-icon-delete"
            plain
            v-if="check.status < 3"
            @click="handleDelete"
            >删除
          </el-button>
        </template>
      </avue-crud>
      <el-dialog
        title="盘点数据导入"
        append-to-body
        :visible.sync="excelBox"
        width="555px"
      >
        <avue-form
          :option="excelOption"
          v-model="excelForm"
          :upload-after="uploadAfter"
        >
          <template slot="excelTemplate">
            <el-button type="primary" @click="handleTemplate">
              点击下载<i class="el-icon-download el-icon--right"></i>
            </el-button>
          </template>
        </avue-form>
      </el-dialog>
    </div>
  </el-drawer>
</template>

<style scoped></style>

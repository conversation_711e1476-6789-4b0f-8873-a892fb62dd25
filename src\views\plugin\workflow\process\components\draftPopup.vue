<template>
    <el-dialog
      title="草稿箱"
      :visible.sync="visible"
      :close-on-click-modal="false"
      append-to-body
      class="fade-in"
      width="1010px"
      @close="close"
    >
      <el-table
        highlight-current-row
        tooltip-effect="dark"
        height="600"
        border
        size="mini"
        ref="multipleTable"
        :data="draftList"
        @current-change="handleCurrentChange"
      >
        <el-table-column type="index" width="60" :index="i => i + 1" label="序号" />
        <el-table-column width="220" prop="draftTitle" label="草稿标题" />
        <el-table-column width="200" prop="createTime" label="创建时间" />
        <el-table-column width="320" prop="formData" label="草稿内容" show-overflow-tooltip/>
        <el-table-column width="80" prop="sameVersion" label="流程版本是否匹配" align="center" :formatter="formatSameVersion"/>
        <el-table-column width="130" label="操作">
          <template slot-scope="scope">
            <el-button type="text" icon="el-icon-delete" @click="deleteDraft(scope.row.id)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          size="mini"
          @click="confirmSelection"
          :disabled="!selectedDraftId">确 定</el-button>
          <el-button  size="mini" @click="close">关 闭</el-button>
      </span>
    </el-dialog>
  </template>

  <script>
  export default {
    name: 'DraftPopup',
    props: {
      visible: {
        type: Boolean,
        default: false
      },
      draftList: {
        type: Array,
        default: () => []
      }
    },
    data() {
      return {
        selectedDraftId: null,
        tableData: [
          { sameVersion: true },
          { sameVersion: false },
        ]
      };
    },
    created() {
    },
    methods: {
      handleCurrentChange(row) {
        this.selectedDraftId = row ? row.id : null;
      },
      // 确认选择
      confirmSelection() {
        if (this.selectedDraftId) {
          const selectedDraft = this.draftList.find(draft => draft.id === this.selectedDraftId);
          this.$emit('select', selectedDraft);
          if(selectedDraft.sameVersion){
            this.close();
          }
        }
      },
      // 删除草稿
      deleteDraft(id) {
        this.$emit('delete', id);
      },
      close() {
        this.$emit('update:visible', false);
      },
      formatSameVersion(row, column) {
        return row.sameVersion? '是' : '否';
      }
    }
  };
  </script>

  <style scoped>
  /* 添加自定义动画 */
  @keyframes fadeIn {
    from { opacity: 0; transform: scale(0.95); }
    to { opacity: 1; transform: scale(1); }
  }

  .fade-in {
    animation: fadeIn 0.2s ease-out forwards;
  }
  </style>

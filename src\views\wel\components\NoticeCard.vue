<template>
  <basic-container class="notice-card-wrapper">
    <span>通知公告</span>
    <el-button
      style="float: right; padding: 3px 0"
      type="text"
      @click="moreClick"
      >更多
    </el-button>
    <el-divider />
    <el-table
      :data="data"
      size="mini"
      fit
      style="width: 100%"
      height="250"
      :show-header="false"
    >
      <el-table-column
        prop="title"
        label="通知标题"
        align="left"
        show-overflow-tooltip
        min-width="120"
      >
        <template slot-scope="scope">
          <span class="notice-title" @click="handleRowClick(scope.row)">
            <el-tag
              v-if="scope.row.isManagerResponse === 1"
              size="mini"
              class="notice-tag manager-response pulse"
            >
              需回复
            </el-tag>
            {{ scope.row.title }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="releaseTime" label="通知日期" align="right">
        <template slot-scope="scope">
          <span v-if="scope.row.releaseTime">{{
            scope.row.releaseTime.substring(0, 10)
          }}</span>
        </template>
      </el-table-column>
    </el-table>
    <notice-detail ref="noticeDetailRef" @read="onLoad" />
  </basic-container>
</template>

<script>
import { getUserNoticePage } from "@/api/desk/notice";
import NoticeDetail from "@/views/desk/components/NoticeDetail.vue";

export default {
  name: "NoticeCard",
  components: { NoticeDetail },
  props: {
    title: String,
  },
  data() {
    return {
      form: {},
      query: {},
      data: [],
      loading: true,
      page: {
        pageSize: 5,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        column: [
          {
            label: "通知标题",
            prop: "title",
            span: 24,
          },
          {
            label: "通知日期",
            prop: "releaseTime",
            type: "datetime",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: true,
          },
          {
            label: "阅读状态",
            prop: "isRead",
            type: "select",
            dataType: "number",
            display: false,
            disabled: true,
          },
        ],
      },
    };
  },
  mounted() {
    this.onLoad();
  },
  methods: {
    handleRowClick(row) {
      this.$refs.noticeDetailRef.onView(row);
    },
    moreClick() {
      this.$router.push("/desk/notice-user");
    },
    onLoad(params = {}) {
      this.loading = true;
      const q = {
        ...params,
        ...this.query,
        isRead: 0,
      };
      getUserNoticePage(this.page.currentPage, this.page.pageSize, q).then(
        (res) => {
          const data = res.data.data;
          this.page.total = data.total;
          data.records.forEach((row) => {
            row.isRead =
              row.isRead === 0 ? "未读" : row.isRead === 1 ? "已读" : "";
          });
          this.data = data.records;
          this.loading = false;
        }
      );
    },
  },
};
</script>

<style lang="scss" scoped>
.notice-title {
  cursor: pointer;
  display: inline-block;
  transition: color 0.2s ease;

  &:hover {
    color: #167c46;
    text-decoration: underline;
  }
}
.notice-tag {
  margin-right: 8px;
  padding: 0 6px;
  border-radius: 4px;
  font-size: 12px;

  &.un-read {
    background: #f56c6c;
    color: #fff;
  }

  &.read {
    background: linear-gradient(45deg, #409eff, #66b1ff);
    color: #fff;
    font-weight: bold;
  }

  &.manager-response {
    background: linear-gradient(45deg, #67c23a, #8de471);
    color: #fff;
    font-weight: bold;
    @media (prefers-reduced-motion: no-preference) {
      animation: pulse 2s infinite;
    }
  }

  &.pulse {
    animation: pulse 2s infinite;
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.1);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 0.8;
  }
}
</style>

import request from "@/router/axios";

export const getList = (current, size, params, deptId) => {
  return request({
    url: "/api/ni/fin/loan/list",
    method: "get",
    params: {
      ...params,
      deptId,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/ni/fin/loan/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const remove = (ids) => {
  return request({
    url: "/api/ni/fin/loan/remove",
    method: "post",
    params: {
      ids,
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/ni/fin/loan/save",
    method: "post",
    data: row,
  });
};

export const update = (row) => {
  return request({
    url: "/api/ni/fin/loan/update",
    method: "post",
    data: row,
  });
};
export const submit = (row, processDefKey) => {
  return request({
    url: "/api/ni/fin/loan/apply",
    method: "post",
    params: {
      processDefKey,
    },
    data: row,
  });
};

export const getDetailByProcessInsId = (processInsId) => {
  return request({
    url: "/api/ni/fin/loan/detail",
    method: "get",
    params: {
      processInsId,
    },
  });
};

export const updateLedgerId = (id, ledgerId) => {
  return request({
    url: "/api/ni/fin/loan/updateLedgerId",
    method: "post",
    params: {
      id,
      ledgerId,
    },
  });
};

export const payLoan = (row) => {
  return request({
    url: "/api/ni/fin/loan/payLoan",
    method: "post",
    data: row,
  });
};

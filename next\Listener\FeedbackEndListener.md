```java
// 定义包路径，属于问题反馈模块的监听器组件
package com.natergy.ni.feedback.listener;

// 导入MyBatis-Plus的查询条件构造器
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
// 导入问题反馈相关实体类和VO
import com.natergy.ni.feedback.entity.FeedbackEntity;
import com.natergy.ni.feedback.entity.FeedbackSolvingRecordEntity;
import com.natergy.ni.feedback.enums.FeedbackStatusEnum;
import com.natergy.ni.feedback.service.IFeedbackService;
import com.natergy.ni.feedback.vo.FeedbackSolvingRecordVO;
// 导入Lombok日志注解
import lombok.extern.slf4j.Slf4j;
// 导入Apache字符串工具类
import org.apache.commons.lang3.StringUtils;
// 导入Flowable工作流执行监听器相关类
import org.flowable.engine.delegate.DelegateExecution;
// 导入Blade框架的缓存工具类和系统实体
import org.springblade.common.cache.SysCache;
import org.springblade.common.cache.UserCache;
import org.springblade.modules.system.entity.User;
// 导入Spring组件注解和事务注解
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

// 导入Java时间处理和集合工具类
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 问题负责人完成任务
 * （当问题负责人完成处理任务后触发的监听器）
 *
 * <AUTHOR>  // 作者标识
 * @since 2025-04-01  // 类创建日期
 * 为了兼容已经发起的流程，后期可以删除掉  // 备注：该类为兼容历史流程而存在，未来可移除
 */
// @Slf4j：Lombok注解，自动生成日志对象，用于打印日志
@Slf4j
// @Component("FeedbackEndListener")：Spring注解，将类注册为组件，名称为"FeedbackEndListener"
// 供Flowable工作流引擎通过该名称调用此监听器
@Component("FeedbackEndListener")
// 实现Flowable的ExecutionListener接口，作为流程执行监听器
public class FeedbackEndListener implements org.flowable.engine.delegate.ExecutionListener {

	// 注入问题反馈服务，用于操作问题反馈数据
	private final IFeedbackService feedbackService;

	// 定义字符串分隔符（用于多值字段的拆分和拼接，如逗号）
	private final static String SEPARATOR = ",";

	// 构造函数注入问题反馈服务
	public FeedbackEndListener(IFeedbackService feedbackService) {
		this.feedbackService = feedbackService;
	}

	// @Transactional(rollbackFor = Exception.class)：声明事务，方法执行中发生任何异常时回滚
	@Transactional(rollbackFor = Exception.class)
	// 实现监听器的notify方法，当流程执行到该监听器绑定的节点时触发
	@Override
	public void notify(DelegateExecution execution) {
		// 获取流程实例ID（用于关联对应的问题反馈实体）
		String processInstanceId = execution.getProcessInstanceId();

		// 根据流程实例ID查询对应的问题反馈实体
		FeedbackEntity feedbackEntity = feedbackService.getOne(
			new LambdaQueryWrapper<FeedbackEntity>()
				.eq(FeedbackEntity::getProcessInstanceId, processInstanceId)
		);

		// 从流程变量中获取"claimList"（问题认领记录列表，可能是VO或Map类型）
		Object claimList = execution.getVariable("claimList");
		List<?> list = (List<?>) claimList;  // 转换为泛型列表

		// 检查列表元素类型，获取最新的解决日期（根据元素类型调用不同方法）
		LocalDate latestDate = checkGenericType(list) ? getLatestDateByVo(claimList) : getLatestDateByMap(claimList);

		// 如果存在最新解决日期，更新到问题反馈实体的最终解决时间字段
		if (Objects.nonNull(latestDate)) {
			feedbackEntity.setFinalResolutionTime(latestDate);
		}

		// 从流程变量中获取"responsibility"（负责人ID列表字符串，用逗号分隔）
		Object responsibility = execution.getVariable("responsibility");
		String responsibilityStr = Optional.ofNullable(responsibility)
			.map(String::valueOf)  // 转换为字符串
			.orElse(StringUtils.EMPTY);  // 若为null则默认空字符串

		// 根据认领记录列表是否为空，设置问题状态：
		// - 列表为空 → "无需处理"
		// - 列表非空 → "问题已解决"
		feedbackEntity.setStatus(
			list.isEmpty() 
				? FeedbackStatusEnum.NO_NEED_TO_HANDLE.getValue() 
				: FeedbackStatusEnum.ISSUE_HAS_RESOLVED.getValue()
		);

		// 处理负责人对应的部门名称列表（仅当负责人ID不为空时）
		List<String> deptNameCollect;
		if (StringUtils.isNotBlank(responsibilityStr)) {
			// 1. 按分隔符拆分负责人ID字符串为数组
			// 2. 遍历每个ID，通过UserCache获取用户信息，再通过SysCache获取用户所属部门名称
			// 3. 收集所有部门名称为列表
			deptNameCollect = Arrays.stream(responsibilityStr.split(SEPARATOR))
				.map(item -> {
					User user = UserCache.getUser(Long.valueOf(item));  // 从缓存获取用户信息
					return SysCache.getDeptName(Long.valueOf(user.getDeptId()));  // 从缓存获取部门名称
				})
				.collect(Collectors.toList());

			// 将部门名称列表用分隔符拼接为字符串，更新到问题反馈实体
			feedbackEntity.setDeptNames(StringUtils.join(deptNameCollect, SEPARATOR));
		}

		// 保存或更新问题反馈实体（若已存在则更新，否则新增）
		feedbackService.saveOrUpdate(feedbackEntity);
	}

	/**
	 * 检查列表元素的泛型类型
	 * @param list 待检查的列表
	 * @return 如果列表非空且第一个元素是FeedbackSolvingRecordVO类型，返回true；否则返回false
	 */
	private static boolean checkGenericType(List<?> list) {
		if (!list.isEmpty()) {
			Object firstElement = list.get(0);
			return firstElement instanceof FeedbackSolvingRecordVO;
		}
		return false;
	}

	/**
	 * 当列表元素是LinkedHashMap时，提取最新的解决日期
	 * （适用于流程变量序列化后可能转为Map的场景，如历史流程兼容）
	 * @param claimList 认领记录列表（Map类型）
	 * @return 最新的解决日期（LocalDate）
	 */
	private static LocalDate getLatestDateByMap(Object claimList) {
		// 将claimList转换为LinkedHashMap列表（若为null则返回空列表）
		List<LinkedHashMap<String, Object>> list = Optional.ofNullable(claimList)
			.map(obj -> (List<LinkedHashMap<String, Object>>) obj)
			.orElse(Collections.EMPTY_LIST);

		// 定义日期格式化器（匹配"yyyy-MM-dd"格式）
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
		LocalDate latestDate = null;

		// 遍历每条记录，解析"resolveDate"字段并找到最新日期
		for (LinkedHashMap<String, Object> record : list) {
			Object dateObj = record.get("resolveDate");  // 获取解决日期字段值
			if (dateObj != null) {
				try {
					LocalDate currentDate = LocalDate.parse(dateObj.toString(), formatter);
					// 比较并更新最新日期（取最晚的日期）
					if (latestDate == null || currentDate.isAfter(latestDate)) {
						latestDate = currentDate;
					}
				} catch (Exception e) {
					// 处理日期解析异常（如格式不匹配）
					e.printStackTrace();
				}
			}
		}
		return latestDate;
	}

	/**
	 * 当列表元素是FeedbackSolvingRecordVO时，提取最新的解决日期
	 * @param claimList 认领记录列表（VO类型）
	 * @return 最新的解决日期（LocalDate）
	 */
	private static LocalDate getLatestDateByVo(Object claimList) {
		// 将claimList转换为FeedbackSolvingRecordVO列表（若为null则返回空列表）
		List<FeedbackSolvingRecordVO> list = Optional.ofNullable(claimList)
			.map(obj -> (List<FeedbackSolvingRecordVO>) obj)
			.orElse(Collections.EMPTY_LIST);

		// 筛选出解决日期不为空的记录，按日期倒序排序后取第一条（即最新日期）
		Optional<FeedbackSolvingRecordVO> finalSolvingRecord = list.stream()
			.filter(item -> Objects.nonNull(item.getResolveDate()))  // 过滤空日期
			.min((r1, r2) -> r2.getResolveDate().compareTo(r1.getResolveDate()));  // 倒序排序（取最大日期）

		// 返回最新日期（若存在）
		return finalSolvingRecord.map(FeedbackSolvingRecordEntity::getResolveDate).orElse(null);
	}
}
```

### 类功能说明

该类是基于**Flowable 工作流引擎**的流程执行监听器，命名为`FeedbackEndListener`，主要作用是在**问题负责人完成处理任务后**触发，自动更新问题反馈的最终解决时间、状态和部门信息。与`FeedbackEnd2Listener`相比，该类是为了兼容历史已发起的流程而存在，未来可移除。

#### 核心业务场景

当问题的负责人完成处理后，工作流执行到该监听器节点，自动汇总处理结果（如最新解决日期），更新问题状态为 “问题已解决” 或 “无需处理”，并记录负责人所属部门信息，确保流程与业务数据同步。

#### 与 FeedbackEnd2Listener 的关键差异

| 对比项                   | FeedbackEndListener                | FeedbackEnd2Listener        |
| ------------------------ | ---------------------------------- | --------------------------- |
| 问题状态设置（非空列表） | `ISSUE_HAS_RESOLVED`（问题已解决） | `TO_BE_CONFIRMED`（待确认） |
| 用途                     | 兼容历史流程                       | 新流程使用                  |
| 生命周期                 | 临时存在，未来可删除               | 长期使用                    |

#### 关键逻辑解析

1. **关联业务实体**：通过流程实例 ID 关联`FeedbackEntity`，实现工作流与业务数据的绑定。
2. **处理认领记录**：根据`claimList`的类型（VO 或 Map）提取最新解决日期，更新为问题的最终解决时间。
3. **更新状态与部门信息**：根据认领记录是否为空设置对应状态，并通过负责人 ID 获取部门名称列表，更新到实体中。

#### 技术特点

- **兼容性设计**：支持`FeedbackSolvingRecordVO`和`LinkedHashMap`两种数据类型，适配不同流程版本的变量格式。
- **缓存优化**：通过`UserCache`和`SysCache`获取用户与部门信息，减少数据库交互。
- **事务保障**：使用`@Transactional`确保数据更新的原子性，避免流程与业务数据不一致。

该类是系统迭代过程中兼容历史数据的过渡方案，核心逻辑与`FeedbackEnd2Listener`基本一致，仅状态设置存在差异，便于平滑迁移旧流程。
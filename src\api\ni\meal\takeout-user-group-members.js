import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/ni/meal/takeout/user-group-members/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/ni/meal/takeout/user-group-members/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/ni/meal/takeout/user-group-members/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/ni/meal/takeout/user-group-members/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/ni/meal/takeout/user-group-members/submit',
    method: 'post',
    data: row
  })
}


import request from "@/router/axios";

export const getPage = (current, size, params) => {
  return request({
    url: "/api/ni/fin/invoice/page",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};
export const getList = (params) => {
  return request({
    url: "/api/ni/fin/invoice/list",
    method: "get",
    params: {
      ...params,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/ni/fin/invoice/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const remove = (ids) => {
  return request({
    url: "/api/ni/fin/invoice/remove",
    method: "post",
    params: {
      ids,
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/ni/fin/invoice/save",
    method: "post",
    data: row,
  });
};

export const update = (row) => {
  return request({
    url: "/api/ni/fin/invoice/update",
    method: "post",
    data: row,
  });
};

export const submit = (ids) => {
  return request({
    url: "/api/ni/fin/invoice/submit",
    method: "post",
    params: {
      ids,
    },
  });
};

export const used = (ids) => {
  return request({
    url: "/api/ni/fin/invoice/used",
    method: "post",
    params: {
      ids,
    },
  });
};

export const back = (ids) => {
  return request({
    url: "/api/ni/fin/invoice/back",
    method: "post",
    params: {
      ids,
    },
  });
};

export const toVoid = (ids) => {
  return request({
    url: "/api/ni/fin/invoice/toVoid",
    method: "post",
    params: {
      ids,
    },
  });
};

export const orderLink = (link) => {
  return request({
    url: "/api/ni/fin/invoice/linkOrder",
    method: "post",
    data: link,
  });
};

export const soaLink = (soaId, ids) => {
  return request({
    url: "/api/ni/fin/invoice/linkSoa",
    method: "post",
    params: {
      soaId,
      ids,
    },
  });
};
export const cancelLink = (ids) => {
  return request({
    url: "/api/ni/fin/invoice/linkCancel",
    method: "post",
    params: {
      ids,
    },
  });
};
export const getUnLinkPage = (current, size, params) => {
  return request({
    url: "/api/ni/fin/invoice/unLinkPage",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};
export const contactLink = (contactId, ids) => {
  return request({
    url: "/api/ni/fin/invoice/linkContact",
    method: "post",
    params: {
      contactId,
      ids,
    },
  });
};
export const exportDetails = (ids) => {
  return request({
    url: "/api/ni/fin/invoice/exportDetails",
    method: "post",
    params: {
      ids,
    },
  });
};
export const validate = (ids) => {
  return request({
    url: "/api/ni/fin/invoice/validate",
    method: "post",
    params: {
      ids,
    },
  });
};

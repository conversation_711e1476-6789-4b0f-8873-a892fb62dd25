<template>
  <el-row :gutter="20">
    <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
      <el-card
        shadow="never"
        :body-style="{ padding: '10px 20px', width: '100%' }"
        :class="{
          'board-item': true,
        }"
      >
        <div slot="header" class="clearfix">
          <span>库存规格统计</span>
        </div>
        <div
          id="inventorySpecPieContainer"
          :style="{
            width: '100%',
            height: `${resizeTime * 326}px`,
            margin: '0 auto',
          }"
        ></div>
      </el-card>
    </el-col>
    <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
      <el-card
        shadow="never"
        :body-style="{ padding: '10px 20px', width: '100%' }"
        :class="{
          'board-item': true,
        }"
      >
        <div slot="header" class="clearfix">
          <span>库存产品统计</span>
        </div>
        <div
          id="inventorySkuPieContainer"
          :style="{
            width: '100%',
            height: `${resizeTime * 326}px`,
            margin: '0 auto',
          }"
        ></div>
      </el-card>
    </el-col>
  </el-row>
</template>

<script>
export default {
  props: {
    inventorySpecData: {
      type: Array,
      default: () => [],
    },
    inventorySkuData: {
      type: Array,
      default: () => [],
    },
  },

  data() {
    return {
      resizeTime: 1,
      inventorySpecPie: null,
      inventorySkuPie: null,
      inventoryPieOption: {
        tooltip: {
          trigger: "item",
          formatter: (params) => {
            const value = parseFloat(params.value);
            const formattedValue = isNaN(value)
              ? params.value
              : value.toLocaleString();
            return `${params.marker} ${params.name}<br/>${formattedValue}kg`;
          },
        },
        legend: {
          type: "scroll",
          orient: "vertical",
          right: 30,
          top: 20,
          bottom: 20,
        },
        series: [
          {
            name: "Access From",
            type: "pie",
            radius: "55%",
            center: ["40%", "50%"],
            itemStyle: {
              borderRadius: 10,
              borderColor: "#fff",
              borderWidth: 2,
            },
            emphasis: {
              label: {
                show: true,
                fontSize: 40,
                fontWeight: "bold",
              },
            },
            labelLine: {
              show: false,
            },
            data: [],
          },
        ],
      },
    };
  },
  beforeDestroy() {
    if (this.inventorySpecPie) {
      this.inventorySpecPie.dispose();
      this.inventorySpecPie = null;
    }
    if (this.inventorySkuPie) {
      this.inventorySkuPie.dispose();
      this.inventorySkuPie = null;
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.renderInit();
    });
    // 确保图表尺寸也得到更新
    window.addEventListener("resize", this.updateContainer, false);
  },
  watch: {
    inventorySpecData: {
      deep: true,
      handler(newVal) {
        if (newVal) {
          this.$nextTick(() => {
            this.renderSpec();
          });
        }
      },
    },
    inventorySkuData: {
      deep: true,
      handler(newVal) {
        if (newVal) {
          this.$nextTick(() => {
            this.renderSku();
          });
        }
      },
    },
  },
  methods: {
    renderInit() {
      if (!this.inventorySpecPie) {
        this.inventorySpecPieContainer = document.getElementById(
          "inventorySpecPieContainer"
        );
        this.inventorySpecPie = this.$echarts.init(
          this.inventorySpecPieContainer
        );
      }
      if (!this.inventorySkuPie) {
        this.inventorySkuPieContainer = document.getElementById(
          "inventorySkuPieContainer"
        );
        this.inventorySkuPie = this.$echarts.init(
          this.inventorySkuPieContainer
        );
      }
    },
    renderSpec() {
      this.renderInit();
      const data = this.inventorySpecData.map((item) => {
        return {
          name: item.key,
          value: item.value,
        };
      });
      const option = {
        tooltip: this.inventoryPieOption.tooltip,
        legend: this.inventoryPieOption.legend,
        series: [
          {
            type: "pie",
            radius: ["40%", "70%"],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: "center",
            },
            emphasis: {
              label: {
                show: true,
                fontSize: 25,
              },
            },
            labelLine: {
              show: false,
            },
            data,
          },
        ],
      };
      this.inventorySpecPie.setOption(option);
    },
    renderSku() {
      this.renderInit();
      const data = this.inventorySkuData.map((item) => {
        return {
          name: item.key,
          value: item.value,
        };
      });
      const option = {
        tooltip: this.inventoryPieOption.tooltip,
        legend: this.inventoryPieOption.legend,
        series: [
          {
            type: "pie",
            radius: ["40%", "70%"],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: "center",
            },
            emphasis: {
              label: {
                show: true,
                fontSize: 25,
              },
            },
            labelLine: {
              show: false,
            },
            data,
          },
        ],
      };
      this.inventorySkuPie.setOption(option);
    },
    updateContainer() {
      if (
        document.documentElement.clientWidth >= 1400 &&
        document.documentElement.clientWidth < 1920
      ) {
        this.resizeTime = (document.documentElement.clientWidth / 2080).toFixed(
          2
        );
      } else if (document.documentElement.clientWidth < 1080) {
        this.resizeTime = (document.documentElement.clientWidth / 1080).toFixed(
          2
        );
      } else {
        this.resizeTime = 1;
      }
      this.inventorySpecPie.resize({
        // 根据父容器的大小设置大小
        width: this.inventorySpecPieContainer.clientWidth,
        height: `${this.resizeTime * 326}px`,
      });
      this.inventorySkuPie.resize({
        width: this.inventorySkuPieContainer.clientWidth,
        height: `${this.resizeTime * 326}px`,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.board-item {
  padding: 8px;

  /deep/ .el-card__header {
    color: #303133;
    font-size: 20px;
    font-weight: 500;
    border-bottom: 0px;
    padding: 16px 18px !important;
  }
}
</style>

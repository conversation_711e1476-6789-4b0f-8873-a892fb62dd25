{"name": "saber-admin", "version": "3.0.1", "private": true, "scripts": {"serve": "vue-cli-service serve", "serve:academy": "vue-cli-service serve --mode academy", "build": "vue-cli-service build", "build:academy": "vue-cli-service build --mode academy", "lint": "vue-cli-service lint", "analyz": "npm_config_report=true npm run build", "test:unit": "vue-cli-service test:unit", "test:e2e": "vue-cli-service test:e2e", "clean": "rm -rf dist"}, "dependencies": {"@fullcalendar/core": "^6.1.10", "@fullcalendar/daygrid": "^6.1.10", "@fullcalendar/interaction": "^6.1.10", "@fullcalendar/timegrid": "^6.1.10", "@fullcalendar/vue": "^6.1.10", "@vxe-ui/plugin-render-element": "^3.0.3", "avue-plugin-ueditor": "^0.1.4", "axios": "^0.18.0", "babel-polyfill": "6.26.0", "classlist-polyfill": "^1.2.0", "crypto-js": "^4.0.0", "echarts": "^5.4.0", "element-ui": "^2.15.14", "fabric": "^5.3.0", "jquery": "^3.7.1", "js-base64": "^2.5.1", "js-cookie": "^2.2.0", "js-md5": "^0.7.3", "monaco-editor": "^0.30.1", "node-gyp": "^5.0.6", "nprogress": "^0.2.0", "portfinder": "^1.0.23", "print-js": "^1.6.0", "script-loader": "^0.7.2", "vue": "^2.6.10", "vue-axios": "^2.1.2", "vue-i18n": "^8.7.0", "vue-pdf": "^4.3.0", "vue-plugin-hiprint": "^0.0.54-fix", "vue-router": "^3.0.1", "vuex": "^3.1.1", "vxe-pc-ui": "3.3.43", "vxe-table": "3.8.5", "xe-utils": "3.5.25"}, "devDependencies": {"@vue/cli-plugin-babel": "3.1.1", "@vue/cli-plugin-eslint": "^3.1.5", "@vue/cli-service": "^3.1.4", "chai": "^4.1.2", "monaco-editor-webpack-plugin": "^6.0.0", "node-sass": "^8.0.0", "sass-loader": "^10.4.1", "vue-template-compiler": "^2.5.17", "webpack-bundle-analyzer": "^3.0.3"}, "lint-staged": {"*.js": ["vue-cli-service lint", "git add"], "*.vue": ["vue-cli-service lint", "git add"]}}
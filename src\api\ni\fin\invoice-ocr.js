import request from "@/router/axios";

export const invoiceOcr = (url, imgsrc) => {
  return request({
    // url:"/predict/ch_pp-ocrv3",
    url:url,
    method:"post",
    data: imgsrc,
    headers: {
      'accept': '*/*',
      'Content-type': 'application/json',
    },
  })
};

export const invoiceQRCode = (url, imgsrc) => {
  return request({
    // url:"/predict/ch_pp-ocrv3",
    url:url,
    method:"post",
    data: imgsrc,
    headers: {
      'accept': '*/*',
      'Content-type': 'application/json',
    },
  })
};

export const invoiceUrl = (url) => {
  return request({
    url:url,
    method:"get",
  })
};

export const formatInvoiceApi = (url, jsn) => {
  return request({
    url:url,
    method:"post",
    data: jsn,
  })
};

export const addSub = (row, row1) => {
  return request({
    url: "/api/ni/fin/invoiceOcr/save",
    method: "post",
    data:{
      finInvoice:row,
      finInvoiceItem:row1
    }
  });
};

export const upload = (formData) => {
  return request({
    url: "/api/blade-resource/oss/endpoint/put-file-attach-business?code=private",
    method: "post",
    data: formData,
  });
};

export const noExist = (fpdm, fphm) => {
  return request({
    url: "/api/ni/fin/invoiceOcr/getSum",
    method: "get",
    params: {
      fpdm,
      fphm,
    },
  });
};

export const verifyInvoice = (fpdm, fphm, kprq, xym, bhsje) => {
  return request({
    url: "/api/ni/fin/invoiceOcr/verifyInvoice",
    method: "get",
    params: {
      fpdm,
      fphm,
      kprq,
      xym,
      bhsje,
    },
  });
};

export const pdfToPng = (formData) => {
  return request({
    url: "/api/ni/fin/invoiceOcr/pdfToPng",
    method: "post",
    data: formData,
  });
};

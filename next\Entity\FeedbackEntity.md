```java
package com.natergy.ni.feedback.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.lang.Boolean;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.Map;

import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 问题反馈 实体类
 *
 * <AUTHOR>
 * @since 2025-02-26
 */
@Data
@TableName(value = "ni_feedback", autoResultMap = true)
@ApiModel(value = "Feedback对象", description = "问题反馈")
@EqualsAndHashCode(callSuper = true)
public class FeedbackEntity extends TenantEntity {

	/**
	 * 发起人id
	 */
	@ApiModelProperty(value = "发起人id")
	private Long applyUserId;
	/**
	 * 发起人姓名
	 */
	@ApiModelProperty(value = "发起人姓名")
	private String applyUserName;
	/**
	 * 负责人姓名
	 */
	@ApiModelProperty(value = "负责人姓名")
	private String responsibility;
	/**
	 * 问题反馈描述
	 */
	@ApiModelProperty(value = "问题反馈描述")
	private String description;
	/**
	 * 问题反馈分类
	 */
	@ApiModelProperty(value = "问题反馈分类")
	private String feedbackCategory;
	/**
	 * 问题反馈合集
	 */
	@ApiModelProperty(value = "问题反馈合集")
	private Integer feedbackCollection;
	/**
	 * 是否十不放过
	 */
	@ApiModelProperty(value = "是否十不放过")
	private Boolean isTenNonNeglect;
	/**
	 * AI指导意见
	 */
	@ApiModelProperty(value = "AI指导意见")
	private String aiGuidance;
	/**
	 * 最终解决日期
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd")
	@ApiModelProperty(value = "最终解决日期")
	private LocalDate finalResolutionTime;
	/**
	 * 最终报告
	 */
	@ApiModelProperty(value = "最终报告")
	private String finalReport;
	/**
	 * 流程实例id
	 */
	@ApiModelProperty(value = "流程实例id")
	private String processInstanceId;


	/**
	 * 负责人名称
	 */
	@ApiModelProperty(value = "负责人名称")
	private String responsibilityName;

	/**
	 * 文件地址
	 */
	@TableField(typeHandler = FastjsonTypeHandler.class)
	private List<Map<String, String>> fileUrls;

	/**
	 * 是否新聚类
	 */
	@ApiModelProperty(value = "是否新聚类")
	private Boolean isNewCluster;

	/**
	 * 部门名称
	 */
	@ApiModelProperty(value = "部门名称")
	private String deptNames;


	/**
	 * 向量数据库中的id
	 */
	@ApiModelProperty(value = "向量数据库中的id")
	private Integer pointId;

}

```


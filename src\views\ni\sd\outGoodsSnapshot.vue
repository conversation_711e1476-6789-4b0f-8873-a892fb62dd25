<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page.sync="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @refresh-change="refreshChange"
               @on-load="onLoad">

      <template #planStatus="{ row, index }">
        <el-tag v-if="row.planStatus === '1'" size="mini" type="danger" >
          未完成
        </el-tag>
        <el-tag v-if="row.planStatus === '2'" size="mini" >
          完成
        </el-tag>
      </template>
      <template #cover="{ row }">
        <div style="
          display: flex;
          justify-content: center;  /* 水平居中 */
          align-items: center;      /* 垂直居中 */
          width: 100%;
          height: 100%;
        ">
            <img
            :src="row.cover"
            style="
              width:50px;
              height:50px;
              cursor:pointer;
              border-radius:5px;
              object-fit: cover;
              "
            @click="handleImageClick(row)"
          />
        </div>
      </template>

      <template slot="menuLeft">
        <el-button type="danger"
                   size="small"
                   icon="el-icon-delete"
                   plain
                   v-if="permission.outGoodsSnapshot_delete"
                   @click="handleDelete">删 除
        </el-button>
        <el-button type="success"
                   size="small"
                   icon="el-icon-download"
                   plain
                   v-if="permission.outGoodsSnapshot_exportPic"
                   @click="handleExportPic">导出图片
        </el-button>
      </template>
      <template #menuRight="{size}">
        <el-button icon="el-icon-time" circle :size="size" @click="handleLog"></el-button>
      </template>
      <template #menu="{row,index,size}">
        <el-button type="text"
                   size="mini"
                   icon="el-icon-edit"
                   v-if="permission.outGoodsSnapshot_edit"
                   @click="$refs.crud.rowEdit(row,index);">编 辑
        </el-button>
        <el-divider direction="vertical" />
        <el-dropdown>
          <el-button type="text" :size="size">
            更多<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item type="text"
                              icon="el-icon-time"
                              :size="size"
                              @click.native="rowLog(row,index)">
              操作日志
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </template>

    </avue-crud>

    <log-opt-dialog ref="logOptDialogRef" :module="module"/>

    <el-dialog
        title="发货快照明细"
        append-to-body
        :visible.sync="outGoodsItemBox"
        width="60%"
        style="height: 80%"
        custom-class="custom-dialog"
        >
          <template #title>
            <div style="font-size: 18px; font-weight: bold; color: #303133; border-bottom: 1px solid #ebeef5; padding-bottom: 10px;">
              发货快照明细
            </div>
          </template>

          <div class="dialog-scroll-wrapper" id="app" style="height: 50vh;padding-bottom: 20px">
            <el-scrollbar class="scrollbar-content" style="height: 100%;">
            <div class="image-list" style="display: flex; flex-wrap: wrap; gap: 20px; padding: 15px;">
              <div class="image-item" v-for="(image, index) in imageList" :key="index"
                   style="background: #fff; border-radius: 8px; padding: 15px; box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1); transition: all 0.3s;">
                <el-image class="el-image"
                  style="width: 150px; height: 150px; border-radius: 6px; "
                  :src="image.url"
                  :preview-src-list="imageList.map(item => item.url)"
                  fit="cover"
                >
                </el-image>
                <div class="image-name" title="无人机编号">{{ image.droneId }}</div>
                <div class="image-name" title="拍照时间">{{ image.capTime }}</div>
              </div>
              <div class="bottom-space"></div> <!-- 底部占位空白区域 -->
            </div>
            </el-scrollbar>
          </div>
      </el-dialog>
  </basic-container>
</template>

<script>
import {getList, getDetail, add, update, remove, getOutGoodsDetail} from "@/api/ni/sd/outGoodsSnapshot";
import {mapGetters} from "vuex";
import LogOptDialog from "@/components/log-opt-dialog/index.vue";
import {now} from "xe-utils";
import {dateNow1} from "@/util/date";
import NProgress from "nprogress";
import {exportBlob} from "@/api/common";
import {getToken} from "@/util/auth";

export default {
  components: {LogOptDialog},
  data() {
    return {
      module: 'ni_sd_outgoodssnapshot',
      outGoodsItemBox: false,
      imageList: {},
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      selectionList: [],
      // planStatusDict: [],
      // planStatusDictKeyValue: [],
      dictData: [{
        dictValue: '未完成',
        dictKey: 1
      }, {
        dictValue: '完成',
        dictKey: 2
      }],

      option: {
        searchEnter: true,
        height: 'auto',
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        searchIndex: 3,
        searchIcon: true,
        border: true,
        index: false,
        viewBtn: false,
        addBtn: true,
        editBtn: false,
        selection: true,
        dialogClickModal: false,
        align: "center",
        column: [
          // {
          //   label: "状态",
          //   prop: "status",
          //   type: "input",
          //   hide: true,
          //   minWidth: 30,
          // },
          {
            label: '封面',
            prop: 'cover',
            type: 'img',
            width: 100,
            value: 'http://************:16689/ni-public/upload/20250325/f0640a215a051d81ce3bd4dc0c0f392e.png',
            align: 'center',
            disabled: true,
            display: false,
            // headerAlign: 'center',
            slot: true, // 表示使用slot自定义图片列
          },
          {
            label: "发货编号",
            prop: "planId",
            type: "input",
            minWidth: 30,
            search: true,
            // disabled: true,
            // headerAlign: 'center',
            rules:[{
              required:true,
              message:"请输入拍照计划",
              trigger:"blur"
            }],
          },
          {
            label: "发货批号",
            prop: "batchNumber",
            type: "input",
            minWidth: 30,
            search: true,
            // disabled: true,
            // headerAlign: 'center',
            rules:[{
              required:true,
              message:"请输入发货批号",
              trigger:"blur"
            }],
          },
          {
            label: "箱型",
            prop: "boxType",
            type: "input",
            minWidth: 30,
            search: true,
            // disabled: true,
            // headerAlign: 'center',
            rules:[{
              required:true,
              message:"请输入箱型",
              trigger:"blur"
            }],
          },
          {
            label: "完成状态",
            prop: "planStatus",
            type: "select",
            dataType: "number",
            minWidth: 20,
            value: 1,
            search: true,
            disabled: true,
            display: false,
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            dicData: [],
          },
          // {
          //   label: "无人机编号",
          //   prop: "droneId",
          //   type: "input",
          //   minWidth: 40,
          // },
          {
            label: "拍摄时间",
            prop: "capTime",
            type: "datetime",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
            // value: dateNow1(),
            minWidth: 30,
            overHidden: true,
            // disabled: true,
            // display: false,
            // headerAlign: 'center',
            rules:[{
              required:true,
              message:"请输入拍摄时间",
              trigger:"blur"
            }],
          },
          {
            label: "拍摄时间",
            prop: "datetimerange",
            type: "datetimerange",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
            startPlaceholder: "开始时间",
            endPlaceholder: "结束时间",
            searchRange: true,
            // minWidth: 30,
            overHidden: true,
            hide:true,
            display:false,
            search: true,
            // disabled: true,
            // headerAlign: 'center',
          },
          {
            label: "备注",
            prop: "remark",
            type: "textarea",
            // headerAlign: 'center',
          },
        ]
      },
      data: []
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.outGoodsSnapshot_add, false),
        viewBtn: this.vaildData(this.permission.outGoodsSnapshot_view, false),
        delBtn: this.vaildData(this.permission.outGoodsSnapshot_delete, false),
        editBtn: this.vaildData(this.permission.outGoodsSnapshot_edit, false)
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach(ele => {
        ids.push(ele.id);
      });
      return ids.join(",");
    }
  },
  mounted() {
    this.dictInit();
  },
  methods: {
    handleLog() {
      this.$refs.logOptDialogRef.init()
    },
    rowLog(row) {
      this.$refs.logOptDialogRef.init(row.id)
    },
    dictInit(){
      const column = this.findObject(this.option.column, "planStatus");
      column.dicData = this.dictData;

    },
    async handleImageClick(row) {
      this.imageList = null
      await getOutGoodsDetail(row.id).then((res) => {
        this.imageList = res.data.data
      }, error => {
        loading();
        window.console.log(error);
      });

      this.outGoodsItemBox = true
      // 1. 跳转路由:
      // this.$router.push({ path: '/detail', query: { id: row.id } });

      // 2. 调用某个方法处理业务逻辑:
      // this.someCustomMethod(row);

    },

    rowSave(row, done, loading) {
      add(row).then(() => {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
        done();
      }, error => {
        loading();
        window.console.log(error);
      });
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(() => {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
        done();
      }, error => {
        loading();
        console.log(error);
      });
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        });
    },
    handleExportPic(){
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择一条数据");
        return;
      }
      if (this.selectionList && this.selectionList.length > 1){
        this.$message.warning("最多只能导出一条数据");
        return;
      }
      let ids = "";
      console.log(this.selectionList)
      let planId = ""
      if (this.selectionList.length > 0) {
        planId = this.selectionList[0].planId;
      }
      if (this.selectionList && this.selectionList.length > 0)
        ids = this.selectionList
          .map((item) => {
            return item.id;
          })
          .join(",");
      this.$confirm("是否导出已选择的发货图片?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        NProgress.start();
        exportBlob(
          `/api/ni/sd/outGoodsSnapshot/export-pic?${
            this.website.tokenHeader
          }=${getToken()}&ids=${ids}`
        ).then((res) => {
          let blob = new Blob([res.data], {type: "application/zip"});
          let url = window.URL.createObjectURL(blob);
          const link = document.createElement("a"); // 创建a标签
          link.href = url;
          link.download = "发货图片_" + planId + ".zip"; // 重命名文件
          link.click();
          URL.revokeObjectURL(url); // 释放内存
          NProgress.done();
        });
      });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen(done, type) {
      if ("add" == type) {
        this.form = {
          capTime: dateNow1(),
        }
      }
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then(res => {
          this.form = res.data.data;
        });
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      //前端传值时添加参数
      if (params.datetimerange && params.datetimerange.length === 2){
        params.startTime = params.datetimerange[0]; // 添加 startTime1 参数
        params.endTime = params.datetimerange[1];
      }

      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      getList(page.currentPage, page.pageSize, Object.assign(params, this.query,{descs:'id'})).then(res => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    }
  }
};
</script>

<style scoped>
    .custom-dialog .el-dialog__body {
      padding-bottom: 0; /* 去除默认的底部padding，根据需求调整 */
    }
    .dialog-scroll-wrapper {
      height: 60vh; /* 设置你想要的dialog内容区域的高度 */
      display: flex;
      flex-direction: column;
    }
    .scrollbar-content {
      flex: 1;
    }

    /* 在滚动区域下方额外留20px空白 */
    .scrollbar-content {
      margin-bottom: 20px;
    }
    .image-list {
      display: flex;
      flex-wrap: wrap;
      gap: 15px;
    }

    .el-image {
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
      transition: transform 0.3s ease, box-shadow 0.3s ease;
      border-radius: 10px;
      overflow: hidden;
      opacity: 0;
      animation: fadeIn 0.5s ease-in-out forwards;
    }

    @keyframes fadeIn {
      from {
        opacity: 0;
      }
      to {
        opacity: 1;
      }
    }

    .el-image:hover {
      transform: scale(1.05);
      box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
    }

    .image-name {
      margin-top: 5px;
      font-size: 14px;
      justify-content: center; /* 水平居中 */
    }
    .image-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;
    }
    .image-item:hover {
      transform: translateY(-5px);
      box-shadow: 0 6px 16px 0 rgba(0,0,0,0.15);
    }
</style>


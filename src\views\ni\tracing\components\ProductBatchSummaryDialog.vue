<script>
import {summaries, summaries1} from "@/api/ni/tracing/productionBatch";

export default {
  data() {
    return {
      title: "生产批次产量统计",
      visible: false,
      selectionList: [],
      option: {
        selectable: (row, index) => {
          return row.materialCode;
        },
        selection: true,
        header: false,
        menu: false,
        search: false,
        editBtn: false,
        delBtn: false,
        dialogFullscreen: true,
        dialogDrag: true,
        size: "mini",
        align: "center",
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        span: 8,
        border: true,
        column: [
          {
            label: "编码",
            prop: "materialCode",
            weight: 100,
            overHidden: true,
          },
          {
            label: "规格",
            prop: "specificationName",
            type: "input",
            weight: 100,
            overHidden: true,
          },
          {
            label: "外包装",
            prop: "outerPackagingName",
            weight: 100,
            overHidden: true,
          },
          {
            label: "内包装",
            prop: "innerPackagingName",
            weight: 100,
            overHidden: true,
          },
          {
            label: "质量",
            prop: "qualityName",
            weight: 800,
            overHidden: true,
          },
          {
            label: "箱数",
            prop: "actualBoxQuantity",
            weight: 100,
            overHidden: true,
          },
          {
            label: "重量",
            prop: "actualTotalCapacity",
            weight: 100,
            overHidden: true,
          },
        ],
      },
      form: {},
      data: [],
      loading: false,
      query: {},
    };
  },
  methods: {
    onShow(form, title) {
      if (title) this.title = title;
      this.query = form;
      this.data = [];
      this.visible = true;
    },
    handleClose(done) {
      this.visible = false;
      if (done && typeof done == "function") done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    handleConfirm() {
      const unCode = this.selectionList.filter((d) => !d.materialCode);
      if (unCode.length > 0) {
        this.$message.error("生产的产品中有未关联编码的数据，请关联后再同步");
        return;
      }
      if (this.data.length <= 0) {
        this.$message.error("未查询到关联的生产记录");
        return;
      }
      const skuIds = this.selectionList.map((d) => d.skuId);
      this.$emit("confirm", this.query, skuIds.join(","));
      this.handleClose();
    },
    onLoad() {
      this.loading = true;
      summaries1(this.query.startDate, this.query.endDate).then((res) => {
        this.data = res.data.data;
        this.selectionClear();
        this.loading = false;
      });
    },
  },
};
</script>

<template>
  <el-dialog
    :title="title"
    :visible.sync="visible"
    width="667px"
    append-to-body
  >
    <template #title>
      {{ title }}
      <el-divider direction="vertical" />
      <el-tag>
        当前已选择
        <span style="font-weight: bolder">{{ selectionList.length }}</span> 项
        <el-button type="text" size="mini" @click="selectionClear">
          清空
        </el-button>
      </el-tag>
    </template>
    <avue-crud
      v-if="visible"
      :option="option"
      :table-loading="loading"
      :data="data"
      ref="crud"
      v-model="form"
      @selection-change="selectionChange"
      @on-load="onLoad"
    >
      <template #materialCode="{ row }">
        <span v-if="row.materialCode">{{ row.materialCode }}</span>
        <span v-else style="color: #f56c6c; font-weight: bold">未关联</span>
      </template>
    </avue-crud>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose" size="mini">取 消</el-button>
      <el-button
        :disabled="selectionList.length < 1"
        type="primary"
        @click="handleConfirm"
        size="mini"
        >确 定</el-button
      >
    </span>
  </el-dialog>
</template>

<style scoped></style>

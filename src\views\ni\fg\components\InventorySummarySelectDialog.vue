<script>
import {getList} from "@/api/ni/fg/fgInventorySummary";

export default {
  name: "SkuSelectDialog",
  props: {
    params: {
      type: Object,
      default: () => {
      },
    },
    multiple: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      visible: false,
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      selectionList: [],
      option: {
        menu: false,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        size: "mini",
        searchSize: "mini",
        align: "center",
        searchIndex: 3,
        searchIcon: true,
        searchEnter: true,
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        selection: true,
        dialogClickModal: false,
        column: [
          {
            label: "",
            prop: "radio",
            type: "radio",
            width: 55,
            hide: true,
          },
          {
            label: "仓库",
            prop: "depotId",
            type: "select",
            dicUrl: "/api/ni/base/depot/info/list?status=2&type=1",
            props: {
              label: "name",
              value: "id",
              desc: "code",
            },
            search: true,
            minWidth: 100,
            overHidden: true
          },
          {
            label: '存货编码',
            prop: 'materialCode',
            placeholder: " ",
            width: 110,
            search: true,
            overHidden: true
          },
          {
            label: '规格',
            prop: "spec",
            type: 'select',
            dicUrl: '/api/ni/product/spec/list',
            props: {
              label: "name",
              value: "code",
              desc: 'code'
            },
            hide: true,
            showColumn: false,
            filterable: true,
            minWidth: 120,
            search: true,
          },
          {
            label: '规格',
            prop: 'specText',
            placeholder: " ",
            display: false,
            overHidden: true,
            width: 120,
          },
          {
            label: '外包装',
            prop: 'packageText',
            placeholder: " ",
            display: false,
            overHidden: true,
            width: 115,
            search: true,
          },
          {
            label: '内包装',
            prop: 'innerPackageText',
            placeholder: " ",
            display: false,
            overHidden: true,
            width: 115,
            search: true,
          },
          {
            label: '质量',
            prop: 'qualityLevel',
            type: 'select',
            dicUrl: "/api/blade-system/dict-biz/dictionary?code=ni_product_sku_quality_level",
            props: {
              label: "dictValue",
              value: "dictKey",
              desc: 'dictKey'
            },
            placeholder: " ",
            width: 90,
            search: true,
          },
          {
            label: "可用箱数",
            prop: "num",
            minWidth: 80,
            type: 'number',
          },
          {
            label: "在途箱数",
            prop: "inTransitBoxCount",
            minWidth: 80,
            type: 'number',
          },
          {
            label: '可用重量',
            prop: 'weight',
            placeholder: " ",
            type: 'number',
            minWidth: 80,
          },
          {
            label: '在途重量',
            prop: 'inTransitKg',
            placeholder: " ",
            type: 'number',
            minWidth: 80,
          },
          {
            label: "单位",
            prop: "unit",
            type: "select",
            filterable: true,
            width: 80,
            dicUrl: "/api/blade-system/dict-biz/dictionary?code=ni_material_unit",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            slot: true,
            placeholder: " ",
          },

        ]
      },
      data: [],
      form: {},
      qualityLevelColorMap: {
        'A': '#67C23A', // 高吸附 - 绿色
        'P': '#409EFF', // 优等品 - 蓝色
        'Q': '#E6A23C', // 合格品 - 橙色
      },
    }
  },
  methods: {
    onShow() {
      if (!this.multiple) {
        this.$set(this.option, "selection", false);
        this.findObject(this.option.column, "radio").hide = false;
      } else {
        this.$set(this.option, "selection", true);
        this.findObject(this.option.column, "radio").hide = true;
      }
      this.visible = true
    },
    rowClick(row) {
      if (!this.multiple) {
        this.selectionList = [row];
        this.$set(this.form, "radio", row.id);
      } else this.$refs.crud.toggleSelection([row]);
    },
    handleClose(done) {
      this.visible = false;
      if (done && typeof done == "function") done();
    },
    handleConfirm() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$emit("confirm", this.selectionList);
      this.handleClose();
    },
    searchChange(params, done) {
      this.query = params
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    searchReset() {
      this.query = {}
      this.page.currentPage = 1;
      this.onLoad(this.page);
    },
    cellStyle({row, column}) {
      if ("qualityLevel" === column.columnKey) {
        const color = this.qualityLevelColorMap[row.qualityLevel];
        return {
          backgroundColor: color || "#909399", // 默认颜色为灰色
          color: "#fff",
        }
      }
    },
    onLoad(page, params = {}) {
      this.loading = true;
      const q = {
        ...params,
        ...this.query,
        ...this.params
      }
      getList(page.currentPage, page.pageSize, q).then(res => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
  }
}
</script>

<template>
  <el-dialog v-dialogDrag :visible.sync="visible" append-to-body width="1150px">
    <avue-crud
      ref="crud"
      v-if="visible"
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      v-model="form"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionList = $event"
      @current-change="page.currentPage = $event"
      @size-change="page.pageSize = $event"
      @row-click="rowClick"
      @on-load="onLoad"
      :cell-style="cellStyle"
    >
      <template v-if="!multiple" #radio="{ row }">
        <el-radio v-model="form.radio" :label="row.id"><i></i></el-radio>
      </template>
    </avue-crud>
    <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose" size="mini">取 消</el-button>
        <el-button type="primary" @click="handleConfirm" size="mini"
        >确 定</el-button
        >
      </span>
  </el-dialog>
</template>

<style scoped>

</style>

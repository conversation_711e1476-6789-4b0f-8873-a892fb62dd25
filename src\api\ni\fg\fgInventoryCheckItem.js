import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/ni/fg/inventory-check-item/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/ni/fg/inventory-check-item/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/ni/fg/inventory-check-item/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/ni/fg/inventory-check-item/save',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/ni/fg/inventory-check-item/update',
    method: 'post',
    data: row
  })
}


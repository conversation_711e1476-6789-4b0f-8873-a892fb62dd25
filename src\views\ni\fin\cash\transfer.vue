<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
      :cell-style="cellStyle"
    >
      <template #serialNo="{ row, index }">
        <flow-timeline-popover
          v-if="row.processInsId"
          :process-ins-id="row.processInsId"
          :process-def-key="processDefKey"
          v-model="row.serialNo"
          :flow.sync="row.flow"
          trigger="click"
          lazy
        />
        <span v-else>{{ row.serialNo }}</span>
      </template>
      <template #menuRight="{ size }">
        <el-button
          icon="el-icon-time"
          circle
          :size="size"
          @click="handleLog"
        ></el-button>
      </template>
      <template #menuLeft>
        <el-button
          type="primary"
          size="mini"
          icon="el-icon-s-promotion"
          v-if="permission.ni_fin_cash_transfer_apply"
          @click="handleApply"
          >发起申请
        </el-button>
        <el-button
          type="info"
          size="mini"
          icon="el-icon-download"
          plain
          v-if="permission.ni_fin_cash_transfer_export"
          @click="handleExport"
          >导出
        </el-button>
        <el-button
          type="warning"
          size="mini"
          icon="el-icon-setting"
          @click="handleSetting"
        >手续费会计科目设置
        </el-button>
      </template>
      <template #menu="{ row, index }">
        <el-button
          type="text"
          size="mini"
          icon="el-icon-s-check"
          v-if="
            permission.ni_fin_cash_transfer_post &&
            row.status === 9 &&
            !row.red &&
            row.postStatus === 0
          "
          @click="rowPost(row)"
          >确认执行
        </el-button>
        <el-button
          type="text"
          size="mini"
          icon="el-icon-delete"
          style="color: #f56c6c"
          v-if="
            permission.ni_fin_cash_transfer_delete &&
            [0, 4, 6].includes(row.status)
          "
          @click="rowDel(row)"
          >删除
        </el-button>
        <el-button
          type="text"
          size="mini"
          icon="el-icon-s-release"
          style="color: #e6a23c"
          v-if="
            permission.ni_fin_cash_transfer_void &&
            row.status === 9 &&
            !row.red &&
            row.postStatus === 0
          "
          @click="rowVoid(row)"
          >作废
        </el-button>
        <el-button
          type="text"
          size="mini"
          icon="el-icon-refresh-left"
          style="color: #e6a23c"
          v-if="
            permission.ni_fin_cash_transfer_red &&
            row.status === 9 &&
            !row.red &&
            row.postStatus === 1
          "
          @click="rowRed(row)"
          >冲销
        </el-button>
      </template>
    </avue-crud>
    <attach-dialog ref="attachDialogRef" />
    <log-opt-dialog ref="logOptDialogRef" :module="module" />
    <el-dialog
      title="会计科目设置"
      append-to-body
      :visible.sync="setting.visible"
      width="555px"
    >
      <el-tabs v-model="setting.brand" @tab-click="handleSettingBrandClick">
        <el-tab-pane
          v-for="(item, index) in brandDict"
          :key="index"
          :label="item.dictValue"
          :name="item.dictKey"
        ></el-tab-pane>
      </el-tabs>
      <avue-form
        :loading="setting.loading"
        :option="setting.option"
        v-model="setting.form"
        @submit="handleSettingSubmit"
      >
      </avue-form>
    </el-dialog>
  </basic-container>
</template>

<script>
import {
  add,
  getDetail,
  getPage,
  remove,
  toVoid,
  update,
  post,
  red,
} from "@/api/ni/fin/cash/transfer";
import { mapGetters } from "vuex";
import { dateNow1 } from "@/util/date";
import AttachDialog from "@/components/attach-dialog";
import FlowTimelinePopover from "@/components/wf-flow-timeline/popover.vue";
import exForm from "@/views/plugin/workflow/mixins/ex-form";
import LogOptDialog from "@/components/log-opt-dialog/index.vue";
import {
  getAccountingSettings,
  getList,
  saveAccountingSettings,
} from "@/api/ni/fin/finFx";
import request from "@/router/axios";

export default {
  components: {
    LogOptDialog,
    FlowTimelinePopover,
    AttachDialog,
  },
  mixins: [exForm],
  data() {
    return {
      module: "ni_fin_cash_transfer",
      processDefKey: "process_fin_cash_transfer_apply",
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        menuWidth: 150,
        editBtn: false,
        delBtn: false,
        searchEnter: true,
        addBtn: false,
        labelWidth: 110,
        searchLabelWidth: 110,
        align: "center",
        span: 8,
        searchIndex: 3,
        searchIcon: true,
        size: "mini",
        searchSize: "mini",
        height: "auto",
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: false,
        selection: true,
        viewBtn: false,
        column: [
          {
            label: "操作日期",
            prop: "opDate",
            type: "date",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            search: true,
            searchRange: true,
            width: 110,
            rules: [
              {
                required: true,
                message: "请输入操作日期",
                trigger: "blur",
              },
            ],
          },
          {
            label: "单据编号",
            prop: "serialNo",
            placeholder: "系统自动生成",
            searchPlaceholder: " ",
            search: true,
            disabled: true,
            overHidden: true,
          },

          {
            label: "摘要",
            prop: "title",
            search: true,
            minWidth: 150,
            rules: [
              {
                required: true,
                message: "请输入主题",
                trigger: "blur",
              },
            ],
          },
          {
            label: "账套",
            prop: "brand",
            placeholder: " ",
            type: "radio",
            overHidden: true,
            dicUrl: "/api/blade-system/dict-biz/dictionary?code=ni_brand",
            cascader: ["ledgerId", "otherLedgerId"],
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            width: 80,
            search: true,
            rules: [
              {
                required: true,
                message: "请选择账套",
                trigger: "blur",
              },
            ],
          },
          {
            label: "审批状态",
            prop: "status",
            dicUrl: "/api/blade-system/dict/dictionary?code=data_status",
            search: true,
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            dataType: "number",
            display: false,
            type: "select",
            width: 80,
          },
          {
            label: "执行状态",
            prop: "postStatus",
            dicUrl: "/api/blade-system/dict-biz/dictionary?code=ni_post_status",
            search: true,
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            dataType: "number",
            type: "select",
            width: 80,
          },
          {
            label: "转出账户",
            prop: "ledgerName",
            display: false,
            overHidden: true,
          },
          {
            label: "转出账户",
            prop: "ledgerId",
            placeholder: " ",
            type: "select",
            dicUrl: "/api/ni/fin/ledger/list?status=2&brand={{key}}",
            overHidden: true,
            props: {
              label: "dictLabel",
              value: "id",
              desc: "currencyName",
            },
            hide: true,
            showColumn: false,
            search: true,
            rules: [
              {
                required: true,
                message: "请选择品牌",
                trigger: "blur",
              },
            ],
          },
          {
            label: "转入账户",
            prop: "otherLedgerName",
            display: false,
            overHidden: true,
          },
          {
            label: "转入账户",
            prop: "otherLedgerId",
            placeholder: " ",
            type: "select",
            dicUrl: "/api/ni/fin/ledger/list?status=2&brand={{key}}",
            overHidden: true,
            props: {
              label: "dictLabel",
              value: "id",
              desc: "currencyName",
            },
            hide: true,
            showColumn: false,
            search: true,
            rules: [
              {
                required: true,
                message: "请选择品牌",
                trigger: "blur",
              },
            ],
          },
          {
            label: "转账金额",
            prop: "amount",
            type: "number",
            placeholder: " ",
            precision: 2,
            width: 110,
            rules: [
              {
                required: true,
                message: "请输入转账金额",
                trigger: "blur",
              },
            ],
          },
          {
            label: "币种",
            prop: "currency",
            type: "select",
            search: true,
            dicUrl: "/api/blade-system/dict/dictionary?code=currency",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            value: "RMB",
            width: 80,
            placeholder: " ",
            rules: [
              {
                required: true,
                message: "请选择币种",
                trigger: "blur",
              },
            ],
            control: (val) => {
              if (val === "RMB") {
                return {
                  excRate: {
                    display: false,
                    value: 1,
                  },
                };
              } else {
                return {
                  excRate: {
                    display: true,
                  },
                };
              }
            },
          },
          {
            label: "手续费",
            prop: "bankFee",
            type: "input",
            width: 80,
          },
          {
            label: "手续费扣减账户",
            prop: "feeLedgerId",
            placeholder: " ",
            type: "select",
            dicUrl: "/api/ni/fin/ledger/list?status=2&brand={{key}}",
            overHidden: true,
            props: {
              label: "dictLabel",
              value: "id",
              desc: "currencyName",
            },
            hide: true,
          },
          {
            label: "申请人",
            prop: "createUserName",
            width: 80,
            overHidden: true,
          },
        ],
      },
      data: [],
      exportColumn: [
        {
          label: "申请日期",
          prop: "opDate",
        },
        {
          label: "流水号",
          prop: "serialNo",
        },
        {
          label: "摘要",
          prop: "title",
        },
        {
          label: "账套",
          prop: "brandText",
        },
        {
          label: "执行状态",
          prop: "postStatusText",
        },
        {
          label: "转出账户",
          prop: "ledgerName",
          overHidden: true,
          width: 150,
        },
        {
          label: "转入账户",
          prop: "otherLedgerName",
          type: "input",
          overHidden: true,
          width: 150,
        },
        {
          label: "币种",
          prop: "currencyText",
          overHidden: true,
          width: 150,
        },
        {
          label: "金额",
          prop: "amount",
          type: "input",
          overHidden: true,
          width: 100,
        },
        {
          label: "手续费",
          prop: "bankFee",
        },
        {
          label: "手续费扣减账户",
          prop: "feeLedgerName",
        },
        {
          label: "申请人",
          prop: "createUserName",
        },
        {
          label: "审批状态",
          prop: "statusText",
        },
      ],
      brandDict: [],
      setting: {
        loading: false,
        visible: false,
        brand: "1",
        option: {
          span: 24,
          labelWidth: 150,
          size: "mini",
          searchSize: "mini",
          emptyBtn: false,
          column: [
            {
              label: "手续费",
              prop: "fee",
              type: "dynamic",
              labelPosition: "top",
              span: 24,
              children: {
                align: "center",
                headerAlign: "center",
                column: [
                  {
                    label: "币种",
                    prop: "currency",
                    type: "select",
                    dicUrl: "/api/blade-system/dict/dictionary?code=currency",
                    props: {
                      label: "dictValue",
                      value: "dictKey",
                    },
                    placeholder: " ",
                    rules: [
                      {
                        required: true,
                        message: "请选择币种",
                        trigger: "blur",
                      },
                    ],
                  },
                  {
                    label: "会计科目",
                    prop: "accountingId",
                    type: "tree",
                    remote: true,
                    parent: false,
                    dicData: [],
                    props: {
                      label: "title",
                      value: "id",
                      desc: "code",
                    },
                    placeholder: " ",
                    rules: [
                      {
                        required: true,
                        message: "请选择会计科目",
                        trigger: "blur",
                      },
                    ],
                  },
                ],
              },
            },
          ],
        },
        form: {},
      },
    };
  },
  created() {
    request({
      url: "/api/blade-system/dict-biz/dictionary?code=ni_brand",
      method: "get",
    }).then((res) => {
      this.brandDict = res.data.data;
    });
  },
  computed: {
    ...mapGetters(["permission", "userInfo"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.finPayable_add, false),
        viewBtn: this.vaildData(this.permission.finPayable_view, false),
        delBtn: this.vaildData(this.permission.finPayable_delete, false),
        editBtn: this.vaildData(this.permission.finPayable_edit, false),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  methods: {
    handleSetting() {
      this.setting.visible = true;
      this.handleSettingBrandClick({ name: "1" });
    },
    handleSettingBrandClick(tab) {
      this.$http
        .get("/api/ni/fin/accounting/submitTree", {
          params: {
            brand: tab.name,
          },
        })
        .then((res) => {
          const fee = this.findObject(this.setting.option.column, "fee");
          if (fee) {
            const accountingIdColumn = fee.children.column.find(
              (item) => item.prop === "accountingId"
            );
            if (accountingIdColumn) accountingIdColumn.dicData = res.data.data;
          }
        });
      this.setting.form = {
        fee: [],
      };
      getAccountingSettings(tab.name).then((res) => {
        if (
          res.data &&
          res.data.code === 200 &&
          res.data.data &&
          typeof res.data.data === "object"
        ) {
          this.setting.form = Object.assign(
            {},
            this.setting.form,
            res.data.data
          );
          if (res.data.data.fee && Array.isArray(res.data.data.fee)) {
            this.setting.form.fee = [...res.data.data.fee];
          }
        }
      });
    },
    handleSettingSubmit(form, done) {
      saveAccountingSettings(this.setting.brand, form)
        .then(() => {
          this.$message({
            type: "success",
            message: "保存成功!",
          });
          this.setting.visible = false;
        })
        .finally(() => {
          done();
        });
    },
    handleLog() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择要查看的数据");
        return;
      } else if (this.selectionList.length > 1) {
        this.$message.warning("只能选择一条数据");
        return;
      }
      this.$refs.logOptDialogRef.init(this.selectionList[0].id);
    },
    handleApply() {
      this.dynamicRoute(
        {
          processDefKey: this.processDefKey,
        },
        "start"
      );
    },
    async handleExport() {
      let msg =
        "确定将<span style='font-weight: bolder;color: #F56C6C'>选择数据</span>导出?";
      if (this.selectionList.length === 0) {
        msg =
          "确定要将<span style='font-weight: bolder;color: #F56C6C'>全部数据</span>导出?";
      }
      try {
        await this.$alert(msg, {
          dangerouslyUseHTMLString: true,
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        });
        const data = [];
        if (this.selectionList.length > 0) {
          this.selectionList.forEach((item) => {
            data.push({
              ...item,
            });
          });
        } else {
          const q = {
            ...this.query,
          };
          if (q.opDate != null && q.opDate.length === 2) {
            q.startOpDate = q.opDate[0];
            q.endOpDate = q.opDate[1];
            q.opDate = null;
          }
          const res = await getList(1, 10000, q);
          const data1 = res.data.data.records;
          data1.forEach((item) => {
            data.push({
              ...item,
            });
          });
        }
        await this.$Export.excel({
          title: "帐间转账",
          columns: this.exportColumn,
          data,
        });
      } catch (e) {
        console.log(e);
      }
    },
    rowAttach(row) {
      this.$refs.attachDialogRef.init(row.id, "ni_fin_cash_transfer");
    },
    rowVoid(row) {
      this.$confirm("此操作将作废提交的数据，是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        center: true,
      })
        .then(() => {
          return toVoid(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    rowSave(row, done, loading) {
      add(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          window.console.log(error);
        }
      );
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          console.log(error);
        }
      );
    },
    rowRed(row, index) {
      this.$confirm(
        `您确定要冲销单据<span style="color: #0d84ff"> ${row.serialNo} </span>吗？此操作将生成一张方向相反的红字凭证，且不可撤销。`,
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
          dangerouslyUseHTMLString: true,
        }
      )
        .then(() => {
          return red(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    rowPost(row) {
      this.$confirm(
        `您确定要将该单据<span style="color: #0d84ff"> ${row.serialNo} </span>的执行状态改为已执行？此操作将不可撤销。`,
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
          dangerouslyUseHTMLString: true,
        }
      )
        .then(() => {
          return post(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen(done, type) {
      if ("add" === type) {
        this.form.type = 1;
        this.form.financialUserId = this.userInfo.user_id;
        this.form.date = dateNow1();
      } else if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then((res) => {
          this.form = res.data.data;
        });
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      const q = {
        ...params,
        ...this.query,
      };
      if (q.date && q.date.length === 2) {
        q.startDate = q.date && q.date[0];
        q.endDate = q.date && q.date[1];
        q.date = null;
      }
      getPage(page.currentPage, page.pageSize, q).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
    cellStyle({ row, column }) {
      if ("postStatus" === column.columnKey) {
        return this.postStatusStyle(row.postStatus);
      }
      if ("status" === column.columnKey) {
        return this.statusStyle(row.status);
      }
      if ("brand" === column.columnKey) {
        return this.brandStyle(row.brand);
      }
      if (
        "ledgerName" === column.columnKey &&
        row.ledgerId === row.feeLedgerId
      ) {
        return {
          color: "#409EFF",
          fontWeight: "bold",
        };
      }
      if (
        "otherLedgerName" === column.columnKey &&
        row.otherLedgerId === row.feeLedgerId
      ) {
        return {
          color: "#409EFF",
          fontWeight: "bold",
        };
      }
    },
    postStatusStyle(postStatus) {
      if (postStatus === 0) {
        return {
          backgroundColor: "#909399",
          color: "#fff",
        };
      } else if (postStatus === 1) {
        return {
          backgroundColor: "#67C23A",
          color: "#fff",
        };
      } else if (postStatus === 2) {
        return {
          backgroundColor: "#F56C6C",
          color: "#fff",
        };
      } else if (postStatus === 9) {
        return {
          backgroundColor: "#E6A23C",
          color: "#fff",
        };
      }
    },
    brandStyle(brand) {
      if (brand === "1") {
        return {
          background: "#167C46",
          color: "#fff",
        };
      } else if (brand === "2") {
        return {
          background: "#06c15c",
          color: "#fff",
        };
      } else if (brand === "4") {
        return {
          background: "#056487",
          color: "#fff",
        };
      } else if (brand === "5") {
        return {
          background: "#ae800c",
          color: "#fff",
        };
      }
      return {
        background: "#167C46",
        color: "#fff",
      };
    },
    statusStyle(status) {
      if (status === 0) {
        return {
          backgroundColor: "#909399",
          color: "#fff",
        };
      } else if (status === 1) {
        return {
          backgroundColor: "#409EFF",
        };
      } else if (status === 2) {
        return {
          backgroundColor: "#E6A23C",
          color: "#fff",
        };
      } else if (status === 3) {
        return {
          backgroundColor: "#F56C6C",
        };
      } else if (status === 6) {
        return {
          backgroundColor: " #F56C6C",
          color: "#fff",
        };
      } else if ([4, 5].includes(status)) {
        return {
          backgroundColor: "#E6A23C",
        };
      } else if (status === 9) {
        return {
          backgroundColor: "#67C23A",
          color: "#fff",
        };
      } else {
        return {
          backgroundColor: "#E6A23C",
        };
      }
    },
  },
};
</script>

<style></style>

import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/ni/meal/takeout/locations/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/ni/meal/takeout/locations/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/ni/meal/takeout/locations/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/ni/meal/takeout/locations/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/ni/meal/takeout/locations/submit',
    method: 'post',
    data: row
  })
}

export const play = (ids) => {
  return request({
    url: '/api/ni/meal/takeout/locations/play',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const pause = (ids) => {
  return request({
    url: '/api/ni/meal/takeout/locations/pause',
    method: 'post',
    params: {
      ids,
    }
  })
}

import request from '@/router/axios';

export const getList = (params) => {
  return request({
    url: '/api/ni/product/spec/list',
    method: 'get',
    params
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/ni/product/spec/detail',
    method: 'get',
    params: {
      id
    }
  })
}
export const remove = (ids) => {
  return request({
    url: '/api/ni/product/spec/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/ni/product/spec/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/ni/product/spec/submit',
    method: 'post',
    data: row
  })
}


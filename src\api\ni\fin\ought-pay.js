import request from "@/router/axios";

export const getPage = (current, size, params) => {
  return request({
    url: "/api/ni/fin/oughtPay/page",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/ni/fin/oughtPay/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const remove = (ids) => {
  return request({
    url: "/api/ni/fin/oughtPay/remove",
    method: "post",
    params: {
      ids,
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/ni/fin/oughtPay/save",
    method: "post",
    data: row,
  });
};

export const update = (row) => {
  return request({
    url: "/api/ni/fin/oughtPay/update",
    method: "post",
    data: row,
  });
};

export const submit = (ids) => {
  return request({
    url: "/api/ni/fin/oughtPay/submit",
    method: "post",
    params: {
      ids,
    },
  });
};

export const back = (ids) => {
  return request({
    url: "/api/ni/fin/oughtPay/back",
    method: "post",
    params: {
      ids,
    },
  });
};

export const toVoid = (ids) => {
  return request({
    url: "/api/ni/fin/oughtPay/toVoid",
    method: "post",
    params: {
      ids,
    },
  });
};
export const addByApply = (applyRow) => {
  return request({
    url: "/api/ni/fin/oughtPay/addByApply",
    method: "post",
    data: applyRow,
  });
};
export const syncOughtPay = (ids) => {
  return request({
    url: "/api/ni/fin/oughtPay/syncOughtPay",
    method: "post",
    params: {
      ids,
    },
  });
};

<template>
  <div>
    <el-input
      v-model="pTitle"
      :size="size"
      :disabled="disabled"
      suffix-icon="el-icon-search"
      :placeholder="placeholder || '预算选择'"
      readonly
      class="select-input"
      @focus="bpFocus"
      @clear="handleClear"
      clearable
    />
    <un-finish-budget-select-dialog
      ref="budgetSelectDialogRef"
      :multiple="multiple"
      :all="all"
      v-model="value"
      :params="params"
      @confirm="handleConfirm"
    />
  </div>
</template>
<script>
import { getDetail } from "@/api/ni/por/budget";
import UnFinishBudgetSelectDialog from "@/views/ni/por/components/UnFinishBudgetSelectDialog";
import Emitter from 'element-ui/src/mixins/emitter';

export default {
  mixins: [Emitter],
  components: { UnFinishBudgetSelectDialog },
  props: {
    value: {
      type: String,
    },
    size: {
      type: String,
      default: "mini",
    },
    placeholder: String,
    disabled: Boolean,
    params: {
      type: Object,
      default: () => {},
    },
    all: Boolean,
    readonly: {
      type: Boolean,
      default: false,
    },
    multiple: {
      type: Boolean,
      default: false,
    },
  },
  watch: {
    value: {
      handler(val) {
        if (val) {
          const name = [];
          const checks = (val + "").split(",");
          const asyncList = [];
          checks.forEach((c) => {
            asyncList.push(getDetail(c));
          });
          Promise.all(asyncList).then((res) => {
            const selectionList = [];
            res.forEach((r) => {
              const data = r.data.data;
              if (data) {
                name.push(data.serialNo);
                selectionList.push(data);
              }
            });
            this.$set(this, "pTitle", name.join(","));
            this.$emit("confirm", selectionList);
          });
        } else this.$set(this, "pTitle", "");
      },
      immediate: true,
    },
  },
  data() {
    return {
      pTitle: "",
    };
  },
  methods: {
    handleClear() {
      this.$emit("update:label", "");
      this.$emit("update:value", "");
      this.$emit("clear");
      this.dispatch("ElFormItem", "el.form.blur", [""]);
    },
    bpFocus() {
      if (this.readonly || this.disabled) return;
      else this.$refs.budgetSelectDialogRef.visible = true;
    },

    handleConfirm(selectionList, ids) {
      this.$emit("input", ids);
      this.$emit("confirm", selectionList, this.ids);
      this.$nextTick(() => {
        this.dispatch('ElFormItem', 'el.form.blur', [ids]);
      });
    },
  },
};
</script>
<style lang="scss">
.p-dialog {
  display: flex;
  flex-direction: column;
  margin: 0 !important;
  position: absolute;
  top: 40%;
  left: 50%;
  transform: translate(-50%, -40%);
  max-height: calc(100% - 30px);
  max-width: calc(100% - 30px);

  .el-dialog__body {
    flex: 1;
    overflow: auto;
  }
}
.select-input {
  cursor: pointer;
}
/* 在 Vue 2 中需要使用深度选择器 >>> 来确保样式生效 */
.select-input >>> .el-input__inner {
  cursor: pointer;
}
</style>

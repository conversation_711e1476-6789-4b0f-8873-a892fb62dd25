```java
// 定义包路径，属于问题反馈模块的数据访问层（Mapper）
package com.natergy.ni.feedback.mapper;

// 导入问题解决记录的实体类和视图对象类
import com.natergy.ni.feedback.entity.FeedbackSolvingRecordEntity;
import com.natergy.ni.feedback.vo.FeedbackSolvingRecordVO;
// 导入MyBatis-Plus的基础Mapper接口（封装了CRUD基础方法）
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
// 导入MyBatis-Plus的分页接口
import com.baomidou.mybatisplus.core.metadata.IPage;

// 导入Java集合框架的List接口
import java.util.List;

/**
 * 问题各负责人解决记录 Mapper 接口
 * （数据访问层，负责与数据库交互，定义问题解决记录的查询方法）
 *
 * <AUTHOR>
 * @since 2025-02-27
 */
// 继承BaseMapper接口，泛型为问题解决记录的实体类FeedbackSolvingRecordEntity，获得基础CRUD操作能力
public interface FeedbackSolvingRecordMapper extends BaseMapper<FeedbackSolvingRecordEntity> {

	/**
	 * 自定义分页查询问题解决记录列表
	 * （支持多条件筛选，返回视图对象VO用于前端展示）
	 *
	 * @param page                     分页对象（包含页码、每页条数等分页参数，由MyBatis-Plus自动处理分页）
	 * @param feedbackSolvingRecord    封装的查询参数对象（如问题ID、负责人ID等，同时作为视图对象承载返回数据）
	 * @return 问题解决记录视图对象列表（List<FeedbackSolvingRecordVO>），包含分页信息
	 */
	List<FeedbackSolvingRecordVO> selectFeedbackSolvingRecordPage(
		IPage page, 
		FeedbackSolvingRecordVO feedbackSolvingRecord
	);

}
```

### 接口功能说明

该接口是问题解决记录模块的**数据访问层（Mapper）**，基于 MyBatis-Plus 的`BaseMapper`扩展，主要负责与数据库交互，定义问题解决记录的查询方法，特别是支持复杂条件的分页查询。

#### 核心方法解析

- **继承基础功能**：通过继承`BaseMapper<FeedbackSolvingRecordEntity>`，自动获得`insert`（新增）、`update`（更新）、`delete`（删除）、`selectById`（根据 ID 查询）等基础 CRUD 方法，无需手动编写 SQL。

- **自定义分页查询**：`selectFeedbackSolvingRecordPage`是核心自定义方法，用于满足业务场景中对问题解决记录的分页查询需求：

  - 参数说明

    ：

    - `page`：MyBatis-Plus 的`IPage`对象，用于传递分页参数（页码、每页条数），并接收查询后的总条数等分页信息。
    - `feedbackSolvingRecord`：既是查询参数载体（如包含问题 ID、负责人 ID 等筛选条件），也是返回结果的视图对象类型，便于前端展示扩展字段（如关联的用户名、流程信息等）。

  - **返回值**：`List<FeedbackSolvingRecordVO>`，即查询到的问题解决记录视图对象列表，包含前端展示所需的完整信息（如解决人姓名、解决时间、处理结果等，由 VO 封装）。

#### 实现方式

该接口的具体 SQL 实现通常放在对应的 XML 映射文件（如`FeedbackSolvingRecordMapper.xml`）中，通过`selectFeedbackSolvingRecordPage`方法的 ID 关联，编写动态 SQL 语句。根据`feedbackSolvingRecord`中的参数构建查询条件（如`WHERE`子句中的问题 ID 匹配、负责人筛选等），并通过 MyBatis-Plus 的分页插件自动实现分页逻辑。

#### 作用

作为服务层（Service）与数据库之间的桥梁，`FeedbackSolvingRecordMapper`封装了数据访问逻辑，服务层通过调用该接口的方法获取问题解决记录数据，无需直接处理 SQL，降低了业务逻辑与数据访问的耦合度。同时，自定义分页方法支持多条件组合查询，满足前端页面查看问题处理历史、筛选特定负责人记录等交互需求。



```xml
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- 定义Mapper接口的命名空间，与FeedbackSolvingRecordMapper接口的全类名对应 -->
<mapper namespace="com.natergy.ni.feedback.mapper.FeedbackSolvingRecordMapper">

    <!-- 通用查询映射结果：定义数据库字段与实体类属性的映射关系 -->
    <resultMap id="feedbackSolvingRecordResultMap" type="com.natergy.ni.feedback.entity.FeedbackSolvingRecordEntity">
        <result column="feedback_id" property="feedbackId"/>                  <!-- 关联的问题ID -->
        <result column="responsibility_person_id" property="responsibilityPersonId"/>  <!-- 负责人ID -->
        <result column="estimated_time" property="estimatedTime"/>            <!-- 预计解决时间 -->
        <result column="remarks" property="remarks"/>                        <!-- 备注信息 -->
        <result column="problem_cause" property="problemCause"/>              <!-- 问题原因分析 -->
        <result column="solution" property="solution"/>                      <!-- 解决方案 -->
        <result column="status" property="status"/>                          <!-- 解决记录状态 -->
        <result column="process_instance_id" property="processInstanceId"/>  <!-- 关联的流程实例ID -->
        <result column="responsibility_person_name" property="responsibilityPersonName"/>  <!-- 负责人姓名 -->
        <result column="id" property="id"/>                                  <!-- 主键ID -->
        <result column="update_time" property="updateTime"/>                  <!-- 更新时间 -->
        <result column="create_dept" property="createDept"/>                  <!-- 创建部门 -->
        <result column="is_deleted" property="isDeleted"/>                    <!-- 是否删除（逻辑删除标识） -->
        <result column="create_time" property="createTime"/>                  <!-- 创建时间 -->
        <result column="create_user" property="createUser"/>                  <!-- 创建人ID -->
        <result column="tenant_id" property="tenantId"/>                      <!-- 租户ID -->
        <result column="update_user" property="updateUser"/>                  <!-- 更新人ID -->
    </resultMap>

    <!-- 自定义分页查询：对应FeedbackSolvingRecordMapper接口的selectFeedbackSolvingRecordPage方法 -->
    <select id="selectFeedbackSolvingRecordPage" resultMap="feedbackSolvingRecordResultMap">
        <!-- 基础查询语句：查询未删除的问题解决记录 -->
        select * from ni_feedback_solving_record where is_deleted = 0
        <!-- 注：当前SQL未包含动态条件，实际使用中可能需要根据参数添加筛选逻辑，例如：
        <if test="feedbackSolvingRecord.feedbackId != null">
            AND feedback_id = #{feedbackSolvingRecord.feedbackId}
        </if>
        <if test="feedbackSolvingRecord.responsibilityPersonId != null">
            AND responsibility_person_id = #{feedbackSolvingRecord.responsibilityPersonId}
        </if>
        ORDER BY create_time DESC
        -->
    </select>

</mapper>
```

### XML 映射文件说明

该文件是`FeedbackSolvingRecordMapper`接口的 SQL 实现，定义了问题解决记录的查询逻辑，主要用于分页查询问题各负责人的解决记录列表。

#### 核心组件解析

1. **resultMap（结果映射）**：
   - `id="feedbackSolvingRecordResultMap"`：唯一标识该映射关系。
   - `type="FeedbackSolvingRecordEntity"`：指定映射的目标实体类，即问题解决记录的实体类。
   - 子标签`<result>`：定义数据库表字段（`column`）与实体类属性（`property`）的映射关系，解决字段名与属性名的命名差异（如数据库字段`feedback_id`对应实体类属性`feedbackId`）。
2. **select 标签（查询语句）**：
   - `id="selectFeedbackSolvingRecordPage"`：与`FeedbackSolvingRecordMapper`接口的`selectFeedbackSolvingRecordPage`方法对应，MyBatis 通过此 ID 关联接口方法与 SQL 语句。
   - `resultMap="feedbackSolvingRecordResultMap"`：指定查询结果使用上述定义的映射关系，将数据库查询结果封装为`FeedbackSolvingRecordEntity`对象。

#### SQL 逻辑说明

当前查询语句的核心逻辑是：

- 从表`ni_feedback_solving_record`中查询所有未删除的记录（`is_deleted = 0`，逻辑删除判断）。
- 未包含动态条件（如按问题 ID、负责人 ID 筛选等），但预留了扩展空间（注释中示例了可能的动态条件）。
- 结合接口参数中的`IPage`对象，MyBatis-Plus 的分页插件会自动拦截该查询，添加分页参数（如`LIMIT`），实现分页功能。

#### 扩展说明

实际业务中，该查询通常需要根据具体需求添加动态筛选条件，例如：

- 按关联的问题 ID（`feedback_id`）筛选，查询某个问题的所有解决记录。
- 按负责人 ID（`responsibility_person_id`）筛选，查询特定负责人的处理记录。
- 按状态（`status`）筛选，查询处于某一处理阶段的记录。
- 按时间范围（`create_time`）筛选，查询特定时间段内的记录。

这些筛选条件可通过 MyBatis 的动态 SQL 标签（如`<if>`）实现，使查询更灵活地适配不同业务场景。

该 XML 文件是问题解决记录模块数据查询的基础，通过结果映射确保数据正确封装，并支持分页查询，为前端展示问题处理历史提供数据支持。
import request from "@/router/axios";

export const getPage = (current, size, params) => {
  return request({
    url: "/api/ni/depot/stock/out/item/page",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};
export const getList = (params) => {
  return request({
    url: "/api/ni/depot/stock/out/item/list",
    method: "get",
    params,
  });
};
export const toVoid = (ids) => {
  return request({
    url: "/api/ni/depot/stock/out/item/toVoid",
    method: "get",
    params: {
      ids,
    },
  });
};

export const linkProject = (data) => {
  return request({
    url: "/api/ni/depot/stock/out/item/linkProject",
    method: "post",
    data,
  });
};
export const outProjectAudit = (params) => {
  return request({
    url: "/api/ni/depot/stock/out/item/outProjectAudit",
    method: "post",
    params,
  });
};
export const outConfirmById = (id) => {
  return request({
    url: "/api/ni/depot/stock/out/item/outConfirm",
    method: "post",
    params: {
      id,
    },
  });
};

export const getProjectIdList = () => {
  return request({
    url: "/api/ni/depot/stock/out/item/getProjectIdList",
    method: "post",
  });
};

export const setFixedAsset = (form) => {
  return request({
    url: "/api/ni/depot/stock/out/item/setFixedAsset",
    method: "post",
    data: form,
  });
};

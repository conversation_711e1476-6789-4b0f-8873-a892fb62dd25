import request from "@/router/axios";

export const getExpendItemList = (current, size, params, loanId) => {
  return request({
    url: "/api/ni/fin/loan/item/getExpendItemList",
    method: "get",
    params: {
      ...params,
      loanId,
      current,
      size,
    },
  });
};

// export const getDetail = (id) => {
//   return request({
//     url: "/api/ni/fin/loan/detail",
//     method: "get",
//     params: {
//       id,
//     },
//   });
// };

// export const remove = (ids) => {
//   return request({
//     url: "/api/ni/fin/loan/remove",
//     method: "post",
//     params: {
//       ids,
//     },
//   });
// };

// export const add = (row) => {
//   return request({
//     url: "/api/ni/fin/loan/save",
//     method: "post",
//     data: row,
//   });
// };

// export const update = (row) => {
//   return request({
//     url: "/api/ni/fin/loan/update",
//     method: "post",
//     data: row,
//   });
// };
export const submit = (form) => {
  return request({
    url: "/api/ni/fin/loan/item/submit",
    method: "post",
    params: {
      loanId: form.loanId,
    },
    data: form.item,
  });
};


export const getItemsDetail = (loanId) => {
  return request({
    url: "/api/ni/fin/loan/item/getItemsDetail",
    method: "get",
    params: {
      loanId
    },
  });
};

export const updateExpensedAmount = (loanId) => {
  return request({
    url: "/api/ni/fin/loan/item/updateExpensedAmount",
    method: "post",
    params: {
      loanId: loanId,
    },
  });
};




// export const getDetailByProcessInsId = (processInsId) => {
//   return request({
//     url: "/api/ni/fin/loan/detail",
//     method: "get",
//     params: {
//       processInsId,
//     },
//   });
// };

// export const updateLedgerId = (id, ledgerId) => {
//   return request({
//     url: "/api/ni/fin/loan/updateLedgerId",
//     method: "post",
//     params: {
//       id,
//       ledgerId,
//     },
//   });
// };

// export const payLoan = (row) => {
//   return request({
//     url: "/api/ni/fin/loan/payLoan",
//     method: "post",
//     data: row,
//   });
// };

import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/customerComplaint/customerComplaint/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/customerComplaint/customerComplaint/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/customerComplaint/customerComplaint/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/customerComplaint/customerComplaint/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/customerComplaint/customerComplaint/submit',
    method: 'post',
    data: row
  })
}

export const getListDetail = (current, size, params) => {
  return request({
    url: '/api/customerComplaint/customerComplaint/listDetail',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getListMasterDetail = (ids) => {
  return request({
    url: '/api/customerComplaint/customerComplaint/listMasterDetail',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const qualityFeedbackForm = (id, data) => {
  return request({
    url: '/api/customerComplaint/customerComplaint/qualityFeedbackForm',
    method: 'post',
    params: {
      id,
      data,
    },
    // data: data,
  })
}

export const getCustomerList1 = (current, size, params) => {
  return request({
    url: '/api/ni/old/customerInformation/customerList1',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getCustomerList2 = (current, size, params) => {
  return request({
    url: '/api/ni/old/customerInformation/customerList2',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :search.sync="query"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
      :cell-style="cellStyle"
      @cell-click="cellClick"
    >
      <template #menuLeft>
        <el-button
          type="primary"
          size="mini"
          icon="el-icon-plus"
          v-if="permission.voucher_add"
          @click="handleAdd"
        >
          新增
        </el-button>
        <el-button
          type="danger"
          size="mini"
          icon="el-icon-delete"
          plain
          v-if="permission.voucher_delete"
          @click="handleBatchDelete"
          >删 除
        </el-button>
        <el-button
          type="success"
          size="mini"
          icon="el-icon-check"
          plain
          @click="handleBatchAudit"
          >批量审核
        </el-button>
        <el-button
          type="warning"
          size="mini"
          icon="el-icon-upload"
          plain
          @click="handleBatchPosting"
          >批量过账
        </el-button>
        <el-button
          type="primary"
          size="mini"
          icon="el-icon-download"
          plain
          @click="handleExport"
          >导出
        </el-button>
      </template>
      <template #menu="{ row, index }">
        <el-button
          type="text"
          size="mini"
          icon="el-icon-edit"
          v-if="row.status === 1 && permission.voucher_edit"
          @click="handleEdit(row, index)"
          >编辑
        </el-button>
        <el-button
          type="text"
          size="mini"
          icon="el-icon-delete"
          style="color: #f56c6c"
          v-if="row.status === 1 && permission.voucher_delete"
          @click="$refs.crud.rowDel(row, index)"
          >删除
        </el-button>
        <el-button
          type="text"
          size="mini"
          icon="el-icon-s-promotion"
          style="color: #e6a23c"
          v-if="row.status === 1"
          @click="handleSubmit(row, index)"
          >提交审核
        </el-button>
        <el-button
          type="text"
          size="mini"
          icon="el-icon-s-check"
          style="color: #67c23a"
          v-if="row.status === 2"
          @click="handleAudit(row, index)"
          >审核
        </el-button>
        <el-button
          type="text"
          size="mini"
          icon="el-icon-s-check"
          style="color: #e6a23c"
          v-if="row.status === 2"
          @click="handleOverrule(row, index)"
          >驳回
        </el-button>
        <el-button
          type="text"
          size="mini"
          icon="el-icon-s-check"
          style="color: #f56c6c"
          v-if="row.status === 3"
          @click="handleAuditBack(row, index)"
          >反审核
        </el-button>
        <el-button
          type="text"
          size="mini"
          icon="el-icon-s-check"
          style="color: #67c23a"
          v-if="row.status === 3"
          @click="handlePosting(row, index)"
          >过账
        </el-button>
        <el-button
          type="text"
          size="mini"
          icon="el-icon-s-check"
          style="color: #f56c6c"
          v-if="row.status === 4"
          @click="handleRemoval(row, index)"
          >冲销
        </el-button>
        <el-button
          type="text"
          size="mini"
          icon="el-icon-s-check"
          style="color: #f56c6c"
          v-if="row.status === 3"
          @click="handleToVoid(row, index)"
          >作废
        </el-button>
      </template>
    </avue-crud>
    <voucher-dialog ref="voucherDialog" @confirm="onLoad(page)" />
    <attach-dialog ref="attachDialogRef" code="private" />
  </basic-container>
</template>

<script>
import {
  add,
  audit,
  auditBack,
  getDetail,
  getList,
  overrule,
  posting,
  removal,
  remove,
  submit, toVoid,
  update,
} from "@/api/ni/fin/voucher";
import { mapGetters } from "vuex";
import VoucherDialog from "@/views/ni/fin/components/VoucherDialog.vue";
import AttachDialog from "@/components/attach-dialog/index.vue";
import { fileLinkByBusinessKeys } from "@/api/resource/attach";
import { getMonthFirst, getMonthLast } from "@/util/date";

export default {
  components: { AttachDialog, VoucherDialog },
  data() {
    return {
      module: "ni_fin_voucher",
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        addBtn: false,
        editBtn: false,
        delBtn: false,
        searchEnter: true,
        labelWidth: 110,
        searchLabelWidth: 110,
        align: "center",
        span: 12,
        searchIndex: 3,
        searchIcon: true,
        size: "mini",
        searchSize: "mini",
        height: "auto",
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: false,
        selection: true,
        viewBtn: false,
        column: [
          {
            label: "会计日期",
            prop: "date",
            type: "date",
            search: true,
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            searchRange: true,
            placeholder: " ",
            minWidth: 100,
            rules: [
              {
                required: true,
                message: "请选择会计日期",
                trigger: "blur",
              },
            ],
          },
          {
            label: "凭证号",
            prop: "serialNo",
            minWidth: 120,
            overHidden: true,
            search: true,
          },
          {
            label: "摘要",
            prop: "summary",
            minWidth: 200,
            overHidden: true,
            search: true,
          },
          {
            label: "借方金额",
            prop: "borrowAmountStr",
            type: "number",
            controls: false,
            precision: 2,
            placeholder: " ",
          },
          {
            label: "贷方金额",
            prop: "loanAmountStr",
            type: "number",
            controls: false,
            precision: 2,
            placeholder: " ",
          },
          {
            label: "状态",
            prop: "status",
            type: "select",
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_fin_voucher_status",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            dataType: "number",
            addDisplay: false,
            editDisplay: false,
            search: true,
          },
          {
            label: "创建人",
            prop: "createUser",
            type: "select",
            remote: true,
            dicUrl: `/api/blade-user/search/otj/user?name={{key}}`,
            props: {
              label: "realName",
              value: "id",
            },
            dicFormatter: (data) => {
              return data.data.records;
            },
            search: true,
            placeholder: " ",
            hide: true,
            showColumn: false,
          },
          {
            label: "创建人",
            prop: "createUserName",
            addDisplay: false,
            editDisplay: false,
          },
          {
            label: "审核人",
            prop: "approverName",
            addDisplay: false,
            editDisplay: false,
          },
          {
            label: "来源系统",
            prop: "sourceSystem",
            type: "select",
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_fin_voucher_source_system",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            search: true,
            placeholder: " ",
          },
          {
            label: "附件数",
            prop: "attachNum",
            type: "number",
          },
        ],
      },
      data: [],
    };
  },
  computed: {
    ...mapGetters(["permission", "colorName", "userInfo"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.voucher_add, false),
        viewBtn: this.vaildData(this.permission.voucher_view, false),
        delBtn: this.vaildData(this.permission.voucher_delete, false),
        editBtn: this.vaildData(this.permission.voucher_edit, false),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  created() {
    this.query.date = [getMonthFirst(new Date()), getMonthLast(new Date())];
  },
  methods: {
    rowSave(row, done, loading) {
      add(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          window.console.log(error);
        }
      );
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          console.log(error);
        }
      );
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    handleAdd() {
      this.$refs.voucherDialog.onAdd();
    },
    handleEdit(row) {
      getDetail(row.id).then((res) => {
        if (res.data.data.status !== 1) {
          this.$message({
            type: "error",
            message: "请选择未审核的凭证!",
          });
          return;
        }
        this.$refs.voucherDialog.onEdit(res.data.data);
      });
    },
    handleSubmit(row) {
      this.$confirm("确定将当前数据提交审核?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return submit(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    handleOverrule(row) {
      this.$confirm("确定要驳回当前数据?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return overrule(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    handleToVoid(row) {
      this.$confirm("确定将当前数据作废?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return toVoid(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    handleRemoval(row) {
      this.$prompt("请输入冲销原因", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
      }).then(({ value }) => {
        if (!value) {
          this.$message({
            type: "error",
            message: "请输入冲销原因",
          });
          return;
        }
        removal(row.id, value).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
      });
    },
    handlePosting(row) {
      this.$confirm("确定将当前数据过账?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return posting(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    handleAuditBack(row) {
      this.$confirm("确定将反审核当前数据?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return auditBack(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    handleAudit(row) {
      this.$confirm("确定将审核当前数据?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return audit(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    handleExport() {
      this.$message({
        type: "success",
        message: "正在建设中!",
      });
    },
    handleBatchPosting() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      const unAuditStatus = this.selectionList.some(
        (item) => item.status !== 3
      );
      if (unAuditStatus) {
        this.$message.warning("选择数据存在不是已审核状态的数据，请重新选择");
        return;
      }
      this.$confirm("确定将当前选择的数据过账?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return posting(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    handleBatchAudit() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      const unWaitAuditStatus = this.selectionList.some(
        (item) => item.status !== 2
      );
      if (unWaitAuditStatus) {
        this.$message.warning("选择数据存在不是待审核的数据");
        return;
      }
      this.$confirm("确定要批量审核选择的数据吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return audit(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    handleBatchDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen(done, type) {
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then((res) => {
          this.form = res.data.data;
        });
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      const q = { ...params, ...this.query };
      if (q.date != null && q.date.length === 2) {
        q.startDate = q.date[0];
        q.endDate = q.date[1];
        q.date = null;
      }
      getList(page.currentPage, page.pageSize, q).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        data.records.forEach((item) => {
          item.attachNum = 0;
          if (item.borrowAmount != null)
            item.borrowAmountStr = Number(item.borrowAmount).toLocaleString(
              "zh-CN",
              {
                minimumFractionDigits: 2,
              }
            );
          if (item.loanAmount != null)
            item.loanAmountStr = Number(item.loanAmount).toLocaleString(
              "zh-CN",
              {
                minimumFractionDigits: 2,
              }
            );
        });
        this.data = data.records;
        if (this.data && this.data.length > 0) {
          const ids = this.data.map((item) => item.id);
          this.loadAttachNum(ids.join(","));
        }
        this.loading = false;
        this.selectionClear();
      });
    },
    loadAttachNum(ids) {
      if (!ids) return;
      fileLinkByBusinessKeys(ids, this.module).then((res) => {
        const data = res.data.data;
        const countMap = data.reduce((acc, cur) => {
          acc[cur.businessKey] = (acc[cur.businessKey] || 0) + 1;
          return acc;
        }, {});
        this.data = this.data.map((item) => ({
          ...item,
          attachNum: countMap[item.id] || 0,
        }));
      });
    },
    cellStyle({ row, column }) {
      if ("status" === column.columnKey && row.status) {
        switch (row.status) {
          case 1: // 草稿
            return { backgroundColor: "#a6a6a6", color: "#fff" };
          case 2: // 待审核
            return { backgroundColor: "#faad14", color: "#fff" };
          case 3: // 已审核
            return { backgroundColor: "#52c41a", color: "#fff" };
          case 4: // 已过账
            return { backgroundColor: "#1890ff", color: "#fff" };
          case 5: // 已冲销
            return { backgroundColor: "#ff4d4f", color: "#fff" };
          default:
            return {};
        }
      }
      if ("serialNo" === column.columnKey) {
        return {
          textDecoration: "underline",
          cursor: "pointer",
          color: this.colorName,
        };
      }
      if ("attachNum" === column.columnKey) {
        return {
          textDecoration: "underline",
          cursor: "pointer",
        };
      }
    },
    cellClick(row, column) {
      if (column.property === "attachNum") {
        this.$refs.attachDialogRef.init(row.id, this.module);
      }
      if (column.property === "serialNo") {
        getDetail(row.id).then((res) => {
          this.$refs.voucherDialog.onShow(res.data.data);
        });
      }
    },
  },
};
</script>

<style></style>

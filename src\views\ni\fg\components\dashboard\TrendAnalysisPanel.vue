<script>
import { dateFormat, getMonthFirst } from "@/util/date";

export default {
  name: "TrendAnalysisPanel",
  props: {
    value: {
      type: Object,
      default: () => {
        return {
          current: [],
          lastMonth: [],
          samePeriodLastYear: [],
        };
      },
    },
    trendPieData: {
      type: Array,
      default: () => {
        return [];
      },
    },
  },
  data() {
    return {
      resizeTime: 1,
      currentMonth: this.getThisMonth(),
      inventoryLine: null,
      inventoryPie: null,
      lineType: "num",
      dateRange: [],
      inventoryLineOption: {
        grid: {
          left: 20,
          containLabel: true,
          bottom: 30,
          top: 50,
          right: 30,
        },
        legend: {
          top: 5,
          x: "center",
          type: "scroll",
          padding: 5,
          data: [
            "本月-件数",
            "本月-重量",
            "上月-件数",
            "上月-重量",
            "去年同期-件数",
            "去年同期-重量",
          ],
        },
        xAxis: {
          type: "category",
          data: ["<PERSON>", "<PERSON><PERSON>", "Wed", "<PERSON>hu", "Fri", "Sat", "Sun"],
        },
        yAxis: [
          {
            type: "value",
            name: "件数",
            min: 0,
          },
          {
            type: "value",
            name: "重量",
            min: 0,
          },
        ],
        series: [
          {
            name: "本月-件数",
            type: "line",
            yAxisIndex: 0,
            data: [120, 132, 101, 134, 90, 230, 210],
          },
          {
            name: "本月-重量",
            type: "line",
            yAxisIndex: 1,
            data: [1200, 1320, 1010, 1340, 900, 2300, 2100],
          },
          {
            name: "上月-件数",
            type: "line",
            yAxisIndex: 0,
            data: [220, 182, 191, 234, 290, 330, 310],
          },
          {
            name: "上月-重量",
            type: "line",
            yAxisIndex: 1,
            data: [4500, 5500, 1910, 2340, 2900, 3300, 3100],
          },
          {
            name: "去年同期-件数",
            type: "line",
            yAxisIndex: 0,
            data: [210, 160, 191, 234, 220, 330, 330],
          },
          {
            name: "去年同期-重量",
            type: "line",
            yAxisIndex: 1,
            data: [2100, 1600, 1901, 2340, 2200, 3300, 3030],
          },
        ],
      },
      inventoryPieOption: {
        tooltip: {
          trigger: "item",
        },
        legend: {
          top: "5%",
          left: "center",
        },
        series: [
          {
            name: "重量",
            type: "pie",
            radius: "60%",
            label: {
              show: false,
              position: "center",
            },
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: "rgba(0, 0, 0, 0.5)",
              },
            },
            data: [],
          },
        ],
      },
    };
  },
  beforeDestroy() {
    if (this.inventoryLine) {
      this.inventoryLine.dispose();
      this.inventoryLine = null;
    }
    if (this.inventoryPie) {
      this.inventoryPie.dispose();
      this.inventoryPie = null;
    }
  },
  mounted() {
    this.dateRange = [
      getMonthFirst(new Date()),
      dateFormat(new Date(), "yyyy-MM-dd"),
    ];
    this.renderInit();
    this.getInventoryLine(this.dateRange);
    // 确保图表尺寸也得到更新
    window.addEventListener("resize", this.updateContainer, false);
  },
  watch: {
    value: {
      deep: true,
      handler(newVal) {
        if (newVal && newVal.current) {
          this.$nextTick(() => {
            this.renderLine();
          });
        }
      },
    },
    trendPieData: {
      deep: true,
      handler(newVal) {
        if (newVal) {
          this.$nextTick(() => {
            this.renderPie();
          });
        }
      },
    },
  },
  methods: {
    reload() {
      this.getInventoryLine(this.dateRange);
    },
    getInventoryLine(dateRange) {
      const [start, end] = dateRange;
      this.$emit("on-load", start, end);
    },
    getThisMonth(checkedValues = "") {
      let date;
      if (!checkedValues || checkedValues.length === 0) {
        date = new Date();
        return `${date.getFullYear()}-${date.getMonth() + 1}`;
      }
      date = new Date(checkedValues[0]);
      const date2 = new Date(checkedValues[1]);
      const startMonth =
        date.getMonth() + 1 > 9
          ? date.getMonth() + 1
          : `0${date.getMonth() + 1}`;
      const endMonth =
        date2.getMonth() + 1 > 9
          ? date2.getMonth() + 1
          : `0${date2.getMonth() + 1}`;

      return `${date.getFullYear()}-${startMonth}  至  ${date2.getFullYear()}-${endMonth}`;
    },
    renderInit() {
      if (!this.inventoryLine) {
        this.inventoryLineContainer = document.getElementById(
          "inventoryLineContainer"
        );
        this.inventoryLine = this.$echarts.init(this.inventoryLineContainer);
      }
      if (!this.inventoryPie) {
        this.inventoryPieContainer = document.getElementById(
          "inventoryPieContainer"
        );
        this.inventoryPie = this.$echarts.init(this.inventoryPieContainer);
      }
    },
    renderLine() {
      if (!this.inventoryLine) {
        this.inventoryLineContainer = document.getElementById(
          "inventoryLineContainer"
        );
        this.inventoryLine = this.$echarts.init(this.inventoryLineContainer);
      }
      // 提取日期作为 X 轴数据
      const { current, lastMonth, samePeriodLastYear } = this.value;
      let xAxisData = [];
      if (current) xAxisData = current.map((item) => item.date);

      // 提取 num 和 weight 数据
      const currentNum = current.map((item) => item.num);
      const currentWeight = current.map((item) => item.weight);

      // 上月数据（假设 this.trendData.lastMonth 已赋值）
      const lastMonthNum = lastMonth.map((item) => item.num);
      const lastMonthWeight = lastMonth.map((item) => item.weight);

      // 去年同期数据（假设 this.trendData.samePeriodLastYear 已赋值）
      const samePeriodNum = samePeriodLastYear.map((item) => item.num);
      const samePeriodWeight = samePeriodLastYear.map((item) => item.weight);
      const option = {
        tooltip: {
          trigger: "axis",
        },
        grid: this.inventoryLineOption.grid,
        legend: this.inventoryLineOption.legend,
        yAxis: this.inventoryLineOption.yAxis,
        xAxis: {
          type: "category",
          data: xAxisData, // 动态X轴
        },
        series: [
          { name: "本月-件数", type: "line", yAxisIndex: 0, data: currentNum },
          {
            name: "本月-重量",
            type: "line",
            yAxisIndex: 1,
            data: currentWeight,
          },
          {
            name: "上月-件数",
            type: "line",
            yAxisIndex: 0,
            data: lastMonthNum,
          },
          {
            name: "上月-重量",
            type: "line",
            yAxisIndex: 1,
            data: lastMonthWeight,
          },
          {
            name: "去年同期-件数",
            type: "line",
            yAxisIndex: 0,
            data: samePeriodNum,
          },
          {
            name: "去年同期-重量",
            type: "line",
            yAxisIndex: 1,
            data: samePeriodWeight,
          },
        ],
      };
      this.inventoryLine.setOption(option);
    },
    renderPie() {
      // 走势
      if (!this.inventoryPie) {
        this.inventoryPieContainer = document.getElementById(
          "inventoryPieContainer"
        );
        this.inventoryPie = this.$echarts.init(this.inventoryPieContainer);
      }
      const data = this.trendPieData.map((item) => {
        return {
          name: item.key,
          value: item.value,
        };
      });
      const option = {
        tooltip: this.inventoryPieOption.tooltip,
        legend: this.inventoryPieOption.legend,
        series: [
          {
            name: "重量(kg)",
            type: "pie",
            radius: "70%",
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: "center",
            },
            emphasis: {
              label: {
                show: true,
                fontSize: 25,
              },
            },
            labelLine: {
              show: false,
            },
            data,
          },
        ],
      };
      this.inventoryPie.setOption(option);
    },
    updateContainer() {
      if (
        document.documentElement.clientWidth >= 1400 &&
        document.documentElement.clientWidth < 1920
      ) {
        this.resizeTime = (document.documentElement.clientWidth / 2080).toFixed(
          2
        );
      } else if (document.documentElement.clientWidth < 1080) {
        this.resizeTime = (document.documentElement.clientWidth / 1080).toFixed(
          2
        );
      } else {
        this.resizeTime = 1;
      }

      this.inventoryPie.resize({
        // 根据父容器的大小设置大小
        width: `${this.resizeTime * 326}px`,
        height: `${this.resizeTime * 326}px`,
      });

      this.inventoryLine.resize({
        // 根据父容器的大小设置大小
        width: this.inventoryLineContainer.clientWidth,
        height: `${this.resizeTime * 326}px`,
      });
    },
  },
};
</script>

<template>
  <el-row :gutter="20">
    <el-col :xs="24" :sm="24" :md="18" :lg="18" :xl="18">
      <el-card
        shadow="never"
        :body-style="{ padding: '10px 20px' }"
        :class="{
          'board-item': true,
        }"
      >
        <div slot="header" class="clearfix">
          <span>库存趋势</span>
          <el-date-picker
            style="float: right; padding: 3px 0"
            v-model="dateRange"
            size="mini"
            type="daterange"
            value-format="yyyy-MM-dd"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="getInventoryLine"
          >
          </el-date-picker>
        </div>
        <div
          id="inventoryLineContainer"
          :style="{ width: '100%', height: `${resizeTime * 326}px` }"
        ></div>
      </el-card>
    </el-col>
    <el-col :xs="24" :sm="24" :md="6" :lg="6" :xl="6">
      <el-card
        shadow="never"
        :body-style="{ padding: '10px 20px' }"
        :class="{
          'board-item': true,
        }"
      >
        <div slot="header" class="clearfix">
          <span>库存情况</span>
        </div>
        <div
          id="inventoryPieContainer"
          :style="{
            width: `${resizeTime * 326}px`,
            height: `${resizeTime * 326}px`,
            margin: '0 auto',
          }"
        ></div>
      </el-card>
    </el-col>
  </el-row>
</template>

<style lang="scss" scoped>
.board-item {
  padding: 8px;

  /deep/ .el-card__header {
    color: #303133;
    font-size: 20px;
    font-weight: 500;
    border-bottom: 0;
    padding: 16px 18px !important;
  }
}
</style>

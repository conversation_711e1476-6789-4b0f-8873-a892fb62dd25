```java
package com.natergy.ni.feedback.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SearchResponseDTO {
	/**
	 * 日期
	 */
	private String date;

	/**
	 * 部门集合
	 */
	private List<String> department;

	/**
	 * 问题类型
	 */
	@JsonProperty("issue_category")
	private String issueCategory;

	/**
	 * 问题原因
	 */
	@JsonProperty("issue_cause")
	private String issueCause;

	/**
	 * 问题描述
	 */
	@JsonProperty("issue_description")
	private String issueDescription;

	/**
	 * 主键id
	 */
	@JsonProperty("point_id")
	private Integer pointId;

	/**
	 * 负责人 responsiblePerson
	 */
	@JsonProperty("responsible_Person")
	private List<String> responsiblePerson;

	/**
	 * 相似度分数
	 */
	private Double score;

	/**
	 * 解决方案
	 */
	private String solution;
}
```


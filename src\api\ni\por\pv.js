import request from "@/router/axios";

export const getDetail = (params) => {
  return request({
    url: "/api/ni/por/pv/detail",
    method: "get",
    params
  });
};

export const remove = (ids) => {
  return request({
    url: "/api/ni/por/pv/remove",
    method: "post",
    params: {
      ids,
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/ni/por/pv/save",
    method: "post",
    data: row,
  });
};

export const update = (row) => {
  return request({
    url: "/api/ni/por/pv/update",
    method: "post",
    data: row,
  });
};

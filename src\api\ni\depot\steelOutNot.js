import request from "@/router/axios";

export const getList = (current, size, params) => {
  return request({
    url: "/api/ni/depot/stock/steel/out/not/list",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/ni/depot/stock/steel/out/not/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const remove = (ids,needUpdateCurrentStock=true) => {
  return request({
    url: "/api/ni/depot/stock/steel/out/not/remove",
    method: "post",
    params: {
      ids,
      needUpdateCurrentStock
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/ni/depot/stock/steel/out/not/submit",
    method: "post",
    data: row,
  });
};

export const update = (row) => {
  return request({
    url: "/api/ni/depot/stock/steel/out/not/submit",
    method: "post",
    data: row,
  });
};
export const adjust = (ids,status) => {
  return request({
    url: "/api/ni/depot/stock/steel/out/not/adjust",
    method: "post",
    params: {
      ids,
      status
    },
  });
};

export const buildDepotSteelOut = (ids) => {
  return request({
    url: "/api/ni/depot/stock/steel/out/not/buildDepotSteelOut",
    method: "post",
    params: {
      ids,
    },
  });
};

export const steelOut = (ids,status) => {
  return request({
    url: "/api/ni/depot/stock/steel/out/not/steelOut",
    method: "post",
    params: {
      ids,
      status
    },
  });
};

export const getCurrentStockCount = (params) => {
  return request({
    url: "/api/ni/depot/currentStock/detail",
    method: "get",
    params,
  });
};

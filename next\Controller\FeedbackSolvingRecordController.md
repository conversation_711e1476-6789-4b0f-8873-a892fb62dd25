```java
// 定义包路径，属于问题反馈模块的解决记录控制器层
package com.natergy.ni.feedback.controller;

// 导入MyBatis-Plus的查询和更新包装类
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
// 导入Knife4j的API文档注解
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
// 导入当前模块的实体类
import com.natergy.ni.feedback.entity.FeedbackEntity;
import com.natergy.ni.feedback.entity.FeedbackSolvingRecordEntity;
// 导入当前模块的枚举类（解决记录状态和反馈状态）
import com.natergy.ni.feedback.enums.FeedbackSolvingRecordStatusEnum;
import com.natergy.ni.feedback.enums.FeedbackStatusEnum;
// 导入当前模块的服务接口
import com.natergy.ni.feedback.service.IFeedbackService;
import com.natergy.ni.feedback.service.IFeedbackSolvingRecordService;
// 导入当前模块的VO类（视图对象）
import com.natergy.ni.feedback.vo.FeedbackSolvingRecordVO;
// 导入当前模块的包装类（用于实体与VO的转换）
import com.natergy.ni.feedback.wrapper.FeedbackSolvingRecordWrapper;
// 导入Swagger的API注解
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
// 导入Lombok注解（构造函数注入）
import lombok.AllArgsConstructor;
// 导入Apache的Bean工具类（属性拷贝）
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang3.StringUtils;
// 导入Flowable工作流的运行时服务
import org.flowable.engine.RuntimeService;
// 导入JetBrains的非空注解
import org.jetbrains.annotations.NotNull;
// 导入BladeX框架的用户缓存工具
import org.springblade.common.cache.UserCache;
// 导入BladeX框架的基础控制器类
import org.springblade.core.boot.ctrl.BladeController;
// 导入BladeX框架的MyBatis-Plus支持类
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
// 导入BladeX框架的安全工具类（获取当前用户信息）
import org.springblade.core.secure.utils.AuthUtil;
// 导入BladeX框架的API响应类
import org.springblade.core.tool.api.R;
// 导入BladeX框架的常量类
import org.springblade.core.tool.constant.BladeConstant;
// 导入BladeX框架的工具类（字符串转换等）
import org.springblade.core.tool.utils.Func;
// 导入系统模块的用户实体类
import org.springblade.modules.system.entity.User;
// 导入BladeX工作流插件的工具类
import org.springblade.plugin.workflow.core.utils.ObjectUtil;
import org.springblade.plugin.workflow.core.utils.WfTaskUtil;
// 导入BladeX工作流插件的流程模型和服务
import org.springblade.plugin.workflow.process.model.WfProcess;
import org.springblade.plugin.workflow.process.service.IWfProcessService;
// 导入Spring的事务注解
import org.springframework.transaction.annotation.Transactional;
// 导入Spring的Web注解
import org.springframework.web.bind.annotation.*;

// 导入Java验证注解
import javax.validation.Valid;
// 导入Java反射相关异常类
import java.lang.reflect.InvocationTargetException;
// 导入Java工具类
import java.util.*;
// 导入Java流相关类
import java.util.stream.Collectors;

/**
 * 问题各负责人解决记录 控制器
 *
 * <AUTHOR>
 * @since 2025-02-27
 */
// 标识为REST控制器
@RestController
// 构造函数注入所有依赖
@AllArgsConstructor
// 定义请求路径
@RequestMapping("feedback/feedbackSolvingRecord")
// Swagger文档注解，说明该控制器的作用
@Api(value = "问题各负责人解决记录", tags = "问题各负责人解决记录接口")
// 继承BladeX框架的基础控制器，获得基础功能
public class FeedbackSolvingRecordController extends BladeController {

	// 注入问题解决记录服务
	private final IFeedbackSolvingRecordService feedbackSolvingRecordService;

	// 注入问题反馈服务
	private final IFeedbackService feedbackService;

	// 注入Flowable的运行时服务（操作工作流变量和实例）
	private final RuntimeService runtimeService;

	// 注入工作流流程服务（完成任务、转办等操作）
	private final IWfProcessService processService;

	// 定义字符串分隔符（用于多ID/名称拼接）
	private final static String SEPARATOR = ",";

	/**
	 * 问题各负责人解决记录 详情
	 */
	// 定义GET请求路径
	@GetMapping("/detail")
	// Swagger接口排序注解
	@ApiOperationSupport(order = 1)
	// Swagger接口说明注解
	@ApiOperation(value = "详情", notes = "传入feedbackSolvingRecord")
	// 方法参数为解决记录实体（用于接收请求参数）
	public R<FeedbackSolvingRecordVO> detail(FeedbackSolvingRecordEntity feedbackSolvingRecord) {
		// 调用服务层方法获取解决记录详情
		FeedbackSolvingRecordEntity detail = feedbackSolvingRecordService.getOne(Condition.getQueryWrapper(feedbackSolvingRecord));
		// 将实体转换为VO并返回成功响应
		return R.data(FeedbackSolvingRecordWrapper.build().entityVO(detail));
	}

	/**
	 * 问题各负责人解决记录 分页
	 */
	// 定义GET请求路径
	@GetMapping("/list")
	// Swagger接口排序注解
	@ApiOperationSupport(order = 2)
	// Swagger接口说明注解
	@ApiOperation(value = "分页", notes = "传入feedbackSolvingRecord")
	// 方法参数为解决记录实体和分页查询对象
	public R<IPage<FeedbackSolvingRecordVO>> list(FeedbackSolvingRecordEntity feedbackSolvingRecord, Query query) {
		// 调用服务层方法获取分页数据（实体）
		IPage<FeedbackSolvingRecordEntity> pages = feedbackSolvingRecordService.page(Condition.getPage(query), Condition.getQueryWrapper(feedbackSolvingRecord));
		// 将实体分页对象转换为VO分页对象并返回
		return R.data(FeedbackSolvingRecordWrapper.build().pageVO(pages));
	}

	/**
	 * 问题各负责人解决记录 自定义分页
	 */
	// 定义GET请求路径
	@GetMapping("/page")
	// Swagger接口排序注解
	@ApiOperationSupport(order = 3)
	// Swagger接口说明注解
	@ApiOperation(value = "分页", notes = "传入feedbackSolvingRecord")
	// 方法参数为解决记录VO和分页查询对象（支持更灵活的查询条件）
	public R<IPage<FeedbackSolvingRecordVO>> page(FeedbackSolvingRecordVO feedbackSolvingRecord, Query query) {
		// 调用服务层自定义分页查询方法（直接返回VO分页）
		IPage<FeedbackSolvingRecordVO> pages = feedbackSolvingRecordService.selectFeedbackSolvingRecordPage(Condition.getPage(query), feedbackSolvingRecord);
		// 返回分页数据
		return R.data(pages);
	}

	/**
	 * 问题各负责人解决记录 修改
	 */
	// 定义POST请求路径
	@PostMapping("/update")
	// Swagger接口排序注解
	@ApiOperationSupport(order = 5)
	// Swagger接口说明注解
	@ApiOperation(value = "修改", notes = "传入feedbackSolvingRecord")
	// 接收请求体中的解决记录实体，并进行参数验证
	public R update(@Valid @RequestBody FeedbackSolvingRecordEntity feedbackSolvingRecord) {
		// 调用服务层更新方法，并返回操作结果
		return R.status(feedbackSolvingRecordService.updateById(feedbackSolvingRecord));
	}

	/**
	 * 问题各负责人解决记录 新增或修改
	 */
	// 定义POST请求路径
	@PostMapping("/submit")
	// Swagger接口排序注解
	@ApiOperationSupport(order = 6)
	// Swagger接口说明注解
	@ApiOperation(value = "新增或修改", notes = "传入feedbackSolvingRecord")
	// 接收请求体中的解决记录实体，并进行参数验证
	public R submit(@Valid @RequestBody FeedbackSolvingRecordEntity feedbackSolvingRecord) {
		// 调用服务层保存或更新方法，并返回操作结果
		return R.status(feedbackSolvingRecordService.saveOrUpdate(feedbackSolvingRecord));
	}

	/**
	 * 问题各负责人解决记录 删除
	 */
	// 定义POST请求路径
	@PostMapping("/remove")
	// Swagger接口排序注解
	@ApiOperationSupport(order = 7)
	// Swagger接口说明注解
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	// 接收请求参数中的ids字符串（多个ID用逗号分隔）
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		// 将ids字符串转换为Long列表，并调用服务层逻辑删除方法
		return R.status(feedbackSolvingRecordService.deleteLogic(Func.toLongList(ids)));
	}

	/**
	 * 认领问题
	 */
	// 定义POST请求路径
	@PostMapping("/feedbackClaimed")
	// Swagger接口排序注解
	@ApiOperationSupport(order = 4)
	// Swagger接口说明注解
	@ApiOperation(value = "认领问题", notes = "传入feedbackSolvingRecord")
	// 声明事务（发生异常时回滚）
	@Transactional(rollbackFor = Exception.class)
	// 接收请求体中的解决记录实体，并进行参数验证
	public R feedbackClaimed(@Valid @RequestBody FeedbackSolvingRecordEntity feedbackSolvingRecord) {
		// 获取当前登录用户ID
		Long userId = AuthUtil.getUserId();
		// 获取当前登录用户账号
		String userAccount = AuthUtil.getUserAccount();

		// 设置认领人ID、姓名和状态（已认领处理中）
		feedbackSolvingRecord.setResponsibilityPersonId(userId);
		feedbackSolvingRecord.setResponsibilityPersonName(userAccount);
		feedbackSolvingRecord.setStatus(FeedbackSolvingRecordStatusEnum.CLAIMED_AND_PROCESSED.getValue());

		// 保存认领记录
		feedbackSolvingRecordService.save(feedbackSolvingRecord);

		// 从工作流中获取当前流程实例的"claimList"变量（存储认领记录）
		Object claimList = runtimeService.getVariable(feedbackSolvingRecord.getProcessInstanceId(), "claimList");

		// 将获取到的变量转换为解决记录VO列表
		List<FeedbackSolvingRecordVO> solvingRecordList = getFeedbackSolvingRecordVOS((List<?>) claimList);

		// 创建新的解决记录VO，并拷贝实体属性
		FeedbackSolvingRecordVO solvingRecordVO = new FeedbackSolvingRecordVO();
		try {
			BeanUtils.copyProperties(solvingRecordVO, feedbackSolvingRecord);
		} catch (IllegalAccessException | InvocationTargetException e) {
			throw new RuntimeException(e);
		}

		// 将新认领记录添加到列表，并更新工作流变量
		solvingRecordList.add(solvingRecordVO);
		runtimeService.setVariable(feedbackSolvingRecord.getProcessInstanceId(), "claimList", solvingRecordList);

		// 更新问题反馈的状态为"认领处理中"
		Long feedbackId = feedbackSolvingRecord.getFeedbackId();
		feedbackService.update(new LambdaUpdateWrapper<FeedbackEntity>()
			.eq(FeedbackEntity::getId, feedbackId)
			.set(FeedbackEntity::getStatus, FeedbackStatusEnum.CLAIMED_AND_PROCESSED.getValue())
		);

		// 返回成功响应
		return R.success("认领成功");
	}

	/**
	 * 获取问题解决记录的状态
	 */
	// 定义GET请求路径
	@GetMapping("/getFeedbackSolvingRecordStatus")
	// Swagger接口排序注解
	@ApiOperationSupport(order = 1)
	// Swagger接口说明注解
	@ApiOperation(value = "问题各负责人解决记录 状态", notes = "传入feedbackSolvingRecord")
	// 方法参数为解决记录实体（包含查询条件）
	public R<Integer> getFeedbackSolvingRecordStatus(FeedbackSolvingRecordEntity feedbackSolvingRecord) {
		// 查询当前用户在指定流程实例中对该问题的解决记录状态
		FeedbackSolvingRecordEntity detail = feedbackSolvingRecordService.getOne(new LambdaQueryWrapper<FeedbackSolvingRecordEntity>()
			.select(FeedbackSolvingRecordEntity::getStatus)  // 只查询状态字段
			.eq(FeedbackSolvingRecordEntity::getFeedbackId, feedbackSolvingRecord.getFeedbackId())
			.eq(FeedbackSolvingRecordEntity::getProcessInstanceId, feedbackSolvingRecord.getProcessInstanceId())
			.eq(FeedbackSolvingRecordEntity::getResponsibilityPersonId, AuthUtil.getUserId())
		);
		// 返回状态（如果不存在则返回0）
		return R.data(Optional.ofNullable(detail)
			.map(FeedbackSolvingRecordEntity::getStatus)
			.orElse(0));
	}

	/**
	 * 解决问题
	 */
	// 定义POST请求路径
	@PostMapping("/feedbackSolve")
	// Swagger接口排序注解
	@ApiOperationSupport(order = 4)
	// Swagger接口说明注解
	@ApiOperation(value = "解决问题", notes = "传入feedbackSolvingRecord")
	// 声明事务
	@Transactional(rollbackFor = Exception.class)
	// 接收请求体中的解决记录VO，并进行参数验证
	public R feedbackSolve(@Valid @RequestBody FeedbackSolvingRecordVO feedbackSolvingRecord) {
		// 从工作流中获取当前流程实例的"claimList"变量
		Object claimList = runtimeService.getVariable(feedbackSolvingRecord.getProcessInstanceId(), "claimList");

		// 转换为解决记录VO列表
		List<FeedbackSolvingRecordVO> solvingRecordList = getFeedbackSolvingRecordVOS((List<?>) claimList);

		// 查找当前用户的认领记录并更新解决信息
		Optional<FeedbackSolvingRecordVO> solvingRecordOption = solvingRecordList.stream()
			.filter(record -> Objects.equals(record.getResponsibilityPersonId(), AuthUtil.getUserId()))
			.findFirst();

		// 更新解决日期、原因、方案和状态（问题已解决）
		solvingRecordOption.ifPresent(record -> {
			record.setResolveDate(feedbackSolvingRecord.getResolveDate());
			record.setProblemCause(feedbackSolvingRecord.getProblemCause());
			record.setSolution(feedbackSolvingRecord.getSolution());
			record.setStatus(FeedbackSolvingRecordStatusEnum.ISSUE_HAS_RESOLVED.getValue());
		});

		// 更新数据库中的解决记录
		feedbackSolvingRecordService.update(new LambdaUpdateWrapper<FeedbackSolvingRecordEntity>()
			.eq(FeedbackSolvingRecordEntity::getProcessInstanceId, feedbackSolvingRecord.getProcessInstanceId())
			.eq(FeedbackSolvingRecordEntity::getResponsibilityPersonId, AuthUtil.getUserId())
			.set(FeedbackSolvingRecordEntity::getResolveDate, feedbackSolvingRecord.getResolveDate())
			.set(FeedbackSolvingRecordEntity::getProblemCause, feedbackSolvingRecord.getProblemCause())
			.set(FeedbackSolvingRecordEntity::getSolution, feedbackSolvingRecord.getSolution())
			.set(FeedbackSolvingRecordEntity::getStatus, FeedbackSolvingRecordStatusEnum.ISSUE_HAS_RESOLVED.getValue())
		);

		// 更新工作流中的"claimList"变量
		runtimeService.setVariable(feedbackSolvingRecord.getProcessInstanceId(), "claimList", solvingRecordList);

		// 根据是否完全解决，决定完成当前任务还是整个流程
		if (feedbackSolvingRecord.isSolveFinsh()) {
			processService.completeAllTask(feedbackSolvingRecord.getProcessInstanceId());  // 完成所有任务
		} else {
			processService.completeTask(feedbackSolvingRecord.getProcess());  // 完成当前任务
		}

		// 返回成功响应
		return R.success("提交成功");
	}

	/**
	 * 将通用列表转换为FeedbackSolvingRecordVO列表（处理类型转换）
	 */
	@NotNull
	private static List<FeedbackSolvingRecordVO> getFeedbackSolvingRecordVOS(List<?> list) {
		// 如果列表为空，返回空列表
				if (Objects.isNull(list)) {
			return new ArrayList<>();
		}

		// 判断列表元素是否为FeedbackSolvingRecordVO类型
		boolean isFeedbackSolvingRecordVO = false;
		if (!list.isEmpty()) {
			Object firstElement = list.get(0);
			isFeedbackSolvingRecordVO = firstElement instanceof FeedbackSolvingRecordVO;
		}

		List<FeedbackSolvingRecordVO> solvingRecordList;

		// 如果已是FeedbackSolvingRecordVO类型，直接转换
		if (isFeedbackSolvingRecordVO) {
			solvingRecordList = Optional.of(list)
				.map(obj -> (List<FeedbackSolvingRecordVO>) obj)
				.orElse(new ArrayList<>());
		} else {
			// 否则将LinkedHashMap转换为FeedbackSolvingRecordVO
			List<FeedbackSolvingRecordVO> resultList = new ArrayList<>();
			List<LinkedHashMap> linkedHashMaps = Optional.of(list)
				.map(obj -> (List<LinkedHashMap>) obj)
				.orElse(new ArrayList<>());

			for (LinkedHashMap<String, Object> map : linkedHashMaps) {
				try {
					FeedbackSolvingRecordVO vo = new FeedbackSolvingRecordVO();
					// 使用BeanUtils自动映射属性
					BeanUtils.populate(vo, map);
					resultList.add(vo);
				} catch (Exception e) {
					// 处理转换异常
					e.printStackTrace();
				}
			}
			solvingRecordList = resultList;
		}
		return solvingRecordList;
	}

	/**
	 * 转办问题
	 */
	@PostMapping("/transferOffice")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "转办问题", notes = "传入feedbackSolvingRecord")
	@Transactional(rollbackFor = Exception.class)
	public R transferOffice(@Valid @RequestBody FeedbackSolvingRecordVO feedbackSolvingRecord) {
		// 将接收的转办目标用户ID字符串拆分为Long列表
		List<Long> replaceWhomUserIds = Arrays.stream(feedbackSolvingRecord.getReplaceWhomUserIds().split(SEPARATOR))
			.map(Long::valueOf)
			.collect(Collectors.toList());

		// 根据用户ID获取用户名列表
		List<String> replaceWhomUserNames = replaceWhomUserIds.stream()
			.map(UserCache::getUser)  // 从缓存获取用户信息
			.map(User::getAccount)    // 提取用户名
			.collect(Collectors.toList());

		// 获取被替换的用户名和ID
		String replaceUserName = feedbackSolvingRecord.getReplaceUser();
		User replaceUserInfo = UserCache.getUser(BladeConstant.ADMIN_TENANT_ID, replaceUserName);
		Long replaceUserId = replaceUserInfo.getId();

		// 查询当前流程实例对应的问题反馈
		FeedbackEntity feedbackEntity = feedbackService.getOne(new LambdaQueryWrapper<FeedbackEntity>()
			.eq(FeedbackEntity::getProcessInstanceId, feedbackSolvingRecord.getProcessInstanceId())
		);

		// 更新问题反馈的负责人ID和姓名（移除被替换用户，添加新用户）
		String responsibilityNames = feedbackEntity.getResponsibilityName();
		String responsibilityIds = feedbackEntity.getResponsibility();

		// 处理负责人ID列表：移除被替换用户ID，添加新用户ID
		List<String> responsibilityIdCollect = Arrays.stream(responsibilityIds.split(SEPARATOR))
			.filter(item -> !Objects.equals(item, replaceUserId.toString()))
			.collect(Collectors.toList());
		responsibilityIdCollect.addAll(replaceWhomUserIds.stream()
			.map(Object::toString)
			.collect(Collectors.toList()));
		responsibilityIds = StringUtils.join(responsibilityIdCollect, SEPARATOR);

		// 处理负责人姓名列表：移除被替换用户名，添加新用户名
		List<String> responsibilityNameCollect = Arrays.stream(responsibilityNames.split(SEPARATOR))
			.filter(item -> !Objects.equals(item, replaceUserName))
			.collect(Collectors.toList());
		responsibilityNameCollect.addAll(replaceWhomUserNames);
		responsibilityNames = StringUtils.join(responsibilityNameCollect, SEPARATOR);

		// 更新工作流变量中的负责人信息
		runtimeService.setVariable(feedbackSolvingRecord.getProcessInstanceId(), "responsibility", responsibilityIds);

		// 更新问题反馈实体并保存
		feedbackEntity.setResponsibilityName(responsibilityNames);
		feedbackEntity.setResponsibility(responsibilityIds);
		feedbackService.updateById(feedbackEntity);

		// 添加多实例任务（工作流相关）
		WfProcess process = feedbackSolvingRecord.getProcess();
		processService.addMultiInstance(process);

		// 处理转办任务的评论信息
		String[] toUserIdArray = process.getAssignee().split(SEPARATOR);
		String accountCollectStr = Arrays.stream(toUserIdArray)
			.map(item -> UserCache.getUser(Long.valueOf(item)))
			.map(User::getAccount)
			.collect(Collectors.joining(SEPARATOR));

		// 记录转办评论并完成当前任务
		String taskUserName = WfTaskUtil.getNickName();
		process.setPass(true);
		process.setComment(taskUserName + "]将任务转办至 -> [" + accountCollectStr + "]：" + process.getComment());
		processService.completeTask(process);

		return R.success("转办成功");
	}

	/**
	 * 非我问题（拒绝处理并转办）
	 */
	@PostMapping("/notSelfTask")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "非我问题", notes = "传入feedbackSolvingRecord")
	@Transactional(rollbackFor = Exception.class)
	public R notSelfTask(@Valid @RequestBody FeedbackSolvingRecordVO feedbackSolvingRecord) {
		WfProcess process = feedbackSolvingRecord.getProcess();
		// 验证参数
		if (ObjectUtil.isAnyEmpty(process.getTaskId(), process.getAssignee())) {
			return R.fail("参数错误");
		}

		// 解析转办目标用户ID和姓名（逻辑同转办问题）
		List<Long> replaceWhomUserIds = Arrays.stream(feedbackSolvingRecord.getReplaceWhomUserIds().split(SEPARATOR))
			.map(Long::valueOf)
			.collect(Collectors.toList());
		List<String> replaceWhomUserNames = replaceWhomUserIds.stream()
			.map(UserCache::getUser)
			.map(User::getAccount)
			.collect(Collectors.toList());

		String replaceUserName = feedbackSolvingRecord.getReplaceUser();
		User replaceUserInfo = UserCache.getUser(BladeConstant.ADMIN_TENANT_ID, replaceUserName);
		Long replaceUserId = replaceUserInfo.getId();

		// 更新问题反馈的负责人信息（逻辑同转办问题）
		FeedbackEntity feedbackEntity = feedbackService.getOne(new LambdaQueryWrapper<FeedbackEntity>()
			.eq(FeedbackEntity::getProcessInstanceId, feedbackSolvingRecord.getProcessInstanceId())
		);
		String responsibilityNames = feedbackEntity.getResponsibilityName();
		String responsibilityIds = feedbackEntity.getResponsibility();

		List<String> responsibilityIdCollect = Arrays.stream(responsibilityIds.split(SEPARATOR))
			.filter(item -> !Objects.equals(item, replaceUserId.toString()))
			.collect(Collectors.toList());
		responsibilityIdCollect.addAll(replaceWhomUserIds.stream()
			.map(Object::toString)
			.collect(Collectors.toList()));
		responsibilityIds = StringUtils.join(responsibilityIdCollect, SEPARATOR);

		List<String> responsibilityNameCollect = Arrays.stream(responsibilityNames.split(SEPARATOR))
			.filter(item -> !Objects.equals(item, replaceUserName))
			.collect(Collectors.toList());
		responsibilityNameCollect.addAll(replaceWhomUserNames);
		responsibilityNames = StringUtils.join(responsibilityNameCollect, SEPARATOR);

		// 更新工作流变量和问题反馈实体
		runtimeService.setVariable(feedbackSolvingRecord.getProcessInstanceId(), "responsibility", responsibilityIds);
		feedbackEntity.setResponsibilityName(responsibilityNames);
		feedbackEntity.setResponsibility(responsibilityIds);
		feedbackService.updateById(feedbackEntity);

		// 转办任务
		processService.transferTask(process);

		return R.success("操作成功");
	}

	/**
	 * 添加人员（为问题添加额外负责人）
	 */
	@PostMapping("/additionUser")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "添加人员", notes = "传入feedbackSolvingRecord")
	@Transactional(rollbackFor = Exception.class)
	public R additionUser(@Valid @RequestBody WfProcess process) {
		// 解析新增的用户ID列表
		List<Long> replaceWhomUserIds = Arrays.stream(process.getAssignee().split(SEPARATOR))
			.map(Long::valueOf)
			.collect(Collectors.toList());

		// 获取新增用户的姓名列表
		List<String> replaceWhomUserNames = replaceWhomUserIds.stream()
			.map(UserCache::getUser)
			.map(User::getAccount)
			.collect(Collectors.toList());

		// 查询问题反馈实体
		FeedbackEntity feedbackEntity = feedbackService.getOne(new LambdaQueryWrapper<FeedbackEntity>()
			.eq(FeedbackEntity::getProcessInstanceId, process.getProcessInstanceId())
		);

		// 合并原负责人与新增人员的ID和姓名
		String responsibilityIds = feedbackEntity.getResponsibility();
		String responsibilityNames = feedbackEntity.getResponsibilityName();

		List<String> responsibilityIdCollect = Arrays.stream(responsibilityIds.split(SEPARATOR))
			.collect(Collectors.toList());
		responsibilityIdCollect.addAll(replaceWhomUserIds.stream()
			.map(Object::toString)
			.collect(Collectors.toList()));
		responsibilityIds = StringUtils.join(responsibilityIdCollect, SEPARATOR);

		List<String> responsibilityNameCollect = Arrays.stream(responsibilityNames.split(SEPARATOR))
			.collect(Collectors.toList());
		responsibilityNameCollect.addAll(replaceWhomUserNames);
		responsibilityNames = StringUtils.join(responsibilityNameCollect, SEPARATOR);

		// 更新工作流变量和问题反馈实体
		runtimeService.setVariable(process.getProcessInstanceId(), "responsibility", responsibilityIds);
		feedbackEntity.setResponsibilityName(responsibilityNames);
		feedbackEntity.setResponsibility(responsibilityIds);
		feedbackService.updateById(feedbackEntity);

		// 添加多实例任务
		processService.addMultiInstance(process);

		return R.success("添加成功");
	}

	/**
	 * 已知确认（确认问题已解决并移除负责人）
	 */
	@PostMapping("/customizeCompleteTask")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "已知确认", notes = "传入feedbackSolvingRecord")
	@Transactional(rollbackFor = Exception.class)
	public R customizeCompleteTask(@Valid @RequestBody FeedbackSolvingRecordVO feedbackSolvingRecord) {
		// 获取被移除的用户名和ID
		String replaceUserName = feedbackSolvingRecord.getReplaceUser();
		User replaceUserInfo = UserCache.getUser(BladeConstant.ADMIN_TENANT_ID, replaceUserName);

		// 查询问题反馈实体
		FeedbackEntity feedbackEntity = feedbackService.getOne(new LambdaQueryWrapper<FeedbackEntity>()
			.eq(FeedbackEntity::getProcessInstanceId, feedbackSolvingRecord.getProcessInstanceId())
		);

		// 移除指定负责人的ID和姓名
		String responsibilityNames = feedbackEntity.getResponsibilityName();
		String responsibilityIds = feedbackEntity.getResponsibility();

		String reaplcedUserNames = replaceStr(responsibilityNames, replaceUserName);
		String reaplcedUserIds = replaceStr(responsibilityIds, replaceUserInfo.getId().toString());

		// 更新问题反馈实体
		feedbackEntity.setResponsibilityName(reaplcedUserNames);
		feedbackEntity.setResponsibility(reaplcedUserIds);
		feedbackService.updateById(feedbackEntity);

		// 更新工作流变量并完成任务
		WfProcess process = feedbackSolvingRecord.getProcess();
		Map<String, Object> variables = process.getVariables();
		variables.put("responsibility", reaplcedUserIds);
		processService.completeTask(process);

		return R.success("操作成功");
	}

	/**
	 * 从字符串中移除指定子串（用于更新负责人列表）
	 */
	private String replaceStr(String original, String charToRemove) {
		String[] parts = original.split(",");
		return Arrays.stream(parts)
			.filter(s -> !s.equals(charToRemove))  // 过滤掉要移除的子串
			.collect(Collectors.joining(","));     // 重新拼接为字符串
	}

}
```


import request from "@/router/axios";

export const getPage = (current, size, params) => {
  return request({
    url: "/api/ni/depot/stock/in/item/page",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};
export const getList = (params) => {
  return request({
    url: "/api/ni/depot/stock/in/item/list",
    method: "get",
    params,
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/ni/depot/stock/in/item/detail",
    method: "get",
    params: { id },
  });
};

export const markPrint = (ids, printStatus) => {
  return request({
    url: "/api/ni/depot/stock/in/item/markPrint",
    method: "post",
    params: { printedList: ids, printStatus },
  });
};

export const getPrintData = (params) => {
  return request({
    url: "/api/ni/depot/stock/in/item/getPrintData",
    method: "get",
    params,
  });
};

export const changeAmount = (params) => {
  return request({
    url: "/api/ni/depot/stock/in/item/changeAmount",
    method: "post",
    params,
  });
};
export const earliestListBySn = (params) => {
  return request({
    url: "/api/ni/depot/stock/in/item/earliestListBySn",
    method: "get",
    params,
  });
};

export const printSign = (arrivalSerialNos) => {
  return request({
    url: "/api/ni/depot/stock/in/item/printSign",
    method: "post",
    params: { arrivalSerialNos },
  });
};
export const printCount = (ids) => {
  return request({
    url: "/api/ni/depot/stock/in/item/printCount",
    method: "post",
    params: { ids },
  });
};

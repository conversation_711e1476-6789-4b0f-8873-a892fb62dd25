<template>
  <div>
    <el-input
      v-model="name"
      :size="size"
      suffix-icon="el-icon-search"
      :placeholder="placeholder"
      readonly
      :disabled="disabled"
      @click.native="handleSelect"
    ></el-input>
    <!-- 费用申请选择弹窗 -->
    <cost-apply-select-dialog
      ref="cost-apply-select"
      :default-checked="value"
      :multiple="multiple"
      @onConfirm="handleCostApplySelectConfirm"
    ></cost-apply-select-dialog>
  </div>
</template>
<script>
import { getDetail } from "@/api/ni/fin/cost-apply";
import CostApplySelectDialog from "@/views/ni/fin/components/CostApplySelectDialog";
import Emitter from 'element-ui/src/mixins/emitter';

export default {
  mixins: [Emitter],
  name: "cost-apply-select",
  components: { CostApplySelectDialog },
  props: {
    value: [String, Number],
    multiple: {
      type: Boolean,
      default: false,
    },
    size: {
      type: String,
      default: () => {
        return "mini";
      },
    },
    readonly: {
      type: Boolean,
      default: false,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    placeholder: String,
    change: Function,
  },
  watch: {
    value: {
      handler(val) {
        if (val) {
          getDetail(val).then((res) => {
            const data = res.data.data;
            if (data) this.$set(this, "name", `${data.serialNo}`);
          });
        } else this.$set(this, "name", "");
      },
      immediate: true,
    },
  },
  data() {
    return {
      name: "",
    };
  },
  methods: {
    handleSelect() {
      if (this.readonly || this.disabled) return;
      else this.$refs["cost-apply-select"].visible = true;
    },
    handleCostApplySelectConfirm(id) {
      this.$emit("input", id);
      if (this.change && typeof this.change == "function") {
        this.change({ value: id });
      }
      this.$nextTick(() => {
        this.dispatch('ElFormItem', 'el.form.blur', [id]);
      });
    },
  },
};
</script>

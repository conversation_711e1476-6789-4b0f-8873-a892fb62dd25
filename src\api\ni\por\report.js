import request from "@/router/axios";

export const getPage = (current, size, params) => {
  return request({
    url: "/api/ni/por/report/page",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};
export const getAmountList = (month, supplierIds) => {
  return request({
    url: "/api/ni/por/report/getAmountList",
    method: "get",
    params: {
      month,
      supplierIds,
    },
  });
};
export const getPayAmountList = (month, supplierIds) => {
  return request({
    url: "/api/ni/por/report/getPayAmountList",
    method: "get",
    params: {
      month,
      supplierIds,
    },
  });
};
export const getBackAmountList = (month, supplierIds) => {
  return request({
    url: "/api/ni/por/report/getBackAmountList",
    method: "get",
    params: {
      month,
      supplierIds,
    },
  });
};

export const getDepotAmountLine = (month, supplierId) => {
  return request({
    url: "/api/ni/por/report/getDepotAmountLine",
    method: "get",
    params: {
      month,
      supplierId,
    },
  });
};

export const getCostAmountLine = (month, supplierId) => {
  return request({
    url: "/api/ni/por/report/getCostAmountLine",
    method: "get",
    params: {
      month,
      supplierId,
    },
  });
};
export const getBackAmountLine = (month, supplierId) => {
  return request({
    url: "/api/ni/por/report/getBackAmountLine",
    method: "get",
    params: {
      month,
      supplierId,
    },
  });
};

export const getPayAmountLine = (month, supplierId) => {
  return request({
    url: "/api/ni/por/report/getPayAmountLine",
    method: "get",
    params: {
      month,
      supplierId,
    },
  });
};

import request from "@/router/axios";

export const getValueType = () => {
  return request({
    url: "/api/ni/base/propertyExtended/value-type",
    method: "get",
    params: {},
  });
};

export const getList = (current, size, params) => {
  return request({
    url: "/api/ni/base/propertyExtended/list",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/ni/base/propertyExtended/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const remove = (ids) => {
  return request({
    url: "/api/ni/base/propertyExtended/remove",
    method: "post",
    params: {
      ids,
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/ni/base/propertyExtended/submit",
    method: "post",
    data: row,
  });
};

export const update = (row) => {
  return request({
    url: "/api/ni/base/propertyExtended/submit",
    method: "post",
    data: row,
  });
};

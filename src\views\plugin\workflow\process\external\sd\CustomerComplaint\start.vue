<template>
  <basic-container>
    <avue-skeleton :loading="waiting" avatar :rows="8">
      <avue-title
        style="margin-bottom: 20px"
        :styles="{ fontSize: '20px' }"
        :value="process.name"
      ></avue-title>
      <el-card shadow="never" style="margin-top: 20px">
        <!-- 自定义表单区域 -->
        <avue-form
          v-if="option && option.column && option.column.length > 0"
          v-model="form"
          ref="form"
          :option="option"
          :defaults.sync="defaults"
          @submit="handleSubmit"
          @error="loading = false"
          :upload-preview="handleUploadPreview"
        >

        </avue-form>

        <!-- 自定义表单区域 -->
      </el-card>

      <el-card shadow="never" style="margin-top: 20px" v-if="showExamForm">
        <wf-examine-form
          ref="examineForm"
          :process="process"
          @user-select="handleUserSelect"
        ></wf-examine-form>
      </el-card>
      <div style="height: 120px"></div>
      <el-row
        class="foot-item avue-affix"
        :style="{
          width: isCollapse ? 'calc(100% - 80px)' : 'calc(100% - 260px)',
        }"
        id="avue-view"
      >
        <el-button
          v-no-more-click
          type="primary"
          size="mini"
          :loading="loading"
          @click="handleSubmit"
          >发起
        </el-button>
<!--        <el-button-->
<!--          v-if="permission.wf_process_draft"-->
<!--          type="success"-->
<!--          size="mini"-->
<!--          :loading="loading"-->
<!--          @click="handleDraftNotClose(process.id, process.formKey, form)"-->
<!--          >存为草稿-->
<!--        </el-button>-->
<!--        <el-button-->
<!--          v-if="permission.wf_process_draft"-->
<!--          type="success"-->
<!--          size="mini"-->
<!--          :loading="loading"-->
<!--          @click="handleDraft(process.id, process.formKey, form)"-->
<!--          >存为草稿并关闭-->
<!--        </el-button>-->
      </el-row>
    </avue-skeleton>

    <!-- 人员选择弹窗 -->
<!--    <wf-user-select-->
<!--      ref="user-select"-->
<!--      :check-type="checkType"-->
<!--      :default-checked="defaultChecked"-->
<!--      @onConfirm="handleUserSelectConfirm"-->
<!--    ></wf-user-select>-->
  </basic-container>
</template>

<script>
import WfExamineForm from "@/views/plugin/workflow/process/components/examForm.vue";
import WfUserSelect from "@/views/plugin/workflow/process/components/user-select";
import MaterialSelect from "@/views/ni/base/components/MaterialSelect1";
import ProjectSelect from "@/views/ni/project/components/MyProjectSelect";

import exForm from "@/views/plugin/workflow/mixins/ex-form";
import draft from "@/views/plugin/workflow/mixins/draft";
import BudgetItemDialog from "@/views/ni/por/components/BudgetItemDialog";
import MaterialSelectDialog from "@/views/ni/base/components/MaterialSelectDialog";
import UnFinishBudgetSelect from "@/views/ni/por/components/UnFinishBudgetSelect";
import ApplyHistoryInquiryDialog from "@/views/ni/por/components/ApplyHistoryInquiryDialog";
import {getCustomerList1, getCustomerList2} from "@/api/ni/sd/customerComplaint";


export default {
  components: {
    ApplyHistoryInquiryDialog,
    WfUserSelect,
    WfExamineForm,
    MaterialSelect,
    ProjectSelect,
    BudgetItemDialog,
    MaterialSelectDialog,
    UnFinishBudgetSelect,
  },
  mixins: [exForm, draft],
  activated() {
    let val = this.$route.query.p;
    if (val) {
      let text = Buffer.from(val, "base64").toString();
      text = text.replace(/[\r|\n|\t]/g, "");
      const param = JSON.parse(text);
      const { processId, processDefKey, form } = param;
      if (form) {
        const f = JSON.parse(
          new Buffer(decodeURIComponent(form), "utf8").toString("utf8")
        );
        this.form = Object.assign(this.form, f);
      }
      if (processId) {
        this.getForm(processId);
      } else if (processDefKey) {
        this.getFormByProcessDefKey(processDefKey);
      }
    }
  },
  computed: {
    showExamForm() {
      const { hideComment, hideCopy, hideExamine } = this.process;
      return !hideComment || !hideCopy || !hideExamine;
    },
  },
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      //  这里的vm指的就是vue实例，可以用来当做this使用
      vm.fromPath = from.path; //获取上一级路由的路径
    });
  },
  data() {
    return {
      customerCodeValue: "",
      customerOptions: [], // 缓存客户选项
      formData: {
        customerName: '',
        customerCode: ''
      },
      // p: "",
      // inquiriesAmount: 0,
      defaults: {},
      salesArea1:'123',
      form: {
        // budgetId: null,
        // items: [],
      },
      option: {
        labelWidth: 120,
        calcHeight: 30,
        size: "mini",
        span: 8,
        menuBtn: false,
        column: [
          {
            label: "投诉编号",
            prop: "complaintNumber",
            type: "input",
            placeholder: "投诉编号自动生成",
            disabled: true,
          },
          {
            label: "销售区域",
            prop: "salesArea",
            type: "select",
            display: true,
            dicData: [
              { label: '国内', value: '1' },
              { label: '国外', value: '2' }
            ],
            rules: [
              {
                required: true,
                message: "请输入销售区域",
                trigger: "blur",
              },
            ],
            change: ({ value}) => {
              const col = this.findObject(this.option.column, "customerName");
              col.dicUrl="";
              if(value==='1'){
                col.dicUrl="/api/ni/old/customerInformation/customerList1?keyword={{key}}"
                col.dicData = []
              }
              if(value==='2'){
                col.dicUrl="/api/ni/old/customerInformation/customerList2?keyword={{key}}"
                col.dicData = []
              }
              this.form.customerName = ""
              this.form.customerCode = ""
            },
          },
          {
            type: "select",
            label: "投诉类型",
            display: true,
            prop: "complaintType",
            dicUrl: "/api/blade-system/dict-biz/dictionary?code=ni_sd_customer_complaint_type",
            dataType: "number",
            props: {
              label: "dictValue",
              value: "dictKey"
            },
            rules: [{
              required: true,
              message: "请输入投诉类型",
              trigger: "blur"
            }],
            change: ({ value}) => {
              const col = this.findObject(this.option.column, "classification");
              if (value === 1 || value === 4) {
                col.disabled = false;
                this.form.classification = ""
              }else{
                col.disabled = true;
                this.form.classification = "10"
              }
            },
          },
          {
            label: "分类",
            prop: "classification",
            type: "select",
            dicUrl: "/api/blade-system/dict-biz/dictionary?code=ni_sd_customer_complaint_classification",
            dataType: "number",
            disabled: true,
            props: {
              label: "dictValue",
              value: "dictKey"
            },
            rules: [{
              required: true,
              message: "请输入分类",
              trigger: "blur"
            }],
          },
          {
            label: "产品批号",
            prop: "prodBatchNum",
            type: "input",
            rules: [{
              required: true,
              message: "请输入产品批号",
              trigger: "blur"
            }],
          },
          {
            label: "投诉时间",
            prop: "complaintTime",
            type: "date",
            format: "yyyy-MM-dd hh:mm:ss",
            valueFormat: "yyyy-MM-dd hh:mm:ss",
            rules: [{
              required: true,
              message: "请输入投诉时间",
              trigger: "blur"
            }],
          },
          {
            label: "业务经理",
            prop: "businessManager",
            type: "select",
            dicData: [],
            // props: {
            //   label: "dictValue",
            //   value: "dictKey",
            // },
            // dicUrl: "/api/blade-user/user-list?deptIds=1558250198611173378",//1648593234170556437
            props: {
              label: "account",
              value: "id",
            },
            rules: [{
              required: true,
              message: "请输入业务经理",
              trigger: "blur"
            }],
          },
          {
            label: "客户编码",
            prop: "customerCode",
            type: "input",
            value: this.customerCodeValue,

          },
          {
            label: "客户名称",
            prop: "customerName",
            remote: true,
            dicUrl: "",//`/api/ni/old/customerInformation/customerList1?keyword={{key}}`,
            props: {
              label: "customerName",
              value: "customerName",
              desc: "customerCode"
            },
            dicFormatter: (data) => {
              return data.data.records;
            },
            placeholder: " ",
            filterable: true,
            type: "select",
            change: async (value) => {
              if (value.value === null) {
                return;
              }
              // console.log(value)
              if (this.form.salesArea === '1') {
                const res = await getCustomerList1(1, 10, {"keyword": value.value})
                if (res.data.data.records.length === 1) {
                  this.form.customerCode = res.data.data.records[0].customerCode;
                }
              }
              if (this.form.salesArea === '2') {
                const res = await getCustomerList2(1, 10, {"keyword": value.value})
                if (res.data.data.records.length === 1) {
                  this.form.customerCode = res.data.data.records[0].customerCode;
                }
              }
            },
            rules: [{
              required: true,
              message: "请输入客户名称",
              trigger: "blur"
            }],
          },
          {
            label: "投诉内容",
            prop: "complaintContent",
            type: "textarea",
            span: 24,
            rules: [{
              required: true,
              message: "请输入投诉内容",
              trigger: "blur"
            }],
          },
          {
            label: "附件",
            type: "upload",
            propsHttp: {
              res: "data",
              url: "attachId",
              name: "originalName",
            },
            action: "/api/blade-resource/oss/endpoint/put-file-attach",
            display: true,
            span: 24,
            showFileList: true,
            multiple: true,
            limit: 10,
            prop: "attach",
          },
        ],
      },

      process: {},
      loading: false,
      payment: 0,
      typeDict: [],
      typeDictKeyValue: {},
      fromPath: "",
    };
  },
  created() {
    this.dictInit();
  },
  methods: {
    handleCustomerChange(value) {
      console.log('选中的客户ID:', value);

      // 从缓存数据中查找选中项
      const selectedItem = this.customerOptions.find(item => item.value === value);

      if (selectedItem) {
        // 更新表单数据
        this.$set(this.formData, 'customerCode', selectedItem.desc);
        console.log('客户编码已更新:', selectedItem.desc);
      }
    },
    dictInit() {
      this.$http
        //国内销售部和国外销售部
        .get("/api/blade-user/user-list?deptIds=1558250198611173378,1558250278609133570")
        .then((res) => {
          const column = this.findObject(this.option.column, "businessManager");
          let dt1 = res.data.data;
          let dt2=[];
          for (let i = 0; i < dt1.length; i++) {
            //岗位是国内销售经理和国外销售经理
            if (dt1[i].postId === '1648593234170556437' || dt1[i].postId === '1648593234170556441') {
              dt2.push(dt1[i])
            }
          }
          column.dicData = dt2
        });
    },

    getForm(processId) {
      this.getStartForm(processId).then((res) => {
        const _this = this;
        let { process } = res;
        this.form.processId = process.id;
        const option = _this.option;
        const { column, group } = option;
        column.forEach((col) => {
          if (col.value) col.value = _this.getDefaultValues(col.value);
        });
        if (group && group.length > 0) {
          // 处理group
          group.forEach((gro) => {
            gro.column.forEach((col) => {
              if (col.value) col.value = _this.getDefaultValues(col.value);
            });
          });
        }
        if (_this.permission.wf_process_draft) {
          // 查询是否有草稿箱
          _this.initDraft(process.id).then((data) => {
            _this
              .$confirm("是否恢复之前保存的草稿？", "提示", {})
              .then(() => {
                _this.form = JSON.parse(data);
              })
              .catch(() => {});
          });
        }
        _this.waiting = false;
      });
    },
    getFormByProcessDefKey(processDefKey) {
      this.getStartFormByProcessDefKey(processDefKey).then((res) => {
        const _this = this;
        let { process } = res;
        _this.form.processId = process.id;
        const option = _this.option;
        const { column, group } = option;
        column.forEach((col) => {
          if (col.value) col.value = _this.getDefaultValues(col.value);
        });
        if (group && group.length > 0) {
          // 处理group
          group.forEach((gro) => {
            gro.column.forEach((col) => {
              if (col.value) col.value = _this.getDefaultValues(col.value);
            });
          });
        }
        if (_this.permission.wf_process_draft) {
          // 查询是否有草稿箱
          _this.initDraft(process.id).then((data) => {
            _this
              .$confirm("是否恢复之前保存的草稿？", "提示", {})
              .then(() => {
                _this.form = JSON.parse(data);
              })
              .catch(() => {});
          });
        }
        _this.waiting = false;
      });
    },
    handleSubmit() {
      this.loading = true;
      this.handleStartProcess(true)
        .then((done) => {
          this.$message.success("发起成功");
          if (this.fromPath) {
            this.handleCloseTag(this.fromPath);
          } else this.handleCloseTag("/plugin/workflow/process/send");
          done();
        })
        .catch(() => {
          this.loading = false;
        });
    },
    getDefaultValues(value) {
      let defaultValue = "";
      if (value.toString().includes("${") && value.toString().includes("}")) {
        try {
          defaultValue = eval("`" + value + "`");
        } catch (err) {
          defaultValue = value;
        }
      } else defaultValue = value;

      return defaultValue;
    },
  },
};
</script>

<style lang="scss" scoped>
.foot-item {
  position: fixed;
  bottom: 0;
  margin-left: -20px;
  // right: 0;
  z-index: 101;
  height: 66px;
  background-color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  -webkit-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
</style>

import request from "@/router/axios";


export const passport = (chatId) => {
  return request({
    url: "/api/ni/chat/app/passport",
    method: "get",
    params: {
      chatId,
    },
  });
};
export const meta = (chatId) => {
  return request({
    url: "/api/ni/chat/app/meta",
    method: "get",
    params: {
      chatId,
    },
  });
};
export const info = (chatId) => {
  return request({
    url: "/api/ni/chat/app/info",
    method: "get",
    params: {
      chatId,
    },
  });
};
export const conversations = (chatId, lastId) => {
  return request({
    url: "/api/ni/chat/app/conversations",
    method: "get",
    params: {
      chatId,
      lastId,
    },
  });
};
export const blockingMsg = (chatId, query, conversationId) => {
  return request({
    url: "/api/ni/chat/app/chat-messages/blocking",
    method: "post",
    params: {
      chatId,
    },
    data: {
      query,
      conversationId,
    },
  });
};

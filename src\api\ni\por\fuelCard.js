import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/ni/por/fuelCard/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/ni/por/fuelCard/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/ni/por/fuelCard/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}
export const toVoid = (ids) => {
  return request({
    url: '/api/ni/por/fuelCard/toVoid',
    method: 'post',
    params: {
      ids,
    }
  })
}
export const add = (row) => {
  return request({
    url: '/api/ni/por/fuelCard/save',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/ni/por/fuelCard/update',
    method: 'post',
    data: row
  })
}

export const submit = (ids) => {
  return request({
    url: '/api/ni/por/fuelCard/submit',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const fuelCardComplete = (ids, rowNo, arrivalId) => {
  return request({
    url: '/api/ni/por/fuelCard/complete',
    method: 'post',
    params: {
      ids,
      rowNo,
      arrivalId
    }
  })
}
export const fuelCardRecovery = (arrivalId) => {
  return request({
    url: '/api/ni/por/fuelCard/recovery',
    method: 'post',
    params: {
      arrivalId
    }
  })
}

export const getDetailListById = (currentPage, pageSize, ids) => {
  return request({
    url: '/api/ni/por/fuelCard//getDetailListById',
    method: 'post',
    params: {
      currentPage,
      pageSize,
      ids
    }
  })
}

export const changeFuelCardNo = (params) => {
  return request({
    url: '/api/ni/por/fuelCard/changeFuelCardNo',
    method: 'post',
    params
  })
}

export const rechargeSync = () => {
  return request({
    url: '/api/ni/por/fuelCard/rechargeSync',
    method: 'post',
  })
}

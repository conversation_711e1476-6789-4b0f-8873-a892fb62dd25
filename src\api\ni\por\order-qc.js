import request from '@/router/axios';

export const getPage = (current, size, params) => {
  return request({
    url: '/api/ni/por/order/qc/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/ni/por/order/qc/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/ni/por/order/qc/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/ni/por/order/qc/addOrUpdate',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/ni/por/order/qc/addOrUpdate',
    method: 'post',
    data: row
  })
}

export const orderItemList = (orderId)=>{
  return request({
    url: '/api/ni/por/order/qc/orderItemList',
    method: 'get',
    params: {
      orderId,
    }
  })
}


export const submit=(ids)=>{
  return request({
    url: '/api/ni/por/order/qc/submit',
    method: 'post',
    params: {
      ids,
    }
  })
}
export const submitBack=(ids)=>{
  return request({
    url: '/api/ni/por/order/qc/submitBack',
    method: 'post',
    params: {
      ids,
    }
  })
}

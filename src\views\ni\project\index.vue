<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template #deliveryTrainerForm="{ disabled, size }">
        <user-select
          multiple
          v-model="form.deliveryTrainer"
          size="mini"
          :disabled="disabled"
        />
      </template>
      <template #projectAcceptorForm="{ disabled, size }">
        <user-select
          multiple
          v-model="form.projectAcceptor"
          size="mini"
          :disabled="disabled"
        />
      </template>
      <template #feasibilityAuditorForm="{ disabled, size }">
        <user-select
          multiple
          v-model="form.feasibilityAuditor"
          size="mini"
          :disabled="disabled"
        />
      </template>
      <template #leaderIdForm="{ disabled, size }">
        <user-select v-model="form.leaderId" size="mini" :disabled="disabled" />
      </template>
      <template #acl="{ row, disabled, size, index }">
        <el-tag v-if="row.acl === '1'" :size="size" effect="dark">
          {{ row.$acl }}
        </el-tag>
        <el-tag v-else :size="size" effect="dark" type="danger">
          {{ row.$acl }}
        </el-tag>
      </template>
      <template #type="{ row, disabled, size, index }">
        <el-tag :size="size" effect="dark" type="success">
          {{ `${row.$type}(${row.type})` }}
        </el-tag>
      </template>
      <template #brand="{ row, disabled, size, index }">
        <el-tag
          v-if="row.brand"
          :size="size"
          :effect="row.brand === '1' ? 'dark ' : 'plain'"
        >
          {{ row.$brand }}
        </el-tag>
      </template>
      <template #yonyouSync="{ row, size }">
        <span v-for="(item, index) in row.yonyouSync" :key="index">
          {{ yonyouSyncSequenceDictKeyValue[item.sequence] }}:
          <el-tag :size="size">
            {{ yonyouSyncStateDictKeyValue[item.value] }}
          </el-tag>
          <template v-if="item.errMsg"> :{{ item.errMsg }} </template>
          <br />
        </span>
      </template>
      <template #status="{ row, index }">
        <el-tag v-if="row.status === 0" size="mini" type="info">
          {{ row.$status }}
        </el-tag>
        <el-tag v-else-if="row.status === 1" size="mini" effect="plain">
          {{ row.$status }}
        </el-tag>
        <el-tag v-else-if="row.status === 2" size="mini" effect="plain">
          {{ row.$status }}
        </el-tag>
        <el-tag v-else-if="row.status === 3" size="mini" type="danger">
          {{ row.$status }}
        </el-tag>
        <el-tag
          v-else-if="row.status === 4"
          size="mini"
          type="warning"
          effect="plain"
        >
          {{ row.$status }}
        </el-tag>
        <el-tag
          v-else-if="row.status === 5"
          size="mini"
          type="warning"
          effect="plain"
        >
          {{ row.$status }}
        </el-tag>
        <el-tag
          v-else-if="row.status === 6"
          size="mini"
          type="danger"
          effect="plain"
        >
          {{ row.$status }}
        </el-tag>
        <el-tag
          v-else-if="row.status === 9"
          size="mini"
          type="success"
          effect="plain"
        >
          {{ row.$status }}
        </el-tag>
        <el-tag v-else size="mini" type="warning" effect="dark">
          {{ row.$status }}
        </el-tag>
      </template>
      <template #title="{ row, index }">
        <el-tag v-if="row.finish" size="mini" effect="dark" type="warning"
          >已完成
        </el-tag>
        <el-button size="mini" @click="rowDetail(row, index)" type="text"
          >{{ row.title }}
        </el-button>
      </template>
      <template slot="menuLeft">
        <el-button
          type="primary"
          v-if="permission.projectinfo_apply"
          size="mini"
          icon="el-icon-s-promotion"
          @click="handleApply"
          >立项申请
        </el-button>
        <el-button
          type="danger"
          size="mini"
          icon="el-icon-delete"
          plain
          v-if="permission.projectinfo_delete"
          @click="handleDelete"
          >删 除
        </el-button>
        <el-button
          type="success"
          v-if="permission.project_printer"
          size="mini"
          icon="el-icon-printer"
          plain
          @click="handlePrint"
          >打 印
        </el-button>
        <el-button
          type="danger"
          v-if="
            permission.project_change_catalog ||
            userInfo.role_name.includes('admin')
          "
          size="mini"
          icon="el-icon-edit"
          plain
          @click="handleChangeCatalog"
          >变更项目集
        </el-button>
        <el-button
          type="warning"
          size="mini"
          icon="el-icon-upload"
          plain
          v-if="
            permission.project_import || userInfo.role_name.includes('admin')
          "
          @click="() => (excelBox = true)"
          >导 入
        </el-button>
        <el-button
          type="warning"
          size="mini"
          icon="el-icon-document-copy"
          plain
          v-if="
            permission.project_export || userInfo.role_name.includes('admin')
          "
          @click="handleExport"
          >导 出
        </el-button>
        <el-divider direction="vertical" />
        <el-radio-group v-model="brand" size="mini" @input="onLoad(page)">
          <el-radio-button label="natergy">能特异</el-radio-button>
          <el-radio-button label="yy">演绎</el-radio-button>
        </el-radio-group>
      </template>
      <template #menuRight="{ size }">
        <el-button
          icon="el-icon-time"
          circle
          :size="size"
          @click="handleLog"
        ></el-button>
      </template>
      <template #menu="{ row, index, size }">
        <el-button
          type="text"
          size="mini"
          icon="el-icon-finished"
          v-if="
            permission.project_finish &&
            row.status === 9 &&
            !row.finish &&
            (row.leaderId === userInfo.user_id ||
              userInfo.role_name.includes('admin'))
          "
          @click="rowFinish(row)"
          >结项
        </el-button>
        <el-button
          type="text"
          size="mini"
          icon="el-icon-share"
          v-if="permission.project_sub"
          @click="rowSub(row)"
          >分项管理
        </el-button>
        <el-button
          type="text"
          size="mini"
          icon="el-icon-user"
          @click="handleTeam(row)"
          >成员管理
        </el-button>
        <el-divider direction="vertical" />
        <el-dropdown>
          <el-button type="text" :size="size">
            更多<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
              v-if="
                permission.project_work_apply && row.status === 9 && !row.finish
              "
              @click.native="rowDispatch(row)"
            >
              <i class="el-icon-money"></i>用工申请
            </el-dropdown-item>
            <el-dropdown-item @click.native="rowAttach(row)">
              <i class="el-icon-time"></i>附件管理
            </el-dropdown-item>
            <el-dropdown-item
              v-if="permission.projectinfo_yonyou_sync && row.status === 9"
              @click.native="rowYonyouSync(row)"
            >
              <i class="el-icon-refresh"></i>同步用友
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </template>
    </avue-crud>
    <el-drawer
      :visible.sync="detailVisible"
      :title="form.title"
      custom-class="wf-drawer"
      size="1200px"
      append-to-body
    >
      <task-detail
        v-if="detailVisible"
        :taskId="form.taskId"
        :formKey="formKey"
        :option="detailOption"
        :processInstanceId="form.processInsId"
      ></task-detail>
    </el-drawer>
    <project-person-drawer ref="projectPersonRef" />
    <el-dialog
      :visible.sync="bpmnVisible"
      append-to-body
      destroy-on-close
      title="流程图"
      width="70%"
      custom-class="wf-dialog"
    >
      <wf-design
        ref="bpmn"
        style="height: 60vh"
        :options="bpmnOption"
      ></wf-design>
    </el-dialog>
    <flow-set-dialog ref="flowSetDialogRef" @confirm="loadDefaultFlowKey" />
    <log-opt-dialog ref="logOptDialogRef" :module="module" />
    <attach-dialog ref="attachDialogRef" :detail="attachDetail" />
    <el-dialog
      title="更改项目集"
      :visible.sync="catalogVisible"
      width="500px"
      append-to-body
      center
    >
      <avue-form
        :option="catalogOpt"
        v-model="catalogForm"
        @submit="handleChangeCatalogSubmit"
      ></avue-form>
    </el-dialog>
    <el-dialog
      title="数据导入"
      append-to-body
      :visible.sync="excelBox"
      width="555px"
    >
      <avue-form
        :option="excelOption"
        v-model="excelForm"
        :upload-after="uploadAfter"
      >
        <template slot="excelTemplate">
          <el-button type="primary" @click="handleTemplate">
            点击下载<i class="el-icon-download el-icon--right"></i>
          </el-button>
        </template>
      </avue-form>
    </el-dialog>
    <el-dialog
      :title="finish.title"
      :visible.sync="finish.visible"
      width="500px"
      append-to-body
    >
      <avue-form
        :option="finish.option"
        v-model="finish.form"
        @submit="handleFinishSubmit"
      ></avue-form>
    </el-dialog>
    <user-select-dialog
      ref="userSelectDialogRef"
      multiple
      @confirm="userSelectConfirm"
    />
    <sub-dialog ref="subDialogRef" />
  </basic-container>
</template>

<script>
import {
  add,
  apply,
  changeCatalog,
  finish,
  getDetail,
  getList,
  getPrintData,
  remove,
  sync,
  update,
} from "@/api/ni/project";
import { mapGetters } from "vuex";
import exForm from "@/views/plugin/workflow/mixins/ex-form";
import ProjectPerson from "@/views/ni/project/person";
import { startProcess } from "@/api/plugin/workflow/process";
import UserSelect from "@/components/user-select";
import FlowSetDialog from "@/components/flow-set-dialog";
import { getByModule } from "@/api/ni/base/module-flow";
import LogOptDialog from "@/components/log-opt-dialog";
import AttachDialog from "@/components/attach-dialog";
import { exportBlob } from "@/api/common";
import { getToken } from "@/util/auth";
import { downloadXls } from "@/util/util";
import ProjectPersonDrawer from "@/views/ni/project/components/ProjectPersonDrawer";
import ProjectDetail from "@/views/ni/project/project-detail";
import { loadPrintTemplate } from "@/api/system/printTemplate";
import { hiprint } from "vue-plugin-hiprint";
import UserSelectDialog from "@/components/user-select-dialog";
import TaskDetail from "@/views/plugin/workflow/ops/detail";
import SubDialog from "@/views/ni/project/components/SubDialog";

export default {
  mixins: [exForm],
  components: {
    SubDialog,
    ProjectPersonDrawer,
    UserSelect,
    ProjectDetail,
    ProjectPerson,
    FlowSetDialog,
    LogOptDialog,
    AttachDialog,
    UserSelectDialog,
    TaskDetail,
  },
  data() {
    return {
      attachDetail: false,
      module: "ni_project_init",
      processDefKey: "process_project_init",
      formKey: "wf_ex_ProjectInit",
      detailVisible: false,
      bpmnVisible: false,
      bpmnOption: {},
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      exportData: [], //打印数据缓存
      exportList: [], //粘贴板数据缓存
      brand: "natergy",
      option: {
        searchEnter: true,
        span: 8,
        searchSpan: 6,
        labelWidth: 125,
        height: "auto",
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        viewBtn: false,
        selection: true,
        size: "mini",
        searchSize: "mini",
        align: "center",
        searchIndex: 3,
        searchIcon: true,
        dialogFullscreen: true,
        column: [
          {
            label: "审批状态",
            prop: "status",
            dicUrl: "/api/blade-system/dict/dictionary?code=data_status",
            minWidth: 80,
            search: true,
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            dataType: "number",
            display: false,
            type: "select",
          },
          {
            label: "申请人",
            prop: "createUserName",
            display: false,
            minWidth: 100,
          },
          {
            label: "申请人",
            prop: "createUser",
            type: "select",
            remote: true,
            row: true,
            dicUrl: `/api/blade-user/search/otj/user?name={{key}}`,
            props: {
              label: "realName",
              value: "id",
            },
            dicFormatter: (data) => {
              return data.data.records;
            },
            placeholder: " ",
            filterable: true,
            showColumn: false,
            hide: true,
            display: false,
            minWidth: 100,
            search: true,
            searchOrder: 97,
          },
          {
            label: "申请部门",
            prop: "createDeptName",
            minWidth: 100,
            display: false,
          },
          {
            label: "申请部门",
            prop: "createDept",
            type: "tree",
            dicUrl: `/api/blade-system/dept/list`,
            props: {
              label: "deptName",
              value: "id",
            },
            search: true,
            minWidth: 100,
            hide: true,
            showColumn: false,
            display: false,
          },
          {
            label: "项目名称",
            prop: "title",
            placeholder: " ",
            search: true,
            searchOrder: 99,
            span: 8,
            overHidden: true,
            minWidth: 150,
            rules: [
              {
                required: true,
                message: "请输入",
                trigger: "blur",
              },
            ],
          },
          {
            label: "项目编号",
            prop: "serialNo",
            placeholder: " ",
            addDisabled: false,
            editDisabled: true,
            overHidden: true,
            minWidth: 120,
            span: 8,
            search: true,
            searchOrder: 98,
          },
          {
            label: "项目集",
            prop: "catalogId",
            type: "select",
            dicUrl: "/api/ni/project/catalog/list?status=2",
            placeholder: " ",
            props: {
              label: "name",
              value: "id",
              desc: "code",
            },
            span: 8,
            hide: true,
            showColumn: false,
            search: true,
          },
          {
            label: "类型",
            prop: "type",
            type: "select",
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_project_type",
            props: {
              label: "dictValue",
              value: "dictKey",
              desc: "dictKey",
            },
            placeholder: " ",
            span: 8,
            search: true,
            rules: [
              {
                required: true,
                message: "请选择类型",
                trigger: "blur",
              },
            ],
          },
          {
            label: "账套",
            prop: "brand",
            type: "radio",
            value: "1",
            dicUrl: "/api/blade-system/dict-biz/dictionary?code=ni_brand",
            props: {
              label: "dictValue",
              value: "dictKey",
            },

            rules: [
              {
                required: true,
                message: "请选择用友账套",
                trigger: "blur",
              },
            ],
          },
          {
            label: "项目集",
            prop: "catalogName",
            placeholder: " ",
            display: false,
            overHidden: true,
            minWidth: 120,
          },
          {
            label: "项目集编号",
            prop: "catalogCode",
            placeholder: " ",
            display: false,
            overHidden: true,
            hide: true,
            minWidth: 120,
          },
          {
            label: "分期情况",
            prop: "stages",
            hide: true,
          },
          {
            label: "负责人",
            prop: "leaderId",
            placeholder: " ",
            filterable: true,
            showColumn: false,
            hide: true,
            span: 12,
            rules: [
              {
                required: true,
                message: "请输入",
                trigger: "blur",
              },
            ],
          },
          {
            label: "负责人",
            prop: "leader",
            display: false,
            minWidth: 100,
            overHidden: true,
            search: true,
          },
          {
            label: "项目成员",
            prop: "teams",
            type: "dynamic",
            hide: true,
            showColumn: false,
            addDisplay: true,
            editDisplay: true,
            viewDisplay: false,
            span: 24,
            children: {
              align: "center",
              headerAlign: "center",
              rowAdd: () => {
                let userIds;
                if (this.form.teams && this.form.teams.length > 0)
                  userIds = this.form.teams
                    .map((item) => item.personId)
                    .join(",");
                this.$refs.userSelectDialogRef.init(userIds);
              },
              column: [
                {
                  label: "团队成员",
                  prop: "personName",
                  placeholder: " ",
                  disabled: true,
                  filterable: true,
                },
                {
                  label: "项目角色",
                  prop: "personRole",
                },
                {
                  label: "备注",
                  prop: "remark",
                  type: "textarea",
                  minRows: 1,
                },
              ],
            },
          },
          {
            label: "项目成员",
            prop: "teams",
            type: "dynamic",
            span: 24,
            hide: true,
            showColumn: false,
            addDisplay: false,
            editDisplay: false,
            viewDisplay: true,
            children: {
              align: "center",
              headerAlign: "center",
              column: [
                {
                  label: "团队成员",
                  prop: "personName",
                  placeholder: " ",
                },
                {
                  label: "项目角色",
                  prop: "personRole",
                },
                {
                  label: "备注",
                  prop: "remark",
                  type: "textarea",
                  minRows: 1,
                },
              ],
            },
          },
          {
            label: "成员数",
            prop: "personNum",
            display: false,
            minWidth: 90,
          },
          {
            label: "总预算(W)",
            prop: "budget",
            type: "number",
            minWidth: 100,
            row: true,
            placeholder: " ",
            rules: [
              {
                required: true,
                message: "请输入",
                trigger: "blur",
              },
            ],
          },
          {
            label: "安全预算(万元)",
            prop: "securityBudget",
            type: "number",
            placeholder: " ",
            span: 6,
            hide: true,
            rules: [
              {
                required: true,
                message: "请输入",
                trigger: "blur",
              },
            ],
          },
          {
            label: "环保预算(万元)",
            prop: "epBudget",
            type: "number",
            placeholder: " ",
            span: 6,
            hide: true,
            rules: [
              {
                required: true,
                message: "请输入",
                trigger: "blur",
              },
            ],
          },
          {
            label: "人工预算(万元)",
            prop: "artificialBudget",
            type: "number",
            placeholder: " ",
            span: 6,
            hide: true,
            rules: [
              {
                required: true,
                message: "请输入",
                trigger: "blur",
              },
            ],
          },
          {
            label: "水电预算(万元)",
            prop: "hydropowerBudget",
            type: "number",
            placeholder: " ",
            span: 6,
            hide: true,
          },
          {
            label: "计划开始",
            prop: "startDate",
            minWidth: 100,
            overHidden: true,
            display: false,
          },
          {
            label: "计划完成",
            prop: "endDate",
            minWidth: 100,
            overHidden: true,
            display: false,
          },
          {
            type: "daterange",
            label: "计划起止日期",
            span: 14,
            display: true,
            row: true,
            hide: true,
            showColumn: false,
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            prop: "datetime",
            required: true,
            rules: [
              {
                required: true,
                message: "计划起止日期必须填写",
              },
            ],
            change: ({ value }) => {
              if (!value || value.length == 0) {
                this.$set(this.form, "startDate", undefined);
                this.$set(this.form, "endDate", undefined);
              } else {
                this.$set(this.form, "startDate", value[0]);
                this.$set(this.form, "endDate", value[1]);
              }
            },
          },
          {
            label: "实际开始时间",
            prop: "actualStartDate",
            minWidth: 100,
            overHidden: true,
            display: false,
          },
          {
            label: "实际完成时间",
            prop: "actualEndDate",
            minWidth: 100,
            overHidden: true,
            display: false,
          },
          {
            label: "可行性报告批准人",
            prop: "feasibilityAuditor",
            hide: true,
          },
          {
            type: "textarea",
            label: "项目描述",
            placeholder: "请对项目做简单描述",
            span: 24,
            hide: true,
            overHidden: true,
            minWidth: 120,
            row: true,
            display: true,
            prop: "remark",
            required: true,
            rules: [
              {
                required: true,
                message: "项目描述必须填写",
              },
            ],
          },
          {
            type: "textarea",
            label: "项目收益",
            placeholder: "请简要描述项目收益",
            span: 24,
            row: true,
            hide: true,
            overHidden: true,
            minWidth: 120,
            display: true,
            prop: "profit",
            required: true,
            rules: [
              {
                required: true,
                message: "项目收益必须填写",
              },
            ],
          },
          {
            type: "textarea",
            label: "项目风险",
            placeholder: "请简要说明项目风险",
            span: 24,
            hide: true,
            row: true,
            overHidden: true,
            minWidth: 120,
            display: true,
            prop: "risk",
            required: true,
            rules: [
              {
                required: true,
                message: "项目风险必须填写",
              },
            ],
          },
          {
            type: "textarea",
            label: "环保风险",
            placeholder: "请简要说明项目环保性风险评估及分析",
            span: 24,
            hide: true,
            row: true,
            overHidden: true,
            minWidth: 120,
            display: true,
            prop: "epRisk",
            required: true,
            minRows: 2,
          },
          {
            type: "textarea",
            label: "实施风险",
            placeholder: "请简要说明项目环保性风险评估及分析",
            span: 24,
            hide: true,
            row: true,
            overHidden: true,
            minWidth: 120,
            display: true,
            prop: "implementRisk",
            required: true,
            minRows: 2,
          },
          {
            label: "访问控制",
            prop: "acl",
            search: true,
            type: "radio",
            overHidden: true,
            minWidth: 100,
            value: "1",
            dicData: [
              {
                label: "私有",
                value: "1",
              },
              {
                label: "公开",
                value: "2",
              },
            ],
            rules: [
              {
                required: true,
                message: "请输入",
                trigger: "blur",
              },
            ],
          },
          {
            label: "用友同步",
            prop: "yonyouSync",
            addDisplay: false,
            editDisplay: false,
            hide: true,
          },
        ],
        group: [
          {
            label: "拓展信息",
            collapse: false,
            column: [
              {
                label: "项目验收人及续期批准人",
                prop: "projectAcceptor",
                placeholder: " ",
              },
              {
                label: "验证性投资所处投资阶段",
                prop: "verifyStage",
              },
              {
                label: "成熟验证性投资变废折价率(%)",
                prop: "scrapDiscountRate",
                type: "number",
                placeholder: " ",
              },
              {
                label: "变废改造后折价率(%)",
                prop: "remodelDiscountRate",
                type: "number",
                placeholder: " ",
              },
              {
                label: "成功后竞争力周期(年)",
                prop: "cycle",
                type: "number",
                placeholder: " ",
              },
              {
                label: "交付培训人",
                prop: "deliveryTrainer",
                placeholder: " ",
              },
              {
                label: "验证部门",
                prop: "verifyDept",
              },
              {
                label: "验证周期(年)",
                prop: "verifyCycle",
                type: "number",
                placeholder: " ",
              },
              {
                label: "维修频次及难度",
                prop: "serviceCycle",
                placeholder: " ",
              },
              {
                label: "验证结果及建议",
                prop: "verifyResult",
                placeholder: " ",
                span: 24,
                minRows: 2,
                type: "textarea",
              },
              {
                label: "考核依据",
                prop: "assessBasis",
                placeholder: " ",
                span: 24,
                minRows: 2,
                type: "textarea",
              },
              {
                label: "考核方法",
                prop: "assessMethod",
                placeholder: " ",
                span: 24,
                minRows: 2,
                type: "textarea",
              },
            ],
          },
        ],
      },
      data: [],
      detailOption: {
        labelWidth: 110,
        column: [
          {
            type: "input",
            label: "项目名称",
            placeholder: " ",
            span: 24,
            row: true,
            display: true,
            prop: "title",
          },
          {
            type: "input",
            label: "项目编号",
            placeholder: " ",
            span: 24,
            row: true,
            display: true,
            prop: "serialNo",
            readonly: true,
          },
          {
            label: "负责人",
            prop: "leaderId",
            type: "select",
            dicUrl: "/api/blade-user/user-vo-list",
            props: {
              label: "name",
              value: "id",
            },
            readonly: true,
            span: 24,
            display: true,
          },
          {
            label: "项目成员",
            prop: "teams",
            type: "dynamic",
            span: 24,
            children: {
              align: "center",
              headerAlign: "center",
              column: [
                {
                  label: "团队成员",
                  prop: "personName",
                  placeholder: " ",
                },
                {
                  label: "项目角色",
                  prop: "personRole",
                },
                {
                  label: "备注",
                  prop: "remark",
                  type: "textarea",
                  minRows: 1,
                },
              ],
            },
          },
          {
            type: "number",
            label: "预算(W)",
            span: 12,
            placeholder: " ",
            display: true,
            prop: "budget",
          },
          {
            type: "number",
            label: "安全预算(W)",
            placeholder: " ",
            span: 4,
            controls: false,
            display: true,
            prop: "securityBudget",
          },
          {
            type: "number",
            label: "环保预算(W)",
            placeholder: " ",
            span: 4,
            controls: false,
            display: true,
            prop: "epBudget",
          },
          {
            type: "number",
            label: "人工预算(W)",
            placeholder: " ",
            span: 4,
            controls: false,
            display: true,
            prop: "artificialBudget",
          },
          {
            type: "daterange",
            label: "计划起止日期",
            span: 24,
            display: true,
            row: true,
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            prop: "datetime",
            required: true,
            rules: [
              {
                required: true,
                message: "开始时间必须填写",
              },
            ],
            change: ({ value }) => {
              if (!value || value.length == 0) {
                this.$set(this.form, "startDate", undefined);
                this.$set(this.form, "endDate", undefined);
              } else {
                this.$set(this.form, "startDate", value[0]);
                this.$set(this.form, "endDate", value[1]);
              }
            },
          },
          {
            type: "textarea",
            label: "项目描述",
            placeholder: "请对项目做简单描述",
            span: 24,
            row: true,
            display: true,
            prop: "remark",
            required: true,
            rules: [
              {
                required: true,
                message: "项目描述必须填写",
              },
            ],
          },
          {
            type: "textarea",
            label: "项目收益",
            placeholder: "请简要描述项目收益",
            span: 24,
            row: true,
            display: true,
            prop: "profit",
            required: true,
            rules: [
              {
                required: true,
                message: "项目收益必须填写",
              },
            ],
          },
          {
            type: "textarea",
            label: "项目风险",
            placeholder: "请简要说明项目风险",
            span: 24,
            row: true,
            display: true,
            prop: "risk",
            required: true,
            rules: [
              {
                required: true,
                message: "项目风险必须填写",
              },
            ],
          },
          {
            type: "radio",
            label: "访问控制",
            span: 24,
            row: true,
            display: true,
            prop: "acl",
            dicData: [
              {
                label: "私有（只有项目负责人、团队成员和干系人可访问）",
                value: "1",
              },
              {
                label: "公开（有项目权限即可访问）",
                value: "2",
              },
            ],
            rules: [
              {
                required: true,
                message: "访问控制必须选择",
              },
            ],
          },
        ],
        group: [
          {
            label: "拓展信息",
            collapse: false,
            column: [
              {
                label: "费用核算依据",
                prop: "hsyj",
                span: 24,
                minRows: 2,
                type: "textarea",
                placeholder: " ",
              },
              {
                label: "项目验收人及续期批准人",
                prop: "projectAcceptor",
                type: "select",
                placeholder: " ",
                multiple: true,
                dicUrl: "/api/blade-user/user-vo-list",
                props: {
                  label: "name",
                  value: "id",
                  desc: "deptName",
                },
              },
              {
                label: "验证结果及建议",
                prop: "verifyResult",
                placeholder: " ",
                span: 24,
                minRows: 2,
                type: "textarea",
              },
              {
                label: "考核依据",
                prop: "assessBasis",
                placeholder: " ",
                span: 24,
                minRows: 2,
                type: "textarea",
              },
              {
                label: "考核方法",
                prop: "assessMethod",
                placeholder: " ",
                span: 24,
                minRows: 2,
                type: "textarea",
              },
              {
                label: "交付培训人",
                prop: "deliveryTrainer",
                type: "select",
                placeholder: " ",
                multiple: true,
                dicUrl: "/api/blade-user/user-vo-list",
                props: {
                  label: "name",
                  value: "id",
                  desc: "deptName",
                },
              },
              {
                label: "交付情况",
                prop: "delivery",
                placeholder: " ",
                span: 24,
                minRows: 2,
                type: "textarea",
              },
              {
                label: "变废折价率(%)",
                prop: "scrapDiscountRate",
                type: "number",
                placeholder: " ",
              },
              {
                label: "变废改造后折价率(%)",
                prop: "remodelDiscountRate",
                type: "number",
                placeholder: " ",
              },
              {
                label: "竞争力周期(年)",
                prop: "cycle",
                type: "number",
                placeholder: " ",
              },
            ],
          },
        ],
      },
      yonyouSyncSequenceDict: [],
      yonyouSyncSequenceDictKeyValue: {},
      yonyouSyncStateDict: [],
      yonyouSyncStateDictKeyValue: {},
      dataStatusDict: [],
      dataStatusDictKeyValue: {},
      brandDict: [],
      brandDictKeyValue: {},
      projectTypeDict: [],
      projectTypeDictKeyValue: {},
      catalogVisible: false,
      catalogOpt: {
        size: "mini",
        span: 24,
        labelWidth: 110,
        column: [
          {
            label: "项目集",
            prop: "catalogId",
            type: "select",
            dicUrl: "/api/ni/project/catalog/list?status=2",
            placeholder: " ",
            props: {
              label: "name",
              value: "id",
              desc: "code",
            },
            row: true,
          },
        ],
      },
      catalogForm: {},
      excelBox: false,
      excelOption: {
        size: "mini",
        searchSize: "mini",
        submitBtn: false,
        emptyBtn: false,
        column: [
          {
            label: "数据上传",
            prop: "excelFile",
            type: "upload",
            drag: true,
            loadText: "模板上传中，请稍等",
            span: 24,
            data: {},
            propsHttp: {
              res: "data",
            },
            tip: "请上传 .xls,.xlsx 标准格式文件",
            action: "/api/ni/project/import",
          },
          {
            label: "模板下载",
            prop: "excelTemplate",
            formslot: true,
            span: 24,
          },
        ],
      },
      excelForm: {},
      printTemplate: {},
      finish: {
        title: "",
        visible: false,
        option: {
          labelWidth: 140,
          size: "mini",
          searchSize: "mini",
          submitText: "关闭项目",
          emptyBtn: false,
          column: [
            {
              label: "实际完成日期",
              prop: "actualEndDate",
              format: "yyyy-MM-dd",
              valueFormat: "yyyy-MM-dd",
              type: "date",
              span: 24,
              rules: [
                {
                  required: true,
                  message: "请选择实际完成日期",
                },
              ],
            },
          ],
        },
        form: {},
      },
      dispatch: {
        title: null,
        projectId: null,
      },
    };
  },
  computed: {
    ...mapGetters(["userInfo", "permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.projectinfo_add, false),
        viewBtn: this.vaildData(this.permission.projectinfo_view, false),
        delBtn: this.vaildData(this.permission.projectinfo_delete, false),
        editBtn: this.vaildData(this.permission.projectinfo_edit, false),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  created() {
    this.dictInit();
    loadPrintTemplate(this.module).then((res) => {
      this.printTemplate = JSON.parse(res.data.data.content);
    });
  },
  methods: {
    //获取搜索的导出数据
    async getExportData() {
      const promises = [];
      this.exportData = [];
      const q = {
        ...this.params,
        ...this.query,
      };
      if (this.brand === "natergy") {
        q.brand = "1,2";
      } else if (this.brand === "yy") {
        q.brand = "4";
      }
      for (var i = 1; i <= this.page.total / this.page.pageSize + 1; i++) {
        const promise = getList(i, this.page.pageSize, q).then(
          (res) => {
            const data = res.data.data.records;
            this.exportData = this.exportData.concat(data);
            this.exportData.forEach(
              (item) => (item.applyBudget = item.applyBudget / 10000)
            );
          }
        );
        promises.push(promise);
      }
      // 等待所有异步请求完成
      await Promise.all(promises);
      return this.exportData;
    },
    getField(value) {
      return value != null ? `${value}` : "";
    },
    /**
     * 导出数据
     */
    async handleExport() {
      this.exportList = [];
      let data = await this.getExportData();
      this.exportList =
        "审批状态\t申请人\t申请部门\t项目名称\t项目编号\t类型\t账套\t项目集\t负责人\t成员数\t总预算(W)\t已申请(W)\t计划开始\t计划完成\t实际开始时间\t实际完成时间\r\n";
      data.forEach((ele) => {
        let values = [
          this.getField(this.dataStatusDictKeyValue[ele.status]),
          this.getField(ele.createUserName),
          this.getField(ele.createDeptName),
          this.getField(ele.title),
          this.getField(ele.serialNo),
          this.getField(this.projectTypeDictKeyValue[ele.type]),
          this.getField(this.brandDictKeyValue[ele.brand]),
          this.getField(ele.catalogName),
          this.getField(ele.leader),
          this.getField(ele.personNum),
          this.getField(ele.budget),
          this.getField(ele.applyBudget),
          this.getField(ele.startDate),
          this.getField(ele.endDate),
          this.getField(ele.actualStartDate),
          this.getField(ele.actualEndDate),
        ];

        this.exportList += values.join("\t") + "\r\n";
      });
      this.$Clipboard({
        text: this.exportList,
      })
        .then(() => {
          this.$message.success("数据导出完成，请复制到excel中");
        })
        .catch(() => {
          this.$message({ type: "waning", message: "该浏览器不支持自动复制" });
        })
        .finally(() => {
          //  操作完成后清空 exportList
          this.exportList = [];
          console.log(this.exportList);
        });
    },
    userSelectConfirm(ids, selectList) {
      selectList.forEach((item) => {
        const teams = [...this.form.teams];
        teams.push({ personId: item.id, personName: item.realName });
        this.$set(this.form, "teams", teams);
      });
    },
    handleTemplate() {
      exportBlob(
        `/api/ni/project/export-template?${
          this.website.tokenHeader
        }=${getToken()}`
      ).then((res) => {
        downloadXls(res.data, "项目导入模板.xlsx");
      });
    },
    uploadAfter(res, done, loading, column) {
      console.log(res);
      window.console.log(column);
      this.excelBox = false;
      this.refreshChange();
      done();
      if (res) {
        this.$confirm(res, "提示", {
          confirmButtonText: "确定",
          type: "warning",
          center: true,
        });
      }
    },
    handleChangeCatalogSubmit(form, done) {
      changeCatalog(form)
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        })
        .finally(() => {
          this.catalogVisible = false;
          done();
        });
    },
    handleChangeCatalog() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择要更改的数据");
        return;
      }
      this.catalogForm = {
        ids: this.selectionList
          .map((item) => {
            return item.id;
          })
          .join(","),
      };
      this.catalogVisible = true;
    },
    async handlePrint() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择要打印的数据");
        return;
      } else if (this.selectionList.length > 1) {
        this.$message.warning("只能选择一条数据");
        return;
      }
      let printData;
      let hiprintTemplate;
      if (!this.printTemplate) {
        this.$message.error("打印模板加载失败，请联系管理员");
        return;
      }
      const data = await getPrintData(this.selectionList[0].id);
      printData = data.data.data;
      hiprintTemplate = new hiprint.PrintTemplate({
        template: this.printTemplate,
      });
      hiprintTemplate.print(printData);
    },
    rowDispatch(row) {
      this.dynamicRoute(
        {
          processDefKey: "process_ni_project_dispatch",
          form: { projectId: row.id },
        },
        "start"
      );
    },
    rowAttach(row) {
      if (row.status === 2) {
        this.attachDetail = true;
      }
      this.$refs.attachDialogRef.init(row.id, this.module);
    },
    rowYonyouSync(row) {
      let msg = "确定将选择数据同步?";
      if (row.yonyouSync && row.yonyouSync.length > 0) {
        msg = "数据已经同步过，是否继续?";
      }
      this.$confirm(msg, {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        sync(row.id).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
      });
    },
    handleLog() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择要查看的数据");
        return;
      } else if (this.selectionList.length > 1) {
        this.$message.warning("只能选择一条数据");
        return;
      }
      this.$refs.logOptDialogRef.init(this.selectionList[0].id);
    },
    loadDefaultFlowKey() {
      getByModule(this.module).then((res) => {
        const moduleFlow = res.data.data;
        if (moduleFlow) {
          this.processDefKey = moduleFlow.flowKey;
        }
      });
    },
    handleSubmit(type) {
      this.form.status = 1;
      if (type === "add") {
        this.$refs.crud.rowSave();
      } else if (type === "edit") {
        this.$refs.crud.rowUpdate();
      }
    },
    rowApply(row) {
      this.$confirm("此操作将提交该项目，是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        center: true,
      }).then(() => {
        getDetail(row.id).then((res) => {
          const form = res.data.data;
          this.getStartFormByProcessDefKey(this.processDefKey).then((res) => {
            let { process } = res;
            form.processId = process.id;
            form.status = 1;
            startProcess(form).then(() => {
              this.onLoad(this.page);
              this.$message({
                type: "success",
                message: "操作成功!",
              });
            });
          });
        });
      });
    },
    nodeClick(data) {
      this.treeDeptId = data.id;
      this.page.currentPage = 1;
      this.onLoad(this.page);
    },
    handleBudget(row) {
      const processDefKey = "process_project_budget";
      const formKey = "wf_ex_ProjectBudget";
      const form = { projectId: row.id };
      this.dynamicRoute({ processDefKey, formKey, form }, "start");
    },
    rowTagType(status) {
      if (!status) {
        return "";
      }
      if (status === 0) {
        //草稿
        return "info";
      } else if ([1, 2].includes(status)) {
        //已提交,审核中
        return "";
      } else if (status === 3) {
        //被驳回
        return "danger";
      } else if (status === 4) {
        //已撤销
        return "warning";
      } else if (status === 5) {
        //已挂起
        return "warning";
      } else if (status === 6) {
        //已终止
        return "danger";
      } else if (status === 9) {
        //已审核
        return "success";
      }
    },
    dictInit() {
      this.$http
        .get("/api/blade-system/dict/dictionary?code=yonyou_sync_sequence")
        .then((res) => {
          this.yonyouSyncSequenceDict = res.data.data;
          this.yonyouSyncSequenceDictKeyValue =
            this.yonyouSyncSequenceDict.reduce((acc, cur) => {
              acc[cur.dictKey] = cur.dictValue;
              return acc;
            }, {});
        });
      this.$http
        .get("/api/blade-system/dict/dictionary?code=yonyou_sync_state")
        .then((res) => {
          this.yonyouSyncStateDict = res.data.data;
          this.yonyouSyncStateDictKeyValue = this.yonyouSyncStateDict.reduce(
            (acc, cur) => {
              acc[cur.dictKey] = cur.dictValue;
              return acc;
            },
            {}
          );
        });
      this.$http
        .get("/api/blade-system/dict/dictionary?code=data_status")
        .then((res) => {
          this.dataStatusDict = res.data.data;
          this.dataStatusDictKeyValue = this.dataStatusDict.reduce(
            (acc, cur) => {
              acc[cur.dictKey] = cur.dictValue;
              return acc;
            },
            {}
          );
        });
      this.$http
        .get("/api/blade-system/dict-biz/dictionary?code=ni_brand")
        .then((res) => {
          this.brandDict = res.data.data;
          this.brandDictKeyValue = this.brandDict.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });
      this.$http
        .get("/api/blade-system/dict-biz/dictionary?code=ni_project_type")
        .then((res) => {
          this.projectTypeDict = res.data.data;
          this.projectTypeDictKeyValue = this.projectTypeDict.reduce(
            (acc, cur) => {
              acc[cur.dictKey] = cur.dictValue;
              return acc;
            },
            {}
          );
        });
    },
    rowSub(row) {
      this.$refs.subDialogRef.onShow(row);
    },
    rowFinish(row) {
      this.finish.title = `[${row.serialNo}]${row.title}`;
      this.finish.form = {
        id: row.id,
      };
      this.finish.visible = true;
    },
    handleFinishSubmit(form, done) {
      finish(form.id, form.date)
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        })
        .finally(() => {
          this.finish.visible = false;
          done();
        });
    },
    //成员管理
    handleTeam(row) {
      this.$refs.projectPersonRef.init(row);
    },
    rowDetail(row, index) {
      if (row.processInsId) {
        this.dynamicRoute(
          {
            processInstanceId: row.processInsId,
            processDefKey: this.processDefKey,
          },
          "detail"
        );
      } else {
        this.$refs.crud.rowView(row, index);
      }
    },
    rowSave(row, done, loading) {
      const form = { ...row };
      if (form.deliveryTrainer && Array.isArray(form.deliveryTrainer)) {
        form.deliveryTrainer = form.deliveryTrainer.join(",");
      }
      if (form.projectAcceptor && Array.isArray(form.projectAcceptor)) {
        form.projectAcceptor = form.projectAcceptor.join(",");
      }
      console.log(form);
      let res;
      if (form.status === 1) {
        res = apply(form, this.processDefKey);
      } else {
        res = add(form);
      }
      res.then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          window.console.log(error);
        }
      );
    },
    rowUpdate(row, index, done, loading) {
      const form = { ...row };
      if (row.deliveryTrainer && row.deliveryTrainer.length == 2) {
        form.deliveryTrainer = form.deliveryTrainer.join(",");
      }
      if (form.projectAcceptor && form.projectAcceptor.length == 2) {
        form.projectAcceptor = form.projectAcceptor.join(",");
      }
      update(form).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          console.log(error);
        }
      );
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    handleApply() {
      const form = {
        subs:[
          {
            name: '机械自动化模块',
            code: '1'
          },
          {
            name: '网络与逻辑感知控制自动化模块',
            code: '2'
          },
          {
            name: '故障自动在线处理模块',
            code: '3'
          },
          {
            name: '传感自查互纠模块',
            code: '4'
          }
        ]
      };
      this.dynamicRoute(
        {
          processDefKey: this.processDefKey,
          form: encodeURIComponent(
              Buffer.from(JSON.stringify(form)).toString("utf8")
          ),
        },
        "start"
      );
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen(done, type) {
      if (type === "add") {
        this.form.status = 0;
      } else if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then((res) => {
          this.form = res.data.data;
          if (this.form.teamIds) this.form.teams = this.form.teamIds.split(",");
          this.form.applyBudget = this.form.applyBudget / 10000;
          this.form.datetime = [this.form.startDate, this.form.endDate];
        });
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      const q = {
        ...params,
        ...this.query,

      };
      if (this.brand === "natergy") {
        q.brand = "1,2";
      } else if (this.brand === "yy") {
        q.brand = "4";
      }
      getList(page.currentPage, page.pageSize, q).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.data.forEach(
          (item) => (item.applyBudget = item.applyBudget / 10000)
        );
        this.loading = false;
        this.selectionClear();
      });
    },
  },
};
</script>

<style></style>

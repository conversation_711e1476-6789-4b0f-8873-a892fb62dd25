import request from "@/router/axios";

export const getPage = (current, size, params) => {
  return request({
    url: "/api/ni/por/apply/page",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/ni/por/apply/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const remove = (ids) => {
  return request({
    url: "/api/ni/por/apply/remove",
    method: "post",
    params: {
      ids,
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/ni/por/apply/save",
    method: "post",
    data: row,
  });
};

export const update = (row) => {
  return request({
    url: "/api/ni/por/apply/update",
    method: "post",
    data: row,
  });
};

export const inquirySubmit = (row) => {
  return request({
    url: "/api/ni/por/apply/inquirySubmit",
    method: "post",
    data: row,
  });
};

export const orderSubmit = (row) => {
  return request({
    url: "/api/ni/por/apply/orderSubmit",
    method: "post",
    data: row,
  });
};

export const getDetailByProcessInsId = (processInsId) => {
  return request({
    url: "/api/ni/por/apply/detail",
    method: "get",
    params: {
      processInsId,
    },
  });
};

export const apply = (row, processDefKey) => {
  return request({
    url: "/api/ni/por/apply/apply",
    method: "post",
    params: {
      processDefKey,
    },
    data: row,
  });
};

export const submit = (id, processDefKey) => {
  return request({
    url: "/api/ni/por/apply/submit",
    method: "post",
    params: {
      id,
      processDefKey,
    },
  });
};
export const getRawMaterialPage = (current, size, params) => {
  return request({
    url: "/api/ni/por/apply/rawMaterialPage",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getOtherPage = (current, size, params, deptId) => {
  return request({
    url: "/api/ni/por/apply/otherPage",
    method: "get",
    params: {
      ...params,
      deptId,
      current,
      size,
    },
  });
};
export const getListById = (ids) => {
  return request({
    url: "/api/ni/por/apply/list",
    method: "get",
    params: {
      ids,
    },
  });
};

export const buildOrderByPurchaseUser = (ids, purchaseUserId) => {
  return request({
    url: "/api/ni/por/apply/buildOrderByPurchaseUser",
    method: "post",
    params: {
      ids,
      purchaseUserId,
    },
  });
};
export const changeBuyer = (ids, buyer) => {
  return request({
    url: "/api/ni/por/apply/changeBuyer",
    method: "post",
    params: {
      ids,
      buyer,
    },
  });
};

export const getPvSetting = () => {
    return request({
        url: "/api/ni/por/apply/pv-setting",
        method: "get",
    });
}
export const updatePvSetting = (data) => {
  return request({
    url: "/api/ni/por/apply/pv-setting",
    method: "post",
    data,
  });
}

import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/ni/fg/inventory-summary/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}


export const changeRemark = (ids, remark) => {
  return request({
    url: '/api/ni/fg/inventory-summary/changeRemark',
    method: 'post',
    params: {
      ids,
      remark
    }
  })
}

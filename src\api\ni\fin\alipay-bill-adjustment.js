import request from "@/router/axios";

export const getPage = (current, size, params) => {
  return request({
    url: "/api/ni/fin/alipay-bill/adjustment/page",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/ni/fin/alipay-bill/adjustment/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const remove = (ids) => {
  return request({
    url: "/api/ni/fin/alipay-bill/adjustment/remove",
    method: "post",
    params: {
      ids,
    },
  });
};

export const submit = (data) => {
  return request({
    url: "/api/ni/fin/alipay-bill/adjustment/submit",
    method: "post",
    data,
  });
};
export const audit = (ids) => {
  return request({
    url: "/api/ni/fin/alipay-bill/adjustment/audit",
    method: "post",
    params: {
      ids,
    },
  });
};
export const auditCancel = (ids) => {
  return request({
    url: "/api/ni/fin/alipay-bill/adjustment/auditCancel",
    method: "post",
    params: {
      ids,
    },
  });
};

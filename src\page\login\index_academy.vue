<template>
  <div class="login-container1">
    <div class="login-content-wrapper">
      <div class="header-title">
        <div>淄博高新区联信智能制造研究院</div>
      </div>
      <div class="login-weaper animated bounceInDown">
        <div class="login-border">
          <div class="login-main">
            <div class="login-time">
              {{ time }}
            </div>
            <h4 class="login-title">企业信息管理系统</h4>
            <userLogin />
            <div class="login-switch-institute" v-if="redirectUri">
              <el-link type="primary" :underline="false" @click="goBack">
                返回能特异
              </el-link>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
// 优化：只引入需要的组件
import userLogin from "./userlogin";
import { dateFormat } from "@/util/date";

export default {
  // 优化：组件名称建议使用多词形式
  name: "InstituteLogin",
  components: {
    userLogin,
  },
  data() {
    return {
      // 优化：只保留需要的数据
      time: "",
      // 新增：用于存放返回地址
      redirectUri: null,
    };
  },
  created() {
    // 优化：逻辑清晰，只做这个页面需要做的事
    this.getTime();
    this.getRedirectUri();

    // 保留：如果此页面固定属于某个租户，在这里设置是合理的
    // this.$store.commit("SET_TENANT", "460198");
  },
  methods: {
    getTime() {
      setInterval(() => {
        this.time = dateFormat(new Date());
      }, 1000);
    },

    // 新增：从URL获取返回地址
    getRedirectUri() {
      const urlParams = new URLSearchParams(window.location.search);
      const uri = urlParams.get("redirect_uri");
      if (uri) {
        this.redirectUri = decodeURIComponent(uri);
      }
    },

    // 新增：返回主系统的方法
    goBack() {
      if (this.redirectUri) {
        window.location.href = this.redirectUri;
      }
    },
  },
};
</script>

<style lang="scss">
@import "@/styles/login.scss"; // 引入基础样式

.login-container1 {
  position: relative;
  width: 100%;
  height: 100vh;
  background-image: url("/img/bg/bg.jpg");
  background-size: cover;
  background-repeat: no-repeat;

  // 新增：使用Flexbox进行主布局
  display: flex;
  justify-content: center;
  align-items: center;
}

// 新增：内容包装器，用于控制标题和登录框的相对位置
.login-content-wrapper {
  position: relative; // 相对定位，作为内部绝对定位元素的基准
  display: flex;
  flex-direction: row; // 水平排列
  align-items: center; // 垂直居中
  gap: 20px; // 元素之间的间距
}

.header-title {
  // 优化：移除了复杂的定位和浮动
  width: 700px;
  text-align: right;

  div {
    color: #fff;
    font-size: 38px;
    font-weight: bolder;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5); // 增加文字阴影，提升可读性
  }
}

.login-weaper {
  // 优化：不再需要复杂的绝对定位来居中
  position: relative; // 设置为 relative 即可
  top: auto;
  left: auto;
  right: auto;
}

.login-title {
  position: relative; // 为了给返回链接定位
}

.login-switch-institute {
  margin-top: 15px;
  text-align: center;
}
</style>

<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      :row-style="rowStyle"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template #purpose="{ row }">
        <span v-if="row.purpose">{{ row.purpose }}</span>
        <span v-else-if="loading" style="color: red">加载中...</span>
        <span v-else>无</span>
      </template>
      <!-- 项目名称（大类） -->
      <template #catalogId="{ row }">
        <div v-if="row.projectInfo && row.projectInfo.length > 0">
          <div
            v-for="item in row.projectInfo"
            :key="item.catalogName"
            class="catalog-item"
          >
            <span
              :style="{
                backgroundColor: getColor(item.catalogNo),
                borderRadius: '50%',
                width: '20px',
                height: '20px',
                display: 'inline-block',
                textAlign: 'center',
                lineHeight: '20px',
                color: 'white',
                marginRight: '5px',
              }"
            >
              {{ item.catalogNo }}
            </span>
            {{ item.catalogName }}
          </div>
        </div>
        <span v-else-if="row.catalogNo">
          <span
            :style="{
              backgroundColor: getColor(row.catalogNo),
              borderRadius: '50%',
              width: '20px',
              height: '20px',
              display: 'inline-block',
              textAlign: 'center',
              lineHeight: '20px',
              color: 'white',
              marginRight: '5px',
            }"
          >
            {{ row.catalogNo }}
          </span>
          {{ row.catalogName }}
        </span>
        <span v-else>无</span>
      </template>
      <!-- <template #catalogId="{ row }">
        <el-dropdown
          trigger="click"
          @command="changeProjectInfo($event, row)"
          v-if="row.projectInfo.length != 0"
        >
          <i class="el-icon-arrow-down el-icon--right"></i>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
              v-for="item in row.projectInfo"
              :key="item.catalogName"
              :command="item.catalogName"
            >
              {{ item.catalogNo }}-{{ item.catalogName }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <span
          v-if="row.catalogNo"
          :style="{
            backgroundColor: getColor(row.catalogNo),
            borderRadius: '50%',
            width: '20px',
            height: '20px',
            display: 'inline-block',
            textAlign: 'center',
            lineHeight: '20px',
            color: 'white',
          }"
        >
          {{ row.catalogNo }}
        </span>
        {{ row.catalogName }}
      </template> -->
      <!-- 项目名称（小类） -->
      <template #projectTitle="{ row }">
         <div v-if="row.projectInfo && row.projectInfo.length > 0">
          <div
            v-for="item in row.projectInfo"
            :key="item.projectTitle"
            style="font-weight: 800"
          >
              {{ item.projectTitle }}
              ({{ item.projectSerialNo }})
          </div>
         </div>
         <div v-else>无</div>
        <!-- <span v-if="row.projectTitle" style="font-weight: 800">{{
          row.projectTitle
        }}</span>
        <span v-if="row.projectSerialNo"> ({{ row.projectSerialNo }})</span> -->
      </template>
      <template slot="menuLeft">
        <el-button
          type="success"
          size="mini"
          icon="el-icon-download"
          plain
          @click="handleExport"
          >导出
        </el-button>
        <el-button
          type="warning"
          size="mini"
          icon="el-icon-document-checked"
          plain
          @click="handleConfirmReport"
          >查看已确认报表
        </el-button>
        <el-button
          v-if="selectionList.length > 0"
          type="info"
          size="mini"
          icon="el-icon-price-tag"
          @click="handleConfirmTag"
          >确认标记
        </el-button>
        <!-- <el-dropdown>
          <el-button type="primary" size="mini">
            选择项目<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
              v-for="item in catalogDict"
              :key="item.name"
              @command="item.name"
              @click.native="handleChangeProject(item)"
            >
              {{ item.no }}-{{ item.name }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown> -->
        <!-- <el-button
          type="danger"
          size="small"
          icon="el-icon-delete"
          plain
          v-if="permission.niFinMaterialAllocation_delete"
          @click="handleDelete"
          >删 除
        </el-button> -->
        <el-divider direction="vertical" />
        <!-- <el-radio-group v-model="updateType">
          <el-radio :label="1">单条修改</el-radio>
          <el-radio :label="2">按发票号修改</el-radio>
        </el-radio-group> -->
        <el-checkbox-group v-model="assProject">
          <el-checkbox :label="1">已关联项目</el-checkbox>
          <el-checkbox :label="2">无关联项目</el-checkbox>
        </el-checkbox-group>
      </template>
    </avue-crud>
    <confirm-dialog ref="confirmDialogRef"></confirm-dialog>
  </basic-container>
</template>

<script>
import {
  getList,
  add,
  update,
  remove,
  // getMaterialPurpose,
  changeProjectInfo,
  confirmTag,
  changeProject,
} from "@/api/ni/fin/material-allocation";
import { mapGetters } from "vuex";
import { dateFormat } from "@/util/date";
import NProgress from "nprogress";
import { exportBlob } from "@/api/common";
import { downloadXls } from "@/util/util";
import ConfirmDialog from "./components/MaterialAllocationConfirmDialog";

export default {
  components: {
    ConfirmDialog,
  },
  data() {
    return {
      dateRange: this.getDefaultDateRange(),
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      assProject: [1],
      updateType: 2,
      option: {
        selection: true,
        index: true,
        menu: false,
        addBtn: false,
        saveBtn: true,
        updateBtn: true,
        cancelBtn: false,
        editBtn: true,
        delBtn: false,
        viewBtn: false,
        labelWidth: 110,
        span: 8,
        searchIndex: 3,
        searchIcon: true,
        size: "mini",
        searchSize: "mini",
        align: "center",
        height: "auto",
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        dialogClickModal: false,
        showSummary: true,
        sumColumnList: [
          { label: "金额总计:", name: "amount", type: "sum", decimals: 2 },
          { label: "不含税金额总计:", name: "unTax", type: "sum", decimals: 2 },
        ],
        column: [
          {
            label: "日期",
            prop: "date",
            overHidden: true,
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
          },
          {
            label: "日期范围",
            prop: "dateRange",
            type: "daterange",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            startPlaceholder: "日期开始范围",
            endPlaceholder: "日期结束范围",
            search: true,
            searchRange: true,
            showColumn: false,
            hide: true,
            display: false,
          },
          {
            label: "物料名称",
            prop: "materialName",
            minWidth: 120,
            overHidden: true,
            search: true,
            disabled: true,
          },
          {
            label: "规格型号",
            prop: "specification",
            overHidden: true,
            disabled: true,
          },
          {
            label: "数量",
            prop: "num",
            overHidden: true,
          },
          {
            label: "单位",
            prop: "unit",
            overHidden: true,
            type: "select",
            dicData: [],
            props: {
              label: "dictValue",
              value: "dictKey",
            },
          },
          {
            label: "单价",
            prop: "price",
            overHidden: true,
          },
          {
            label: "总价",
            prop: "amount",
            overHidden: true,
          },
          {
            label: "不含税",
            prop: "unTax",
            overHidden: true,
          },
          {
            label: "发票号",
            prop: "billSerialNo",
            minWidth: 120,
            overHidden: true,
            search: true,
          },
          {
            label: "项目名称（大类）",
            prop: "catalogId",
            minWidth: 120,
            overHidden: true,
            type: "select",
            // search: true,
            multiple: true,
            collapseTags: true,
            dicUrl: "/api/ni/fin/material-allocation/getProjectCatalog",
            props: {
              label: "name",
              value: "id",
              desc: "no",
            },
            display: false,
          },
          {
            label: "项目名称（大类）",
            prop: "catalogName",
            minWidth: 120,
            overHidden: true,
            // search: true,
            hide: true,
            showColumn: true,
          },
          {
            label: "项目名称（小类）",
            prop: "projectTitle",
            minWidth: 120,
            overHidden: true,
            disabled: true,
          },
          {
            label: "物料用途",
            prop: "purpose",
            overHidden: true,
            disabled: true,
          },
        ],
      },
      data: [],
      unitDict: [],
      unitDictKeyValue: {},
      exportData: [],
      catalogDict: [],
      catalogDictKeyValue: {},
    };
  },
  created() {
    const dateRange = this.findObject(this.option.column, "dateRange");
    dateRange.searchValue = this.getDefaultDateRange();
    this.query.startDate =
      dateFormat(dateRange.searchValue[0], "yyyy-MM-dd") + " 00:00:00";
    this.query.endDate =
      dateFormat(dateRange.searchValue[1], "yyyy-MM-dd") + " 23:59:59";

    this.dictInit();
  },
  watch: {
    assProject() {
      this.page.currentPage = 1;
      this.onLoad(this.page, this.query);
    },
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        // addBtn: this.vaildData(
        //   this.permission.niFinMaterialAllocation_add,
        //   false
        // ),
        // viewBtn: this.vaildData(
        //   this.permission.niFinMaterialAllocation_view,
        //   false
        // ),
        // delBtn: this.vaildData(
        //   this.permission.niFinMaterialAllocation_delete,
        //   false
        // ),
        // editBtn: this.vaildData(
        //   this.permission.niFinMaterialAllocation_edit,
        //   false
        // ),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  methods: {
    changeProjectInfo(command, row) {
      //确保projectInfo是一个数组并且有数据
      if (!Array.isArray(row.projectInfo) || row.projectInfo.length === 0) {
        console.error("111");
        return;
      }
      // 查找与command匹配的项目信息
      const selectedProjectInfo = row.projectInfo.find(
        (item) => item.catalogName === command
      );

      // 检查是否找到了匹配的项目信息
      if (!selectedProjectInfo) {
        console.error("选择项未找到");
        return;
      }

      const data = {
        ...row,
        catalogNo: selectedProjectInfo.catalogNo,
        catalogName: selectedProjectInfo.catalogName,
        projectTitle: selectedProjectInfo.projectTitle,
        updateType: this.updateType,
      };

      changeProjectInfo(data)
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        })
        .catch((error) => {
          console.error(error);
        });
    },
    //查看已确认报表
    handleConfirmReport() {
      this.$refs.confirmDialogRef.visible = true;
    },
    //修改项目名称
    handleChangeProject(item) {
      if (this.selectionList.length === 0) {
        this.$message({
          type: "warning",
          message: "请至少选择一条数据!",
        });
        return;
      }
      this.$confirm("是否修改已选中数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        changeProject(this.selectionList, item.id).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
      });
    },
    //标记为已确认
    handleConfirmTag() {
      this.$confirm("是否标记为已确认?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        NProgress.start();
        const rows = this.selectionList;
        confirmTag(rows).then(() => {
          NProgress.done();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
      });
    },
    //数据导出
    handleExport() {
      this.$confirm("是否导出当前筛选的数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        NProgress.start();
        const q = this.query;
        q.currentPage = 1;
        q.size = 10000;
        exportBlob(`/api/ni/fin/material-allocation/export`, q).then((res) => {
          if (q.startDate && q.endDate) {
            downloadXls(
              res.data,
              `发票货物明细${q.startDate.substring(
                0,
                10
              )}至${q.endDate.substring(0, 10)}.xlsx`
            );
          } else {
            downloadXls(res.data, `发票货物明细.xlsx`);
          }

          NProgress.done();
        });
      });
    },
    //行样式
    rowStyle({ row }) {
      //已经确认
      if (row.confirm) {
        return {
          backgroundColor: "#ffffcc",
        };
      }
    },
    //大项目编号获取颜色
    getColor(catalogNo) {
      const colorMap = {
        1: "#FF0000",
        2: "#FF7F00",
        3: "#B3EE3A",
        4: "#FFD700",
        5: "#96CDCD",
        6: "#7A378B",
        7: "#BC8F8F",
        8: "#BABABA",
        9: "#404040",
        10: "#A0522D",
        11: "#1874CD",
      };
      return colorMap[catalogNo] || "transparent";
    },
    //字典初始化
    dictInit() {
      //单位
      this.$http
        .get("/api/blade-system/dict-biz/dictionary?code=ni_material_unit")
        .then((res) => {
          const column = this.findObject(this.option.column, "unit");
          column.dicData = res.data.data;
          this.unitDict = res.data.data;
          this.unitDictKeyValue = this.unitDict.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });
      //项目名称
      this.$http
        .get("/api/ni/fin/material-allocation/getProjectCatalog")
        .then((res) => {
          this.catalogDict = res.data.data;
          this.catalogDictKeyValue = this.catalogDict.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });
    },
    //默认搜本月
    getDefaultDateRange() {
      const now = new Date();
      const year = now.getFullYear();
      const month = now.getMonth();

      // 本月第一天
      const firstDay = new Date(year, month, 1);
      // 本月最后一天
      const lastDay = new Date(year, month + 1, 0);

      return [firstDay, lastDay];
    },
    rowSave(row, done, loading) {
      add(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          window.console.log(error);
        }
      );
    },
    rowUpdate(row, index, done, loading) {
      console.log(row);
      update(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          console.log(error);
        }
      );
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen(done, type) {
      if (["edit", "view"].includes(type)) {
        // getDetail(this.form.id).then((res) => {
        //   if (res.data.data) {
        //     this.form = res.data.data;
        //   }
        // });
        console.log();
        // getDetailByParams()
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;

      if (params.dateRange && params.dateRange.length === 2) {
        const startDate = new Date(params.dateRange[0]);
        const endDate = new Date(params.dateRange[1]);

        params.startDate = dateFormat(startDate, "yyyy-MM-dd") + " 00:00:00";
        params.endDate = dateFormat(endDate, "yyyy-MM-dd") + " 23:59:59";
      }

      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      const query = { ...this.query };
      // query.updateType = this.updateType;
      query.assProject = this.assProject;
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, query)
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;

        //二次加载用途
        // const orderItemIds = data.records
        //   .map((item) => item.orderItemId)
        //   .join(",");
        // if (this.data.length > 0) {
        //   getMaterialPurpose(orderItemIds).then((res) => {
        //     const data = res.data.data;
        //     let map = {};
        //     data.forEach((item) => {
        //       map[item.orderItemId] = item.purpose;
        //     });
        //     this.data.forEach((item) => {
        //       item.purpose = map[item.orderItemId];
        //     });
        //   });
        // }
        this.loading = false;
        this.selectionClear();
      });
    },
  },
};
</script>

<style scoped>
.catalog-item {
  margin-bottom: 5px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.catalog-item:last-child {
  margin-bottom: 0;
}
</style>

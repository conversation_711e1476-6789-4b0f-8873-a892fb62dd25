export default {
  height:'auto',
  calcHeight: 30,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: true,
  selection: true,
  dialogClickModal: false,
  column: [
    {
      label: "员工ID",
      prop: "employeeId",
      type: "input",
    },
    {
      label: "户口所在地",
      prop: "householdRegister",
      type: "input",
    },
    {
      label: "户口所在地编码",
      prop: "householdRegisterCode",
      type: "input",
    },
    {
      label: "婚姻状况",
      prop: "marriage",
      type: "input",
    },
    {
      label: "政治面貌",
      prop: "politic",
      type: "input",
    },
    {
      label: "个人信仰",
      prop: "religion",
      type: "input",
    },
    {
      label: "当前所在地",
      prop: "currentAddress",
      type: "input",
    },
    {
      label: "当前所在地编码",
      prop: "currentAddressCode",
      type: "input",
    },
    {
      label: "职称",
      prop: "professionalTitle",
      type: "input",
    },
    {
      label: "驾照",
      prop: "driverLicense",
      type: "input",
    },
    {
      label: "驾龄",
      prop: "drivingExperienceYears",
      type: "input",
    },
    {
      label: "个人爱好",
      prop: "hobbies",
      type: "input",
    },
    {
      label: "英语水平",
      prop: "englishProficiency",
      type: "input",
    },
    {
      label: "是否拥有知识产权",
      prop: "hasIntellectualProperty ",
      type: "input",
    },
    {
      label: "是否具有竞业协议",
      prop: "hasCompetitionRestriction",
      type: "input",
    },
    {
      label: "研究的专业领域",
      prop: "researchField",
      type: "input",
    },
    {
      label: "家庭地址",
      prop: "familyAddress",
      type: "input",
    },
    {
      label: "家庭地址编码",
      prop: "familyAddressCode",
      type: "input",
    },
    {
      label: "状态",
      prop: "status",
      type: "input",
    },
    {
      label: "创建人",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
    },
    {
      label: "创建人所属部门",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
    },
    {
      label: "更新人",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
    },
    {
      label: "更新时间",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
    },
    {
      label: "是否删除",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
    },
    {
      label: "",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
    },
  ]
}

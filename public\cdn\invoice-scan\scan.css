html{
	height:100%
}
body{
	margin:0;
	font-size:14px;
	font-family: "PingFang SC";
	min-width:1200px;
	min-height:900px;
	height:100%;
	-webkit-touch-callout: none;
	-webkit-user-select: none;
	-khtml-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
}
.el-container{
	height:100%;
	border: 1px solid #eee;
}
ul{
	list-style:none;
}
.imageListSelect{
	/* border: 1px solid #909399; */
	/*box-shadow: 0 2px 6px #ADADAD;*/
	border:2px solid #fa7857 !important;
}
.aside{
	border-right:1px solid #eee;
	border-bottom: 1px solid #eee;
	height:100%
}
.aside .header{
	height:40px;
	background-color: #eaeaea;
	line-height: 40px;
	padding-left:20px;
	/* margin-top:10px; */
	color:#09aaff;
}
.scroll::-webkit-scrollbar{
	width: 8px;
	height: 8px;
}

/* 设置滚动条的滑轨 */
.scroll::-webkit-scrollbar-track {
	background-color: #eaeaea;
}

/* 滑块 */
.scroll::-webkit-scrollbar-thumb {
	/* background-color: rgba(0, 0, 0, 0.6); */
	background-color: #cdcdcd;
	border-radius: 4px;
}

/* 滑轨两头的监听按钮 */
.scroll::-webkit-scrollbar-button {
	/* background-color: #444; */
	background-color: #cdcdcd;
	display: none;
}
.el-container .el-header{
	border-bottom: 1px solid #eee;
}
.el-container .el-header .iconContainer{
	width:100%;
	text-align: center;
	height:35px
}
.el-container .el-header .menus{
	width: 100%;
	text-align: center;
	font-size:14px
}
.el-container .el-header .menuContainer{
	float:left;
	margin-left: 15px;
}

.el-container .el-header .menuContainer ul{
	padding-left:10px;
}

.el-container .el-header ul li{
	float:left;
	padding-left:20px;
	cursor: pointer;
}

.el-container .el-header .menuRight{
	float:right;
}

.el-container .el-header .menuRight ul li{
	padding-right:20px;
	float:right;
	cursor: pointer;
}
.el-container .el-header .menuRight .iconContainer{
	height:28px;
}
.el-main{
	height:100%;
	padding:0px;
	position: relative;
	overflow: hidden;
}
.el-main .opt{
	width:58px;
	height:100%;
	border-right:1px solid #eee;
	float:left;
	background-color: #eaeaea;
}
.el-main .opt .active{
	border-radius: 4px;
	border: 1px solid #fff;
	background-color: #cecece;
	margin-left: 4px;
	margin-right: 4px;
}
.image-container{
	overflow: auto;
	height:100%;
	box-sizing:border-box;
	margin:auto
}
.el-main .opt ul li{
	cursor: pointer;
	text-align: center;
	line-height:16px;
	padding-top:4px
}

.mask{
	background-color:rgba(0,0,0,0.6);
	position: fixed;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
}
.mask .messageBox {
	width: 500px;
	border-radius: 3px;
	position: fixed;
	left: 50%;
	top: 45%;
	-webkit-transform: translate(-50%, -50%);
	transform: translate(-50%, -50%);
	background-color: #fff;
	padding: 15px;
}
.mask .messageBox .title {
	border-radius: 3px;
	margin-bottom: 30px;
	padding-bottom: 15px;
	border-bottom: 1px solid #eee;
	font-size: 16px;
}

.mask .messageBox .title .closeReport {
	float: right;
	cursor: pointer;
}
.canvas-container{
	margin:0 auto;
}


.mask-defined{
	position: absolute;
	z-index: 99999;
	background-color: hsla(0,0%,100%,.9);
	margin: 0;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	transition: opacity .3s;
}
.mask-defined-process{
	top: 50%;
	margin-top: -21px;
	text-align: center;
	position: absolute;
	width: 20%;
	left: 40%;
}
.right-menu {
	display: none;
	position: fixed;
	background: #fff;
	border: 1px solid #bababa;
	border-radius: 3px;
	z-index: 999;
	box-shadow: 2px 2px 3px 0px rgba(51, 42, 40, 0.7);
	border-radius: 1px;
}
.right-menu	p{
	margin: 0;
	display: block;
	width: 200px;
	height: 35px;
	line-height: 35px;
	text-align: left;
	padding: 0 24px 0 32px;
	color: #000000;
	cursor: pointer;
	font-size: 15px;
	border-bottom: 1px solid #e8eaed;
}
.right-menu	p:hover{
	background: #e8eaed;
}
.info_button{
	background-color: #eaeaea;
	border-color: #eaeaea;
}
.info_button:hover{
	background-color: #909399;
	border-color: #909399;
}
.mask{
	position:absolute;
	height:100%;
	width:100%;
	filter:Alpha(opacity=20);
	-moz-opacity: 0.2;
	opacity:0.8;
	background-color: #efefef;
	z-index:1
}
.pageBtn{
	position:fixed;
	height:60px;
	bottom: 40%;
}
.right_panel{
	float:right;
	padding:10px;
	border-left: 1px solid #cfcfcf;
	height:100%;
}

.maincanvas_width_right{
	width:calc(100% - 171px)
}
.maincanvas_width{
	width:100%
}

<template>
  <basic-container>
    <avue-skeleton :loading="waiting" avatar :rows="8">
      <avue-title
        style="margin-bottom: 20px"
        :styles="{ fontSize: '20px' }"
        :value="process.name"
      ></avue-title>
      <el-card shadow="never" style="margin-top: 20px">
        <!-- 自定义表单区域 -->
        <avue-form
          v-if="
            option &&
            ((option.column && option.column.length > 0) ||
              (option.group && option.group.length > 0))
          "
          v-model="form"
          ref="form"
          :option="option"
          :defaults.sync="defaults"
          @submit="handleSubmit"
          @error="loading = false"
          :upload-preview="handleUploadPreview"
        >
        </avue-form>
        <!-- 自定义表单区域 -->
      </el-card>

      <el-card shadow="never" style="margin-top: 20px" v-if="showExamForm">
        <wf-examine-form
          ref="examineForm"
          :process="process"
          @user-select="handleUserSelect"
        ></wf-examine-form>
      </el-card>
      <div style="height: 120px"></div>
      <el-row
        class="foot-item avue-affix"
        :style="{
          width: isCollapse ? 'calc(100% - 80px)' : 'calc(100% - 260px)',
        }"
        id="avue-view"
      >
        <el-button
          type="primary"
          size="medium"
          v-loading="loading"
          @click="handleSubmit"
          >发起
        </el-button>
        <el-button
            v-if="permission.wf_process_draft"
            type="success"
            size="mini"
            v-loading="loading"
            @click="handleDraftNotClose(process.id, process.formKey, form)"
        >存为草稿
        </el-button>
        <el-button
            v-if="permission.wf_process_draft"
            type="success"
            size="mini"
            v-loading="loading"
            @click="handleDraft(process.id, process.formKey, form)"
        >存为草稿并关闭
        </el-button>
      </el-row>
    </avue-skeleton>

    <!-- 人员选择弹窗 -->
    <wf-user-select
      ref="user-select"
      :check-type="checkType"
      :default-checked="defaultChecked"
      @onConfirm="handleUserSelectConfirm"
    ></wf-user-select>
    <stock-report-dialog ref="stockReportRef" @submit="stockReportSubmit" />
  </basic-container>
</template>

<script>
import WfExamineForm from "@/views/plugin/workflow/process/components/examForm.vue";
import WfUserSelect from "@/views/plugin/workflow/process/components/user-select";

import exForm from "@/views/plugin/workflow/mixins/ex-form";
import draft from "@/views/plugin/workflow/mixins/draft";
import StockReportDialog from "@/views/ni/depot/components/StockReportDialog";
import debounce from "@/util/debounce";

export default {
  components: {
    WfUserSelect,
    WfExamineForm,
    StockReportDialog,
  },
  mixins: [exForm, draft],
  watch: {
    "$route.query.p": {
      handler(val) {
        if (val) {
          const param = JSON.parse(Buffer.from(val, "base64").toString());
          const { processId } = param;
          if (processId) this.getForm(processId);
        }
      },
      immediate: true,
    },
  },
  computed: {
    showExamForm() {
      const { hideComment, hideCopy, hideExamine } = this.process;
      return !hideComment || !hideCopy || !hideExamine;
    },
  },
  data() {
    return {
      defaults: {},
      form: {},
      option: {
        size: "mini",
        span: 8,
        menuBtn: false,
        column: [
          {
            type: "input",
            label: "创建人",
            span: 12,
            display: true,
            prop: "creator",
            value: "${this.$store.getters.userInfo.nick_name}",
            readonly: true,
          },
          {
            type: "input",
            label: "创建部门",
            span: 12,
            display: true,
            prop: "createDept",
            value: "${this.$store.getters.userInfo.dept_name}",
            readonly: true,
          },
          {
            label: "调拨主题",
            prop: "title",
            minWidth: 120,
            search: true,
            rules: [
              {
                required: true,
                message: "请输入",
                trigger: "blur",
              },
            ],
          },
          {
            label: "调拨编号",
            prop: "serialNo",
            placeholder: "系统自动生成",
            search: true,
            minWidth: 110,
            disabled: true,
          },
          {
            label: "调拨时间",
            prop: "opTime",
            search: true,
            searchRange: true,
            minWidth: 110,
            type: "datetime",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
            rules: [
              {
                required: true,
                message: "请输入",
                trigger: "blur",
              },
            ],
          },
          {
            label: "账套",
            prop: "brand",
            type: "radio",
            minWidth: 90,
            value: "1",
            dicUrl: "/api/blade-system/dict-biz/dictionary?code=ni_brand",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
          },
          {
            label: "调出仓",
            prop: "outDepotId",
            placeholder: " ",
            type: "select",
            dicUrl: "/api/ni/base/depot/info/list?type=7,8&status=2",
            props: {
              label: "name",
              value: "id",
            },
            rules: [
              {
                required: true,
                message: "请选择",
                trigger: "blur",
              },
            ],
          },
          {
            label: "库管人员",
            prop: "keeperId",
            type: "select",
            filterable: true,
            dicUrl: "/api/blade-user/user-vo-list",
            props: {
              label: "name",
              value: "id",
            },
            rules: [
              {
                required: true,
                message: "请选择",
                trigger: "blur",
              },
            ],
          },
          {
            label: "备注",
            prop: "remark",
            hide: true,
            type: "textarea",
            span: 24,
            minRows: 3,
          },
        ],
        group: [
          {
            label: "调拨明细",
            arrow: false,
            column: [
              {
                size: "mini",
                labelWidth: 0,
                label: "",
                prop: "items",
                span: 24,
                type: "dynamic",
                children: {
                  showSummary: true,
                  sumColumnList: [
                    {
                      name: "num",
                      type: "sum",
                      decimals: 1,
                    },
                  ],
                  rowAdd: () => {
                    if (!this.form.outDepotId) {
                      this.$message.warning("请选择调出仓");
                      return;
                    }
                    if (!this.form.brand) {
                      this.$message.warning("请选择用友套账号");
                      return;
                    }
                    this.$refs.stockReportRef.init(
                      this.form.outDepotId,
                      this.form.brand
                    );
                  },
                  align: "center",
                  headerAlign: "center",
                  column: [
                    {
                      label: "库位",
                      prop: "depotLocation",
                      disabled: true,
                    },
                    {
                      label: "产品编码",
                      prop: "materialCode",
                      placeholder: " ",
                      disabled: true,
                    },
                    {
                      label: "品名",
                      placeholder: " ",
                      prop: "materialName",
                      disabled: true,
                    },
                    {
                      label: "规格型号",
                      placeholder: " ",
                      prop: "specification",
                      disabled: true,
                    },
                    {
                      label: "材质",
                      prop: "quality",
                      disabled: true,
                    },
                    {
                      label: "国标",
                      prop: "gb",
                      placeholder: " ",
                      disabled: true,
                    },
                    {
                      label: "单位",
                      prop: "unit",
                      type: "select",
                      dicUrl:
                        "/api/blade-system/dict-biz/dictionary?code=ni_material_unit",
                      props: {
                        label: "dictValue",
                        value: "dictKey",
                      },
                      width: 120,
                      slot: true,
                      disabled: true,
                    },
                    {
                      label: "库存数量",
                      prop: "stockNum",
                      placeholder: " ",
                      type: "number",
                      disabled: true,
                    },
                    {
                      label: "调拨库位",
                      prop: "otherDepotLocation",
                      placeholder: " ",
                    },
                    {
                      label: "调拨数量",
                      prop: "num",
                      placeholder: " ",
                      type: "number",
                      cell: true,
                      rules: [
                        {
                          required: true,
                          message: "请输入数量",
                          trigger: "blur",
                        },
                      ],
                    },
                    {
                      label: "备注",
                      placeholder: " ",
                      type: "textarea",
                      minRows: 1,
                      prop: "remark",
                    },
                  ],
                },
              },
            ],
          },
        ],
      },
      process: {},
      loading: false,
    };
  },
  methods: {
    stockReportSubmit(selectList) {
      if (selectList) {
        this.form.items = selectList.map((item) => {
          return {
            depotId: item.depotId,
            depotLocation: item.depotLocation,
            projectId: item.projectId,
            personId: item.personId,
            materialId: item.materialId,
            materialCode: item.materialCode,
            materialName: item.materialName,
            specification: item.specification,
            quality: item.quality,
            gb: item.gb,
            unit: item.unit,
            stockNum: item.num,
          };
        });
      }
    },
    getForm(processId) {
      this.getStartForm(processId).then((res) => {
        let { process, startForm } = res;
        this.form.processId = process.id;
        const option = this.option;
        const { column, group } = option;

        const groupArr = [];
        const columnArr = this.filterAvueColumn(column, startForm, true).column;
        if (group && group.length > 0) {
          // 处理group
          group.forEach((gro) => {
            gro.column = this.filterAvueColumn(
              gro.column,
              startForm,
              true
            ).column;
            if (gro.column.length > 0) groupArr.push(gro);
          });
        }

        option.column = columnArr;
        option.group = groupArr;
        this.option = option;

        if (this.permission.wf_process_draft) {
          // 查询是否有草稿箱
          this.initDraft(process.id).then((data) => {
            this.$confirm("是否恢复之前保存的草稿？", "提示", {})
              .then(() => {
                this.form = JSON.parse(data);
              })
              .catch(() => {});
          });
        }
        this.waiting = false;
      });
    },
    handleSubmit:debounce(function () {
      this.handleStartProcess(true)
        .then((done) => {
          this.$message.success("发起成功");
          this.handleCloseTag("/plugin/workflow/process/send");
          done();
        })
        .catch(() => {
          this.loading = false;
        });
    },1000)
  },
};
</script>

<style lang="scss" scoped>
.foot-item {
  position: fixed;
  bottom: 0;
  margin-left: -20px;
  // right: 0;
  z-index: 101;
  height: 66px;
  background-color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  -webkit-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
</style>

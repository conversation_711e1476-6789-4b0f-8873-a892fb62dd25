import request from "@/router/axios";

export const getDetail = (originInspectionId) => {
  return request({
    url: "/api/ni/tracing/quality-inspection-modified/detail",
    method: "get",
    params: {
      originInspectionId,
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/ni/tracing/quality-inspection-modified/submit",
    method: "post",
    data: row,
  });
};

export const update = (row) => {
  if(row.adsorptionEN == null){
    row.adsorptionEN = ""
  }
  return request({
    url: "/api/ni/tracing/quality-inspection-modified/submit",
    method: "post",
    data: row,
  });
};

export const getModifiedList = (current, size, params) => {
  return request({
    url: "/api/ni/tracing/quality-inspection-modified/list",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

```java
package com.natergy.ni.feedback.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;


/**
 * 问题状态枚举
 *
 * <AUTHOR>
 * @since 2025-02-27
 */
@AllArgsConstructor
@Getter
public enum FeedbackStatusEnum {
	WAITING_FOR_CLAIM(1,"等待负责人认领"),
	CLAIMED_AND_PROCESSED(2,"认领处理中"),
	TO_BE_CONFIRMED(3,"待发起人确认"),
	MANUAL_ALLOCATION(4,"人工分配中"),
	NO_NEED_TO_HANDLE(5,"无需处理"),
	MANUAL_REJECTION(6,"人工驳回"),
	INITIATOR_REJECTED(7,"发起人驳回"),
	ISSUE_HAS_RESOLVED(8,"问题已解决");
	private final Integer value;
	private final String name;
}

```


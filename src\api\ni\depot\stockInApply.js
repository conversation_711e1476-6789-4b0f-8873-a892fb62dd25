import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/ni/depot/stock/apply/in/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/ni/depot/stock/apply/in/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/ni/depot/stock/apply/in/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/ni/depot/stock/apply/in/save',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/ni/depot/stock/apply/in/update',
    method: 'post',
    data: row
  })
}

export const submitApply = (ids) => {
  return request({
    url: '/api/ni/depot/stock/apply/in/submitApply',
    method: 'post',
    params: {
      ids
    }
  })
}

export const confirm = (row) => {
  return request({
    url: '/api/ni/depot/stock/apply/in/confirm',
    method: 'post',
    data: row
  })
}

export const submit = (ids) => {
  return request({
    url: '/api/ni/depot/stock/apply/in/submit',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const back = (ids) => {
  return request({
    url: '/api/ni/depot/stock/apply/in/back',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const toVoid = (ids) => {
  return request({
    url: '/api/ni/depot/stock/apply/in/toVoid',
    method: 'post',
    params: {
      ids,
    }
  })
}


export const getList1 = (params) => {
  return request({
    url: '/api/ni/depot/stock/apply/in/list',
    method: 'get',
    params: {
      ...params,
    }
  })
}

<template>
  <el-dialog
    ref="cost-apply-dialog"
    v-dialogdrag
    custom-class="cost-apply-dialog"
    :visible.sync="visible"
    title="费用申请选择"
    width="60%"
    :before-close="handleClose"
    append-to-body
  >
    <avue-crud
      v-if="isInit && visible"
      :option="option"
      :table-loading="loading"
      :page.sync="page"
      :data="data"
      v-model="form"
      ref="crud"
      @search-change="searchChange"
      @search-reset="searchReset"
      @current-change="page.currentPage = $event"
      @size-change="page.pageSize = $event"
      @selection-change="selectionList = $event"
      @row-click="rowClick"
      @on-load="onLoad"
    >
      <template v-if="!multiple" #radio="{ row }">
        <el-radio v-model="form.radio" :label="row.id"><i></i></el-radio>
      </template>
      <template #amount="{ row, index, size }">
        <span style="color: green; font-weight: bolder">{{
          Number(row.amount).toLocaleString("zh-CN", {
            minimumFractionDigits: 2,
          })
        }}</span>
      </template>
      <template #costEaAmount="{ row, index, size }">
        <span
          v-if="row.costEaAmount && row.costEaAmount != 0"
          style="color: #f56c6c; font-weight: bolder"
          >{{
            Number(row.costEaAmount).toLocaleString("zh-CN", {
              minimumFractionDigits: 2,
            })
          }}</span
        >
      </template>
      <template #payableApplyAmount="{ row, index, size }">
        <span
          v-if="row.payableApplyAmount && row.payableApplyAmount != 0"
          style="color: #f56c6c; font-weight: bolder"
          >{{
            Number(row.payableApplyAmount).toLocaleString("zh-CN", {
              minimumFractionDigits: 2,
            })
          }}</span
        >
      </template>
    </avue-crud>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose" size="mini">取 消</el-button>
      <el-button type="primary" @click="handleConfirm" size="mini">
        确 定
      </el-button>
    </span>
  </el-dialog>
</template>
<script>
import { getDetail, getPage } from "@/api/ni/fin/cost-apply";

export default {
  props: {
    defaultChecked: String,
    customOption: Object,
    multiple: {
      type: Boolean,
      default: false,
    },
  },
  watch: {
    multiple: {
      handler(val) {
        if (!val) {
          this.$set(this.option, "selection", false);
          this.findObject(this.option.column, "radio").hide = false;
        } else {
          this.$set(this.option, "selection", true);
          this.findObject(this.option.column, "radio").hide = true;
        }
      },
      immediate: true,
    },
  },
  computed: {
    ids() {
      let ids = new Set();
      this.selectionList.forEach((ele) => {
        ids.add(ele.id);
      });
      return Array.from(ids).join(",");
    },
    names() {
      let names = new Set();
      this.selectionList.forEach((ele) => {
        names.add(ele.serialNo);
      });
      return Array.from(names).join(",");
    },
  },
  data() {
    return {
      isInit: false,
      visible: false,
      form: {},
      query: {},
      loading: false,
      selectionList: [],
      data: [],
      props: {
        id: "id",
        name: "title",
        records: "data.data.records",
        total: "data.data.total",
      },
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      option: {
        searchEnter: true,
        size: "mini",
        searchSize: "mini",
        align: "center",
        menu: false,
        addBtn: false,
        header: false,
        border: true,
        tip: false,
        reserveSelection: true,
        highlightCurrentRow: true,
        gutter: 5,
        searchMenuSpan: 6,
        selection: true,
        column: [
          {
            label: "",
            prop: "radio",
            type: "radio",
            width: 55,
            hide: true,
          },
          {
            label: "申请人",
            prop: "createUserName",
            addDisplay: false,
            editDisplay: false,
            overHidden: true,
          },
          {
            label: "申请部门",
            prop: "createDeptName",
            addDisplay: false,
            editDisplay: false,
            overHidden: true,
            hide: true,
          },
          {
            label: "申请时间",
            prop: "createTime",
            type: "datetime",
            minWidth: 127,
            overHidden: true,
            display: false,
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
          },
          {
            label: "申请编号",
            prop: "serialNo",
            overHidden: true,
            minWidth: 135,
            search: true,
            disabled: true,
            placeholder: "系统自动生成",
          },
          {
            label: "申请主题",
            prop: "title",
            placeholder: " ",
            overHidden: true,
            minWidth: 135,
            search: true,
            rules: [
              {
                required: true,
                message: "请输入申请主题",
                trigger: "blur",
              },
            ],
          },
          {
            label: "关联项目",
            prop: "projectTitle",
            overHidden: true,
            placeholder: " ",
            disabled: true,
          },
          {
            label: "关联预算",
            prop: "budgetTitle",
            display: false,
            minWidth: 117,
            overHidden: true,
          },
          {
            label: "总金额",
            prop: "amount",
            type: "number",
            minWidth: 90,
            placeholder: " ",
            disabled: true,
            overHidden: true,
            controls: false,
          },
          {
            label: "报销金额",
            prop: "costEaAmount",
            type: "number",
            minWidth: 90,
            placeholder: " ",
            display: false,
            overHidden: true,
            controls: false,
          },
          {
            label: "付款申请金额",
            prop: "payableApplyAmount",
            type: "number",
            minWidth: 90,
            placeholder: " ",
            display: false,
            overHidden: true,
            controls: false,
          },
          {
            label: "账套",
            prop: "brand",
            type: "radio",
            minWidth: 90,
            value: "1",
            dicUrl: "/api/blade-system/dict-biz/dictionary?code=ni_brand",
            disabled: true,
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            search: true,
          },
          {
            label: "备注",
            overHidden: true,
            hide: true,
            prop: "remark",
            type: "textarea",
            span: 24,
            minRows: 3,
          },
        ],
      },
    };
  },
  mounted() {
    this.init();
  },
  methods: {
    init() {
      if (!this.isInit) {
        if (this.customOption) {
          const { column, props } = this.customOption;
          if (column) this.$set(this.option, "column", column);
          if (props) this.$set(this, "props", props);
        }
        this.isInit = true;
      }
    },
    handleConfirm() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$emit("onConfirm", this.ids, this.names);
      this.handleClose();
    },
    handleClose(done) {
      // this.selectionClear()
      this.visible = false;
      if (done && typeof done == "function") done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionClear() {
      this.selectionList = [];
      if (this.$refs.crud) this.$refs.crud.toggleSelection();
    },
    rowClick(row) {
      if (!this.multiple) {
        this.selectionList = [row];
        this.$set(this.form, "radio", row.id);
      } else this.$refs.crud.toggleSelection([row]);
    },
    async changeDefaultChecked() {
      if (!this.defaultChecked) return;
      let defaultChecked = this.defaultChecked;

      if (this.multiple) {
        // this.selectionClear()
        const checks = defaultChecked.split(",");
        if (checks.length > 0) {
          setTimeout(() => {
            checks.forEach(async (c) => {
              let row = this.data.find((d) => d.id == c); // 当前页查找
              if (!row) {
                row = this.selectionList.find((d) => d.id == c); // 勾选列表查找
                if (!row) {
                  let res = await getDetail(c); // 接口查找
                  if (res.data.data) row = res.data.data;
                }
              }
              if (row && this.$refs.crud)
                this.$refs.crud.toggleRowSelection(row, true);
            });
          }, 500);
        }
      } else {
        let row = this.data.find((d) => d.id == defaultChecked);
        if (!row) {
          let res = await getDetail(defaultChecked);
          if (res.data.data) row = res.data.data;
        }

        if (row) {
          this.selectionList = [row];
          this.$set(this.form, "radio", defaultChecked);
        } else {
          this.selectionList = [];
          this.$set(this.form, "radio", "");
        }
      }
    },
    onLoad(page, params = {}) {
      this.loading = true;
      const query = {
        ...params,
        ...this.query,
        status: 9,
      };
      getPage(page.currentPage, page.pageSize, query).then((res) => {
        this.page.total = this.getAsVal(res, this.props.total);
        this.data = this.getAsVal(res, this.props.records) || [];
        this.loading = false;
        this.changeDefaultChecked();
      });
    },
  },
};
</script>
<style lang="scss">
.cost-apply-dialog {
  display: flex;
  flex-direction: column;
  margin: 0 !important;
  position: absolute;
  top: 40%;
  left: 50%;
  transform: translate(-50%, -40%);
  max-height: calc(100% - 30px);
  max-width: calc(100% - 30px);

  .el-dialog__body {
    flex: 1;
    overflow: auto;
  }
}
</style>

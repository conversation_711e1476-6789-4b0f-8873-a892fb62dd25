<template>
  <basic-container>
    <el-dropdown @command="handleAss">
      <el-button type="primary" size="mini">
        任务分配<i class="el-icon-arrow-down el-icon--right"></i>
      </el-button>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item
          v-for="item in userDict"
          :key="item.id"
          :command="item.id"
        >{{ item.realName }}
        </el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>

    <el-button type="danger" size="mini" @click="handleReject">驳回</el-button>
    <el-divider direction="vertical"/>
    <el-radio-group size="mini" v-model="orders">
      <el-radio-button label="asc">正序</el-radio-button>
      <el-radio-button label="desc">倒序</el-radio-button>
    </el-radio-group>
    <vxe-button
      size="mini"
      status="primary"
      content="导 出"
      @click="handleExcel"
    ></vxe-button>
    <el-divider direction="vertical"/>
    <el-tag>
      当前表格已选择
      <span style="font-weight: bolder">{{ selectionList.length }}</span> 项
      <el-button type="text" size="mini" @click="selectionClear">
        清空
      </el-button>
    </el-tag>
    <el-divider direction="vertical"/>
    <el-checkbox v-model="pv">压力容器</el-checkbox>
    <el-button
      icon="el-icon-refresh"
      circle
      size="mini"
      style="float: right"
      @click="onLoad"
    ></el-button>
    <el-divider/>
    <virtual-scroll
      ref="virtualScroll"
      :data="tableData"
      v-loading="loading"
      :item-size="62"
      key-prop="id"
      :style="{ height: 'calc(92vh - 65px - 80px)' }"
      @change="(renderData) => (data = renderData)"
      @selection-change="handleSelectionChange"
    >
      <el-table
        id="apply-table"
        ref="crud"
        row-key="id"
        :data="data"
        size="mini"
        stripe
        border
        highlight-current-row
        style="width: 100%"
        :cell-style="cellStyle"
        height="100%"
        @sort-change="onSortChange"
        @filter-change="onFilterChange"
      >
        <virtual-column
          width="55"
          fixed
          type="selection"
          align="center"
        ></virtual-column>
        <el-table-column prop="row" label="序号" width="55"/>
        <el-table-column
          label="品名"
          min-width="95"
          prop="materialName"
          column-key="materialNames"
          :filters="materialList"
          show-overflow-tooltip
        >
          <template v-slot:default="{ row, index, disabled, size }">
            <el-button type="text" size="mini" @click="showHistoryDialog(row)">
              {{ row.materialName }}
            </el-button>
          </template>
        </el-table-column>
        <el-table-column
          label="规格"
          prop="specification"
          min-width="105"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          label="材质"
          prop="quality"
          min-width="95"
          show-overflow-tooltip
        />
        <el-table-column
          label="待采购量"
          prop="unOrderNum"
          min-width="85"
          align="center"
        >
          <template v-slot:default="scope">
            <span style="font-weight: bolder; color: red">{{
                scope.row.num -
                (scope.row.purchaseNum ? scope.row.purchaseNum : 0) +
                (scope.row.backNum ? scope.row.backNum : 0) -
                (scope.row.rejectNum ? scope.row.rejectNum : 0)
              }}</span>
          </template>
        </el-table-column>
        <el-table-column label="单位" prop="unit" min-width="70">
          <template v-slot:default="{ row }">
            <span>{{ unitDictKeyValue[row.unit] }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="明细备注"
          prop="remark"
          min-width="110"
          column-key="remarks"
          show-overflow-tooltip
          :filters="remarkList"
        >
          <template v-slot:default="{ row }">
            <span v-if="row.remark">{{ htmlDecode(row.remark) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="申请附件" prop="attachNum" min-width="80">
          <template v-slot:default="{ row }">
            <el-link v-if="row.attachNum" type="primary" target="_blank" @click="rowAttach(row)">
              附件({{ row.attachNum }})
            </el-link>
          </template>
        </el-table-column>
        <el-table-column
          label="申请人"
          prop="createUserName"
          min-width="80"
          show-overflow-tooltip
          column-key="createUserNames"
          :filters="createUserNameList"
        >
          <template v-slot:default="{ row, size, index }">
            <span v-if="row.crash" style="color: white">
              {{ row.createUserName }}
            </span>
            <span v-else>{{ row.createUserName }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="需用日期"
          min-width="135"
          prop="needDate"
          column-key="needDates"
          :filters="needDateList"
          show-overflow-tooltip
          sortable
        >
          <template v-slot:default="{ row, size, index }">
            <span>{{ row.needDate }}</span>
            <el-tag
              size="mini"
              v-if="row.status && row.status != 9"
              type="warning"
              effect="plain"
            >{{ statusDictKeyValue[row.status] }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="比价" prop="inquiries" min-width="80">
          <template v-slot:default="{ row, index, size }">
            <a
              v-if="row.inquiries && row.inquiries.length > 0"
              href="#"
              style="
                color: #f56c6c;
                text-decoration: underline;
                font-weight: bold;
              "
              @click.p.prevent="rowInquiry(row, index)"
            >
              比价
              <span v-if="row.inquiries"> [{{ row.inquiries.length }}] </span>
            </a>
            <span v-else>无比价</span>
          </template>
        </el-table-column>
        <el-table-column
          label="类型"
          min-width="70"
          prop="cost"
          column-key="costs"
          :filters="costList"
        >
          <template v-slot:default="{ row, index, disabled, size }">
            <el-tag size="mini" type="danger" effect="dark" v-if="row.cost">
              费用
            </el-tag>
            <el-tag size="mini" type="info" effect="plain" v-else>
              实物
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          label="编码"
          min-width="120"
          prop="materialCode"
          column-key="materialCodes"
          :filters="materialCodeList"
        >
          <template v-slot:default="{ row, index, disabled, size }">
            <el-button type="text" size="mini" @click="showHistoryDialog(row)">
              {{ row.materialCode }}
            </el-button>
          </template>
        </el-table-column>
        <el-table-column
          label="收货地"
          width="70"
          prop="receivingAddress"
          column-key="receivingAddresses"
        >
          <template v-slot:default="{ row }">
            <span>{{ receivingAddressDictKeyValue[row.receivingAddress] }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="国标"
          prop="gb"
          min-width="95"
          show-overflow-tooltip
          column-key="gbs"
          :filters="gbList"
        />
        <el-table-column label="建议单价" prop="price" min-width="95"/>
        <el-table-column label="建议金额" prop="amount" min-width="95">
          <template v-slot:default="{ row, index, size }">
            <span style="color: green; font-weight: bolder">{{
                row.amount
              }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="用途"
          prop="purpose"
          min-width="100"
          show-overflow-tooltip
        />
        <el-table-column label="申请数量" prop="num" min-width="95"/>
        <el-table-column
          label="压力容器"
          prop="pv"
          min-width="75"
          column-key="units"
        >
          <template v-slot:default="{ row, index, size }">
            <el-tag size="mini" v-if="row.pv" type="danger" effect="dark">
              是
            </el-tag>
            <el-tag size="mini" v-else type="info" effect="plain">否</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          label="固定资产"
          prop="fa"
          min-width="75"
          column-key="units"
        >
          <template v-slot:default="{ row, index, size }">
            <el-tag size="mini" v-if="row.fa" type="danger" effect="dark">
              是
            </el-tag>
            <el-tag size="mini" v-else type="info" effect="plain">否</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          label="采购申请"
          prop="applySerialNo"
          min-width="120"
          show-overflow-tooltip
          column-key="applySerialNos"
          :filters="applySerialNoList"
        >
          <template v-slot:default="{ row, index, size }">
            <flow-timeline-popover
              v-if="row.processInsId"
              :process-ins-id="row.processInsId"
              form-key="wf_ex_por/Apply"
              process-def-key="process_por_apply"
              v-model="row.applySerialNo"
              :flow.sync="row.flow"
              trigger="click"
              lazy
            />
            <span v-else>{{ row.applySerialNo }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="采购分类"
          prop="type"
          min-width="95"
          column-key="types"
          :filters="typeList"
          show-overflow-tooltip
        >
          <template v-slot:default="{ row }">
            <span>{{ typeDictKeyValue[row.type] }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="所属项目"
          prop="projectTitle"
          min-width="95"
          show-overflow-tooltip
          column-key="projectTitles"
          :filters="projectTitleList"
        />
        <el-table-column
          label="关联预算"
          prop="budgetSerialNo"
          min-width="115"
          show-overflow-tooltip
          column-key="budgetSerialNos"
          :filters="budgetSerialNoList"
        >
          <template v-slot:default="{ row, disabled, size, index }">
            <span>{{ row.budgetSerialNo }}-{{ row.budgetTitle }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="采购类型"
          prop="subType"
          min-width="95"
          column-key="subTypes"
          show-overflow-tooltip
          :filters="subTypeList"
        >
          <template v-slot:default="{ row, disabled, size, index }">
            <span>{{
                `${subTypeDictKeyValue[row.subType]}(${row.subType})`
              }}</span>
          </template>
        </el-table-column>
        <el-table-column label="账套" prop="brand" min-width="75">
          <template v-slot:default="{ row, disabled, index }">
            <el-tag
              v-if="row.brand"
              size="mini"
              :effect="row.brand === '1' ? 'dark ' : 'plain'"
            >
              {{ brandDictKeyValue[row.brand] }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          label="申请时间"
          prop="createTime"
          min-width="100"
          show-overflow-tooltip
        />
        <el-table-column label="采购方式" prop="buyer" min-width="75">
          <template v-slot:default="{ row, index, size }">
            <el-tag
              v-if="row.buyer === 2"
              effect="dark"
              type="danger"
              size="mini"
            >
              办公室
            </el-tag>
            <el-tag v-else-if="row.buyer===1" size="mini" effect="plain">采购</el-tag>
            <el-tag v-else size="mini" type="danger" effect="plain">演绎</el-tag>
          </template>
        </el-table-column>
      </el-table>
    </virtual-scroll>
    <order-item-history-dialog ref="orderItemHistoryRef"/>
    <order-cost-item-history-dialog ref="orderCostItemHistoryRef"/>
    <attach-dialog ref="attachRef" code="public" detail :delBtn="false"/>
    <el-dialog
      title="比价"
      append-to-body
      :visible.sync="inquiry.visible"
      width="1000px"
    >
      <avue-form
        v-if="inquiry.visible"
        :option="inquiry.option"
        v-model="inquiry.form"
      >
      </avue-form>
    </el-dialog>
  </basic-container>
</template>
<script>
import {unAssItemList} from "@/api/ni/por/to-be-order";
import {dateFormat} from "@/util/date";
import SupplierMultipleSelect from "@/views/ni/base/components/SupplierSelect";
import {getDetail as getSupplierDetail} from "@/api/ni/base/supplier/supplierinfo";
import ContractSelect from "@/views/ni/base/components/ContractSelect";
import OrderItemHistoryDialog from "@/views/ni/por/components/OrderItemHistoryDialog";
import OrderCostItemHistoryDialog from "@/views/ni/por/components/OrderCostItemHistoryDialog";
import {buildOrderByPurchaseUser} from "@/api/ni/por/apply";
import {reject} from "@/api/ni/por/apply-item";
import {VirtualColumn, VirtualScroll} from "@/components/virtual-scroll";
import {mapGetters} from "vuex";
import AttachDialog from "@/components/attach-dialog";
import FlowTimelinePopover from "@/components/wf-flow-timeline/popover";
import debounce from "@/util/debounce";

export default {
  components: {
    SupplierMultipleSelect,
    ContractSelect,
    OrderItemHistoryDialog,
    OrderCostItemHistoryDialog,
    VirtualScroll,
    VirtualColumn,
    AttachDialog,
    FlowTimelinePopover,
  },
  watch: {
    "$route.query.type": {
      handler(val) {
        if (val) {
          this.type = val;
        }
      },
      immediate: true,
    },
    pv: {
      handler(val) {
        if (val) this.query.pv = 1;
        else this.query.pv = null;
        this.onLoad();
      },
      immediate: false,
    },
    orders: {
      handler() {
        this.onLoad();
      },
    },
  },
  beforeRouteEnter(to, from, next) {
    to.meta.keepAlive = true;
    next();
  },
  data() {
    return {
      type: null,
      form: {},
      query: {},
      selectionList: [],
      loading: true,
      data: [],
      tableData: [],
      statusDictKeyValue: {},
      subTypeDict: [],
      subTypeDictKeyValue: {},
      brandDict: [],
      brandDictKeyValue: {},
      userDict: [],
      userDictKeyValue: {},
      unitDict: [],
      unitDictKeyValue: {},
      typeDict: [],
      typeList: [],
      costList: [],
      typeDictKeyValue: {},
      receivingAddressDict:[],
      receivingAddressDictKeyValue: {},
      createUserNameList: [],
      needDateList: [],
      gbList: [],
      remarkList: [],
      materialList: [],
      materialCodeList: [],
      applySerialNoList: [],
      projectTitleList: [],
      budgetSerialNoList: [],
      subTypeList: [],
      filterVal: {
        subTypes: [],
        // 过滤的值，可以有多个
        costs: [],
        materialNames: [],
        materialCodes: [],
        applySerialNos: [],
        types: [],
        projectTitles: [],
        budgetSerialNos: [],
        createUserNames: [],
        remarks: [],
        gbList: [],
        needDateList: [],
      },
      inquiry: {
        visible: false,
        option: {
          submitText: "修改",
          detail: false,
          size: "mini",
          emptyBtn: false,
          labelWidth: 120,
          column: [
            {
              label: "采购原因",
              prop: "reason",
              type: "textarea",
              minRows: 1,
              placeholder: " ",
              rules: [
                {
                  required: true,
                  message: "请输入采购原因",
                  trigger: "blur",
                },
              ],
            },
            {
              label: "采购风险",
              prop: "risk",
              type: "textarea",
              minRows: 1,
              placeholder: " ",
              rules: [
                {
                  required: true,
                  message: "请输入采购风险",
                  trigger: "blur",
                },
              ],
            },
            {
              label: "采购带来的收益及效果",
              prop: "profit",
              span: 24,
              type: "textarea",
              minRows: 2,
              placeholder: " ",
              rules: [
                {
                  required: true,
                  message: "请输入采购带来的收益及效果",
                  trigger: "blur",
                },
              ],
            },
            {
              label: "项目安全性分析及风险控制",
              prop: "security",
              span: 24,
              minRows: 2,
              type: "textarea",
              placeholder: " ",
              rules: [
                {
                  required: true,
                  message: "请输入项目安全性分析及风险控制",
                  trigger: "blur",
                },
              ],
            },
            {
              label: "项目环保因素分析及风险控制",
              prop: "ep",
              span: 24,
              minRows: 2,
              type: "textarea",
              placeholder: " ",
              rules: [
                {
                  required: true,
                  message: "请输入项目环保因素分析及风险控制",
                  trigger: "blur",
                },
              ],
            },
            {
              label: "验收标准",
              prop: "ac",
              span: 12,
              minRows: 1,
              type: "textarea",
              placeholder: " ",
            },
            {
              label: "技术参数",
              prop: "technology",
              span: 12,
              minRows: 1,
              type: "textarea",
              placeholder: " ",
            },
            {
              label: "供应商比价",
              prop: "inquiries",
              span: 24,
              type: "dynamic",
              children: {
                span: 8,
                addBtn: false,
                delBtn: false,
                align: "center",
                headerAlign: "center",
                size: "mini",
                column: [
                  {
                    label: "供应商名称",
                    prop: "supplier",
                    placeholder: " ",
                    type: "textarea",
                    overHidden: true,
                    minRows: 1,
                    rules: [
                      {
                        required: true,
                        message: "请填写供应商名称",
                        trigger: "blur",
                      },
                    ],
                  },
                  {
                    label: "联系方式",
                    prop: "supplierLinkman",
                    placeholder: " ",
                    type: "textarea",
                    overHidden: true,
                    minRows: 1,
                    rules: [
                      {
                        required: true,
                        message: "请填写供应商联系人",
                        trigger: "blur",
                      },
                    ],
                  },

                  {
                    label: "比价详情",
                    prop: "remark",
                    type: "textarea",
                    minRows: 1,
                    placeholder: " ",
                    overHidden: true,
                    rules: [
                      {
                        required: true,
                        message: "请填写比价详情",
                        trigger: "blur",
                      },
                    ],
                  },
                  {
                    label: "建议供应商",
                    prop: "recommend",
                    type: "radio",
                    value: false,
                    dicData: [
                      {
                        label: "是",
                        value: true,
                      },
                      {
                        label: "否",
                        value: false,
                      },
                    ],
                    placeholder: " ",
                    rules: [
                      {
                        required: true,
                        message: "请选择建议供应商",
                        trigger: "blur",
                      },
                    ],
                    change: ({value, index}) => {
                      if (value)
                        this.inquiry.form.inquiries.forEach((item, i) => {
                          if (i === index) {
                            return;
                          }
                          item.recommend = false;
                        });
                    },
                  },
                ],
              },
            },
            {
              label: "部门主管意见",
              prop: "comments1",
              minRows: 1,
              type: "textarea",
              placeholder: " ",
              disabled: true,
              span: 12,
            },
            {
              label: "工艺组意见",
              prop: "comments2",
              minRows: 1,
              type: "textarea",
              placeholder: " ",
              disabled: true,
              span: 12,
            },
            {
              label: "总经理意见",
              prop: "comments3",
              minRows: 1,
              type: "textarea",
              placeholder: " ",
              disabled: true,
              span: 12,
            },
            {
              label: "总经办意见",
              prop: "comments4",
              minRows: 1,
              type: "textarea",
              placeholder: " ",
              disabled: true,
              span: 12,
            },
          ],
        },
        form: {},
        historyShow: false,
      },
      sortVal: {}, // 排序的值，只有一个
      originList: [],
      pv: false,
      orders: "desc",
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
    filteredData() {
      let list = this.tableData.filter((item, index) => {
        if (index < this.currentStartIndex) {
          return false;
        } else if (index > this.currentEndIndex) {
          return false;
        } else {
          return true;
        }
      });
      return list;
    },
  },
  mounted() {
    this.onLoad();
    this.dictInit();
  },
  methods: {
    rowInquiry(row, index) {
      Object.keys(this.inquiry.form).forEach(
        (key) => (this.inquiry.form[key] = "")
      );
      this.inquiry.form = {
        ...row,
        index: [index],
      };
      this.inquiry.option.detail = true;
      this.inquiry.visible = true;
    },
    handleExcel() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择要导出的数据");
        return;
      }
      this.$Export.excel({
        title: "订单明细",
        columns: [
          {
            label: "序号",
            prop: "row",
          },
          {
            label: "品名",
            prop: "materialName",
          },
          {
            label: "规格",
            prop: "specification",
          },
          {
            label: "材质",
            prop: "quality",
          },
          {
            label: "数量",
            prop: "num",
          },
          {
            label: "单位",
            prop: "unit",
          },
          {
            label: "单价",
            prop: "price",
          },
          {
            label: "金额",
            prop: "amount",
          },
          {
            label: "申请备注",
            prop: "applyRemark",
          },
          {
            label: "用途",
            prop: "purpose",
          },
          {
            label: "供应商",
            prop: "supplier",
          },
          {
            label: "申请人",
            prop: "createUserName",
          },
          {
            label: "需用日期",
            prop: "needDate",
          },
          {
            label:'紧急申购',
            prop:'crash'
          },
          {
            label: "类型",
            prop: "cost",
          },
          {
            label: "编码",
            prop: "materialCode",
          },
          {
            label: "国标",
            prop: "gb",
          },
          {
            label: "备注",
            prop: "remark",
          },
          {
            label: "采购申请编号",
            prop: "serialNo",
          },
          {
            label: "采购分类",
            prop: "type",
          },
          {
            label: "所属项目",
            prop: "projectTitle",
          },
          {
            label: "关联预算",
            prop: "budgetSerialNo",
          },
        ],
        data: this.selectionList.map((item) => {
          return {
            ...item,
            unit: this.unitDictKeyValue[item.unit],
            cost: item.cost ? "费用" : "实物",
            applyType: this.typeDictKeyValue[item.applyType],
          };
        }),
      });
    },
    handleReject() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择要驳回的数据");
        return;
      }
      this.$prompt("确定要要选择的数据驳回?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        inputPlaceholder: "请输入驳回原因",
        inputErrorMessage: "驳回原因",
        inputValidator: (value) => {
          if (!value.trim()) {
            return "驳回原因不能为空";
          }
        },
      })
        .then(({value}) => {
          reject(this.ids, value).then(() => {
            this.onLoad();
            this.$message({
              type: "success",
              message: "操作成功!",
            });
          });
        })
        .catch(() => {
        });
    },
    htmlDecode(input) {
      const doc = new DOMParser().parseFromString(input, "text/html");
      return doc.documentElement.textContent;
    },
    rowAttach(row) {
      this.$refs.attachRef.init(row.id, "ni_por_apply_item");
    },
    onSortChange({prop, order}) {
      this.sortVal = {prop, order};
      this.doFilter();
    },
    onFilterChange(filters) {
      console.log(filters);
      Object.assign(this.filterVal, filters);
      this.doFilter();
    },
    doFilter() {
      // 过滤
      const {
        costs,
        subTypes,
        materialNames,
        materialCodes,
        applySerialNos,
        types,
        projectTitles,
        budgetSerialNos,
        createUserNames,
        remarks,
        gbs,
      } = this.filterVal;
      this.tableData = this.originList.filter((item) => {
        return (
          (!subTypes ||
            !subTypes.length ||
            subTypes.some((val) => item.subType === val)) &&
          (!costs ||
            !costs.length ||
            costs.some((val) => {
              if (!val) return !item.cost || item.cost === val;
              return item.cost === val;
            })) &&
          (!materialNames ||
            !materialNames.length ||
            materialNames.some((val) => item.materialName === val)) &&
          (!materialCodes ||
            !materialCodes.length ||
            materialCodes.some((val) => item.materialCode === val)) &&
          (!applySerialNos ||
            !applySerialNos.length ||
            applySerialNos.some((val) => item.applySerialNo === val)) &&
          (!types || !types.length || types.some((val) => item.type === val)) &&
          (!projectTitles ||
            !projectTitles.length ||
            projectTitles.some((val) => item.projectTitle === val)) &&
          (!budgetSerialNos ||
            !budgetSerialNos.length ||
            budgetSerialNos.some((val) => item.budgetSerialNo === val)) &&
          (!createUserNames ||
            !createUserNames.length ||
            createUserNames.some((val) => item.createUserName === val)) &&
          (!remarks ||
            !remarks.length ||
            remarks.some((val) => item.remark === val)) &&
          (!gbs || !gbs.length || gbs.some((val) => item.gb === val))
        );
      });
      // 排序
      const {prop, order} = this.sortVal;
      if (order) {
        this.tableData.sort((a, b) => {
          if (order === "ascending") {
            return a[prop] - b[prop];
          } else if (order === "descending") {
            return b[prop] - a[prop];
          }
        });
      }
      this.updateFilters();
    },
    handleSelectionChange(val) {
      this.selectionList = val;
    },
    handleAss: debounce(function (val) {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择要分配的数据");
        return;
      }
      const ids = this.selectionList.map((item) => item.id).join(",");
      //TODO 分配任务
      buildOrderByPurchaseUser(ids, val).then(() => {
        this.onLoad();
        this.$message({
          type: "success",
          message: "操作成功!",
        });
      });
    }, 1000),
    dictInit() {
      this.costList = [
        {
          text: "费用",
          value: true,
        },
        {
          text: "实物",
          value: false,
        },
      ];
      this.$http .get("/api/blade-system/dict-biz/dictionary?code=ni_por_receiving_address")
        .then((res) => {
          this.receivingAddressDict = res.data.data;
          this.receivingAddressDictKeyValue = this.receivingAddressDict.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });
      this.$http.get("/api/ni/por/type/list?status=0").then((res) => {
        this.subTypeDict = res.data.data;
        this.subTypeList = Array.from(this.subTypeDict).map((item) => ({
          text: `${item.name}(${item.code})`,
          value: item.code,
        }));
        this.subTypeDictKeyValue = this.subTypeDict.reduce((acc, cur) => {
          acc[cur.code] = cur.name;
          return acc;
        }, {});
      });
      this.$http
        .get("/api/blade-system/dict-biz/dictionary?code=ni_brand")
        .then((res) => {
          this.brandDict = res.data.data;
          this.brandDictKeyValue = this.brandDict.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });
      this.$http
        .get("/api/blade-system/dict/dictionary?code=data_status")
        .then((res) => {
          this.statusDict = res.data.data;
          this.statusDictKeyValue = this.statusDict.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });
      this.$http.get("/api/ni/por/order/purchaseUserList").then((res) => {
        this.userDict = res.data.data;
        this.userDictKeyValue = this.userDict.reduce((acc, cur) => {
          acc[cur.id] = cur.realName;
          return acc;
        }, {});
      });
      this.$http
        .get("/api/blade-system/dict-biz/dictionary?code=ni_material_unit")
        .then((res) => {
          this.unitDict = res.data.data;
          this.unitDictKeyValue = this.unitDict.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });
      this.$http
        .get("/api/blade-system/dict-biz/dictionary?code=ni_por_type")
        .then((res) => {
          this.typeDict = res.data.data;
          this.typeList = Array.from(this.typeDict).map((item) => ({
            text: item.dictValue,
            value: item.dictKey,
          }));
          this.typeDictKeyValue = this.typeDict.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });
    },
    cellStyle({row, column}) {
      if ("createUserName" === column.property && row.crash) {
        return "backgroundColor:#F56C6C";
      }
      if ("row" === column.property && row.yearsAgo) {
        return {
          backgroundColor: "#F56C6C",
          color: "#fff",
        };
      }
      if ("materialName" === column.property && row.type === '21') {
        return {
          background: 'url(/img/yzdyzb.png) no-repeat left center / contain',
          backgroundColor: "#F56C6C",
          color: "#fff",
          position: 'relative', // 确保伪元素可以正确定位
        };
      }
    },
    showCostHistoryDialogByAccountingId(row) {
      this.$refs.orderCostItemHistoryRef.initByAccountingId(row);
    },
    showCostHistoryDialog(row) {
      this.$refs.orderCostItemHistoryRef.initByCostName(row);
    },
    showHistoryDialogByMaterialTypeId(row) {
      this.$refs.orderItemHistoryRef.initByMaterialTypeId(row);
    },
    showHistoryDialog(row) {
      this.$refs.orderItemHistoryRef.initByMaterialId(row);
    },
    handleContractIdConfirm(selectList) {
      if (selectList != null && selectList.length > 0) {
        const contract = selectList[0];
        this.form.contractAmount = contract.amount;
        console.log(contract);
        this.form.brand = contract.brand;
        if (contract.bbType === "supplier") {
          this.form.supplier = contract.bname;
          this.form.supplierId = contract.b;
          getSupplierDetail(contract.b).then((res) => {
            const data = res.data.data;
            const selectList = [data];
            this.handleSupplierSubmit(selectList);
          });
        }
      }
    },
    handleSupplierSubmit(selectList) {
      this.form.supplierId = selectList
        .map((item) => {
          return item.id;
        })
        .join(",");
      this.form.supplierLinkman = selectList[0].linkman;
      this.form.supplierLinkPhone = selectList[0].linkPhone;
      this.form.title =
        dateFormat(new Date(), "yyyy-MM-dd") +
        "[" +
        this.form.supplier +
        "]采购订单";
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.virtualScroll.clearSelection();
    },
    searchReset() {
      this.query = {};
      this.pv = false;
      this.orders = "desc";
      this.selectionClear();
      this.onLoad();
    },
    searchChange(params, done) {
      this.onLoad(params);
      done();
    },
    refreshChange() {
      this.selectionClear();
      this.onLoad(this.query);
    },
    onLoad(params = {}) {
      this.loading = true;
      unAssItemList({
        ...params,
        ...this.query,
        orders: this.orders,
        status: 9,
        type: this.type,
      }).then((res) => {
        this.tableData = res.data.data;
        this.originList = [...this.tableData];
        this.updateFilters();
        this.loading = false;
        this.doFilter();
        this.selectionClear();
      });
    },
    updateFilters() {
      const createUserNames = new Set();
      const s = new Set();
      const materialCodes = new Set();
      const applySerialNoS = new Set();
      const projectTitles = new Set();
      const remarks = new Set();
      const gbs = new Set();
      const needDates = new Set();
      const budgetSerialNos = new Set();
      this.tableData.forEach((item) => {
        s.add(item.materialName);
        materialCodes.add(item.materialCode);
        applySerialNoS.add(item.applySerialNo);
        projectTitles.add(item.projectTitle);
        createUserNames.add(item.createUserName);
        remarks.add(item.remark);
        gbs.add(item.gb);
        needDates.add(item.needDate);
        budgetSerialNos.add(item.budgetSerialNo);
      });
      this.budgetSerialNoList = Array.from(budgetSerialNos).map((item) => ({
        text: item,
        value: item,
      }));
      this.needDateList = Array.from(needDates).map((item) => ({
        text: item,
        value: item,
      }));
      this.createUserNameList = Array.from(createUserNames).map((item) => ({
        text: item,
        value: item,
      }));
      this.remarkList = Array.from(remarks).map((item) => ({
        text: item,
        value: item,
      }));
      this.gbList = Array.from(gbs).map((item) => ({
        text: item,
        value: item,
      }));
      this.materialList = Array.from(s).map((item) => ({
        text: item,
        value: item,
      }));
      this.materialCodeList = Array.from(materialCodes).map((item) => ({
        text: item,
        value: item,
      }));
      this.applySerialNoList = Array.from(applySerialNoS).map((item) => ({
        text: item,
        value: item,
      }));
      this.projectTitleList = Array.from(projectTitles).map((item) => ({
        text: item,
        value: item,
      }));
    },
  },
};
</script>

<style lang="scss" scoped>
.wrapper {
  height: calc(100% - 40px);
}
</style>

```java
// 定义包路径，属于问题反馈模块的监听器组件
package com.natergy.ni.feedback.listener;

// 导入FastJSON相关类，用于JSON序列化和反序列化
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.PropertyNamingStrategy;
import com.alibaba.fastjson.serializer.SerializeConfig;
// 导入MyBatis-Plus的查询和更新条件构造器
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
// 导入问题反馈相关的DTO、实体和枚举
import com.natergy.ni.feedback.dto.*;
import com.natergy.ni.feedback.entity.FeedbackEntity;
import com.natergy.ni.feedback.enums.FeedbackStatusEnum;
import com.natergy.ni.feedback.service.IFeedbackService;
// 导入Lombok日志注解
import lombok.extern.slf4j.Slf4j;
// 导入Apache的Bean工具类和字符串工具类
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang3.StringUtils;
// 导入Flowable任务监听器相关类
import org.flowable.task.service.delegate.DelegateTask;
import org.flowable.task.service.delegate.TaskListener;
// 导入Blade框架的缓存工具类、REST模板工具类和常量类
import org.springblade.common.cache.UserCache;
import org.springblade.common.utils.RestTemplateUtil;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.modules.system.entity.User;
import org.springblade.plugin.workflow.core.constant.WfProcessConstant;
// 导入Spring注解和HTTP相关类
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

// 导入Java反射、集合等工具类
import java.lang.reflect.InvocationTargetException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 发起转出流程监听器-确认转出
 * （在问题反馈流程中，确认转出负责人时触发的监听器）
 *
 * <AUTHOR>  // 作者标识
 * @since 2025-03-24  // 类创建日期
 */
// @Slf4j：Lombok注解，自动生成日志对象
@Slf4j
// @Component("ResponsibilityListener")：Spring注解，将类注册为组件，名称为"ResponsibilityListener"
// 供Flowable工作流引擎通过该名称调用此监听器
@Component("ResponsibilityListener")
// 实现Flowable的TaskListener接口，作为任务监听器
public class ResponsibilityListener implements TaskListener {

	// 从配置文件注入Dify服务的请求URL（用于AI分析问题）
	@Value("${dify.feedback.post-url}")
	private String postUrl;

	// 从配置文件注入删除向量数据库点的URL
	@Value("${dify.feedback.delete-point-url}")
	private String deletePointUrl;

	// 从配置文件注入Dify服务的API密钥
	@Value("${dify.feedback.api-key}")
	private String apiKey;

	// 从配置文件注入Dify服务的响应模式
	@Value("${dify.feedback.response-mode}")
	private String responseMode;

	// 从配置文件注入Dify服务的用户账号
	@Value("${dify.feedback.user}")
	private String userAccout;

	// 注入问题反馈服务，用于操作问题反馈数据
	private final IFeedbackService feedbackService;

	// 构造函数注入问题反馈服务
	public ResponsibilityListener(IFeedbackService feedbackService) {
		this.feedbackService = feedbackService;
	}

	// @Transactional(rollbackFor = Exception.class)：声明事务，发生异常时回滚
	@Transactional(rollbackFor = Exception.class)
	// 实现TaskListener接口的notify方法，任务事件触发时执行
	@Override
	public void notify(DelegateTask delegateTask) {
		// 获取流程实例ID（用于关联问题反馈实体和加锁）
		String processInstanceId = delegateTask.getProcessInstanceId();

		// 同步代码块：使用流程实例ID的intern()方法作为锁对象，确保同一流程实例的操作串行执行
		synchronized (processInstanceId.intern()) {
			// 获取任务相关的流程变量（包含表单数据、审批结果等）
			Map<String, Object> variables = delegateTask.getVariables();

			// 初始化问题反馈实体
			FeedbackEntity feedBack = new FeedbackEntity();

			// 根据流程实例ID查询已存在的问题反馈实体（处理重新提交场景）
			FeedbackEntity oneFeedback = feedbackService.getOne(
				new LambdaQueryWrapper<FeedbackEntity>()
					.eq(FeedbackEntity::getProcessInstanceId, processInstanceId)
			);

			// 过滤重复请求：若已存在实体且审批结果为null（发起阶段的重复提交），直接返回
			if (Objects.nonNull(oneFeedback) && Objects.isNull(variables.get(WfProcessConstant.PASS_KEY))) {
				return;
			}

			// 获取向量数据库中的点ID（用于后续更新或删除）
			Integer pointId = Objects.nonNull(oneFeedback) ? oneFeedback.getPointId() : null;

			// 处理审核人员驳回场景
			if (Objects.nonNull(variables.get(WfProcessConstant.TASK_VARIABLE_PROCESS_TERMINATE))) {
				// 更新问题状态为"人工驳回"
				feedbackService.update(new LambdaUpdateWrapper<FeedbackEntity>()
					.eq(FeedbackEntity::getId, oneFeedback.getId())
					.set(FeedbackEntity::getStatus, FeedbackStatusEnum.MANUAL_REJECTION.getValue())
				);
				return;
			}

			// 若已存在实体，设置ID用于更新操作
			if (Objects.nonNull(oneFeedback)) {
				feedBack.setId(oneFeedback.getId());
			}

			// 将流程变量中的属性复制到问题反馈实体（如表单提交的字段）
			try {
				BeanUtils.copyProperties(feedBack, variables);
			} catch (IllegalAccessException | InvocationTargetException e) {
				throw new RuntimeException(e);  // 复制失败时抛出异常
			}

			// 处理附件信息：从流程变量中获取附件列表并设置到实体
			Object attachment = variables.get("attachments");
			List<Map<String, String>> attachmentList = Optional.ofNullable(attachment)
				.map(obj -> (List<Map<String, String>>) obj)
				.orElse(Collections.EMPTY_LIST);
			feedBack.setFileUrls(attachmentList);

			// 提取问题描述（用于AI分析）
			Object description = variables.get("description");
			String descriptionStr = Optional.ofNullable(description)
				.map(String::valueOf)
				.orElse(StringUtils.EMPTY);

			// 构建HTTP请求头（包含认证信息和内容类型）
			HttpHeaders headers = new HttpHeaders();
			headers.set("Authorization", apiKey);  // 设置API密钥
			headers.setContentType(MediaType.APPLICATION_JSON);  // 设置内容类型为JSON

			// 构建Dify服务的请求参数（包含问题描述和向量点ID）
			DifyRequestPostDTO.Inputs inputs = new DifyRequestPostDTO.Inputs(descriptionStr, pointId);
			DifyRequestPostDTO feedbackPostDTO = new DifyRequestPostDTO(inputs, responseMode, userAccout);

			// 配置FastJSON序列化策略：小驼峰转蛇形命名（适配Dify服务的参数格式）
			SerializeConfig config = new SerializeConfig();
			config.propertyNamingStrategy = PropertyNamingStrategy.SnakeCase;

			// 将请求参数序列化为JSON字符串
			String dataJson = JSON.toJSONString(feedbackPostDTO, config);

			// 发送POST请求到Dify服务，获取AI分析结果
			ResponseEntity<DifyResponseDTO> responseEntity = RestTemplateUtil.post(
				postUrl, headers, DifyResponseDTO.class, dataJson
			);

			// 解析响应结果并校验非空
			DifyResponseDTO difyResponseDTO = responseEntity.getBody();
			Objects.requireNonNull(difyResponseDTO, "智能体响应异常");

			// 提取AI分析结果中的关键信息
			DifyResponseDTO.Outputs outputs = difyResponseDTO.getData().getOutputs();
			String bool = outputs.getBool();  // 是否属于"十不放过"
			String responsiblePerson = outputs.getResponsiblePerson();  // 推荐的负责人
			String suggestion = outputs.getSuggestion();  // AI建议

			// 更新问题反馈实体的AI相关字段
			feedBack.setIsTenNonNeglect(Objects.equals("是", bool));  // 设置是否为"十不放过"
			feedBack.setAiGuidance(suggestion);  // 设置AI建议
			feedBack.setProcessInstanceId(processInstanceId);  // 关联流程实例ID

			// 处理负责人信息：转换为用户ID和姓名
			String responsibilityUserIds = "";
			String[] persons = new String[0];
			if (StringUtils.isNotBlank(responsiblePerson)) {
				// 将负责人字符串转换为数组（如"张三,李四" → ["张三","李四"]）
				persons = convertStringToArray(responsiblePerson);

				// 根据姓名查询用户信息（过滤状态正常的用户）
				List<User> users = Arrays.stream(persons)
					.map(item -> UserCache.getUser(BladeConstant.ADMIN_TENANT_ID, item))  // 从缓存获取用户
					.filter(user -> user != null && user.getStatus().equals(User.STATUS_ON))  // 过滤有效用户
					.collect(Collectors.toList());

				// 提取用户姓名和ID（限制只取第一个负责人）
				persons = users.stream().map(User::getRealName).toArray(String[]::new);
				if (!users.isEmpty()) {
					feedBack.setResponsibilityName(users.get(0).getRealName());  // 负责人姓名
					responsibilityUserIds = users.get(0).getId().toString();  // 负责人ID
				}
			}

			// 设置负责人ID和问题状态
			feedBack.setResponsibility(responsibilityUserIds);
			// 状态规则：若有负责人则设为"等待认领"，否则设为"需人工分配"
			feedBack.setStatus(
				persons.length == 0 
					? FeedbackStatusEnum.MANUAL_ALLOCATION.getValue() 
					: FeedbackStatusEnum.WAITING_FOR_CLAIM.getValue()
			);

			// 保存或更新问题反馈实体
			feedbackService.saveOrUpdate(feedBack);

			// 将关键信息设置为流程变量，供后续节点使用
			delegateTask.setVariable("feedbackId", feedBack.getId());  // 问题ID
			delegateTask.setVariable("aiGuidance", suggestion);  // AI建议
			delegateTask.setVariable("responsibility", responsibilityUserIds);  // 负责人ID
			delegateTask.setVariable("isTenNonNeglect", feedBack.getIsTenNonNeglect());  // 是否为"十不放过"
		}
	}

	/**
	 * 将字符串转换为数组（处理逗号分隔且可能包含空格的情况）
	 * @param input 输入字符串（如"张三, 李四"）
	 * @return 处理后的字符串数组（如["张三","李四"]）
	 */
	public static String[] convertStringToArray(String input) {
		if (input == null || input.trim().equals("[]")) {
			return new String[0];  // 空输入返回空数组
		} else {
			// 去除所有空格后按逗号分割
			return input.replaceAll("\\s", "").split(",");
		}
	}
}
```

### 类功能说明

该类是基于**Flowable 工作流引擎**的**任务监听器**，命名为`ResponsibilityListener`，主要作用是在问题反馈流程的 “确认转出负责人” 环节触发，通过调用 AI 服务（Dify）分析问题描述，自动推荐负责人，并更新问题反馈的状态和相关信息，实现流程的智能化流转。

#### 核心业务场景

当用户发起问题反馈或重新提交时，监听器被触发，将问题描述发送给 Dify 服务进行 AI 分析，获取推荐的负责人、是否属于 “十不放过” 问题及处理建议，然后更新问题反馈实体的状态（如 “等待认领” 或 “需人工分配”），并将关键信息设置为流程变量，供后续节点使用。同时支持处理驳回场景，确保流程状态正确同步。

#### 关键逻辑解析

1. **并发控制**：使用`synchronized (processInstanceId.intern())`确保同一流程实例的操作串行执行，避免并发冲突。
2. **重复请求过滤**：通过判断已有实体和审批结果，过滤发起阶段的重复提交。
3. **AI 服务调用**：构建请求参数并发送到 Dify 服务，获取 AI 分析结果（负责人推荐、问题分类、建议）。
4. **数据同步**：将 AI 结果更新到问题反馈实体，设置负责人 ID、姓名及状态，并同步到流程变量。
5. **驳回处理**：若流程被驳回，更新问题状态为 “人工驳回”。

#### 技术亮点

- **AI 集成**：与 Dify 服务对接，实现问题的智能化分析和负责人推荐，减少人工干预。
- **并发安全**：通过字符串 intern () 方法实现细粒度锁，确保同一流程实例的操作原子性。
- **数据格式适配**：使用 FastJSON 的命名策略转换（小驼峰→蛇形），适配外部服务的参数格式。
- **缓存优化**：通过`UserCache`获取用户信息，减少数据库查询，提升性能。
- **异常处理**：对 Bean 复制和 API 调用异常进行包装，确保流程中断时的错误可见性。

该监听器是问题反馈流程智能化的核心组件，通过 AI 推荐负责人和自动状态更新，提升了流程处理效率和准确性。
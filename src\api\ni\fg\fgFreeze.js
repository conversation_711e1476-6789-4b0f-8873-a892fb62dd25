import request from "@/router/axios";

export const getList = (current, size, params) => {
  return request({
    url: "/api/ni/fg/freeze/list",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/ni/fg/freeze/detail",
    method: "get",
    params: {
      id,
    },
  });
};
export const cancel = (ids) => {
  return request({
    url: "/api/ni/fg/freeze/cancel",
    method: "post",
    params: {
      ids,
    },
  });
};
export const remove = (ids) => {
  return request({
    url: "/api/ni/fg/freeze/remove",
    method: "post",
    params: {
      ids,
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/ni/fg/freeze/save",
    method: "post",
    data: row,
  });
};
export const addBatch = (data) => {
  return request({
    url: "/api/ni/fg/freeze/saveBatch",
    method: "post",
    data
  });
};
export const update = (row) => {
  return request({
    url: "/api/ni/fg/freeze/update",
    method: "post",
    data: row,
  });
};

export const freeze = (params) => {
  return request({
    url: "/api/ni/fg/freeze/freeze",
    method: "post",
    params,
  });
};

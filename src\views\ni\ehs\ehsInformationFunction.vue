<template>
  <basic-container>
    <avue-crud  :defaults.sync="defaults"
                :option="option"
               :table-loading="loading"
               :data="data"
               :page.sync="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @refresh-change="refreshChange"
               @on-load="onLoad">



      <template slot="menuLeft">


        <!-- 使用 div 包裹并设置 flex 布局 -->
        <div style="display: flex; align-items: center;">
          <el-button type="primary" size="mini" icon="el-icon-plus" v-if="permissionList.addBtn" @click="handleAdd">新增</el-button>
          <el-button type="danger" size="mini" icon="el-icon-delete" plain v-if="permission.ehsInformationFunction_delete" @click="handleDelete">删 除</el-button>
          <el-button
            type="success"
            size="mini"
            icon="el-icon-edit"
            plain
            @click="handleBatchRegister"
          >
            批量登记
          </el-button>
          <el-button
            type="info"
            size="mini"
            icon="el-icon-refresh"
            @click="handleShowAll" style="background-color: #67C23A; color: white; border: none;"
          >
            显示全部
          </el-button>
          <el-radio-group v-model="brand" size="mini" @input="onLoad(page)"  style="margin-right: 20px;">
            <el-radio-button label="1">能特异</el-radio-button>
            <el-radio-button label="2">演绎</el-radio-button>
          </el-radio-group>
          <el-radio-group v-model="equipmentCategory" size="mini" @change="onDeviceTypeChange" style="margin-right: 20px;">
            <el-radio-button :label="1">特种设备</el-radio-button>
            <el-radio-button :label="2">承压设备</el-radio-button>
            <el-radio-button :label="3">机电设备</el-radio-button>
            <el-radio-button :label="4">安全装置</el-radio-button>
          </el-radio-group>
          <!-- 右侧按钮自动撑开距离 -->
          <el-button
            size="mini"
            icon="el-icon-user"
            @click="handleUserSelect({ type: 'notify', checkType: 'checkbox' })"
            style="margin-right: 20px;"
          >
            选择通知人
          </el-button>
          <el-button type="primary" size="mini" icon="el-icon-upload2" plain
                     @click="handleImport">导入
          </el-button>

          <el-button type="primary" size="mini" icon="el-icon-download" @click="handleExcel">导出</el-button>

        </div>
      </template>
      <template #status="{ row }">
        <el-tag v-if="row.status === 1" size="mini" type="plain">
          {{ row.$status }}
        </el-tag>
        <el-tag v-else-if="row.status === 2" size="mini" effect="warning">
          {{ row.$status }}
        </el-tag>
        <el-tag v-else-if="row.status === 3" size="mini" effect="danger">
          {{ row.$status }}
        </el-tag>
        <el-tag v-else-if="row.status === 4" size="mini" type="info">
          {{ row.$status }}
        </el-tag>
      </template>
      <template slot-scope="{ row, type, size }" slot="menu">
        <el-button
          type="text"
          icon="el-icon-view"
          :size="size"
          @click="handleView(row)"
        >查看</el-button>

        <el-button
          type="text"
          icon="el-icon-edit"
          size="mini"
          @click.stop="handleEdit(row)"
        >编辑</el-button>

        <el-button type="text" icon="el-icon-delete" size="mini"
                   @click.stop="$refs.crud.rowDel(row, index)">删除
        </el-button>
        <!-- 原有其他按钮可以保留 -->
        <el-button
          icon="el-icon-edit"
          :size="size"
          type="text"
          autofocus
          @click.stop="handleRegister(row)"
        >
          登记
        </el-button>


        <el-button
          icon="el-icon-document"
          :size="size"
          type="text"
          @click.stop="$refs.attachDialogRef.init(row.id)"
        >
          附件日志
        </el-button>
      </template>

      <template #menuRight="{size}">
        <el-button icon="el-icon-time" circle :size="size" @click="handleLog"></el-button>
      </template>


    </avue-crud>
    <log-opt-dialog ref="logOptDialogRef" :module="module" />
    <attach-dialog ref="attachDialogRef" code="xxx" :ids="batchRegisterAttachIds" />

    <!-- 人员选择弹窗 -->
    <user-select ref="user-select"
                 :check-type="checkType"
                 :default-checked="defaultChecked"
                 @onConfirm="handleUserSelectConfirm"></user-select>

    <el-dialog
      title="登记"
      :visible.sync="registerDialogVisible"
      width="30%"
      append-to-body
      :z-index="99999"
    >
      <el-form label-position="right" label-width="120px">
        <el-form-item label="选择状态">
          <el-select v-model="registerStatus" placeholder="请选择状态" style="width: 100%">
            <el-option label="正常" :value="1"></el-option>
            <el-option label="临近检验" :value="2"></el-option>
            <el-option label="逾期未检验" :value="3"></el-option>
            <el-option label="报废" :value="4"></el-option>
          </el-select>
        </el-form-item>

        <!-- 新增：检验日期 -->
        <el-form-item label="检验日期">
          <el-date-picker
            v-model="registerInspectionDate"
            type="date"
            placeholder="选择检验日期"
            value-format="yyyy-MM-dd"
            style="width: 100%"
          />
        </el-form-item>

        <!-- 新增：下次检验日期 -->
        <el-form-item label="下次检验日期">
          <el-date-picker
            v-model="registerNextVerificationDate"
            type="date"
            placeholder="选择下次检验日期"
            value-format="yyyy-MM-dd"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>

      <span slot="footer" class="dialog-footer">

    <el-button icon="el-icon-document" @click.stop="$refs.attachDialogRef.init(selectedRow.id)">
    上传附件
    </el-button>
     <el-button type="primary" @click="confirmRegister">确 定</el-button>
    <el-button @click="registerDialogVisible = false">取 消</el-button>
  </span>
    </el-dialog>


    <!-- 批量登记弹窗 -->
    <el-dialog
      title="批量状态登记"
      :visible.sync="batchRegisterDialogVisible"
      width="30%"
      append-to-body
      :z-index="99999"
    >
      <el-form label-position="right" label-width="100px">
        <el-form-item label="选择状态">
          <el-select v-model="batchRegisterStatus" placeholder="请选择状态" style="width: 100%">
            <el-option label="正常" :value="1"></el-option>
            <el-option label="临近检验" :value="2"></el-option>
            <el-option label="逾期未检验" :value="3"></el-option>
            <el-option label="报废" :value="4"></el-option>
          </el-select>

        </el-form-item>
        <el-form-item label="检验日期">
          <el-date-picker
            v-model="batchRegisterInspectionDate"
            type="date"
            placeholder="选择检验日期"
            value-format="yyyy-MM-dd"      style="width: 100%"
          />
        </el-form-item>

        <!-- 新增：下次检验日期 -->
        <el-form-item label="下次检验日期">
          <el-date-picker
            v-model="batchRegisterNextVerificationDate"
            type="date"
            placeholder="选择下次检验日期"
            value-format="yyyy-MM-dd"      style="width: 100%"
          />
        </el-form-item>
      </el-form>

      <span slot="footer" class="dialog-footer">
    <el-button icon="el-icon-document" @click.stop="$refs.attachDialogRef.init(selectedRow.id)">
      上传附件
    </el-button>
    <el-button type="primary" @click="confirmBatchRegister">确 定</el-button>
    <el-button @click="batchRegisterDialogVisible = false">取 消</el-button>
  </span>
    </el-dialog>


    <el-dialog
      title="数据导入"
      append-to-body
      :visible.sync="excelBox"
      width="555px"
    >
      <avue-form
        :option="excelOption"
        v-model="excelForm"
        :upload-after="uploadAfter"
      >
        <template slot="excelTemplate">
          <el-button size="mini" type="primary" @click="handleTemplate">
            点击下载<i class="el-icon-download el-icon--right"></i>
          </el-button>
        </template>
      </avue-form>
    </el-dialog>


  </basic-container>
  <!-- 人员选择弹窗 -->

</template>

<script>
import {getList, getDetail, add, update, remove} from "@/api/ni/ehs/ehsInformationFunction";
  import {mapGetters} from "vuex";
  import LogOptDialog from "@/components/log-opt-dialog";
  import AttachDialog from "@/components/attach-dialog";

  import { batchUpdateNotifyUser } from "@/api/ni/ehs/ehsInformationFunction";

  import {exportBlob} from "@/api/common";
  import {downloadXls} from "@/util/util";
  import { getToken } from "@/util/auth";

  import userSelect from "@/views/plugin/workflow/process/components/user-select.vue";


  export default {
    components: {userSelect,LogOptDialog,AttachDialog},
    data() {
      return {
        selectedRow: null,
        registerInspectionDate: null,
        registerNextVerificationDate: null,
        batchRegisterInspectionDate: null,
        batchRegisterNextVerificationDate: null,
        batchRegisterAttachIds: [],

        isShowAll: false,       // 是否是“显示全部”状态
        showAllPage: 1,         // “显示全部”时的页码

        userSelectType: "",
        checkType: "checkbox", // 人员选择check类型 radio单选 checkbox多选
        defaultChecked: "",

        notifyUserIds: [],       // 通知人ID数组
        notifyUserNames: [],     // 通知人名称数组


        module: 'ni_ehs_information_function',
        brand: "1",
        equipmentCategory:1, // 新增：设备种类筛选值
        type:false,
        defaults:{},
        data:[],

        form: {

        },

        query: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        // 新增：登记弹窗相关数据
        registerDialogVisible: false,
        registerStatus: null,
        batchRegisterDialogVisible: false,
        batchRegisterStatus: null,

        selectionList: [],
        option: {

          addBtn: false,
          height:'auto',
          calcHeight: 30,
          tip: false,
          searchShow: true,
          searchMenuSpan: 6,

          searchIndex:3,
          searchIcon:true,

          border: true,
          index: true,
          viewBtn: false,
          editBtn: false,
          delBtn:false,
          selection: true,
          dialogClickModal: false,
          column: [
            // 基础字段
            { label: "ID", prop: "id",hide: true,display: false, addDisplay:  false,editDisplay: false, type: "input", disabled: true ,search: false},
            { label: "状态",
              prop: "status",
              hide: false,
              display: true,
              type: "select",
              dicUrl: "/api/blade-system/dict-biz/dictionary?code=device_use_status" ,
              search: true,
              props: {
                label: "dictValue",
                value: "dictKey"
              },
              dataType: "number",   //这个不加就不行
              disabled: (context) => {
                // 编辑或查看时禁用
                return ['edit', 'view'].includes(context.type);
              }
            },
            { label: "设备名称", prop: "deviceName", hide: false, type: "input" ,search: true,
              rules: [{
                required: true,
                message: "设备名称不能为空",
                trigger: "blur"
              }]
            },
            { label: "规格型号", prop: "specification", hide: false, type: "input" ,search: true},
            { label: "出厂编号", prop: "startNumber", hide: false, type: "input" ,search: true},
            { label: "使用登记证编号", prop: "useRegistrationNumber", hide: false, type: "input" },
            { label: "制造单位", prop: "manufactureCompany", hide: false, type: "input" ,search: true},
            { label: "数量", prop: "number", hide: false, type: "number" },
            { label: "单位", prop: "unit", hide: false, type: "input" },
            { label: "出厂日期", prop: "appearanceDate", hide: false, type: "date", format: "yyyy-MM-dd", valueFormat: "yyyy-MM-dd" },
            { label: "检验日期", prop: "inspectionDate", hide: false, type: "date", format: "yyyy-MM-dd", valueFormat: "yyyy-MM-dd" },
            { label: "下次检验日期", prop: "nextVerificationDate", hide: false, type: "date", format: "yyyy-MM-dd", valueFormat: "yyyy-MM-dd" },
            { label: "报废日期", prop: "scrapDate", hide:false,type: "date", format: "yyyy-MM-dd", valueFormat: "yyyy-MM-dd" },
            { label: "存放位置", prop: "position", hide: false, type: "input" },
            { label: "备注", prop: "note", hide: false, type: "textarea" },

            // 承压设备字段
            { label: "位号(内部编号)", prop: "positionNumber",display: false, hide: true, type: "input" },
            { label: "使用证号", prop: "certificateNumber",display: false, hide: true, type: "input" },
            { label: "注册代码", prop: "registrationCode", display: false,hide: true, type: "input" },
            { label: "设计(工作)压力", prop: "designPressure", display: false,hide: true, type: "input" },
            { label: "设计(工作)温度", prop: "designTemperature",display: false, hide: true, type: "input" },
            { label: "介质", prop: "medium", hide: true,display: false, type: "input" },
            { label: "主体材质", prop: "material", hide: true,display: false, type: "input" },
            { label: "投用时间", prop: "usageTime", hide: true,display: false, type: "date", format: "yyyy-MM-dd", valueFormat: "yyyy-MM-dd" },
            { label: "检验报告编号", prop: "inspectionReportNumber", display: false,hide: true, type: "input" },
            { label: "安全状态等级", prop: "safetyStatusLevel",display: false, hide: true, type: "input" },


            // 机电设备字段
            { label: "特征参数", prop: "characteristicParameter", display: false,hide: true, type: "input" },
            { label: "检验单位", prop: "verificationUnit", display: false,hide: true, type: "input" },
            { label: "检验报告", prop: "inspectionReport", display: false,hide: true, type: "input" },
            { label: "检验结论", prop: "inspectionConclusion", display: false,hide: true, type: "input" },

            // 安全装置字段
            { label: "公称压力", prop: "nominalPressure",display: false, hide: true, type: "input" },
            { label: "安装位置", prop: "installationPosition", display: false,hide: true, type: "input" },
            { label: "安装位置工作压力", prop: "installationPositionPressure",display: false, hide: true, type: "input" },
            { label: "整定(爆破)压力", prop: "setPressure",display: false,hide: true, type: "input" },
            // { label: "校验单位", prop: "verificationUnit",display: false, hide: true, type: "input" },
            // { label: "校验日期", prop: "inspectionDate", display: false,hide: true, type: "date", format: "yyyy-MM-dd", valueFormat: "yyyy-MM-dd" },
            // { label: "校验报告编号", prop: "inspectionReportNumber", hide: true, display: false,type: "input" },

            // { label: "使用状态", prop: "userStatus", hide: true, type: "select", dicUrl: "/api/blade-system/dict-biz/dictionary?code=device_use_status" },

            // 公司
            { label: "公司", prop: "brand", hide: true,display: false,
              type: "select",
              dicUrl: "/api/blade-system/dict-biz/dictionary?code=natergy_company_abbreviation" ,
              props: {
                label: "dictValue",
                value: "dictKey"
              },
              dataType: "number",
            },

            { label: "设备种类",
              prop: "equipmentCategory",
              hide: true,
              display: true,
              type: "select",
              dicUrl: "/api/blade-system/dict-biz/dictionary?code=equipment_category" ,
              props: {
                label: "dictValue",
                value: "dictKey"
              },
              dataType: "number",   //这个不加就不行
            },


            {
              label: "通知人",
              prop: "notifyUserName",
              hide: true,
              // display: false,
              addDisplay: false,
              editDisplay: false,
              viewDisplay: true,
              type: "input",
              disabled: true,
              click: () => {
                this.handleUserSelect({ type: "notify", checkType: "checkbox" });
              }
            }

            // {label: "租户ID", prop: "tenant_id", type: "input"}
          ]

        },


        excelBox: false,
        excelOption: {
          size: "mini",
          searchSize: "mini",
          submitBtn: false,
          emptyBtn: false,
          column: [
            {
              label: "数据上传",
              prop: "excelFile",
              type: "upload",
              drag: true,
              loadText: "模板上传中，请稍等",
              span: 24,
              data: {},
              propsHttp: {
                res: "data",
              },
              tip: "请上传 .xls,.xlsx 标准格式文件",
              action: "/api/ni/ehs/ehsInformationFunction/import",
            },
            {
              label: "模板下载",
              prop: "excelTemplate",
              formslot: true,
              span: 24,
            },
          ],
        },
        excelForm: {},
        pen: {
          visible: false,
          form: {},
          option: {
            size: "mini",
            span: 24,
            emptyBtn: false,
            column: [
              {
                label: '选择日期',
                prop: 'date',
                type: "daterange",
                format: 'yyyy-MM-dd',
                valueFormat: 'yyyy-MM-dd',
                placeholder: '请选择日期',
                rules: [
                  {
                    required: true,
                    message: '请选择日期',
                    trigger: 'blur'
                  }
                ]
              }
            ]
          }
        },

      };
    },
    computed: {
      ...mapGetters(["permission"]),

      permissionList() {
        const canAdd = this.vaildData(this.permission.ehsInformationFunction_add, false);
        const hasBrand = !!this.brand;
        const hasEquipmentCategory = !!this.equipmentCategory;

        return {
          addBtn: this.isShowAll ? false : (canAdd && hasBrand && hasEquipmentCategory),
          viewBtn: this.vaildData(this.permission.ehsInformationFunction_view, false),
          delBtn: this.vaildData(this.permission.ehsInformationFunction_delete, false),
          editBtn: this.vaildData(this.permission.ehsInformationFunction_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },




    methods: {

      handleView(row) {
        this.$refs.crud.rowView(row);
      },
      handleEdit(row) {
        this.$refs.crud.rowEdit(row);
      },



      handleImport() {
        this.excelBox = true
      },


      async handleExcel() {
        console.log("导出按钮被点击");

        let opt = {
          column: [
            { label: "状态", prop: "statusLabel" }, // 后端返回的状态标签（可选）
            { label: "设备名称", prop: "deviceName" },
            { label: "规格型号", prop: "specification" },
            { label: "出厂编号", prop: "startNumber" },
            { label: "使用登记证编号", prop: "useRegistrationNumber" },
            { label: "制造单位", prop: "manufactureCompany" },
            { label: "数量", prop: "number" },
            { label: "单位", prop: "unit" },
            { label: "出厂日期", prop: "appearanceDate" },
            { label: "检验日期", prop: "inspectionDate" },
            { label: "下次检验日期", prop: "nextVerificationDate" },
            { label: "报废日期", prop: "scrapDate" },
            { label: "存放位置", prop: "position" },
            { label: "备注", prop: "note" },
            // 承压设备字段
            { label: "位号(内部编号)", prop: "positionNumber"},
            { label: "使用证号", prop: "certificateNumber"},
            { label: "注册代码", prop: "registrationCode"},
            { label: "设计(工作)压力", prop: "designPressure"},
            { label: "设计(工作)温度", prop: "designTemperature"},
            { label: "介质", prop: "medium"},
            { label: "主体材质", prop: "material"},
            { label: "投用时间", prop: "usageTime"},
            { label: "检验报告编号", prop: "inspectionReportNumber"},
            { label: "安全状态等级", prop: "safetyStatusLevel"},


            // 机电设备字段
            { label: "特征参数", prop: "characteristicParameter" },
            { label: "检验单位", prop: "verificationUnit"},
            { label: "检验报告", prop: "inspectionReport"},
            { label: "检验结论", prop: "inspectionConclusion" },

            // 安全装置字段
            { label: "公称压力", prop: "nominalPressure"},
            { label: "安装位置", prop: "installationPosition"},
            { label: "安装位置工作压力", prop: "installationPositionPressure"},
            { label: "整定(爆破)压力", prop: "setPressure"},

            { label: "公司", prop: "brand",
              dicUrl: "/api/blade-system/dict-biz/dictionary?code=natergy_company_abbreviation" ,
              props: {
                label: "dictValue",
                value: "dictKey"
              },
              dataType: "number",
            },

            { label: "设备种类",
              prop: "equipmentCategory",
              dicUrl: "/api/blade-system/dict-biz/dictionary?code=equipment_category" ,
              props: {
                label: "dictValue",
                value: "dictKey"
              },
              dataType: "number",   //这个不加就不行
            },
            {
              label: "通知人",
              prop: "notifyUserName",
            }
          ]
        };

        this.exportData = [];

        await this.getExportData(); // 获取或处理导出的数据

        this.$Export.excel({
          title: "设备信息",
          columns: opt.column,
          data: this.exportData.map(item => {
            const row = {};

            // 遍历所有列进行赋值
            opt.column.forEach(col => {
              const prop = col.prop;

              if (prop === 'status') {
                // 状态字段：1 → 正常 / 2 → 临近检验 ...
                row[prop] = ['正常', '临近检验', '逾期未检验', '报废'][item[prop] - 1] || item[prop];
              } else if (prop === 'brand' || prop === 'equipmentCategory') {
                // 字典字段翻译（假设后端返回了 *Label 字段）
                row[prop] = item[prop + 'Label'] || item[prop];
              } else if (prop === 'notifyUserName') {
                // 通知人字段（如果需要特殊处理）
                row[prop] = item[prop] || '';
              } else {
                // 普通字段直接赋值
                row[prop] = item[prop] !== undefined ? item[prop] : '';
              }
            });

            return row;
          })
        });
      },

      async getExportData() {
        const promises = [];
        this.exportData = [];
        for (var i = 1; i <= this.page.total / this.page.pageSize + 1; i++) {
          const promise = getList(i, this.page.pageSize, {
            ...this.params,
            ...this.query,
          }).then((res) => {
            const data = res.data.data.records;
            this.exportData = this.exportData.concat(data);
          });

          promises.push(promise);
        }

        // 等待所有异步请求完成
        await Promise.all(promises);
        return this.exportData;
      },


      handleTemplate() {
        exportBlob(
          `/api/ni/ehs/ehsInformationFunction/export-template?${
            this.website.tokenHeader
          }=${getToken()}`
        ).then((res) => {
          downloadXls(res.data, "特种设备信息导入模板.xlsx");
        });
      },


      handleBatchRegister() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请先选择数据");
          return;
        }

        this.batchRegisterStatus = this.selectionList[0].status;
        this.batchRegisterAttachIds = this.selectionList.map(row => row.id); // 记录所有选中ID
        this.batchRegisterDialogVisible = true;
      },

      // 批量登记确认
      confirmBatchRegister() {
        const { batchRegisterStatus, batchRegisterInspectionDate, batchRegisterNextVerificationDate, selectionList } = this;

        if (!batchRegisterStatus) {
          this.$message.warning("请选择要登记的状态");
          return;
        }

        if (!batchRegisterInspectionDate || !batchRegisterNextVerificationDate) {
          this.$message.warning("请填写检验日期和下次检验日期");
          return;
        }

        const now = new Date();
        const dateStr = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')}`;

        const updatedList = selectionList.map(row => {
          const newRow = {
            ...row,
            status: batchRegisterStatus,
            inspectionDate: batchRegisterInspectionDate,
            nextVerificationDate: batchRegisterNextVerificationDate
          };

          if (batchRegisterStatus === 4) {
            newRow.scrapDate = dateStr;
          }

          return newRow;
        });

        this.$confirm(`确认将 ${updatedList.length} 条数据状态登记为【${
          [null, "正常", "临近检验", "逾期未检验", "报废"][batchRegisterStatus]
        }】？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          const promises = updatedList.map(row =>
            update(row).catch(err => {
              this.$message.error(`ID:${row.id} 更新失败：${err.message}`);
            })
          );

          Promise.all(promises)
            .then(() => {
              this.$message.success("批量登记成功");
              this.onLoad(this.page);
              this.batchRegisterDialogVisible = false;
            })
            .catch(() => {
              this.$message.warning("部分数据更新失败，请查看控制台");
            });
        }).catch(() => {
          this.$message.info("已取消登记");
        });
      },



      handleRegisterConfirm(status) {
        const updatedRow = { ...this.selectedRow, status };
        update(updatedRow).then(() => {
          this.onLoad(this.page);
          this.$message.success("状态登记成功");
        }).catch(err => {
          this.$message.error("状态更新失败：" + err.message);
        });
      },
      handleRegisterCancel() {
        this.$message.info("已取消登记");
      },


      handleShowAll() {
        // 重置筛选条件
        this.isShowAll = true;
        this.brand = '';
        this.equipmentCategory = '';

        // 显示所有字段：将所有 column 的 hide 设置为 false
        this.option.column.forEach(col => {
          col.hide = false;
          col.display = true;
        });

        // 强制隐藏 ID 和 notifyUserName
        const idColumn = this.option.column.find(col => col.prop === 'id');
        if (idColumn) {
          idColumn.hide = true;
          idColumn.display = false;
        }

        const notifyColumn = this.option.column.find(col => col.prop === 'notifyUserName');
        if (notifyColumn) {
          notifyColumn.hide = true;
          notifyColumn.display = false;
        }

        // 固定显示顺序：把 "brand" 和 "equipmentCategory" 放到最前面
        const fixedFields = ['brand', 'equipmentCategory'];
        const restFields = this.option.column
          .filter(col => !fixedFields.includes(col.prop))
          .map(col => col.prop);

        const allFields = [...fixedFields, ...restFields];

        // 更新列顺序并刷新表格
        const sortedColumns = allFields.map(prop =>
          this.option.column.find(col => col.prop === prop)
        ).filter(Boolean);

        this.option.column = sortedColumns;


        // 可选：刷新表格以确保生效
        this.$refs.crud.refreshTable();

        // 刷新表格数据
        this.onLoad(this.page);
      },




      handleUserSelect({ type, checkType }) {
        // if (this.selectionList.length === 0) {
        //   this.$message.warning("请先选择数据");
        //   return;
        // }
        this.userSelectType = type;
        this.checkType = checkType;
        this.$refs["user-select"].visible = true;
      },

      handleUserSelectConfirm(ids, names) {
        const userIds = typeof ids === 'string' ? ids.split(',') : (Array.isArray(ids) ? ids : []);
        const userNames = typeof names === 'string' ? names.split(',') : (Array.isArray(names) ? names : []);

        if (this.userSelectType === "notify") {
          // 获取所有记录的 id（如果 this.data 中包含全部数据）
          const allIds = this.data.map(row => row.id);

          // 更新 data 中所有行的 notifyUser 字段
          this.data = this.data.map(row => {
            row.notifyUser = userIds.join(",");
            row.notifyUserName = userNames.join("、");
            return row;
          });

          // 同步更新 form（如果当前编辑行存在）
          if (this.form.id) {
            this.form.notifyUser = userIds.join(",");
            this.form.notifyUserName = userNames.join("、");
          }

          // 存储全局通知人信息用于显示
          this.notifyUserIds = userIds;
          this.notifyUserNames = userNames;

          // 调用批量更新接口（推荐方式）

          batchUpdateNotifyUser({
            ids: allIds,
            notifyUser: userIds.join(","),
            notifyUserName: userNames.join("、")
          }).then(() => {
            this.$message.success("已成功更新数据库中的所有记录");
            this.onLoad(this.page); // 可选：刷新表格
          }).catch(err => {
            this.$message.error("数据库更新失败：" + err.message);
          });

          this.$refs["user-select"].visible = false;
        }
      },




      handleRegister(row) {
        this.selectedRow = row;
        this.registerStatus = row.status;

        // 如果 inspectionDate 不存在，则设为当前日期
        this.registerInspectionDate = row.inspectionDate || this.getCurrentDate();

        // 如果 nextVerificationDate 不存在，则设为当前日期
        this.registerNextVerificationDate = row.nextVerificationDate || this.getCurrentDate();

        this.registerDialogVisible = true;
      },

      getCurrentDate() {
        const now = new Date();
        return `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')}`;
      },



      confirmRegister() {
        if (!this.registerStatus) {
          this.$message.warning("请选择状态");
          return;
        }

        const updatedRow = {
          ...this.selectedRow,
          status: this.registerStatus,
          inspectionDate: this.registerInspectionDate,
          nextVerificationDate: this.registerNextVerificationDate
        };

        // 如果状态为 4（报废），则更新报废日期
        if (this.registerStatus === 4) {
          const now = new Date();
          const dateStr = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')}`;
          updatedRow.scrapDate = dateStr;
        }

        update(updatedRow)
          .then(() => {
            this.$message.success("状态已更新");
            this.onLoad(this.page); // 刷新表格
            this.registerDialogVisible = false; // 关闭弹窗
          })
          .catch(err => {
            this.$message.error("状态更新失败：" + err.message);
          });
      },



      handleLog() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择要查看的数据");
          return;
        } else if (this.selectionList.length > 1) {
          this.$message.warning("只能选择一条数据");
          return;
        }
        this.$refs.logOptDialogRef.init(this.selectionList[0].id);
      },

      handleAdd() {
        this.form = {
          brand: this.brand, // 当前选择的品牌
          equipmentCategory: this.equipmentCategory, // 当前选择的设备种类
          status: 1, // 设置默认状态为 1
          inspectionDate: this.getCurrentDate(),         // 默认检验日期
          nextVerificationDate: this.getCurrentDate()    // 默认下次检验日期
        };
        this.$refs.crud.rowAdd();
        this.change(this.equipmentCategory);
      },

      onDeviceTypeChange(val) {
        this.isShowAll = false;
        this.change(val); // val 是 equipmentCategory 的值
        this.onLoad(this.page);
      },

      showFields(fields) {
        const visibleColumns = [];

        // 清除所有 hide 属性，并记录顺序
        this.option.column.forEach(col => {
          const isVisible = fields.includes(col.prop);
          col.hide = !isVisible;
          col.display  = isVisible;


          if (col.prop === 'notifyUserName') {
            col.hide  = true;
            col.addisplay = false;
            col.editDisplay = false; // 强制保持 editDisplay 为 false
            col.viewDisplay = true;
          }

          if (isVisible) {
            visibleColumns.push(col);
          }
        });

        // 按照 fields 的顺序重新排序列
        visibleColumns.sort((a, b) => {
          return fields.indexOf(a.prop) - fields.indexOf(b.prop);
        });

        // 更新 avue-crud 的 column 显示顺序
        this.option.column = [...visibleColumns, ...this.option.column.filter(col => !fields.includes(col.prop))];
        this.$refs.crud.refreshTable();
      },


      change(type, updateFilter = true) {
        this.equipmentCategory = updateFilter ? type : this.equipmentCategory; // 控制是否更新筛选值

        if (type === 1) {
          this.showFields([
            'status', 'deviceName', 'specification',
            'startNumber', 'useRegistrationNumber', 'manufactureCompany',
            'number', 'unit', 'appearanceDate',
            'inspectionDate','nextVerificationDate', 'scrapDate','position', 'note','notifyUserName'
          ]);
        } else if (type === 2) {
          this.showFields([
            'status', 'deviceName', 'specification',
            'positionNumber', 'certificateNumber', 'registrationCode',
            'designPressure', 'designTemperature', 'medium', 'material',
            'manufactureCompany', 'startNumber', 'usageTime',
            'inspectionReportNumber', 'safetyStatusLevel',
            'inspectionDate','nextVerificationDate', 'scrapDate','note', 'appearanceDate', 'notifyUserName'
          ]);
        } else if (type === 3) {
          this.showFields([
            'status', 'deviceName',
            'positionNumber', 'certificateNumber', 'registrationCode',
            'characteristicParameter', 'manufactureCompany', 'startNumber',
            'usageTime', 'verificationUnit', 'inspectionReport',
            'inspectionConclusion', 'inspectionDate', 'nextVerificationDate', 'scrapDate','note','notifyUserName'
          ]);
        } else if (type === 4) {
          this.showFields([
            'status', 'deviceName',
            'manufactureCompany', 'startNumber', 'specification',
            'nominalPressure', 'installationPosition', 'installationPositionPressure',
            'setPressure', 'verificationUnit',
            'inspectionReportNumber', 'inspectionDate','nextVerificationDate', 'scrapDate','userStatus','notifyUserName'
          ]);
        }
      },

      onLoad(page, params = {}) {
        this.loading = true;

        let brandParam = {};
        if (this.brand && !this.isShowAll) {
          brandParam.brand = this.brand; // 只有选中且非“显示全部”才传参
        }

        let equipmentCategoryParam = {};
        if (this.equipmentCategory && !this.isShowAll) {
          equipmentCategoryParam.equipmentCategory = this.equipmentCategory;
        }

        const queryParams = Object.assign({}, params, this.query, brandParam, equipmentCategoryParam);

        getList(page.currentPage, page.pageSize, queryParams).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      },




      rowSave(row, done, loading) {
        add(row).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          loading();
          window.console.log(error);
        });
      },
      rowUpdate(row, index, done, loading) {
        update(row).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          loading();
          console.log(error);
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      beforeOpen(done, type) {
        if (type === 'edit' || type === 'view') {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
            // 设置 status 字段为只读
            const statusColumn = this.option.column.find(col => col.prop === 'status');
            if (statusColumn) {
              statusColumn.disabled = true;
              statusColumn.readonly = true;
            }
            // 查看/编辑时不更改全局筛选条件
            this.change(res.data.data.equipmentCategory,false);// 第二个参数为 false 表示不更新 equipmentCategory
            // this.change(this.form.equipmentCategory); // 保持原逻辑不变
          });
        } else if (type === 'add') {
          // 新增模式下初始化 form 表单数据
          this.form = {
            brand: this.brand,
            equipmentCategory: this.equipmentCategory,
            status: 1,
            inspectionDate: this.getCurrentDate(),
            nextVerificationDate: this.getCurrentDate()
          };
        }
        done();
      },


      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1;
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      refreshChange() {
        this.onLoad(this.page, this.query);
      },
    }
  };
</script>

<style>
</style>

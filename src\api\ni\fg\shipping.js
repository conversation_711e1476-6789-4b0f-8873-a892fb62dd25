import request from "@/router/axios";

export const getList = (current, size, params) => {
  return request({
    url: "/api/ni/fg/shipping/list",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/ni/fg/shipping/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const remove = (ids) => {
  return request({
    url: "/api/ni/fg/shipping/remove",
    method: "post",
    params: {
      ids,
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/ni/fg/shipping/save",
    method: "post",
    data: row,
  });
};

export const update = (row) => {
  return request({
    url: "/api/ni/fg/shipping/submit",
    method: "post",
    data: row,
  });
};
export const audit = (id) => {
  return request({
    url: "/api/ni/fg/shipping/audit",
    method: "post",
    params: {
      id,
    },
  });
};

export const loadItemMaterial = (id) => {
  return request({
    url: "/api/ni/fg/shipping/loadItemMaterial",
    method: "get",
    params: {
      id,
    },
  });
};
export const count = (params) => {
  return request({
    url: "/api/ni/fg/shipping/count",
    method: "get",
    params,
  });
};

export const reloadShippingNum = (ids) => {
  return request({
    url: "/api/ni/fg/shipping/reloadShippingNum",
    method: "get",
    params: {
      ids,
    },
  });
};

export const syncHuowuliuzhuanjilu = (ids) => {
  return request({
    url: "/api/ni/fg/shipping/syncHuowuliuzhuanjilu",
    method: "post",
    params: {
      ids,
    },
  });
};

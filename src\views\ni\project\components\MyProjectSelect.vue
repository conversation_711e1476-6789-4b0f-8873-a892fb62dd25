<template>
  <div>
    <el-input
      v-model="pTitle"
      :size="size"
      :disabled="disabled"
      suffix-icon="el-icon-search"
      @focus="bpFocus"
      @clear="handleClear"
      clearable
    />
    <el-dialog
      ref="p-dialog"
      v-dialogdrag
      custom-class="p-dialog"
      :visible.sync="visible"
      title="项目选择"
      width="60%"
      :before-close="handleClose"
      append-to-body
    >
      <avue-crud
        v-if="isInit && visible"
        :option="option"
        :table-loading="loading"
        :data="data"
        v-model="form"
        :search="query"
        ref="crud"
        @search-change="searchChange"
        @search-reset="searchReset"
        @selection-change="selectionList = $event"
        @row-click="rowClick"
        @on-load="onLoad"
      >
        <template v-if="!multiple" #radio="{ row }">
          <el-radio v-model="form.radio" :label="row.id"><i></i></el-radio>
        </template>
        <template #status="{ row, index }">
          <el-tag v-if="row.status" size="mini" :type="rowTagType(row.status)">
            {{
              statusDictKeyValue[row.status + ""]
                ? statusDictKeyValue[row.status + ""]
                : row.status
            }}
          </el-tag>
        </template>
      </avue-crud>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose" size="mini">取 消</el-button>
        <el-button type="primary" @click="handleConfirm" size="mini"
          >确 定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { getDetail, getMyRunning as getPage } from "@/api/ni/project";
import Emitter from "element-ui/src/mixins/emitter";

/**
 * 只查询已审核的项目
 */
export default {
  mixins: [Emitter],
  props: {
    value: {
      type: String,
    },
    size: {
      type: String,
      default: "mini",
    },
    disabled: Boolean,
    customOption: Object,
    multiple: {
      type: Boolean,
      default: false,
    },
    params: Object,
  },
  watch: {
    value: {
      handler(val) {
        if (val) {
          const name = [];
          const checks = (val + "").split(",");
          const asyncList = [];
          checks.forEach((c) => {
            asyncList.push(getDetail(c));
          });
          Promise.all(asyncList).then((res) => {
            res.forEach((r) => {
              const data = r.data.data;
              if (data) name.push(data.serialNo);
            });
            this.$set(this, "pTitle", name.join(","));
          });
        } else this.$set(this, "pTitle", "");
      },
      immediate: true,
    },
    multiple: {
      handler(val) {
        if (!val) {
          this.$set(this.option, "selection", false);
          this.findObject(this.option.column, "radio").hide = false;
        } else {
          this.$set(this.option, "selection", true);
          this.findObject(this.option.column, "radio").hide = true;
        }
      },
      immediate: true,
    },
  },
  computed: {
    ids() {
      let ids = new Set();
      this.selectionList.forEach((ele) => {
        ids.add(ele.id);
      });
      return Array.from(ids).join(",");
    },
    titles() {
      let titles = new Set();
      this.selectionList.forEach((ele) => {
        titles.add(ele.serialNo);
      });
      return Array.from(titles).join(",");
    },
  },
  data() {
    return {
      statusDict: [],
      statusDictKeyValue: {},
      pTitle: "",
      visible: false,
      isInit: false,
      form: {},
      query: {},
      loading: false,
      selectionList: [],
      data: [],
      props: {
        id: "id",
        name: "realName",
        records: "data.data.records",
        total: "data.data.total",
      },
      option: {
        searchIndex: 3,
        searchIcon: true,
        size: "mini",
        searchSize: "mini",
        align: "center",
        menu: false,
        addBtn: false,
        header: false,
        border: true,
        tip: false,
        reserveSelection: true,
        highlightCurrentRow: true,
        gutter: 5,
        searchMenuSpan: 6,
        selection: true,
        column: [
          {
            label: "",
            prop: "radio",
            type: "radio",
            width: 55,
            hide: true,
          },
          {
            label: "状态",
            prop: "status",
            dicData: [],
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            dataType: "number",
            display: false,
            type: "select",
          },
          {
            label: "申请人",
            prop: "createUserName",
          },
          {
            label: "项目编号",
            prop: "serialNo",
          },
          {
            label: "项目名称",
            prop: "title",
            overHidden: true,
          },
          {
            label: "负责人",
            prop: "leader",
            width: 120,
            slot: true,
          },
          {
            label: "成员数",
            prop: "personNum",
            display: true,
          },
          {
            label: "总预算(W)",
            prop: "budget",
          },
          {
            label: "已申请(W)",
            prop: "applyBudget",
          },
          {
            label: "计划开始",
            prop: "startDate",
          },
          {
            label: "计划完成",
            prop: "endDate",
          },
          {
            label: "访问控制",
            prop: "acl",
            dicData: [
              {
                label: "私有",
                value: "1",
              },
              {
                label: "公开",
                value: "2",
              },
            ],
          },
        ],
      },
    };
  },
  mounted() {
    this.init();
  },
  methods: {
    handleClear() {
      this.pTitle = "";
      this.selectionList = [];
      this.$emit("update:label", "");
      this.$emit("update:value", "");
      this.$emit("clear");
      this.dispatch("ElFormItem", "el.form.blur", [""]);
    },
    rowTagType(status) {
      if (!status) {
        return "";
      }
      if (status === 0) {
        //草稿
        return "info";
      } else if ([1, 2].includes(status)) {
        //已提交,审核中
        return "";
      } else if (status === 3) {
        //被驳回
        return "danger";
      } else if (status === 4) {
        //已撤销
        return "warning";
      } else if (status === 5) {
        //已挂起
        return "warning";
      } else if (status === 6) {
        //已终止
        return "danger";
      } else if (status === 9) {
        //已审核
        return "success";
      }
    },
    bpFocus() {
      this.visible = true;
      this.query = { ...this.params };
      this.selectionList = [];
    },
    init() {
      if (!this.isInit) {
        if (this.customOption) {
          const { column, userProps } = this.customOption;
          if (column) this.$set(this.option, "column", column);
          if (userProps) this.$set(this, "props", userProps);
        }
        this.$http
          .get("/api/blade-system/dict/dictionary?code=data_status")
          .then((res) => {
            const column = this.findObject(this.option.column, "status");
            column.dicData = res.data.data;
            this.statusDict = res.data.data;
            this.statusDictKeyValue = this.statusDict.reduce((acc, cur) => {
              acc[cur.dictKey] = cur.dictValue;
              return acc;
            }, {});
          });
        this.isInit = true;
      }
    },
    handleConfirm() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.pTitle = this.titles;
      this.$emit("input", this.ids);
      this.$emit("confirm", this.selectionList, this.ids, this.titles);
      this.$nextTick(() => {
        this.dispatch("ElFormItem", "el.form.blur", [this.ids]);
      });
      this.handleClose();
    },
    handleClose(done) {
      // this.selectionClear()
      this.visible = false;
      if (done && typeof done == "function") done();
    },
    searchReset() {
      this.query = {};
      this.onLoad();
    },
    searchChange(params, done) {
      this.query = params;
      this.onLoad(params);
      done();
    },
    selectionClear() {
      this.selectionList = [];
      if (this.$refs.crud) this.$refs.crud.toggleSelection();
    },
    rowClick(row) {
      if (!this.multiple) {
        this.selectionList = [row];
        this.$set(this.form, "radio", row.id);
      } else this.$refs.crud.toggleSelection([row]);
    },
    async changeDefaultChecked() {
      if (!this.value || this.value.length < 1) return;
      let defaultChecked = this.value;

      if (this.multiple) {
        // this.selectionClear()
        const checks = defaultChecked.split(",");
        if (checks.length > 0) {
          setTimeout(() => {
            checks.forEach(async (c) => {
              let row = this.data.find((d) => d.id == c); // 当前页查找
              if (!row) {
                row = this.selectionList.find((d) => d.id == c); // 勾选列表查找
                if (!row) {
                  let res = await getDetail(c); // 接口查找
                  if (res.data.data) row = res.data.data;
                }
              }
              if (row && this.$refs.crud)
                this.$refs.crud.toggleRowSelection(row, true);
            });
          }, 500);
        }
      } else {
        let row = this.data.find((d) => d.id == defaultChecked);
        if (!row) {
          let res = await getDetail(defaultChecked);
          if (res.data.data) row = res.data.data;
        }
        if (row) {
          this.selectionList = [row];
          this.$set(this.form, "radio", defaultChecked);
        } else {
          this.selectionList = [];
          this.$set(this.form, "radio", "");
        }
      }
      this.pTitle = this.titles;
    },
    onLoad(params = {}) {
      this.loading = true;
      getPage(Object.assign(params, this.query, this.params)).then((res) => {
        this.data = res.data.data || [];
        this.data.forEach(
          (item) => (item.applyBudget = item.applyBudget / 10000)
        );
        this.loading = false;
        this.changeDefaultChecked();
      });
    },
    getAsVal(obj, bind = "") {
      let result = this.deepClone(obj);
      if (this.validatenull(bind)) return result;
      bind.split(".").forEach((ele) => {
        if (!this.validatenull(result[ele])) {
          result = result[ele];
        } else {
          result = "";
        }
      });
      return result;
    },
  },
};
</script>
<style lang="scss">
.p-dialog {
  display: flex;
  flex-direction: column;
  margin: 0 !important;
  position: absolute;
  top: 40%;
  left: 50%;
  transform: translate(-50%, -40%);
  max-height: calc(100% - 30px);
  max-width: calc(100% - 30px);

  .el-dialog__body {
    flex: 1;
    overflow: auto;
  }
}
</style>

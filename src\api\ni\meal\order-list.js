import request from "@/router/axios";

export const gePage = (current, size, params) => {
  return request({
    url: "/api/ni/meal/order/list/page",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/ni/meal/order/list/detail",
    method: "get",
    params: {
      id,
    },
  });
};
export const add = (row) => {
  return request({
    url: "/api/ni/meal/order/list/submit",
    method: "post",
    data: row,
  });
};
export const update = (row) => {
  return request({
    url: "/api/ni/meal/order/list/submit",
    method: "post",
    data: row,
  });
};
export const remove = (ids) => {
  return request({
    url: "/api/ni/meal/order/list/remove",
    method: "post",
    params: {
      ids,
    },
  });
};

export const pickedUp = (ids) => {
  return request({
    url: "/api/ni/meal/order/list/pickedUp",
    method: "post",
    params: {
      ids,
    },
  });
};
export const repair = (ids) => {
  return request({
    url: "/api/ni/meal/order/list/repair",
    method: "post",
    params: {
      ids,
    },
  });
};

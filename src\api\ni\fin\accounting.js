import request from '@/router/axios';

export const getPage = (current, size, params) => {
  return request({
    url: '/api/ni/fin/accounting/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/ni/fin/accounting/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const getTree = (params) => {
  return request({
    url: '/api/ni/fin/accounting/tree',
    method: 'get',
    params
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/ni/fin/accounting/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/ni/fin/accounting/save',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/ni/fin/accounting/update',
    method: 'post',
    data: row
  })
}

export const submit = (ids) => {
  return request({
    url: '/api/ni/fin/accounting/submit',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const back = (ids) => {
  return request({
    url: '/api/ni/fin/accounting/back',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const toVoid = (ids) => {
  return request({
    url: '/api/ni/fin/accounting/toVoid',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const getLazyList = (parentId, params) => {
  return request({
    url: '/api/ni/fin/accounting/lazy-list',
    method: 'get',
    params: {
      ...params,
      parentId
    }
  })
}

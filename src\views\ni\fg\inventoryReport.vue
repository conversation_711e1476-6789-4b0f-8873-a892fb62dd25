<template>
  <div class="dashboard-container">
    <div class="header">
      <h1><i class="el-icon-data-analysis"></i> 库存监控仪表盘</h1>
      <!--      <h1><i class="fa-solid fa-warehouse"></i> 库存监控仪表盘</h1>-->
      <div class="date-info">
        <i class="el-icon-timer"></i> 数据更新: {{ currentDate }}
      </div>
    </div>
    <div style="display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    margin-bottom: 5px;
    padding-bottom: 5px;
    flex-direction:row-reverse;">
      <div>
        <el-radio-group  v-model="mode" size="mini" @input="this.onLoad">
          <el-radio-button label="Day">日</el-radio-button>
          <el-radio-button label="Week">周</el-radio-button>
          <el-radio-button label="Month">月</el-radio-button>
        </el-radio-group>
      </div>
    </div>
    <div class="kpi-cards kpi-cards-fixed">

      <div class="kpi-card">
        <div class="kpi-title">
          <i class="el-icon-s-grid"></i>
          <span>库存余量 (箱)</span>
        </div>
        <div class="kpi-value" >{{inventoryData.total}}</div>
        <div class="kpi-desc">当前仓库中的实际库存总量，反映即时库存水平</div>
      </div>
      <div class="kpi-card">
        <div class="kpi-title">
          <i class="el-icon-box"></i>
          <span>当期净变化</span>
        </div>
        <div class="kpi-value">
          <span :class="inventoryData.netWorth < 0 ? 'trend-down' : 'trend-up'">
            {{ inventoryData.netWorth > 0 ? '+' : '' }}{{ inventoryData.netWorth.toLocaleString() }} 箱
          </span>
        </div>
        <div class="kpi-desc">入库量减出库量的净值，反映库存增减趋势</div>
      </div>
      <div class="kpi-card">
        <div class="kpi-title">
          <i class="el-icon-shopping-cart-full"></i>
          <span>当期入库量</span>
        </div>
        <div class="kpi-value" :style="{color: inventoryData.inbound < 5 ? '#f56c6c' : '#67c23a'}">
          {{ inventoryData.inbound }} 箱
        </div>
        <div class="kpi-desc">当期入库量</div>
        <div v-if="inventoryData.continuous !== 0"  :class = "inventoryData.isGrowing ? 'alert alert-green' : 'alert alert-orange'">
          <i class="el-icon-info"></i> 入库量连续 {{ inventoryData.continuous }} 天 {{inventoryData.isGrowing ? '增加':'减少' }}
        </div>
      </div>
      <div class="kpi-card">
        <div class="kpi-title">
          <i class="el-icon-shopping-cart-full"></i>
          <span>当期出库量</span>
        </div>
        <div class="kpi-value" :style="{color: inventoryData.outbound < 5 ? '#f56c6c' : '#67c23a'}">
          {{ inventoryData.outbound }} 箱
        </div>
        <div class="kpi-desc">当期出库量</div>
        <!--        <div v-if="inventoryData.inboundOfMonth < 5" class="alert alert-red">-->
        <!--          <i class="el-icon-info"></i> 低于安全库存线-->
        <!--        </div>-->
      </div>
    </div>
    <div class="charts-container charts-container-fixed">
      <div class="chart-card">
        <div class="chart-title">
          <i class="el-icon-data-line"></i> 14天库存变化趋势
        </div>
        <div ref="inventoryChart" class="chart"></div>
      </div>
      <div class="chart-card">
        <div class="chart-title">
          <i class="el-icon-data-analysis"></i> 最近7天出入库对比
        </div>
        <div ref="inOutChart" class="chart"></div>
      </div>
    </div>
    <!--
    <div class="footer">
      数据更新时间: {{ currentDate }} | 安全库存线: 5天 | 系统建议: 库存连续3天减少需关注
    </div>
    -->
  </div>
</template>

<script >
import * as echarts from 'echarts'
import request from "@/router/axios";
export  default {
  data(){
    return {
      mode:'Day',
      currentDate : new Date().toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        weekday: 'long'
      }),
      inventoryData: {
        total: 0,
        netWorth: 0,
        inboundOfMonth: 0,
        inbound: 0,
        outbound:0,
        continuous: 0,
        isGrowing : true
      },
      inventoryHistory:[],
      inOutData:{
        dates: [],
        in: [],
        out: []
      },
      // inventoryChart:{},
      // inOutChart:{},
      chartInstances : {
        inventory: null,
        inout: null
      }
    }
  },
  methods :{
    handleResize() {
      this.chartInstances.inventory && this.chartInstances.inventory.resize()
      this.chartInstances.inout && this.chartInstances.inout.resize()
    },
    renderCharts() {

      // 14天库存曲线
      this.chartInstances.inventory = echarts.init(this.$refs.inventoryChart)
      this.chartInstances.inventory.setOption({
        tooltip: {
          trigger: 'axis',
          formatter: '{b}: {c} 箱'
        },
        grid: {
          left: '3%', right: '3%', bottom: '3%', top: '10%', containLabel: true
        },
        xAxis: {
          inverse:true,
          type: 'category',
          data: Array.from({length: 14}, (_, i) => `${i+1}天前`),
          axisLine: { show: false },
          axisTick: { show: false }
        },
        yAxis: {
          type: 'value',
          name: '箱',
          min: 100,
          splitLine: { lineStyle: { type: 'dashed' } }
        },
        series: [{
          name: '库存总量',
          type: 'line',
          data: this.inventoryHistory,
          smooth: true,
          lineStyle: { width: 3, color: '#5470c6' },
          itemStyle: { color: '#5470c6' },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: 'rgba(84, 112, 198, 0.5)' },
              { offset: 1, color: 'rgba(84, 112, 198, 0.1)' }
            ])
          }
          // markLine: {
          //   silent: true,
          //   lineStyle: { color: '#f56c6c', type: 'dashed' },
          //   data: [{ yAxis: 12500, name: '警戒线' }]
          // }
        }]
      })

      // 最近7天出入库对比
      this.chartInstances.inout = echarts.init(this.$refs.inOutChart)
      this.chartInstances.inout.setOption({
        tooltip: { trigger: 'axis', axisPointer: { type: 'shadow' } },
        legend: { data: ['入库', '出库'], right: 10, top: 0 },
        grid: { left: '3%', right: '3%', bottom: '3%', top: '15%', containLabel: true },
        xAxis: {
          type: 'category',
          data: this.inOutData.dates,
          axisLine: { show: false },
          axisTick: { show: false }
        },
        yAxis: {
          type: 'value',
          name: '箱',
          splitLine: { lineStyle: { type: 'dashed' } }
        },
        series: [
          {
            name: '入库',
            type: 'bar',
            barWidth: '35%',
            data: this.inOutData.in,
            itemStyle: { color: '#91cc75' },
            label: { show: true, position: 'top' }
          },
          {
            name: '出库',
            type: 'bar',
            barWidth: '35%',
            data: this.inOutData.out,
            itemStyle: { color: '#5470c6' },
            label: { show: true, position: 'top' }
          }
        ]
      })

      window.addEventListener('resize', this.handleResize)
    },
    onLoad(){
      try {
        request({
          url: '/api/ni/fg/inventory-report/report',
          method: 'get',
          params:{
            mode: this.mode
          }
        }).then((res) => {
          let reportData = res.data.data;
          this.inventoryData.total = reportData.total;
          this.inventoryData.inbound = reportData.inbound;
          this.inventoryData.outbound = reportData.outbound;
          this.inventoryData.inboundOfMonth = reportData.inboundOfMonth;
          this.inventoryData.netWorth = reportData.netWorth;
          this.inventoryData.isGrowing = parseInt(reportData.continuous) > 0;
          this.inventoryData.continuous = Math.abs(parseInt(reportData.continuous));
          this.inventoryHistory = reportData.details.map(d=>d.total);
          this.inOutData.dates = reportData.details.map(d=>new Date(d.snapDate).toLocaleDateString("zh-CN"));
          this.inOutData.out = reportData.details.map(d=>d.outboundTotal);
          this.inOutData.in = reportData.details.map(d=>d.inboundTotal);

          this.currentDate = new Date(reportData.snapDate).toLocaleDateString('zh-CN', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            weekday: 'long'
          });

          this.renderCharts()
        })
      } catch (e) {
        this.$message.error('数据加载失败');
        console.error('获取数据失败', e)
      }
    }
  },
  mounted () {
    this.onLoad();
  },
  beforeMount() {
    window.removeEventListener('resize', this.handleResize)
    this.chartInstances.inventory && this.chartInstances.inventory.dispose()
    this.chartInstances.inout && this.chartInstances.inout.dispose()
  },

}

</script>

<style >
.dashboard-container {
  /* max-width: 1200px; */
  margin: 0 auto;
  background: white;
  border-radius: 12px;
  box-shadow: 0 6px 18px rgba(0, 0, 0, 0.08);
  padding: 25px;
}
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #eaeef5;
}
.header h1 {
  color: #2c3e50;
  font-size: 26px;
  font-weight: 600;
}
.date-info {
  background: #f0f7ff;
  padding: 8px 15px;
  border-radius: 8px;
  font-size: 14px;
  color: #409eff;
  font-weight: 500;
}
.kpi-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}
.kpi-cards-fixed {
  grid-template-columns: repeat(4, 1fr) !important;
}
.kpi-card {
  background: white;
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border-left: 4px solid #409eff;
}
.kpi-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
}
.kpi-title {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  font-size: 16px;
  color: #606266;
}
.kpi-icon {
  margin-right: 10px;
  font-size: 18px;
  color: #409eff;
}
.kpi-value {
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 8px;
  color: #2c3e50;
}
.kpi-desc {
  font-size: 14px;
  color: #909399;
  line-height: 1.5;
}
.alert {
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 13px;
  font-weight: 500;
  margin-top: 10px;
  display: inline-block;
}
.alert-red {
  background: #fef0f0;
  color: #f56c6c;
  border-left: 3px solid #f56c6c;
}
.alert-orange {
  background: #fdf6ec;
  color: #e6a23c;
  border-left: 3px solid #e6a23c;
}
.alert-green {
  background: #fdf6ec;
  color: #2dcd12;
  border-left: 3px solid #03c541;
}
.charts-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
  gap: 20px;
  margin-top: 30px;
}
.charts-container-fixed {
  grid-template-columns: repeat(2, 1fr) !important;
}
.chart-card {
  background: white;
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}
.chart-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 15px;
  color: #2c3e50;
  display: flex;
  align-items: center;
}
.chart-title i {
  margin-right: 10px;
  color: #409eff;
}
.chart {
  height: 250px;
}
.trend-up {
  color: #67c23a;
}
.trend-down {
  color: #f56c6c;
}
.footer {
  text-align: center;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #eaeef5;
  color: #909399;
  font-size: 14px;
}
@media (max-width: 768px) {
  .charts-container,
  .charts-container-fixed {
    grid-template-columns: 1fr !important;
  }
  .kpi-cards,
  .kpi-cards-fixed {
    grid-template-columns: 1fr !important;
  }
  .chart {
    height: 220px;
  }
}
</style>

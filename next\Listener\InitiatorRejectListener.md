```java
// 定义包路径，属于问题反馈模块的监听器组件
package com.natergy.ni.feedback.listener;

// 导入MyBatis-Plus的查询条件构造器
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
// 导入问题反馈实体类和状态枚举
import com.natergy.ni.feedback.entity.FeedbackEntity;
import com.natergy.ni.feedback.enums.FeedbackStatusEnum;
import com.natergy.ni.feedback.service.IFeedbackService;
// 导入Lombok日志注解
import lombok.extern.slf4j.Slf4j;
// 导入Flowable任务监听器相关类
import org.flowable.engine.delegate.TaskListener;
import org.flowable.task.service.delegate.DelegateTask;
// 导入工作流常量类
import org.springblade.plugin.workflow.core.constant.WfProcessConstant;
// 导入Spring组件注解和事务注解
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

// 导入Java工具类
import java.util.Map;

// 静态导入工作流驳回状态常量
import static org.springblade.plugin.workflow.core.constant.WfProcessConstant.STATUS_REJECT;

/**
 * 发起人驳回，流程回到问题负责人
 * （当问题发起人驳回处理结果时触发的监听器）
 *
 * <AUTHOR>  // 作者标识
 * @since 2025-04-19  // 类创建日期
 */
// @Slf4j：Lombok注解，自动生成日志对象，用于打印日志
@Slf4j
// @Component("InitiatorRejectListener")：Spring注解，将类注册为组件，名称为"InitiatorRejectListener"
// 供Flowable工作流引擎通过该名称调用此监听器
@Component("InitiatorRejectListener")
// 实现Flowable的TaskListener接口，作为任务监听器（监听任务相关事件）
public class InitiatorRejectListener implements TaskListener {

	// 注入问题反馈服务，用于操作问题反馈数据
	private final IFeedbackService feedbackService;

	// 构造函数注入问题反馈服务
	public InitiatorRejectListener(IFeedbackService feedbackService) {
		this.feedbackService = feedbackService;
	}

	// @Transactional(rollbackFor = Exception.class)：声明事务，发生异常时回滚
	@Transactional(rollbackFor = Exception.class)
	// 实现TaskListener接口的notify方法，任务事件触发时执行
	@Override
	public void notify(DelegateTask delegateTask) {
		// 获取任务相关的流程变量（包含审批结果等信息）
		Map<String, Object> variables = delegateTask.getVariables();

		// 仅处理被驳回的情况（判断流程变量中"process_terminate"是否为驳回状态）
		if (STATUS_REJECT.equals(variables.get(WfProcessConstant.TASK_VARIABLE_PROCESS_TERMINATE))) {
			// 获取流程实例ID（用于关联问题反馈实体）
			String processInstanceId = delegateTask.getProcessInstanceId();

			// 根据流程实例ID查询对应的问题反馈实体
			FeedbackEntity feedbackEntity = feedbackService.getOne(
				new LambdaQueryWrapper<FeedbackEntity>()
					.eq(FeedbackEntity::getProcessInstanceId, processInstanceId)
			);

			// 更新问题状态为"发起人已驳回"
			feedbackEntity.setStatus(FeedbackStatusEnum.INITIATOR_REJECTED.getValue());

			// 保存更新后的问题反馈实体
			feedbackService.saveOrUpdate(feedbackEntity);
		}
	}
}
```

### 类功能说明

该类是基于**Flowable 工作流引擎**的**任务监听器**，命名为`InitiatorRejectListener`，主要作用是在**问题发起人驳回处理结果时**触发，更新问题状态为 “发起人已驳回”，并使流程回退到负责人处理环节，实现流程的反向流转。

#### 核心业务场景

当问题负责人提交处理结果后，发起人审核时认为问题未解决并选择驳回，监听器被触发，将问题状态更新为 “发起人已驳回”，同时工作流自动将任务退回给负责人，要求重新处理，确保问题处理符合发起人的预期。

#### 关键逻辑解析

1. **判断驳回状态**：从流程变量中获取`TASK_VARIABLE_PROCESS_TERMINATE`（流程终止 / 驳回标识），仅处理状态为`STATUS_REJECT`（驳回）的场景。
2. **关联业务实体**：通过流程实例 ID 查询对应的`FeedbackEntity`，确保操作的是当前流程关联的问题。
3. **更新问题状态**：将问题状态设置为`INITIATOR_REJECTED`（发起人已驳回），明确标识问题处理被驳回，便于负责人识别并重新处理。

#### 技术亮点

- **针对性处理**：仅响应驳回事件，避免干扰其他流程状态（如通过、撤回等）。
- **状态同步**：实时更新业务实体状态与流程状态，确保两者一致，便于用户在系统中查看最新进展。
- **事务保障**：通过`@Transactional`确保状态更新的原子性，避免流程驳回后业务数据未同步的情况。

该监听器是流程反向流转的关键组件，通过自动处理驳回事件，实现流程状态与业务数据的联动，提升系统的流程管控能力。
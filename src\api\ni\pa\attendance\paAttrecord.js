import request from "@/router/axios";

export const getList = (current, size, params) => {
  return request({
    url: "/api/ni/pa/paAttRecord/page",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/ni/pa/paAttRecord/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const getListByUser = (current, size, params) => {
  return request({
    url: "/api/ni/pa/paAttRecord/page-report-item",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const reset = (userId, attDate) => {
  return request({
    url: "/api/ni/pa/attendance/reset",
    method: "post",
    params: {
      userId,
      attDate,
    },
  });
};

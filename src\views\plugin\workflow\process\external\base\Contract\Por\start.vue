<template>
  <basic-container>
    <avue-skeleton :loading="waiting" avatar :rows="8">
      <div style="display: flex">
        <avue-title
          style="margin-bottom: 20px"
          :styles="{ fontSize: '20px' }"
          :value="process.name"
        ></avue-title>
        <el-badge
          v-if="permission.wf_process_draft && draftCount > 0"
          :value="draftCount"
          style="margin-top: 5px; margin-right: 40px"
          type="warning"
        >
          <el-button size="mini" v-loading="loading" @click="handleDraftBox"
            >草稿箱
          </el-button>
        </el-badge>
      </div>
      <el-card shadow="never" style="margin-top: 20px">
        <!-- 自定义表单区域 -->
        <avue-form
          v-if="
            option &&
            ((option.column && option.column.length > 0) ||
              (option.group && option.group.length > 0))
          "
          v-model="form"
          ref="form"
          :option="option"
          :defaults.sync="defaults"
          @submit="handleSubmit"
          @error="loading = false"
          :upload-preview="handleUploadPreview"
        >
          <template #parentId="{ disabled, size, index }">
            <contract-select
              v-model="form.parentId"
              pay-type="2"
              :disabled="disabled"
              :size="size"
              :params="{
                status: 9,
              }"
            />
          </template>
          <template #amount="{ disabled, size }">
            <el-input-number
              v-if="form.porOrderAmount"
              :size="size"
              v-model="form.amount"
              :precision="2"
              controls-position="right"
              :max="form.porOrderAmount"
              style="width: 100%"
              @change="handleAmountChange"
            ></el-input-number>
            <el-input-number
              v-else
              :size="size"
              v-model="form.amount"
              :precision="2"
              controls-position="right"
              style="width: 100%"
              @change="handleAmountChange"
            ></el-input-number>
          </template>
          <template #b="{ row, index, size, disabled }">
            <supplier-select
              v-model="form.b"
              :size="size"
              :multiple="false"
              :disabled="disabled"
              @submit="handleSupplierSubmit"
            />
          </template>
          <template #orderId="{ disabled, size, index }">
            <por-order-select
              v-model="form.orderId"
              :label.sync="form.order"
              :size="size"
              :multiple="false"
              :disabled="disabled"
              :params="{
                brand: form.brand,
                supplierId: form.b,
              }"
              :before-select="beforeOrderSelect"
              @clear="handleOrderClear"
              @submit="handleOrderSubmit"
            />
          </template>
          <template #items="{ row, index, size }">
            <payable-apply-order-item v-model="form.items" />
          </template>
        </avue-form>
        <!-- 自定义表单区域 -->
      </el-card>
      <el-card shadow="never" style="margin-top: 20px" v-if="showExamForm">
        <wf-examine-form
          ref="examineForm"
          :process="process"
          @user-select="handleUserSelect"
        ></wf-examine-form>
      </el-card>
      <div style="height: 120px"></div>
      <el-row
        class="foot-item avue-affix"
        :style="{
          width: isCollapse ? 'calc(100% - 80px)' : 'calc(100% - 260px)',
        }"
        id="avue-view"
      >
        <el-button
          v-no-more-click
          type="primary"
          size="mini"
          v-loading="loading"
          @click="handleSubmit"
          >发起
        </el-button>
        <el-button
          v-if="permission.wf_process_draft"
          type="success"
          size="mini"
          v-loading="loading"
          @click="
            handleDraftNotClose(process.id, process.formKey, form, process.key)
          "
          >存为草稿
        </el-button>
        <el-button
          v-if="permission.wf_process_draft"
          type="success"
          size="mini"
          v-loading="loading"
          @click="handleDraft(process.id, process.formKey, form, process.key)"
          >存为草稿并关闭
        </el-button>
      </el-row>
    </avue-skeleton>

    <!-- 人员选择弹窗 -->
    <wf-user-select
      ref="user-select"
      :check-type="checkType"
      :default-checked="defaultChecked"
      @onConfirm="handleUserSelectConfirm"
    ></wf-user-select>
    <!-- 草稿弹窗 -->
    <draft-popup
      :visible.sync="isDraftPopupVisible"
      :draftList="draftList"
      @select="handleDraftSelect"
      @delete="handleDraftDelete"
    ></draft-popup>
  </basic-container>
</template>

<script>
import WfExamineForm from "@/views/plugin/workflow/process/components/examForm.vue";
import WfUserSelect from "@/views/plugin/workflow/process/components/user-select";

import exForm from "@/views/plugin/workflow/mixins/ex-form";
import draft from "@/views/plugin/workflow/mixins/draft";
import { dateFormat } from "@/util/date";
import SupplierSelect from "@/views/ni/base/components/SupplierSelect1.vue";
import OrderItem from "@/views/ni/por/components/OrderItem";
import PorOrderSelect from "@/views/ni/por/components/OrderSelect";
import OrderSelectDialog from "@/views/ni/por/components/OrderSelectDialog";
import PayableApplyOrderItem from "@/views/ni/fin/components/PayableApplyOrderItem";
import { getDetail } from "@/api/ni/por/order";
import { numToCapital } from "@/util/util";
import UserSelect from "@/components/user-select";
import DraftPopup from "@/views/plugin/workflow/process/components/draftPopup.vue";
import debounce from "@/util/debounce";
import ContractSelect from "@/views/ni/base/components/ContractSelect.vue";

export default {
  components: {
    ContractSelect,
    PorOrderSelect,
    WfUserSelect,
    WfExamineForm,
    SupplierSelect,
    OrderItem,
    OrderSelectDialog,
    PayableApplyOrderItem,
    UserSelect,
    DraftPopup,
  },
  mixins: [exForm, draft],
  activated() {
    let val = this.$route.query.p;
    if (val) {
      let text = Buffer.from(val, "base64").toString();
      text = text.replace(/[\r|\n|\t]/g, "");
      const param = JSON.parse(text);
      const { processId, processDefKey, form } = param;
      if (form) {
        const f = JSON.parse(
          new Buffer(decodeURIComponent(form), "utf8").toString("utf8")
        );
        this.form = Object.assign(this.form, f);
        const items = this.findObject(this.option.column, "items");
        if (this.form.items && this.form.items.length > 0) {
          items.display = true;
        } else {
          items.display = false;
        }
      }
      this.form.taxRate = 0;
      if (processId) {
        this.getForm(processId);
      } else if (processDefKey) {
        this.getFormByProcessDefKey(processDefKey);
      }
    }
  },
  computed: {
    showExamForm() {
      const { hideComment, hideCopy, hideExamine } = this.process;
      return !hideComment || !hideCopy || !hideExamine;
    },
  },
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      //  这里的vm指的就是vue实例，可以用来当做this使用
      vm.fromPath = from.path; //获取上一级路由的路径
    });
  },
  data() {
    return {
      orderSelectShow: false,
      fromPath: "",
      defaults: {},
      form: {},
      option: {
        labelWidth: 120,
        calcHeight: 30,
        size: "mini",
        menuBtn: false,
        column: [
          {
            type: "input",
            label: "申请人",
            display: true,
            prop: "createUserName",
            value: "${this.$store.getters.userInfo.nick_name}",
            readonly: true,
            span: 8,
          },
          {
            type: "input",
            label: "申请部门",
            display: true,
            prop: "createDeptName",
            value: "${this.$store.getters.userInfo.dept_name}",
            readonly: true,
            span: 8,
          },
          {
            label: "账套",
            prop: "brand",
            placeholder: " ",
            type: "radio",
            dicUrl: "/api/blade-system/dict-biz/dictionary?code=ni_brand",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            row: true,
            span: 8,
            rules: [
              {
                required: true,
                message: "请选择账套",
                trigger: "blur",
              },
            ],
          },

          {
            label: "合同名称",
            prop: "name",
            span: 8,
            rules: [
              {
                required: true,
                message: "请输入合同名称",
                trigger: "blur",
              },
            ],
          },
          {
            label: "合同编号",
            prop: "serialNo",
            placeholder: "系统自动生成",
            type: "input",
            span: 8,
          },
          {
            label: "模板合同",
            labelTip: "模板合同不需要法务审批",
            prop: "template",
            type: "radio",
            value: true,
            span: 8,
            dicData: [
              {
                label: "是",
                value: true,
              },
              {
                label: "否",
                value: false,
              },
            ],
            rules: [
              {
                required: true,
                message: "请选择是否模板合同",
                trigger: "blur",
              },
            ],
          },
          {
            label: "供应商",
            prop: "b",
            span: 8,
            rules: [
              {
                required: true,
                message: "请选择供应商",
                trigger: "blur",
              },
            ],
          },
          {
            label: "供应商负责人",
            prop: "bbPic",
            span: 8,
            row: true,
            rules: [
              {
                required: true,
                message: "请输入供应商负责人",
                trigger: "blur",
              },
            ],
          },
          {
            label: "合同类型",
            prop: "type",
            type: "cascader",
            span: 8,
            dicUrl:
              "/api/blade-system/dict-biz/dictionary-tree?code=ni_base_contract_type",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            slot: true,
            rules: [
              {
                required: true,
                message: "请选择合同类型",
                trigger: "blur",
              },
            ],
            control: (val) => {
              const config = {};
              // 处理其他字段的显示逻辑（保留原有逻辑）
              if (val.includes("1")) {
                if (val.includes("11")) {
                  config.orderId = { display: false };
                  config.amount = {
                    rules: [
                      {
                        required: false,
                        message: "请输入申请金额",
                        trigger: "blur",
                      },
                    ],
                  };
                  config.items = { display: false };
                } else {
                  config.orderId = { display: true };
                  config.amount = {
                    rules: [
                      {
                        required: true,
                        message: "请输入申请金额",
                        trigger: "blur",
                      },
                    ],
                  };
                  config.items = { display: true };
                }
              } else {
                config.orderId = { display: false };
                config.amount = {
                  rules: [
                    {
                      required: true,
                      message: "请输入申请金额",
                      trigger: "blur",
                    },
                  ],
                };
                config.items = { display: false };
              }
              config.parentId = { display: val === "4" };
              return config;
            },
          },
          {
            label: "关联合同",
            prop: "parentId",
            span: 8,
            display: true,
            row: true,
            rules: [
              {
                required: true,
                message: "请选择关联合同",
                trigger: "blur",
              },
            ],
          },
          {
            label: "采购订单",
            prop: "orderId",
            display: false,
            span: 8,
            rules: [
              {
                required: true,
                message: "请选择采购订单",
                trigger: "blur",
              },
            ],
          },
          {
            label: "发票类型",
            prop: "billType",
            minWidth: 93,
            span: 8,
            type: "select",
            dicUrl: "/api/blade-system/dict-biz/dictionary?code=ni_bill_type",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            placeholder: " ",
            rules: [
              {
                required: true,
                message: "请输入",
                trigger: "blur",
              },
            ],
            change: ({ value }) => {
              if (value === "1") {
                this.form.taxRate = 13;
              } else if (value === "2") {
                this.form.taxRate = 0;
              }
            },
          },
          {
            label: "税率(%)",
            prop: "taxRate",
            hide: true,
            placeholder: " ",
            showColumn: false,
            type: "number",
            precision: 2,
            span: 8,
            value: 0.0,
            controls: false,
            change: ({ value }) => {
              if (value != null && value !== 0) {
                this.form.unTaxAmount =
                  this.form.amount -
                  (this.form.amount / ((100 + value) / 100)) * (value / 100);
                this.form.unTaxUpperAmount = numToCapital(
                  this.form.unTaxAmount
                );
              } else {
                this.form.unTaxAmount = this.form.amount;
                this.form.unTaxUpperAmount = numToCapital(this.form.amount);
              }
            },
          },
          {
            label: "含税金额",
            prop: "amount",
            type: "number",
            placeholder: " ",
            precision: 2,
            span: 8,
            rules: [
              {
                required: true,
                message: "请输入含税金额",
                trigger: "blur",
              },
            ],
            change: ({ value }) => {
              this.form.upperAmount = numToCapital(value);
              if (this.form.taxRate && this.form.taxRate > 0) {
                this.form.unTaxAmount =
                  this.form.amount -
                  (this.form.amount / ((100 + this.form.taxRate) / 100)) *
                    (this.form.taxRate / 100);
              } else {
                this.form.unTaxAmount = value;
              }
              this.form.unTaxUpperAmount = numToCapital(this.form.unTaxAmount);
            },
          },
          {
            label: "含税大写",
            prop: "upperAmount",
            readonly: true,
            placeholder: " ",
            hide: true,
            showColumn: false,
            span: 8,
          },
          {
            label: "不含税金额",
            prop: "unTaxAmount",
            type: "number",
            placeholder: " ",
            disabled: true,
            precision: 2,
            span: 8,
          },
          {
            label: "不含税大写",
            prop: "unTaxUpperAmount",
            readonly: true,
            placeholder: " ",
            hide: true,
            span: 8,
            showColumn: false,
          },
          {
            label: "币种",
            prop: "currency",
            type: "select",
            dicUrl: "/api/blade-system/dict/dictionary?code=currency",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            display: false,
            value: "RMB",
            placeholder: " ",
            span: 8,
            rules: [
              {
                required: true,
                message: "请选择币种",
                trigger: "blur",
              },
            ],
            control: (val) => {
              if (val === "RMB") {
                return {
                  excRate: {
                    display: false,
                    value: 1,
                  },
                };
              } else {
                return {
                  excRate: {
                    display: true,
                  },
                };
              }
            },
          },
          {
            label: "汇率",
            prop: "excRate",
            labelTip:
              "汇率=本位币/原币.如本位币为人民币，原币为美元: 汇率为:0.1439.",
            type: "number",
            placeholder: " ",
            span: 8,
            rules: [
              {
                required: true,
                message: "请输入汇率",
                trigger: "blur",
              },
            ],
          },
          {
            label: "附件",
            type: "upload",
            propsHttp: {
              res: "data",
              url: "attachId",
              name: "originalName",
            },
            action: "/api/blade-resource/oss/endpoint/put-file-attach",
            dragFile: true,
            display: true,
            span: 24,
            showFileList: true,
            multiple: true,
            limit: 10,
            prop: "attachment",
          },
          {
            label: "备注",
            prop: "remark",
            type: "textarea",
            span: 24,
            minRows: 3,
          },
          {
            label: "合同明细",
            prop: "items",
            span: 24,
            display: true,
          },
        ],
      },
      process: {},
      loading: false,
      payment: 0,
      typeDict: [],
      typeDictKeyValue: {},
      isDraftPopupVisible: false,
      draftList: [],
      draftCount: 0,
      draftId: null,
    };
  },
  methods: {
    handleAmountChange(value) {
      this.form.upperAmount = numToCapital(value);
      if (this.form.taxRate && this.form.taxRate > 0) {
        this.form.unTaxAmount =
          this.form.amount -
          (this.form.amount / ((100 + this.form.taxRate) / 100)) *
            (this.form.taxRate / 100);
      } else {
        this.form.unTaxAmount = value;
      }
      this.form.unTaxUpperAmount = numToCapital(this.form.unTaxAmount);
    },
    beforeOrderSelect(done) {
      if (!this.form.brand) {
        this.$message({
          type: "warning",
          message: "请选择账套!",
        });
        return;
      }
      if (!this.form.b) {
        this.$message({
          type: "warning",
          message: "请选择供应商!",
        });
        return;
      }
      done();
    },
    handleOrderClear() {
      this.form.amount = null;
      this.form.porOrderAmount = null;
      this.form.upperAmount = null;
      this.form.unTaxUpperAmount = null;
      this.form.items = [];
    },
    handleOrderSubmit(selectList) {
      if (selectList) {
        getDetail(selectList[0].id).then((res) => {
          const { data } = res.data;
          this.form.b = data.supplierId;
          this.form.bName = data.supplier;
          this.form.amount = data.amount;
          this.form.porOrderAmount = data.amount;
          this.form.upperAmount = numToCapital(data.amount);
          this.form.items = data.items.map((item) => ({
            ...item,
            serialNo: data.serialNo,
          }));
        });
      }
    },
    handleSupplierSubmit(selectList) {
      this.form.bName = selectList[0].name;
      this.form.bbType = "supplier";
      this.form.bbPic = selectList[0].linkman;
    },
    getForm(processId) {
      this.getStartForm(processId).then((res) => {
        const _this = this;
        let { process } = res;
        this.form.processId = process.id;
        const option = _this.option;
        const { column, group } = option;
        column.forEach((col) => {
          if (col.value) col.value = _this.getDefaultValues(col.value);
        });
        if (group && group.length > 0) {
          // 处理group
          group.forEach((gro) => {
            gro.column.forEach((col) => {
              if (col.value) col.value = _this.getDefaultValues(col.value);
            });
          });
        }
        if (_this.permission.wf_process_draft) {
          // 查询草稿箱
          this.initDraft(process.id, process.key).then((data) => {
            this.draftCount = data.length;
            this.draftList = data;
            if (data && Array.isArray(data) && data.length > 0) {
              _this
                .$confirm("是否引用之前保存的草稿？", "提示", {})
                .then(() => {
                  this.isDraftPopupVisible = true; // 打开草稿弹窗
                });
            }
          });
        }
        _this.waiting = false;
      });
    },
    getFormByProcessDefKey(processDefKey) {
      this.getStartFormByProcessDefKey(processDefKey).then((res) => {
        const _this = this;
        let { process } = res;
        _this.form.processId = process.id;
        const option = _this.option;
        const { column, group } = option;
        column.forEach((col) => {
          if (col.value) col.value = _this.getDefaultValues(col.value);
        });
        if (group && group.length > 0) {
          // 处理group
          group.forEach((gro) => {
            gro.column.forEach((col) => {
              if (col.value) col.value = _this.getDefaultValues(col.value);
            });
          });
        }
        if (_this.permission.wf_process_draft) {
          // 查询草稿箱
          this.initDraft(process.id, process.key).then((data) => {
            this.draftCount = data.length;
            this.draftList = data;
            if (data && Array.isArray(data) && data.length > 0) {
              _this
                .$confirm("是否引用之前保存的草稿？", "提示", {})
                .then(() => {
                  this.isDraftPopupVisible = true; // 打开草稿弹窗
                });
            }
          });
        }
        _this.waiting = false;
      });
    },
    handleSubmit: debounce(function () {
      this.loading = true;
      //保存再提交
      if (this.form.inquiry) {
        this.form.inquiryState = 1;
      }
      this.form.porState = 1;
      if (this.form.type && this.form.type instanceof Array) {
        this.form.type = this.form.type.join(",");
      }
      if (this.form.type === "4" && !this.form.parentId) {
        this.$message({
          type: "error",
          message: "未选择主合同",
        });
        return;
      }
      if (this.form.type !== "4") {
        this.form.parentId = null;
      }
      this.form.draftId = this.draftId;
      this.handleStartProcess(true)
        .then((done) => {
          this.$message.success("发起成功");
          if (this.draftId != null) {
            this.draftCount = this.draftCount - 1;
            this.draftList = this.draftList.filter(
              (item) => item.id !== this.draftId
            );
          }
          if (this.fromPath) {
            this.handleCloseTag(this.fromPath);
          } else this.handleCloseTag("/ni/base/contract-por");
          done();
        })
        .catch(() => {
          this.loading = false;
        });
    }, 1000),
    getDefaultValues(value) {
      let defaultValue = "";
      if (value.toString().includes("${") && value.toString().includes("}")) {
        try {
          defaultValue = eval("`" + value + "`");
        } catch (err) {
          defaultValue = value;
        }
      } else defaultValue = value;

      return defaultValue;
    },
    //选择草稿
    handleDraftSelect(selectedDraft) {
      //草稿版本与流程版本不一致
      if (!selectedDraft.sameVersion) {
        this.$confirm(
          "选中的草稿与当前流程版本不一致，是否继续引用？",
          "提示",
          {}
        ).then(() => {
          this.draftId = selectedDraft.id;
          this.form = JSON.parse(selectedDraft.variables);
          this.isDraftPopupVisible = false;
        });
      } else {
        this.draftId = selectedDraft.id;
        this.form = JSON.parse(selectedDraft.variables);
      }
    },
    //删除草稿
    handleDraftDelete(draftId) {
      this.$confirm("是否删除选中的草稿箱数据？", "提示", {}).then(() => {
        this.$axios
          .post(`/api/blade-workflow/process/draft/delete/${draftId}`)
          .then((response) => {
            this.$message.success("草稿删除成功");
            this.draftCount = this.draftCount - 1;
            this.draftList = this.draftList.filter(
              (item) => item.id !== draftId
            );
          })
          .catch((error) => {
            this.$message.error("草稿删除失败，请重试");
          });
      });
    },
    handleDraftBox() {
      if (this.draftList.length > 0) {
        this.isDraftPopupVisible = true;
      } else {
        // 重新获取草稿数据
        this.initDraft(this.form.processId).then((data) => {
          this.draftCount = data.length;
          this.draftList = data;
          if (data && Array.isArray(data) && data.length > 0) {
            this.isDraftPopupVisible = true;
          }
        });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.foot-item {
  position: fixed;
  bottom: 0;
  margin-left: -20px;
  // right: 0;
  z-index: 101;
  height: 66px;
  background-color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  -webkit-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
</style>

```java
package com.natergy.ni.feedback.controller;

// 导入FastJSON相关类，用于JSON处理
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.PropertyNamingStrategy;
import com.alibaba.fastjson.serializer.SerializeConfig;
// 导入MyBatis-Plus的查询相关类
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
// 导入Knife4j的API文档注解
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
// 导入当前模块的DTO类
import com.natergy.ni.feedback.dto.QdrantPostDTO;
import com.natergy.ni.feedback.dto.QdrantResponseDTO;
import com.natergy.ni.feedback.dto.SearchResponseDTO;
// 导入当前模块的实体类
import com.natergy.ni.feedback.entity.FeedbackEntity;
import com.natergy.ni.feedback.entity.FeedbackSolvingRecordEntity;
// 导入当前模块的枚举类
import com.natergy.ni.feedback.enums.FeedbackStatusEnum;
// 导入当前模块的参数类
import com.natergy.ni.feedback.params.FeedbackParams;
// 导入当前模块的服务接口
import com.natergy.ni.feedback.service.IFeedbackService;
import com.natergy.ni.feedback.service.IFeedbackSolvingRecordService;
// 导入当前模块的VO类
import com.natergy.ni.feedback.vo.FeedbackVO;
// 导入当前模块的包装类
import com.natergy.ni.feedback.wrapper.FeedbackWrapper;
// 导入其他模块的实体和VO类
import com.natergy.ni.project.entity.ProjectDispatch;
import com.natergy.ni.project.vo.ProjectDispatchVO;
// 导入Swagger的API注解
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
// 导入Lombok注解
import lombok.AllArgsConstructor;
// 导入Apache的工具类
import org.apache.commons.lang3.StringUtils;
// 导入Flowable工作流相关类
import org.flowable.engine.RuntimeService;
// 导入自定义的工具类
import org.springblade.common.utils.RestTemplateUtil;
// 导入BladeX框架的基础控制器类
import org.springblade.core.boot.ctrl.BladeController;
// 导入BladeX框架的MyBatis-Plus支持类
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
// 导入BladeX框架的安全工具类
import org.springblade.core.secure.utils.AuthUtil;
// 导入BladeX框架的API响应类
import org.springblade.core.tool.api.R;
// 导入BladeX框架的工具类
import org.springblade.core.tool.utils.Func;
// 导入BladeX框架的权限注解
import org.springblade.modules.system.annotation.DataPermissions;
// 导入系统模块的用户服务接口
import org.springblade.modules.system.service.IUserService;
// 导入Spring的注解
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

// 导入Java验证相关类
import javax.validation.Valid;
// 导入Java工具类
import java.util.*;
// 导入Java并发相关类
import java.util.concurrent.CompletableFuture;
// 导入Java流相关类
import java.util.stream.Collectors;

/**
 * 问题反馈 控制器
 *
 * <AUTHOR>
 * @since 2025-02-26
 */
// 标识为REST控制器
@RestController
// 构造函数注入所有依赖
@AllArgsConstructor
// 定义请求路径
@RequestMapping("feedback/feedback")
// Swagger文档注解，说明该控制器的作用
@Api(value = "问题反馈", tags = "问题反馈接口")
// 继承BladeX框架的基础控制器，获得基础功能
public class FeedbackController extends BladeController {

	// 注入问题反馈服务
	private final IFeedbackService feedbackService;

	// 注入问题反馈解决记录服务
	private final IFeedbackSolvingRecordService feedbackSolvingRecordService;

	// 注入用户服务
	private final IUserService userService;

	// 注入Flowable的运行时服务
	private final RuntimeService runtimeService;

	/**
	 * 问题反馈 详情
	 */
	// 定义GET请求路径
	@GetMapping("/detail")
	// Swagger接口排序注解
	@ApiOperationSupport(order = 1)
	// Swagger接口说明注解
	@ApiOperation(value = "详情", notes = "传入feedback")
	// 方法参数为问题反馈实体（用于接收请求参数）
	public R<FeedbackVO> detail(FeedbackEntity feedback) {
		// 调用服务层方法获取问题反馈详情
		FeedbackEntity detail = feedbackService.getOne(Condition.getQueryWrapper(feedback));

		// 检查详情是否为空，如果为空则抛出异常
		Objects.requireNonNull(detail, "问题反馈详情不见了");

		// 查询该问题反馈的所有解决记录
		List<FeedbackSolvingRecordEntity> solvingRecordList = feedbackSolvingRecordService
			.list(new LambdaQueryWrapper<FeedbackSolvingRecordEntity>()
				.eq(FeedbackSolvingRecordEntity::getFeedbackId, detail.getId()));

		// 搜索与问题描述相关的文本
		List<SearchResponseDTO> searchResponseDTOS = feedbackService.searchText(detail.getDescription());

		// 将实体转换为VO（视图对象）
		FeedbackVO feedbackVO = FeedbackWrapper.build().entityVO(detail);

		// 设置解决记录列表到VO中
		feedbackVO.setSolvingRecordList(solvingRecordList);

		// 设置反馈收集次数（搜索结果数量）
		feedbackVO.setFeedbackCollectionCount(searchResponseDTOS.size());

		// 返回封装了VO数据的成功响应
		return R.data(feedbackVO);
	}

	/**
	 * 问题反馈 分页
	 */
	// 定义GET请求路径
	@GetMapping("/list")
	// Swagger接口排序注解
	@ApiOperationSupport(order = 2)
	// Swagger接口说明注解
	@ApiOperation(value = "分页", notes = "传入feedback")
	// 方法参数为问题反馈实体和分页查询对象
	public R<IPage<FeedbackVO>> list(FeedbackEntity feedback, Query query) {
		// 调用服务层方法获取分页数据
		IPage<FeedbackEntity> pages = feedbackService.page(Condition.getPage(query), Condition.getQueryWrapper(feedback));
		// 将实体分页对象转换为VO分页对象并返回
		return R.data(FeedbackWrapper.build().pageVO(pages));
	}

	/**
	 * 问题反馈 自定义分页
	 */
	// 定义GET请求路径
	@GetMapping("/page")
	// Swagger接口排序注解
	@ApiOperationSupport(order = 3)
	// Swagger接口说明注解
	@ApiOperation(value = "分页", notes = "传入feedback")
	// 方法参数包括查询参数、描述、分页对象和数据权限用户ID列表
	public R<IPage<FeedbackVO>> page(FeedbackParams feedbackParams, String description, Query query, @DataPermissions("ni_feedback") List<Long> dataPermissionsUserIdList) {

		// 获取所有用户的数量
		long allUserCount = userService.count();

		// 判断是否查看所有人的数据，如果是，则查询所有数据
		feedbackParams.setCanAllUser(dataPermissionsUserIdList.size() == allUserCount);

		// 将数据权限用户ID列表转换为逗号分隔的字符串
		String userIdsStr = StringUtils.join(dataPermissionsUserIdList, ",");

		// 设置当前登录用户ID到查询参数中
		feedbackParams.setCurrentUserId(AuthUtil.getUserId());

		// 如果指定了问题反馈ID
		if (Objects.nonNull(feedbackParams.getFeedbackId())) {
			// 查询该问题反馈的描述
			FeedbackEntity detail = feedbackService.getOne(new LambdaQueryWrapper<FeedbackEntity>().select(FeedbackEntity::getDescription).eq(FeedbackEntity::getId, feedbackParams.getFeedbackId()));
			// 搜索与描述相关的文本
			List<SearchResponseDTO> searchResponseDTOS = feedbackService.searchText(detail.getDescription());
			// 提取搜索结果中的pointId列表
			List<Integer> pointIds = searchResponseDTOS.stream().map(SearchResponseDTO::getPointId).collect(Collectors.toList());
			// 设置pointId列表到查询参数中
			feedbackParams.setPointIds(pointIds);
		}

		// 调用服务层自定义分页查询方法
		IPage<FeedbackVO> pages = feedbackService.selectFeedbackPage(Condition.getPage(query), feedbackParams, description, userIdsStr);

		// 返回分页数据
		return R.data(pages);
	}

	/**
	 * 问题反馈 新增
	 */
	// 定义POST请求路径
	@PostMapping("/save")
	// Swagger接口排序注解
	@ApiOperationSupport(order = 4)
	// Swagger接口说明注解
	@ApiOperation(value = "新增", notes = "传入feedback")
	// 接收请求体中的问题反馈实体，并进行参数验证
	public R save(@Valid @RequestBody FeedbackEntity feedback) {
		// 调用服务层保存方法，并返回操作结果
		return R.status(feedbackService.save(feedback));
	}

	/**
	 * 问题反馈 修改
	 */
	// 定义POST请求路径
	@PostMapping("/update")
	// Swagger接口排序注解
	@ApiOperationSupport(order = 5)
	// Swagger接口说明注解
	@ApiOperation(value = "修改", notes = "传入feedback")
	// 接收请求体中的问题反馈实体，并进行参数验证
	public R update(@Valid @RequestBody FeedbackEntity feedback) {
		// 调用服务层更新方法，并返回操作结果
		return R.status(feedbackService.updateById(feedback));
	}

	/**
	 * 问题反馈 新增或修改
	 */
	// 定义POST请求路径
	@PostMapping("/submit")
	// Swagger接口排序注解
	@ApiOperationSupport(order = 6)
	// Swagger接口说明注解
	@ApiOperation(value = "新增或修改", notes = "传入feedback")
	// 接收请求体中的问题反馈实体，并进行参数验证
	public R submit(@Valid @RequestBody FeedbackEntity feedback) {
		// 调用服务层保存或更新方法，并返回操作结果
		return R.status(feedbackService.saveOrUpdate(feedback));
	}

	/**
	 * 问题反馈 删除
	 */
	// 定义POST请求路径
	@PostMapping("/remove")
	// Swagger接口排序注解
	@ApiOperationSupport(order = 7)
	// Swagger接口说明注解
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	// 接收请求参数中的ids字符串
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		// 将ids字符串转换为Long列表，并调用服务层逻辑删除方法
		return R.status(feedbackService.deleteLogic(Func.toLongList(ids)));
	}

	// 定义POST请求路径，用于同步数据到Qdrant
	@PostMapping("/asyncQdrant")
	// Swagger接口排序注解
	@ApiOperationSupport(order = 7)
	// Swagger接口说明注解
	@ApiOperation(value = "同步到Qdrant", notes = "传入ids")
	// 接收请求参数中的ids字符串
	public R asyncQdrant(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		// 根据ids查询对应的问题反馈实体列表
		List<FeedbackEntity> feedbackEntities = feedbackService.listByIds(Func.toLongList(ids));

		// 批量执行asyncQdrant方法，并收集CompletableFuture
		List<CompletableFuture<Void>> futures = new ArrayList<>();
		for (FeedbackEntity feedback : feedbackEntities) {
			futures.add(feedbackService.asyncQdrant(feedback));
		}
		// 创建一个新的CompletableFuture，用于等待所有任务完成
		CompletableFuture<Void> allFutures = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));

		// 等待所有任务完成
		allFutures.join();

		// 返回成功响应
		return R.success("执行完成");
	}

}
```

### 代码功能总结

这段代码是一个基于 Spring Boot 和 BladeX 框架的问题反馈模块控制器，主要实现了以下功能：

1. **数据查询**：提供了详情查询和分页查询接口，支持自定义查询条件和数据权限控制
2. **数据操作**：实现了问题反馈的新增、修改、保存或更新、逻辑删除等基本 CRUD 操作
3. **数据同步**：提供了将问题反馈数据同步到 Qdrant 的接口，支持批量异步处理
4. **权限控制**：集成了数据权限注解，控制用户可访问的数据范围
5. **文档支持**：使用 Swagger/knife4j 注解，自动生成 API 文档

代码遵循了 RESTful API 设计规范，使用了 BladeX 框架提供的基础功能，如统一响应格式、分页支持、权限控制等，同时结合 MyBatis-Plus 实现了高效的数据访问操作。异步处理部分使用了 CompletableFuture 来提高同步操作的效率。
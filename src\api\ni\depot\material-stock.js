import request from '@/router/axios';

/**
 * 获取库存列表
 * @param current
 * @param size
 * @param params
 * @returns {AxiosPromise}
 */
export const getStockPage = (current, size, params) => {
  return request({
    url: '/api/ni/depot/material-stock/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getList = (current, size, params) => {
  return request({
    url: '/api/ni/depot/material-stock/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}


export const getPageGroupByMaterial = (current, size, params) => {
  return request({
    url: '/api/ni/depot/material-stock/pageGroupByMaterial',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (materialId) => {
  return request({
    url: '/api/ni/depot/material-stock/detail',
    method: 'get',
    params: {
      materialId
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/ni/depot/material-stock/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/ni/depot/material-stock/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/ni/depot/material-stock/submit',
    method: 'post',
    data: row
  })
}

export const getDetailByDepot = (depotId, materialId) => {
  return request({
    url: '/api/ni/depot/material-stock/detailByDepot',
    method: 'get',
    params: {
      depotId,
      materialId
    }
  })
}

<template>
  <div class="app-container">
    <!-- 操作工具栏 -->
    <div class="toolbar">
      <el-button-group>

      <el-button type="primary"
                 v-if="permission.niEmployeeResumeInfo_add" size="small"
                 @click="batchUploadDialog">批量新增</el-button>
      <el-button type="danger" size="small"
                 v-if="permission.niEmployeeResumeInfo_delete"
                 @click="batchDelete">批量删除</el-button>
      </el-button-group>
      <el-button-group>

      <el-button type="success" size="small"
                 v-if="permission.niEmployeeResumeInfo_add" >待约面试</el-button>
      <el-button type="success" size="small"
                 v-if="permission.niEmployeeResumeInfo_add"
      >待办入职</el-button>
      </el-button-group>

    </div>

    <!-- 数据表格 -->
    <el-table
      :data="tableData"
      border
      style="width: 100%"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" min-width="5%"></el-table-column>
      <el-table-column prop="name" label="姓名" min-width="10%"></el-table-column>
      <el-table-column prop="recommendationLevel" label="推荐等级" min-width="10%"></el-table-column>
      <el-table-column prop="recommendationReason" label="推荐原因" show-overflow-tooltip min-width="10%"></el-table-column>
      <el-table-column prop="summary" label="简历总结" show-overflow-tooltip min-width="15%"></el-table-column>
      <el-table-column prop="aiPrompt" label="提示词" show-overflow-tooltip min-width="15%"></el-table-column>
      <el-table-column prop="position" label="职位" min-width="15%"></el-table-column>
      <el-table-column prop="education" label="学历" min-width="10%"></el-table-column>
      <el-table-column prop="resumeStatus" label="状态" min-width="15%">
        <template slot-scope="scope">
          <el-tag :type="statusType[scope.row.resumeStatus]">
            {{ statusMap[scope.row.resumeStatus] }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="uploadTime" label="上传时间" min-width="15%"></el-table-column>
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-button
            size="mini"
            v-if="scope.row.resumeStatus  < 4"
            @click="reanalysisDialog(scope.row)">重新分析</el-button>
          <el-button
            v-if="scope.row.resumeStatus === 3"
            size="mini"  type="primary"
            @click="inviteOnDeptDialog(scope.row)">约面试</el-button>
          <el-button
            v-if="scope.row.resumeStatus === 4 && permission.niEmployeeResumeInfo_edit"
            size="mini"  type="primary"
            @click="inviteOnHrDialog(scope.row)">邀约信息</el-button>
          <el-button
            v-if="scope.row.resumeStatus === 5"
            size="mini"  type="primary"
            @click="evaluationDialog(scope.row)">录入面评</el-button>
          <el-button
            v-if="scope.row.resumeStatus === 6"
            size="mini"  type="primary"
            @click="entry(scope.row)">办理入职</el-button>
          <el-button
            size="mini"
            @click="details(scope.row)">查看详情</el-button>
          <el-button
            size="mini"
            v-if="permission.niEmployeeResumeInfo_delete"
            type="danger"
            @click="deleteInfo(scope.$index, scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页组件 -->
    <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="pagination.current"
      :page-sizes="[10, 20, 50]"
      :page-size="pagination.size"
      layout="total, sizes, prev, pager, next, jumper"
      :total="pagination.total"
    ></el-pagination>

    <!-- 批量新增对话框 -->
    <el-dialog title="批量新增简历" :visible.sync="batchSaveDialogVisible" width="40%"
               append-to-body @closed="handleDialogClosed">
      <el-form label-width="100px">
        <!-- 部门选择 -->
        <el-form-item label="选择部门">
          <el-select
            v-model="deptList"
            multiple
            placeholder="请选择部门"
            style="width: 100%"
          >
            <el-option
              v-for="item in departments"
              :key="item.id"
              :label="item.label"
              :value="item"
            ></el-option>
          </el-select>
        </el-form-item>


        <el-form-item label="简历筛选条件">
          <el-input
            type="textarea"
            :rows="5"
            placeholder="请输入简历筛选条件"
            v-model="ai_prompt"
          ></el-input>
        </el-form-item>

        <!-- 文件上传 -->
        <el-form-item label="简历附件">
          <el-upload ref="batchUpload"
            action="/api/blade-resource/oss/endpoint/put-file-attach"
            multiple drag
            limit="10"
            :headers="header"
            :file-list="fileList"
            :on-success="handleUploadSuccess"
          >
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">
              将文件拖到此处，或<em>点击上传</em>
              <br>单个文件不超过10MB,单次最多10个文件
            </div>
          </el-upload>
        </el-form-item>
      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button @click="batchSaveDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirmBatchAdd">确 定</el-button>
      </span>
    </el-dialog>

    <el-dialog title="重新分析" :visible.sync="promptDialogVisible"
               append-to-body>
      <el-form>
        <el-form-item label="提示词" >
          <el-input v-model="reanalysisPrompt" type="textarea" autocomplete="off"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="promptDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="reanalysis">确 定</el-button>
      </div>
    </el-dialog>

    <el-dialog title="面试邀请" :visible.sync="inviteDialogVisible"
               @close="handleDialogClosed"
               append-to-body>
      <el-form :model="inviteForm" ref="inviteForm">
        <el-row>
          <el-col :span="12">
            <el-form-item label="候选人" >
              <el-input v-model="inviteForm.name" readonly ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">

            <el-form-item label="岗位" :span="6" >
    <!--          <el-input v-model="inviteForm.position" ></el-input>-->
              <el-select
                v-model="inviteForm.position"
                filterable
                allow-create
                default-first-option
                placeholder="岗位">
                <el-option
                  v-for="item in jobs"
                  :key="item.id"
                  :label="item.postName"
                  :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="isHrInvite">
            <el-form-item label="面试官" >
              <el-select
                v-model="inviteForm.interviewer"
                filterable
                remote
                :remote-method="selectUser"
                :loading="loading"
                placeholder="面试官">
                <el-option
                  v-for="item in employees"
                  :key="item.id"
                  :label="item.account"
                  :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12" >
            <el-form-item label="期望面试日期">
              <el-date-picker
                :readonly="isHrInvite"
                v-model="inviteForm.expectedTime"
                type="date"
                value-format="yyyy-MM-dd HH:mm:ss"
                placeholder="选择日期">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="isHrInvite">
            <el-form-item label="邀请面试日期" >
              <el-date-picker
                v-model="inviteForm.interviewTime"
                type="date"
                value-format="yyyy-MM-dd HH:mm:ss"
                placeholder="选择日期">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="inviteDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="invite">确 定</el-button>
      </div>
    </el-dialog>

    <el-dialog title="面试评价" :visible.sync="evaluationDialogVisible"
               @close="handleDialogClosed"
               append-to-body>
      <el-form :model="evaluationForm" ref="evaluationForm">
        <el-row>
          <el-col :span="12">
            <el-form-item label="面试人" >
              <el-input v-model="evaluationForm.name" readonly ></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="12" >
            <el-form-item label="面试官" >
              <el-input  v-model="evaluationForm.interviewer"  readonly ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12"  >
            <el-form-item label="面试日期" >
              <el-input v-model="evaluationForm.interviewTime" readonly></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="面试岗位"  >
              <el-input v-model="evaluationForm.position" readonly ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
        </el-row>

        <el-form-item label="是否通过面试">
          <el-checkbox v-model="evaluationForm.interviewResult" true-label="1" false-label="2">通过</el-checkbox>
        </el-form-item>
        <el-form-item label="面试评语">
          <el-input type="textarea" v-model="evaluationForm.interviewSummary" autocomplete="false" ></el-input>
        </el-form-item>

        <el-form-item label="扫描件">
          <el-upload ref="singleUpload"
            action="/api/blade-resource/oss/endpoint/put-file-attach"
            drag
            limit="1"
            :headers="header"
            :file-list="fileList"
            :on-success="handleSingleUploadSuccess"
          >
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">
              将文件拖到此处，或<em>点击上传</em>
              <br>单个文件不超过10MB
            </div>
          </el-upload>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="evaluationDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="evaluation">确 定</el-button>
      </div>
    </el-dialog>

    <el-dialog title="详情" :visible.sync="detailDialogVisible" append-to-body >
      <el-main>
        <!-- 基础信息 -->
        <el-card class="base-info">
          <div class="info-group">
            <h3>候选人信息</h3>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form label-width="120px">
                  <el-form-item label="姓名："> {{ detailInfo.name || '暂无'}}</el-form-item>
                  <el-form-item label="意向岗位："> {{ detailInfo.position||'暂无'}} </el-form-item>
                  <el-form-item label="最高学历："> {{ detailInfo.education || '暂无' }}</el-form-item>
                  <el-form-item label="工作年限："> {{ resumeInfo.workingAge || '暂无' }}</el-form-item>

                </el-form>
              </el-col>
              <el-col :span="12">
                <el-form label-width="120px">
                  <el-form-item label="年龄：">{{ detailInfo.age||'暂无'}}</el-form-item>
                  <el-form-item label="联系方式："> {{ detailInfo.phone || '暂无'}} </el-form-item>
                  <el-form-item label="毕业院校">{{ resumeInfo.school || '暂无'}}</el-form-item>
                  <el-form-item label="投递渠道：">{{ detailInfo.recruitmentChannels === 0 ? '社招' : '校招'}}</el-form-item>
                </el-form>
              </el-col>
            </el-row>
          </div>
        </el-card>

        <!-- 解析信息 -->
        <el-card class="analysis-section">
          <h3>简历分析结果</h3>
          <el-collapse >
            <el-collapse-item title="推荐面试题" name="1">

              <div class="project-notes">
                <h4>{{ parseResumeInfo.examQuestions || '暂无'}}</h4>
                <p></p>
              </div>
            </el-collapse-item>
            <!-- 分析结果 -->
            <el-collapse-item title="能力评估" name="2">
              <el-row :gutter="20">
                <el-col :span="12">
                  <div class="score-item">专业匹配度：{{analysisInfo.major_match_score || '暂无'}}</div>
                  <div class="score-item">关键词匹配度：{{ analysisInfo.keyword_match_score || '暂无'}}</div>
                  <div class="score-item">项目相关性：{{ analysisInfo.project_relevance_score || '暂无'}}</div>
                </el-col>
                <el-col :span="12">
                  <div class="score-item">综合能力：{{ analysisInfo.comprehensive_ability_score || '暂无'}}</div>
                  <div class="score-item">成长潜力：{{ analysisInfo.growth_potential_score || '暂无'}}</div>
                </el-col>
              </el-row>
              <div class="project-notes">
                <h4>关键词：{{ analysisInfo.keywords || '暂无'}}</h4>
                <p></p>
              </div>
              <div class="project-notes">
                <h4>项目说明：{{ analysisInfo.project_relevance_notes || '暂无'}}</h4>
                <p></p>
              </div>
            </el-collapse-item>
          </el-collapse>
        </el-card>

        <!-- 面试安排 -->
        <el-card class="interview-section">
          <h3>面试安排</h3>
          <el-table
            :data="detailInfo.interviews"
            style="width: 100%"
            empty-text="暂无面试安排">
            <el-table-column prop="interviewTime" label="面试时间">
              <template slot-scope="{row}">
                {{(row.interviewTime != null && row.interviewTime.length>10) ? row.interviewTime.substring(0,10) : ''}}
<!--                {{row.interviewTime.length > 10 ?row.interviewTime.substring(0,10) : '' }}-->
              </template>
            </el-table-column>
            <el-table-column prop="position" label="面试岗位">
              <template slot-scope="{row}">
               {{  displayPosition(row.position) }}
              </template>
            </el-table-column>
            <el-table-column prop="interviewer" label="面试官"></el-table-column>
            <el-table-column prop="interviewResult" label="面试结果">
                <template slot-scope="{row}">
                  <p v-if="row.interviewResult === 1 ">通过</p>
                  <p v-else-if="row.interviewResult === 2 ">未通过</p>
                  <p v-else>未面试</p>

                </template>
            </el-table-column>
            <el-table-column prop="interviewSummary" label="面试评价"></el-table-column>
            <el-table-column prop="attachmentUrl" label="扫描件">
              <template slot-scope="{row}">
                <el-button
                  v-if="row.attachmentUrl"
                  @click="downloadFileBlob(row.attachmentUrl,'面评.png')">下载</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-main>
    </el-dialog>
  </div>
</template>

<script>
import {getAuthorizationHeader} from "@/api/resource/fileManagement";
import * as api from "@/api/ni/emhire/niEmployeeResumeInfo";
import {downloadFileBlob} from "@/util/util";
import {mapGetters} from "vuex";
export default {
  data() {
    return {
      singleUpload:{},
      batchUpload:{},
      // 新增表单
      ai_prompt: '', // AI提示词
      fileList: [], // 上传文件列表
      deptList:[],
      departments: [],
      employees:[],
      jobs:[],
      jobsMap:{},
      loading: false,

      header: getAuthorizationHeader(),
      batchSaveDialogVisible: false, // 批量新增对话框显示状态

      tableData: [], // 表格数据
      multipleSelection: [], // 选中的行

      // 邀请面试
      isHrInvite: false,
      inviteDialogVisible: false,
      inviteForm:{
        id:'',
        name:'',
        position:"",
        interviewer:'',
        expectedTime : null,
        interviewTime:null
      },

      evaluationDialogVisible:false,
      evaluationForm:{
        resumeId:'',
        resumeInterviewId:'',
        name:'',
        position:"",
        interviewer:'',
        expectedTime : null,
        interviewTime:null,
        interviewSummary:'',
        interviewResult:1,
        attachmentId:'',
        attachmentUrl:''
      },


      detailDialogVisible:false,
      detailInfo:{
        interviews:[]
      },

      promptDialogVisible: false, // 提示词对话框显示状态
      reanalysisPrompt:"", //重新分析用提示词
      // 分页配置
      pagination: {
        current: 1,
        size: 10,
        total: 0
      },
      // 状态映射
      statusMap: {
        1: '待分析',
        2: '分析失败',
        3: '可约面试',
        4: '部门邀约',
        5: '待面试',
        6: '已评价',
        7: '通过'
      },
      statusType: {
        1: 'info',
        2: 'danger',
        3: 'info',
        4: 'warning',
        5: 'warning',
        6: 'warning',
        7: 'success',
      }
    }
  },
  computed:{
    ...mapGetters(["permission"]),
    parseResumeInfo(){
      return this.detailInfo.parseResumeResultVO ||{};
    },
    resumeInfo(){
      return  this.parseResumeInfo.resumeInfo||{};
    },
    analysisInfo(){
      return this.parseResumeInfo.analysisRepo ||{};
    }
  },
  created() {

    console.info(this.permission)
    this.$http.get("/api/ni/oa/oaDailyTask/select/deps").then((res) => {
      const data = res.data.data;
      this.departments = data.map(item => {return { value: item.id, label: item.deptName }});
    });
    api.getJobs().then(res => {
      this.jobs = res.data.data;
      this.jobsMap = new Map(
        this.jobs.map(object => {
          return [object.id, object.postName];
        })
      );
    })
  },
  methods: {
    downloadFileBlob,
    // 办理入职
    entry(row){
      this.$message.info("办理入职");

    },
    selectUser(query) {
      if (query !== '' && query.trim() !== '') {
        this.loading = true;
        api.filterUser(query).then(res=>{
          this.loading = false;
          this.employees = res.data.data.records;
        });
      } else {
        this.options = [];
      }
    },
    displayPosition(positionId){
      if (positionId){
        return this.jobsMap.get(positionId)
      }
    },
    // 重新分析弹框显示
    reanalysisDialog(row){
      this.reanalysisPrompt = row.aiPrompt;
      this.promptDialogVisible = true;
    },
    // 重新分析
    reanalysis(){
      this.$message.warning('建设中')
    },
    inviteOnDeptDialog(row){
      this.isHrInvite = false;
      this.inviteDialogVisible = true;
      this.inviteForm.id = row.id;
    },
    // 录入面试邀请信息
    inviteOnHrDialog(row){
      this.inviteForm.id = row.id;
      this.isHrInvite = true;
      this.inviteDialogVisible = true;
      api.inviteRecord(row.id).then((res)=>{
        if (res.data.data.length > 0){
          let lastRecord = res.data.data[res.data.data.length - 1];
          this.employees =[{id:lastRecord.interviewerId,account:lastRecord.interviewer}]
          this.inviteForm.position = lastRecord.position;
          this.inviteForm.interviewTime = lastRecord.interviewTime;
          this.inviteForm.expectedTime = lastRecord.expectedTime
          this.inviteForm.interviewer = lastRecord.interviewerId;
          this.inviteForm.recordId= lastRecord.id
        }
      });
    },
    //发送面试邀请
    invite(){
      if (this.isHrInvite){
        // HR
        api.invite(this.inviteForm).then(res=>{
          this.$message.success(res.data.msg);
          this.inviteDialogVisible = false;
          this.fetchData();
        })
      }else{
        api.interested(this.inviteForm).then((res)=>{
          this.$message.success(res.data.msg);
          this.inviteDialogVisible = false;
          this.fetchData();
        });
      }
    },
    evaluationDialog(row){
        this.evaluationDialogVisible = true;
        api.inviteRecord(row.id).then((res)=> {
          // 当前存在问题 面试记录一对多问题 后续迭代一对多记录
          if (res.data.data.length > 0) {
            let lastRecord = res.data.data[res.data.data.length - 1];
            this.evaluationForm.position = this.jobs.findLast(j=>j.id === lastRecord.position).postName;
            if (lastRecord.interviewTime && lastRecord.interviewTime.length > 10){
              this.evaluationForm.interviewTime = lastRecord.interviewTime.substring(0,10);
            }
            this.evaluationForm.interviewer = lastRecord.interviewer;
            this.evaluationForm.resumeInterviewId = lastRecord.id
            this.evaluationForm.resumeId = row.id
            if (lastRecord.attachmentId){
              this.fileList =[{
                uid:lastRecord.attachmentId,
                status:'success',
                url:lastRecord.attachmentUrl,
                name:'附件'
              }]
            }
          }
        });
    },
    // 录入面试评价
    evaluation(){
      console.info(this.fileList)
      if (this.fileList.length > 0){
        this.evaluationForm.attachmentId = this.fileList[0].response.data.attachId;
        this.evaluationForm.attachmentUrl = this.fileList[0].response.data.link;
      }
      api.evaluation(this.evaluationForm).then(res=>{
        this.$message.success(res.data.msg);
        this.evaluationDialogVisible = false;
        this.fetchData()
      });
      // update
    },
    // 查看详情
    details(row){
      this.detailDialogVisible = true;
      api.getDetail(row.id).then((res)=>{
        console.info(res)
        this.detailInfo = res.data.data;
      })
    },
    deleteInfo(row){
      this.$confirm('确认删除选中的记录吗？', '提示', {
        type: 'warning'
      }).then(() => {
        api.remove(row.id).then(res=>{
          this.$message.success('删除成功')
          this.fetchData();
        });
      })
    },
    // 显示批量新增对话框
    batchUploadDialog() {
      this.batchSaveDialogVisible = true
      this.handleDialogClosed();
    },
    handleDialogClosed() {
      this.deptList = []    // 清空部门选择
      this.ai_prompt = ''                 // 清空备注
      this.fileList = []               // 清空文件列表
      this.inviteForm = Object.assign({}, this.$options.data().inviteForm)
      this.evaluationForm = Object.assign({}, this.$options.data().evaluationForm)
    },
    // 批量删除
    batchDelete() {
      if (this.multipleSelection.length === 0) {
        this.$message.warning('请至少选择一条记录')
        return
      }
      this.$confirm('确认删除选中的记录吗？', '提示', {
        type: 'warning'
      }).then(() => {
        api.remove(this.multipleSelection.map(r=>r.id).join(",")).then(res=>{
          this.$message.success('删除成功')
          this.fetchData();
        });
      })
    },
    // 确认批量新增
    confirmBatchAdd() {
      let resumeInfo = {
        aiPrompt: this.ai_prompt,
        skipExamination: 0,
        channels: 0,
        departments: this.deptList.map(item => {return {left:item.value,right:item.label }}),
        files: this.fileList.map(item => {
          return {link:item.response.data.link,attachId:item.response.data.attachId }
        }),
      }
      api.batchUpload(resumeInfo)
        .then(res => {
          console.info(res)
          this.batchSaveDialogVisible = false
          this.fetchData()
        })
    },
    // 处理文件上传成功
    handleUploadSuccess(response, file, fileList) {
      this.fileList = fileList
    },
    handleSingleUploadSuccess(resp,file,allFile){
      this.fileList = allFile;
      this.$refs.singleUpload.clearFiles();
    },
    // 处理分页大小变化
    handleSizeChange(size) {
      this.pagination.size = size
      this.fetchData()
    },
    // 处理页码变化
    handleCurrentChange(current) {
      this.pagination.current = current
      this.fetchData()
    },
    // 获取表格数据
    fetchData() {
      api.page(this.pagination.current,this.pagination.size,{}).then((res)=>{
        this.tableData = res.data.data.records;
        this.pagination.current = res.data.data.current;
        this.pagination.total = res.data.data.total;
      });
    },

    // 处理表格选择
    handleSelectionChange(selection) {
      this.multipleSelection = selection
    }
  },

  mounted() {
    this.fetchData()
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}
.toolbar {
  margin-bottom: 20px;
  padding: 10px;
}
.el-pagination {
  margin-top: 20px;
  text-align: right;
}
.base-info {
  margin-bottom: 20px;
}
.analysis-section {
  margin-bottom: 20px;
}
.interview-section {
  margin-top: 20px;
}
.score-item {
  margin: 10px 0;
  font-size: 16px;
}
.project-notes {
  margin-top: 15px;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 4px;
}
</style>

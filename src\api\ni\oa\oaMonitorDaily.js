import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/ni/oa/oaMonitorDaily/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getPage = (current, size, params) => {
    return request({
      url: '/api/ni/oa/oaMonitorDaily/page',
      method: 'get',
      params: {
        ...params,
        current,
        size,
    }
  })
}



export const getDetail = (id) => {
  return request({
    url: '/api/ni/oa/oaMonitorDaily/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const add = (row) => {
    return request({
      url: '/api/ni/oa/oaMonitorDaily/save',
      method: 'post',
      data: row
    })
  }

  export const remove = (ids) => {
    return request({
      url: '/api/ni/oa/oaMonitorDaily/remove',
      method: 'post',
      params: {
        ids,
      }
    })
  }




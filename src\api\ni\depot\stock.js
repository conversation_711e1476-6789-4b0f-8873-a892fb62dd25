import request from "@/router/axios";

export const listWithDepot = (params) => {
  return request({
    url: "/api/ni/depot/stock/listWithDepot",
    method: "get",
    params: {
      ...params,
    },
  });
};

export const getDetail = (params) => {
  return request({
    url: "/api/ni/depot/stock/detail",
    method: "get",
    params,
  });
};
export const getStockHistory = (current, size, params) => {
  return request({
    url: "/api/ni/depot/stock/getStockHistory",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};
export const getStockInHistory = (current, size, params) => {
  return request({
    url: "/api/ni/depot/stock/getStockInHistory",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};
export const getStockPvHistory = (current, size, params) => {
  return request({
    url: "/api/ni/depot/stock/pv/getStockHistory",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};
export const currentStock = (materialId, projectId, depotId) => {
  return request({
    url: "/api/ni/depot/stock/currentStock",
    method: "get",
    params: {
      materialId,
      projectId,
      depotId,
    },
  });
};

export const projectStock = (materialId, depotId, time) => {
  return request({
    url: "/api/ni/depot/stock/projectStock",
    method: "get",
    params: {
      materialId,
      depotId,
      time,
    },
  });
};
/**
 * 获取库存列表
 * @param current
 * @param size
 * @param params
 * @returns {AxiosPromise}
 */
export const stockPage = (current, size, params) => {
  return request({
    url: "/api/ni/depot/stock/page",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};
/**
 * 获取库存列表
 * @param current
 * @param size
 * @param params
 * @returns {AxiosPromise}
 */
export const projectStockPage = (current, size, params) => {
  return request({
    url: "/api/ni/depot/stock/projectStockPage",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};
export const budgetStockPage = (current, size, params) => {
  return request({
    url: "/api/ni/depot/stock/budgetStockPage",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};
/**
 * 获取项目和年度预算的库存列表
 * @param current
 * @param size
 * @param params
 * @returns {*}
 */
export const stockUnionPage = (current, size, params) => {
  return request({
    url: "/api/ni/depot/stock/stockPage",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const changeRemark = (remarkId, remark) => {
  return request({
    url: "/api/ni/depot/stock/changeRemark",
    method: "post",
    params: {
      remarkId,
      remark,
    },
  });
};

export const changeLocation = (data) => {
  return request({
    url: "/api/ni/depot/stock/changeLocation",
    method: "post",
    data,
  });
};
export const changeLocationLite = (data) => {
  return request({
    url: "/api/ni/depot/stock/v1/changeLocation",
    method: "post",
    data,
  });
};
export const introductionStockPage = (current, size, params) => {
  return request({
    url: "/api/ni/depot/stock/introduction/stockPage",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const stockList = (params) => {
  return request({
    url: "/api/ni/depot/stock/stockList",
    method: "get",
    params,
  });
};


export const changeDate = (params) => {
    return request({
        url: "/api/ni/depot/stock/changeDate",
        method: "post",
        params,
    });
}

export const getDepotBill = (data) => {
  return request({
    url: "/api/ni/depot/stock/getDepotBill",
    method: "post",
    data,
  });
};

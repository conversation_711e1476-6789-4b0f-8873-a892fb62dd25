<template>
  <basic-container>
    <vxe-toolbar ref="toolbarRef" size="mini" custom>
      <template #buttons>
        <el-radio-group v-model="dataType" size="mini" @input="dataTypeChange">
          <el-radio-button label="1">本人</el-radio-button>
          <el-radio-button label="2">全部</el-radio-button>
        </el-radio-group>
        <el-divider direction="vertical" />
        <vxe-button
          size="mini"
          status="info"
          content="清空"
          @click="handleClear"
        ></vxe-button>
        <vxe-button
          size="mini"
          status="primary"
          content="导 出"
          @click="handleExcel"
        ></vxe-button>
        <vxe-button status="success" size="mini" @click="handleArrival"
          >生成到货单
        </vxe-button>
        <vxe-button status="info" size="mini" @click="handleCancel"
          >取 消
        </vxe-button>
        <vxe-button status="warning" size="mini" @click="handleManufacturer"
          >设置制造商
        </vxe-button>
        <el-divider direction="vertical" />
        <el-tag>
          当前表格已选择
          <span style="font-weight: bolder">{{ selectionList.length }}</span> 项
          <el-button type="text" size="mini" @click="handleSelectionClear">
            清空
          </el-button>
          <template v-if="selectionList.length > 0">
            选中金额:
            <span style="font-weight: bolder; color: #f56c6c">
              {{
                amount.toLocaleString("zh-CN", {
                  minimumFractionDigits: 2,
                })
              }}
            </span>
          </template>
        </el-tag>
        <el-divider direction="vertical" />
        <el-checkbox v-model="pv">压力容器</el-checkbox>
        <el-checkbox v-model="crash">紧急申购</el-checkbox>
        <el-divider direction="vertical" />
        <el-tooltip
          class="item"
          effect="dark"
          content="支付宝付款客户可以多供应商合并生成到货单，到货单供应商默认是支付宝"
          placement="top-start"
        >
          <i class="el-icon-warning-outline"></i>
        </el-tooltip>
      </template>
      <template #tools>
        <vxe-button
          type="text"
          icon="vxe-icon-refresh"
          class="tool-btn"
          @click="onLoad"
        ></vxe-button>
      </template>
    </vxe-toolbar>
    <vxe-table
      border
      stripe
      show-overflow="tooltip"
      row-id="id"
      ref="table"
      :height="tableHeight"
      :cell-class-name="cellStyle"
      :filter-config="{ showIcon: false }"
      :mouse-config="{ selected: true }"
      :row-config="{ isCurrent: true, isHover: true }"
      :sort-config="{
        trigger: 'cell',
        multiple: true,
      }"
      :column-config="{ resizable: true }"
      :checkbox-config="{ checkField: 'checked', trigger: 'row' }"
      :edit-config="{ trigger: 'dblclick', mode: 'cell' }"
      :keyboard-config="{
        isArrow: true,
        isDel: true,
        isEnter: true,
        isTab: true,
        isEdit: true,
      }"
      :scroll-x="{ enabled: true }"
      :scroll-y="{ enabled: true }"
      @checkbox-change="handleSelectionChange"
      @checkbox-all="handleSelectionChange"
    >
      <vxe-column type="checkbox" width="55" />
      <vxe-colgroup title="序号">
        <vxe-column
          field="row"
          width="80"
          :filters="[{ data: '' }]"
          :filter-method="customStringFilterMethod"
        >
          <template #header="{ column }">
            <vxe-input
              size="mini"
              type="text"
              v-for="(option, index) in column.filters"
              :key="index"
              v-model="option.data"
              @change="confirmFilterEvent(option)"
              placeholder=" "
              clearable
            ></vxe-input>
          </template>
        </vxe-column>
      </vxe-colgroup>
      <vxe-colgroup title="采购人">
        <vxe-column
          field="purchaseUserName"
          width="85"
          :filters="[{ data: '' }]"
          :filter-method="customStringFilterMethod"
        >
          <template #header="{ column }">
            <vxe-input
              size="mini"
              type="text"
              v-for="(option, index) in column.filters"
              :key="index"
              v-model="option.data"
              @change="confirmFilterEvent(option)"
              placeholder=" "
              clearable
            ></vxe-input>
          </template>
        </vxe-column>
      </vxe-colgroup>
      <vxe-colgroup title="品名">
        <vxe-column
          field="materialName"
          width="105"
          :filters="[{ data: '' }]"
          :filter-method="customStringFilterMethod"
        >
          <template #header="{ column }">
            <vxe-input
              size="mini"
              type="text"
              v-for="(option, index) in column.filters"
              :key="index"
              v-model="option.data"
              @change="confirmFilterEvent(option)"
              placeholder=" "
              clearable
            ></vxe-input>
          </template>
          <template #default="{ row, rowIndex }">
            <span
              :style="{
                color: colorName,
                cursor: 'pointer',
                textDecoration: 'underline',
              }"
              @click="showHistoryDialog(row)"
              >{{ row.materialName }}</span
            >
          </template>
        </vxe-column>
      </vxe-colgroup>
      <vxe-colgroup title="规格">
        <vxe-column
          field="specification"
          width="105"
          :filters="[{ data: '' }]"
          :filter-method="customStringFilterMethod"
        >
          <template #header="{ column }">
            <vxe-input
              size="mini"
              type="text"
              v-for="(option, index) in column.filters"
              :key="index"
              v-model="option.data"
              @change="confirmFilterEvent(option)"
              placeholder=" "
              clearable
            ></vxe-input>
          </template>
        </vxe-column>
      </vxe-colgroup>
      <vxe-colgroup title="材质">
        <vxe-column
          field="quality"
          show-overflow="tooltip"
          width="105"
          :filters="[{ data: '' }]"
          :filter-method="customStringFilterMethod"
        >
          <template #header="{ column }">
            <vxe-input
              size="mini"
              type="text"
              v-for="(option, index) in column.filters"
              :key="index"
              v-model="option.data"
              @change="confirmFilterEvent(option)"
              placeholder=" "
              clearable
            ></vxe-input>
          </template>
        </vxe-column>
      </vxe-colgroup>
      <vxe-colgroup title="数量">
        <vxe-column
          field="num"
          title="数量"
          show-overflow="tooltip"
          width="85"
          :filters="[{ data: '' }]"
          :filter-method="customEqualFilterMethod"
        >
          <template #header="{ column }">
            <vxe-input
              size="mini"
              type="text"
              v-for="(option, index) in column.filters"
              :key="index"
              v-model="option.data"
              @change="confirmFilterEvent(option)"
              placeholder=" "
              clearable
            ></vxe-input>
          </template>
        </vxe-column>
      </vxe-colgroup>
      <vxe-column
        field="unit"
        title="单位"
        :formatter="({ cellValue }) => unitDictKeyValue[cellValue]"
        show-overflow="tooltip"
        width="60"
      ></vxe-column>
      <vxe-column
        field="price"
        show-overflow="tooltip"
        width="95"
        :edit-render="{ autofocus: '.vxe-input--inner' }"
        :filters="[{ data: '' }]"
        :filter-method="
          ({ option, row }) => {
            if (option.data === 1) {
              return Number(row.price) !== 0;
            } else if (option.data === 2) {
              return Number(row.price) === 0 || !row.price;
            }
            return true;
          }
        "
      >
        <template #header="{ column }">
          单价
          <vxe-select
            size="mini"
            v-for="(option, index) in column.filters"
            :key="index"
            v-model="option.data"
            @change="confirmFilterEvent(option)"
            :options="[
              { label: '不是0元', value: 1 },
              { label: '0元或未填', value: 2 },
            ]"
            placeholder=" "
            clearable
            transfer
          >
          </vxe-select>
        </template>
        <template #edit="scope">
          <vxe-input
            v-model="scope.row.price"
            type="number"
            size="mini"
            placeholder=" "
            :disabled="
              (['1', '4'].includes(scope.row.payType) &&
                ['1'].includes(scope.row.payState)) ||
              (['1', '3'].includes(scope.row.payType) && scope.row.soa === 1)
            "
            @blur="rowPriceChangeSubmit($event, scope.row)"
          ></vxe-input>
        </template>
      </vxe-column>
      <vxe-column
        field="amount"
        show-overflow="tooltip"
        :edit-render="{ autofocus: '.vxe-input--inner' }"
        :filters="[{ data: '' }]"
        :filter-method="
          ({ option, row }) => Number(row.amount) === Number(option.data)
        "
        width="95"
      >
        <template #header="{ column }">
          金额
          <vxe-input
            size="mini"
            type="text"
            v-for="(option, index) in column.filters"
            :key="index"
            v-model="option.data"
            @change="confirmFilterEvent(option)"
            placeholder=" "
            clearable
          ></vxe-input>
        </template>
        <template #edit="scope">
          <vxe-input
            v-model="scope.row.amount"
            type="number"
            size="mini"
            className="amount-input"
            placeholder=" "
            :disabled="
              (['1', '4'].includes(scope.row.payType) &&
                ['1'].includes(scope.row.payState)) ||
              (['1', '3'].includes(scope.row.payType) && scope.row.soa === 1)
            "
            @blur="rowAmountChangeSubmit($event, scope.row)"
          />
        </template>
      </vxe-column>
      <vxe-colgroup title="申请备注">
        <vxe-column
          field="applyRemark"
          show-overflow="tooltip"
          width="105"
          :filters="[{ data: '' }]"
          :filter-method="customStringFilterMethod"
        >
          <template #header="{ column }">
            <vxe-input
              size="mini"
              type="text"
              v-for="(option, index) in column.filters"
              :key="index"
              v-model="option.data"
              @change="confirmFilterEvent(option)"
              placeholder=" "
              clearable
            ></vxe-input>
          </template>
        </vxe-column>
      </vxe-colgroup>
      <vxe-column
        field="applyAttachNum"
        title="申请附件"
        show-overflow="tooltip"
        width="80"
      >
        <template #default="{ row, rowIndex }">
          <el-link type="primary" target="_blank" @click="rowAttach(row)">
            附件({{ row.applyAttachNum }})
          </el-link>
        </template>
      </vxe-column>
      <vxe-colgroup title="用途">
        <vxe-column
          field="purpose"
          show-overflow="tooltip"
          width="105"
          :filters="[{ data: '' }]"
          :filter-method="customStringFilterMethod"
        >
          <template #header="{ column }">
            <vxe-input
              size="mini"
              type="text"
              v-for="(option, index) in column.filters"
              :key="index"
              v-model="option.data"
              @change="confirmFilterEvent(option)"
              placeholder=" "
              clearable
            ></vxe-input>
          </template>
        </vxe-column>
      </vxe-colgroup>
      <vxe-colgroup title="供应商">
        <vxe-column
          field="supplier"
          show-overflow="tooltip"
          width="110"
          :filters="[{ data: '' }]"
          sortable
          :filter-method="customStringFilterMethod"
        >
          <template #header="{ column }">
            <vxe-input
              size="mini"
              type="text"
              v-for="(option, index) in column.filters"
              :key="index"
              v-model="option.data"
              @change="confirmFilterEvent(option)"
              placeholder=" "
              clearable
            ></vxe-input>
          </template>
        </vxe-column>
      </vxe-colgroup>
      <vxe-colgroup title="申请人">
        <vxe-column
          field="applyUserName"
          show-overflow="tooltip"
          width="84"
          :filters="[{ data: '' }]"
          :filter-method="customStringFilterMethod"
        >
          <template #header="{ column }">
            <vxe-input
              size="mini"
              type="text"
              v-for="(option, index) in column.filters"
              :key="index"
              v-model="option.data"
              @change="confirmFilterEvent(option)"
              placeholder=" "
              clearable
            ></vxe-input>
          </template>
        </vxe-column>
      </vxe-colgroup>
      <vxe-column
        field="receivingAddress"
        show-overflow="tooltip"
        min-width="85"
        :formatter="({ cellValue }) => receivingAddressDictKeyValue[cellValue]"
        :filters="[{ data: '' }]"
        :filter-method="customEqualFilterMethod"
        :edit-render="{}"
      >
        <template #header="{ column }">
          到货地
          <vxe-select
            size="mini"
            v-for="(option, index) in column.filters"
            :key="index"
            v-model="option.data"
            @change="confirmFilterEvent(option)"
            :options="receivingAddressDict"
            placeholder=" "
            clearable
            transfer
          >
          </vxe-select>
        </template>
        <template #edit="{ row }">
          <vxe-select
            size="mini"
            clearable
            transfer
            v-model="row.receivingAddress"
            @change="rowReceivingAddress($event, row)"
          >
            <vxe-option
              v-for="(item, index) in receivingAddressDict"
              :key="index"
              :value="item.value"
              :label="item.label"
            ></vxe-option>
          </vxe-select>
        </template>
      </vxe-column>
      <vxe-column
        field="serialNo"
        title="订单号"
        show-overflow="tooltip"
        sortable
        width="110"
      ></vxe-column>

      <vxe-colgroup title="暂存位置">
        <vxe-column
          field="depotLocation"
          show-overflow="tooltip"
          width="100"
          :filters="[{ data: '' }]"
          :filter-method="customStringFilterMethod"
        >
          <template #header="{ column }">
            <vxe-input
              size="mini"
              type="text"
              v-for="(option, index) in column.filters"
              :key="index"
              v-model="option.data"
              @change="confirmFilterEvent(option)"
              placeholder=" "
              clearable
            ></vxe-input>
          </template>
        </vxe-column>
      </vxe-colgroup>
      <vxe-colgroup title="暂存备注">
        <vxe-column
          field="receiveRemark"
          show-overflow="tooltip"
          width="100"
          :filters="[{ data: '' }]"
          :filter-method="customStringFilterMethod"
        >
          <template #header="{ column }">
            <vxe-input
              size="mini"
              type="text"
              v-for="(option, index) in column.filters"
              :key="index"
              v-model="option.data"
              @change="confirmFilterEvent(option)"
              placeholder=" "
              clearable
            ></vxe-input>
          </template>
        </vxe-column>
      </vxe-colgroup>
      <vxe-colgroup title="需用日期">
        <vxe-column
          field="needDate"
          show-overflow="tooltip"
          width="125"
          :filters="[{ data: '' }]"
          :filter-method="customEqualFilterMethod"
        >
          <template #header="{ column }">
            <vxe-input
              size="mini"
              type="date"
              v-for="(option, index) in column.filters"
              :key="index"
              v-model="option.data"
              @change="confirmFilterEvent(option)"
              placeholder=" "
              clearable
              transfer
            ></vxe-input>
          </template>
        </vxe-column>
      </vxe-colgroup>
      <vxe-colgroup title="备注">
        <vxe-column
          field="remark"
          show-overflow="tooltip"
          :filters="[{ data: '' }]"
          :filter-method="customStringFilterMethod"
          width="110"
        >
          <template #header="{ column }">
            <vxe-input
              size="mini"
              type="text"
              v-for="(option, index) in column.filters"
              :key="index"
              v-model="option.data"
              @change="confirmFilterEvent(option)"
              placeholder=" "
              clearable
            ></vxe-input>
          </template>
        </vxe-column>
      </vxe-colgroup>
      <vxe-colgroup title="关联合同">
        <vxe-column
          field="contractSerialNo"
          show-overflow="tooltip"
          width="120"
          :filters="[{ data: '' }]"
          :filter-method="customStringFilterMethod"
        >
          <template #header="{ column }">
            <vxe-input
              size="mini"
              type="text"
              v-for="(option, index) in column.filters"
              :key="index"
              v-model="option.data"
              @change="confirmFilterEvent(option)"
              placeholder=" "
              clearable
            ></vxe-input>
          </template>
        </vxe-column>
      </vxe-colgroup>
      <vxe-colgroup title="发票类型">
        <vxe-column
          field="billType"
          show-overflow="tooltip"
          width="85"
          :formatter="({ cellValue }) => billTypeDictKeyValue[cellValue]"
          :filters="[{ data: '' }]"
          :filter-method="customEqualFilterMethod"
        >
          <template #header="{ column }">
            <vxe-select
              size="mini"
              v-for="(option, index) in column.filters"
              :key="index"
              v-model="option.data"
              @change="confirmFilterEvent(option)"
              :options="billTypeDict"
              placeholder=" "
              clearable
              transfer
            >
            </vxe-select>
          </template>
        </vxe-column>
      </vxe-colgroup>
      <vxe-colgroup title="类型">
        <vxe-column
          field="cost"
          show-overflow="tooltip"
          width="70"
          :filters="[{ data: '' }]"
          :filter-method="customEqualFilterMethod"
        >
          <template #header="{ column }">
            <vxe-select
              size="mini"
              v-for="(option, index) in column.filters"
              :key="index"
              v-model="option.data"
              @change="confirmFilterEvent(option)"
              :options="[
                { label: '费用', value: true },
                { label: '实物', value: false },
              ]"
              placeholder=" "
              clearable
              transfer
            >
            </vxe-select>
          </template>
          <template #default="{ row, rowIndex }">
            <el-tag size="mini" type="danger" effect="dark" v-if="row.cost">
              费用
            </el-tag>
            <el-tag size="mini" type="info" effect="plain" v-else> 实物</el-tag>
          </template>
        </vxe-column>
      </vxe-colgroup>
      <vxe-colgroup title="账套">
        <vxe-column
          field="brand"
          show-overflow="tooltip"
          width="70"
          :filters="[{ data: '' }]"
          :filter-method="customEqualFilterMethod"
        >
          <template #header="{ column }">
            <vxe-select
              size="mini"
              v-for="(option, index) in column.filters"
              :key="index"
              v-model="option.data"
              @change="confirmFilterEvent(option)"
              :options="brandDict"
              placeholder=" "
              clearable
              transfer
            >
            </vxe-select>
          </template>
          <template #default="{ row, rowIndex }">
            <dict-tag size="mini" v-model="row.brand" :dict="brandDict" />
          </template>
        </vxe-column>
      </vxe-colgroup>
      <vxe-colgroup title="编码">
        <vxe-column
          field="materialCode"
          show-overflow="tooltip"
          width="120"
          :filters="[{ data: '' }]"
          :filter-method="customStringFilterMethod"
        >
          <template #header="{ column }">
            <vxe-input
              size="mini"
              type="text"
              v-for="(option, index) in column.filters"
              :key="index"
              v-model="option.data"
              @change="confirmFilterEvent(option)"
              placeholder=" "
              clearable
            ></vxe-input>
          </template>
        </vxe-column>
      </vxe-colgroup>
      <vxe-colgroup title="压力容器">
        <vxe-column
          field="pv"
          show-overflow="title"
          width="80"
          :filters="[{ data: '' }]"
          :filter-method="customEqualFilterMethod"
        >
          <template #header="{ column }">
            <vxe-select
              size="mini"
              v-for="(option, index) in column.filters"
              :key="index"
              v-model="option.data"
              @change="confirmFilterEvent(option)"
              :options="[
                { label: '是', value: true },
                { label: '否', value: false },
              ]"
              placeholder=" "
              clearable
              transfer
            >
            </vxe-select>
          </template>
          <template #default="{ row, rowIndex }">
            <el-tag size="mini" type="danger" effect="dark" v-if="row.pv">
              是
            </el-tag>
            <el-tag size="mini" type="info" effect="plain" v-else> 否</el-tag>
          </template>
        </vxe-column>
      </vxe-colgroup>
      <vxe-colgroup title="采购分类">
        <vxe-column
          field="applyType"
          show-overflow="tooltip"
          :formatter="({ cellValue }) => typeDictKeyValue[cellValue]"
          :filters="[{ data: '' }]"
          :filter-method="customEqualFilterMethod"
          width="95"
        >
          <template #header="{ column }">
            <vxe-select
              size="mini"
              v-for="(option, index) in column.filters"
              :key="index"
              v-model="option.data"
              @change="confirmFilterEvent(option)"
              :options="typeDict"
              placeholder=" "
              clearable
              transfer
            >
            </vxe-select>
          </template>
        </vxe-column>
      </vxe-colgroup>
      <vxe-colgroup title="所属项目">
        <vxe-column
          field="projectSerialNo"
          show-overflow="tooltip"
          width="100"
          :filters="[{ data: '' }]"
          :filter-method="customStringFilterMethod"
        >
          <template #header="{ column }">
            <vxe-input
              size="mini"
              type="text"
              v-for="(option, index) in column.filters"
              :key="index"
              v-model="option.data"
              @change="confirmFilterEvent(option)"
              placeholder=" "
              clearable
            ></vxe-input>
          </template>
        </vxe-column>
      </vxe-colgroup>
      <vxe-colgroup title="关联预算">
        <vxe-column
          field="budgetSerialNo"
          show-overflow="tooltip"
          width="100"
          :filters="[{ data: '' }]"
          :filter-method="customStringFilterMethod"
        >
          <template #header="{ column }">
            <vxe-input
              size="mini"
              type="text"
              v-for="(option, index) in column.filters"
              :key="index"
              v-model="option.data"
              @change="confirmFilterEvent(option)"
              placeholder=" "
              clearable
            ></vxe-input>
          </template>
        </vxe-column>
      </vxe-colgroup>
      <vxe-colgroup title="采购方式">
        <vxe-column
          field="buyer"
          show-overflow="tooltip"
          width="100"
          :filters="[{ data: '' }]"
          :filter-method="
            ({ option, row }) => (row.buyer ? row.buyer : 1) === option.value
          "
        >
          <template #header="{ column }">
            <vxe-select
              size="mini"
              v-for="(option, index) in column.filters"
              :key="index"
              v-model="option.data"
              @change="confirmFilterEvent(option)"
              :options="[
                { label: '采购部', value: 1 },
                { label: '办公室', value: 2 },
              ]"
              placeholder=" "
              clearable
              transfer
            >
            </vxe-select>
          </template>
          <template #default="{ row, rowIndex }">
            <el-tag
              v-if="row.buyer === 2"
              effect="dark"
              type="danger"
              size="mini"
            >
              办公室
            </el-tag>
            <el-tag v-else size="mini" effect="plain">采购</el-tag>
          </template>
        </vxe-column>
      </vxe-colgroup>
      <vxe-colgroup title="制造商">
        <vxe-column
          field="manufacturer"
          show-overflow="tooltip"
          width="115"
          :filters="[{ data: '' }]"
          :filter-method="customStringFilterMethod"
        >
          <template #header="{ column }">
            <vxe-input
              size="mini"
              type="text"
              v-for="(option, index) in column.filters"
              :key="index"
              v-model="option.data"
              @change="confirmFilterEvent(option)"
              placeholder=" "
              clearable
            ></vxe-input>
          </template>
        </vxe-column>
      </vxe-colgroup>
      <vxe-colgroup title="到货">
        <vxe-column
          field="receive"
          show-overflow="tooltip"
          width="70"
          :filter-multiple="false"
          :formatter="({ cellValue }) => (cellValue ? '是' : '否')"
          :filters="[{ data: '' }]"
          :filter-method="customEqualFilterMethod"
        >
          <template #header="{ column }">
            <vxe-select
              size="mini"
              v-for="(option, index) in column.filters"
              :key="index"
              v-model="option.data"
              @change="confirmFilterEvent(option)"
              :options="[
                { label: '是', value: true },
                { label: '否', value: false },
              ]"
              placeholder=" "
              clearable
              transfer
            >
            </vxe-select>
          </template>
        </vxe-column>
      </vxe-colgroup>
    </vxe-table>
    <order-arrival-dialog ref="orderArrivalDialogRef" @submit="onLoad()" />
    <order-item-history-dialog ref="orderItemHistoryRef" />
    <attach-dialog ref="attachRef" code="public" detail :delBtn="false" />
  </basic-container>
</template>

<script>
import { changeAmount, unArrivalItemList } from "@/api/ni/por/order";
import SupplierSelectDialog from "@/views/ni/base/components/SupplierSelectDialog";
import SupplierMultipleSelect from "@/views/ni/base/components/SupplierSelect";
import { mapGetters } from "vuex";
import OrderItemHistoryDialog from "@/views/ni/por/components/OrderItemHistoryDialog";
import AttachDialog from "@/components/attach-dialog";
import InquiryShowCard from "@/views/ni/por/components/inquiry-show-card";
import { getDetail1 } from "@/api/ni/base/supplier/supplierinfo";
import { dateFormat } from "@/util/date";
import OrderArrivalDialog from "@/views/ni/por/components/OrderArrivalDialog1";
import XEUtils from "xe-utils";
import {
  cancel,
  changeManufacturer,
  changeReceivingAddress,
} from "@/api/ni/por/order-item";

export default {
  components: {
    InquiryShowCard,
    AttachDialog,
    SupplierSelectDialog,
    SupplierMultipleSelect,
    OrderItemHistoryDialog,
    OrderArrivalDialog,
  },
  computed: {
    ...mapGetters(["userInfo", "colorName"]),
    amount() {
      let amount = 0;
      if (this.selectionList && this.selectionList.length > 0) {
        amount = this.selectionList.reduce((acc, cur) => {
          return Number(acc) + Number(cur.amount);
        }, 0);
      }
      return amount;
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  data() {
    const PAY_TYPE_PAY = "2";
    return {
      dataType: "1",
      payType: PAY_TYPE_PAY,
      query: {},
      loading: true,
      selectionList: [],
      data: [],
      brandDict: [],
      brandDictKeyValue: {},
      currentStartIndex: 0,
      currentEndIndex: 20,
      unitDict: [],
      unitDictKeyValue: {},
      billTypeDict: [],
      billTypeDictKeyValue: {},
      typeDict: [],
      typeList: [],
      typeDictKeyValue: {},
      tableHeight: this.calculateTableHeight(), // 初始化表格高度
      pv: false,
      crash: false,
      receivingAddressDict: [],
      receivingAddressDictKeyValue: {},
    };
  },
  watch: {
    pv: {
      handler(val) {
        if (val) this.query.pv = 1;
        else this.query.pv = null;
        this.onLoad();
      },
      immediate: false,
    },
    crash: {
      handler(val) {
        if (val) this.query.crash = true;
        else this.query.crash = null;
        this.onLoad();
      },
      immediate: false,
    },
  },
  created() {
    this.$nextTick(() => {
      // 将表格和工具栏进行关联
      const $table = this.$refs.table;
      const $toolbar = this.$refs.toolbarRef;
      if ($table && $toolbar) {
        $table.connect($toolbar);
      }
    });
    this.$http
      .get("/api/blade-system/dict-biz/dictionary?code=ni_por_type")
      .then((res) => {
        this.typeDict = res.data.data.map((item) => {
          return {
            label: item.dictValue,
            value: item.dictKey,
          };
        });
        this.typeDictKeyValue = this.typeDict.reduce((acc, cur) => {
          acc[cur.dictKey] = cur.dictValue;
          return acc;
        }, {});
      });
    this.$http
      .get("/api/blade-system/dict-biz/dictionary?code=ni_bill_type")
      .then((res) => {
        this.billTypeDict = res.data.data.map((item) => {
          return {
            label: item.dictValue,
            value: item.dictKey,
          };
        });
        this.billTypeDictKeyValue = this.billTypeDict.reduce((acc, cur) => {
          acc[cur.value] = cur.label;
          return acc;
        }, {});
        const billType = this.findObject(this.bill.option.column, "billType");
        billType.dicData = this.billTypeDict;
      });
    this.$http
      .get("/api/blade-system/dict-biz/dictionary?code=ni_material_unit")
      .then((res) => {
        this.unitDict = res.data.data;
        this.unitDictKeyValue = this.unitDict.reduce((acc, cur) => {
          acc[cur.dictKey] = cur.dictValue;
          return acc;
        }, {});
      });
    this.$http
      .get("/api/blade-system/dict-biz/dictionary?code=ni_brand")
      .then((res) => {
        this.brandDict = res.data.data.map((item) => {
          return {
            label: item.dictValue,
            value: item.dictKey,
          };
        });
        this.brandDictKeyValue = this.brandDict.reduce((acc, cur) => {
          acc[cur.dictKey] = cur.dictValue;
          return acc;
        }, {});
      });
    this.$http
      .get(
        "/api/blade-system/dict-biz/dictionary?code=ni_por_receiving_address"
      )
      .then((res) => {
        this.receivingAddressDict = res.data.data.map((item) => {
          return {
            label: item.dictValue,
            value: item.dictKey,
          };
        });
        this.receivingAddressDictKeyValue = res.data.data.reduce((acc, cur) => {
          acc[cur.dictKey] = cur.dictValue;
          return acc;
        }, {});
      });
    this.onLoad();
  },
  mounted() {
    window.addEventListener("resize", this.handleResize);
  },
  destroyed() {
    window.removeEventListener("resize", this.handleResize);
  },
  beforeRouteEnter(to, from, next) {
    to.meta.keepAlive = true;
    next();
  },
  methods: {
    rowReceivingAddress($event, row) {
      changeReceivingAddress(row.id, $event.value).then(() => {
        row.receivingAddress = $event.value;
        this.$message({
          type: "success",
          message: "操作成功!",
        });
      });
    },
    handleSelectionClear() {
      this.$refs.table.clearCheckboxRow();
      this.selectionList = [];
    },
    handleResize() {
      // 窗口大小变化时重新计算表格高度
      this.tableHeight = this.calculateTableHeight();
      console.log(this.tableHeight);
    },
    calculateTableHeight() {
      console.log(window.innerHeight - 224);
      // 根据实际需求计算表格高度
      return window.innerHeight - 224;
    },
    htmlDecode(input) {
      const doc = new DOMParser().parseFromString(input, "text/html");
      return doc.documentElement.textContent;
    },
    rowAttach(row) {
      this.$refs.attachRef.init(row.applyItemId, "ni_por_apply_item");
    },
    handleSelectionChange({ checked }) {
      this.selectionList = this.$refs.table.getCheckboxRecords();
    },
    cellStyle({ row, column }) {
      if ("applyUserName" === column.field && row.crash) {
        return "col-red";
      }
      if ("row" === column.field && row.yearsAgo) {
        return "col-red";
      }
      if (
        "purchaseUserName" === column.property &&
        row.payType === "3" &&
        row.receive
      ) {
        return "col-alipay-orange";
      } else if (
        "purchaseUserName" === column.property &&
        row.payType === "3" &&
        !row.receive
      ) {
        return "col-alipay";
      } else if (
        "purchaseUserName" === column.property &&
        row.payType !== "3" &&
        row.receive
      ) {
        return "col-orange";
      }
      if ("materialName" === column.property && row.applyType === "21") {
        return "col-yzdyzb";
      }
      return null;
    },
    async handleArrival() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择到货的数据");
        return;
      }
      const noSupplier = this.selectionList.some((item) => !item.supplierId);
      if (noSupplier) {
        this.$message.warning("数据中存在未关联供应商的数据，请重新选择");
        return;
      }
      const supplierIds = new Set();
      const purchaseUserId = new Set();
      const notAlipayPayType = new Set();
      const brand = new Set();
      this.selectionList.forEach((item) => {
        brand.add(item.brand);
        supplierIds.add(item.supplierId);
        purchaseUserId.add(item.purchaseUserId);
        if (item.payType !== "3") notAlipayPayType.add(item.payType);
      });
      if (brand.size > 1) {
        this.$message.warning("请选择同一账套的数据");
        return;
      }
      if (supplierIds.size > 1 && notAlipayPayType.size > 0) {
        this.$message.warning("请选择同一供应商的数据");
        return;
      }
      if (purchaseUserId.size > 1) {
        this.$message.warning("请选择同一采购的数据");
        return;
      }
      const pvs = this.selectionList.some(
        (item) => item.pv && !item.manufacturer
      );
      if (pvs) {
        this.$message.warning(
          "数据中存在压力容器数据，且未选择制造商，请选择制造商后再操作"
        );
        return;
      }
      let supplierName = this.selectionList[0].supplier;
      let supplierId = this.selectionList[0].supplierId;
      if (notAlipayPayType.size === 0) {
        //如果是支付宝，则取支付宝的供应商  050337
        const res = await getDetail1({ code: "050337" });
        supplierName = res.data.data.name;
        supplierId = res.data.data.id;
      }
      const row = {
        arrivalDate: dateFormat(new Date(), "yyyy-MM-dd"),
        brand: this.selectionList[0].brand,
        purchaseUserName: this.selectionList[0].purchaseUserName,
        purchaseUserId: this.selectionList[0].purchaseUserId,
        supplierId,
        supplierName,
        items: this.selectionList.map((item) => {
          return {
            row: item.row,
            applyUserName: item.applyUserName,
            applyUserId: item.applyUserId,
            orderId: item.orderId,
            applyId: item.applyId,
            orderItemId: item.id,
            applyItemId: item.applyItemId,
            orderNum: item.num,
            applyTitle: item.applyTitle,
            applySerialNo: item.applySerialNo,
            materialId: item.materialId,
            materialCode: item.materialCode,
            materialName: item.materialName,
            specification: item.specification,
            quality: item.quality,
            gb: item.gb,
            unit: item.unit,
            finishNum: item.arrivalNum,
            brand: item.brand,
            depotLocation: item.depotLocation,
            receiveRemark: item.receiveRemark,
            arrivalAmount:
              Number(item.amount) -
              Number(item.arrivalNum) * Number(item.price),
            price: item.price,
            arrivalNum: Number(item.num) - Number(item.arrivalNum),
            cost: item.cost,
            pv: item.pv,
            fa: item.fa,
            manufacturer: item.manufacturer,
          };
        }),
      };
      this.$refs.orderArrivalDialogRef.init(row);
    },
    dataTypeChange(val) {
      this.onLoad();
    },
    handleClear() {
      this.pv = false;
      this.crash = false;
      this.$refs.table.clearFilter();
    },
    handleManufacturer() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择要修改的数据");
        return;
      }
      this.$prompt("制造商", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
      }).then(({ value }) => {
        changeManufacturer(this.ids, value).then(() => {
          this.data.forEach((item) => {
            if (this.ids.split(",").includes(item.id)) {
              item.manufacturer = value;
            }
          });
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
      });
    },
    handleCancel() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择要取消的数据");
        return;
      }
      this.$confirm("确定将选择数据取消?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let ids = new Set();
          this.selectionList.forEach((ele) => {
            ids.add(ele.id);
          });
          return cancel(Array.from(ids).join(","));
        })
        .then(() => {
          this.onLoad();
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    handleExcel() {
      this.selectionList = this.$refs.table.getCheckboxRecords();
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择要导出的数据");
        return;
      }
      this.$Export.excel({
        title: "未到货明细",
        columns: [
          {
            label: "序号",
            prop: "row",
          },
          {
            label: "采购人",
            prop: "purchaseUserName",
          },
          {
            label: "品名",
            prop: "materialName",
          },
          {
            label: "规格",
            prop: "specification",
          },
          {
            label: "材质",
            prop: "quality",
          },
          {
            label: "数量",
            prop: "num",
          },
          {
            label: "单位",
            prop: "unit",
          },
          {
            label: "单价",
            prop: "price",
          },
          {
            label: "金额",
            prop: "amount",
          },
          {
            label: "申请备注",
            prop: "applyRemark",
          },
          {
            label: "用途",
            prop: "purpose",
          },
          {
            label: "供应商",
            prop: "supplier",
          },
          {
            label: "申请人",
            prop: "applyUserName",
          },
          {
            label: "需用日期",
            prop: "needDate",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
          },
          {
            label: "暂存位置",
            prop: "depotLocation",
          },
          {
            label: "暂存备注",
            prop: "receiveRemark",
          },
          {
            label: "订单号",
            prop: "serialNo",
          },
          {
            label: "采购申请",
            prop: "applySerialNo",
          },
          {
            label: "申请时间",
            prop: "applyTime",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
          },
          {
            label: "到货",
            prop: "receive",
          },
          {
            label: "采购分类",
            prop: "applyType",
          },
          {
            label: "所属项目",
            prop: "projectSerialNo",
          },
          {
            label: "关联预算",
            prop: "budgetSerialNo",
          },
          {
            label: "账套",
            prop: "brand",
          },
          {
            label: "压力容器",
            prop: "pv",
          },
          {
            label: "制造商",
            prop: "manufacturer",
          },
          {
            label: "到货地",
            prop: "receivingAddress",
          },
        ],
        data: this.selectionList.map((item) => {
          return {
            ...item,
            num: Number(item.num),
            price: Number(item.price),
            amount: Number(item.amount),
            unit: this.unitDictKeyValue[item.unit],
            receive: item.receive ? "是" : "否",
            applyType: this.typeDictKeyValue[item.applyType],
            brand: this.brandDictKeyValue[item.brand],
            pv: item.pv ? "是" : "否",
            receivingAddress:
              this.receivingAddressDictKeyValue[item.receivingAddress],
          };
        }),
      });
    },
    showHistoryDialogByMaterialTypeId(row) {
      this.$refs.orderItemHistoryRef.initByMaterialTypeId(row);
    },
    showHistoryDialog(row) {
      this.$refs.orderItemHistoryRef.initByMaterialId(row);
    },
    sortEvent(field, order) {
      const $table = this.$refs.table;
      if ($table) {
        $table.sort({ field, order });
      }
    },
    confirmFilterEvent(option) {
      const $table = this.$refs.table;
      if ($table) {
        // 设置为选中状态
        option.checked = true;
        // 修改条件之后，需要手动调用 updateData 处理表格数据
        $table.updateData();
      }
    },
    customEqualFilterMethod({ option, row, column }) {
      if (option.data) {
        return row[column.field] === option.data;
      }
      return true;
    },
    customStringFilterMethod({ option, row, column }) {
      if (option.data) {
        return (
          XEUtils.toValueString(row[column.field])
            .toLowerCase()
            .indexOf(option.data) > -1
        );
      }
      return true;
    },
    onLoad() {
      this.loading = true;

      const q = {
        ...this.query,
      };
      if (this.dataType === "1") {
        q.purchaseUserId = this.userInfo.user_id;
      } else if (this.dataType === "2") {
        q.purchaseUserId = null;
      }
      unArrivalItemList(q).then((res) => {
        const data = res.data.data;
        data.forEach((item) => {
          item.checked = false;
          if (item.no) {
            item.row += "-" + item.no;
          }
          if (item.price) {
            item.priceStr = Number(item.price).toLocaleString("zh-CN", {
              minimumFractionDigits: 2,
            });
          }
          if (item.applyRemark)
            item.applyRemark = this.htmlDecode(item.applyRemark);
        });
        this.$refs.table.loadData(data);
        this.$refs.table.clearCheckboxRow();
        this.selectionList = [];
        this.loading = false;
      });
    },
    async rowAmountChangeSubmit($event, row) {
      try {
        if (["1", "2"].includes(row.depotState)) {
          await this.$confirm(
            "该数据已入库,修改后需手动修改同步用友的数据,是否继续修改?",
            "提示",
            {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
            }
          );
        }
        await changeAmount(row.id, "", $event.value);
        row.price = ($event.value / row.num).toFixed(2);
        row.priceStr = Number(row.price).toLocaleString("zh-CN", {
          minimumFractionDigits: 2,
        });
        row.amount = $event.value;
        row.amountStr = Number(row.amount).toLocaleString("zh-CN", {
          minimumFractionDigits: 2,
        });
        this.$message({
          type: "success",
          message: "操作成功!",
        });
      } catch (action) {
        row.amount = Number(row.amountStr.replace(/,/g, ""));
      }
    },
    async rowPriceChangeSubmit($event, row) {
      try {
        const amount = (row.num * $event.value).toFixed(2);
        await changeAmount(row.id, "", amount);
        row.price = $event.value;
        row.priceStr = Number($event.value).toLocaleString("zh-CN", {
          minimumFractionDigits: 2,
        });
        row.amount = amount;
        row.amountStr = Number(amount).toLocaleString("zh-CN", {
          minimumFractionDigits: 2,
        });
        this.$message({
          type: "success",
          message: "操作成功!",
        });
      } catch (action) {
        row.price = Number(row.priceStr.replace(/,/g, ""));
      }
    },
  },
};
</script>

<style lang="scss" scoped>
/deep/ .el-link--inner {
  font-size: 12px;
}

/deep/ .vxe-table .vxe-body--column.col-red {
  background-color: #f56c6c;
  color: #fff;
}

/deep/ .vxe-table .vxe-body--column.col-orange {
  background-color: #e6a23c;
  color: #fff;
}

/deep/ .vxe-table .vxe-body--column.col-alipay-orange {
  background: #e6a23c url(/img/alipay.png) no-repeat calc(100% - 10px) center;
  color: #fff;
}

/deep/ .vxe-table .vxe-body--column.col-yzdyzb {
  color: #fff;
  background: #f56c6c url(/img/yzdyzb.png) no-repeat left center / contain;
  position: relative;
}

/deep/ .vxe-table .vxe-body--column.col-alipay {
  background: url(/img/alipay.png) no-repeat calc(100% - 10px) center;
}
</style>

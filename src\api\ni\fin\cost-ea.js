import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/ni/fin/costEa/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/ni/fin/costEa/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/ni/fin/costEa/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/ni/fin/costEa/save',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/ni/fin/costEa/update',
    method: 'post',
    data: row
  })
}

export const apply = (id, processDefKey) => {
  return request({
    url: '/api/ni/fin/costEa/apply',
    method: 'post',
    params: {
      id,
      processDefKey
    }
  })
}

export const getDetailByProcessInsId = (processInsId) => {
  return request({
    url: '/api/ni/fin/costEa/detailByProcessInsId',
    method: 'get',
    params: {
      processInsId
    }
  })
}


export const pay = (row) => {
  return request({
    url: '/api/ni/fin/costEa/pay',
    method: 'post',
    data: row

  })
}
export const sync = (id, isCovered = false) => {
  return request({
    url: "/api/ni/fin/costEa/sync",
    method: "post",
    params: {
      id,
      isCovered,
    },
  });
};
export const syncRange = (startDate,endDate,voucher) => {
  return request({
    url: "/api/ni/fin/costEa/syncRange",
    method: "post",
    params: {
      voucher,
      startDate,
      endDate,
    },
  });
};

export const getPrintData = (id) => {
  return request({
    url: "/api/ni/fin/costEa/getPrintData",
    method: "get",
    params: {
      id,
    },
  });
};

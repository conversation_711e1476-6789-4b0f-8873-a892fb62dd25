import request from "@/router/axios";

export const getList = (current, size, params) => {
    return request({
        url:"/api/ni/pa/paExpressRegister/page",
        method:"get",
        params:{
            ...params,
            current,
            size,
        },
    });
};

export const getDetail = (id) => {
    return request({
      url: "/api/ni/pa/paExpressRegister/detail",
      method: "get",
      params: {
        id,
      },
    });
  };

  export const add = (row) => {
    return request({
        url:"/api/ni/pa/paExpressRegister/add",
        method:"post",
        params:{
            data:row,
        },
    });
  };

  export const remove = (ids) => {
    return request({
        url:"/api/ni/pa/paExpressRegister/remove",
        method:"post",
        params:{
            ids,
        },
    });
  };

  export const update = (row , pos) => {
    return request({
        url:"/api/ni/pa/paExpressRegister/update",
        method:"post",
        data: row,  
        params:pos
    });
  };

  export const submit = (ids) => {
    return request({
      url: '/api/ni/pa/paExpressRegister/submit',
      method: 'post',
      params: {
        ids,
      }
    })
  }
  
  export const back = (ids) => {
    return request({
      url: '/api/ni/pa/paExpressRegister/back',
      method: 'post',
      params: {
        ids,
      }
    })
  }

  export const save = (row , pos) => {
    return request({
      url: "/api/ni/pa/paExpressRegister/save",
      method: "post",
      data:row,
      params:pos
    });
  }; 
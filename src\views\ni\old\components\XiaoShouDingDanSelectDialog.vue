<script>


import HuoWuLiuZhuanMingxiListDialog from "@/views/ni/old/components/HuoWuLiuZhuanJiLuListDialog.vue";

export default {
  components: {HuoWuLiuZhuanMingxiListDialog},
  props: {
    multiple: {
      type: Boolean,
      default: false,
    },

  },
  data() {
    return {
      multiple1: false,
      url: '/api/ni/old/xiaoShouDingDan/list',
      detail: false,
      title: "销售订单",
      params: {},
      visible: false,
      form: {},
      query: {},
      loading: false,
      selectionList: [],
      data: [],
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      option: {
        menuWidth: 100,
        editBtn: false,
        delBtn: false,
        searchEnter: true,
        searchIndex: 3,
        searchIcon: true,
        size: "mini",
        searchSize: "mini",
        align: "center",
        addBtn: false,
        header: false,
        border: true,
        tip: false,
        reserveSelection: true,
        highlightCurrentRow: true,
        searchMenuSpan: 6,
        selection: true,
        column: [
          {
            label: "",
            prop: "radio",
            type: "radio",
            width: 55,
            hide: true,
          },
          {
            label: "Id",
            prop: "id",
            search: true,
            width: 70,
          },
          {
            label: "状态",
            prop: "zt",
            width: 70,
            overHidden: true
          },
          {
            label: "状态",
            prop: "queryZt",
            hide: true,
            showColumn: false,
            search: true
          },
          {
            label: "发货编号",
            prop: "fdbh",
            width: 100, search: true,
            overHidden: true
          },
          {
            label: "批号",
            prop: "ph",
            width: 100,
            search: true,
            overHidden: true
          },
          {
            label: "发货日期",
            prop: "fhrq",
            overHidden: true,
            width: 110,
          },
          {
            label: "发货日期",
            prop: "fhrq1",
            type: "date",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            searchRange: true,
            search: true,
            hide: true,
            showColumn: false,
            overHidden: true,
            width: 110,
          },
          {
            label: "收货地址",
            prop: "shdz",
            placeholder: " ",
            overHidden: true, search: true,
            width: 110,
          }, {
            label: "发货地",
            prop: "fhd",
            placeholder: " ",
            overHidden: true, search: true,
            width: 90,
          }, {
            label: "生产商",
            prop: "scs",
            placeholder: " ",
            overHidden: true,
            width: 80,
            hide: true,
          }, {
            label: "客户名称",
            prop: "khmc",
            placeholder: " ",
            overHidden: true, search: true,
            width: 100,
          }, {
            label: "净重Kg",
            prop: "jzkg",
            placeholder: " ",
            overHidden: true,
            width: 100,
          }, {
            label: "提货车号",
            prop: "txch",
            placeholder: " ",
            overHidden: true, search: true,
            width: 100,
          }, {
            label: "运货车号",
            prop: "yuch",
            placeholder: " ",
            overHidden: true,
            search: true,
            width: 100,
          },
          {
            label: "发货附件",
            prop: "fhfj",
            placeholder: " ",
            overHidden: true,
            width: 80,
          }, {
            label: "质检附件",
            prop: "zjfj",
            placeholder: " ",
            overHidden: true,
            width: 80,
          },
        ],
      },
    }
  },
  methods: {
    onShow(params, url) {
      this.multiple1 = this.multiple
      if (!this.multiple1) {
        this.$set(this.option, "selection", false);
        this.findObject(this.option.column, "radio").hide = false;
      } else {
        this.$set(this.option, "selection", true);
        this.findObject(this.option.column, "radio").hide = true;
      }
      if (url)
        this.url = url
      this.detail = true
      this.page.currentPage = 1
      this.query = {}
      this.params = params
      this.visible = true
    },
    onSelect(params, url, multiple) {
      this.multiple1 = this.multiple || multiple
      if (!this.multiple1) {
        this.$set(this.option, "selection", false);
        this.findObject(this.option.column, "radio").hide = false;
      } else {
        this.$set(this.option, "selection", true);
        this.findObject(this.option.column, "radio").hide = true;
      }
      if (url)
        this.url = url
      this.detail = false
      this.page.currentPage = 1
      this.query = {}
      this.params = params
      this.visible = true
    },
    handleConfirm() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$emit("confirm", this.selectionList);
      this.handleClose();
    },
    handleClose(done) {
      this.selectionClear()
      this.visible = false;
      if (done && typeof done == "function") done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.onLoad(this.page, params);
      done();
    },
    selectionClear() {
      this.selectionList = [];
      if (this.$refs.crud) this.$refs.crud.toggleSelection();
    },
    rowClick(row) {
      if (!this.multiple1) {
        this.selectionList = [row];
        this.$set(this.form, "radio", row.id);
      } else this.$refs.crud.toggleSelection([row]);
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      const qu = {...params, ...this.query, ...this.params};
      if (qu.fhrq1 && qu.fhrq1.length === 2) {
        qu.startFhrq = qu.fhrq1[0]
        qu.endFhrq = qu.fhrq1[1]
        qu.fhrq1 = null
      }
      this.$http.get(this.url, {
        params: {
          ...qu,
          current: page.currentPage,
          size: page.pageSize,
        },
      }).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records || [];
        this.loading = false;
      });
    },
    handleDataSub(row) {
      this.$refs.huoWuLiuZhuanMingxiListDialogRef.onShow({shippingNo: row.fdbh})
    },
  }
}
</script>

<template>
  <el-dialog
    ref="ai-dialog"
    custom-class="ai-dialog"
    :visible.sync="visible"
    :title="title"
    width="60%"
    append-to-body
    :before-close="handleClose"
  >
    <avue-crud
      v-if="visible"
      :option="option"
      :table-loading="loading"
      :data="data"
      v-model="form"
      :search.sync="query"
      :page.sync="page"
      ref="crud"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionList = $event"
      @row-click="rowClick"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template v-if="!multiple1" #radio="{ row }">
        <el-radio v-model="form.radio" :label="row.id"><i></i></el-radio>
      </template>
      <template #menu="{row,index}">
        <el-button type="text"
                   icon="el-icon-reading"
                   size="mini"
                   @click.stop="handleDataSub(row)">明细
        </el-button>
      </template>
    </avue-crud>
    <span slot="footer" class="dialog-footer" v-if="!detail">
      <el-button @click="handleClose" size="mini">取 消</el-button>
      <el-button type="primary" @click="handleConfirm" size="mini"
      >确 定</el-button
      >
    </span>
    <HuoWuLiuZhuanMingxiListDialog ref="huoWuLiuZhuanMingxiListDialogRef"/>
  </el-dialog>
</template>

<style scoped>

</style>

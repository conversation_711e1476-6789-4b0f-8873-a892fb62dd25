import request from "@/router/axios";

export const getList = (current, size, params) => {
  return request({
    url: "/api/ni/tracing/quality-inspection/list",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/ni/tracing/quality-inspection/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const getBatch = (inspectionId) => {
  return request({
    url: "/api/ni/tracing/production-batch/getBatch",
    method: "get",
    params: {
      inspectionId,
    },
  });
};

export const remove = (ids) => {
  return request({
    url: "/api/ni/tracing/quality-inspection/remove",
    method: "post",
    params: {
      ids,
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/ni/tracing/quality-inspection/submit",
    method: "post",
    data: row,
  });
};

export const update = (row) => {
  return request({
    url: "/api/ni/tracing/quality-inspection/submit",
    method: "post",
    data: row,
  });
};

export const publish = (row) => {
  return request({
    url: "/api/ni/tracing/quality-inspection/publish",
    method: "post",
    data: row,
  });
};

export const associate = (data) => {
  return request({
    url: "/api/ni/tracing/quality-inspection/associate",
    method: "post",
    data: data,
  });
};

export const linkGuoWaiBeiHuoJiHua = (ids, stockId) => {
  return request({
    url: "/api/ni/tracing/quality-inspection/linkGuoWaiBeiHuoJiHua",
    method: "post",
    params: {
      ids,
      stockId,
    },
  });
};

export const copy = (id) => {
  return request({
    url: "/api/ni/tracing/quality-inspection/copy",
    method: "post",
    params: {
      id
    },
  });
};

export const synchronous = (id) => {
  return request({
    url: "/api/ni/tracing/quality-inspection/synchronousManual",
    method: "post",
    params: {
      id
    },
  });
};

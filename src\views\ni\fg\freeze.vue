<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      ref="crud"
      v-model="form"
      :permission="permissionList"
      :before-open="beforeOpen"
      @row-save="rowSave"
      @row-update="rowUpdate"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
      :cell-style="cellStyle"
    >
      <template #status="{ row, index }">
        <span v-if="row.status === 1 && !row.red">已冻结</span>
        <span v-else-if="row.status === 1 && row.red">取消冻结</span>
        <span v-else>已取消</span>
      </template>
      <template #menuLeft>
        <el-button
          type="primary"
          size="mini"
          icon="el-icon-plus"
          v-if="permission.freeze_add"
          @click="handleAdd"
          >新增
        </el-button>
        <el-button
          type="danger"
          size="mini"
          icon="el-icon-delete"
          plain
          v-if="permission.freeze_delete"
          @click="handleDelete"
          >删除
        </el-button>
        <el-divider direction="vertical" />
        <el-checkbox v-model="freeze">冻结中</el-checkbox>
      </template>
      <template #menu="{ row, index }">
        <el-button
          type="text"
          icon="el-icon-truck"
          size="mini"
          v-if="permission.freeze_cancel && row.status === 1 && !row.red"
          @click="rowCancel(row)"
          >取消
        </el-button>
      </template>
    </avue-crud>
    <freeze-form-dialog ref="freezeFormDialogRef" @confirm="onLoad(page)" />
  </basic-container>
</template>

<script>
import {
  add,
  cancel,
  getDetail,
  getList,
  remove,
  update,
} from "@/api/ni/fg/fgFreeze";
import { mapGetters } from "vuex";
import { dateFormat } from "@/util/date";
import FreezeFormDialog from "@/views/ni/fg/components/FreezeFormDialog.vue";

export default {
  components: { FreezeFormDialog },
  data() {
    return {
      form: {},
      loading: true,
      dialogVisible: false,
      query: {},
      data: [],
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        menuWidth: 100,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        span: 12,
        dialogDrag: true,
        size: "mini",
        searchSize: "mini",
        align: "center",
        searchIndex: 3,
        searchIcon: true,
        searchEnter: true,
        height: "auto",
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        selection: true,
        dialogClickModal: false,
        column: [
          {
            label: "操作人",
            prop: "createUser",
            type: "select",
            remote: true,
            dicUrl: `/api/blade-user/search/otj/user?name={{key}}`,
            props: {
              label: "realName",
              value: "id",
            },
            dicFormatter: (data) => {
              return data.data.records;
            },
            search: true,
            hide: true,
            showColumn: false,
            placeholder: " ",
            filterable: true,
            overHidden: true,
            rules: [
              {
                required: true,
                message: "请选择操作人",
                trigger: "blur",
              },
            ],
          },
          {
            label: "操作人",
            prop: "createUserName",
            display: false,
            minWidth: 80,
          },
          {
            label: "操作时间",
            prop: "createTime",
            search: true,
            type: "date",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            searchRange: true,
            placeholder: " ",
            clearable: false,
            minWidth: 120,
            rules: [
              {
                required: true,
                message: "请选择操作时间",
                trigger: "blur",
              },
            ],
          },
          {
            label: "冻结原因",
            prop: "reason",
            overHidden: true,
            width: 150,
            search: true,
          },
          {
            label: "冻结类型",
            prop: "type",
            type: "select",
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_fg_inventory_freeze_type",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            rules: [
              {
                required: true,
                message: "请选择冻结类型",
                trigger: "blur",
              },
            ],
          },
          {
            label: "仓库",
            prop: "depotId",
            type: "select",
            dicUrl: "/api/ni/base/depot/info/list?status=2&type=1",
            props: {
              label: "name",
              value: "id",
              desc: "code",
            },
            search: true,
            rules: [
              {
                required: true,
                message: "请选择仓库",
                trigger: "blur",
              },
            ],
          },
          {
            label: "存货编码",
            prop: "materialCode",
            placeholder: " ",
            minWidth: 110,
            overHidden: true,
            search: true,
          },
          {
            label: "规格",
            prop: "spec",
            type: "select",
            dicUrl: "/api/ni/product/spec/list",
            props: {
              label: "name",
              value: "code",
              desc: "code",
            },
            hide: true,
            showColumn: false,
            filterable: true,
            search: true,
          },
          {
            label: "规格",
            prop: "specText",
            placeholder: " ",
            display: false,
            overHidden: true,
            minWidth: 90,
          },
          {
            label: "质量",
            prop: "qualityLevel",
            type: "select",
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_product_sku_quality_level",
            props: {
              label: "dictValue",
              value: "dictKey",
              desc: "dictKey",
            },
            placeholder: " ",
            width: 100,
          },
          {
            label: "外包装",
            prop: "packageText",
            placeholder: " ",
            display: false,
            overHidden: true,
            minWidth: 115,
            search: true,
          },
          {
            label: "内包装",
            prop: "innerPackageText",
            placeholder: " ",
            display: false,
            overHidden: true,
            minWidth: 115,
          },
          {
            label: "内包装",
            prop: "innerPackageId",
            type: "select",
            dicUrl:
              "/api/ni/product/packaging/list?innerPark=1&current=1&size=20&status=1&&name={{key}}",
            props: {
              label: "name",
              value: "id",
              desc: "type",
            },
            search: true,
            remote: true,
            dicFormatter: (data) => {
              return data.data.records;
            },
            hide: true,
            showColumn: false,
            filterable: true,
          },
          {
            label: "批号",
            prop: "batchNo",
            search: true,
            overHidden: true,
            width: 110,
          },
          {
            label: "件数",
            prop: "num",
            display: false,
          },
          {
            label: "重量",
            prop: "weight",
            display: false,
          },
          {
            label: "状态",
            prop: "status",
            type: "select",
            dicData: [
              {
                label: "已冻结",
                value: 1,
              },
              {
                label: "已取消",
                value: 2,
              },
            ],
          },
        ],
      },
      freeze: true,
      qualityLevelColorMap: {
        A: "#67C23A", // 高吸附 - 绿色
        P: "#409EFF", // 优等品 - 蓝色
        Q: "#E6A23C", // 合格品 - 橙色
      },
    };
  },
  watch: {
    freeze: {
      handler() {
        this.page.currentPage = 1;
        this.onLoad(this.page);
      },
    },
  },
  computed: {
    ...mapGetters(["permission", "userInfo"]),
    permissionList() {
      return {
        addBtn: this.permission.freeze_add,
        viewBtn: this.permission.freeze_view,
        delBtn: this.permission.freeze_delete,
        editBtn: this.permission.freeze_edit,
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  methods: {
    rowCancel(row, index) {
      this.$confirm("确定将选择数据撤销冻结?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return cancel(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.$message.success("删除成功");
          this.onLoad(this.page);
        });
    },
    handleAdd() {
      this.$refs.freezeFormDialogRef.onFreeze();
    },
    beforeOpen(done, type) {
      if ("add" === type) {
        this.form.operator = this.userInfo.user_id;
        this.form.transferDate = dateFormat(new Date(), "yyyy-MM-dd");
      }
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then((res) => {
          this.form = res.data.data;
        });
      }
      done();
    },
    rowSave(row, done, loading) {
      add(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          window.console.log(error);
        }
      );
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          console.log(error);
        }
      );
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      const q = {
        ...params,
        ...this.query,
      };
      if (this.freeze) {
        q.status = 1;
        q.red = false;
      } else {
        q.status = null;
        q.red = null;
      }
      getList(page.currentPage, page.pageSize, q).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
    cellStyle({ row, column }) {
      if ("qualityLevel" === column.columnKey) {
        const color = this.qualityLevelColorMap[row.qualityLevel];
        return {
          backgroundColor: color || "#909399", // 默认颜色为灰色
          color: "#fff",
        };
      }
      if ("status" === column.columnKey && row.status === 2) {
        return {
          backgroundColor: "#E6A23C",
          color: "#fff",
        };
      } else if ("status" === column.columnKey && row.status === 1 && row.red) {
        return {
          backgroundColor: "#67C23A",
          color: "#fff",
        };
      } else if (
        "status" === column.columnKey &&
        row.status === 1 &&
        !row.red
      ) {
        return {
          backgroundColor: "#409EFF",
          color: "#fff",
        };
      }
      if ("num" === column.columnKey && row.num <= 0) {
        return {
          backgroundColor: "#F56C6C",
          color: "#fff",
        };
      }
      if ("weight" === column.columnKey && row.num <= 0) {
        return {
          backgroundColor: "#F56C6C",
          color: "#fff",
        };
      }
    },
  },
};
</script>

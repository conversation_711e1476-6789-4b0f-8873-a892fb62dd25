<script>
import { getShippingList, getList } from "@/api/ni/old/guoWaiFaHuo";

export default {
  props: {
    multiple: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      detail: false,
      title: "国外发货",
      params: {},
      visible: false,
      form: {},
      query: {},
      loading: false,
      selectionList: [],
      data: [],
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      option: {
        searchEnter: true,
        searchIndex: 3,
        searchIcon: true,
        size: "mini",
        searchSize: "mini",
        align: "center",
        menu: false,
        addBtn: false,
        header: false,
        border: true,
        tip: false,
        reserveSelection: true,
        highlightCurrentRow: true,
        searchMenuSpan: 6,
        selection: true,
        column: [
          {
            label: "",
            prop: "radio",
            type: "radio",
            width: 55,
            hide: true,
          },
          {
            label: "Id",
            prop: "id",
            search: true,
            width: 70,
          },
          {
            label: "状态",
            prop: "zhuangTai",
            width: 70,
            overHidden: true,
          },
          {
            label: "发货编号",
            prop: "faHuoBianHao",
            width: 100,
            search: true,
            overHidden: true,
          },

          {
            label: "发货日期",
            prop: "faHuoRiQi",
            overHidden: true,
            width: 110,
          },
          {
            label: "公司名称",
            prop: "gongSiMingCheng",
            placeholder: " ",
            overHidden: true,
            search: true,
            minWidth: 120,
          },
          {
            label: "国家",
            prop: "guoJia",
            placeholder: " ",
            overHidden: true,
            search: true,
            width: 100,
          },
          {
            label: "目的港",
            prop: "muDiGang",
            placeholder: " ",
            overHidden: true,
            search: true,
            minWidth: 100,
          },
          {
            label: "净重Kg",
            prop: "jingZhong",
            placeholder: " ",
            overHidden: true,
            width: 100,
          },
          {
            label: "质检附件",
            prop: "zhiJianFuJian",
            placeholder: " ",
            overHidden: true,
            minWidth: 120,
          },
          {
            label: "附件",
            prop: "fuJian",
            placeholder: " ",
            overHidden: true,
            minWidth: 120,
          },
          {
            label: "理货单附件",
            prop: "liHuoDanFuJian",
            placeholder: " ",
            overHidden: true,
            minWidth: 120,
          },
        ],
      },
    };
  },
  methods: {
    onShow(params) {
      if (!this.multiple) {
        this.$set(this.option, "selection", false);
        this.findObject(this.option.column, "radio").hide = false;
      } else {
        this.$set(this.option, "selection", true);
        this.findObject(this.option.column, "radio").hide = true;
      }
      this.detail = true;
      this.page.currentPage = 1;
      this.query = {};
      this.params = params;
      this.visible = true;
    },
    onSelect(params) {
      if (!this.multiple) {
        this.$set(this.option, "selection", false);
        this.findObject(this.option.column, "radio").hide = false;
      } else {
        this.$set(this.option, "selection", true);
        this.findObject(this.option.column, "radio").hide = true;
      }
      this.detail = false;
      this.page.currentPage = 1;
      this.query = {};
      this.params = params;
      this.visible = true;
    },
    handleConfirm() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$emit("confirm", this.selectionList);
      this.handleClose();
    },
    handleClose(done) {
      this.selectionClear();
      this.visible = false;
      if (done && typeof done == "function") done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.onLoad(this.page, params);
      done();
    },
    selectionClear() {
      this.selectionList = [];
      if (this.$refs.crud) this.$refs.crud.toggleSelection();
    },
    rowClick(row) {
      if (!this.multiple) {
        this.selectionList = [row];
        this.$set(this.form, "radio", row.id);
      } else this.$refs.crud.toggleSelection([row]);
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      const qu = { ...params, ...this.query, ...this.params };
      let res;
      if (this.detail) {
        res = getList(page.currentPage, page.pageSize, qu);
      } else res = getShippingList(page.currentPage, page.pageSize, qu);
      res.then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records || [];
        this.loading = false;
      });
    },
  },
};
</script>

<template>
  <el-dialog
    ref="ai-dialog"
    custom-class="ai-dialog"
    :visible.sync="visible"
    :title="title"
    width="60%"
    append-to-body
    :before-close="handleClose"
  >
    <avue-crud
      v-if="visible"
      :option="option"
      :table-loading="loading"
      :data="data"
      v-model="form"
      :search.sync="query"
      :page.sync="page"
      ref="crud"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionList = $event"
      @row-click="rowClick"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template v-if="!multiple" #radio="{ row }">
        <el-radio v-model="form.radio" :label="row.id"><i></i></el-radio>
      </template>
    </avue-crud>
    <span slot="footer" class="dialog-footer" v-if="!detail">
      <el-button @click="handleClose" size="mini">取 消</el-button>
      <el-button type="primary" @click="handleConfirm" size="mini"
        >确 定</el-button
      >
    </span>
  </el-dialog>
</template>

<style scoped></style>

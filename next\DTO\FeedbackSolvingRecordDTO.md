```java
package com.natergy.ni.feedback.dto;

import com.natergy.ni.feedback.entity.FeedbackSolvingRecordEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 问题各负责人解决记录 数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2025-02-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FeedbackSolvingRecordDTO extends FeedbackSolvingRecordEntity {
	private static final long serialVersionUID = 1L;

}
```

### 类功能说明

该类是问题反馈模块的数据**数据传输对象（DTO）**，主要作用是：

1. **继承实体类**：通过继承`FeedbackEntity`，获得其所有字段和方法，无需重复定义与实体类相同的属性。
2. **扩展灵活性**：作为 DTO，可在不修改原实体类的情况下，根据传输需求添加额外字段或方法（当前代码未添加新字段，为基础扩展扩展预留）。
3. **适配分层架构**：在控制器与服务层、服务层与外部接口之间传输数据时使用，避免直接暴露实体类，便于维护数据传输规则。

通过 Lombok 的`@Data`和`@EqualsAndHashCode`注解，简化了 JavaBean 的样板代码，同时保证了 equals 和 hashCode 方法能正确处理父类字段，确保对象比较的准确性。
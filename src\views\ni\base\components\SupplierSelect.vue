<template>
  <div>
    <el-input
      v-model="codes"
      :size="size"
      suffix-icon="el-icon-search"
      :disabled="disabled"
      clearable
      @focus="handleFocus"
      @clear="handleClear"
    />
    <supplier-select-dialog
      ref="supplierSelectRef"
      :multiple="multiple"
      :params="params"
      @submit="handleSupplierSubmit"
    />
  </div>
</template>

<script>
import SupplierSelectDialog from "@/views/ni/base/components/SupplierSelectDialog";
import Emitter from "element-ui/src/mixins/emitter";

export default {
  name: "SupplierSelect",
  mixins: [Emitter],
  components: {
    SupplierSelectDialog,
  },
  props: {
    value: {
      type: String,
    },
    size: {
      type: String,
      default: "mini",
    },
    code: {
      type: String,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    multiple: {
      type: Boolean,
      default: true,
    },
    params: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      show: false,
    };
  },
  computed: {
    codes: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit("input", val);
      },
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  methods: {
    handleClear() {
      this.codes = "";
      this.$emit("clear");
    },
    handleFocus() {
      if (!this.disabled) {
        this.$refs.supplierSelectRef.init();
      }
    },
    handleSupplierSubmit(selectList) {
      if (selectList) {
        const names = selectList.map((item) => item.name);
        this.codes = names.join(",");
        this.$emit("submit", selectList);
        this.$nextTick(() => {
          this.dispatch("ElFormItem", "el.form.blur", [this.codes]);
        });
      }
      this.show = false;
    },
  },
};
</script>

<style scoped></style>

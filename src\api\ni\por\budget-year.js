import request from "@/router/axios";

export const getBudgetTree = (params) => {
  return request({
    url: "/api/ni/por/budget/tree",
    method: "get",
    params,
  });
};

export const getList = (current, size, params, deptId) => {
  return request({
    url: "/api/ni/por/budget/year/page",
    method: "get",
    params: {
      ...params,
      deptId,
      current,
      size,
    },
  });
};

export const getBasicPage = (current, size, params, deptId) => {
  return request({
    url: "/api/ni/por/budget/year/basic/page",
    method: "get",
    params: {
      ...params,
      deptId,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/ni/por/budget/year/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const remove = (ids) => {
  return request({
    url: "/api/ni/por/budget/remove",
    method: "post",
    params: {
      ids,
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/ni/por/budget/year/save",
    method: "post",
    data: row,
  });
};

export const update = (row) => {
  return request({
    url: "/api/ni/por/budget/year/update",
    method: "post",
    data: row,
  });
};

export const getDetailByProcessInsId = (processInsId) => {
  return request({
    url: "/api/ni/por/budget/year/detailByProcessInsId",
    method: "get",
    params: {
      processInsId,
    },
  });
};

export const apply = (id, processDefKey) => {
  return request({
    url: "/api/ni/por/budget/year/apply",
    method: "post",
    params: {
      processDefKey,
      id,
    },
  });
};

export const updatePerson = (row) => {
  return request({
    url: "/api/ni/por/budget/year/updatePerson",
    method: "post",
    data: row,
  });
};
export const getPrintData = (id) => {
  return request({
    url: "/api/ni/por/budget/year/getPrintData",
    method: "get",
    params: {
      id,
    },
  });
};
export const getItemsPrintData = (budgetId) => {
  return request({
    url: "/api/ni/por/budget/year/getItemsPrintData",
    method: "get",
    params: {
      budgetId,
    },
  });
};
export const finish = (id) => {
  return request({
    url: "/api/ni/por/budget/year/finish",
    method: "post",
    params: {
      id,
    },
  });
};

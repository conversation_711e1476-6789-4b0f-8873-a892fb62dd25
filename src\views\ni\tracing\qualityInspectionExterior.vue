<template>
  <basic-container>

    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :search.sync="query"
      v-model="form"
      ref="crud"
      @search-change="handleSearchChange"
      @search-reset="handleSearchReset"
      @selection-change="handleSelectionChange"
      @current-change="handlePageChange"
      @size-change="handleSizeChange"
      @refresh-change="loadData"
      @on-load="loadData"
    >

    <template #menuLeft>
        <el-button
          type="primary"
          size="small"
          icon="el-icon-check"
          @click="handleSelectedPrint"
          :disabled="selectionList.length === 0"
          style="margin-left: 10px;"
        >打印</el-button>

        <el-radio-group v-model="reportType" size="mini" @input="loadData({ currentPage: 1 })">
          <el-radio-button label="outside">对外报告</el-radio-button>
          <el-radio-button label="inside">对内报告</el-radio-button>
        </el-radio-group>

      </template>


      <template #menu="{ row }">        
        <el-button
          type="text"
          icon="el-icon-setting"
          size="mini"
          plain
          :disabled="row.publishStatus == 0"
          style="border: 0; background-color: transparent !important"
          @click.stop="handleModifiedShow(row)"
        >
          修改对外报告
        </el-button>
      </template>
    </avue-crud>

    <el-dialog
      title="修改对外展示质检报告"
      :visible.sync="modifiedVisible"
      :modal="false"
      :close-on-click-modal="false"
      width="60%"
    >
      <quality-inspection-modified
        v-if="modifiedVisible"
        :modified-inspection="modifiedInspection"
        @submit-modified="submitModified"
        @close-modified="closeModified"
      />
    </el-dialog>
    <el-dialog
      title="修改对外展示质检报告"
      :visible.sync="modifiedVisibleOs"
      :modal="false"
      :close-on-click-modal="false"
      width="60%"
    >
      <quality-inspection-modified-os
        v-if="modifiedVisibleOs"
        :modified-inspection="modifiedInspection"
        @submit-modified="submitModified"
        @close-modified="closeModified"
      />
    </el-dialog>

  </basic-container>
</template>

<script>
import { getModifiedList,getDetail as getModifedDetail,update as updateModified} from "@/api/ni/tracing/qualityInspectionModified";
import option from "@/const/ni/tracing/qualityInspectionExterior";
import QualityInspectionModified from "./qualityInspectionModified.vue";
import QualityInspectionModifiedOs from "./qualityInspectionModifiedOS.vue";
import { loadPrintTemplate } from "@/api/system/printTemplate";
import { getReportDetail } from "@/api/ni/tracing/productionBatch";
import { dateFormat } from "@/util/date";

export default {
  components: {
    QualityInspectionModified,
    QualityInspectionModifiedOs,
  },
  data() {
    return {
      form: {},
      query: {},
      loading: false,
      page: {
        currentPage: 1,
        pageSize: 100,
        total: 0,
      },
      selectionList: [],
      reportType: 'outside',
      option: {
        ...option,
        column: option.column.map(item => ({
          ...item,
          editDisplay: false,
          delDisplay: false,
        })),
        addBtn: false,
        editBtn: false,
        delBtn: false,
        menu: true,
        menuWidth: 260,
        searchIndex: 3, 
      },
      data: [],
      printTemplate: null,
      currentPrintRow: null,
      printDialogVisible: false,
      printDataList: [],
      selectedPrintData: null,
      printIds: [],
      modifiedVisible: false,
      modifiedVisibleOs: false,
      modifiedInspection: {},
    };
  },
  created() {
    loadPrintTemplate("ni_tracing_production_batch").then((res) => {
      this.printTemplate = JSON.parse(res.data.data.content);
    });
  },

  methods: {
   
    // 搜索控制
    handleSearchChange(params,done) {
      this.query = params;
      this.loadData({ currentPage: 1 });
      done();
    },
    handleSearchReset(done) {
      this.query = {};
      this.loadData({ currentPage: 1 });
      done();
    },
    
    // 选择控制
    handleSelectionChange(list) {
      this.selectionList = list;
    },
    
    // 分页控制
    handlePageChange(currentPage) {
      this.loadData({ currentPage });
    },
    handleSizeChange(pageSize) {
      this.loadData({ pageSize });
    },
    
    // 数据加载
    loadData({ currentPage = this.page.currentPage, pageSize = this.page.pageSize } = {}) {
      this.loading = true;
      this.page.currentPage = currentPage;
      this.page.pageSize = pageSize;
      if (this.query.batchEndTime) {
        this.query.batchEndTime = this.query.batchEndTime.map((item) => {
          if(item && typeof item === 'string'){
            item = new Date(item);
          }
          return item ? dateFormat(item, "yyyy-MM-dd hh:mm:ss") : null;
        });
      }
      this.query.reportType = this.reportType;
      getModifiedList(currentPage, pageSize, this.query).then((res) => {
        const { data } = res.data;
        this.data = data.records.map(item => ({
          ...item,
          attach: JSON.parse(item.attach || '[]')
        }));
        this.page.total = data.total;
        this.loading = false;
        console.log(this.loading)
      });
    },

    async handleSelectedPrint(){
      if (!this.printTemplate) {
        this.$message.error("打印模板加载失败，请联系管理员");
        return;
      }
      const domesticReports = this.selectionList.filter(row => row.area === 'CN');
      if (domesticReports.length === 0) {
        this.$message.warning("所选报告中没有国内质检报告");
        return;
      }
        if(this.selectionList.length == 1){
          console.log("选择一个")
          try {
            const { value: quantity } = await this.$prompt(
              '请输入批次箱数', 
              '打印设置', 
            {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              inputValue: '40',
              inputPattern: /^[1-9]\d*$/,
              inputErrorMessage: '请输入正整数'
            });
      
            if (quantity) {
              this.getPrintData(parseInt(quantity));
            }
          } catch (error) {
          // 用户取消操作
          }
          
        }else{
          try {
            await this.$confirm("当前打印功能仅支持打印国内质检报告，系统将自动进行过滤，是否继续？", {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
            });

            this.getPrintData(null);
            this.printDialogVisible = false;

          } catch (error) {
            if (error === 'cancel' || error === 'action cancelled') {
      
            } else {
              this.$message.error("获取报告数据失败，请重试");
            }
            this.printDialogVisible = false;
          }
        }
    },

    async getPrintData(quantity){
      this.printIds = this.selectionList.map(row => row.id);
            const res = await getReportDetail(this.printIds,quantity);
            const printData = res.data.data;
            if (!printData) {
              this.$message.error("获取打印数据失败");
              return;
            }
            const hiprintTemplate = new hiprint.PrintTemplate({
              template: this.printTemplate,
            });
            hiprintTemplate.print(printData);
    },
      
    handleModifiedShow(row) {
      getModifedDetail(row.originInspectionId).then((res) => {
        this.modifiedInspection = res.data.data;
        this.showModified(row);
      });
    },
    showModified(row) {
      console.log(row.area)
      if(row.area == 'CN'){
        this.modifiedVisible = true;
      }else{
        this.modifiedVisibleOs = true;
      }
    },
    closeModified() {
      this.modifiedVisible = false;
      this.modifiedVisibleOs = false;
    },
    submitModified(modified) {
      updateModified(modified).then(() => {
        this.$message({
          type: "success",
          message: "修改成功!",
        });
        this.closeModified();
        this.modifiedInspection = {};
        this.query = {};
        this.loadData({ currentPage: 1 });
      });
    },

  },
};
</script>
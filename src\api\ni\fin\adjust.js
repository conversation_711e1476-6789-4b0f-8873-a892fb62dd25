import request from "@/router/axios";

export const getList = (current, size, params) => {
  return request({
    url: "/api/ni/fin/adjust/list",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/ni/fin/adjust/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const remove = (ids) => {
  return request({
    url: "/api/ni/fin/adjust/remove",
    method: "post",
    params: {
      ids,
    },
  });
};

export const used = (ids,used) => {
  return request({
    url: "/api/ni/fin/adjust/used",
    method: "post",
    params: {
      ids,used
    },
  });
};
export const add = (row) => {
  return request({
    url: "/api/ni/fin/adjust/submit",
    method: "post",
    data: row,
  });
};

export const update = (row) => {
  return request({
    url: "/api/ni/fin/adjust/submit",
    method: "post",
    data: row,
  });
};


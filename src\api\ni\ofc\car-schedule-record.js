import request from "@/router/axios";

export const getPage = (current, size, params) => {
  return request({
    url: "/api/ni/ofc/car/schedule/record/page",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

// export const getDetail = (serialNo) => {
//   return request({
//     url: "/api/ni/ofc/car/schedule/record/detail",
//     method: "get",
//     params: {
//       serialNo,
//     },
//   });
// };
export const getDetail = (id,condition) => {
  return request({
    url: "/api/ni/ofc/car/schedule/record/detail",
    method: "get",
    params: {
      id,
      condition
    },
  });
};

  export const remove = (ids) => {
    return request({
      url: "/api/ni/ofc/car/schedule/record/remove",
      method: "post",
      params: {
        ids,
      },
    });
  };

  export const registerSave = (row) => {
    return request({
      url: "/api/ni/ofc/car/schedule/record/registerSave",
      method: "post",
      data:row
    });
  };

  export const update = (row) => {
    return request({
      url: "/api/ni/ofc/car/schedule/record/updateWeb",
      method: "post",
      data:row,
      // params: {
      //   serialNo,
      //   row
      // },
    });
  };

  export const itemUpdate = (row) => {
    return request({
      url: "/api/ni/ofc/car/schedule/record/rowUpdate",
      method: "post",
      data: row,
    });
  };
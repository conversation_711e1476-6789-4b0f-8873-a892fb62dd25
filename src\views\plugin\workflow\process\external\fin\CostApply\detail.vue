<template>
  <basic-container>
    <avue-skeleton :loading="waiting" avatar :rows="8">
      <avue-affix id="avue-view" :offset-top="114">
        <div class="header">
          <avue-title :value="process.processDefinitionName"></avue-title>
          <div v-if="process.status != 'todo'">
            主题：
            <avue-select
              v-model="theme"
              size="mini"
              :clearable="false"
              :dic="themeList"
            ></avue-select>
          </div>
        </div>
      </avue-affix>
      <el-tabs v-model="activeName">
        <el-tab-pane label="申请信息" name="first">
          <el-card shadow="never">
            <div
              id="printBody"
              :class="process.status !== 'todo' ? `wf-theme-${theme}` : ''"
            >
              <avue-form
                v-if="
                  option &&
                  ((option.column && option.column.length > 0) ||
                    (option.group && option.group.length > 0))
                "
                v-model="form"
                ref="form"
                :defaults.sync="defaults"
                :option="option"
                :upload-preview="handleUploadPreview"
              >
                <template #supplierId="{ disabled, size }">
                  <supplier-select
                    v-model="form.supplierId"
                    :disabled="disabled"
                    :size="size"
                  />
                </template>
                <template #projectId="{ disabled, size }">
                  <project-select
                    v-model="form.projectId"
                    :size="size"
                    :params="{ status: 9 }"
                    :disabled="disabled"
                    @confirm="projectConfirm"
                    @clear="projectClear"
                  />
                </template>
                <template #budgetId="{ disabled, size }">
                  <un-finish-budget-select
                    v-model="form.budgetId"
                    :size="size"
                    :disabled="disabled"
                    @confirm="handleBudgetConfirm($event, form)"
                    @clear="handleBudgetClear"
                  />
                </template>
              </avue-form>
            </div>
          </el-card>
          <el-card
            shadow="never"
            style="margin-top: 20px"
            v-if="process.status === 'todo'"
          >
            <wf-examine-form
              ref="examineForm"
              :comment.sync="comment"
              :process="process"
              @user-select="handleUserSelect"
            ></wf-examine-form>
          </el-card>
        </el-tab-pane>
        <el-tab-pane label="流转信息" name="second">
          <el-card shadow="never" style="margin-top: 5px">
            <wf-flow :flow="flow"></wf-flow>
          </el-card>
        </el-tab-pane>
        <el-tab-pane label="流程跟踪" name="third">
          <template v-if="activeName === 'third'">
            <el-card shadow="never" style="margin-top: 5px">
              <wf-design
                ref="bpmn"
                style="height: 500px"
                :options="bpmnOption"
              ></wf-design>
            </el-card>
          </template>
        </el-tab-pane>
      </el-tabs>
    </avue-skeleton>

    <!-- 底部按钮 -->
    <wf-button
      :loading="submitLoading"
      :button-list="buttonList"
      :process="process"
      :comment="comment"
      @examine="handleExamine"
      @user-select="handleUserSelect"
      @print="handlePrint"
      @rollback="handleRollbackTask"
      @terminate="handleTerminateProcess"
      @withdraw="handleWithdrawTask"
    ></wf-button>
    <!-- 人员选择弹窗 -->
    <user-select
      ref="user-select"
      :check-type="checkType"
      :default-checked="defaultChecked"
      @onConfirm="handleUserSelectConfirm"
    ></user-select>
    <budget-item-dialog
      ref="porBudgetItemRef"
      multiple
      :params="{ used: false }"
      @confirm="handleItemSelect"
    />
  </basic-container>
</template>

<script>
import WfExamineForm from "@/views/plugin/workflow/process/components/examForm.vue";
import WfButton from "@/views/plugin/workflow/process/components/button.vue";
import WfFlow from "@/views/plugin/workflow/process/components/flow.vue";
import userSelect from "@/views/plugin/workflow/process/components/user-select";

import exForm from "@/views/plugin/workflow/mixins/ex-form";
import theme from "@/views/plugin/workflow/mixins/theme";
import MaterialSelect from "@/views/ni/base/components/MaterialSelect";
import ProjectSelect from "@/views/ni/project/components/MyProjectSelect";
import BudgetItemDialog from "@/views/ni/por/components/BudgetItemDialog";
import UnFinishBudgetSelect from "@/views/ni/por/components/UnFinishBudgetSelect";
import { getList as getAttachList } from "@/api/resource/attach";
import SupplierSelect from "@/views/ni/base/components/SupplierSelect1.vue";

export default {
  mixins: [exForm, theme],
  components: {
    SupplierSelect,
    userSelect,
    WfExamineForm,
    WfButton,
    WfFlow,
    MaterialSelect,
    ProjectSelect,
    BudgetItemDialog,
    UnFinishBudgetSelect,
  },
  watch: {
    "$route.query.p": {
      immediate: true,
      handler(val) {
        if (val) {
          this.submitLoading = true;
          Object.keys(this.form).forEach((key) => (this.form[key] = ""));
          const param = JSON.parse(Buffer.from(val, "base64").toString());
          const { taskId, processInsId } = param;
          if (processInsId) {
            this.getDetail(taskId, processInsId);
          }
        }
      },
    },
  },
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      console.log(from, "from");
      //  这里的vm指的就是vue实例，可以用来当做this使用
      vm.fromPath = from.path; //获取上一级路由的路径
    });
  },
  computed: {
    showExamForm() {
      const { hideComment, hideCopy, hideExamine } = this.process;
      return !hideComment || !hideCopy || !hideExamine;
    },
    itemIds() {
      const ids = new Set(this.form.items.map((ele) => ele.budgetItemId));
      return Array.from(ids).join(",");
    },
  },
  data() {
    return {
      fromPath: "",
      activeName: "first",
      defaults: {},
      form: {
        items: [],
      },
      option: {
        detail: true,
        labelWidth: 120,
        calcHeight: 30,
        size: "mini",
        span: 8,
        menuBtn: false,
        column: [
          {
            label: "申请人",
            display: true,
            span: 8,
            prop: "createUserName",
            readonly: true,
          },
          {
            label: "申请部门",
            display: true,
            span: 8,
            prop: "createDeptName",
            readonly: true,
          },
          {
            label: "账套",
            prop: "brand",
            type: "radio",
            minWidth: 90,
            span: 8,
            value: "1",
            dicUrl: "/api/blade-system/dict-biz/dictionary?code=ni_brand",
            disabled: true,
            props: {
              label: "dictValue",
              value: "dictKey",
            },
          },
          {
            label: "申请主题",
            prop: "title",
            overHidden: true,
            minWidth: 135,
            span: 8,
            rules: [
              {
                required: true,
                message: "请输入",
                trigger: "blur",
              },
            ],
          },
          {
            label: "申请编号",
            prop: "serialNo",
            overHidden: true,
            minWidth: 135,
            span: 8,
            search: true,
            disabled: true,
          },
          {
            label: "申请类型",
            prop: "type",
            placeholder: " ",
            type: "select",
            dicUrl: "/api/ni/por/type/listWithPermission",
            props: {
              label: "name",
              value: "code",
              desc: "code",
            },
            rules: [
              {
                required: true,
                message: "请选择申请类型",
                trigger: "blur",
              },
            ],
          },
          {
            label: "供应商",
            prop: "supplierId",
            rules: [
              {
                required: true,
                message: "请选择供应商",
                trigger: "blur",
              },
            ],
          },
          {
            label: "关联项目",
            prop: "projectId",
            placeholder: " ",
            filterable: true,
            span: 8,
          },

          {
            label: "关联预算",
            labelTip: "只关联已审核的预算",
            prop: "budgetId",
            placeholder: " ",
            dicData: [],
            type: "tree",
            props: {
              label: "title",
              value: "id",
            },
            filterable: true,
            rules: [
              {
                required: true,
                message: "请输入",
                trigger: "blur",
              },
            ],
          },
          {
            label: "附件",
            type: "upload",
            propsHttp: {
              res: "data",
              url: "attachId",
              name: "originalName",
            },
            dragFile: true,
            action:
              "/api/blade-resource/oss/endpoint/put-file-attach?code=private",
            display: true,
            span: 24,
            showFileList: true,
            multiple: true,
            limit: 10,
            prop: "attach",
          },
          {
            label: "",
            labelWidth: 0,
            prop: "items",
            type: "dynamic",
            span: 24,
            children: {
              align: "center",
              headerAlign: "center",
              showSummary: true,
              sumColumnList: [
                {
                  name: "num",
                  type: "sum",
                  decimals: 1,
                },
                {
                  name: "amount",
                  type: "sum",
                },
              ],
              rowAdd: () => {
                this.itemAdd();
              },
              rowDel: (row, done) => {
                done();
              },
              column: [
                {
                  label: "费用名称",
                  prop: "materialName",
                  placeholder: " ",
                  overHidden: true,
                  cell: true,
                  rules: [
                    {
                      required: true,
                      message: "请输入费用名称",
                      trigger: "blur",
                    },
                  ],
                },
                {
                  label: "用途",
                  prop: "purpose",
                  type: "textarea",
                  minRows: 1,
                  placeholder: " ",
                  overHidden: true,
                  cell: true,
                },
                {
                  label: "编码",
                  minWidth: 100,
                  placeholder: " ",
                  prop: "materialCode",
                  overHidden: true,
                  clearable: false,
                  cell: true,
                  rules: [
                    {
                      required: false,
                      message: "请选择编码",
                      trigger: "blur",
                    },
                  ],
                },
                {
                  label: "规格",
                  prop: "specification",
                  placeholder: " ",
                  overHidden: true,
                  disabled: true,
                },
                {
                  label: "数量",
                  prop: "num",
                  type: "number",
                  precision: 0,
                  placeholder: " ",
                  minWidth: 100,
                  cell: true,
                  rules: [
                    {
                      required: true,
                      message: "请输入数量",
                      trigger: "blur",
                    },
                  ],
                },
                {
                  label: "单价",
                  prop: "price",
                  type: "number",
                  controls: false,
                  precision: 2,
                  placeholder: " ",
                  cell: true,
                },
                {
                  label: "金额",
                  prop: "amount",
                  overHidden: true,
                  type: "number",
                  cell: true,
                  minWidth: 100,
                  precision: 2,
                  placeholder: " ",
                  rules: [
                    {
                      required: true,
                      message: "请输入金额",
                      trigger: "blur",
                    },
                  ],
                },
              ],
            },
          },
        ],
      },
      vars: [], // 需要提交的字段
      submitLoading: false, // 提交时按钮loading
      actionItem: {},
      actionItemIndex: null,
    };
  },
  methods: {
    handleRefreshAttach() {
      getAttachList({
        businessName: "ni_fin_cost_apply_item",
        businessKey: this.actionItem.id,
      }).then((res) => {
        const data = res.data.data;
        console.log(data);
        this.actionItem.attach = data.map((item) => {
          return {
            label: item.originalName,
            value: item.id,
          };
        });
        this.form.items.splice(this.actionItemIndex, 1, { ...this.actionItem });
      });
    },
    handleMaterialSubmit(selectList, row1) {
      if (selectList.length > 0) {
        const row = selectList[0];
        this.$nextTick(() => {
          this.$set(row1, "materialName", row.name);
          this.$set(row1, "materialCode", row.code);
          this.$set(row1, "specification", row.specification);
          this.$set(row1, "quality", row.quality);
          this.$set(row1, "gb", row.gb);
          this.$set(row1, "unit", row.unit);
          this.$set(row1, "cost", row.cost);
        });
      }
    },
    handleBudgetClear() {
      this.form.budgetId = null;
      this.form.items = [];
    },
    handleBudgetConfirm(selectList, row) {
      if (selectList && selectList.length > 0) {
        row.projectId = selectList[0].projectId;
        row.projectTitle = selectList[0].projectTitle;
        row.projectSerialNo = selectList[0].projectSerialNo;
        row.brand = selectList[0].brand;
        row.subType = selectList[0].type;
      }
    },
    projectClear() {
      const column = this.findObject(this.option.column, "budgetId");
      column.dicData = [];
      this.form.budgetId = null;
    },
    projectConfirm(selectionList) {
      if (selectionList) {
        this.form.projectTitle = selectionList[0].title;
        this.buildTitle(this.form.type, this.form.projectTitle);
      }
    },
    sumAmount() {
      this.$nextTick(() => {
        const itemAmount = this.form.items.reduce((acc, cur) => {
          return Number(acc) + Number(cur.amount);
        }, 0);
        this.form.amount = Number(itemAmount.toFixed(2));
      });
    },
    handleItemSelect(selectionList) {
      selectionList.forEach((item) => {
        this.form.items.push({
          budgetItemId: item.id,
          purpose: item.purpose,
          materialCode: item.materialCode,
          materialName: item.materialName,
          materialId: item.materialId,
          specification: item.specification,
          quality: item.quality,
          gb: item.gb,
          unit: item.unit,
          num: item.num,
          budgetNum: item.num,
          usedNum: item.applyNum ? item.applyNum : 0,
          amount: item.amount,
          price: item.price,
          unitDicData: item.unitDicData,
          remark: item.remark,
          cost: item.cost,
        });
      });
      this.sumAmount();
    },
    itemAdd() {
      if (!this.form.budgetId) {
        this.$message.warning("请选择预算");
        return;
      }
      this.$refs.porBudgetItemRef.init(this.form.budgetId);
    },
    // 获取任务详情
    getDetail(taskId, processInsId) {
      this.getTaskDetail(taskId, processInsId).then((res) => {
        const { process } = res;
        const { variables, status, assignee } = process;

        const option = this.option;
        option.menuBtn = false;
        const { column, group } = option;
        if (status !== "todo") {
          // 已办，删除字段默认值
          option.detail = true;
        } else {
          this.vars = column.vars || [];
        }
        for (let key in variables) {
          if (!variables[key]) delete variables[key];
        }
        this.option.detail = !(
          assignee === this.userInfo.user_id &&
          ["recall", "reject"].includes(this.process.processIsFinished)
        );
        const serialNumber = this.findObject(
          this.option.column,
          "serialNumber"
        );
        if (
          option.column &&
          process.variables &&
          process.variables.serialNumber &&
          (!serialNumber || serialNumber === -1)
        ) {
          option.column.unshift({
            label: "流水号",
            prop: "serialNumber",
            span: variables.auto ? 16 : 24,
            detail: true,
          });
        }
        this.option = option;
        this.form = variables;
        this.form.items.forEach((item) => (item.$cellEdit = false));
        this.waiting = false;
        this.submitLoading = false;
      });
    },
    // 审核
    handleExamine(pass) {
      this.submitLoading = true;
      this.$refs.form.validate((valid, done) => {
        if (valid) {
          const variables = {};
          this.sumAmount();
          this.option.column.forEach((v) => {
            if (this.form[v.prop]) variables[v.prop] = this.form[v.prop];
          });
          console.log(variables);
          this.handleCompleteTask(pass, variables)
            .then(() => {
              this.$message.success("处理成功");
              if (this.fromPath) {
                this.handleCloseTag(this.fromPath);
              } else this.handleCloseTag("/plugin/workflow/process/todo");
            })
            .catch(() => {
              done();
              this.submitLoading = false;
            });
        } else {
          done();
          this.submitLoading = false;
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
/deep/ .el-table__expand-icon {
  visibility: hidden;
}

/deep/ .el-link--inner {
  font-size: 12px;
}

/deep/ .el-upload-list__item-name {
  font-size: 12px;
}

.header {
  width: 100%;
  height: 50px;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 10px 10px 0;
}
</style>

<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      ref="crud"
      @row-del="rowDel"
      v-model="form"
      :permission="permissionList"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :before-open="beforeOpen"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template #createUserName="{ row }">
        {{ row.createUserName ? row.createUserName : "系统通知" }}
      </template>
      <template #category="{ row, size, label }">
        <el-tag :size="size" :type="rowTagType(row.category)"
          >{{ label }}
        </el-tag>
      </template>
      <template #isRead="{ row, size, label }">
        <el-tag :size="size" :type="rowTagType(row.isRead)">{{ label }}</el-tag>
      </template>
      <template #content="{ size, row }">
        <div>
          <el-tag
            v-if="row.isManagerResponse === 1"
            :size="size"
            :class="{
              'manager-response-tag': row.isManagerResponse === 1,
            }"
            :style="{
              animation:
                row.isManagerResponse === 1 ? 'pulse 2s infinite' : 'none',
            }"
          >
            需回复
          </el-tag>
          {{ replaceContent(row.content) }}
        </div>
      </template>
      <template #menuLeft>
        <el-radio-group v-model="isRead" size="mini" @input="onLoad(page)">
          <el-radio-button label="all">全部</el-radio-button>
          <el-radio-button label="1">已读</el-radio-button>
          <el-radio-button label="0">未读</el-radio-button>
        </el-radio-group>
      </template>
      <template #menu="{ row, index, size }">
        <el-button
          type="text"
          icon="el-icon-view"
          :size="size"
          v-if="row.status === 1 && !row.isManagerResponse"
          @click="handleView1(row)"
          >查 看
        </el-button>
        <el-button
          type="text"
          icon="el-icon-view"
          :size="size"
          v-else-if="[1].includes(row.status)"
          @click="handleView1(row)"
          >查 看
        </el-button>
        <el-button
          type="text"
          icon="el-icon-link"
          :size="size"
          v-if="row.jumpUrl"
          @click="handleJump(row.jumpUrl)"
          >跳 转
        </el-button>
      </template>
      <template slot-scope="{ size, type }" slot="menuForm"></template>
    </avue-crud>
    <notice-detail ref="noticeDetailRef" @read="onLoad(page)" />
  </basic-container>
</template>

<script>
import {
  add,
  getUserNotice,
  getUserNoticePage,
  remove,
  update,
} from "@/api/desk/notice";
import { mapGetters } from "vuex";
import { tagType } from "@/api/ni/pa/pa-common";
import { change2Read } from "@/api/desk/userNotice";
import NoticeDetail from "@/views/desk/components/NoticeDetail.vue";

export default {
  components: { NoticeDetail },
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        searchEnter: true,
        menuWidth: 150,
        size: "mini",
        searchSize: "mini",
        height: "auto",
        calcHeight: 30,
        dialogWidth: 950,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        searchIcon: true, // 搜索栏能否收缩
        searchIndex: 3, // 搜索按钮索引,超出的搜索栏收缩
        border: true,
        index: false,
        selection: true,
        excelBtn: true,
        dialogClickModal: false,
        // 操作栏
        menu: true,
        align: "center",
        menuAlign: "center",
        addBtn: false,
        editBtn: false,
        viewBtn: false,
        delBtn: false,
        column: [
          {
            label: "创建人",
            prop: "createUser",
            type: "select",
            dicUrl: `/api/blade-user/user-list`,
            props: {
              label: "name",
              value: "id",
            },
            filterable: true,
            search: true,
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "创建人",
            prop: "createUserName",
            span: 24,
            row: true,
            addDisplay: false,
            editDisplay: false,
            viewDisplay: true,
            width: 110,
          },
          {
            label: "通知标题",
            prop: "title",
            span: 24,
            row: true,
            search: true,
            width: 130,
            overHidden: true,
            rules: [
              {
                required: true,
                message: "请输入通知标题",
                trigger: "blur",
              },
            ],
          },
          {
            label: "通知时间",
            prop: "releaseTimeRange",
            type: "datetime",
            format: "yyyy-MM-dd hh:mm:ss",
            valueFormat: "yyyy-MM-dd hh:mm:ss",
            searchRange: true,
            hide: true,
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            search: true,
          },
          {
            label: "通知时间",
            prop: "releaseTime",
            type: "datetime",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
            width: 130,
            rules: [
              {
                required: true,
                message: "请输入通知时间",
                trigger: "blur",
              },
            ],
            addDisplay: false,
            editDisplay: false,
            viewDisplay: true,
          },
          {
            label: "阅读状态",
            prop: "isRead",
            type: "select",
            width: 70,
            dicData: [
              {
                label: "未读",
                value: 0,
              },
              {
                label: "已读",
                value: 1,
              },
            ],
            dataType: "number",
            display: false,
            disabled: true,
          },
          {
            label: "通知内容",
            prop: "content",
            overHidden: true,
            minRows: 6,
            span: 24,
            minWidth: 230,
          },
        ],
      },
      data: [],
      isRead: "0",
      read: {
        attach: [],
        visible: false,
        read: 0,
        props: {
          title: "title",
          lead: "lead",
          meta: "meta",
          body: "content",
        },
        comment: {
          option: {
            size: "mini",
            span: 24,
            emptyText: "取消",
            submitText: "已读",
            column: [
              {
                label: "回复",
                prop: "comment",
                type: "textarea",
                span: 24,
                minRows: 2,
                rules: [
                  {
                    required: true,
                    message: "请输入回复",
                    trigger: "blur",
                  },
                ],
              },
            ],
          },
          form: {},
        },
        isManagerResponse: 0,
        title: "",
        meta: "",
        content: "",
        lead: "",
      },
    };
  },
  computed: {
    ...mapGetters(["permission", "colorName"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.notice_add, false),
        viewBtn: this.vaildData(this.permission.notice_view, false),
        delBtn: this.vaildData(this.permission.notice_delete, false),
        editBtn: this.vaildData(this.permission.notice_edit, false),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  methods: {
    replaceContent(htmlContent) {
      if (htmlContent) {
        return htmlContent
          .replace(/<img.*?>/gi, "【图片】")
          .replace(/<[^>]+>/g, "")
          .replace(/\n/g, " ")
          .replace(/&nbsp;/g, " ")
          .trim();
      }
      return "";
    },
    rowSave(row, done, loading) {
      add(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          window.console.log(error);
          loading();
        }
      );
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          window.console.log(error);
          loading();
        }
      );
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen(done, type) {
      if (["edit", "view"].includes(type)) {
        getUserNotice(this.form.id).then((res) => {
          this.form = res.data.data;
          if (this.form.isRead === 0) {
            change2Read(this.form.userNoticeId).then(() => {
              this.onLoad(this.page);
            });
          }
        });
      }
      done();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      const { releaseTimeRange } = this.query;
      let values = {
        ...params,
        ...this.query,
      };
      if (releaseTimeRange) {
        values = {
          ...values,
          releaseTime_datege: releaseTimeRange[0],
          releaseTime_datelt: releaseTimeRange[1],
        };
        values.releaseTimeRange = null;
      }
      if (this.isRead === "all") {
        values.isRead = null;
      } else {
        values.isRead = this.isRead;
      }
      this.loading = true;
      getUserNoticePage(page.currentPage, page.pageSize, values).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
    handleView1(row) {
      this.$refs.noticeDetailRef.onView(row);
    },
    handleJump(url) {
      this.$router.push(url);
    },
    rowTagType(status) {
      return tagType(status);
    },
  },
};
</script>
<style>
.el-message-box__wrapper > .el-message-box.notice-box {
  width: 50%;
  height: 70vh;
  overflow: scroll;
}

.notice-box > .el-message-box__header > .el-message-box__title {
  text-align: center;
  font-size: 30px;
  font-style: italic;
}

.manager-response-tag {
  background: linear-gradient(45deg, #67c23a, #8de471);
  color: #fff;
  font-weight: bold;
}

.manager-response-tag {
  background: linear-gradient(45deg, #67c23a, #8de471);
  color: #fff;
  font-weight: bold;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.1);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 0.8;
  }
}
</style>

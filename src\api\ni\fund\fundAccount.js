import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/fund/fundAccount/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}


export const intoAccount = (row) => {
  return request({
    url: '/api/fund/fundIntoAccount/intoAccount',
    method: 'post',
    data: row
  })
}



export const getDetail = (id) => {
  return request({
    url: '/api/fund/fundAccount/detail',
    method: 'get',
    params: {
      id
    }
  })
}



export const getIntoAccountList = (current, size, params) => {
  return request({
    url: '/api/fund/fundIntoAccount/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}



export const getIntoAccountDetail = (id) => {
  return request({
    url: '/api/fund/fundIntoAccount/info',
    method: 'get',
    params: {
      id
    }
  })
}




export const remove = (ids) => {
  return request({
    url: '/api/fund/fundAccount/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/fund/fundAccount/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/fund/fundAccount/submit',
    method: 'post',
    data: row
  })
}


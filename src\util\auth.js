import Cookies from "js-cookie";

const TokenKey = "saber-access-token";
const RefreshTokenKey = "saber-refresh-token";
const prefix = window.location.port || "default";

export function getToken() {
  return Cookies.get(`${prefix}_${TokenKey}`);
}

export function setToken(token) {
  return Cookies.set(`${prefix}_${TokenKey}`, token);
}

export function getRefreshToken() {
  return Cookies.get(`${prefix}_${RefreshTokenKey}`);
}

export function setRefreshToken(token) {
  return Cookies.set(`${prefix}_${RefreshTokenKey}`, token);
}

export function removeToken() {
  return Cookies.remove(`${prefix}_${TokenKey}`);
}

export function removeRefreshToken() {
  return Cookies.remove(`${prefix}_${RefreshTokenKey}`);
}

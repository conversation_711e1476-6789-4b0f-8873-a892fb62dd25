<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      v-model="form"
      ref="crud"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
      :span-method="arraySpanMethod"
      :summary-method="summaryMethod"
      :row-class-name="tableRowClassName"
      @row-click="rowClick"
      :cell-style="cellStyle"
    >
      <template #billStatus="{ row, index }">
        <span
          v-if="
            (row.status === 2 ||
              row.soa === 1 ||
              !permission.alipay_bill_compare) &&
            !userInfo.role_name.includes('admin') &&
            !userInfo.role_name.includes('pa')
          "
        >
          {{ row.$billStatus }}
        </span>
        <el-dropdown
          v-else
          trigger="click"
          size="mini"
          @command="rowBillStatusChange($event, row)"
        >
          <span
            class="el-dropdown-link"
            :style="{ color: row.status === 2 ? '#fff' : '', fontSize: '12px' }"
          >
            {{ row.$billStatus
            }}<i class="el-icon-arrow-down el-icon--right"></i>
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
              v-for="item in billStatusDict"
              :key="item.dictKey"
              :command="item.dictKey"
              :disabled="Number(item.dictKey) === row.billStatus"
              >{{ item.dictValue }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </template>
      <template #note="{ row, index }">
        <template v-if="row.bills">
          <div v-for="(item, index) in row.bills" :key="index">
            <span v-if="item.note"> {{ item.note }} </span>
            <span v-else style="color: #6b6b6b">未填写</span>
          </div>
        </template>
        <span v-else>{{ row.note }}</span>
      </template>
      <template #balance="{ row, index }">
        <template v-if="row.bills">
          <div v-for="(item, index) in row.bills" :key="index">
            <span v-if="item.balance">
              {{
                Number(item.balance).toLocaleString("zh-CN", {
                  minimumFractionDigits: 2,
                })
              }}
            </span>
          </div>
        </template>
        <span v-else-if="row.balance">
          {{
            Number(row.balance).toLocaleString("zh-CN", {
              minimumFractionDigits: 2,
            })
          }}
        </span>
      </template>
      <template #expenditures="{ row, index }">
        <template v-if="row.bills">
          <div v-for="(item, index) in row.bills" :key="index">
            <span v-if="item.expenditures">
              {{
                Number(item.expenditures).toLocaleString("zh-CN", {
                  minimumFractionDigits: 2,
                })
              }}
            </span>
          </div>
        </template>
        <span v-else-if="row.expenditures">
          {{
            Number(row.expenditures).toLocaleString("zh-CN", {
              minimumFractionDigits: 2,
            })
          }}
        </span>
      </template>
      <template #revenue="{ row, index }">
        <template v-if="row.bills">
          <div v-for="(item, index) in row.bills" :key="index">
            <span v-if="item.revenue">
              {{
                Number(item.revenue).toLocaleString("zh-CN", {
                  minimumFractionDigits: 2,
                })
              }}
            </span>
          </div>
        </template>
        <span v-else-if="row.revenue">
          {{
            Number(row.revenue).toLocaleString("zh-CN", {
              minimumFractionDigits: 2,
            })
          }}
        </span>
      </template>
      <template #time="{ row, index }">
        <template v-if="row.bills">
          <div v-for="(item, index) in row.bills" :key="index">
            <span>{{ item.time }}</span>
          </div>
        </template>
        <span v-else>{{ row.time }}</span>
      </template>
      <template #type="{ row, index }">
        <template v-if="row.bills">
          <div v-for="(item, index) in row.bills" :key="index">
            <span>{{ item.type }}</span>
          </div>
        </template>
        <span v-else>{{ row.type }}</span>
      </template>
      <template #businessSerialNo="{ row, index }">
        <template v-if="row.bills">
          <div v-for="(item, index) in row.bills" :key="index">
            <span
              :style="
                row.billNum > 1
                  ? {
                      color: row.billStatus !== 2 ? colorName : '',
                      cursor: 'pointer',
                      textDecoration: 'underline',
                    }
                  : {}
              "
              @click="rowBusinessSerialNoShow(row)"
            >
              {{ item.businessSerialNo }}
            </span>
          </div>
        </template>
        <span
          v-else
          :style="
            row.billNum > 1
              ? {
                  color: row.billStatus !== 2 ? colorName : '',
                  cursor: 'pointer',
                  textDecoration: 'underline',
                }
              : {}
          "
          @click="rowBusinessSerialNoShow(row)"
        >
          {{ row.businessSerialNo }}
        </span>
      </template>
      <template #name="{ row, index }">
        <template v-if="row.bills">
          <div v-for="(item, index) in row.bills" :key="index">
            <span>{{ item.name }}</span>
          </div>
        </template>
        <span v-else>{{ row.name }}</span>
      </template>
      <template #account="{ row, index }">
        <template v-if="row.bills">
          <div v-for="(item, index) in row.bills" :key="index">
            <span>{{ item.account }}</span>
          </div>
        </template>
        <span v-else>{{ row.account }}</span>
      </template>
      <template #alipayBusinessNo="{ row, index }">
        <i
          class="el-icon-sort"
          :class="row.rowNo || row.status === 2 ? 'hover-icon' : ''"
          style="transform: rotate(90deg)"
          @click="rowLinkShow(row)"
        />
      </template>
      <template #rowNo="{ row, size }">
        <span
          v-if="
            row.rowNo || row.status === 2 || !permission.alipay_bill_compare
          "
        >
          {{ row.rowNo }}
        </span>
        <span
          v-else
          style="color: #6b6b6b; text-decoration: underline; cursor: pointer"
          @click="rowLinkOrderItem(row)"
        >
          未关联
        </span>
      </template>
      <template #adjustment="{ row, index, size }">
        <dict-tag
          v-if="row.adjustment"
          :size="size"
          v-model="row.adjustment"
          :dict="adjustmentDict"
          :dict-props="{
            label: 'dictValue',
            value: 'dictKey',
          }"
        />
      </template>
      <template #materialName="{ row, index }">
        <el-tag
          size="mini"
          type="danger"
          effect="dark"
          v-if="row.businessType && row.businessType === 'back'"
          >退换货
        </el-tag>
        <el-tag
          size="mini"
          type="warning"
          effect="plain"
          v-else-if="row.businessType && row.businessType === 'adjustment'"
          >调账
        </el-tag>
        <span>{{ row.materialName }}</span>
      </template>

      <template #adjustmentType="{ row }">
        <el-tag
          v-if="row.adjustmentType"
          size="mini"
          type="warning"
          effect="dark"
        >
          {{ adjustmentTypeDictKeyValue[row.adjustmentType] }}
        </el-tag>
        <el-tag v-else size="mini" type="info" effect="plain"> 未调账</el-tag>
      </template>
      <template #menu="{ row, size, index }">
        <el-dropdown
          v-if="permission.alipay_bill_compare && row.status === 1"
          @command="rowAdjustment($event, row)"
        >
          <el-button
            type="text"
            icon="el-icon-s-management"
            :size="size"
            @click="rowAdjustment('edit', row)"
            >调账<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="add">新增</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-button
          type="text"
          icon="el-icon-s-claim"
          :size="size"
          v-if="
            permission.alipay_bill_auditing &&
            Number(row.billStatus) > 1 &&
            row.status === 1
          "
          @click="rowAuditing(row, index)"
          >审核
        </el-button>
        <el-button
          type="text"
          icon="el-icon-s-release"
          :size="size"
          style="color: red"
          v-if="permission.alipay_bill_un_auditing && row.status === 2"
          @click="rowUnAuditing(row, index)"
          >弃审
        </el-button>
      </template>

      <template #amount="{ row, index }">
        <span v-if="row.amount && Number(row.amount) !== 0">{{
          Number(row.amount).toLocaleString("zh-CN", {
            minimumFractionDigits: 2,
          })
        }}</span>
      </template>
      <template #soa="{ row, index }">
        <el-tag size="mini" v-if="row.soa" effect="dark">是</el-tag>
        <el-tag size="mini" v-else effect="plain" type="danger">否</el-tag>
      </template>
      <template #menuLeft>
        <el-button
          type="warning"
          size="mini"
          icon="el-icon-refresh"
          plain
          v-if="permission.alipay_bill_load_bill"
          @click="handleLoadBill"
          >拉取对账单
        </el-button>
        <el-button
          type="primary"
          size="mini"
          icon="el-icon-toilet-paper"
          v-if="permission.alipay_bill_compare"
          @click="handleCompare"
          >对账
        </el-button>
        <el-button
          type="warning"
          size="mini"
          icon="el-icon-s-ticket"
          v-if="permission.alipay_bill_note"
          @click="handleNote"
          >添加批注
        </el-button>
        <el-dropdown @command="handleSoa">
          <el-button type="primary" size="mini">
            应付对账<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="add">新增</el-dropdown-item>
            <el-dropdown-item command="edit">补充</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-button
          type="primary"
          size="mini"
          icon="el-icon-download"
          plain
          @click="handleExport"
        >
          导出
        </el-button>
        <el-dropdown @command="handleWriteExport">
          <el-button type="primary" size="mini">
            核销导出<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="order">订单核销</el-dropdown-item>
            <el-dropdown-item command="alipay">支付宝核销</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-divider direction="vertical" />
        <el-radio-group v-model="billStatus" size="mini">
          <el-radio-button label="all">全部</el-radio-button>
          <el-radio-button label="2">已对账</el-radio-button>
          <el-radio-button label="1">未对账</el-radio-button>
        </el-radio-group>
      </template>
    </avue-crud>
    <el-dialog
      title="对账"
      append-to-body
      :visible.sync="check.visible"
      width="500px"
    >
      <avue-form
        v-if="check.visible"
        ref="form"
        :option="check.option"
        v-model="check.form"
        @submit="handleCompareSubmit"
      >
      </avue-form>
    </el-dialog>
    <el-dialog
      title="调账"
      append-to-body
      :visible.sync="adjustment.visible"
      width="500px"
    >
      <avue-form
        v-if="adjustment.visible"
        ref="form"
        :option="adjustment.option"
        v-model="adjustment.form"
        @submit="handleAdjustmentSubmit"
      >
        <template #row="{ size }">
          <el-tag :size="size" effect="dark">{{ adjustment.form.row }}</el-tag>
        </template>
      </avue-form>
    </el-dialog>
    <alipay-bill-table-dialog ref="billListRef" />
    <el-dialog
      :title="detail.title"
      append-to-body
      :visible.sync="detail.visible"
      width="1150px"
    >
      <avue-crud
        v-if="detail.visible"
        ref="detailRef"
        :table-loading="detail.loading"
        :data="detail.data"
        :option="detail.option"
        v-model="detail.form"
      >
      </avue-crud>
    </el-dialog>
    <el-dialog
      :title="link.title"
      append-to-body
      :visible.sync="link.visible"
      width="1150px"
    >
      <avue-crud
        v-if="link.visible"
        ref="linkRef"
        :table-loading="link.loading"
        :data="link.data"
        :option="link.option"
        v-model="link.form"
        :cell-style="linkCellStyle"
      >
        <template #menu="{ row, size, index }">
          <el-button
            type="text"
            icon="el-icon-s-management"
            :size="size"
            v-if="
              row.adjustment &&
              row.businessStatus === 1 &&
              permission.alipayBillAdjustment_audit
            "
            @click="rowAdjustmentAudit(row, index)"
          >
            审核
          </el-button>
          <el-button
            type="text"
            icon="el-icon-no-smoking"
            :size="size"
            v-else-if="
              row.adjustment &&
              row.businessStatus === 2 &&
              permission.alipayBillAdjustment_audit_cancel
            "
            @click="rowAdjustmentAuditCancel(row, index)"
          >
            弃审
          </el-button>
        </template>
        <template #adjustment="{ row, index, size }">
          <dict-tag
            v-if="row.adjustment"
            :size="size"
            v-model="row.adjustment"
            :dict="adjustmentDict"
            :dict-props="{
              label: 'dictValue',
              value: 'dictKey',
            }"
          />
        </template>
        <template #materialName="{ row, index }">
          <el-tag
            size="mini"
            type="danger"
            effect="dark"
            v-if="row.businessType && row.businessType === 'back'"
            >退换货
          </el-tag>
          <el-tag
            size="mini"
            type="warning"
            effect="plain"
            v-else-if="row.businessType && row.businessType === 'adjustment'"
            >调账
          </el-tag>
          <span>{{ row.materialName }}</span>
        </template>

        <template #adjustmentType="{ row }">
          <el-tag
            v-if="row.adjustmentType"
            size="mini"
            type="warning"
            effect="dark"
          >
            {{ adjustmentTypeDictKeyValue[row.adjustmentType] }}
          </el-tag>
          <el-tag v-else size="mini" type="info" effect="plain"> 未调账</el-tag>
        </template>
        <template #amount="{ row, index }">
          <span v-if="row.amount && Number(row.amount) !== 0">{{
            Number(row.amount).toLocaleString("zh-CN", {
              minimumFractionDigits: 2,
            })
          }}</span>
        </template>
        <template #soa="{ row, index }">
          <el-tag size="mini" v-if="row.soa" effect="dark">是</el-tag>
          <el-tag size="mini" v-else effect="plain" type="danger">否</el-tag>
        </template>
      </avue-crud>
    </el-dialog>
    <fin-pay-soa-form
      ref="paySoaRef"
      v-model="soaForm"
      type="2"
      @submit="refreshChange"
    />
    <pay-soa-select-dialog
      ref="alipaySoaSelectRef"
      :params="{ type: '2', confirm: false }"
      @confirm="handlePorSoaEditConfirm"
    />
    <el-dialog
      title="选择日期"
      append-to-body
      :visible.sync="syncBill.visible"
      width="300px"
    >
      <avue-form
        ref="form"
        :option="syncBill.option"
        v-model="syncBill.form"
        @submit="handleSyncBill"
      >
      </avue-form>
    </el-dialog>
    <el-dialog
      title="选择月份"
      append-to-body
      :visible.sync="write.visible"
      width="300px"
    >
      <avue-form
        ref="form"
        :option="write.option"
        v-model="write.form"
        @submit="handleWriteSubmit"
      >
      </avue-form>
    </el-dialog>
  </basic-container>
</template>

<script>
import {
  auditing,
  changeNote,
  getAlipayWriteOff,
  getList,
  getOrderWriteOff,
  getPageJoinOrderItem1,
  getSumData,
  loadBill,
  manualCompareBill,
  unAuditing,
} from "@/api/ni/fin/alipay-bill";
import {
  audit,
  auditCancel,
  getDetail as getAdjustment,
  submit as adjustmentBill,
} from "@/api/ni/fin/alipay-bill-adjustment";
import { mapGetters } from "vuex";
import OrderItemTableDialog from "@/views/ni/por/components/OrderItemTableDialog";
import AlipayBillTableDialog from "@/views/ni/fin/components/AlipayBillTableDialog";
import { getDetail1 } from "@/api/ni/base/supplier/supplierinfo";
import FinPaySoaForm from "@/views/ni/fin/components/FinPaySoaForm";
import PaySoaSelectDialog from "@/views/ni/fin/components/PaySoaSelectDialog";
import { addTo } from "@/api/ni/fin/pay-soa";

export default {
  components: {
    AlipayBillTableDialog,
    OrderItemTableDialog,
    FinPaySoaForm,
    PaySoaSelectDialog,
  },
  data() {
    return {
      supplerId: 0, //支付宝供应商id
      module: "ni_fin_alipay_bill",
      form: {},
      query: {},
      loading: true,
      parentId: 0,
      selectionList: [],
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
        pageSizes: [10, 20, 60, 100, 200, 300],
      },
      option: {
        height: "auto",
        showSummary: true,
        sumColumnList: [
          {
            label: "合计:",
            prop: "count",
            name: "date",
          },
          {
            name: "revenue",
            type: "sum",
            decimals: 2,
          },
          {
            name: "expenditures",
            type: "sum",
            decimals: 2,
          },
          {
            name: "amount",
            type: "sum",
            decimals: 2,
          },
        ],
        menuWidth: 150,
        searchEnter: true,
        searchLabelWidth: 110,
        addBtn: false,
        saveBtn: false,
        updateBtn: false,
        cancelBtn: false,
        editBtn: false,
        delBtn: false,
        align: "center",
        span: 12,
        searchIndex: 3,
        searchIcon: true,
        size: "mini",
        searchSize: "mini",
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: false,
        viewBtn: false,
        selection: true,
        dialogClickModal: false,
        column: [
          {
            label: "发生时间",
            prop: "date",
            search: true,
            searchRange: true,
            hide: true,
            showColumn: false,
            minWidth: 110,
            type: "date",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            overHidden: true,
          },
          {
            label: "发生时间",
            prop: "time",
            minWidth: 100,
            type: "datetime",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
            overHidden: true,
          },
          {
            label: "账务流水号",
            prop: "billSerialNo",
            type: "input",
            minWidth: 120,
            overHidden: true,
            hide: true,
          },
          {
            label: "业务流水号",
            prop: "businessSerialNo",
            minWidth: 110,
            search: true,
            overHidden: true,
          },
          {
            label: "商户订单号",
            prop: "orderCode",
            minWidth: 110,
            overHidden: true,
            hide: true,
          },
          {
            label: "商品名称",
            prop: "name",
            minWidth: 110,
            search: true,
            overHidden: true,
          },
          {
            label: "对方账号",
            prop: "account",
            overHidden: true,
            minWidth: 110,
            hide: true,
          },
          {
            label: "收入金额",
            prop: "revenue",
            minWidth: 100,
            overHidden: true,
          },
          {
            label: "支出金额",
            prop: "expenditures",
            minWidth: 110,
          },
          {
            label: "账户余额",
            prop: "balance",
            minWidth: 100,
            overHidden: true,
          },
          {
            label: "交易渠道",
            prop: "channel",
            overHidden: true,
            hide: true,
          },
          {
            label: "业务类型",
            prop: "type",
            minWidth: 80,
            overHidden: true,
          },
          {
            label: "批注",
            prop: "note",
            search: true,
            overHidden: true,
            width: 80,
          },
          {
            label: " ",
            prop: "alipayBusinessNo",
            width: 40,
          },
          {
            label: "序号",
            prop: "rowNo",
            width: 75,
            overHidden: true,
            search: true,
          },
          {
            label: "品名",
            prop: "materialName",
            overHidden: true,
            width: 80,
          },
          {
            label: "编码",
            prop: "materialCode",
            overHidden: true,
            hide: true,
            width: 80,
          },
          {
            label: "规格型号",
            prop: "specification",
            overHidden: true,
            width: 100,
          },
          {
            label: "采购人",
            prop: "purchaseUserName",
            overHidden: true,
            width: 80,
          },
          {
            label: "订单数量",
            prop: "num",
            width: 80,
          },
          {
            label: "金额",
            prop: "amount",
            fixed: "right",
            search: true,
            width: 100,
          },

          {
            label: "到货单号",
            prop: "arrivalSerialNo",
            overHidden: true,
            search: true,
            width: 100,
          },
          {
            label: "付款申请单号",
            prop: "payableApplySerialNo",
            overHidden: true,
            search: true,
            width: 100,
          },
          {
            label: "应付对账",
            prop: "soa",
            type: "select",
            dicData: [
              {
                label: "是",
                value: 1,
              },
              {
                label: "否",
                value: 0,
              },
            ],
            overHidden: true,
            search: true,
            width: 70,
          },
          {
            label: "应付对账编号",
            prop: "soaSerialNo",
            overHidden: true,
            search: true,
            width: 100,
          },
          {
            label: "对账状态",
            prop: "billStatus",
            type: "select",
            dataType: "number",
            width: 75,
            fixed: "right",
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_fin_compare_bill_status",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            overHidden: true,
            display: false,
          },
          {
            label: "调账类型",
            prop: "adjustment",
            search: true,
            type: "select",
            width: 81,
            fixed: "right",
            dicData: [],
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            display: false,
          },
          {
            label: "调账业务",
            prop: "adjustmentType",
            search: true,
            type: "select",
            width: 81,
            fixed: "right",
            dicData: [],
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            display: false,
          },
          {
            label: "备注",
            prop: "remark",
            overHidden: true,
          },
        ],
      },
      data: [],
      check: {
        visible: false,
        option: {
          labelWidth: 110,
          span: 24,
          size: "mini",
          emptyBtn: false,
          column: [
            {
              label: "对账状态",
              prop: "billStatus",
              type: "select",
              dataType: "number",
              width: 75,
              fixed: "right",
              dicUrl:
                "/api/blade-system/dict-biz/dictionary?code=ni_fin_compare_bill_status",
              props: {
                label: "dictValue",
                value: "dictKey",
              },
              rules: [
                {
                  required: true,
                  message: "请选择对账状态",
                  trigger: "blur",
                },
              ],
            },
            {
              label: "备注",
              prop: "adjustmentRemark",
              type: "textarea",
              minRows: 2,
            },
          ],
        },
        form: {},
      },
      ad: false,
      adjustment: {
        visible: false,
        option: {
          labelWidth: 110,
          span: 24,
          size: "mini",
          emptyBtn: false,
          column: [
            {
              label: "调账序号",
              prop: "row",
              display: true,
              readonly: true,
            },
            {
              label: "业务流水号",
              prop: "businessSerialNo",
              readonly: true,
            },
            {
              label: "商品名称",
              prop: "name",
              readonly: true,
            },
            {
              label: "收入金额",
              prop: "revenue",
              readonly: true,
            },
            {
              label: "支出金额",
              prop: "expenditures",
              readonly: true,
            },
            {
              label: "调账金额",
              labelTip: "金额区分正负",
              prop: "amount",
              type: "number",
              rules: [
                {
                  required: true,
                  message: "请输入调账金额",
                  trigger: "blur",
                },
              ],
            },
            {
              label: "调账类型",
              prop: "adjustment",
              type: "select",
              dicData: [],
              props: {
                label: "dictValue",
                value: "dictKey",
              },
              rules: [
                {
                  required: true,
                  message: "请选择类型",
                  trigger: "blur",
                },
              ],
            },
            {
              label: "业务类型",
              prop: "type",
              type: "select",
              dicData: [],
              props: {
                label: "dictValue",
                value: "dictKey",
              },
              dataType: "number",
              rules: [
                {
                  required: true,
                  message: "请选择业务类型",
                  trigger: "blur",
                },
              ],
            },
            {
              label: "备注",
              prop: "remark",
              type: "textarea",
              minRows: 2,
            },
          ],
        },
        form: {},
      },
      detail: {
        title: "支付宝对账单",
        visible: false,
        loading: false,
        data: [],
        option: {
          showSummary: true,
          sumColumnList: [
            {
              label: "合计:",
              prop: "count",
              name: "date",
            },
            {
              name: "revenue",
              type: "sum",
              decimals: 2,
            },
            {
              name: "expenditures",
              type: "sum",
              decimals: 2,
            },
            {
              name: "amount",
              type: "sum",
              decimals: 2,
            },
          ],
          search: false,
          menu: false,
          header: false,
          saveBtn: false,
          updateBtn: false,
          cancelBtn: false,
          editBtn: false,
          delBtn: false,
          align: "center",
          size: "mini",
          calcHeight: 30,
          tip: false,
          border: true,
          index: false,
          viewBtn: false,
          selection: false,
          dialogClickModal: false,
          column: [
            {
              label: "发生时间",
              prop: "time",
              minWidth: 100,
              type: "datetime",
              format: "yyyy-MM-dd HH:mm:ss",
              valueFormat: "yyyy-MM-dd HH:mm:ss",
              overHidden: true,
            },
            {
              label: "账务流水号",
              prop: "billSerialNo",
              type: "input",
              minWidth: 120,
              overHidden: true,
            },
            {
              label: "业务流水号",
              prop: "businessSerialNo",
              minWidth: 110,
              overHidden: true,
            },
            {
              label: "商户订单号",
              prop: "orderCode",
              minWidth: 110,
              overHidden: true,
              hide: true,
            },
            {
              label: "商品名称",
              prop: "name",
              minWidth: 110,
              overHidden: true,
            },
            {
              label: "对方账号",
              prop: "account",
              overHidden: true,
              minWidth: 110,
              hide: true,
            },
            {
              label: "收入金额",
              prop: "revenue",
              minWidth: 80,
              overHidden: true,
            },
            {
              label: "支出金额",
              prop: "expenditures",
              minWidth: 80,
            },
            {
              label: "账户余额",
              prop: "balance",
              minWidth: 80,
              overHidden: true,
            },
            {
              label: "交易渠道",
              prop: "channel",
              overHidden: true,
            },
            {
              label: "业务类型",
              prop: "type",
              minWidth: 80,
              overHidden: true,
            },
            {
              label: "备注",
              prop: "remark",
              type: "textarea",
              overHidden: true,
              minWidth: 100,
              minRows: 2,
            },
          ],
        },
      },
      link: {
        title: "支付宝对账单",
        visible: false,
        loading: false,
        data: [],
        option: {
          showSummary: true,
          sumColumnList: [
            {
              label: "合计:",
              prop: "count",
              name: "date",
            },
            {
              name: "amount",
              type: "sum",
              decimals: 2,
            },
          ],
          search: false,
          menuWidth: 100,
          header: false,
          saveBtn: false,
          updateBtn: false,
          cancelBtn: false,
          editBtn: false,
          delBtn: false,
          align: "center",
          size: "mini",
          calcHeight: 30,
          tip: false,
          border: true,
          index: false,
          viewBtn: false,
          selection: false,
          dialogClickModal: false,
          column: [
            {
              label: "序号",
              prop: "rowNo",
              width: 75,
              overHidden: true,
            },
            {
              label: "品名",
              prop: "materialName",
              overHidden: true,
              width: 80,
            },
            {
              label: "编码",
              prop: "materialCode",
              overHidden: true,
              hide: true,
              width: 80,
            },
            {
              label: "规格型号",
              prop: "specification",
              overHidden: true,
              width: 100,
            },
            {
              label: "采购人",
              prop: "purchaseUserName",
              overHidden: true,
              width: 80,
            },
            {
              label: "订单数量",
              prop: "num",
              width: 80,
            },
            {
              label: "金额",
              prop: "amount",
              fixed: "right",
              width: 100,
            },

            {
              label: "到货单号",
              prop: "arrivalSerialNo",
              overHidden: true,
              width: 100,
            },
            {
              label: "付款申请单号",
              prop: "payableApplySerialNo",
              overHidden: true,
              width: 100,
            },
            {
              label: "应付对账",
              prop: "soa",
              type: "select",
              dicData: [
                {
                  label: "是",
                  value: 1,
                },
                {
                  label: "否",
                  value: 0,
                },
              ],
              overHidden: true,
              width: 70,
            },
            {
              label: "应付对账编号",
              prop: "soaSerialNo",
              overHidden: true,
              width: 100,
            },
            {
              label: "对账状态",
              prop: "billStatus",
              type: "select",
              dataType: "number",
              width: 75,
              fixed: "right",
              dicUrl:
                "/api/blade-system/dict-biz/dictionary?code=ni_fin_compare_bill_status",
              props: {
                label: "dictValue",
                value: "dictKey",
              },
              overHidden: true,
              display: false,
            },
            {
              label: "调账类型",
              prop: "adjustment",
              type: "select",
              width: 81,
              fixed: "right",
              dicData: [],
              props: {
                label: "dictValue",
                value: "dictKey",
              },
              display: false,
            },
            {
              label: "调账业务",
              prop: "adjustmentType",
              type: "select",
              width: 81,
              fixed: "right",
              dicData: [],
              props: {
                label: "dictValue",
                value: "dictKey",
              },
              display: false,
            },
            {
              label: "备注",
              prop: "remark",
              overHidden: true,
            },
          ],
        },
      },
      needMergeArr: [
        "time",
        "billSerialNo",
        "businessSerialNo",
        "orderCode",
        "name",
        "account",
        "revenue",
        "expenditures",
        "balance",
        "channel",
        "type",
        "billStatus",
        "remark",
        "note",
      ], // 有合并项的列
      needMergeArr1: [
        "rowNo",
        "materialName",
        "materialCode",
        "specification",
        "num",
        "amount",
        "soa",
        "arrivalSerialNo",
        "payableApplySerialNo",
        "soaSerialNo",
        "adjustmentType",
        "adjustment",
      ], // 有合并项的列
      needMergeArr2: [
        "time",
        "billSerialNo",
        "businessSerialNo",
        "orderCode",
        "name",
        "account",
        "revenue",
        "expenditures",
        "balance",
        "channel",
        "type",
        "billStatus",
        "remark",
        "note",
        "alipayBusinessNo",
      ], // 有合并项的列
      rowMergeArrs: {},
      rowMergeArrs1: {},
      rowMergeArrs2: {},
      syncBill: {
        visible: false,
        option: {
          span: 24,
          size: "mini",
          emptyBtn: false,
          column: [
            {
              label: "",
              labelWidth: 0,
              prop: "time",
              type: "date",
              format: "yyyy-MM-dd",
              valueFormat: "yyyy-MM-dd",
              pickerOptions: {
                disabledDate(time) {
                  return time.getTime() > Date.now();
                },
              },
              rules: [
                {
                  required: true,
                  message: "请选择日期",
                  trigger: "blur",
                },
              ],
            },
          ],
        },
        form: {},
      },
      write: {
        visible: false,
        option: {
          span: 24,
          size: "mini",
          emptyBtn: false,
          column: [
            {
              label: "",
              labelWidth: 0,
              prop: "month",
              type: "month",
              format: "yyyy-MM",
              valueFormat: "yyyy-MM",
              rules: [
                {
                  required: true,
                  message: "请选择月份",
                  trigger: "blur",
                },
              ],
            },
          ],
        },
        form: {},
        orderExportColumn: [
          {
            label: "采购申请单号",
            prop: "porApplySerialNo",
          },
          {
            label:'采购申请人',
            prop:'porApplyUserName',
          },
          {
            label:'序号',
            prop:'porApplyRow',
          },
          {
            label:'分单号',
            prop:'porOrderItemNo',
          },
          {
            label:'采购人',
            prop:'porOrderUserName',
          },
          {
            label:'采购订单号',
            prop:'porOrderSerialNo',
          },
          {
            label:'品名',
            prop:'materialName',
          },
          {
            label:'编码',
            prop:'materialCode',
          },
          {
            label:'数量',
            prop:'num',
          },
          {
            label:'金额',
            prop:'amount',
          },
          {
            label:'支付宝业务流水号',
            prop:'alipayBusinessNo',
          },
          {
            label:'应付对账单号',
            prop:'finSoaSerialNo',
          },
          {
            label:'付款申请单号',
            prop:'finPayableApplySerialNo',
          },
          {
            label:'是否核销',
            prop:'state',
          },
        ],
        alipayExportColumn: [
          {
            label: "发生时间",
            prop: "time",
          },
          {
            label:'业务流水号',
            prop:'businessSerialNo',
          },
          {
            label:'商品名称',
            prop:'name',
          },
          {
            label:'对方账号',
            prop:'account',
          },
          {
            label:'收入金额（+元）',
            prop:'revenue',
          },
          {
            label:'支出金额（-元）',
            prop:'expenditures',
          },
          {
            label:'账户余额（元）',
            prop:'balance',
          },
          {
            label:'业务类型',
            prop:'type',
          },
          {
            label:'备注',
            prop:'remark',
          },
          {
            label:'批注',
            prop:'note',
          },
          {
            label:'是否核销',
            prop:'state',
          },

        ],
      },
      billStatusDict: [],
      billStatusDictKeyValue: {},
      adjustmentTypeDict: [],
      adjustmentTypeDictKeyValue: {},
      adjustmentMap: {},
      adjustmentDict: [],
      exportData: [],
      export: {
        data: [],
        column: [
          {
            label: "发生时间",
            prop: "time",
            type: "datetime",
          },
          {
            label: "业务流水号",
            prop: "businessSerialNo",
          },
          {
            label: "商品名称",
            prop: "name",
          },
          {
            label: "收入金额（+元）",
            prop: "revenue",
          },
          {
            label: "支出金额（-元）",
            prop: "expenditures",
          },
          {
            label: "账户余额（元）",
            prop: "balance",
          },
          {
            label: "业务类型",
            prop: "type",
          },
          {
            label: "申请序号",
            prop: "rowNo",
          },
          {
            label: "品名",
            prop: "materialName",
          },
          {
            label: "编码",
            prop: "materialCode",
          },
          {
            label: "规格型号",
            prop: "specification",
          },
          {
            label: "采购人",
            prop: "purchaseUserName",
          },
          {
            label: "订单数量",
            prop: "num",
          },
          {
            label: "金额",
            prop: "amount",
          },
          {
            label: "到货单号",
            prop: "arrivalSerialNo",
          },
          {
            label: "付款申请单号",
            prop: "payableApplySerialNo",
          },
          {
            label: "应付对账编号",
            prop: "soaSerialNo",
          },
          {
            label: "对账状态",
            prop: "billStatus",
          },
          {
            label: "调账类型",
            prop: "adjustmentType",
          },
          {
            label: "备注",
            prop: "remark",
          },
        ],
      },
      billStatus: "all",
      soaForm: {},
    };
  },
  watch: {
    billStatus: {
      handler() {
        this.onLoad(this.page);
      },
      immediate: false,
    },
  },
  computed: {
    ...mapGetters(["userInfo", "permission", "colorName"]),
    ids() {
      let ids = new Set();
      this.selectionList.forEach((ele) => {
        if (ele.alipayId) ids.add(ele.alipayId);
      });
      return Array.from(ids).join(",");
    },
  },
  created() {
    this.dicInit();
    getDetail1({ code: "050337" }).then((res) => {
      this.supplierId = res.data.data.id;
    });
  },
  methods: {
    //获取搜索的导出数据
    async getExportData() {
      const promises = [];
      this.exportData = [];
      let startTime = null;
      let endTime = null;
      console.log(this.query.date);
      if (this.query.date && this.query.date.length === 2) {
        startTime = this.query.date[0];
        endTime = this.query.date[1];
      }
      const promise = getSumData(startTime, endTime).then((res) => {
        const data = res.data.data;
        this.exportData = this.exportData.concat(data);
      });
      promises.push(promise);

      // 等待所有异步请求完成
      await Promise.all(promises);

      return this.exportData;
    },
    //数据导出
    async handleExportData() {
      let opt = {
        column: [
          {
            label: "汇总",
            prop: "",
          },
          {
            label: "收入笔数",
            prop: "revenueCount",
          },
          {
            label: "收入金额(元)",
            prop: "totalRevenue",
          },
          {
            label: "支出笔数",
            prop: "expenditureCount",
          },
          {
            label: "支出金额(元)",
            prop: "totalExpenditures",
          },
          {
            label: "总金额(元)",
            prop: "totalBalance",
          },
          {
            label: "订单金额(元)",
            prop: "orderAmount",
          },
          {
            label: "调账金额(元)",
            prop: "adjustmentAmount",
          },
          {
            label: "退换货(金额)",
            prop: "backItemAmount",
          },
        ],
      };

      await this.getExportData();
      console.log(this.exportData);
      this.$Export.excel({
        title: "支付宝账务汇总",
        columns: opt.column,
        data: this.exportData,
      });
      this.exportData = [];
    },
    handleSumExport() {
      this.$confirm("是否导出当前筛选的所有数据？", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.handleExportData();
      });
    },
    rowAdjustmentAuditCancel(row, index) {
      auditCancel(row.businessId).then(() => {
        this.link.data[index].businessStatus = 1;
        this.$message({
          type: "success",
          message: "操作成功!",
        });
      });
    },
    rowAdjustmentAudit(row, index) {
      audit(row.businessId).then(() => {
        this.link.data[index].businessStatus = 2;
        this.$message({
          type: "success",
          message: "操作成功!",
        });
      });
    },
    handlePorSoaEditConfirm(selectionList) {
      if (selectionList && selectionList.length === 1) {
        const data = {
          items: this.selectionList.map((item) => {
            let type = "1";
            if (item.orderItemId) {
              type = "1";
            } else if (item.backItemId) {
              type = "2";
            } else if (item.adjustmentId) {
              type = "3";
            }
            return {
              ...item,
              id: item.businessId,
              type,
              num: Number(item.num),
              amount: -Number(item.amount),
              orderNum: Number(item.num),
              billSerialNo: null,
            };
          }),
        };
        addTo(selectionList[0].alipayId, data).then((res) => {
          const data = res.data;
          this.onLoad(this.page);
          this.$message({
            type: data.code === 200 ? "success" : "danger",
            message: data.msg,
          });
        });
      }
    },
    rowBusinessSerialNoShow(row) {
      if (!row.billNum || row.billNum <= 1) {
        return;
      }
      const { businessSerialNo } = row;
      this.detail.loading = true;
      this.detail.title = `支付宝对账单[${businessSerialNo}]`;
      getList({ businessSerialNo }).then((res) => {
        this.detail.data = res.data.data;
        this.detail.loading = false;
      });
      this.detail.visible = true;
    },
    rowLinkShow(row) {
      if (!row.rowNo) {
        return;
      }
      let businessSerialNos = row.businessSerialNo;
      if (row.bills) {
        businessSerialNos = row.bills
          .map((item) => item.businessSerialNo)
          .join(",");
      }
      this.link.loading = true;
      this.link.title = `支付宝对账单[${businessSerialNos}]`;
      getPageJoinOrderItem1(1, 20, { businessSerialNos }).then((res) => {
        const businessMap = new Map();
        const records = res.data.data.records;
        // 遍历数据,只保留每个 businessId 的第一条记录
        records.forEach((item) => {
          if (!businessMap.has(item.businessId)) {
            businessMap.set(item.businessId, item);
          }
        });
        // 转换为数组
        this.link.data = Array.from(businessMap.values()).map((item) => {
          if (item.row) item.rowNo = item.row + (item.no ? "-" + item.no : "");
          if (!item.billStatus) item.billStatus = 1;
          if (!item.alipayBusinessNo)
            item.alipayBusinessNo = item.businessSerialNo;
          if (item.orderItemId) {
            item.amount = -Number(item.amount);
          }
          item.alipayId = item.id;
          item.id = null;
          return {
            ...item,
          };
        });
        this.link.loading = false;
      });
      this.link.visible = true;
    },
    rowLinkOrderItem(row) {
      alert("关联订单明细");
    },
    rowShowBack(row) {
      this.$refs.billListRef.onShow(row.businessSerialNo);
    },
    rowUnAuditing(row) {
      this.$confirm("是否确认取消审核?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return unAuditing(row.alipayId);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    rowAuditing(row) {
      this.$confirm("审核后将无法修改调账状态，是否继续?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return auditing(row.alipayId);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    rowBillStatusChange(billStatus, row) {
      if (row.bills) {
        this.$confirm("该操作将同时修改这几条记录,是否继续?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(() => {
          const businessSerialNos = row.bills
            .map((ele) => ele.businessSerialNo)
            .join(",");
          manualCompareBill({
            businessSerialNo: businessSerialNos,
            billStatus,
          }).then(() => {
            row.billStatus = Number(billStatus);
            this.$message.success("修改成功");
          });
        });
        return;
      }
      if (
        billStatus === "2" &&
        !row.businessId &&
        Number(row.revenue) + Number(row.expenditures) !== 0
      ) {
        this.$message({
          type: "warning",
          message: "该流水号收支未平,且未关联业务数据/调账，请重新选择！",
        });
        return;
      }
      if (
        Number(row.revenue) + Number(row.expenditures) === 0 &&
        (Number(row.amount) === 0 || row.amount == null)
      ) {
        manualCompareBill({
          businessSerialNo: row.businessSerialNo,
          billStatus,
        }).then(() => {
          row.billStatus = Number(billStatus);
          this.onLoad(this.page);
          this.$message.success("修改成功");
        });
      } else
        manualCompareBill({ ids: row.alipayId, billStatus }).then(() => {
          row.billStatus = Number(billStatus);
          this.$message.success("修改成功");
        });
    },
    dicInit() {
      this.$http
        .get(
          "/api/blade-system/dict-biz/dictionary?code=ni_fin_compare_bill_status"
        )
        .then((res) => {
          this.billStatusDict = res.data.data;
          this.billStatusDictKeyValue = this.billStatusDict.reduce(
            (acc, cur) => {
              acc[cur.dictKey] = cur.dictValue;
              return acc;
            },
            {}
          );
        });
      this.$http
        .get(
          "/api/blade-system/dict-biz/dictionary?code=ni_fin_adjustment_type"
        )
        .then((res) => {
          this.adjustmentTypeDict = res.data.data;
          const adjustmentType = this.findObject(
            this.option.column,
            "adjustmentType"
          );
          adjustmentType.dicData = this.adjustmentTypeDict;
          const adjustmentType1 = this.findObject(
            this.adjustment.option.column,
            "type"
          );
          adjustmentType1.dicData = this.adjustmentTypeDict;
          this.adjustmentTypeDictKeyValue = this.adjustmentTypeDict.reduce(
            (acc, cur) => {
              acc[cur.dictKey] = cur.dictValue;
              return acc;
            },
            {}
          );
        });
      this.$http
        .get("/api/blade-system/dict-biz/dictionary?code=ni_fin_adjustment")
        .then((res) => {
          this.adjustmentDict = res.data.data;
          const adjustment = this.findObject(this.option.column, "adjustment");
          adjustment.dicData = this.adjustmentDict;
          const adjustment1 = this.findObject(
            this.adjustment.option.column,
            "adjustment"
          );
          adjustment1.dicData = this.adjustmentDict;
        });
    },
    handleSyncBill(form, done) {
      loadBill(form.time)
        .then(() => {
          this.syncBill.visible = false;
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        })
        .finally(() => {
          done();
        });
    },
    async handleWriteExport(command) {
      this.write.form.type = command;
      this.write.visible = true;
    },
    handleWriteSubmit(form, done) {
      if (form.type === "order") {
        getOrderWriteOff(form.month).then((res) => {
          this.$Export.excel({
            title: "采购订单核销数据",
            columns: this.write.orderExportColumn,
            data: res.data.data.map((item) => {
              return {
                ...item,
                state:
                  item.state === 0
                    ? "未核销"
                    : item.state === 1
                    ? "部分核销"
                    : item.state === 2
                    ? "已核销"
                    : item,
              };
            }),
          });
        });
      } else if (form.type === "alipay") {
        getAlipayWriteOff(form.month).then((res) => {
          this.$Export.excel({
            title: "支付宝核销数据",
            columns: this.write.alipayExportColumn,
            data: res.data.data.map((item) => {
              return {
                ...item,
                state:
                  item.state === 0
                    ? "未核销"
                    : item.state === 1
                    ? "部分核销"
                    : item.state === 2
                    ? "已核销"
                    : item,
              };
            }),
          });
        });
      }
      done();
      this.write.visible = false;
    },
    async handleExport() {
      let msg =
        "确定将<span style='font-weight: bolder;color: #F56C6C'>选择数据</span>导出?";
      if (this.selectionList.length === 0) {
        msg =
          "确定要将<span style='font-weight: bolder;color: #F56C6C'>全部数据</span>导出?";
      }
      try {
        await this.$alert(msg, {
          dangerouslyUseHTMLString: true,
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        });
        const data = [];
        if (this.selectionList.length > 0) {
          this.selectionList.forEach((item) => {
            data.push({
              ...item,
              billStatus:
                item.billStatus == null
                  ? "未对账"
                  : this.billStatusDictKeyValue[item.billStatus],
              adjustmentType:
                this.adjustmentTypeDictKeyValue[item.adjustmentType],
            });
          });
        } else {
          const q = {
            ...this.params,
            ...this.query,
          };
          if (q.date != null && q.date.length === 2) {
            q.startDate = q.date[0];
            q.endDate = q.date[1];
            q.date = null;
          }
          q.billStatus = this.billStatus === "all" ? null : this.billStatus;
          const res = await getPageJoinOrderItem1(
            this.page.currentPage,
            100000,
            q
          );
          const data1 = res.data.data.records;
          data1.forEach((item) => {
            data.push({
              ...item,
              billStatus:
                item.billStatus == null
                  ? "未对账"
                  : this.billStatusDictKeyValue[item.billStatus],
              adjustmentType:
                this.adjustmentTypeDictKeyValue[item.adjustmentType],
            });
          });
        }
        await this.$Export.excel({
          title: "支付宝对账",
          columns: this.export.column,
          data,
        });
      } catch (e) {
        console.log(e);
      }
    },
    async rowAdjustment(command, row) {
      if (row.status === 2 && !this.permission.alipay_bill_compare) {
        return;
      }
      const rowNo = this.findObject(this.adjustment.option.column, "row");
      if (row.adjustmentId && command === "edit") {
        console.log(row.adjustmentId);
        rowNo.display = true;
        const res = await getAdjustment(row.adjustmentId);
        this.adjustment.form = res.data.data;
      } else {
        rowNo.display = false;
        this.adjustment.form = {
          time: row.time,
          name: row.name,
          businessSerialNo: row.businessSerialNo,
          alipayBusinessNo: row.businessSerialNo,
          revenue: row.revenue,
          expenditures: row.expenditures,
          num: 1,
        };
      }
      this.adjustment.visible = true;
    },
    handleSoa(value) {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      const unBill = this.selectionList.some((item) => item.billStatus !== 2);
      if (unBill) {
        this.$message.warning("数据中存在对账状态为未对账的数据，请重新选择");
        return;
      }
      const soa = this.selectionList.some((item) => item.soa);
      if (soa) {
        this.$message.warning("数据中存在已生成应付对账的数据,请重新选择");
        return;
      }
      const recharge = this.selectionList.some(
        (item) => item.adjustment === "8"
      );
      if (recharge) {
        this.$message.warning("数据中存在大额充值数据,请重新选择");
        return;
      }
      const businessIds = new Set();
      const items = this.selectionList
        .filter((item) => {
          if (!businessIds.has(item.businessId)) {
            businessIds.add(item.businessId);
            return true;
          }
          return false;
        })
        .map((item) => {
          let type = "1";
          if (item.orderItemId) {
            type = "1";
          } else if (item.backItemId) {
            type = "2";
          } else if (item.adjustmentId) {
            type = "3";
          }
          return {
            ...item,
            id: item.businessId,
            type,
            num: Number(item.num),
            amount: -Number(item.amount),
            orderNum: Number(item.num),
            billSerialNo: null,
            cell: false,
          };
        });
      this.soaForm = {
        type: "2",
        brand: this.selectionList[0].brand || "1",
        amount: this.amount,
        supplierId: this.supplierId,
        backItems: [],
        items,
      };
      if (value === "add") this.$refs.paySoaRef.show();
      else if (value === "edit") {
        this.$refs.alipaySoaSelectRef.visible = true;
      }
    },
    handleNote() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$prompt("", "请输入备注", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
      }).then(({ value }) => {
        const businessSerialNos = this.selectionList.map(
          (item) => item.businessSerialNo
        );
        changeNote(null, new Array(businessSerialNos).join(","), value).then(
          () => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!",
            });
          }
        );
      });
    },
    handleBuildVoucher() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$message.warning("功能正在建设中...");
    },
    handleCompare() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }

      this.check.form.ids = this.ids;
      this.check.form.businessSerialNo = this.selectionList
        .map((item) => item.businessSerialNo)
        .join(",");
      this.check.visible = true;
    },
    handleCompareSubmit(form, done) {
      manualCompareBill(form)
        .then(() => {
          this.check.visible = false;
          this.onLoad(this.page);
          Object.keys(this.check.form).forEach((key) => (this.form[key] = ""));
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        })
        .finally(() => {
          done();
        });
    },
    handleAdjustmentSubmit(form, done) {
      form.row = null;
      adjustmentBill(form)
        .then(() => {
          this.adjustment.visible = false;
          this.onLoad(this.page);
          Object.keys(this.adjustment.form).forEach(
            (key) => (this.form[key] = "")
          );
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        })
        .finally(() => {
          done();
        });
    },
    handleLoadBill() {
      this.$confirm("系统会定时拉取账单，是否手动拉取?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.syncBill.visible = true;
      });
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page);
    },
    rowClick(row) {
      this.$refs.crud.toggleSelection([row]);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      const q = {
        ...params,
        ...this.query,
      };
      if (q.date != null && q.date.length === 2) {
        q.startDate = q.date[0];
        q.endDate = q.date[1];
        q.date = null;
      }
      q.billStatus = this.billStatus === "all" ? null : this.billStatus;
      getPageJoinOrderItem1(page.currentPage, page.pageSize, q).then((res) => {
        this.page.total = res.data.data.total;
        //跨月的id，在行合并时需要将此剔除
        this.data = res.data.data.records.map((item) => {
          if (item.row) item.rowNo = item.row + (item.no ? "-" + item.no : "");
          if (!item.billStatus) item.billStatus = 1;
          if (!item.alipayBusinessNo)
            item.alipayBusinessNo = item.businessSerialNo;
          if (item.orderItemId) {
            item.amount = -Number(item.amount);
          }
          item.alipayId = item.id;
          item.id = null;
          return {
            ...item,
          };
        });
        this.loading = false;
        const needMerge = this.rowMergeHandle(
          this.needMergeArr,
          this.data,
          "alipayId"
        );
        const needMerge1 = this.rowMergeHandle(
          this.needMergeArr1,
          this.data,
          "businessId"
        );
        //把跨月的剔除
        const needMerge2 = this.rowMergeHandle(
          this.needMergeArr2,
          this.data,
          "alipayBusinessNo"
        );
        this.rowMergeArrs = needMerge;
        this.rowMergeArrs1 = needMerge1;
        this.rowMergeArrs2 = needMerge2;
        this.selectionClear();
      });
    },
    arraySpanMethod({ column, rowIndex }) {
      if (this.needMergeArr2.includes(column.property)) {
        return this.mergeAction(column.property, rowIndex, this.rowMergeArrs2);
      } else if (this.needMergeArr.includes(column.property)) {
        return this.mergeAction(column.property, rowIndex, this.rowMergeArrs);
      }
      if (this.needMergeArr1.includes(column.property)) {
        return this.mergeAction(column.property, rowIndex, this.rowMergeArrs1);
      }
    },
    summaryMethod({ columns }) {
      const sums = [];
      if (columns.length > 0) {
        columns.forEach((column, index) => {
          let prop = column.property;
          if ("date" === prop) {
            sums[index] = "-";
          } else if ("amount" === prop) {
            sums[index] = this.sumOrderAmount();
          } else if (prop === "revenue") {
            sums[index] = this.sumRevenue();
          } else if (prop === "expenditures") {
            sums[index] = this.sumExpenditures();
          } else {
            sums[index] = " ";
          }
        });
      }
      return sums;
    },
    tableRowClassName({ row }) {
      if (row.status === 2) {
        return "audit-row";
      }
      return "";
    },
    sumExpenditures() {
      let amount = 0;
      const ids = new Map();
      this.data.forEach((item) => {
        if (item.bills) {
          item.bills.forEach((item1) => {
            if (!ids.has(item1.id) && ids.set(item1.id, 1)) {
              amount += Number(item1.expenditures);
            }
          });
        } else if (!ids.has(item.alipayId) && ids.set(item.alipayId, 1)) {
          amount += Number(item.expenditures);
        }
      });
      return amount.toLocaleString("zh-CN", {
        minimumFractionDigits: 2,
      });
    },
    sumRevenue() {
      let amount = 0;
      const ids = new Map();
      this.data.forEach((item) => {
        if (item.bills) {
          item.bills.forEach((item1) => {
            if (!ids.has(item1.id) && ids.set(item1.id, 1)) {
              amount += Number(item1.revenue);
            }
          });
        } else if (!ids.has(item.alipayId) && ids.set(item.alipayId, 1)) {
          amount += Number(item.revenue);
        }
      });
      return amount.toLocaleString("zh-CN", {
        minimumFractionDigits: 2,
      });
    },
    sumOrderAmount() {
      const businessIds = new Set();
      let amount = 0;
      this.data.forEach((item) => {
        if (item.businessId && !businessIds.has(item.businessId)) {
          amount += Number(item.amount);
          businessIds.add(item.businessId);
        }
      });
      return amount.toLocaleString("zh-CN", {
        minimumFractionDigits: 2,
      });
    },
    mergeAction(val, rowIndex, rowMergeArrs) {
      let _row = rowMergeArrs[val].rowArr[rowIndex];
      let _col = _row > 0 ? 1 : 0;
      return {
        rowspan: _row,
        colspan: _col,
      };
    },
    linkCellStyle({ row, column }) {
      if ("rowNo" === column.columnKey && row.businessStatus === 2) {
        return {
          backgroundColor: this.colorName,
          color: "#fff",
        };
      }
    },
    cellStyle({ row, column }) {
      if ("rowNo" === column.columnKey && row.soa) {
        return {
          backgroundColor: this.colorName,
          color: "#fff",
        };
      } else if (
        "businessSerialNo" === column.columnKey &&
        row.billStatus === 2
      ) {
        return {
          backgroundColor: "#1677ff",
          color: "#fff",
        };
      }
      if (
        "amount" === column.columnKey &&
        +row.amount === Number(row.revenue) + Number(row.expenditures)
      ) {
        return {
          backgroundColor: "#e1f3d8",
        };
      }
    },
    rowMergeHandle(arr, data, column, func) {
      if (!Array.isArray(arr) && !arr.length) return false;
      if (!Array.isArray(data) && !data.length) return false;
      let needMerge = {};
      arr.forEach((i) => {
        needMerge[i] = {
          rowArr: [],
          rowMergeNum: 0,
        };
        data.forEach((item, index) => {
          if (index === 0) {
            needMerge[i].rowArr.push(1);
            needMerge[i].rowMergeNum = 0;
          } else {
            if (
              item[column] &&
              item[column] === data[index - 1][column] &&
              (func === undefined || func(item[column]) === true)
            ) {
              needMerge[i].rowArr[needMerge[i].rowMergeNum] += 1;
              needMerge[i].rowArr.push(0);
            } else {
              needMerge[i].rowArr.push(1);
              needMerge[i].rowMergeNum = index;
            }
          }
        });
      });
      return needMerge;
    },
  },
};
</script>

<style lang="scss" scoped>
/deep/ .audit-row {
  background-color: #55b980 !important;
  color: #fff;
}

/deep/ .hover-icon {
  transition: transform 0.3s ease;
  cursor: pointer;
  color: #409eff;

  &:hover {
    transform: rotate(90deg) translateY(-1px) !important;
  }
}
</style>

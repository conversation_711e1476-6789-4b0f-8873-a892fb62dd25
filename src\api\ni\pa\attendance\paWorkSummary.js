import request from '@/router/axios';

export const getPage = (current, size, params) => {
    return request({
      url: '/api/ni/pa/work/summary/page',
      method: 'get',
      params: {
        ...params,
        current,
        size,
      }
    })
  }

export const getDetail = (id) => {
  return request({
    url: '/api/ni/pa/work/summary/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/ni/pa/work/summary/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const changeDeduction = (id,deduction) => {
    return request({
      url: '/api/ni/pa/work/summary/changeDeduction',
      method: 'post',
      params: {
        id,
        deduction
      }
    })
  }

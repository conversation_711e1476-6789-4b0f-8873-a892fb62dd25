import { shallowRef } from "vue";

export default {
  height: "auto",
  calcHeight: 30,
  tip: false,
  searchIcon: true,
  searchShow: true,
  indexLabel: "序号",
  searchMenuSpan: 6,
  searchIndex: 3,
  border: true,
  index: false,
  viewBtn: true,
  addBtn: true,
  editBtn: true,
  delBtn: true,
  selection: true,
  dialogClickModal: false,
  menuWidth: 220,
  labelWidth: 150,
  column: [
    {
      label: "编码",
      prop: "code",
      type: "input",
      width: 120,
      disabled: true,
      search: true,
      placeholder: "系统默认生成"
    },
    {
      label: "是否合格",
      prop: "result",
      type: "select",
      width: 80,
      value: true,
      labelSuffix: "<span style='color: red;'>*</span>",
      dicData: [
        {
          label: "是",
          value: true,
        },
        {
          label: "否",
          value: false,
        },
      ],
      rules: [
        {
          required: true,
          message: "是否合格为必填项",
          trigger: ["blur", "change"]
        }
      ],
      html: true,
      formatter: function (val, value, label) {
        if (value === true) {
          return `<span style="color:green">${label}</span>`;
        } else {
          return `<span style="color:red">${label}</span>`;
        }
      },
    },
    {
      label: "发布状态",
      prop: "publishStatus",
      type: "select",
      addDisplay: false,
      editDisplay: false,
      width: 80,
      dicData: [
        {
          label: "已发布",
          value: "1",
        },
        {
          label: "未发布",
          value: "0",
        },
      ],
      html: true,
      formatter: function (val, value, label) {
        if (value == 1) {
          return `<span style="color:green">${label}</span>`;
        } else {
          return `<span style="color:red">${label}</span>`;
        }
      },
    },
    {
      label: "质检日期",
      prop: "date",
      type: "date",
      format: "yyyy-MM-dd",
      valueFormat: "yyyy-MM-dd",
      width: 100,
      search: true,
      labelSuffix: "<span style='color: red;'>*</span>",
      rules: [
        {
          required: true,
          message: "质检日期为必填项",
          trigger: ["blur", "change"]
        }
      ],
      html: true
    },

    {
      label: "班次",
      prop: "shift",
      type: "select",
      width: 80,
      dicData: [
        {
          label: "白班",
          value: "白班",
        },
        {
          label: "夜班",
          value: "夜班",
        }
      ]
    },

    {
      label: "品牌",
      prop: "brandName",
      type: "select",
      dicUrl:
        "/api/blade-system/dict-biz/dictionary?code=ni_product_brand",
      props: {
        label: "dictValue",
        value: "dictValue",
      },
      showColumn: false,
      viewDisplay: false,
      allowCreate: true,
      filterable: true,
      overHidden: true,
      minWidth: 70,
      search: true,
    },

    {
      label: "规格",
      prop: "spec",
      type: "select",
      dicUrl: "/api/ni/product/spec/list",
      props: {
        label: "name",
        value: "name",
        desc: "code"
      },
      remote: true,
      dicFormatter: (data) => {
        return data.data;
      },
      filterable: true,
      placeholder: "请选择规格",
      showColumn: true,
      overHidden: true,
      minWidth: 120,
      search: true,
    },

    {
      label: "内包装",
      prop: "innerPackages",
      type: "select",
      dicUrl: "/api/ni/product/packaging/list?innerPark=1&current=1&size=20&status=1", 
      props: {
        label: "name",
        value: "name",
        desc: "type"
      },
      remote: true,
      dicFormatter: (data) => {
        return data.data.records;
      },
      filterable: true,
      placeholder: "请选择内包装",
      showColumn: true,
      overHidden: true,
      minWidth: 120,
      multiple: true,
      collapseTags: false, 
      search: true,
      multipleLimit: 0,
      formatter: (row, column) => {
        if (row.innerPackages && row.innerPackages.length > 0) {
          return row.innerPackages.join('、');
        }
        return column == null ? '' : column.placeholder;
      },
      clearable: true,
    },

    {
      label: "备注",
      prop: "remark",
      type: "input",
      width: 160,
    },
    {
      label: "含水",
      prop: "waterContent",
      type: "input",
      width: 60,
    },
    {
      label: "烧后NaCl",
      prop: "afterBurningNaCl",
      type: "input",
      width: 80,
    },
    {
      label: "烧后LiCl",
      prop: "afterBurningLiCl",
      type: "input",
      width: 80,
    },
    {
      label: "有效水吸附",
      prop: "effectiveWaterAdsorption",
      type: "input",
      width: 90,
    },
    {
      label: "强度",
      prop: "strength",
      type: "input",
      width: 60,
    },
    {
      label: "变异系数",
      prop: "coefficientOfVariation",
      type: "input",
      width: 80,
    },
    {
      label: "堆积密度",
      prop: "bulkDensity",
      type: "input",
      width: 80,
    },
    {
      label: "粒度合格率(%)",
      prop: "particleSizeQualifiedRate",
      type: "input",
      width: 110,
    },
    {
      label: "粒度",
      prop: "particleSize",
      type: "input",
      width: 60,
    },
    {
      label: "温升10g",
      prop: "temperatureRise10",
      type: "input",
      width: 70,
    },
    {
      label: "温升20g",
      prop: "temperatureRise20",
      type: "input",
      width: 70,
    },
    {
      label: "温升30g",
      prop: "temperatureRise30",
      type: "input",
      width: 70,
    },
    {
      label: "磨前落粉",
      prop: "fallingPowderBeforeGrinding",
      type: "input",
      width: 80,
    },
    {
      label: "磨后落粉",
      prop: "fallingPowderAfterGrinding",
      type: "input",
      width: 80,
    },
    {
      label: "渣子",
      prop: "waste",
      type: "input",
      width: 60,
    },
    {
      label: "170度吸气量",
      prop: "inspiratoryVolume170t",
      type: "input",
      width: 90,
    },
    {
      label: "70度吸气量",
      prop: "inspiratoryVolume70t",
      type: "input",
      width: 90,
    },
    {
      label: "96H吸气量",
      prop: "inspiratoryVolume96h",
      type: "input",
      width: 90,
    },
    {
      label: "气体解吸量(ml/g)",
      prop: "gasDesorption",
      type: "input",
      width: 120,
    },
    {
      label: "检验员",
      prop: "inspectorName",
      type: "input",
      addDisplay: false,
      editDisplay: false,
    },
  ],
};

import request from "@/router/axios";

export const getMonthList = (params) => {
  return request({
    url: "/api/ni/fin/report/month/list",
    method: "get",
    params: {
      ...params,
    },
  });
};
export const getPage = (current, size, params) => {
  return request({
    url: "/api/ni/fin/report/page",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};
export const getList = (params) => {
  return request({
    url: "/api/ni/fin/report/list",
    method: "get",
    params,
  });
};
export const getLastUpdateTime = () => {
  return request({
    url: "/api/ni/fin/report/lastUpdateTime",
    method: "get",
  });
};
export const sync = (startDate, endDate, supplierId, withOld, isCovered) => {
  return request({
    url: "/api/ni/fin/report/sync",
    method: "post",
    params: {
      startDate,
      endDate,
      supplierId,
      withOld,
      isCovered,
    },
  });
};
export const confirm = (id) => {
  return request({
    url: "/api/ni/fin/report/confirm",
    method: "post",
    params: { id },
  });
};
export const confirmCancel = (id) => {
  return request({
    url: "/api/ni/fin/report/confirmCancel",
    method: "post",
    params: { id },
  });
};

import request from "@/router/axios";

export const getPage = (current, size, params) => {
  return request({
    url: "/api/ni/fin/cash/receive/page",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/ni/fin/cash/receive/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const remove = (ids) => {
  return request({
    url: "/api/ni/fin/cash/receive/remove",
    method: "post",
    params: {
      ids,
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/ni/fin/cash/receive/save",
    method: "post",
    data: row,
  });
};

export const update = (row) => {
  return request({
    url: "/api/ni/fin/cash/receive/update",
    method: "post",
    data: row,
  });
};

export const submit = (ids) => {
  return request({
    url: "/api/ni/fin/cash/receive/submit",
    method: "post",
    params: {
      ids,
    },
  });
};

export const back = (ids) => {
  return request({
    url: "/api/ni/fin/cash/receive/back",
    method: "post",
    params: {
      ids,
    },
  });
};

export const toVoid = (ids) => {
  return request({
    url: "/api/ni/fin/cash/receive/toVoid",
    method: "post",
    params: {
      ids,
    },
  });
};

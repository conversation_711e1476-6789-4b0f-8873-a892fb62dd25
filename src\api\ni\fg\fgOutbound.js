import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/ni/fg/outbound/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/ni/fg/outbound/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/ni/fg/outbound/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/ni/fg/outbound/save',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/ni/fg/outbound/update',
    method: 'post',
    data: row
  })
}
export const red = (id, reason) => {
  return request({
    url: '/api/ni/fg/outbound/red/' + id,
    method: 'post',
    params: {
      reason,
    }
  })
}
/**
 * 根据产成品id添加出库流水，如果时没有批次号，则自动根据生产日期关联批次号
 * @param row
 * @returns {AxiosPromise}
 */
export const addWithAutoBatch = (row) => {
  return request({
    url: '/api/ni/fg/outbound/saveWithAutoBatch',
    method: 'post',
    data: row
  })
}

export const linkXiaoShouDingDan = (id, orderId) => {
  return request({
    url: '/api/ni/fg/outbound/linkXiaoShouDingDan',
    method: 'post',
    params: {
      id, orderId
    }
  })
}

export const linkXiaoShouWaiKuBuHuo = (id, orderId) => {
  return request({
    url: '/api/ni/fg/outbound/linkXiaoShouWaiKuBuHuo',
    method: 'post',
    params: {
      id, orderId
    }
  })
}
export const linkGuoWaiFaHuo = (id, orderId) => {
  return request({
    url: '/api/ni/fg/outbound/linkGuoWaiFaHuo',
    method: 'post',
    params: {
      id, orderId
    }
  })
}

export const syncOldCN = (id) => {
  return request({
    url: '/api/ni/fg/outbound/syncOldCN',
    method: 'post',
    params: {
      id
    }
  })
}
export const syncOldOS = (id) => {
  return request({
    url: '/api/ni/fg/outbound/syncOldOS',
    method: 'post',
    params: {
      id
    }
  })
}
export const syncXiaoShouWaiKuBuHuo = (id) => {
  return request({
    url: '/api/ni/fg/outbound/syncXiaoShouWaiKuBuHuo',
    method: 'post',
    params: {
      id
    }
  })
}

export const registerBatchNo = (params) => {
  return request({
    url: '/api/ni/fg/outbound/registerBatchNo',
    method: 'post',
    params
  })
}

export const syncFromXiaoShouDingDan = (ids, withNoBatchNo = 1) => {
  return request({
    url: '/api/ni/fg/outbound/syncFromXiaoShouDingDan',
    method: 'post',
    params: {
      ids,
      withNoBatchNo
    }
  })
}
export const syncFromXiaoShouWaiKuBuHuo = (ids, withNoBatchNo = 1) => {
  return request({
    url: '/api/ni/fg/outbound/syncFromXiaoShouWaiKuBuHuo',
    method: 'post',
    params: {
      ids,
      withNoBatchNo
    }
  })
}
export const itemsByOldFaHuoBianHao = (fhbh) => {
  return request({
    url: '/api/ni/fg/outbound/itemsByOldFaHuoBianHao',
    method: 'get',
    params: {
      fhbh,
    }
  })
}

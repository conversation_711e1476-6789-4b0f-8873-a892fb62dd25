```java
// 定义包路径，属于问题反馈模块的视图对象包
package com.natergy.ni.feedback.vo;

// 导入问题反馈实体类和问题解决记录实体类
import com.natergy.ni.feedback.entity.FeedbackEntity;
import com.natergy.ni.feedback.entity.FeedbackSolvingRecordEntity;
// 导入BladeX框架的节点接口（用于树形结构等场景）
import org.springblade.core.tool.node.INode;
// 导入Lombok注解，用于简化代码
import lombok.Data;
import lombok.EqualsAndHashCode;

// 导入Java集合框架中的List接口
import java.util.List;

/**
 * 问题反馈 视图实体类
 *
 * <AUTHOR>
 * @since 2025-02-26
 */
// @Data：Lombok注解，自动生成该类所有字段的getter、setter、toString、equals、hashCode等方法
@Data
// @EqualsAndHashCode(callSuper = true)：Lombok注解，生成equals和hashCode方法时包含父类的字段
// 确保在比较FeedbackVO对象时，父类FeedbackEntity的字段也参与比较
@EqualsAndHashCode(callSuper = true)
// 继承FeedbackEntity实体类，获得其所有字段和方法
public class FeedbackVO extends FeedbackEntity {
    // 序列化版本号，用于对象序列化时的版本控制
    private static final long serialVersionUID = 1L;

    /**
     * 问题认领记录：存储当前问题对应的所有解决记录列表
     */
    private List<FeedbackSolvingRecordEntity> solvingRecordList;

    /**
     * 返回问题合集存在多少问题：表示当前问题所属合集的问题总数
     */
    private Integer feedbackCollectionCount;

}
```

### 类功能说明

该类是问题反馈模块的**视图对象（VO，View Object）**，主要用于在前端展示问题反馈相关数据，在实体类基础上扩展了前端所需的额外信息：

1. **继承实体类**：通过继承`FeedbackEntity`，直接获得问题反馈的核心字段（如问题描述、状态、负责人等），无需重复定义。
2. **扩展前端展示字段**：
   - `solvingRecordList`：存储当前问题对应的所有解决记录列表（类型为`FeedbackSolvingRecordEntity`），用于在前端展示问题的处理过程和历史记录。
   - `feedbackCollectionCount`：表示当前问题所属合集的问题总数，用于统计展示或关联分析。
3. **适配视图层需求**：VO 作为控制器层返回给前端的数据载体，整合了实体类和关联数据，减少前端的多次请求，提高数据展示效率。同时，通过 Lombok 注解简化了代码，确保对象的 equals 和 hashCode 方法能正确处理父类字段。

这种设计符合分层架构中视图层的数据需求，使前端能便捷地获取展示所需的完整信息。
<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template #status="{ row }">
        <!-- 草稿 -->
        <el-tag v-if="row.status === 0" size="mini" type="info">
          {{ row.$status }}
        </el-tag>
        <!-- 已提交 -->
        <el-tag v-else-if="row.status === 1" size="mini" type="success">
          {{ row.$status }}
        </el-tag>
        <!-- 审核中 -->
        <el-tag v-else-if="row.status === 2" size="mini" type="warning">
          {{ row.$status }}
        </el-tag>
        <!-- 已审核 -->
        <el-tag v-else-if="row.status === 3" size="mini" type="success">
          {{ row.$status }}
        </el-tag>
        <!-- 开发中 -->
        <el-tag v-else-if="row.status === 4" size="mini" type="warning">
          {{ row.$status }}
        </el-tag>
        <!-- 开发完成 -->
        <el-tag v-else-if="row.status === 5" size="mini" type="success">
          {{ row.$status }}
        </el-tag>
        <!-- 验收中 -->
        <el-tag v-else-if="row.status === 6" size="mini" type="warning">
          {{ row.$status }}
        </el-tag>
        <!-- 已验收 -->
        <el-tag v-else-if="row.status === 7" size="mini" type="success">
          {{ row.$status }}
        </el-tag>
        <!-- 被驳回 -->
        <el-tag v-else-if="row.status === 8" size="mini" type="danger">
          {{ row.$status }}
        </el-tag>
        <!-- 已终止 -->
        <el-tag v-else-if="row.status === 9" size="mini" type="danger">
          {{ row.$status }}
        </el-tag>
      </template>
      <!-- 超出文本省略 -->
      <template slot="demandContent" slot-scope="scope">
        <avue-text-ellipsis :text="scope.row.demandContent" :height="50">
          <small slot="more">...</small>
        </avue-text-ellipsis>
      </template>
      <template slot="solution" slot-scope="scope">
        <avue-text-ellipsis :text="scope.row.solution" :height="50">
          <small slot="more">...</small>
        </avue-text-ellipsis>
      </template>
      <!-- 右侧操作栏菜单 -->
      <template slot-scope="{ row, index, size }" slot="menu">
        <el-button
          type="text"
          icon="el-icon-edit"
          :size="size"
          v-if="permissionList.editBtn && 0 === row.status"
          @click="$refs.crud.rowEdit(row, index)"
          >编辑
        </el-button>
        <el-button
          type="text"
          icon="el-icon-help"
          :size="size"
          @click="handleDemandDetail(row.processInsId)"
          >详情
        </el-button>
        <el-button
          type="text"
          :size="size"
          icon="el-icon-delete"
          v-if="permissionList.delBtn && 0 === row.status"
          @click="$refs.crud.rowDel(row, index)"
          >删除
        </el-button>
        <el-button
          type="text"
          icon="el-icon-download"
          :size="size"
          @click="rowAttach(row)"
          >附件
        </el-button>
      </template>
      <!-- 新增弹窗 -->
      <template slot-scope="{ size, type }" slot="menuForm">
        <el-button
          type="primary"
          icon="el-icon-s-promotion"
          :size="size"
          v-if="
            (permissionList.addBtn || permissionList.editBtn) &&
            ['add', 'edit'].includes(type)
          "
          @click="handleSubmit(type)"
          >提交
        </el-button>
      </template>
      <!-- 左侧菜单按钮 -->
      <template #menuLeft>
        <el-button
          type="success"
          size="mini"
          icon="el-icon-s-check"
          plain
          @click="handleDemand"
          >需求申请
        </el-button>
        <el-button
          type="danger"
          size="mini"
          icon="el-icon-delete"
          plain
          @click="handleDelete"
          >删除
        </el-button>
        <el-divider direction="vertical"></el-divider>
        <el-checkbox v-model="toList">待办</el-checkbox>
      </template>
    </avue-crud>
    <!-- 附件 -->
    <attach-dialog ref="attachDialogRef" code="private" />
    <!-- 详情 -->
    <el-drawer
      :visible.sync="wfDetailVisible"
      :title="form.title"
      custom-class="wf-drawer"
      size="100%"
      append-to-body
    >
      <demand-detail
        v-if="wfDetailVisible"
        :taskId="form.taskId"
        :formKey="formKey"
        :processInstanceId="processInstanceId"
      />
    </el-drawer>
  </basic-container>
</template>
<script>
import {
  getList,
  getDetail,
  add,
  update,
  remove,
  saveAndStart,
  getProcessIdByProcessKey,
} from "@/api/ni/base/demand";
import { mapGetters } from "vuex";
import AttachDialog from "@/components/attach-dialog";
import exForm from "@/views/plugin/workflow/mixins/ex-form";
import { rowStart, statusTagType } from "@/api/ni/pa/pa-common";
import DemandDetail from "@/views/ni/base/demand-detail";
import { dateFormat } from "@/util/date";

export default {
  mixins: [exForm],
  components: { DemandDetail, AttachDialog },
  data() {
    return {
      processDefKey: "process_base_demand",
      formKey: "wf_ex_base/Demand",
      processInstanceId: "",
      wfDetailVisible: false,
      form: {},
      query: {},
      loading: false,
      toList: false,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        menuWidth: 120,
        // 组件的尺寸
        size: "mini",
        // 搜索栏
        searchSize: "mini",
        searchEnter: true,
        searchShow: true,
        searchIcon: true,
        searchSpan: 6,
        searchBtn: true,
        searchIndex: 3,
        emptyBtn: true,
        columnBtn: false,
        filterBtn: false,
        refreshBtn: true,
        align: "center",
        height: "auto",
        calcHeight: 30,
        rowKey: "id",
        border: true,
        tip: false,
        selection: true,
        menu: true,
        menuAlign: "center",
        addBtn: false,
        editBtn: false,
        viewBtn: false,
        delBtn: false,
        dialogClickModal: false,
        dialogModal: true,
        dialogType: "dialog",
        saveBtn: true,
        cancelBtn: true,
        column: [
          {
            label: "状态",
            prop: "status",
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_demand_status",
            hide: false,
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            dataType: "number",
            display: true,
            type: "select",
            search: true,
            width: 70,
            rules: [
              {
                required: true,
                message: "请选择状态",
                trigger: "blur",
              },
            ],
          },
          {
            label: "申请日期",
            prop: "applyTime",
            type: "date",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            addDisplay: true,
            editDisplay: false,
            viewDisplay: true,
            width: 84,
          },
          {
            label: "申请人",
            prop: "userId",
            type: "select",
            filterable: true,
            overHidden: true,
            dicData: [],
            // dicUrl: "/api/blade-user/user-vo-list",
            props: {
              label: "name",
              value: "id",
            },
            width: 60,
            slot: true,
            hide: true,
            showColumn: false,
            search: true,
            rules: [
              {
                required: true,
                message: "请选择申请人",
                trigger: "blur",
              },
            ],
          },
          {
            label: "申请人",
            prop: "userName",
            width: 60,
            display: false,
          },
          {
            label: "部门",
            prop: "depId",
            type: "tree",
            dicData: [],
            // dicUrl: `/api/blade-system/dept/list`,
            props: {
              label: "deptName",
              value: "id",
            },
            width: 74,
            overHidden: true,
            addDisplay: true,
            editDisplay: false,
            viewDisplay: true,
          },
          {
            label: "类别",
            prop: "classify",
            type: "select",
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_demand_classify",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            search: true,
            width: 60,
            rules: [
              {
                required: true,
                message: "请选择需求类别",
                trigger: "blur",
              },
            ],
          },
          {
            label: "需求内容",
            prop: "demandContent",
            type: "textarea",
            minWidth: 150,
            overHidden: true,
            span: 24,
            slot: true,
          },
          {
            label: "优先级",
            prop: "priority",
            type: "select",
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_demand_priority",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            width: 60,
            search: false,
            rules: [
              {
                required: true,
                message: "请选择需求优先级",
                trigger: "blur",
              },
            ],
          },
          {
            label: "研发人员",
            prop: "developer",
            type: "select",
            remote: true,
            dicUrl: `/api/blade-user/search/otj/user?name={{key}}`,
            props: {
              label: "realName",
              value: "id",
            },
            dicFormatter: (data) => {
              return data.data.records;
            },
            placeholder: " ",
            search: true,
            hide: true,
            showColumn: false,
            slot: true,
            rules: [
              {
                required: true,
                message: "请指定研发人员",
                trigger: "blur",
              },
            ],
          },
          {
            label: "研发人员",
            prop: "developerName",
            display: false,
            width: 70,
          },
          {
            label: "解决方案",
            prop: "solution",
            type: "textarea",
            span: 24,
            minWidth: 150,
            hide: true,
            addDisplay: false,
            slot: true,
          },
          {
            label: "预计完成时间",
            prop: "planTime",
            type: "date",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: true,
            hide: false,
            width: 94,
          },
          {
            label: "实际完成时间",
            prop: "actualTime",
            type: "date",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: true,
            width: 94,
          },
          {
            label: "验收人",
            prop: "acceptor",
            type: "select",
            filterable: true,
            overHidden: true,
            addDisplay: false,
            dicData: [],
            // dicUrl: "/api/blade-user/user-vo-list",
            props: {
              label: "name",
              value: "id",
            },
            width: 60,
            hide: true,
            slot: true,
            rules: [
              {
                required: true,
                message: "请选择验收人",
                trigger: "blur",
              },
            ],
          },
          {
            label: "验收时间",
            prop: "acceptTime",
            type: "date",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: true,
            hide: false,
            width: 84,
          },
          {
            label: "验收结果",
            prop: "acceptResult",
            type: "select",
            addDisplay: false,
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_demand_acceptResult",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            width: 71,
            search: true,
            rules: [
              {
                required: true,
                message: "请选择验收结果",
                trigger: "blur",
              },
            ],
          },
          // {
          //   label: "不合格原因",
          //   prop: "unqualifiedReason",
          //   type: "textarea",
          //   span: 24,
          //   addDisplay: false,
          // },
        ],
      },
      data: [],
    };
  },

  created() {
    //用户姓名
    this.$http.get("/api/blade-user/user-vo-list").then((res) => {
      const userId = this.findObject(this.option.column, "userId");
      const acceptor = this.findObject(this.option.column, "acceptor");
      userId.dicData = res.data.data;
      acceptor.dicData = res.data.data;
      console.log(userId.dicData);
    });
    //部门
    this.$http.get("/api/blade-system/dept/list").then((res) => {
      const depId = this.findObject(this.option.column, "depId");
      depId.dicData = res.data.data;
    });
  },

  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.demand_add, true),
        viewBtn: this.vaildData(this.permission.demand_view, true),
        delBtn: this.vaildData(this.permission.demand_delete, true),
        editBtn: this.vaildData(this.permission.demand_edit, true),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },

  watch: {
    toList: {
      handler(val) {
        if (val) {
          this.query.todo = true;
        } else {
          this.query.todo = null;
        }
        this.page.currentPage = 1;
        this.onLoad(this.page);
      },
      immediate: true,
    },
  },
  methods: {
    //跳转到研发需求申请流程上
    async handleDemand() {
      const result = await getProcessIdByProcessKey("process_base_demand");

      console.log(result);

      let param = Buffer.from(
        JSON.stringify({
          processId: result.data.data,
        })
      ).toString("base64");

      console.log(param);

      this.$router.push({
        path: `/workflow/process/start/${param}`,
      });
    },
    //跳转到研发需求申请详情上
    async handleDemandDetail(processInsId) {
      let param = Buffer.from(JSON.stringify({ processInsId })).toString(
        "base64"
      );

      console.log(param);

      this.$router.push({
        path: `/workflow/process/detail/${param}`,
      });
    },

    rowTagType(status) {
      return statusTagType(status);
    },
    rowSave(row, done, loading) {
      if (row.status === 1) {
        saveAndStart(this.processDefKey, row).then(
          () => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!",
            });
            done();
          },
          (error) => {
            loading();
            console.log(error);
          }
        );
      } else {
        add(row).then(
          () => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!",
            });
            done();
          },
          (error) => {
            loading();
            window.console.log(error);
          }
        );
      }
    },
    rowUpdate(row, index, done, loading) {
      if (row.status === 1) {
        saveAndStart(this.processDefKey, row).then(
          () => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!",
            });
            done();
          },
          (error) => {
            loading();
            console.log(error);
          }
        );
      } else {
        update(row).then(
          () => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!",
            });
            done();
          },
          (error) => {
            loading();
            console.log(error);
          }
        );
      }
    },
    //数据删除
    rowDel(row) {
      if (row.processIsFinished == "unfinished") {
        this.$message.warning("当前流程未结束，不可删除！");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    rowStart(row) {
      rowStart(row, this.processDefKey, this);
    },
    handleSubmit(type) {
      this.$refs.crud.validate((valid, done) => {
        if (valid) {
          this.$confirm("此操作将提交该数据，是否继续?", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
            center: true,
          }).then(
            () => {
              this.form.status = 1;
              if (type === "add") {
                this.$refs.crud.rowSave();
              } else if (type === "edit") {
                this.$refs.crud.rowUpdate();
              }
            },
            (error) => {
              if (error === "cancel") {
                this.$message({
                  type: "success",
                  message: "取消成功",
                });
              }
            }
          );
        }
        done();
      });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen(done, type) {
      if (type === "add") {
        this.form.status = 0;
        this.form.userId = this.userInfo.user_id;
        this.form.depId = this.userInfo.dept_id;
        this.form.applyTime = dateFormat(new Date(), "yyyy-MM-dd");
      }
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then((res) => {
          this.form = res.data.data;
        });
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
    rowAttach(row) {
      return this.$refs.attachDialogRef.init(row.id, "ni_base_demand");
    },
    rowFlow(row, index) {
      if (row.processInsId) {
        this.dynamicRoute(
          {
            processInstanceId: row.processInsId,
          },
          "detail",
          true
        ).then(() => {
          this.processInstanceId = row.processInsId;
          this.wfDetailVisible = true;
        });
      } else {
        this.$refs.crud.rowView(row, index);
      }
    },
  },
};
</script>

<style></style>

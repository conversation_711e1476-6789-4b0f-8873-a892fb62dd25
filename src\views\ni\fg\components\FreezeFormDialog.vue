<script>
import InventorySelectDialog from "@/views/ni/fg/components/InventorySelectDialog.vue";
import { mapGetters } from "vuex";
import { dateFormat } from "@/util/date";
import { add, addBatch, getDetail } from "@/api/ni/fg/fgFreeze";
import { getList as getBatchNos } from "@/api/ni/fg/fgInventory";

export default {
  name: "FreezeFormDialog",
  components: { InventorySelectDialog },
  data() {
    return {
      visible: false,
      option: {
        emptyBtn: false,
        size: "mini",
        span: 8,
        labelWidth: 110,
        column: [
          {
            label: "冻结原因",
            prop: "reason",
            overHeight: true,
            rules: [
              {
                required: true,
                message: "请输入冻结原因",
                trigger: "blur",
              },
            ],
          },
          {
            label: "冻结类型",
            prop: "type",
            type: "select",
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_fg_inventory_freeze_type",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            rules: [
              {
                required: true,
                message: "请选择冻结类型",
                trigger: "blur",
              },
            ],
          },
          {
            label: "仓库",
            prop: "depotId",
            type: "select",
            dicUrl: "/api/ni/base/depot/info/list?status=2&type=1",
            props: {
              label: "name",
              value: "id",
              desc: "code",
            },
            rules: [
              {
                required: true,
                message: "请选择仓库",
                trigger: "blur",
              },
            ],
          },
          {
            label: "冻结明细",
            prop: "items",
            span: 24,
            type: "dynamic",
            hide: true,
            showColumn: false,
            children: {
              rowAdd: () => {
                this.handleInventorySelect();
              },
              size: "mini",
              align: "center",
              headerAlign: "center",
              showSummary: true,
              sumColumnList: [
                {
                  name: "num",
                  type: "sum",
                  decimals: 1,
                },
              ],
              column: [
                {
                  label: "规格",
                  prop: "specText",
                  placeholder: " ",
                  display: false,
                  overHidden: true,
                  cell: false,
                  width: 120,
                },
                {
                  label: "外包装",
                  prop: "packageText",
                  placeholder: " ",
                  display: false,
                  overHidden: true,
                  cell: false,
                  minWidth: 115,
                },
                {
                  label: "内包装",
                  prop: "innerPackageText",
                  placeholder: " ",
                  display: false,
                  overHidden: true,
                  cell: false,
                  width: 115,
                },
                {
                  label: "质量",
                  prop: "qualityLevel",
                  type: "select",
                  dicUrl:
                    "/api/blade-system/dict-biz/dictionary?code=ni_product_sku_quality_level",
                  props: {
                    label: "dictValue",
                    value: "dictKey",
                    desc: "dictKey",
                  },
                  placeholder: " ",
                  width: 100,
                  cell: false,
                },
                {
                  label: "批号",
                  prop: "batchNo",
                  placeholder: " ",
                  minWidth: 110,
                  overHidden: true,
                  rules: [
                    {
                      required: true,
                      message: "请输入批号",
                      trigger: "blur",
                    },
                  ],
                },
                {
                  label: "当前库存",
                  prop: "currentStock",
                  placeholder: " ",
                  width: 110,
                  overHidden: true,
                  cell: false,
                },
                {
                  label: "件数",
                  prop: "num",
                  placeholder: " ",
                  width: 110,
                  overHidden: true,
                },
                {
                  label: "重量",
                  prop: "weight",
                  placeholder: " ",
                  minWidth: 80,
                  type: "number",
                  cell: false,
                },
                {
                  label: "单位",
                  prop: "unit",
                  type: "select",
                  filterable: true,
                  width: 80,
                  cell: false,
                  dicUrl:
                    "/api/blade-system/dict-biz/dictionary?code=ni_material_unit",
                  props: {
                    label: "dictValue",
                    value: "dictKey",
                  },
                  rules: [
                    {
                      required: true,
                      message: "请选择单位",
                      trigger: "blur",
                    },
                  ],
                  slot: true,
                  placeholder: " ",
                },
                {
                  label: "存货编码",
                  prop: "materialCode",
                  placeholder: " ",
                  width: 110,
                  cell: false,
                  display: false,
                },
                {
                  label: "#",
                  prop: "action",
                  width: 60,
                  fixed: "right",
                },
              ],
            },
          },
        ],
      },
      form: {},
    };
  },
  computed: {
    ...mapGetters(["userInfo"]),
    items() {
      return this.form.items || [];
    },
  },
  methods: {
    onEdit(id) {
      Object.keys(this.form).forEach((key) => (this.form[key] = null));
      this.form.items = [];
      this.option.detail = false;
      getDetail(id).then((res) => {
        this.form = res.data.data;
        this.visible = true;
      });
    },
    onShow(id) {
      Object.keys(this.form).forEach((key) => (this.form[key] = null));
      this.form.items = [];
      this.option.detail = true;
      getDetail(id).then((res) => {
        this.form = res.data.data;
        this.visible = true;
      });
    },
    onFreeze(depotId, items) {
      this.option.detail = false;
      Object.keys(this.form).forEach((key) => (this.form[key] = null));
      if (items) {
        this.form.items = [];
        this.handleInventorySelectConfirm(items);
        this.items.forEach((item) => {
          item.inventoryId = null;
        });
      }
      this.form.depotId = depotId;
      this.form.opUserId = this.userInfo.user_id;
      this.form.opDate = dateFormat(new Date(), "yyyy-MM-dd");
      this.visible = true;
    },
    handleInventorySelect() {
      if (!this.form.depotId) {
        this.$message({
          type: "warning",
          message: "请选择仓库!",
        });
        return;
      }
      this.$refs.inventorySelectDialog.onShow();
    },
    handleInventorySelectConfirm(selectionList) {
      selectionList.forEach((item) => {
        this.items.push({
          inventoryId: item.id,
          skuId: item.skuId,
          skuText: item.skuText,
          spec: item.spec,
          specText: item.specText,
          packageId: item.packageId,
          packageText: item.packageText,
          innerPackageText: item.innerPackageText,
          materialId: item.materialId,
          materialCode: item.materialCode,
          currentStock: item.num,
          capacity: item.capacity,
          unit: item.unit,
          qualityLevel: item.qualityLevel,
          batchNoOptions: [],
          batchNoLoading: false,
        });
      });
    },
    handleConfirm(form, done) {
      addBatch(form)
        .then(() => {
          this.visible = false;
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$emit("confirm", form);
        })
        .finally(() => {
          done();
        });
    },
    batchNoRemoteMethod(query, row) {
      console.log(query);
      if (query !== "") {
        row.batchNoLoading = true;
        getBatchNos(1, 20, {
          batchNo: query,
          skuId: row.skuId,
          status: ["0202", "0207", "0203", "0208"].includes(this.form.type)
            ? null
            : 1,
        }).then((res) => {
          row.batchNoLoading = false;
          const data = res.data.data;
          data.records.forEach((item) => {
            item.label = `${item.batchNo}(${item.depotName})`;
          });
          row.batchNoOptions = data.records.filter((item) => {
            return item.batchNo.toLowerCase().indexOf(query.toLowerCase()) > -1;
          });
        });
      } else {
        row.batchNoOptions = [];
      }
    },
    async rowBatchNoChange(val, row) {
      if (row.batchNoOptions && row.batchNoOptions.length > 0) {
        const selectedItem = row.batchNoOptions.find(
          (item) => item.batchNo === val
        );
        if (selectedItem) {
          //校验批次是否存在较早的批次
          row.inventoryId = selectedItem.id;
          row.inventoryDepotId = selectedItem.depotId;
          row.num = selectedItem.num; // 将批号对应数量赋值
          row.productionDate = selectedItem.productionDate;
          this.rowNumChange(row.num, row);
        }
      }
    },
    rowNumChange(value, row) {
      row.weight = Number(value) * Number(row.capacity);
    },
    rowCopy(row, index) {
      const copy = {
        ...row,
        id: null,
        batchNo: null,
      };
      this.form.items.splice(index + 1, 0, copy);
    },
  },
};
</script>

<template>
  <el-dialog :visible.sync="visible" title="冻结库存" fullscreen append-to-body>
    <avue-form
      v-if="visible"
      :option="option"
      v-model="form"
      @submit="handleConfirm"
    >
      <template #batchNo="{ row, size, disabled, index }">
        <el-select
          :size="size"
          :disabled="disabled"
          v-model="row.batchNo"
          filterable
          remote
          allow-create
          placeholder=" "
          :remote-method="
            (query) => {
              batchNoRemoteMethod(query, row);
            }
          "
          :loading="row.batchNoLoading"
          @change="rowBatchNoChange($event, row)"
        >
          <el-option
            v-for="item in row.batchNoOptions"
            :key="item.id"
            :label="item.batchNo"
            :value="item.batchNo"
          >
            <span style="float: left">{{
              `${item.batchNo}(${item.num})`
            }}</span>
            <span
              style="
                position: absolute;
                right: 18px;
                font-size: 12px;
                color: #8492a6;
              "
              >{{ item.depotName }}</span
            >
          </el-option>
        </el-select>
      </template>
      <template #action="{ row, index }">
        <el-button
          type="text"
          icon="el-icon-copy-document"
          @click="rowCopy(row, index)"
        ></el-button>
      </template>
    </avue-form>
    <inventory-select-dialog
      ref="inventorySelectDialog"
      :params="{ depotId: form.depotId, status: 1, descs: 'production_date' }"
      multiple
      :check-before="false"
      @confirm="handleInventorySelectConfirm"
    />
  </el-dialog>
</template>

<style scoped></style>

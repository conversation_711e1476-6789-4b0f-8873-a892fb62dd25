<template>
  <basic-container>
    <avue-skeleton :loading="waiting" avatar :rows="8">
      <div style="display: flex">
        <avue-title
          style="margin-bottom: 20px"
          :styles="{ fontSize: '20px' }"
          :value="process.name"
        ></avue-title>
        <el-badge
          v-if="permission.wf_process_draft && draftCount > 0"
          :value="draftCount"
          style="margin-top: 5px; margin-right: 40px"
          type="warning"
        >
          <el-button size="mini" v-loading="loading" @click="handleDraftBox"
            >草稿箱
          </el-button>
        </el-badge>
      </div>
      <el-card shadow="never" style="margin-top: 20px">
        <!-- 自定义表单区域 -->
        <avue-form
          v-if="option && option.column && option.column.length > 0"
          v-model="form"
          ref="form"
          :option="option"
          :defaults.sync="defaults"
          @submit="handleSubmit"
          @error="loading = false"
          :upload-preview="handleUploadPreview"
        >
          <template #projectId="{ disabled, size, index, row }">
            <project-select
              v-model="form.projectId"
              :size="size"
              :params="{ status: 9 }"
              :disabled="disabled"
              @confirm="projectConfirm"
              @clear="projectClear"
            />
          </template>
          <template #budgetId="{ size }">
            <un-finish-budget-select
              v-model="form.budgetId"
              :size="size"
              @confirm="handleBudgetConfirm($event, form)"
              @clear="handleBudgetClear"
            />
          </template>
          <template #materialCode="{ row, size, disabled, index }">
            <material-select
              v-model="row.materialId"
              :size="size"
              :disabled="disabled"
              @submit="handleMaterialSubmit($event, row)"
            />
          </template>
        </avue-form>
        <!-- 自定义表单区域 -->
      </el-card>

      <el-card shadow="never" style="margin-top: 20px" v-if="showExamForm">
        <wf-examine-form
          ref="examineForm"
          :process="process"
          @user-select="handleUserSelect"
        ></wf-examine-form>
      </el-card>
      <div style="height: 120px"></div>
      <el-row
        class="foot-item avue-affix"
        :style="{
          width: isCollapse ? 'calc(100% - 80px)' : 'calc(100% - 260px)',
        }"
        id="avue-view"
      >
        <el-button
          v-no-more-click
          type="primary"
          size="mini"
          v-loading="loading"
          @click="handleSubmit"
          >发起
        </el-button>
        <el-button
          v-if="permission.wf_process_draft"
          type="success"
          size="mini"
          v-loading="loading"
          @click="
            handleDraftNotClose(process.id, process.formKey, form, process.key)
          "
          >存为草稿
        </el-button>
        <el-button
          v-if="permission.wf_process_draft"
          type="success"
          size="mini"
          v-loading="loading"
          @click="handleDraft(process.id, process.formKey, form, process.key)"
          >存为草稿并关闭
        </el-button>
      </el-row>
    </avue-skeleton>

    <!-- 人员选择弹窗 -->
    <wf-user-select
      ref="user-select"
      :check-type="checkType"
      :default-checked="defaultChecked"
      @onConfirm="handleUserSelectConfirm"
    ></wf-user-select>
    <budget-item-dialog
      ref="porBudgetItemRef"
      multiple
      :params="{ used: false, cost: true }"
      @confirm="handleItemSelect"
    />
    <!-- 草稿弹窗 -->
    <draft-popup
      :visible.sync="isDraftPopupVisible"
      :draftList="draftList"
      @select="handleDraftSelect"
      @delete="handleDraftDelete"
    ></draft-popup>
  </basic-container>
</template>

<script>
import WfExamineForm from "@/views/plugin/workflow/process/components/examForm.vue";
import WfUserSelect from "@/views/plugin/workflow/process/components/user-select";
import MaterialSelect from "@/views/ni/base/components/MaterialSelect1";
import ProjectSelect from "@/views/ni/project/components/MyProjectSelect";

import exForm from "@/views/plugin/workflow/mixins/ex-form";
import draft from "@/views/plugin/workflow/mixins/draft";
import BudgetItemDialog from "@/views/ni/por/components/BudgetItemDialog";
import UnFinishBudgetSelect from "@/views/ni/por/components/UnFinishBudgetSelect";
import { detail } from "@/api/system/param";
import { getList as getAttachList } from "@/api/resource/attach";
import debounce from "@/util/debounce";
import DraftPopup from "@/views/plugin/workflow/process/components/draftPopup.vue";

export default {
  components: {
    DraftPopup,
    WfUserSelect,
    WfExamineForm,
    MaterialSelect,
    ProjectSelect,
    BudgetItemDialog,
    UnFinishBudgetSelect,
  },
  mixins: [exForm, draft],
  activated() {
    let val = this.$route.query.p;
    if (val) {
      let text = Buffer.from(val, "base64").toString();
      text = text.replace(/[\r|\n|\t]/g, "");
      const param = JSON.parse(text);
      const { processId, processDefKey, form } = param;
      detail({ paramKey: "ni.por.apply.inquiries.amount" }).then((res) => {
        this.inquiriesAmount = Number(res.data.data.paramValue);
        this.form.tip = `采购金额大于 ${this.inquiriesAmount}元，需比价!`;
      });
      if (processId) {
        this.getForm(processId);
      } else if (processDefKey) {
        console.log(processDefKey);
        this.getFormByProcessDefKey(processDefKey);
      }
      if (form) {
        const f = JSON.parse(
          new Buffer(decodeURIComponent(form), "utf8").toString("utf8")
        );
        Object.assign(this.form, f);
      }
    }
  },
  computed: {
    showExamForm() {
      const { hideComment, hideCopy, hideExamine } = this.process;
      return !hideComment || !hideCopy || !hideExamine;
    },
    itemIds() {
      const ids = new Set(this.form.items.map((ele) => ele.budgetItemId));
      return Array.from(ids).join(",");
    },
  },
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      console.log(from, "from");
      //  这里的vm指的就是vue实例，可以用来当做this使用
      vm.fromPath = from.path; //获取上一级路由的路径
    });
  },
  data() {
    return {
      inquiriesAmount: 0,
      defaults: {},
      form: {
        items: [],
        attach: [],
      },
      option: {
        labelWidth: 120,
        calcHeight: 30,
        size: "mini",
        span: 8,
        menuBtn: false,
        column: [
          {
            type: "input",
            label: "申请人",
            display: true,
            prop: "createUserName",
            value: "${this.$store.getters.userInfo.nick_name}",
            readonly: true,
          },
          {
            type: "input",
            label: "申请部门",
            display: true,
            prop: "createDeptName",
            value: "${this.$store.getters.userInfo.dept_name}",
            readonly: true,
          },
          {
            label: "账套",
            prop: "brand",
            type: "radio",
            minWidth: 90,
            value: "1",
            dicUrl: "/api/blade-system/dict-biz/dictionary?code=ni_brand",
            disabled: true,
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            search: true,
          },
          {
            label: "申请主题",
            prop: "title",
            placeholder: " ",
            minWidth: 135,
            rules: [
              {
                required: true,
                message: "请输入",
                trigger: "blur",
              },
            ],
          },
          {
            label: "申请编号",
            prop: "serialNo",
            minWidth: 135,
            placeholder: "系统自动生成",
          },
          {
            label: "申请类型",
            prop: "type",
            placeholder: " ",
            type: "select",
            dicUrl: "/api/ni/por/type/listWithPermission",
            props: {
              label: "name",
              value: "code",
              desc: "code",
            },
            rules: [
              {
                required: true,
                message: "请选择申请类型",
                trigger: "blur",
              },
            ],
          },
          {
            label: "供应商",
            prop: "supplierId",
            search: true,
            type: "select",
            remote: true,
            placeholder: " ",
            dicUrl: `/api/ni/base/supplier/info/page?status=2&blacklist=0&keyword={{key}}`,
            props: {
              label: "name",
              value: "id",
              desc: "code",
            },
            dicFormatter: (data) => {
              return data.data.records;
            },
            rules: [
              {
                required: true,
                message: "请选择供应商",
                trigger: "blur",
              },
            ],
          },
          {
            label: "关联项目",
            prop: "projectId",
            placeholder: " ",
            filterable: true,
          },
          {
            label: "关联预算",
            labelTip: "只关联已审核的预算",
            prop: "budgetId",
            rules: [
              {
                required: true,
                message: "请输入",
                trigger: "blur",
              },
            ],
          },
          {
            label: "附件",
            type: "upload",
            propsHttp: {
              res: "data",
              url: "attachId",
              name: "originalName",
            },
            dragFile: true,
            action:
              "/api/blade-resource/oss/endpoint/put-file-attach?code=private",
            display: true,
            span: 24,
            showFileList: true,
            multiple: true,
            limit: 10,
            prop: "attach",
          },
          {
            label: "",
            labelWidth: 0,
            prop: "items",
            type: "dynamic",
            span: 24,
            children: {
              align: "center",
              headerAlign: "center",
              showSummary: true,
              sumColumnList: [
                {
                  name: "num",
                  type: "sum",
                  decimals: 1,
                },
                {
                  name: "amount",
                  type: "sum",
                },
              ],
              rowAdd: () => {
                this.itemAdd();
              },
              rowDel: (row, done) => {
                done();
              },
              column: [
                {
                  label: "费用名称",
                  prop: "materialName",
                  placeholder: " ",
                  overHidden: true,
                  cell: true,
                  rules: [
                    {
                      required: true,
                      message: "请输入费用名称",
                      trigger: "blur",
                    },
                  ],
                },
                {
                  label: "用途",
                  prop: "purpose",
                  type: "textarea",
                  minRows: 1,
                  placeholder: " ",
                  overHidden: true,
                  cell: true,
                },
                {
                  label: "编码",
                  minWidth: 100,
                  placeholder: " ",
                  prop: "materialCode",
                  overHidden: true,
                  clearable: false,
                  cell: true,
                  rules: [
                    {
                      required: false,
                      message: "请选择编码",
                      trigger: "blur",
                    },
                  ],
                },
                {
                  label: "规格",
                  prop: "specification",
                  placeholder: " ",
                  overHidden: true,
                  disabled: true,
                },
                {
                  label: "数量",
                  prop: "num",
                  type: "number",
                  precision: 0,
                  placeholder: " ",
                  minWidth: 100,
                  cell: true,
                  rules: [
                    {
                      required: true,
                      message: "请输入数量",
                      trigger: "blur",
                    },
                  ],
                },
                {
                  label: "单价",
                  prop: "price",
                  type: "number",
                  controls: false,
                  precision: 2,
                  placeholder: " ",
                  cell: true,
                },
                {
                  label: "金额",
                  prop: "amount",
                  overHidden: true,
                  type: "number",
                  cell: true,
                  minWidth: 100,
                  precision: 2,
                  placeholder: " ",
                  rules: [
                    {
                      required: true,
                      message: "请输入金额",
                      trigger: "blur",
                    },
                  ],
                },
              ],
            },
          },
        ],
      },
      process: {},
      loading: false,
      payment: 0,
      typeDict: [],
      typeDictKeyValue: {},
      fromPath: "",
      isDraftPopupVisible: false,
      draftList: [],
      draftCount: 0,
      draftId: null,
    };
  },
  methods: {
    handleMaterialSubmit(selectList, row1) {
      if (selectList.length > 0) {
        const row = selectList[0];
        this.$nextTick(() => {
          this.$set(row1, "materialName", row.name);
          this.$set(row1, "materialCode", row.code);
          this.$set(row1, "specification", row.specification);
          this.$set(row1, "quality", row.quality);
          this.$set(row1, "gb", row.gb);
          this.$set(row1, "unit", row.unit);
          this.$set(row1, "cost", row.cost);
        });
      }
    },
    handleBudgetClear() {
      this.form.budgetId = null;
      this.form.items = [];
    },
    handleBudgetConfirm(selectList, row) {
      if (selectList && selectList.length > 0) {
        row.projectId = selectList[0].projectId;
        row.projectTitle = selectList[0].projectTitle;
        row.projectSerialNo = selectList[0].projectSerialNo;
        row.brand = selectList[0].brand;
        row.type = selectList[0].type;
      }
    },

    sumAmount() {
      this.$nextTick(() => {
        const itemAmount = this.form.items.reduce((acc, cur) => {
          return Number(acc) + Number(cur.amount);
        }, 0);
        this.$set(this.form, "amount", Number(itemAmount.toFixed(2)));
      });
    },
    async handleItemSelect(selectionList) {
      for (const item of selectionList) {
        const i = {
          budgetItemId: item.id,
          purpose: item.purpose,
          materialCode: item.materialCode,
          materialName: item.materialName,
          materialId: item.materialId,
          specification: item.specification,
          quality: item.quality,
          gb: item.gb,
          unit: item.unit,
          num: item.num,
          budgetNum: item.num,
          usedNum: item.applyNum ? item.applyNum : 0,
          amount: item.amount,
          price: item.price,
          remark: item.remark,
          cost: item.cost,
        };
        this.form.items.push(i);
      }
      this.sumAmount();
    },
    itemAdd() {
      if (!this.form.budgetId) {
        this.$message.warning("请选择预算");
        return;
      }
      this.$refs.porBudgetItemRef.init(this.form.budgetId);
    },
    projectClear() {
      const column = this.findObject(this.option.column, "budgetId");
      column.dicData = [];
      this.form.budgetId = null;
    },
    projectConfirm(selectionList) {
      if (selectionList) {
        this.form.projectTitle = selectionList[0].title;
        this.form.budgetId = null;
        this.form.items = [];
      }
    },
    getForm(processId) {
      this.getStartForm(processId).then((res) => {
        const _this = this;
        let { process } = res;
        this.form.processId = process.id;
        const option = _this.option;
        const { column } = option;
        column.forEach((col) => {
          if (col.value) col.value = _this.getDefaultValues(col.value);
        });
        if (_this.permission.wf_process_draft) {
          // 查询是否有草稿箱
          this.initDraft(process.id, process.key).then((data) => {
            this.draftCount = data.length;
            this.draftList = data;
            if (data && Array.isArray(data) && data.length > 0) {
              _this
                .$confirm("是否引用之前保存的草稿？", "提示", {})
                .then(() => {
                  this.isDraftPopupVisible = true; // 打开草稿弹窗
                });
            }
          });
        }
        _this.waiting = false;
      });
    },
    getFormByProcessDefKey(processDefKey) {
      this.getStartFormByProcessDefKey(processDefKey).then((res) => {
        const _this = this;
        let { process } = res;
        _this.form.processId = process.id;
        const option = _this.option;
        const { column } = option;
        column.forEach((col) => {
          if (col.value) col.value = _this.getDefaultValues(col.value);
        });
        if (_this.permission.wf_process_draft) {
          // 查询是否有草稿箱
          this.initDraft(process.id, process.key).then((data) => {
            this.draftCount = data.length;
            this.draftList = data;
            if (data && Array.isArray(data) && data.length > 0) {
              _this
                .$confirm("是否引用之前保存的草稿？", "提示", {})
                .then(() => {
                  this.isDraftPopupVisible = true; // 打开草稿弹窗
                });
            }
          });
        }
        _this.waiting = false;
      });
    },
    validateItems(items) {
      return !items || !items.some((item) => !item.num || !item.amount);
    },
    handleSubmit: debounce(function () {
      this.loading = true;
      if (this.form.inquiry) {
        this.form.inquiryState = 1;
      }
      const items = this.validateItems(this.form.items);
      if (items) {
        this.handleStartProcess(true)
          .then((done) => {
            this.$message.success("发起成功");
            if (this.fromPath) {
              this.handleCloseTag(this.fromPath);
            } else this.handleCloseTag("/ni/fin/cost-apply");
            done();
          })
          .catch(() => {
            this.loading = false;
          });
      } else {
        this.$message.warning("明细中存在未填写的数量/金额");
      }
    }, 1000),
    //选择草稿
    handleDraftSelect(selectedDraft) {
      //草稿版本与流程版本不一致
      if (!selectedDraft.sameVersion) {
        this.$confirm(
          "选中的草稿与当前流程版本不一致，是否继续引用？",
          "提示",
          {}
        ).then(() => {
          this.draftId = selectedDraft.id;
          this.form = JSON.parse(selectedDraft.variables);
          this.isDraftPopupVisible = false;
        });
      } else {
        this.draftId = selectedDraft.id;
        this.form = JSON.parse(selectedDraft.variables);
        this.form.draftId = selectedDraft.id;
      }
    },
    //删除草稿
    handleDraftDelete(draftId) {
      this.$confirm("是否删除选中的草稿箱数据？", "提示", {}).then(() => {
        this.$axios
          .post(`/api/blade-workflow/process/draft/delete/${draftId}`)
          .then((response) => {
            this.$message.success("草稿删除成功");
            this.draftCount = this.draftCount - 1;
            this.draftList = this.draftList.filter(
              (item) => item.id !== draftId
            );
          })
          .catch((error) => {
            this.$message.error("草稿删除失败，请重试");
          });
      });
    },
  },
};
</script>

<style lang="scss" scoped>
/deep/ .el-table__expand-icon {
  visibility: hidden;
}

/deep/ .el-link--inner {
  font-size: 12px;
}

/deep/ .el-upload-list__item-name {
  font-size: 12px;
}

.foot-item {
  position: fixed;
  bottom: 0;
  margin-left: -20px;
  // right: 0;
  z-index: 101;
  height: 66px;
  background-color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  -webkit-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
</style>

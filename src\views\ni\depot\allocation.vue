<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template #status="{ row, index }">
        <template v-if="row.status">
          <el-tag
            v-if="row.status === 1"
            size="mini"
            type="info"
            effect="plain"
          >
            {{ row.$status }}
          </el-tag>
          <el-tag
            v-else-if="row.status === 2"
            size="mini"
            type="success"
            effect="plain"
          >
            {{ row.$status }}
          </el-tag>
          <el-tag
            v-else-if="row.status === 0"
            size="mini"
            type="danger"
            effect="plain"
          >
            {{ row.$status }}
          </el-tag>
        </template>
      </template>
      <template #brand="{ row, disabled, size, index }">
        <el-tag
          v-if="row.brand"
          :size="size"
          :effect="row.brand === '1' ? 'dark ' : 'plain'"
        >
          {{ brandDictKeyValue[row.brand] }}
        </el-tag>
      </template>
      <template #serialNo="{ row, index }">
        <span
          :style="{
            color: colorName,
            fontWeight: 'bold',
            cursor: 'pointer',
            textDecoration: 'underline',
          }"
          @click="$refs.crud.rowView(row, index)"
          >{{ row.serialNo }}</span
        >
      </template>
      <template #numForm="{ row, index, disabled, size }">
        <el-input-number
          :size="size"
          v-if="row.stockNum > 0"
          v-model="row.num"
          controls-position="right"
          :max="row.stockNum"
        ></el-input-number>
        <el-input-number
          :size="size"
          v-else
          v-model="row.num"
          controls-position="right"
        ></el-input-number>
      </template>
      <template #menuLeft>
        <el-button
          type="danger"
          size="mini"
          icon="el-icon-delete"
          plain
          v-if="permission.ni_depot_allocation_delete"
          :disabled="selectionList.length < 1"
          @click="handleDelete"
          >删 除
        </el-button>
      </template>
      <template #menu="{ row, index, type, size }">
        <el-button
          type="text"
          icon="el-icon-s-promotion"
          :size="size"
          v-if="1 === row.status && permission.ni_depot_allocation_edit"
          @click="$refs.crud.rowEdit(row, index)"
          >编 辑
        </el-button>
        <el-button
          type="text"
          icon="el-icon-s-promotion"
          :size="size"
          v-if="1 === row.status && permission.ni_depot_allocation_delete"
          @click="$refs.crud.rowDel(row, index)"
          >删 除
        </el-button>
        <el-button
          type="text"
          icon="el-icon-s-promotion"
          :size="size"
          v-if="row.status === 1&&permission.ni_depot_allocation_audit"
          @click="rowSubmit(row)"
          >审核
        </el-button>
      </template>
      <template #itemsLabel>
        <span style="font-size: 16px; font-weight: 500"> 调拨明细 </span>
        <el-divider direction="vertical" />
        <el-tag>
          数量总计:
          <span style="font-weight: bolder; color: #f56c6c">
            {{
              Number(num).toLocaleString("zh-CN", {
                minimumFractionDigits: 2,
              })
            }}
          </span>
        </el-tag>
      </template>
      <template #itemsForm="{ row, index }">
        <allocation-item-table-list
          v-model="items"
          :depot-id="form.outDepotId"
          :brand="form.brand"
          @onAdd="handleAddSubmit"
          @onDel="handleDelSubmit"
          :before-add="beforeAdd"
          @onUpdate="handleItemUpdate"
        />
      </template>
    </avue-crud>
  </basic-container>
</template>
<script>
import { mapGetters } from "vuex";
import { dateNow1 } from "@/util/date";
import {
  add,
  audit,
  getDetail,
  getList,
  remove,
  update,
} from "@/api/ni/depot/allocation";
import MaterialSelect from "@/views/ni/base/components/MaterialSelect";
import LogOptDialog from "@/components/log-opt-dialog";
import CurrentStockSelectDialog from "@/views/ni/depot/components/CurrentStockSelectDialog";
import AllocationItemTableList from "@/views/ni/depot/components/AllocationItemTableList";

export default {
  name: "allocation",
  components: {
    AllocationItemTableList,
    MaterialSelect,
    LogOptDialog,
    CurrentStockSelectDialog,
  },
  computed: {
    ...mapGetters(["permission", "userInfo", "colorName"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.ni_depot_allocation_add, false),
        viewBtn: this.vaildData(this.permission.ni_depot_allocation_view, false),
        delBtn: this.vaildData(this.permission.ni_depot_allocation_delete, false),
        editBtn: this.vaildData(this.permission.ni_depot_allocation_edit, false),
      };
    },
    items() {
      return this.form.items || [];
    },
    num() {
      if (this.form.items)
        return (
          this.form.items
            .map((item) => (item.num ? item.num : 0))
            .reduce((prev, curr) => prev + Number(curr) * 10000, 0) / 10000
        );
      return "0";
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  data() {
    return {
      form: {
        outDepotId: null,
        items: [],
      },
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        searchEnter: true,
        span: 6,
        size: "mini",
        searchSize: "mini",
        delBtn: false,
        editBtn: false,
        dialogFullscreen: true,
        searchIndex: 3,
        searchIcon: true,
        height: "auto",
        calcHeight: 30,
        tip: false,
        labelWidth: 110,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: false,
        align: "center",
        viewBtn: false,
        selection: true,
        dialogClickModal: false,
        column: [
          {
            label: "状态",
            prop: "status",
            type: "select",
            minWidth: 70,
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_depot_allocation_status",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            dataType: "number",
            fixed: "left",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            search: true,
          },
          {
            label: "调拨主题",
            prop: "title",
            minWidth: 120,
            search: true,
            rules: [
              {
                required: true,
                message: "请输入",
                trigger: "blur",
              },
            ],
          },
          {
            label: "调拨编号",
            prop: "serialNo",
            placeholder: "系统自动生成",
            search: true,
            minWidth: 110,
            disabled: true,
          },
          {
            label: "调拨时间",
            prop: "opTime",
            search: true,
            searchRange: true,
            minWidth: 110,
            overHidden: true,
            type: "date",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            rules: [
              {
                required: true,
                message: "请输入",
                trigger: "blur",
              },
            ],
          },
          {
            label: "账套",
            prop: "brand",
            type: "radio",
            minWidth: 90,
            value: "1",
            dicData: [],
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            search: true,
          },
          {
            label: "调出仓",
            prop: "outDepotId",
            placeholder: " ",
            type: "select",
            overHidden: true,
            dicUrl: "/api/ni/base/depot/info/list?type=7,8&status=2",
            props: {
              label: "name",
              value: "id",
            },
            width: 110,
            rules: [
              {
                required: true,
                message: "请选择",
                trigger: "blur",
              },
            ],
          },
          {
            label: "调入仓",
            prop: "inDepotId",
            type: "select",
            dicUrl: "/api/ni/base/depot/info/list?type=7,8&status=2",
            props: {
              label: "name",
              value: "id",
            },
            overHidden: true,
            width: 110,
            rules: [
              {
                required: true,
                message: "请选择调入仓",
                trigger: "blur",
              },
              {
                trigger: "blur",
                validator: (rule, value, callback) => {
                  if (value === this.form.outDepotId) {
                    callback(new Error("调入仓不能和调出仓相同"));
                  } else {
                    callback();
                  }
                },
              },
            ],
          },
          {
            label: "库管人员",
            prop: "keeperId",
            type: "select",
            remote: true,
            dicUrl: `/api/blade-user/search/otj/user?name={{key}}`,
            props: {
              label: "realName",
              value: "id",
            },
            dicFormatter: (data) => {
              return data.data.records;
            },
            search: true,
            hide: true,
            showColumn: false,
          },
          {
            label: "库管人员",
            prop: "keeperName",
            minWidth: 90,
            overHidden: true,
            display: false,
          },

          {
            label: "数量合计",
            prop: "total",
            minWidth: 90,
            overHidden: true,
            display: false,
          },
          {
            label: "备注",
            prop: "remark",
            hide: true,
            type: "textarea",
            span: 24,
            minRows: 3,
          },
          {
            label: "调拨明细",
            prop: "items",
            labelPosition: "top",
            hide: true,
            showColumn: false,
            span: 24,
          },
        ],
      },
      data: [],
      brandDict: [],
      brandDictKeyValue: {},
    };
  },
  mounted() {
    this.dictInit();
  },
  methods: {
    handleDelSubmit(row, index) {
      this.items = this.items.splice(index, 1);
    },
    handleItemUpdate(newItems) {
      this.items = newItems;
    },
    handleAddSubmit(selectionList) {
      if (selectionList) {
        selectionList.forEach((item) => {
          this.items.push({
            ...item,
            currentStockId: item.id,
            id: null,
            stockNum: item.num,
            $cellEdit :false,
            cell :false
          });
        });
      }
    },
    rowSubmit(row) {
      this.$confirm("审核后将生成调拨出入库记录，是否继续?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return audit(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    beforeAdd(done) {
      if (!this.form.outDepotId) {
        this.$message.warning("请选择调出仓");
        return;
      }
      if (!this.form.brand) {
        this.$message.warning("请选择用友套账号");
        return;
      }
      done(this.form.outDepotId);
    },
    dictInit() {
      this.$http
        .get("/api/blade-system/dict-biz/dictionary?code=ni_brand")
        .then((res) => {
          const column = this.findObject(this.option.column, "brand");
          column.dicData = res.data.data;
          this.brandDict = res.data.data;
          this.brandDictKeyValue = this.brandDict.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      const status2 = this.selectionList.some((item) => item.status === 2);
      if (status2) {
        this.$message.warning("请选择未审核的数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        remove(this.ids).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
      });
    },
    rowSave(row, done, loading) {
      add(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          window.console.log(error);
        }
      );
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          console.log(error);
        }
      );
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        remove(row.id).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
      });
    },
    beforeOpen(done, type) {
      if (["add"].includes(type)) {
        this.form.keeperId = this.userInfo.user_id;
        this.form.opTime = dateNow1();
        this.form.items = [];
      }
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then((res) => {
          this.form = res.data.data;
          this.form.items = this.form.items.map((item) => {
            return {
              ...item,
              $cellEdit: false,
              cell: false,
            };
          });
        });
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      const query = Object.assign(params, this.query);
      query.recordType = 1;
      if (query.warehousingTime && query.warehousingTime.length > 1) {
        query.startWarehousingTime = query.warehousingTime[0];
        query.endWarehousingTime = query.warehousingTime[1];
        query.warehousingTime = null;
      }
      query.descs = "id";
      getList(page.currentPage, page.pageSize, query).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
  },
};
</script>

<style scoped></style>

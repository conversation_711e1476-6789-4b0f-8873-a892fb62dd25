<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page.sync="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @refresh-change="refreshChange"
               @on-load="onLoad">
      <template slot="menuLeft">
        <el-button
          type="primary"
          size="mini"
          icon="el-icon-plus"
          v-if="
            permissionList.addBtn ||
            userInfo.role_name.includes('admin')
          "
          @click="handleAdd"
          >新增
        </el-button>
        <el-button
          type="primary"
          size="mini"
          icon="el-icon-food"
          @click="handleStartMealAdd"
          >加餐申请
        </el-button>
        <el-button
          type="danger"
          size="mini"
          icon="el-icon-delete"
          plain
          v-if="
            permissionList.delBtn ||
            userInfo.user_id === row.createUser||
            userInfo.role_name.includes('admin')
          "
          @click="handleDelete"
          >删 除
        </el-button>
      </template>

      <!-- 操作栏按钮 -->
      <template #menu="{ row, index, size }">
        <el-button
          type="text"
          icon="el-icon-view"
          :size="size"
          @click.native="rowFlow(row, index)"
          >详情
        </el-button>
      </template>

      <template #serialNo="{ row }">
        <flow-timeline-popover
          v-if="row.processInsId"
          :process-ins-id="row.processInsId"
          :form-key="formKey"
          :process-def-key="processDefKey"
          v-model="row.serialNo"
          :flow.sync="row.flow"
          trigger="click"
          lazy
        />
        <span v-else>{{ row.serialNo }}</span>
      </template>

      <template #mealData="{ row }">
        <flow-timeline-popover
          v-if="row.processInsId"
          :process-ins-id="row.processInsId"
          :form-key="formKey"
          :process-def-key="processDefKey"
          v-model="row.mealData"
          :flow.sync="row.flow"
          trigger="click"
          lazy
        />
        <span v-else>{{ row.mealData }}</span>
      </template>

      <template #status="{ row }">
        <el-tag v-if="row.status === 0" size="mini" type="info">
          {{ statusDictKeyValue[row.status] }}
        </el-tag>
        <el-tag v-else-if="row.status === 1" size="mini" effect="plain">
          {{ statusDictKeyValue[row.status] }}
        </el-tag>
        <el-tag v-else-if="row.status === 2" size="mini" effect="plain">
          {{ statusDictKeyValue[row.status] }}
        </el-tag>
        <el-tag v-else-if="row.status === 3" size="mini" type="danger">
          {{ statusDictKeyValue[row.status] }}
        </el-tag>
        <el-tag
          v-else-if="row.status === 4"
          size="mini"
          type="warning"
          effect="plain"
        >
          {{ statusDictKeyValue[row.status] }}
        </el-tag>
        <el-tag
          v-else-if="row.status === 5"
          size="mini"
          type="warning"
          effect="plain"
        >
          {{ statusDictKeyValue[row.status] }}
        </el-tag>
        <el-tag
          v-else-if="row.status === 6"
          size="mini"
          type="danger"
          effect="plain"
        >
          {{ statusDictKeyValue[row.status] }}
        </el-tag>
        <el-tag
          v-else-if="row.status === 9"
          size="mini"
          type="success"
          effect="plain"
        >
          {{ statusDictKeyValue[row.status] }}
        </el-tag>
      </template>

      <!-- 申请人选择插槽 -->
      <template #userNameForm="{ disabled, size }">
        <user-select
          v-model="form.userId"
          :size="size"
          :disabled="disabled"
          @confirm="handleUserSelectConfirm"
        ></user-select>
      </template>

      <template #standard="{ row }">
        <span v-if="row.standard !== undefined && standardDictKeyValue[row.standard]">
          {{ standardDictKeyValue[row.standard] }}
        </span>
        <span v-else>{{ row.standard }}</span>
      </template>

      <template #drink="{ row }">
        <span v-if="row.drink !== undefined && drinkDictKeyValue[row.drink]">
          {{ drinkDictKeyValue[row.drink] }}
        </span>
        <span v-else>{{ row.drink }}</span>
      </template>

      <!-- 搜索栏申请人选择 -->
      <template #userNameSearch="{ disabled, size }">
        <user-select
          v-model="query.createUser"
          :size="size"
          :disabled="disabled"
          @confirm="handleSearchUserSelectConfirm"
        ></user-select>
      </template>

    </avue-crud>

    <!-- 详情抽屉 -->
    <el-drawer
      :visible.sync="detailVisible"
      :title="form.mealData ? form.mealData + ' 加餐申请' : '加餐申请详情'"
      custom-class="wf-drawer"
      size="100%"
      append-to-body
    >
      <task-detail
        v-if="detailVisible"
        :taskId="form.taskId"
        :processInstanceId="form.processInsId"
      />
    </el-drawer>
  </basic-container>
</template>

<script>
  import UserSelect from "@/components/user-select";
  import {getList, getDetail, add, update, remove} from "@/api/ni/meal/mealAdd";
  import {getUser} from "@/api/system/user";
  import option from "@/const/ni/meal/mealAdd";
  import {mapGetters} from "vuex";
  import FlowTimelinePopover from "@/components/wf-flow-timeline/popover";
  import exForm from "@/views/plugin/workflow/mixins/ex-form";
  import {flow} from "@/api/plugin/workflow/process";
  import TaskDetail from "@/views/plugin/workflow/ops/detail.vue";
  import {getByModule} from "@/api/ni/base/module-flow";

  export default {
    components: {
      FlowTimelinePopover,
      TaskDetail,
      UserSelect
    },
    mixins: [exForm],
    data() {
      return {
        form: {},
        query: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        option: option,
        data: [],
        formKey: "ni_meal_add",
        processDefKey: "process_addfood",
        module: "ni_meal_add",
        statusDict: [],
        statusDictKeyValue: {},
        standardDict: [],
        standardDictKeyValue: {},
        drinkDict: [],
        drinkDictKeyValue: {},
        detailVisible: false // 详情抽屉是否可见
      };
    },
    computed: {
      ...mapGetters(["permission", "userInfo"]),
      permissionList() {
        return {
          addBtn: this.vaildData(this.permission.mealAdd_add, false),
          viewBtn: this.vaildData(this.permission.mealAdd_view, false),
          delBtn: this.vaildData(this.permission.mealAdd_delete, false),
          editBtn: this.vaildData(this.permission.mealAdd_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    created() {
      this.loadDefaultFlowKey();
    },
    mounted() {
      this.dictInit();
    },
    methods: {
      // 加载默认流程键
      loadDefaultFlowKey() {
        getByModule(this.module).then((res) => {
          const moduleFlow = res.data.data;
          if (moduleFlow) {
            this.processDefKey = moduleFlow.flowKey;
          }
        });
      },

      dictInit() {
        this.$http
        .get("/api/blade-system/dict/dictionary?code=data_status")
        .then((res) => {
          const column = this.findObject(this.option.column, "status");
          column.dicData = res.data.data;
          this.statusDict = res.data.data;
          this.statusDictKeyValue = this.statusDict.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });

        // 初始化用餐标准字典
        this.$http
          .get("/api/blade-system/dict-biz/dictionary?code=ni_meal_add_stand")
          .then((res) => {
            this.standardDict = res.data.data;
            this.standardDictKeyValue = this.standardDict.reduce((acc, cur) => {
              acc[cur.dictKey] = cur.dictValue;
              return acc;
            }, {});
          });

        // 初始化酒水标准字典
        this.$http
          .get("/api/blade-system/dict-biz/dictionary?code=ni_meal_add_estand")
          .then((res) => {
            this.drinkDict = res.data.data;
            this.drinkDictKeyValue = this.drinkDict.reduce((acc, cur) => {
              acc[cur.dictKey] = cur.dictValue;
              return acc;
            }, {});
          });
      },

      rowSave(row, done, loading) {
        // 确保targetBill和actualBill是数字类型
        if (row.targetBill !== undefined && row.targetBill !== null) {
          row.targetBill = Number(row.targetBill);
        }
        if (row.actualBill !== undefined && row.actualBill !== null) {
          row.actualBill = Number(row.actualBill);
        }

        // 确保mealData日期格式正确

        // If userId is provided but userName or department is missing, fetch user info
        if (row.userId && (!row.userName || !row.department)) {
          getUser(row.userId).then(res => {
            const userData = res.data.data;
            if (userData) {
              row.userName = userData.realName;
              row.department = userData.deptName;
            }
            // Continue with saving
            this.saveData(row, done, loading);
          }).catch(() => {
            // Continue with saving even if user fetch fails
            this.saveData(row, done, loading);
          });
        } else {
          // Continue with saving if all data is already present
          this.saveData(row, done, loading);
        }
      },

      saveData(row, done, loading) {
        add(row).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          loading();
          window.console.log(error);
        });
      },
      rowUpdate(row, _index, done, loading) {
        // 确保targetBill和actualBill是数字类型
        if (row.targetBill !== undefined && row.targetBill !== null) {
          row.targetBill = Number(row.targetBill);
        }
        if (row.actualBill !== undefined && row.actualBill !== null) {
          row.actualBill = Number(row.actualBill);
        }

        // If userId is provided but userName or department is missing, fetch user info
        if (row.userId && (!row.userName || !row.department)) {
          getUser(row.userId).then(res => {
            const userData = res.data.data;
            if (userData) {
              row.userName = userData.realName;
              row.department = userData.deptName;
            }
            // Continue with updating
            this.updateData(row, done, loading);
          }).catch(() => {
            // Continue with updating even if user fetch fails
            this.updateData(row, done, loading);
          });
        } else {
          // Continue with updating if all data is already present
          this.updateData(row, done, loading);
        }
      },

      updateData(row, done, loading) {
        update(row).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          loading();
          console.log(error);
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });
        } else if (type === "add") {
          // Set current user info as default for new records
          const userInfo = this.$store.getters.userInfo;
          this.form.userId = userInfo.user_id;
          this.form.userName = userInfo.nick_name;

          // 确保department是字符串类型
          if (Array.isArray(userInfo.dept_name)) {
            this.form.department = userInfo.dept_name.join(','); // 将数组转换为逗号分隔的字符串
          } else {
            this.form.department = userInfo.dept_name;
          }
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        console.log("搜索原始参数:", JSON.stringify(params));

        // 清空之前的查询条件
        this.query = {};

        // 处理申请人搜索 - 保留原有的多参数名称方式
        if (params.userName) {
          this.query.userName = params.userName;
          this.query.createUserName = params.userName;
          console.log("设置申请人搜索:", params.userName);
        }

        // 处理部门搜索
        if (params.department) {
          this.query.department = params.department;
          console.log("设置部门搜索:", params.department);
        }

        // 处理用餐日期搜索
        if (params.mealData) {
          this.query.mealData = params.mealData;
          console.log("用餐日期搜索:", this.query.mealData);
        }

        // 复制其他搜索参数
        Object.keys(params).forEach(key => {
          if (params[key] !== null && params[key] !== undefined && params[key] !== '' &&
              key !== 'userName' && key !== 'department' && key !== 'mealData') {
            this.query[key] = params[key];
          }
        });

        // 重置到第一页
        this.page.currentPage = 1;

        console.log("最终搜索参数:", JSON.stringify(this.query));

        // 传递参数到onLoad方法
        this.onLoad(this.page, this.query);

        // 执行回调
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      refreshChange() {
        this.onLoad(this.page, this.query);
      },
      onLoad(page) {
        this.loading = true;

        // 创建一个新的参数对象，避免修改原始对象
        const queryParams = { ...this.query };

        // 添加模糊搜索参数，使用like查询
        if (queryParams.userName) {
          queryParams.userNameLike = queryParams.userName;
        }
        if (queryParams.department) {
          queryParams.departmentLike = queryParams.department;
        }

        console.log("最终API请求参数:", JSON.stringify(queryParams));

        getList(
          page.currentPage,
          page.pageSize,
          queryParams
        ).then(res => {
          console.log("API响应:", JSON.stringify(res.data));
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;

          // 获取流程状态信息
          const processInsIds = this.data
            .filter(item => item.processInsId)
            .map(item => item.processInsId)
            .join(',');

          if (processInsIds) {
            flow({ processInsId: processInsIds }).then(flowRes => {
              const flowData = flowRes.data.data;
              this.data.forEach(item => {
                if (item.processInsId) {
                  item.flow = flowData.filter(flow => flow.processInsId === item.processInsId);
                }
              });
            });
          }

          this.loading = false;
          this.selectionClear();
        });
      },

      // 查看流程详情
      rowFlow(row, index) {
        if (row.processInsId) {
          this.dynamicRoute(
            {
              processInstanceId: row.processInsId,
              id: row.id,
            },
            "detail",
            true
          ).then(() => {
            this.form = { ...row };
            this.detailVisible = true;
          });
        } else {
          this.$refs.crud.rowView(row, index);
        }
      },

      // 新增加餐记录
      handleAdd() {
        this.$refs.crud.rowAdd();
      },

      // 发起加餐申请
      handleStartMealAdd() {
        if (!this.processDefKey) {
          this.$message.error("流程定义键未正确加载，请刷新页面重试");
          return;
        }

        this.dynamicRoute(
          {
            processDefKey: this.processDefKey,
            formKey: this.formKey
          },
          "start"
        );
      },

      // 处理用户选择确认
      handleUserSelectConfirm(userId, userName) {
        // 设置用户ID
        this.form.userId = userId;

        // 设置用户名
        this.form.userName = userName;

        // 获取用户详细信息，包括部门
        if (userId) {
          getUser(userId).then(res => {
            const userData = res.data.data;
            if (userData) {
              // 设置部门
              this.form.department = userData.deptName;
            }
          });
        }
      },

      // 处理搜索栏用户选择确认
      handleSearchUserSelectConfirm(_userId, userName) {
        // 设置搜索条件中的用户名
        this.query.userName = userName;

        // 触发搜索
        this.onLoad(this.page, this.query);
      }
    }
  };
</script>

<style scoped>
/* Custom styling for status tags */
.el-tag.el-tag--mini.el-tag--success.el-tag--plain {
  background-color: transparent;
  border-color: #67C23A;
  color: #67C23A;
}
</style>

<style>
</style>

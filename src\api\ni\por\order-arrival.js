import request from "@/router/axios";

export const arrivalSave = (row) => {
  return request({
    url: "/api/ni/por/order/arrival/save",
    method: "post",
    data: row,
  });
};

export const initData = (orderId) => {
  return request({
    url: "/api/ni/por/order/arrival/initData",
    method: "get",
    params: {
      orderId,
    },
  });
};

export const listByApplyId = (applyId) => {
  return request({
    url: "/api/ni/por/order/arrival/listByApplyId",
    method: "get",
    params: {
      applyId,
    },
  });
};

export const detail = (id, applyId) => {
  return request({
    url: "/api/ni/por/order/arrival/rootDetail",
    method: "get",
    params: {
      id,
      applyId,
    },
  });
};
export const inspection = (row) => {
  return request({
    url: "/api/ni/por/order/arrival/inspection",
    method: "post",
    data: row,
  });
};

export const listByOrderId = (orderId) => {
  return request({
    url: "/api/ni/por/order/arrival/listByOrderId",
    method: "get",
    params: {
      orderId,
    },
  });
};

export const arrivalNumByOrderId = (id) => {
  return request({
    url: "/api/ni/por/order/arrival/arrivalNumById",
    method: "get",
    params: {
      id
    },
  });
};
export const getPage = (current, size, params) => {
  return request({
    url: "/api/ni/por/order/arrival/page",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};
export const getItemPage = (current, size, params) => {
  return request({
    url: "/api/ni/por/order/arrival/item/page",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getItemListByApplyItemId = (applyItemId) => {
  return request({
    url: "/api/ni/por/order/arrival/itemsByApplyItemId",
    method: "get",
    params: {
      applyItemId,
    },
  });
};

export const getUnStockInOrderArrivalList = (materialId, brand) => {
  return request({
    url: "/api/ni/por/order/arrival/unStockInOrderArrivalList",
    method: "get",
    params: {
      materialId,
      brand,
    },
  });
};
export const changeRemark = (ids, remark, itemType) => {
  return request({
    url: "/api/ni/por/order/arrival/changeRemark",
    method: "post",
    params: {
      ids,
      itemType,
      remark,
    },
  });
};
export const unStockInPage = (current, size, params) => {
  return request({
    url: "/api/ni/por/order/arrival/item/v2/unStockInPage",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};
export const unQualifiedPage = (current, size, params) => {
  return request({
    url: "/api/ni/por/order/arrival/item/unQualifiedPage",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};
export const canBackPage = (current, size, params) => {
  return request({
    url: "/api/ni/por/order/arrival/item/canBackPage",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getItemList = (params) => {
  return request({
    url: "/api/ni/por/order/arrival/item/list",
    method: "get",
    params,
  });
};
export const getItemsPrintData = (id) => {
  return request({
    url: "/api/ni/por/order/arrival/getItemsPrintData",
    method: "get",
    params: {
      id,
    },
  });
};
export const toVoid = (ids) => {
  return request({
    url: "/api/ni/por/order/arrival/toVoid",
    method: "post",
    params: {
      ids,
    },
  });
};
export const changeDesignationInspectionUserId = (
  ids,
  designationInspectionUserId
) => {
  return request({
    url: "/api/ni/por/order/arrival/changeDesignationInspectionUserId",
    method: "post",
    params: {
      ids,
      designationInspectionUserId,
    },
  });
};

export const reject = (ids, reason) => {
  return request({
    url: "/api/ni/por/order/arrival/item/reject",
    method: "post",
    params: {
      ids,
      reason,
    },
  });
};
export const printSign = (ids) => {
  return request({
    url: "/api/ni/por/order/arrival/printSign",
    method: "post",
    params: {
      ids,
    },
  });
};
export const changeMaterial = (ids, materialId) => {
  return request({
    url: "/api/ni/por/order/arrival/changeMaterial",
    method: "post",
    params: {
      ids,
      materialId,
    },
  });
};
export const changePurposeUserId = (ids, purposeUserId) => {
  return request({
    url: "/api/ni/por/order/arrival/changePurposeUserId",
    method: "post",
    params: {
      ids,
      purposeUserId,
    },
  });
};

export  const getItemPageWithUnOrder = (current, size, params) => {
  return request({
    url: "/api/ni/por/order/arrival/item/v1/withUnOrderPage",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail=(id)=>{
  return request({
    url: "/api/ni/por/order/arrival/detail",
    method: "get",
    params: {id},
  });
}

export  const startFixedAssetConfirm = (data) => {
  return request({
    url: "/api/ni/por/order/arrival/startFixedAssetConfirm",
    method: "post",
    data: data,
  });
};

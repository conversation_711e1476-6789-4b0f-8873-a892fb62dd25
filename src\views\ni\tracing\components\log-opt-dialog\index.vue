<template>
  <el-drawer
    title="操作日志"
    :visible.sync="visible"
    :direction="direction"
    size="65%"
    append-to-body>
    <basic-container>
      <avue-crud
        v-if="visible"
        :option="option"
        :table-loading="loading"
        :data="data"
        :page.sync="page"
        :before-open="beforeOpen"
        v-model="form"
        ref="crud"
        @search-change="searchChange"
        @search-reset="searchReset"
        @selection-change="selectionChange"
        @current-change="currentChange"
        @size-change="sizeChange"
        @refresh-change="refreshChange"
        @on-load="onLoad"
      >
        <template #success="{row}">
          <el-tag size="mini" :type="row.success? '': 'danger'">{{ row.success ? '是' : '否' }}</el-tag>
        </template>
      </avue-crud>
    </basic-container>
  </el-drawer>
</template>

<script>
import {getOptList, getOptLogs} from "@/api/logs";

export default {
  name: "logOptDialog",
  props: {
    module: String,
  },
  data() {
    return {
      businessId: -1,
      visible: false,
      direction: 'rtl',
      loading: false,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      form: {},
      query: {},
      option: {
        searchIndex: 2,
        searchIcon: true,
        labelWidth: 110,
        align: 'center',
        size: 'mini',
        searchSize: 'mini',
        height: 'auto',
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        viewBtn: true,
        editBtn: false,
        addBtn: false,
        delBtn: false,
        menuWidth: 120,
        dialogType: 'drawer',
        column: [
          {
            label: "请求ip",
            prop: "requestIp",
          },
          {
            label: "模块名称",
            prop: "module",
            type: 'select',
            dicData: [
              { label: "对外质检报告", value: 'ni_tracing_quality_inspection_modified' },
              { label: "对内质检报告", value: 'ni_tracing_quality_inspection' },
            ],
            search: true,
          },
          {
            label: "操作时间",
            prop: "createTime",
            type: 'datetime',
            format: 'yyyy-MM-dd HH:mm:ss',
            valueFormat: 'yyyy-MM-dd HH:mm:ss',
            searchSpan: 12,
            searchRange: true,
            search: true,
            width: '180'
          },
          {
            label: "操作人",
            prop: "operator",
            overHidden: true,
            search: true,
          },
          {
            label: "操作内容",
            prop: "content",
            type: "textarea",
            span: 24,
            minRows: 2,
            overHidden: true,
          },
          {
            label: "是否成功",
            prop: "success",
            type: 'select',
            dicData: [{
              label: '是',
              value: true
            }, {
              label: '否',
              value: false
            }],
            search: true,
          },
          {
            label: "执行时长(ms)",
            prop: "time",
          },
          {
            label: "用户代理",
            prop: "userAgent",
            span: 24,
            hide: true
          },
          {
            label: "请求数据",
            prop: "params",
            type: "textarea",
            span: 24,
            minRows: 2,
            hide: true
          }
        ]
      },
      data: [],
    }
  },
  methods: {
    init(id) {
      this.businessId = id
      this.visible = true
    },
    beforeOpen(done, type) {
      const that = this
      if (["edit", "view"].includes(type)) {
        getOptLogs(this.form.id).then(res => {
          that.form = res.data.data;
        });
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      const param = {
        module: this.module,
        businessId: this.businessId,
        ...params,
        ...this.query
      }
      if (param.createTime && param.createTime.length > 1) {
        param.startTime = param.createTime[0]
        param.endTime = param.createTime[1]
        param.createTime = null
      }
      getOptList(page.currentPage, page.pageSize, param).then(res => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    }

  }
}
</script>

<style scoped>

</style>

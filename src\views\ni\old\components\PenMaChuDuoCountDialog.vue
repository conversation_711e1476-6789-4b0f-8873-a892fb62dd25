<script>
import {count} from "@/api/ni/old/penmaChuduo";

export default {
  data() {
    return {
      title: '喷码出垛产量统计',
      visible: false,
      option: {
        header: false,
        menu: false,
        search: false,
        editBtn: false,
        delBtn: false,
        dialogFullscreen: true,
        dialogDrag: true,
        size: "mini",
        align: "center",
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        span: 8,
        border: true,
        column: [
          {
            label: '编码',
            prop: 'youyinCunhuodaleimaibiao',
            weight: 100,
            overHidden: true,
          },
          {
            label: '规格',
            prop: 'guige',
            type: 'input',
            weight: 100,
            overHidden: true,

          },
          {
            label: '外包装',
            prop: 'waibaozhuang',
            weight: 100,
            overHidden: true,
          },
          {
            label: '箱数',
            prop: 'benduoXiangshu',
            weight: 100,
            overHidden: true,
          },
        ]
      },
      form: {},
      data: [],
      loading: false,
      query: {}
    }
  },
  methods: {
    onShow(form, title) {
      if (title)
        this.title = title
      this.query = form
      this.data = []
      this.visible = true
    },
    handleClose(done) {
      this.visible = false;
      if (done && typeof done == "function") done();
    },
    handleConfirm() {
      this.$emit('confirm', this.query);
      this.handleClose()
    },
    onLoad() {
      this.loading = true
      count(this.query.startDate, this.query.endDate).then((res) => {
        this.data = res.data.data
        this.loading = false
      })
    },
  }
}
</script>

<template>
  <el-dialog :title="title" :visible.sync="visible" width="450px" append-to-body>
    <avue-crud v-if="visible" :option="option" :table-loading="loading" :data="data" ref="crud" v-model="form"
               @on-load="onLoad">
    </avue-crud>
    <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose" size="mini">取 消</el-button>
        <el-button type="primary" @click="handleConfirm" size="mini"
        >确 定</el-button
        >
      </span>
  </el-dialog>
</template>

<style scoped>

</style>

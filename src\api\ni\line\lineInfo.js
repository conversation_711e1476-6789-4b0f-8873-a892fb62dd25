import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/lineInfo/lineInfo/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/lineInfo/lineInfo/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/lineInfo/lineInfo/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/lineInfo/lineInfo/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/lineInfo/lineInfo/submit',
    method: 'post',
    data: row
  })
}



export const getLineDeviceList = (lineUniqueNumber) => {
  return request({
    url: '/api/line/deviceManage/getLineDeviceList',
    method: 'get',
    params: {
      lineUniqueNumber
    }
  })
}





export const getSpacerList = (current, size, lineUniqueNumber) => {
  return request({
    url: '/api/line/spacerInventory/list',
    method: 'get',
    params: {
      lineUniqueNumber,
      type:2,
      current,
      size
    }
  })
}


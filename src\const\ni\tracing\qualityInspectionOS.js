export default {
  align: "center",
  size: "mini",
  searchSize: "mini",
  height: "auto",
  calcHeight: 30,
  tip: false,
  searchIcon: true,
  searchShow: true,
  indexLabel: "序号",
  searchMenuSpan: 6,
  searchIndex: 3,
  border: true,
  index: false,
  addBtn: true,
  editBtn: true,
  delBtn: true,
  selection: true,
  dialogClickModal: false,
  menuWidth: 220,
  labelWidth: 150,
  column: [
    {
      label: "编码",
      prop: "code",
      type: "input",
      width: 120,
      disabled: true,
      search: true,
      placeholder: "系统默认生成",
    },
    {
      label: "发布状态",
      prop: "publishStatus",
      type: "select",
      addDisplay: false,
      editDisplay: false,
      width: 80,
      dicData: [
        {
          label: "已发布",
          value: "1",
        },
        {
          label: "未发布",
          value: "0",
        },
      ],
      search: true,
      html: true,
      formatter: function (val, value, label) {
        if (value === "1") {
          return `<span style="color:green">${label}</span>`;
        } else {
          return `<span style="color:red">${label}</span>`;
        }
      },
    },
    {
      label: "发货日期",
      prop: "shipDate",
      type: "date",
      format: "yyyy-MM-dd",
      valueFormat: "yyyy-MM-dd",
      width: 100,
    },
    {
      label: "发货编号",
      prop: "shipCode",
      type: "input",
      width: 100,
    },
    {
      label: "备货id",
      prop: "stockId",
      type: "input",
      width: 100,
      search: true,
    },
    {
      label: "质检日期",
      prop: "date",
      type: "date",
      format: "yyyy-MM-dd",
      valueFormat: "yyyy-MM-dd",
      width: 100,
      search: true,
      labelSuffix: "<span style='color: red;'>*</span>",
      rules: [
        {
          required: true,
          message: "质检日期为必填项",
          trigger: ["blur", "change"],
        },
      ],
      html: true,
    },
    {
      label: "班次",
      prop: "shift",
      type: "select",
      width: 80,
      dicData: [
        {
          label: "白班",
          value: "白班",
        },
        {
          label: "夜班",
          value: "夜班",
        },
      ],
    },
    {
      label: "品牌",
      prop: "brandName",
      type: "select",
      width: 80,
      search: true,
      dicData: [
        {
          label: "至简",
          value: "至简",
        },
        {
          label: "绿能",
          value: "绿能",
        },
      ],
    },
    {
      label: "国外箱号",
      prop: "outerPackaging",
      type: "select",
      dicUrl:
        "/api/ni/product/packaging/list?innerPark=0&current=1&size=20&status=1&&name={{key}}",
      props: {
        label: "name",
        value: "name",
        desc: "type",
      },
      search: true,
      remote: true,
      dicFormatter: (data) => {
        return data.data.records;
      },
      filterable: true,
      placeholder: "请选择内包装",
      showColumn: true,
      overHidden: true,
      minWidth: 120,
    },

    {
      label: "内包装",
      prop: "innerPackages",
      type: "select",
      dicUrl:
        "/api/ni/product/packaging/list?innerPark=1&current=1&size=20&status=1",
      props: {
        label: "name",
        value: "name",
        desc: "type",
      },
      search: true,
      remote: true,
      dicFormatter: (data) => {
        return data.data.records;
      },
      filterable: true,
      placeholder: "请选择内包装",
      showColumn: true,
      overHidden: true,
      minWidth: 120,
      multiple: true,
      collapseTags: false,
      multipleLimit: 0,
      formatter: (row, column) => {
        if (row.innerPackages && row.innerPackages.length > 0) {
          return row.innerPackages.join("、");
        }
        return column == null ? "" : column.placeholder;
      },
      clearable: true,
    },
    {
      label: "规格",
      prop: "spec",
      type: "select",
      dicUrl: "/api/ni/product/spec/list",
      props: {
        label: "name",
        value: "name",
        desc: "code",
      },
      remote: true,
      dicFormatter: (data) => {
        return data.data;
      },
      filterable: true,
      placeholder: "请选择规格",
      showColumn: true,
      overHidden: true,
      minWidth: 120,
      search: true,
    },
    {
      label: "件数",
      prop: "quantity",
      type: "number",
      width: 100,
    },
    {
      label: "重量（kg）",
      prop: "weight",
      type: "number",
      width: 100,
    },
    {
      label: "桶号说明",
      prop: "bucketNumber",
      type: "input",
      width: 160,
      search: true,
    },
    {
      label: "发货批号",
      prop: "batchCode",
      type: "input",
      width: 160,
      search: true,
    },
    {
      label: "备注",
      prop: "remark",
      type: "input",
      width: 160,
      search: true,
    },
    {
      label: "含水550度",
      prop: "waterContent",
      type: "input",
      width: 100,
    },
    {
      label: "变异系数",
      prop: "coefficientOfVariation",
      type: "input",
      width: 100,
    },
    {
      label: "敦实堆积密度",
      prop: "solidBulkDensity",
      type: "input",
      width: 100,
    },
    {
      label: "欧标堆积密度",
      prop: "enBulkDensity",
      type: "input",
      width: 100,
    },
    {
      label: "粒度合格率(%)",
      prop: "particleSizeQualifiedRate",
      type: "input",
      width: 110,
    },
    {
      label: "粒度",
      prop: "particleSize",
      type: "input",
      width: 60,
    },
    {
      label: "温升10g",
      prop: "temperatureRise10",
      type: "input",
      width: 70,
    },
    {
      label: "温升30g",
      prop: "temperatureRise30",
      type: "input",
      width: 70,
    },
    {
      label: "温升50g",
      prop: "temperatureRise50",
      type: "input",
      width: 70,
    },
    {
      label: "5g落粉",
      prop: "fallingPowder5g",
      type: "input",
      width: 80,
    },
    {
      label: "磨前落粉",
      prop: "fallingPowderBeforeGrinding",
      type: "input",
      width: 80,
    },
    {
      label: "磨后落粉",
      prop: "fallingPowderAfterGrinding",
      type: "input",
      width: 80,
    },
    {
      label: "渣子",
      prop: "waste",
      type: "input",
      width: 60,
    },
    {
      label: "70度吸气量",
      prop: "inspiratoryVolume70t",
      type: "input",
      width: 90,
    },
    {
      label: "气体解吸量(ml/g)",
      prop: "gasDesorption",
      type: "input",
      width: 120,
    },
    {
      label: "PH值",
      prop: "ph",
      type: "input",
      width: 90,
    },
    {
      label: "KOH",
      prop: "koh",
      type: "input",
      width: 90,
    },
    {
      label: "强度",
      prop: "strength",
      type: "input",
      width: 90,
    },
    {
      label: "欧标吸附",
      prop: "adsorptionEN",
      type: "input",
      width: 90,
    },

    {
      label: "检验员",
      prop: "inspectorName",
      type: "input",
      addDisplay: false,
      editDisplay: false,
    },
    {
      label: "附件",
      prop: "attach",
      type: "upload",
      minWidth: 100,
      drag: true,
      loadText: "图片上传中，请稍等",
      span: 24,
      dataType: "string",
      // props: {
      //   label: 'link',
      //   value: 'url'
      // },
      propsHttp: {
        res: "data",
        url: "link",
      },
      action: "/api/blade-resource/oss/endpoint/put-file-attach",
    },
    {
      label: "来源编码",
      prop: "sourceCode",
      type: "input",
      width: 120,
      addDisplay: false,
      editDisplay: false,
    },

  ],
};

import request from "@/router/axios";

export const getList = (current, size, params) => {
  return request({
    url: "/api/ni/tracing/production-batch/list",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/ni/tracing/production-batch/detail",
    method: "get",
    params: {
      id,
    },
  });
};
/**
 * 根据批次查询质检报告信息
 */
export const getReportDetail = (printIds, quantity) => {
  return request({
    url: "/api/ni/tracing/production-batch/reportDetail",
    method: "get",
    params: {
      printIds,
      quantity,
    },
  });
};

export const remove = (ids) => {
  return request({
    url: "/api/ni/tracing/production-batch/remove",
    method: "post",
    data: ids, // 直接传递数组
    headers: {
      "Content-Type": "application/json", // 指定JSON格式
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/ni/tracing/production-batch/submit",
    method: "post",
    data: row,
  });
};

export const addBatch = (row) => {
  return request({
    url: "/api/ni/tracing/production-batch/addBatch",
    method: "post",
    data: row,
  });
};

export const update = (row) => {
  return request({
    url: "/api/ni/tracing/production-batch/submit",
    method: "post",
    data: row,
  });
};

export const updateEncode = (productionBatch) => {
  return request({
    url: "/api/ni/tracing/production-batch/updateEncode",
    method: "post",
    data: productionBatch,
  });
};

export const summaries = (startDate, endDate) => {
  return request({
    url: "/api/ni/tracing/production-batch/summaries",
    method: "get",
    params: {
      startDate,
      endDate,
    },
  });
};
export const summaries1 = (startDate, endDate) => {
  return request({
    url: "/api/ni/tracing/production-batch/v1/summaries",
    method: "get",
    params: {
      startDate,
      endDate,
    },
  });
};

export const updateQrCodeStyle = (ids, qrCodeStyle) => {
  return request({
    url: "/api/ni/tracing/production-batch/updateQrCodeStyle",
    method: "post",
    params: {
      ids,
      qrCodeStyle,
    },
  });
};

export const linkGuoWaiBeiHuoJiHua = (ids, inventoryPlanId) => {
  return request({
    url: "/api/ni/tracing/production-batch/linkGuoWaiBeiHuoJiHua",
    method: "post",
    params: {
      ids,
      inventoryPlanId,
    },
  });
};
export const linkInspectionId=(ids,inspectionId)=>{
  return request({
    url: "/api/ni/tracing/production-batch/linkInspectionId",
    method: "post",
    params: {
      ids,
      inspectionId,
    },
  });
}
export const unLinkInspectionId=(ids)=>{
  return request({
    url: "/api/ni/tracing/production-batch/unLinkInspectionId",
    method: "post",
    params: {
      ids,
    },
  });
}

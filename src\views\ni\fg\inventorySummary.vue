<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      v-model="form"
      ref="crud"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      :cell-style="cellStyle"
      @cell-click="cellClick"
      @on-load="onLoad"
    >
      <template #remark="{ row }">
        <span>{{ row.remark ? row.remark : "未填写" }}</span>
      </template>
      <template #menuLeft>
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          v-if="permission.ni_fg_inventory_summary_out"
          @click="handleOutbound"
          >出 库
        </el-button>
        <el-button
          type="warning"
          icon="el-icon-shopping-cart-full"
          size="mini"
          v-if="permission.ni_fg_inventory_summary_restocking"
          @click="handleRestocking"
          >倒 箱
        </el-button>
        <el-button
          type="warning"
          icon="el-icon-truck"
          size="mini"
          plain
          v-if="permission.ni_fg_inventory_summary_transfer"
          @click="handleTransfer"
          >调 拨
        </el-button>
        <el-button
          type="danger"
          icon="el-icon-turn-off-microphone"
          size="mini"
          v-if="permission.ni_fg_inventory_summary_isolation"
          @click="handleIsolation"
          >标记冻结
        </el-button>
        <el-button
          type="warning"
          icon="el-icon-refresh-left"
          size="mini"
          v-if="permission.ni_fg_inventory_summary_back_production"
          @click="handleBackProduction"
          >退生产
        </el-button>
        <el-button
          type="danger"
          icon="el-icon-lollipop"
          size="mini"
          v-if="userInfo.role_name.includes('admin')"
          @click="handleCheck"
          >库存校验
        </el-button>
        <!--导出-->
        <el-button
          type="info"
          icon="el-icon-download"
          size="mini"
          v-if="permission.ni_fg_inventory_summary_export"
          @click="handleExport"
          >导 出
        </el-button>
        <el-divider direction="vertical"></el-divider>
        <el-radio-group v-model="area" size="mini" @input="onLoad(page)">
          <el-radio-button label="ALL">全部</el-radio-button>
          <el-radio-button label="CN">国内</el-radio-button>
          <el-radio-button label="OS">国外</el-radio-button>
        </el-radio-group>
      </template>
      <template #menu="{ row, index }">
        <el-button
          type="text"
          icon="el-icon-more"
          size="mini"
          @click="rowBatchNo(row, index)"
        >
        </el-button>
      </template>
    </avue-crud>
    <outbound-form-dialog ref="outboundFormDialogRef" @confirm="onLoad(page)" />
    <restocking-form-dialog
      ref="restockingFormDialogRef"
      @confirm="handleRestockingConfirm"
    />
    <transfer-form-dialog
      ref="transferFormDialogRef"
      @confirm="handleTransferConfirm"
    />
    <inventory-drawer ref="inventoryDrawerRef" />
    <inventory-select-dialog
      ref="inventorySelectDialogRef"
      multiple
      @confirm="handleInventorySelectConfirm"
    />
    <freeze-form-dialog ref="freezeFormDialogRef" @confirm="onLoad(page)" />
    <el-dialog
      title="库存校验"
      append-to-body
      :visible.sync="check.visible"
      width="455px"
    >
      <avue-form
        :option="check.option"
        v-model="check.form"
        @submit="handleCheckSubmit"
      >
      </avue-form>
    </el-dialog>
    <freeze-list-dialog ref="freezeListDialogRef" />
  </basic-container>
</template>

<script>
import { changeRemark, getList } from "@/api/ni/fg/fgInventorySummary";
import { mapGetters } from "vuex";
import OutboundFormDialog from "@/views/ni/fg/components/OutboundForm1Dialog.vue";
import RestockingFormDialog from "@/views/ni/fg/components/RestockingFormDialog.vue";
import InventoryDrawer from "@/views/ni/fg/components/InventoryDrawer.vue";
import InventorySelectDialog from "@/views/ni/fg/components/InventorySelectDialog.vue";
import TransferFormDialog from "@/views/ni/fg/components/TransferFormDialog.vue";
import FreezeFormDialog from "@/views/ni/fg/components/FreezeFormDialog.vue";
import FreezeListDialog from "@/views/ni/fg/components/FreezeListDialog.vue";

export default {
  components: {
    TransferFormDialog,
    InventoryDrawer,
    OutboundFormDialog,
    RestockingFormDialog,
    InventorySelectDialog,
    FreezeFormDialog,
    FreezeListDialog,
  },
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        menu: false,
        showSummary: true,
        sumColumnList: [
          {
            name: "num",
            type: "sum",
          },
          {
            name: "freezeNum",
            type: "sum",
          },
          {
            name: "inTransitQuantity",
            type: "sum",
          },
          {
            name: "availableKg",
            type: "sum",
          },
          {
            name: "weight",
            type: "sum",
          },
          {
            name: "freezeKg",
            type: "sum",
          },
          {
            name: "inTransitKg",
            type: "sum",
          },
        ],
        menuWidth: 80,
        addBtn: false,
        delBtn: false,
        editBtn: false,
        size: "mini",
        searchSize: "mini",
        align: "center",
        searchIndex: 3,
        searchIcon: true,
        searchEnter: true,
        height: "auto",
        calcHeight: 30,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        selection: true,
        dialogClickModal: false,
        reserveSelection: true,
        column: [
          {
            label: "仓库",
            prop: "depotId",
            type: "select",
            dicUrl: "/api/ni/base/depot/info/list?status=2&type=1",
            props: {
              label: "name",
              value: "id",
              desc: "code",
            },
            sortable: true,
            filters: true,
            search: true,
            minWidth: 100,
            overHidden: true,
            searchOrder: 99,
          },
          {
            label: "品名",
            prop: "skuText",
            placeholder: " ",
            overHidden: true,
            minWidth: 150,
            hide: true,
          },
          {
            label: "规格",
            prop: "spec",
            type: "select",
            dicUrl: "/api/ni/product/spec/list",
            props: {
              label: "name",
              value: "code",
              desc: "code",
            },
            hide: true,
            showColumn: false,
            filterable: true,
            search: true,
            searchOrder: 98,
          },
          {
            label: "规格",
            prop: "specText",
            placeholder: " ",
            display: false,
            overHidden: true,
            sortable: true,
            filters: true,
            minWidth: 150,
          },
          {
            label: "质量",
            prop: "qualityLevel",
            type: "select",
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_product_sku_quality_level",
            props: {
              label: "dictValue",
              value: "dictKey",
              desc: "dictKey",
            },
            placeholder: " ",
            sortable: true,
            filters: true,
            width: 100,
          },
          {
            label: "外包装",
            prop: "packageText",
            placeholder: " ",
            display: false,
            overHidden: true,
            minWidth: 115,
            search: true,
            sortable: true,
            filters: true,
            searchOrder: 97,
          },
          {
            label: "内包装",
            prop: "innerPackageText",
            placeholder: " ",
            display: false,
            overHidden: true,
            sortable: true,
            filters: true,
            minWidth: 115,
          },
          {
            label: "内包装",
            prop: "innerPackageId",
            type: "select",
            dicUrl:
              "/api/ni/product/packaging/list?innerPark=1&current=1&size=20&status=1&&name={{key}}",
            props: {
              label: "name",
              value: "id",
              desc: "type",
            },
            search: true,
            remote: true,
            dicFormatter: (data) => {
              return data.data.records;
            },
            hide: true,
            showColumn: false,
            filterable: true,
          },
          {
            label: "总箱数",
            prop: "num",
            minWidth: 80,
            sortable: true,
            type: "number",
          },
          {
            label: "冻结箱数",
            prop: "freezeNum",
            minWidth: 80,
            sortable: true,
            type: "number",
          },
          {
            label: "在途箱数",
            prop: "inTransitQuantity",
            minWidth: 80,
            type: "number",
            hide: true,
          },
          {
            label: "总重量",
            prop: "weight",
            placeholder: " ",
            type: "number",
            sortable: true,
            minWidth: 80,
          },
          {
            label: "冻结重量",
            prop: "freezeKg",
            placeholder: " ",
            type: "number",
            sortable: true,
            minWidth: 80,
          },
          {
            label: "在途重量",
            prop: "inTransitKg",
            placeholder: " ",
            type: "number",
            minWidth: 80,
            hide: true,
          },
          {
            label: "单位",
            prop: "unit",
            type: "select",
            filterable: true,
            minWidth: 80,
            hide: true,
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_material_unit",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            slot: true,
            placeholder: " ",
          },
          {
            label: "存货编码",
            prop: "materialCode",
            sortable: true,
            placeholder: " ",
            minWidth: 110,
            overHidden: true,
            filters: true,
            search: true,
          },
          {
            label: "国内/外",
            prop: "area",
            type: "radio",
            dicData: [
              {
                label: "国内",
                value: "CN",
              },
              {
                label: "国外",
                value: "OS",
              },
            ],
            width: 100,
            filters: true,
            sortable: true,
          },
          {
            label: "备注",
            prop: "remark",
            filters: true,
            placeholder: " ",
            minWidth: 110,
            overHidden: true,
            search: true,
          },
        ],
      },
      data: [],
      exportColumn: [
        {
          label: "仓库",
          prop: "depotName",
        },
        {
          label: "存货编码",
          prop: "materialCode",
        },
        {
          label: "规格",
          prop: "specText",
        },
        {
          label: "质量",
          prop: "qualityLevel",
        },
        {
          label: "外包装",
          prop: "packageText",
        },
        {
          label: "内包装",
          prop: "innerPackageText",
        },
        {
          label: "数量",
          prop: "num",
        },
        {
          label: "重量",
          prop: "weight",
        },
        {
          label: "单位",
          prop: "unit",
        },
        {
          label: "国内/外",
          prop: "area",
        },
        {
          label: "备注",
          prop: "remark",
        },
        {
          label: "状态",
          prop: "status",
        },
      ],
      depotKeyValue: {},
      statusDictKeyValue: {},
      unitKeyValue: {},
      action: null,
      area: "ALL",
      qualityLevelDictKeyValue: {},
      qualityLevelColorMap: {
        A: "#67C23A", // 高吸附 - 绿色
        P: "#409EFF", // 优等品 - 蓝色
        Q: "#E6A23C", // 合格品 - 橙色
      },
      check: {
        visible: false,
        option: {
          size: "mini",
          span: 24,
          emptyBtn: false,
          column: [
            {
              label: "仓库",
              prop: "depotId",
              type: "select",
              dicUrl: "/api/ni/base/depot/info/list?status=2&type=1",
              props: {
                label: "name",
                value: "id",
                desc: "code",
              },
              minWidth: 100,
              overHidden: true,
              rules: [
                {
                  required: true,
                  message: "请选择仓库",
                  trigger: "blur",
                },
              ],
            },
          ],
        },
        form: {},
      },
    };
  },
  computed: {
    ...mapGetters(["permission", "userInfo", "colorName"]),
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  created() {
    this.dictInit();
  },
  methods: {
    rowBatchNo(row) {
      this.$refs.inventoryDrawerRef.onShow(row);
    },
    dictInit() {
      this.$http
        .get("/api/ni/base/depot/info/list?status=2&type=1")
        .then((res) => {
          this.depotKeyValue = res.data.data.reduce((acc, cur) => {
            acc[cur.id] = cur.name;
            return acc;
          }, {});
        });
      this.$http
        .get(
          "/api/blade-system/dict-biz/dictionary?code=ni_product_sku_quality_level"
        )
        .then((res) => {
          const dict = res.data.data;
          this.qualityLevelDictKeyValue = dict.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });
      this.$http
        .get(
          "/api/blade-system/dict-biz/dictionary?code=ni_fg_inventory_status"
        )
        .then((res) => {
          this.statusDictKeyValue = res.data.data.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });
      this.$http
        .get("/api/blade-system/dict-biz/dictionary?code=ni_material_unit")
        .then((res) => {
          this.unitKeyValue = res.data.data.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });
    },
    handleExport() {
      let msg = "是否导出当前选取的数据？";
      if (this.selectionList.length === 0) {
        msg = "是否导出全部数据？";
      }
      this.$confirm(msg, {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        let data = this.selectionList;
        if (this.selectionList.length === 0) {
          const q = { ...this.query };
          q.area = this.area === "ALL" ? null : this.area;
          const res = await getList(1, 100000, q);
          data = res.data.data.records;
        }
        this.$Export.excel({
          title: "当前库存",
          columns: this.exportColumn,
          data: data.map((item) => {
            return {
              ...item,
              depotName: this.depotKeyValue[item.depotId],
              status: this.statusDictKeyValue[item.status],
              unit: this.unitKeyValue[item.unit],
              qualityLevel: this.qualityLevelDictKeyValue[item.qualityLevel],
              area: item.area === "CN" ? "国内" : "国外",
            };
          }),
        });
      });
    },
    handleCheck() {
      this.check.visible = true;
    },
    handleCheckSubmit(form, done) {
      this.$http
        .post("/api/ni/fg/inventory-summary/check/" + form.depotId)
        .then((res) => {
          if (res.data.msg)
            this.$alert(res.data.msg, "异常信息", {
              dangerouslyUseHTMLString: true,
              type: "warning",
              center: true,
            });
          else
            this.$message({
              type: "success",
              message: "校验完成，暂未发现问题!",
            });
        })
        .finally(() => {
          done();
        });
    },
    handleBackProduction() {
      if (this.selectionList && this.selectionList.length > 0) {
        const depotIds = new Set();
        this.selectionList.forEach((item) => {
          depotIds.add(item.deptId);
        });
        if (depotIds.size > 1) {
          this.$message({
            type: "warning",
            message: "请选择同一仓库的数据进行出库!",
          });
          return;
        }
      }
      this.$refs.outboundFormDialogRef.onAdd(
        this.selectionList[0].depotId,
        this.selectionList,
        { type: "0207" }
      );
    },
    handleIsolation() {
      const depotIds = new Set();
      this.selectionList.forEach((item) => {
        depotIds.add(item.deptId);
      });
      if (depotIds.size > 1) {
        this.$message({
          type: "warning",
          message: "请选择同一仓库的数据进行出库!",
        });
        return;
      }
      this.$refs.freezeFormDialogRef.onFreeze(
        this.selectionList[0].depotId,
        this.selectionList
      );
    },
    handleTransfer() {
      const depotIds = new Set();
      this.selectionList.forEach((item) => {
        depotIds.add(item.deptId);
      });
      if (depotIds.size > 1) {
        this.$message({
          type: "warning",
          message: "请选择同一仓库的数据进行出库!",
        });
        return;
      }
      this.$refs.transferFormDialogRef.onAdd(
        this.selectionList[0].depotId,
        this.selectionList
      );
    },
    handleRestocking() {
      const depotIds = new Set();
      this.selectionList.forEach((item) => {
        depotIds.add(item.deptId);
      });
      if (depotIds.size > 1) {
        this.$message({
          type: "warning",
          message: "请选择同一仓库的数据进行操作!",
        });
        return;
      }
      const depotId = this.selectionList[0].depotId;
      this.$refs.restockingFormDialogRef.onAdd(depotId, this.selectionList);
    },
    handleInventorySelectConfirm(selectionList) {
      const depotIds = new Set();
      selectionList.forEach((item) => {
        depotIds.add(item.deptId);
      });
      if (depotIds.size > 1) {
        this.$message({
          type: "warning",
          message: "请选择同一仓库的数据进行出库!",
        });
        return;
      }
      const depotId = selectionList[0].depotId;
      if (this.action === "restocking") {
        this.$refs.restockingFormDialogRef.onAdd(depotId, selectionList);
      } else if (this.action === "outbound") {
        const freeze = selectionList.some((item) => item.status !== 1);
        if (freeze) {
          this.$message({
            type: "warning",
            message: "请选择非冻结的数据进行出库!",
          });
          return;
        }
        this.$refs.outboundFormDialogRef.onAdd(depotId, selectionList);
      } else if (this.action === "transfer") {
        this.$refs.transferFormDialogRef.onAdd(depotId, selectionList);
      } else if (this.action === "backProduction") {
        this.$refs.outboundFormDialogRef.onAdd(depotId, selectionList, {
          type: "0207",
        });
      } else if (this.action === "freeze") {
        this.$refs.freezeFormDialogRef.onFreeze(depotId, selectionList);
      }
    },
    handleTransferConfirm() {
      this.onLoad(this.page);
      const path = "/ni/fg/transfer";
      if (path) this.$router.push(path);
    },
    handleRestockingConfirm() {
      this.onLoad(this.page);
      const path = "/ni/fg/restocking";
      if (path) this.$router.push(path);
    },
    handleOutbound() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      if (this.selectionList && this.selectionList.length > 0) {
        const depotIds = new Set();
        this.selectionList.forEach((item) => {
          depotIds.add(item.deptId);
        });
        if (depotIds.size > 1) {
          this.$message({
            type: "warning",
            message: "请选择同一仓库的数据进行出库!",
          });
          return;
        }
      }
      this.$refs.outboundFormDialogRef.onAdd(
        this.selectionList[0].depotId,
        this.selectionList
      );
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      const q = {
        ...params,
        ...this.query,
      };
      q.area = this.area === "ALL" ? null : this.area;
      getList(page.currentPage, page.pageSize, q).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
      });
    },
    cellStyle({ row, column }) {
      if ("area" === column.columnKey) {
        return {
          backgroundColor: row.area === "CN" ? "#F56C6C" : this.colorName,
          color: "#fff",
        };
      }
      if ("freezeNum" === column.columnKey && row.freezeNum > 0) {
        return {
          backgroundColor: "#409EFF",
          color: "#fff",
          textDecoration: "underline",
          cursor: "pointer",
        };
      }
      if ("qualityLevel" === column.columnKey) {
        const color = this.qualityLevelColorMap[row.qualityLevel];
        return {
          backgroundColor: color || "#909399", // 默认颜色为灰色
          color: "#fff",
        };
      }
      if ("remark" === column.columnKey) {
        return {
          textDecoration: "underline",
          cursor: "pointer",
        };
      }
    },
    cellClick(row, column) {
      if (column.property === "freezeNum" && row.freezeNum > 0) {
        this.rowFreezeShow(row);
      }
      if (column.property === "remark") {
        this.rowRemark(row, row.$index);
      }
    },
    rowRemark(row, index) {
      this.$prompt("填写备注", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
      }).then(({ value }) => {
        changeRemark(row.id, value).then(() => {
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.data[index].remark = value;
        });
      });
    },
    rowFreezeShow(row) {
      this.$refs.freezeListDialogRef.onShow(row.skuId);
    },
  },
};
</script>

<style></style>

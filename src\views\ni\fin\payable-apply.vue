<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :search.sync="query"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      :row-class-name="tableRowClassName"
      v-model="form"
      ref="crud"
      @sort-change="sortChange"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
      :cell-style="cellStyle"
    >
      <template #applyPayableDate="{ row, index }">
        <el-tooltip
          v-if="row.payState === '2' && row.payableDate"
          class="item"
          open-delay="500"
          :content="`申请付款日期：${row.applyPayableDate}`"
          placement="top"
        >
          <span :style="{ color: colorName, fontWeight: 'bold' }">{{
            row.payableDate
          }}</span>
        </el-tooltip>
        <span v-else>{{ row.applyPayableDate }}</span>
      </template>
      <template #adjustIdForm="{ size, index }">
        <fin-adjust-select
          v-model="form.adjustId"
          :size="size"
          :params="{
            status: 9,
            supplierId: form.supplierId,
            brand: form.brand,
          }"
          :before-select="beforeAdjustSelect"
          @change="handleAdjustChange"
        />
      </template>
      <template #contractSerialNo="{ row, index }">
        <el-button
          v-if="row.contractSerialNo"
          size="mini"
          type="text"
          style="user-select: unset"
          @click="rowContractPay(row)"
        >
          {{ row.contractSerialNo }}
        </el-button>
      </template>
      <template #costApplyIdForm="{ size, index }">
        <cost-apply-select
          v-model="form.costApplyId"
          :size="size"
          :change="rowCostApplyChange"
        />
      </template>
      <template #financeAccountForm="{ size, disabled }">
        <bank-card-input
          v-model="form.financeAccount"
          :size="size"
          :disabled="disabled"
        />
      </template>
      <template #alipayIdForm="{ size, disabled }">
        <alipay-bill-select
          multiple
          v-model="form.alipayId"
          :disabled="disabled"
          :params="{
            payApplyStates: '0,1',
            brand: form.brand,
            supplierId: form.supplierId,
          }"
          :before-select="beforeContractSelect"
          @confirm="handleAlipayBillSelectConfirm"
        />
      </template>
      <template #budgetIdForm="{ size, disabled }">
        <un-finish-budget-select
          v-model="form.budgetId"
          :size="size"
          :params="{ brand: form.brand }"
          @clear="handleBudgetClear"
        />
      </template>
      <template #paymentType="{ row, size, index }">
        <el-tag
          v-if="row.paymentType === '1'"
          :size="size"
          type=""
          effect="plain"
        >
          {{ row.$paymentType }}
        </el-tag>
        <el-tag
          v-else-if="row.paymentType === '2'"
          :size="size"
          type="success"
          effect="plain"
        >
          {{ row.$paymentType }}
        </el-tag>
        <el-tag
          v-else-if="row.paymentType === '3'"
          :size="size"
          type="info"
          effect="plain"
        >
          {{ row.$paymentType }}
        </el-tag>
        <el-tag
          v-else-if="row.paymentType === '4'"
          :size="size"
          type="danger"
          effect="plain"
        >
          {{ row.$paymentType }}
        </el-tag>
        <el-tag
          v-else-if="row.paymentType === '5'"
          :size="size"
          type="warning"
          effect="plain"
        >
          {{ row.$paymentType }}
        </el-tag>
        <el-tag
          v-else-if="row.paymentType === '6'"
          :size="size"
          type=""
          effect="dark"
        >
          {{ row.$paymentType }}
        </el-tag>
        <el-tag
          v-else-if="row.paymentType === '7'"
          :size="size"
          type="danger"
          effect="plain"
        >
          {{ row.$paymentType }}
        </el-tag>
        <el-tag
          v-else-if="row.paymentType === '8'"
          :size="size"
          type="warning"
          effect="plain"
        >
          {{ row.$paymentType }}
        </el-tag>
        <el-tag v-else :size="size">
          {{ row.$paymentType }}
        </el-tag>
      </template>
      <template #billType="{ row, size, index }">
        <el-tag
          v-if="row.billType === '1'"
          :size="size"
          type="danger"
          effect="dark"
        >
          {{ row.$billType }}
        </el-tag>
        <el-tag v-else :size="size" type="success" effect="plain">
          {{ row.$billType }}
        </el-tag>
      </template>
      <template #toVoid="{ row, size, index }">
        <el-tag v-if="row.toVoid" :size="size" type="danger" effect="dark"
          >{{ row.$toVoid }}
        </el-tag>
        <el-tag v-else :size="size" type="info">否</el-tag>
      </template>
      <template #title="{ row, index, size }">
        <el-tag type="danger" size="mini" effect="dark" v-if="row.toVoid">
          已作废
        </el-tag>
        <span>{{ row.title }} </span>
      </template>
      <template #serialNo="{ row, index }">
        <flow-timeline-popover
          v-if="row.processInsId"
          :process-ins-id="row.processInsId"
          :form-key="formKey"
          :process-def-key="processDefKey"
          v-model="row.serialNo"
          :flow.sync="row.flow"
          trigger="click"
          lazy
        />
        <span v-else>{{ row.serialNo }}</span>
      </template>
      <template #status="{ row, size, index, dic }">
        <el-tag v-if="row.status === 0" size="mini" type="info">
          {{ row.$status }}
        </el-tag>
        <el-tag v-else-if="row.status === 1" size="mini" effect="plain">
          {{ row.$status }}
        </el-tag>
        <el-tag v-else-if="row.status === 2" size="mini" effect="dark">
          {{ row.$status }}
        </el-tag>
        <el-tag v-else-if="row.status === 3" size="mini" type="danger">
          {{ row.$status }}
        </el-tag>
        <el-tag
          v-else-if="row.status === 4"
          size="mini"
          type="warning"
          effect="plain"
        >
          {{ row.$status }}
        </el-tag>
        <el-tag
          v-else-if="row.status === 5"
          size="mini"
          type="warning"
          effect="plain"
        >
          {{ row.$status }}
        </el-tag>
        <el-tag
          v-else-if="row.status === 6"
          size="mini"
          type="danger"
          effect="plain"
        >
          {{ row.$status }}
        </el-tag>
        <el-tag
          v-else-if="row.status === 9"
          size="mini"
          type="success"
          effect="plain"
        >
          {{ row.$status }}
        </el-tag>
        <el-tag v-else size="mini" type="warning" effect="dark">
          {{ row.$status }}
        </el-tag>
      </template>
      <template #type="{ row, size, index }">
        <el-tag
          v-if="row.type === '1'"
          :size="size"
          type="warning"
          effect="dark"
          >{{ row.$type }}
        </el-tag>
        <el-tag v-else-if="row.type === '2'" :size="size" effect="dark"
          >{{ row.$type }}
        </el-tag>
        <el-tag
          v-else-if="row.type === '3'"
          type="danger"
          :size="size"
          effect="dark"
          >{{ row.$type }}
        </el-tag>
        <el-tag v-else-if="row.type === '4'" :size="size" effect="plain"
          >{{ row.$type }}
        </el-tag>
        <el-tag
          v-else-if="row.type === '5'"
          type="danger"
          :size="size"
          effect="plain"
          >{{ row.$type }}
        </el-tag>
        <el-tag
          v-else-if="row.type === '9'"
          type="warning"
          :size="size"
          effect="plain"
          >{{ row.$type }}
        </el-tag>
      </template>
      <template #contractIdForm="{ disabled, size, index }">
        <contract-select
          v-model="form.contractId"
          :pay-type="payType"
          :disabled="disabled"
          :size="size"
          :params="{
            b: form.supplierId,
            status: 9,
          }"
          :before-select="beforeContractSelect"
          @confirm="handleContractIdConfirm"
        />
      </template>
      <template #brand="{ row, disabled, size, index }">
        <el-tag
          v-if="row.brand"
          :size="size"
          type="success"
          :effect="row.brand == '1' ? 'dark ' : 'plain'"
        >
          {{ row.$brand }}
        </el-tag>
      </template>
      <template #currency="{ row, disabled, size, index }">
        <el-tag v-if="row.currency" :size="size" effect="plain">
          {{ row.$currency }}
        </el-tag>
      </template>
      <template #amount="{ row, disabled, size, index }">
        <span
          v-if="row.amount < 0 && !row.toVoid"
          style="color: #167c46; font-weight: bolder"
          >{{ row.amount }}</span
        >
        <span
          v-else-if="row.amount >= 0 && !row.toVoid"
          style="color: #f56c6c; font-weight: bolder"
          >{{ row.amount }}</span
        >
        <span
          v-else-if="row.toVoid"
          style="
            color: #909399;
            font-weight: bolder;
            text-decoration: line-through;
          "
          >{{ row.amount }}</span
        >
      </template>
      <template #amountForm="{ size }">
        <el-input-number
          v-if="form.type !== '3'"
          :size="size"
          style="width: 100%"
          v-model="form.amount"
          precision="2"
          controls-position="right"
          :max="form.unPayAmount"
          @change="handleAmount"
        ></el-input-number>
        <el-input-number
          v-else
          :size="size"
          style="width: 100%"
          v-model="form.amount"
          precision="2"
          disabled
          controls-position="right"
          @change="handleAmount"
        ></el-input-number>
      </template>
      <template #payState="{ row, index }">
        <el-tag
          v-if="row.payState == null || row.payState === '0'"
          size="mini"
          type="danger"
          effect="dark"
        >
          未付款
        </el-tag>
        <el-tag
          v-else-if="row.payState === '1'"
          type="warning"
          size="mini"
          effect="dark"
        >
          {{ row.$payState }}
        </el-tag>
        <el-tag v-else size="mini" effect="dark">
          {{ row.$payState }}
        </el-tag>
      </template>
      <template #supplierIdForm="{ disabled, size, index }">
        <supplier-select
          v-model="form.supplierId"
          :size="size"
          :disabled="disabled"
          @submit="handleSupplierSubmit"
        />
      </template>
      <template #paySoaIdForm="{ disabled, size, index }">
        <pay-soa-select
          v-model="form.paySoaId"
          :size="size"
          :disabled="disabled"
          :before-select="beforeContractSelect"
          :params="{
            brand: form.brand,
            supplierId: form.supplierId,
            confirm: true,
            payApplyStates: '0,1',
          }"
          @submit="handlePaySoaSubmit"
        />
      </template>
      <template #porOrderIdForm="{ disabled, size, index }">
        <por-order-select
          v-model="form.porOrderId"
          :label.sync="form.porOrderSerialNo"
          :size="size"
          :disabled="disabled"
          :params="{
            brand: form.brand,
            supplierId: form.supplierId,
            payType: '2',
            payApplyStates: '0,1',
            payState: 0,
          }"
          :before-select="beforeContractSelect"
          @submit="handleOrderSubmit"
          @clear="handleOrderClear"
        />
      </template>
      <template #alipaysLabel>
        <span style="font-size: 16px; font-weight: 500">支付宝明细</span>
      </template>
      <template #alipaysForm="{ row, index, size }">
        <payable-apply-order-alipay v-model="form.alipays" />
      </template>
      <template #itemsLabel>
        <span style="font-size: 16px; font-weight: 500">付款明细</span>
        <el-divider
          v-if="['1', '3'].includes(form.type)"
          direction="vertical"
        />
        <el-button-group v-if="['1', '3'].includes(form.type)">
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-plus"
            @click="itemAdd"
            >添加
          </el-button>
          <el-button
            type="danger"
            size="mini"
            icon="el-icon-delete"
            :disabled="item.selectionList.length <= 0"
            @click="itemDelete"
            >删 除
          </el-button>
        </el-button-group>
      </template>
      <template #itemsForm="{ row, index, size }">
        <alipay-bill-list v-if="form.type === '4'" v-model="form.items" />
        <payable-apply-order-item
          v-else
          v-model="form.items"
          :selectionList.sync="item.selectionList"
          :cost="form.type === '3'"
          @sumAmount="handleSumAmount"
        />
      </template>
      <template #menuLeft>
        <!--        <el-dropdown-->
        <!--          v-if="permission.payableApply_add && 1 != 1"-->
        <!--          @command="handleAdd"-->
        <!--        >-->
        <!--          <el-button type="primary" size="mini" icon="el-icon-plus"-->
        <!--            >新 增<i class="el-icon-arrow-down el-icon&#45;&#45;right"></i>-->
        <!--          </el-button>-->
        <!--          <el-dropdown-menu slot="dropdown">-->
        <!--            <el-dropdown-item-->
        <!--              v-for="item in brandDict"-->
        <!--              :key="item.dictKey"-->
        <!--              :command="item.dictKey"-->
        <!--              >{{ item.dictValue }}-->
        <!--            </el-dropdown-item>-->
        <!--          </el-dropdown-menu>-->
        <!--        </el-dropdown>-->
        <el-button
          type="warning"
          size="mini"
          icon="el-icon-position"
          plain
          v-if="permission.payableApply_apply"
          @click="handlePayApply"
          >付款申请
        </el-button>
        <el-button
          type="danger"
          size="mini"
          icon="el-icon-delete"
          plain
          v-if="permission.payableApply_delete"
          @click="handleDelete"
          >删 除
        </el-button>
        <el-button
          type="warning"
          size="mini"
          icon="el-icon-delete"
          plain
          v-if="permission.payableApply_toVoid"
          @click="handleToVoid"
          >作 废
        </el-button>
        <el-button
          type="success"
          size="mini"
          icon="el-icon-download"
          plain
          v-if="permission.payableApply_export"
          @click="handleExport"
          >导出
        </el-button>
        <el-dropdown
          trigger="click"
          @command="handleChangeDocStatus"
          v-if="permission.payableApply_doc_status"
        >
          <el-button type="warning" size="mini" icon="el-icon-document-checked"
            >单据状态
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
              v-for="(item, index) in docStatusDict"
              :key="index"
              :command="item.dictKey"
              >{{ item.dictValue }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-dropdown @command="handlePrintChange">
          <el-button icon="el-icon-printer" type="info" size="mini">
            打 印<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="payApply">付款申请单</el-dropdown-item>
            <el-dropdown-item command="payApplyItem"
              >付款申请明细单
            </el-dropdown-item>
            <el-dropdown-item command="payApplyItemPlus"
              >付款明细单(申请人)
            </el-dropdown-item>
            <el-dropdown-item command="items">费用明细</el-dropdown-item>
            <el-dropdown-item command="expense">费用报销单</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-divider direction="vertical" />
        <el-radio-group v-model="brand" size="mini" @input="onLoad(page)">
          <el-radio-button label="natergy">能特异</el-radio-button>
          <el-radio-button label="yy">演绎</el-radio-button>
        </el-radio-group>
        <el-divider direction="vertical" />
        <el-radio-group
          v-model="dataType"
          size="mini"
          @input="handleDataTypeChange"
        >
          <el-radio-button label="all">全部</el-radio-button>
          <el-radio-button label="unPay">未付款</el-radio-button>
          <el-radio-button label="pay">已付款</el-radio-button>
        </el-radio-group>
        <el-divider direction="vertical" />
        <el-checkbox v-model="todo">待办</el-checkbox>
        <el-checkbox v-model="un2Void">排除终止</el-checkbox>
      </template>
      <template #menu="{ row, index, size }">
        <el-button
          type="text"
          icon="el-icon-download"
          :size="size"
          @click="rowAttach(row)"
          >附件
        </el-button>
        <el-button
          type="text"
          icon="el-icon-s-grid"
          :size="size"
          v-if="1 !== 1"
          @click="rowDetails(row)"
          >明细
        </el-button>
        <el-button
          type="text"
          icon="el-icon-s-grid"
          :size="size"
          v-if="1 !== 1 && row.type === '2' && row.soaType === '2'"
          @click="rowAlipayBills(row)"
          >支付宝对账明细
        </el-button>
        <el-button
          type="text"
          icon="el-icon-s-ticket"
          :size="size"
          @click="rowBillSerialNo(row)"
          >发票登记
        </el-button>
        <el-button
          type="text"
          icon="el-icon-s-check"
          :size="size"
          v-if="dataType === 'todo'"
          @click="rowPass(row)"
          >快捷通过
        </el-button>
        <el-button
          type="text"
          :size="size"
          icon="el-icon-bank-card"
          v-if="
            permission.payableApply_pay &&
            [1, 2, 9].includes(row.status) &&
            !row.toVoid &&
            (row.payState == null || ['0', '1'].includes(row.payState))
          "
          @click="rowPayable(row)"
          >付款
        </el-button>
      </template>
    </avue-crud>
    <budget-item-dialog
      ref="porBudgetItemRef"
      multiple
      :params="{ used: false }"
      @confirm="handleItemSelect"
    />
    <payable-form-dialog ref="payableFormDialogRef" @submit="onLoad(page)" />
    <el-dialog
      title="财务信息选择"
      append-to-body
      :visible.sync="financeVisible"
      width="800px"
    >
      <avue-crud
        ref="financeCrud"
        :option="financeOption"
        v-model="financeForm"
        :data="financeData"
        @row-click="rowClick"
      >
        <template #radio="{ row, index }">
          <el-radio v-model="financeForm.radio" :label="index"
            ><i></i
          ></el-radio>
        </template>
      </avue-crud>
      <span slot="footer" class="dialog-footer">
        <el-button @click="() => (financeVisible = false)" size="mini"
          >取 消</el-button
        >
        <el-button type="primary" @click="handleFinanceConfirm" size="mini"
          >确 定</el-button
        >
      </span>
    </el-dialog>
    <payable-alipay-drawer ref="payableAlipayRef" />
    <el-drawer
      :visible.sync="detailVisible"
      :title="form.title"
      custom-class="wf-drawer"
      size="100%"
      append-to-body
    >
      <payable-apply-detail
        v-if="detailVisible"
        :taskId="form.taskId"
        :businessKey="form.id"
        :processInstanceId="form.processInsId"
      />
    </el-drawer>
    <attach-dialog ref="attachDialogRef" :detail="attachDetail" />
    <cost-apply-item-dialog
      ref="costApplyItemRef"
      :apply-id="form.costApplyId"
      multiple
      @onConfirm="handleCostApplySelectConfirm"
    />
    <alipay-bill-table-drawer ref="alipayBillTableDrawerRef" />
    <contract-pay-build-drawer
      detail
      :show-draft="false"
      ref="contractPayBuildDialogRef"
      @submit="onLoad(page)"
    />
  </basic-container>
</template>

<script>
import {
  add,
  back,
  getDetail,
  getPage,
  getPrintData,
  remove,
  submit,
  toVoid,
  update,
  changeBillSerialNo,
  changeDocStatus,
} from "@/api/ni/fin/payable-apply";
import {
  getList as getOrderItemList,
  getList as getItemList,
} from "@/api/ni/por/order-item";
import { getDetail as getContractDetail } from "@/api/ni/base/contract";
import { getAlipayList } from "@/api/ni/por/order-alipay";
import { getDetail as getPaySoaDetail } from "@/api/ni/fin/pay-soa";
import { mapGetters } from "vuex";
import SupplierSelect from "@/views/ni/base/components/SupplierSelect1";
import PorOrderSelect from "@/views/ni/por/components/OrderSelect";
import { dateNow1 } from "@/util/date";
import UserSelect from "@/components/user-select";
import ContractSelect from "@/views/ni/base/components/ContractSelect";
import {
  getDetail as getSupplierDetail,
  getList,
} from "@/api/ni/base/supplier/supplierinfo";
import PayableApplyOrderItem from "@/views/ni/fin/components/PayableApplyItem";
import PorOrderCostItemSelect from "@/views/ni/por/components/OrderCostItemSelect";
import { numToCapital } from "@/util/util";
import exForm from "@/views/plugin/workflow/mixins/ex-form";
import PayableApplyDetail from "@/views/ni/fin/payable-apply-detail";
import PayableAlipayDrawer from "@/views/ni/fin/components/PayableAlipayDrawer";
import FinPayableApplyForm from "@/views/ni/fin/components/FinPayableApplyForm";
import MaterialSelectDialog from "@/views/ni/base/components/MaterialSelectDialog";
import FinAccountingSelect from "@/views/ni/fin/components/AccountingSelect";
import unFinishBudgetSelect from "@/views/ni/por/components/UnFinishBudgetSelect";
import BudgetItemDialog from "@/views/ni/por/components/BudgetCostItemDialog1";
import AlipayBillSelect from "@/views/ni/por/components/AlipayBillSelect";
import AlipayBillList from "@/views/ni/por/components/AlipayBillList";
import PayableApplyOrderAlipay from "@/views/ni/fin/components/PayableApplyOrderAlipay";
import BankCardInput from "@/components/bank-card-input";
import PaySoaSelect from "@/views/ni/fin/components/PaySoaSelect";
import { hiprint } from "vue-plugin-hiprint";
import { loadPrintTemplate } from "@/api/system/printTemplate";
import AttachDialog from "@/components/attach-dialog";
import { completeTask } from "@/api/plugin/workflow/ops";
import CostApplySelect from "@/views/ni/fin/components/CostApplySelect";
import { getDetail as getCostApplyDetail } from "@/api/ni/fin/cost-apply";
import CostApplyItemDialog from "@/views/ni/fin/components/CostApplyItemDialog";
import AlipayBillTableDrawer from "@/views/ni/fin/components/AlipayBillTableDrawer";
import PayableFormDialog from "@/views/ni/fin/components/PayableFormDialog";
import ContractPayBuildDrawer from "@/views/ni/base/components/ContractPayBuildDrawer";
import FlowTimelinePopover from "@/components/wf-flow-timeline/popover";

export default {
  mixins: [exForm],
  components: {
    AlipayBillTableDrawer,
    CostApplyItemDialog,
    PaySoaSelect,
    BankCardInput,
    AlipayBillList,
    AlipayBillSelect,
    FinAccountingSelect,
    MaterialSelectDialog,
    FinPayableApplyForm,
    PayableApplyDetail,
    PorOrderCostItemSelect,
    PayableApplyOrderItem,
    SupplierSelect,
    PorOrderSelect,
    UserSelect,
    ContractSelect,
    PayableAlipayDrawer,
    unFinishBudgetSelect,
    BudgetItemDialog,
    PayableApplyOrderAlipay,
    AttachDialog,
    CostApplySelect,
    PayableFormDialog,
    ContractPayBuildDrawer,
    FlowTimelinePopover,
  },
  data() {
    const PAY_TYPE_PAY = "2";
    return {
      payType: PAY_TYPE_PAY,
      detailVisible: false,
      module: "ni_fin_payable_apply",
      processDefKey: "process_fin_payable_apply",
      formKey: "wf_ex_fin/PayableApply",
      payableFormShow: false,
      payableForm: {},
      form: {
        items: [],
      },
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        showSummary: true,
        sumColumnList: [
          {
            name: "amount",
            type: "sum",
            decimals: 2,
          },
        ],
        editBtn: false,
        delBtn: false,
        searchLabelWidth: 130,
        searchEnter: true,
        addTitle: "新增",
        addBtn: false,
        dialogFullscreen: true,
        menu: true,
        menuWidth: 200,
        labelWidth: 150,
        span: 6,
        searchIndex: 3,
        searchIcon: true,
        size: "mini",
        searchSize: "mini",
        align: "center",
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        selection: true,
        dialogClickModal: false,
        column: [
          {
            label: "审批状态",
            prop: "status",
            minWidth: 69,
            dicData: [],
            search: true,
            fixed: "right",
            placeholder: " ",
            span: 6,
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            dataType: "number",
            type: "select",
            rules: [
              {
                required: true,
                message: "请选择审批状态",
                trigger: "blur",
              },
            ],
          },
          {
            label: "是否作废",
            prop: "toVoid",
            search: true,
            hide: true,
            display: false,
            type: "select",
            span: 6,
            dicData: [
              {
                label: "是",
                value: true,
              },
              {
                label: "否",
                value: false,
              },
            ],
          },
          {
            label: "主题",
            prop: "title",
            overHidden: true,
            placeholder: " ",
            minWidth: 120,
            span: 6,
            hide:true,
            rules: [
              {
                required: true,
                message: "请输入主题",
                trigger: "blur",
              },
            ],
          },
          {
            label: "编号",
            prop: "serialNo",
            placeholder: "系统自动生成",
            searchPlaceholder: " ",
            search: true,
            searchOrder: 9,
            disabled: true,
            overHidden: true,
            minWidth: 112,
            span: 6,
          },
          {
            label: "付款用途",
            prop: "useTo",
            type: "textarea",
            placeholder: " ",
            minRows: 1,
            overHidden: true,
            span: 6,
            row: true,
          },
          {
            label: "供应商",
            prop: "supplier",
            minWidth: 140,
            placeholder: " ",
            display: false,
            overHidden: true,
            hide: true,
            searchOrder: 8,
          },
          {
            label: "供应商",
            prop: "supplierId",
            type: "select",
            remote: true,
            overHidden: true,
            dicUrl: "/api/ni/base/supplier/info/page?status=2&keyword={{key}}",
            props: {
              label: "name",
              value: "id",
              desc: "code",
            },
            dicFormatter: (data) => {
              return data.data.records;
            },
            search: true,
            minWidth: 140,
            span: 6,
            placeholder: " ",
            searchOrder: 8,
            hide: true,
            showColumn: false,
            rules: [
              {
                required: true,
                message: "请选择供应商",
                trigger: "blur",
              },
            ],
          },
          {
            label: "开户银行",
            prop: "financeBank",
            placeholder: " ",
            span: 6,
            hide: true,
            rules: [
              {
                required: true,
                message: "请输入开户银行",
                trigger: "blur",
              },
            ],
          },
          {
            label: "收款人全称",
            prop: "financeName",
            placeholder: " ",
            overHidden: true,
            minWidth: 140,
            search: true,
            span: 6,
            rules: [
              {
                required: true,
                message: "请输入收款人全称",
                trigger: "blur",
              },
            ],
          },
          {
            label: "银行账号",
            prop: "financeAccount",
            placeholder: " ",
            span: 6,
            row: true,
            hide: true,
            rules: [
              {
                required: true,
                message: "请输入银行账号",
                trigger: "blur",
              },
            ],
          },
          {
            label: "申请类型",
            prop: "type",
            type: "select",
            dicData: [],
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            search: true,
            row: false,
            minWidth: 100,
            span: 6,
            rules: [
              {
                required: true,
                message: "请选择申请类型",
                trigger: "blur",
              },
            ],
            control: (val) => {
              this.form.porOrderId = null;
              this.form.porOrderSerialNo = null;
              this.form.porOrderAmount = null;
              this.form.contractId = null;
              this.form.contractAmount = null;
              this.form.alipayId = null;
              this.form.alipayAmount = null;
              this.form.unPayAmount = null;
              this.form.costApplyId = null;
              this.form.budgetId = null;
              this.form.paySoaAmount = null;
              this.form.paySoaId = null;
              this.form.items = [];
              this.form.alipays = [];
              if (val === "1") {
                //合同
                return {
                  type: {
                    row: false,
                  },
                  paySoaId: {
                    display: false,
                  },
                  paySoaAmount: {
                    display: false,
                  },
                  porOrderId: {
                    display: false,
                  },
                  porOrderAmount: {
                    display: false,
                  },
                  contractId: {
                    display: true,
                  },
                  contractAmount: {
                    display: true,
                  },
                  alipayId: {
                    display: false,
                  },
                  alipayAmount: {
                    display: false,
                  },
                  unPayAmount: {
                    display: true,
                  },
                  costApplyId: {
                    display: false,
                  },
                  budgetId: {
                    display: true,
                    rules: [
                      {
                        required: false,
                      },
                    ],
                  },
                  alipays: {
                    display: false,
                  },
                  items: {
                    display: true,
                  },
                };
              } else if (val === "2") {
                //采购
                return {
                  type: {
                    row: false,
                  },
                  paySoaId: {
                    display: true,
                  },
                  paySoaAmount: {
                    display: true,
                  },
                  porOrderId: {
                    display: false,
                  },
                  porOrderAmount: {
                    display: false,
                  },
                  contractId: {
                    display: false,
                  },
                  contractAmount: {
                    display: false,
                  },
                  alipayId: {
                    display: false,
                  },
                  alipayAmount: {
                    display: false,
                  },
                  unPayAmount: {
                    display: true,
                  },
                  costApplyId: {
                    display: false,
                  },
                  budgetId: {
                    display: false,
                  },
                  alipays: {
                    display: false,
                  },
                  items: {
                    display: true,
                  },
                };
              } else if (val === "3") {
                //费用
                return {
                  type: {
                    row: false,
                  },
                  paySoaId: {
                    display: false,
                  },
                  paySoaAmount: {
                    display: false,
                  },
                  porOrderId: {
                    display: false,
                  },
                  porOrderAmount: {
                    display: false,
                  },
                  contractId: {
                    display: false,
                  },
                  contractAmount: {
                    display: false,
                  },
                  alipayId: {
                    display: false,
                  },
                  alipayAmount: {
                    display: false,
                  },
                  unPayAmount: {
                    display: false,
                  },
                  costApplyId: {
                    display: true,
                  },
                  budgetId: {
                    display: true,
                    rules: [
                      {
                        required: true,
                        message: "请选择关联预算",
                        trigger: "blur",
                      },
                    ],
                  },
                  alipays: {
                    display: false,
                  },
                  items: {
                    display: true,
                  },
                };
              } else if (val === "4") {
                return {
                  type: {
                    row: false,
                  },
                  paySoaId: {
                    display: false,
                  },
                  paySoaAmount: {
                    display: false,
                  },
                  porOrderId: {
                    display: false,
                  },
                  porOrderAmount: {
                    display: false,
                  },
                  contractId: {
                    display: false,
                  },
                  contractAmount: {
                    display: false,
                  },
                  alipayId: {
                    display: true,
                  },
                  alipayAmount: {
                    display: true,
                  },
                  unPayAmount: {
                    display: true,
                  },
                  costApplyId: {
                    display: false,
                  },
                  budgetId: {
                    display: false,
                  },
                  alipays: {
                    display: true,
                  },
                  items: {
                    display: false,
                  },
                };
              } else if (val === "5") {
                //预付款
                return {
                  type: {
                    row: false,
                  },
                  paySoaId: {
                    display: false,
                  },
                  paySoaAmount: {
                    display: false,
                  },
                  porOrderId: {
                    display: true,
                  },
                  porOrderAmount: {
                    display: true,
                  },
                  contractId: {
                    display: false,
                  },
                  contractAmount: {
                    display: false,
                  },
                  alipayId: {
                    display: false,
                  },
                  alipayAmount: {
                    display: false,
                  },
                  unPayAmount: {
                    display: true,
                  },
                  costApplyId: {
                    display: false,
                  },
                  budgetId: {
                    display: false,
                  },
                  alipays: {
                    display: false,
                  },
                  items: {
                    display: true,
                  },
                };
              } else {
                //其他
                return {
                  type: {
                    row: true,
                  },
                  paySoaId: {
                    display: false,
                  },
                  paySoaAmount: {
                    display: false,
                  },
                  porOrderId: {
                    display: false,
                  },
                  porOrderAmount: {
                    display: false,
                  },
                  contractId: {
                    display: false,
                  },
                  contractAmount: {
                    display: false,
                  },
                  alipayId: {
                    display: false,
                  },
                  alipayAmount: {
                    display: false,
                  },
                  unPayAmount: {
                    display: false,
                  },
                  costApplyId: {
                    display: false,
                  },
                  budgetId: {
                    display: false,
                  },
                  alipays: {
                    display: false,
                  },
                  items: {
                    display: false,
                  },
                };
              }
            },
          },
          {
            label: "采购对账单",
            prop: "paySoaId",
            span: 6,
            display: false,
            hide: true,
            rules: [
              {
                required: true,
                message: "请选择采购对账单",
                trigger: "blur",
              },
            ],
          },
          {
            label: "采购订单",
            prop: "porOrderId",
            span: 6,
            display: false,
            hide: true,
            rules: [
              {
                required: true,
                message: "请选择采购订单",
                trigger: "blur",
              },
            ],
          },
          {
            label: "关联合同",
            prop: "contractId",
            type: "select",
            remote: true,
            dicUrl: "/api/ni/base/contract/page?status=9&keyword={{key}}",
            props: {
              label: "serialNo",
              value: "id",
              desc: "name",
            },
            dicFormatter: (data) => {
              return data.data.records;
            },
            span: 6,
            display: false,
            hide: true,
            showColumn: false,
            search: true,
            rules: [
              {
                required: true,
                message: "请选择关联合同",
                trigger: "blur",
              },
            ],
          },
          {
            label: "关联合同",
            prop: "contractSerialNo",
            overHidden: true,
            span: 6,
            display: false,
            minWidth: 110,
          },
          {
            label: "关联订单",
            prop: "alipayId",
            placeholder: " ",
            span: 6,
            display: false,
            hide: true,
            rules: [
              {
                required: true,
                message: "请选择支付宝关联订单",
                trigger: "blur",
              },
            ],
          },
          {
            label: "费用申请",
            prop: "costApplyId",
            span: 6,
            hide: true,
            showColumn: false,
            rules: [
              {
                required: true,
                message: "请选择费用申请",
                trigger: "blur",
              },
            ],
          },
          {
            label: "关联预算",
            prop: "budgetId",
            placeholder: " ",
            span: 6,
            row: true,
            display: false,
            hide: true,
            rules: [
              {
                required: true,
                message: "请选择关联预算",
                trigger: "blur",
              },
            ],
          },
          {
            label: "关联项目",
            prop: "projectSerialNo",
            searchLabelTip: "只筛选费用付款的条目",
            placeholder: " ",
            search: true,
            display: false,
            hide: true,
            showColumn: false,
          },
          {
            label: "关联预算",
            prop: "budgetSerialNo",
            searchLabelTip: "只筛选费用付款的条目",
            placeholder: " ",
            search: true,
            display: false,
            hide: true,
            showColumn: false,
          },
          {
            label: "对账金额",
            prop: "paySoaAmount",
            type: "number",
            placeholder: " ",
            disabled: true,
            display: false,
            hide: true,
            showColumn: false,
            precision: 2,
            span: 6,
          },
          {
            label: "订单金额",
            prop: "porOrderAmount",
            type: "number",
            placeholder: " ",
            disabled: true,
            display: false,
            hide: true,
            showColumn: false,
            precision: 2,
            span: 6,
          },
          {
            label: "合同金额",
            prop: "contractAmount",
            type: "number",
            precision: 2,
            placeholder: " ",
            disabled: true,
            display: false,
            hide: true,
            showColumn: false,
            span: 6,
          },
          {
            label: "订单金额",
            prop: "alipayAmount",
            type: "number",
            placeholder: " ",
            disabled: true,
            display: false,
            hide: true,
            showColumn: false,
            precision: 2,
            span: 6,
          },
          {
            span: 6,
            label: "是否调账",
            prop: "adjust",
            type: "radio",
            value: 0,
            hide: true,
            row: false,
            dicData: [
              {
                label: "是",
                value: 1,
              },
              {
                label: "否",
                value: 0,
              },
            ],
            change: ({ column, value }) => {
              const adjustId = this.findObject(this.option.column, "adjustId");
              const adjustAmount = this.findObject(
                this.option.column,
                "adjustAmount"
              );
              if (value === 1) {
                adjustId.display = true;
                adjustAmount.display = true;
                column.row = false;
              } else {
                adjustId.display = false;
                adjustAmount.display = false;
                column.row = true;
                if (this.form.adjustId && this.form.adjustAmount) {
                  this.form.unPayAmount += this.form.adjustAmount;
                  this.form.amount = null;
                  this.form.upperAmount = null;
                }
                this.form.adjustId = null;
                this.form.adjustAmount = 0;
              }
            },
          },
          {
            label: "调账申请",
            prop: "adjustSerialNo",
            placeholder: " ",
            display: false,
            search: true,
            hide: true,
            minWidth: 110,
            overHidden: true,
          },
          {
            label: "调账金额",
            prop: "adjustAmount",
            type: "number",
            placeholder: " ",
            disabled: true,
            precision: 2,
            span: 6,
            hide: true,
            row: true,
            display: false,
          },
          {
            label: "待付款金额",
            prop: "unPayAmount",
            type: "number",
            placeholder: " ",
            disabled: true,
            display: true,
            hide: true,
            showColumn: false,
            precision: 2,
            span: 6,
          },
          {
            label: "本次付款金额",
            labelTip: "含税",
            prop: "amount",
            type: "number",
            placeholder: " ",
            minWidth: 100,
            precision: 2,
            span: 6,
            rules: [
              {
                required: true,
                message: "请输入本次付款金额(含税)",
                trigger: "blur",
              },
            ],
          },
          {
            label: "金额大写",
            prop: "upperAmount",
            readonly: true,
            placeholder: " ",
            hide: true,
            showColumn: false,
            span: 6,
          },
          {
            label: "币种",
            prop: "currency",
            type: "select",
            minWidth: 70,
            dicUrl: "/api/blade-system/dict/dictionary?code=currency",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            value: "RMB",
            placeholder: " ",
            span: 6,
            row: true,
            rules: [
              {
                required: true,
                message: "请选择币种",
                trigger: "blur",
              },
            ],
            control: (val) => {
              if (val === "RMB") {
                return {
                  excRate: {
                    display: false,
                    value: 1,
                  },
                  currency: {
                    row: true,
                  },
                };
              } else {
                return {
                  excRate: {
                    display: true,
                  },
                  currency: {
                    row: false,
                  },
                };
              }
            },
          },
          {
            label: "汇率",
            prop: "excRate",
            labelTip:
              "汇率=本位币/原币.如本位币为人民币，原币为美元: 汇率为:0.1439.",
            type: "number",
            placeholder: " ",
            hide: true,
            display: false,
            span: 6,
            row: true,
            rules: [
              {
                required: true,
                message: "请输入汇率",
                trigger: "blur",
              },
            ],
          },
          {
            label: "付款日期",
            prop: "applyPayableDate",
            type: "date",
            minWidth: 95,
            placeholder: " ",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            span: 6,
            rules: [
              {
                required: true,
                message: "请选择申请付款日期",
                trigger: "blur",
              },
            ],
          },
          {
            label: "付款方式",
            prop: "paymentType",
            type: "select",
            minWidth: 85,
            dicData: [],
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            placeholder: " ",
            rules: [
              {
                required: true,
                message: "请输入",
                trigger: "blur",
              },
            ],
          },
          {
            label: "发票类型",
            prop: "billType",
            minWidth: 70,
            type: "select",
            dicData: [],
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            placeholder: " ",
            span: 6,
            rules: [
              {
                required: true,
                message: "请输入",
                trigger: "blur",
              },
            ],
            change: ({ value }) => {
              if (value === "1") {
                this.form.taxRate = 13;
              } else if (value === "2") {
                this.form.taxRate = 0;
              }
            },
          },
          {
            label: "税率(%)",
            prop: "taxRate",
            minWidth: 65,
            placeholder: " ",
            type: "number",
            precision: 2,
            controls: false,
            hide: true,
            span: 6,
          },
          {
            label: "账套",
            prop: "brand",
            placeholder: " ",
            type: "radio",
            overHidden: true,
            minWidth: 58,
            display: false,
            dicData: [],
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            search: true,
            rules: [
              {
                required: true,
                message: "请选择账套",
                trigger: "blur",
              },
            ],
          },
          {
            label: "发票号",
            prop: "billSerialNo",
            overHidden: true,
            minWidth: 110,
            display: false,
          },
          {
            label: "备注",
            prop: "remark",
            type: "textarea",
            search: true,
            minRows: 2,
            span: 24,
            overHidden: true,
            minWidth: 110,
          },
          {
            label: "单据状态",
            prop: "docStatus",
            type: "select",
            search: true,
            dicData: [],
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            display: false,
            width: 80,
          },
          {
            label: "申请人",
            prop: "createUser",
            type: "select",
            remote: true,
            dicUrl: `/api/blade-user/search/otj/user?name={{key}}`,
            props: {
              label: "realName",
              value: "id",
            },
            dicFormatter: (data) => {
              return data.data.records;
            },
            hide: true,
            showColumn: false,
            placeholder: " ",
            filterable: true,
            display: false,
            overHidden: true,
            minWidth: 65,
            search: true,
            searchOrder: 7,
          },
          {
            label: "申请人",
            prop: "createUserName",
            display: false,
            addDisplay: false,
            editDisplay: false,
            overHidden: true,
            minWidth: 65,
          },
          {
            label: "申请时间",
            prop: "applyTime",
            type: "date",
            overHidden: true,
            sortable: true,
            display: false,
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            minWidth: 100,
            search: true,
            searchRange: true,
          },
          {
            label: "付款状态",
            prop: "payState",
            type: "select",
            search: true,
            dicData: [],
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            fixed: "left",
            dataType: "number",
            minWidth: 85,
            searchOrder: 1,
            overHidden: true,
            display: false,
          },
          {
            label: "支付宝明细",
            prop: "alipays",
            labelPosition: "top",
            display: false,
            hide: true,
            span: 24,
            showColumn: false,
          },
          {
            label: "实物信息",
            prop: "items",
            labelPosition: "top",
            display: false,
            hide: true,
            span: 24,
            showColumn: false,
          },
          {
            label: "费用明细",
            prop: "costItems",
            labelPosition: "top",
            display: false,
            span: 24,
            hide: true,
            showColumn: false,
          },
        ],
      },
      data: [],
      item: {
        selectionList: [],
        option: {
          cellBtn: true,
          addBtn: false,
          refreshBtn: false,
          columnBtn: false,
          menu: false,
          saveBtn: false,
          updateBtn: false,
          cancelBtn: false,
          editBtn: false,
          delBtn: false,
          dialogFullscreen: true,
          size: "mini",
          align: "center",
          calcHeight: 30,
          tip: false,
          border: true,
          viewBtn: false,
          dialogClickModal: false,
          selection: true,
          showSummary: true,
          sumColumnList: [
            {
              name: "num",
              type: "sum",
              decimals: 1,
            },
            {
              name: "amount",
              type: "sum",
            },
          ],
          column: [
            {
              label: "编码",
              minWidth: 100,
              placeholder: " ",
              prop: "materialCode",
              overHidden: true,
              clearable: false,
              rules: [
                {
                  required: true,
                  message: "请输入",
                  trigger: "blur",
                },
              ],
            },
            {
              label: "品名",
              prop: "materialName",
              placeholder: " ",
              disabled: true,
              overHidden: true,
            },
            {
              label: "规格",
              prop: "specification",
              placeholder: " ",
              overHidden: true,
              disabled: true,
            },
            {
              label: "材质",
              prop: "quality",
              placeholder: " ",
              disabled: true,
              overHidden: true,
            },
            {
              label: "国标",
              prop: "gb",
              placeholder: " ",
              disabled: true,
              overHidden: true,
            },
            {
              label: "单位",
              prop: "unit",
              type: "select",
              dicUrl:
                "/api/blade-system/dict-biz/dictionary?code=ni_material_unit",
              props: {
                label: "dictValue",
                value: "dictKey",
              },
              placeholder: " ",
              slot: true,
              rules: [
                {
                  required: true,
                  message: "请选择单位",
                  trigger: "blur",
                },
              ],
            },
            {
              label: "数量",
              prop: "num",
              type: "number",
              precision: 0,
              placeholder: " ",
              minWidth: 100,
              cell: true,
              rules: [
                {
                  required: true,
                  message: "请输入数量",
                  trigger: "blur",
                },
              ],
            },
            {
              label: "单价",
              prop: "price",
              type: "number",
              controls: false,
              disabled: true,
              precision: 2,
              placeholder: " ",
            },
            {
              label: "金额",
              prop: "amount",
              overHidden: true,
              type: "number",
              cell: true,
              minWidth: 100,
              precision: 2,
              placeholder: " ",
              rules: [
                {
                  required: true,
                  message: "请输入金额",
                  trigger: "blur",
                },
              ],
            },
            {
              label: "备注",
              prop: "remark",
              type: "textarea",
              minRows: 1,
              placeholder: " ",
              cell: true,
            },
          ],
        },
      },
      financeVisible: false,
      financeOption: {
        refreshBtn: false,
        columnBtn: false,
        menu: false,
        highlightCurrentRow: true,
        size: "mini",
        searchSize: "mini",
        align: "center",
        searchIndex: 3,
        searchIcon: true,
        calcHeight: 30,
        tip: false,
        border: true,
        viewBtn: true,
        reserveSelection: true,
        labelWidth: 120,
        dialogClickModal: false,
        addBtn: false,
        delBtn: false,
        editBtn: false,
        column: [
          {
            label: "",
            prop: "radio",
            type: "radio",
            width: 55,
          },
          {
            label: "开户银行",
            prop: "financeBank",
            minWidth: 110,
            placeholder: " ",
          },
          {
            label: "收款人全称",
            prop: "financeName",
            placeholder: " ",
          },
          {
            label: "银行账号",
            prop: "financeAccount",
            placeholder: " ",
            minWidth: 180,
          },
          {
            label: "付款方式",
            prop: "payType",
            placeholder: " ",
            minWidth: 120,
          },
          {
            label: "发票类型",
            prop: "bill",
            placeholder: " ",
            minWidth: 120,
          },
          {
            label: "备注",
            prop: "remark",
            placeholder: " ",
            minWidth: 120,
          },
        ],
      },
      financeSelectionList: [],
      financeForm: {},
      financeData: [],
      brandDict: [],
      brandDictKeyValue: {},
      docStatusDict: [],
      docStatusDictKeyValue: {},
      statusDictKeyValue: {},
      paymentTypeKeyValue: {},
      payStateDictKeyValue: {},
      billTypeDictKeyValue: {},
      itemDialogShow: false,
      applyPrintTemplate: null,
      printTemplate: null,
      itemsPrintTemplate: null,
      alipayPrintTemplate: null,
      expensePrintTemplate: null, //费用报销
      attachDetail: false,
      dataType: "all",
      todo: false,
      un2Void: true,
      brand: "natergy",
      costApplyId: null,
      export: {
        column: [
          {
            label: "审批状态",
            prop: "status",
          },
          {
            label: "主题",
            prop: "title",
          },
          {
            label: "编号",
            prop: "serialNo",
          },
          {
            label: "收款人全称",
            prop: "financeName",
          },
          {
            label: "申请类型",
            prop: "type",
          },
          {
            label: "关联合同",
            prop: "contractSerialNo",
          },
          {
            label: "本次付款金额",
            prop: "amount",
          },
          {
            label: "付款日期",
            prop: "applyPayableDate",
          },
          {
            label: "付款方式",
            prop: "paymentType",
          },
          {
            label: "发票类型",
            prop: "billType",
          },
          {
            label: "账套",
            prop: "brand",
          },
          {
            label: "备注",
            prop: "remark",
          },
          {
            label: "单据状态",
            prop: "docStatus",
          },
          {
            label: "申请人",
            prop: "createUserName",
          },
          {
            label: "申请时间",
            prop: "createTime",
          },
          {
            label: "付款状态",
            prop: "payState",
          },
        ],
      },
    };
  },
  computed: {
    ...mapGetters(["permission", "userInfo", "colorName"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.payableApply_add, false),
        viewBtn: this.vaildData(this.permission.payableApply_view, false),
        delBtn: this.vaildData(this.permission.payableApply_delete, false),
        editBtn: this.vaildData(this.permission.payableApply_edit, false),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  created() {
    this.dictInit();
  },
  beforeRouteEnter(to, from, next) {
    to.meta.keepAlive = true;
    next();
  },
  watch: {
    todo: {
      handler() {
        this.page.currentPage = 1;
        this.onLoad(this.page);
      },
      immediate: false,
    },
    un2Void: {
      handler() {
        this.page.currentPage = 1;
        this.onLoad(this.page);
      },
      immediate: false,
    },
  },
  activated() {
    //付款申请打印模板
    loadPrintTemplate("ni_fin_payable_apply_order").then((res) => {
      this.applyPrintTemplate = JSON.parse(res.data.data.content);
    });
    loadPrintTemplate("ni_fin_payable_apply").then((res) => {
      this.printTemplate = JSON.parse(res.data.data.content);
    });
    loadPrintTemplate("ni_fin_payable_apply-plus").then((res) => {
      this.printTemplatePlus = JSON.parse(res.data.data.content);
    });
    loadPrintTemplate("ni_fin_payable_apply_item").then((res) => {
      this.itemsPrintTemplate = JSON.parse(res.data.data.content);
    });
    loadPrintTemplate("ni_fin_payable_apply_alipay").then((res) => {
      this.alipayPrintTemplate = JSON.parse(res.data.data.content);
    });
    loadPrintTemplate("ni_fin_expense_account").then((res) => {
      this.expensePrintTemplate = JSON.parse(res.data.data.content);
    });
    this.onLoad(this.page);
  },
  methods: {
    handleExport() {
      this.$confirm("是否导出当前筛选的所有数据？", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const q = { ...this.query };
        if (!q.descs && !q.ascs) {
          q.descs = "id";
        }
        if (this.brand === "natergy") {
          q.brand = "1,2";
        } else if (this.brand === "yy") {
          q.brand = "4";
        }
        if (this.todo) {
          q.todo = true;
        } else {
          q.todo = null;
        }
        if (this.un2Void) {
          q.unStatus = "0,4,6";
        } else {
          q.unStatus = "0";
        }
        if (q.applyTime && q.applyTime.length > 0) {
          q.startApplyTime = q.applyTime[0] + " 00:00:00";
          q.endApplyTime = q.applyTime[1] + " 23:59:59";
          q.applyTime = null;
        }
        const res = await getPage(1, 100000, q);
        this.$Export.excel({
          title: "付款申请",
          columns: this.export.column,
          data: res.data.data.records.map((item) => {
            return {
              ...item,
              brand: this.brandDictKeyValue[item.brand],
              status: this.statusDictKeyValue[item.status],
              paymentType: this.paymentTypeKeyValue[item.paymentType],
              payState: this.payStateDictKeyValue[item.payState],
              docStatus: this.docStatusDictKeyValue[item.docStatus],
              billType: this.billTypeDictKeyValue[item.billType],
            };
          }),
        });
      });
    },
    handleCostApplySelectConfirm(selectionList) {
      selectionList.forEach((item) => {
        this.form.items.push({
          budgetId: item.budgetId,
          budgetItemId: item.budgetItemId,
          costApplyItemId: item.id,
          materialName: item.materialName,
          materialCode: item.materialCode,
          specification: item.specification,
          quality: item.quality,
          unit: item.unit,
          num: item.num,
          amount: item.amount,
          remark: item.remark,
          cost: true,
          budgetSerialNo: item.serialNo,
          gb: item.gb,
          price: item.price,
        });
      });
    },
    rowCostApplyChange(row) {
      if (row && row.value)
        getCostApplyDetail(row.value).then((res) => {
          const data = res.data.data;
          this.form.budgetId = data.budgetId;
        });
    },
    handleDataTypeChange(val) {
      if (val === "all") {
        this.query.payStates = null;
      } else if (val === "pay") {
        this.query.payStates = "2";
      } else if (val === "unPay") {
        this.query.payStates = "0,1";
      }
      this.onLoad(this.page);
    },
    rowPass(row) {
      if (!row.taskId) {
        this.$message.warning("数据异常，请刷新后重试");
        return;
      }
      this.$confirm(
        `确定要将该申请全部<span style='color: red;'>通过</span>吗？`,
        "警告",
        {
          type: "warning",
          dangerouslyUseHTMLString: true,
        }
      )
        .then(() => {
          this.loading = true;
          completeTask({ taskId: row.taskId, pass: true })
            .then(() => {
              this.$message.success("操作成功");
              this.onLoad(this.page);
            })
            .catch(() => {
              this.loading = false;
            });
        })
        .catch(() => {});
    },
    rowDetails(row) {
      // this.$refs.payableApplyItemRef.init(row);
    },
    rowAlipayBills(row) {
      this.$refs.alipayBillTableDrawerRef.onShowByPayableApplyId(row.id);
    },
    rowBillSerialNo(row) {
      let res;
      if (["1", "2", "5"].includes(row.type)) {
        res = this.$confirm(
          "该操作将会同步更新订单中关联的发票编号,是否继续?",
          {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }
        ).then(() => {
          return this.$prompt("", "请输入发票号", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
          });
        });
      } else {
        res = this.$prompt("", "请输入发票号", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
        });
      }
      res.then(({ value }) => {
        changeBillSerialNo(row.id, value).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
      });
    },
    rowAttach(row) {
      // if (row.status > 0) {
      //   this.attachDetail = true;
      // } else {
      this.attachDetail = false;
      // }
      this.$refs.attachDialogRef.init(row.id, this.module);
    },
    handleSumAmount(amount) {
      if (["1", "3"].includes(this.form.type)) {
        this.form.amount = amount;
        this.form.upperAmount = numToCapital(this.form.amount);
      }
    },
    handleItemSelect(selectionList) {
      this.form.items = selectionList.map((item) => {
        return {
          budgetSerialNo: item.serialNo,
          budgetId: item.budgetId,
          budgetItemId: item.id,
          materialName: item.materialName,
          materialCode: item.materialCode,
          specification: item.specification,
          quality: item.quality,
          gb: item.gb,
          unit: item.unit,
          price: item.price,
          usedNum: item.applyNum ? item.applyNum : 0,
          budgetNum: item.num,
          remark: item.remark,
          cost: item.cost,
        };
      });
    },
    handleBudgetClear() {
      this.form.budgetId = null;
      this.form.items = [];
    },
    dictInit() {
      const typeColumn = this.findObject(this.option.column, "type");
      const brandColumn = this.findObject(this.option.column, "brand");
      const docStatusColumn = this.findObject(this.option.column, "docStatus");
      const status = this.findObject(this.option.column, "status");
      const paymentType = this.findObject(this.option.column, "paymentType");
      const payState = this.findObject(this.option.column, "payState");
      const billType = this.findObject(this.option.column, "billType");
      this.$http
        .get("/api/blade-system/dict-biz/dictionary?code=ni_bill_type")
        .then((res) => {
          billType.dicData = res.data.data;
          this.billTypeDictKeyValue = res.data.data.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });
      this.$http
        .get(
          "/api/blade-system/dict-biz/dictionary?code=ni_fin_payable_apply_pay_state"
        )
        .then((res) => {
          payState.dicData = res.data.data;
          this.payStateDictKeyValue = res.data.data.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          });
        });
      this.$http
        .get("/api/blade-system/dict-biz/dictionary?code=ni_fin_payment_type")
        .then((res) => {
          paymentType.dicData = res.data.data;
          this.paymentTypeKeyValue = res.data.data.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });
      this.$http
        .get("/api/blade-system/dict/dictionary?code=data_status")
        .then((res) => {
          status.dicData = res.data.data;
          this.statusDictKeyValue = res.data.data.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });
      this.$http
        .get(
          "/api/blade-system/dict-biz/dictionary?code=ni_fin_payable_apply_doc_status"
        )
        .then((res) => {
          docStatusColumn.dicData = res.data.data;
          this.docStatusDict = res.data.data;
          this.docStatusDictKeyValue = this.docStatusDict.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });
      this.$http
        .get(
          "/api/blade-system/dict-biz/dictionary?code=ni_fin_payable_apply_type"
        )
        .then((res) => (typeColumn.dicData = res.data.data));
      this.$http
        .get("/api/blade-system/dict-biz/dictionary?code=ni_brand")
        .then((res) => {
          brandColumn.dicData = res.data.data;
          this.brandDict = res.data.data;
          this.brandDictKeyValue = this.brandDict.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });
    },
    sumAmount() {
      this.$nextTick(() => {
        const itemAmount = this.form.items.reduce((acc, cur) => {
          return Number(acc) + Number(cur.amount);
        }, 0);
        this.form.amount = Number(itemAmount.toFixed(2));
        this.form.upperAmount = numToCapital(this.form.amount);
      });
    },
    itemDelete() {
      const indexList = this.item.selectionList.map((item) => item.$index);
      const [...items] = this.form.items.filter(
        (item, index) => !indexList.includes(index)
      );
      this.form.items = items;
      this.sumAmount();
    },
    itemAdd() {
      if (
        this.form.type === "3" &&
        !this.form.costApplyId &&
        !this.form.budgetId
      ) {
        this.$message.warning("请选择费用/预算");
        return;
      }
      if (!this.form.budgetId) {
        this.$message.warning("请选择预算");
        return;
      }
      if (this.form.type === "3" && this.form.costApplyId) {
        this.$refs.costApplyItemRef.visible = true;
      } else this.$refs.porBudgetItemRef.init(this.form.budgetId);
    },
    async handlePrintChange(command) {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择要打印的数据");
        return;
      } else if (this.selectionList.length > 1) {
        this.$message.warning("只能选择一条数据");
        return;
      }
      let printData;
      let hiprintTemplate;
      if (command === "payApplyItem") {
        if (!this.printTemplate) {
          this.$message.error("打印模板加载失败，请联系管理员");
          return;
        }
        const data = await getPrintData(this.selectionList[0].id);
        printData = data.data.data;
        console.log(printData);
        if (printData && (!printData.items || printData.items.length <= 0)) {
          this.$message.error("没有查询到可打印的明细");
          return;
        }
        if (this.selectionList[0].contractSerialNo) {
          printData.contractSerialNo = this.selectionList[0].contractSerialNo;
        }
        if (!printData.projectSerialNo) {
          printData.projectSerialNo = printData.budgetSerialNo;
        }
        //根据账套修改标题
        if (printData.brand === "2") {
          printData.title = "至简付款申请单";
        } else if (printData.brand === "4") {
          printData.title = "演绎付款申请单";
        } else {
          printData.title = "付款申请单";
        }
        if (printData.items)
          printData.items.forEach((item) => {
            if (item.remark && item.remark.length > 20) {
              item.remark = item.remark.substr(0, 20) + "...";
            }
          });
        console.log(printData);
        hiprintTemplate = new hiprint.PrintTemplate({
          template: this.printTemplate,
        });
      } else if (command === "payApplyItemPlus") {
        if (!this.printTemplatePlus) {
          this.$message.error("打印模板加载失败，请联系管理员");
          return;
        }
        const data = await getPrintData(this.selectionList[0].id);
        printData = data.data.data;
        if (this.selectionList[0].contractSerialNo) {
          printData.contractSerialNo = this.selectionList[0].contractSerialNo;
        }
        if (!printData.projectSerialNo) {
          printData.projectSerialNo = printData.budgetSerialNo;
        }
        //根据账套修改标题
        if (printData.brand === "2") {
          printData.title = "至简付款申请单";
        } else if (printData.brand === "4") {
          printData.title = "演绎付款申请单";
        } else {
          printData.title = "付款申请单";
        }
        if (printData.items)
          printData.items.forEach((item) => {
            if (item.remark && item.remark.length > 20) {
              item.remark = item.remark.substr(0, 20) + "...";
            }
          });
        hiprintTemplate = new hiprint.PrintTemplate({
          template: this.printTemplatePlus,
        });
      } else if (command === "items") {
        if (!this.itemsPrintTemplate) {
          this.$message.error("打印模板加载失败，请联系管理员");
          return;
        }
        const data = await getPrintData(this.selectionList[0].id);
        printData = data.data.data;
        if (this.selectionList[0].contractSerialNo) {
          printData.contractSerialNo = this.selectionList[0].contractSerialNo;
        }
        if (printData.alipays) {
          hiprintTemplate = new hiprint.PrintTemplate({
            template: this.alipayPrintTemplate,
          });
        } else {
          hiprintTemplate = new hiprint.PrintTemplate({
            template: this.itemsPrintTemplate,
          });
        }
      } else if (command === "payApply") {
        if (!this.applyPrintTemplate) {
          this.$message.error("打印模板加载失败，请联系管理员");
          return;
        }
        const data = await getPrintData(this.selectionList[0].id);
        printData = data.data.data;
        if (this.selectionList[0].contractSerialNo) {
          printData.contractSerialNo = this.selectionList[0].contractSerialNo;
        }
        if (!printData.projectSerialNo) {
          printData.projectSerialNo = printData.budgetSerialNo;
        }
        //根据账套修改标题
        if (printData.brand === "2") {
          printData.title = "至简付款申请单";
        } else if (printData.brand === "4") {
          printData.title = "演绎付款申请单";
        } else {
          printData.title = "付款申请单";
        }
        hiprintTemplate = new hiprint.PrintTemplate({
          template: this.applyPrintTemplate,
        });
      } else if (command === "expense") {
        if (!this.expensePrintTemplate) {
          this.$message.error("打印模板加载失败，请联系管理员");
          return;
        }
        const data = await getPrintData(this.selectionList[0].id);
        printData = data.data.data;
        if (this.selectionList[0].contractSerialNo) {
          printData.contractSerialNo = this.selectionList[0].contractSerialNo;
        }
        if (!printData.projectSerialNo) {
          printData.projectSerialNo = printData.budgetSerialNo;
        }
        hiprintTemplate = new hiprint.PrintTemplate({
          template: this.expensePrintTemplate,
        });
      }
      hiprintTemplate.print(printData);
    },
    tableRowClassName({ row }) {
      if (row.toVoid) return "del-line";
    },
    rowView(row, index) {
      if (row.processInsId) {
        this.dynamicRoute(
          {
            processInstanceId: row.processInsId,
            formKey: this.formKey,
            processDefKey: this.processDefKey,
          },
          "detail"
        );
      } else {
        this.$refs.crud.rowView(row, index);
      }
    },
    handleApply() {
      this.dynamicRoute(
        {
          processDefKey: this.processDefKey,
          formKey: this.formKey,
        },
        "start"
      );
    },
    handleAmount(value) {
      if (value) {
        this.form.upperAmount = numToCapital(value);
      }
    },
    handleFinanceConfirm() {
      this.form.financeBank = this.financeSelectionList[0].financeBank;
      this.form.financeName = this.financeSelectionList[0].financeName;
      this.form.financeAccount = this.financeSelectionList[0].financeAccount;
      this.form.payType = this.financeSelectionList[0].payType;
      this.form.bill = this.financeSelectionList[0].bill;
      this.financeVisible = false;
    },
    rowClick(row) {
      this.financeSelectionList = [row];
      this.$set(this.financeForm, "radio", row.$index);
    },
    async handleAlipayBillSelectConfirm(selectList) {
      if (selectList && selectList.length > 0) {
        const itemRes = await getAlipayList({
          ids: selectList.map((item) => item.id).join(","),
        });
        this.form.alipayAmount = selectList.reduce((acc, cur) => {
          return Number(acc) + Number(cur.amount);
        }, 0);
        this.form.unPayAmount = this.form.alipayAmount;
        this.form.alipays = itemRes.data.data;
      }
    },
    beforeContractSelect(done) {
      if (!this.form.brand) {
        this.$message({
          type: "warning",
          message: "请选择账套!",
        });
        return;
      }
      if (!this.form.supplierId) {
        this.$message({
          type: "warning",
          message: "请选择供应商!",
        });
        return;
      }
      done();
    },
    async handleContractIdConfirm(selectList) {
      if (selectList != null && selectList.length > 0) {
        const contract = selectList[0];
        this.form.contractAmount = contract.amount;
        this.form.unPayAmount =
          Number(contract.amount) -
          (Number(contract.payApplyAmount)
            ? Number(contract.payApplyAmount)
            : 0);
        //如果是采购合同，则把采购订单的明细拉过来
        const itemRes = await getItemList({
          contractId: this.form.contractId,
        });
        this.form.items = itemRes.data.data;
      }
    },
    rowPayable(row) {
      getDetail(row.id).then((res) => {
        const { data } = res.data;
        const form = {
          financeBank: row.financeBank,
          financeName: row.financeName,
          financeAccount: row.financeAccount,
          relatedId: row.supplierId,
          applyAmount: row.amount,
          payAmount: row.payAmount ? row.payAmount : 0,
          amount:
            Number(row.amount) - Number(row.payAmount ? row.payAmount : 0),
          type: "1",
          subType: row.type,
          brand: row.brand,
          currency: row.currency,
          financialUserId: this.userInfo.user_id,
          date: dateNow1(),
          source: row.id,
          unPayAmount: Number(row.amount) - Number(row.payAmount),
          remark: row.remark,
          paymentType: row.paymentType,
          red: false,
        };
        form.items = data.items.map((item) => {
          return {
            ...item,
            rowNo: item.row + (item.no ? "-" + item.no : ""),
            sourceId: item.id,
            id: null,
          };
        });
        if ([1, 2].includes(row.status)) {
          this.$confirm(
            "该申请还在<span style='color: #F56C6C;font-weight: bolder'>审核中</span>，确认要付款？",
            "提示",
            {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
              dangerouslyUseHTMLString: true,
              center: true,
            }
          ).then(() => {
            this.$refs.payableFormDialogRef.onAdd(form);
          });
        } else this.$refs.payableFormDialogRef.onAdd(form);
      });
    },
    handleSubmit(type) {
      this.form.status = 2;
      if (type == "add") {
        this.$refs.crud.rowSave();
      } else if (type == "edit") {
        this.$refs.crud.rowUpdate();
      }
    },
    handlePaySoaSubmit(selectList) {
      if (selectList && selectList.length > 0) {
        getPaySoaDetail(selectList[0].id).then((res) => {
          const data = res.data.data;
          this.form.paySoaAmount = data.amount;
          this.form.unPayAmount =
            Number(data.amount) - Number(data.payApplyAmount);
          this.form.billType = data.billType;
          this.form.taxRate = data.taxRate;
          this.form.items = data.items;
        });
      }
    },
    handleOrderClear() {
      this.form.porOrderSerialNo = null;
      this.form.amount = null;
      this.form.porOrderAmount = null;
      this.form.items = [];
    },
    async handleOrderSubmit(selectList) {
      this.form.currency = selectList[0].currency
        ? selectList[0].currency
        : "RMB";
      this.form.title = `[${this.form.supplier}]采购订单(${selectList[0].title})的付款申请单`;
      this.form.porOrderAmount = selectList.reduce((acc, cur) => {
        return acc + Number(cur.amount);
      }, 0);
      const itemRes = await getOrderItemList({
        orderIds: selectList.map((item) => item.id).join(","),
      });
      const items = itemRes.data.data;
      items.forEach(
        (item) =>
          (item.price = (Number(item.amount) / Number(item.num)).toFixed(2))
      );
      this.form.items = items;
      this.form.unPayAmount = selectList.reduce((acc, cur) => {
        return acc + Number(cur.amount) - Number(cur.payApplyAmount);
      }, 0);
    },
    handleSupplierClear() {
      this.form.supplierId = null;
      this.form.supplier = null;
    },
    handleSupplierSubmit(selectList) {
      this.form.supplier = selectList[0].name;
      this.form.title = `供应商[${selectList[0].name}]的付款申请单`;
      //选择付款账户
      getSupplierDetail(selectList[0].id).then((res) => {
        const data = res.data.data;
        const finance = data.finance;
        this.form.financeBank = null;
        this.form.financeName = null;
        this.form.financeAccount = null;
        if (finance && finance.length === 1) {
          this.form.financeBank = finance[0].financeBank;
          this.form.financeName = finance[0].financeName;
          this.form.financeAccount = finance[0].financeAccount;
          this.form.payType = finance[0].payType;
          this.form.bill = finance[0].bill;
        } else if (finance && finance.length > 1) {
          this.financeData = finance;
          this.financeVisible = true;
        }
      });
      this.form.porOrderId = null;
      this.form.porOrderSerialNo = null;
      this.form.porOrderAmount = null;
      this.form.contractId = null;
      this.form.contractAmount = null;
      this.form.alipayId = null;
      this.form.alipayAmount = null;
      this.form.unPayAmount = null;
      this.form.budgetId = null;
      this.form.paySoaAmount = null;
      this.form.paySoaId = null;
      this.form.items = [];
      this.form.alipays = [];
    },
    rowToVoid(row) {
      this.$confirm("此操作作废提交的数据，是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        center: true,
      })
        .then(() => {
          return toVoid(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    rowBack(row) {
      this.$confirm("此操作将撤回提交的数据，是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        center: true,
      })
        .then(() => {
          return back(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    rowSubmit(row) {
      this.$confirm("此操作将提交该数据，是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        center: true,
      })
        .then(() => {
          return submit(row.id, this.processDefKey);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    rowSave(row, done, loading) {
      add(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          window.console.log(error);
        }
      );
    },
    sortChange(val) {
      const { prop, order } = val;
      console.log(val);
      if (order == "ascending") {
        this.query.ascs = prop;
      } else if (order == "descending") {
        this.query.descs = prop;
      }
      this.onLoad(this.page);
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          console.log(error);
        }
      );
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    handleChangeDocStatus(status) {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      const toVoid = this.selectionList.some((item) => item.toVoid);
      if (toVoid) {
        this.$message.warning("选择的数据存在已作废的数据，请重新选择");
        return;
      }
      const cancelStatus = this.selectionList.some((item) =>
        [4, 6].includes(item.status)
      );
      if (cancelStatus) {
        this.$message.warning("选择的数据有取消/終止的数据，请重新选择");
        return;
      }
      const msg = `确定将选择数据的单据状态修改为<span style='color: #F56C6C;font-weight: bold'>${this.docStatusDictKeyValue[status]}</span>`;
      this.$confirm(msg, {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        dangerouslyUseHTMLString: true,
        type: "warning",
      })
        .then(() => {
          return changeDocStatus(this.ids, status);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    handleToVoid() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      const toVoid1 = this.selectionList.some((item) => item.toVoid);
      if (toVoid1) {
        this.$message.warning("选择的数据存在已作废的数据，请重新选择");
        return;
      }
      const payState = this.selectionList.some(
        (item) => item.payState != null && item.payState != 0
      );
      if (payState) {
        this.$message.warning("选择的数据已经付款，不能作废");
        return;
      }
      this.$confirm("确定将选择数据作废?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return toVoid(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    handleAdd(brand) {
      this.form.brand = brand;
      this.option.addTitle = `新增【${this.brandDictKeyValue[brand]}】`;
      this.$refs.crud.rowAdd();
    },
    handlePayApply() {
      this.dynamicRoute(
        {
          processDefKey: this.processDefKey,
          formKey: this.formKey,
        },
        "start"
      );
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen(done, type) {
      if ("add" === type) {
        this.form.status = 9;
        this.form.type = "2";
        this.form.payState = "1";
        this.form.items = [];
      } else if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then((res) => {
          this.form = res.data.data;
          this.form.upperAmount = numToCapital(this.form.amount);
        });
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.page.currentPage = 1;
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      const q = {
        ...params,
        ...this.query,
      };
      if (!q.descs && !q.ascs) {
        q.descs = "id";
      }
      if (this.brand === "natergy") {
        q.brand = "1,2";
      } else if (this.brand === "yy") {
        q.brand = "4";
      }
      if (this.todo) {
        q.todo = true;
      } else {
        q.todo = null;
      }
      if (this.un2Void) {
        q.unStatus = "0,4,6";
      } else {
        q.unStatus = "0";
      }
      if (q.applyTime && q.applyTime.length > 0) {
        q.startApplyTime = q.applyTime[0] + " 00:00:00";
        q.endApplyTime = q.applyTime[1] + " 23:59:59";
        q.applyTime = null;
      }
      getPage(page.currentPage, page.pageSize, q).then((res) => {
        const data = res.data.data;
        data.records.forEach((item) => {
          if (!item.billSerialNo) {
            item.billSerialNo = "";
          }
        });
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
    cellStyle({ row, column }) {
      if ("title" === column.columnKey && row.docStatus === "1") {
        return {
          backgroundColor: "#faecd8",
        };
      } else if ("title" === column.columnKey && row.docStatus === "2") {
        return {
          backgroundColor: "#67C23A",
          color: "#fff",
        };
      }
    },
    async rowContractPay(row) {
      const { data } = await getContractDetail(row.contractId);
      this.$refs.contractPayBuildDialogRef.init(data.data);
    },
    beforeAdjustSelect(done) {
      if (!this.form.brand) {
        this.$message({
          type: "warning",
          message: "请选择账套!",
        });
        return;
      }
      if (!this.form.supplierId) {
        this.$message({
          type: "warning",
          message: "请选择供应商!",
        });
        return;
      }
      done();
    },
    handleAdjustChange(selection) {
      if (selection) {
        this.form.adjustAmount = selection[0].amount;
        this.form.unPayAmount -= this.form.adjustAmount;
        this.form.amount = this.form.unPayAmount;
        this.form.upperAmount = numToCapital(this.form.amount);
      }
    },
  },
};
</script>

<style>
.del-line {
  text-decoration: line-through !important;
}

.del-line.hover-row td {
  text-decoration: initial !important;
}
</style>

<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      :cell-style="cellStyle"
      @on-load="onLoad"
    >
      <template #serialNo="{ row, index }">
        <span @click="rowView(row)">{{ row.serialNo }}</span>
      </template>
      <template #fromNumForm="{ row, index }">
        <el-input-number
          size="mini"
          v-model="row.fromNum"
          style="width: 100%"
          :max="row.currentStock"
          :controls="false"
        />
      </template>
      <template #toSkuIdForm="{ row, index }">
        <span
          :style="{
            color: colorName,
            textDecoration: 'underline',
            cursor: 'pointer',
          }"
          @click="rowSkuSelect(row, index)"
          >{{ row.toSkuId ? row.toSkuText : "未选择" }}</span
        >
      </template>
      <template #menuLeft>
        <el-button
          type="primary"
          size="mini"
          icon="el-icon-plus"
          plain
          v-if="permission.niFgRestocking_add"
          @click="handleAdd"
          >新 增
        </el-button>
        <el-button
          type="danger"
          size="mini"
          icon="el-icon-delete"
          plain
          v-if="permission.niFgRestocking_delete"
          @click="handleDelete"
          >删 除
        </el-button>
      </template>
      <template #menu="{ row, index }">
        <el-button
          type="text"
          size="mini"
          icon="el-icon-edit"
          v-if="permission.niFgRestocking_edit && row.status === 1"
          @click="rowEdit(row)"
          >编辑
        </el-button>
        <el-button
          type="text"
          size="mini"
          icon="el-icon-delete"
          v-if="permission.niFgRestocking_delete && row.status === 1"
          @click="$refs.crud.rowDel(row, index)"
          >删 除
        </el-button>
        <!--审核-->
        <el-button
          type="text"
          size="mini"
          icon="el-icon-check"
          v-if="row.status === 1 && permission.niFgRestocking_audit"
          @click="rowAudit(row, index)"
          >审核
        </el-button>
        <!--冲红-->
        <el-button
          type="text"
          size="mini"
          icon="el-icon-back"
          v-if="permission.niFgRestocking_red && row.status === 2 && !row.red"
          @click="rowRed(row, index)"
          >冲红
        </el-button>
      </template>
    </avue-crud>
    <inventory-select-dialog
      ref="inventorySelectDialog"
      :params="{ depotId: form.depotId }"
      @confirm="handleInventorySelectConfirm"
    />
    <sku-select-dialog
      ref="skuSelectDialogRef"
      @confirm="handleSkuSelectChange"
      :params="{ status: 1 }"
    />
    <restocking-form-dialog
      ref="restockingFormDialog"
      @confirm="handleRestockingConfirm"
    />
  </basic-container>
</template>

<script>
import {
  add,
  audit,
  getDetail,
  getList,
  red,
  remove,
  update,
} from "@/api/ni/fg/fgRestocking";
import { mapGetters } from "vuex";
import { dateFormat } from "@/util/date";
import InventorySelectDialog from "@/views/ni/fg/components/InventorySelectDialog.vue";
import SkuSelectDialog from "@/views/ni/product/components/SkuSelectDialog.vue";
import RestockingFormDialog from "@/views/ni/fg/components/RestockingFormDialog.vue";

export default {
  components: { SkuSelectDialog, InventorySelectDialog, RestockingFormDialog },
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        addBtn: false,
        editBtn: false,
        delBtn: false,
        span: 8,
        dialogFullscreen: true,
        dialogDrag: true,
        size: "mini",
        searchSize: "mini",
        align: "center",
        searchIndex: 3,
        searchIcon: true,
        searchEnter: true,
        height: "auto",
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        selection: true,
        dialogClickModal: false,
        column: [
          {
            label: "倒箱编号",
            prop: "serialNo",
            placeholder: "系统自动生成",
            minWidth: 100,
            overHidden: true,
            search: true,
          },
          {
            label: "倒箱主题",
            prop: "title",
            minWidth: 110,
            overHidden: true,
            search: true,
            rules: [
              {
                required: true,
                message: "请输入倒箱主题",
                trigger: "blur",
              },
            ],
          },
          {
            label: "仓库",
            prop: "depotId",
            type: "select",
            dicUrl: "/api/ni/base/depot/info/list?status=2&type=1",
            props: {
              label: "name",
              value: "id",
              desc: "code",
            },
            overHidden: true,
            search: true,
            rules: [
              {
                required: true,
                message: "请选择仓库",
                trigger: "blur",
              },
            ],
          },
          {
            label: "操作人",
            prop: "opUserId",
            type: "select",
            remote: true,
            dicUrl: `/api/blade-user/search/otj/user?name={{key}}`,
            props: {
              label: "realName",
              value: "id",
            },
            dicFormatter: (data) => {
              return data.data.records;
            },
            placeholder: " ",
            filterable: true,
            overHidden: true,
            rules: [
              {
                required: true,
                message: "请选择操作人",
                trigger: "blur",
              },
            ],
            search: true,
            hide: true,
            showColumn: false,
          },
          {
            label: "操作人",
            prop: "opUserName",
            display: false,
            overHidden: true,
            width: 70,
          },
          {
            label: "操作日期",
            prop: "opDate",
            search: true,
            type: "date",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            searchRange: true,
            placeholder: " ",
            clearable: false,
            width: 90,
            rules: [
              {
                required: true,
                message: "请选择操作时间",
                trigger: "blur",
              },
            ],
          },
          {
            label: "产品",
            prop: "toSkuText",
            width: 100,
            hide: true,
            display: false,
            overHidden: true,
          },
          {
            label: "存货编码",
            prop: "toMaterialCode",
            minWidth: 90,
            display: false,
            overHidden: true,
          },
          {
            label: "规格",
            prop: "toSpecText",
            width: 80,
            display: false,
            overHidden: true,
          },
          {
            label: "外包装",
            prop: "toPackageText",
            minWidth: 95,
            display: false,
            overHidden: true,
          },
          {
            label: "内包装",
            prop: "toInnerPackageText",
            minWidth: 95,
            display: false,
            overHidden: true,
          },
          {
            label: "批次",
            prop: "toBatchNo",
            minWidth: 95,
            display: false,
            search: true,
            overHidden: true,
          },
          {
            label: "倒箱数量",
            prop: "toNum",
            overHidden: true,
            minWidth: 90,
            display: false,
          },
          {
            label: "单位",
            prop: "toUnit",
            type: "select",
            filterable: true,
            width: 55,
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_material_unit",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
          },

          {
            label: "备注",
            prop: "remark",
            type: "textarea",
            span: 24,
            minRows: 2,
            overHidden: true,
            search: true,
          },
          {
            label: "创建人",
            prop: "createUserName",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            hide: true,
          },
          {
            label: "创建时间",
            prop: "createTime",
            type: "date",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            addDisplay: false,
            editDisplay: false,
            hide: true,
          },
          {
            label: "状态",
            prop: "status",
            type: "select",
            dicData: [
              {
                label: "未审核",
                value: 1,
              },
              {
                label: "已审核",
                value: 2,
              },
              {
                label: "已冲红",
                value: 3,
              },
            ],
            addDisplay: false,
            editDisplay: false,
          },
          {
            label: "倒箱明细",
            prop: "items",
            span: 24,
            type: "dynamic",
            hide: true,
            showColumn: false,
            children: {
              rowAdd: () => {
                this.handleInventorySelect();
              },
              size: "mini",
              align: "center",
              headerAlign: "center",
              showSummary: true,
              sumColumnList: [
                {
                  name: "num",
                  type: "sum",
                  decimals: 1,
                },
              ],
              column: [
                {
                  label: "库位",
                  prop: "fromLocation",
                  placeholder: " ",
                  width: 110,
                  cell: false,
                  hide: true,
                },
                {
                  label: "原品名",
                  prop: "fromSkuText",
                  placeholder: " ",
                  overHidden: true,
                  width: 120,
                  cell: false,
                },
                {
                  label: "原规格",
                  prop: "fromSpecText",
                  placeholder: " ",
                  display: false,
                  overHidden: true,
                  cell: false,
                  width: 100,
                },
                {
                  label: "原外包装",
                  prop: "fromPackageText",
                  placeholder: " ",
                  display: false,
                  overHidden: true,
                  cell: false,
                  width: 115,
                },
                {
                  label: "原内包装",
                  prop: "fromInnerPackageText",
                  placeholder: " ",
                  display: false,
                  overHidden: true,
                  cell: false,
                  width: 115,
                },
                {
                  label: "原批号",
                  prop: "fromBatchNo",
                  placeholder: " ",
                  minWidth: 100,
                  cell: false,
                },
                {
                  label: "当前库存",
                  prop: "currentStock",
                  placeholder: " ",
                  minWidth: 100,
                  cell: false,
                },
                {
                  label: "原数量",
                  prop: "fromNum",
                  placeholder: " ",
                  type: "number",
                  minWidth: 80,
                  rules: [
                    {
                      required: true,
                      message: "请输入数量",
                      trigger: "blur",
                    },
                  ],
                },
                {
                  label: "单位",
                  prop: "fromUnit",
                  type: "select",
                  filterable: true,
                  width: 55,
                  cell: false,
                  dicUrl:
                    "/api/blade-system/dict-biz/dictionary?code=ni_material_unit",
                  props: {
                    label: "dictValue",
                    value: "dictKey",
                  },
                  rules: [
                    {
                      required: true,
                      message: "请选择单位",
                      trigger: "blur",
                    },
                  ],
                  slot: true,
                  placeholder: " ",
                },
                {
                  label: "品名",
                  prop: "toSkuId",
                  overHidden: true,
                  placeholder: " ",
                  width: 125,
                },
                {
                  label: "规格",
                  prop: "toSpecText",
                  overHidden: true,
                  cell: false,
                  width: 115,
                },
                {
                  label: "外包装",
                  prop: "toPackageText",
                  overHidden: true,
                  cell: false,
                  width: 115,
                },
                {
                  label: "内包装",
                  prop: "toInnerPackageText",
                  overHidden: true,
                  cell: false,
                  width: 115,
                },
                {
                  label: "批号",
                  prop: "toBatchNo",
                  placeholder: " ",
                  minWidth: 100,
                  rules: [
                    {
                      required: true,
                      message: "请输入批号",
                      trigger: "blur",
                    },
                  ],
                },
                {
                  label: "倒箱数量",
                  prop: "toNum",
                  placeholder: " ",
                  type: "number",
                  controls: false,
                  minWidth: 80,
                  rules: [
                    {
                      required: true,
                      message: "请输入倒箱数量",
                      trigger: "blur",
                    },
                  ],
                },
                {
                  label: "单位",
                  prop: "toUnit",
                  type: "select",
                  filterable: true,
                  width: 55,
                  cell: false,
                  dicUrl:
                    "/api/blade-system/dict-biz/dictionary?code=ni_material_unit",
                  props: {
                    label: "dictValue",
                    value: "dictKey",
                  },
                  rules: [
                    {
                      required: true,
                      message: "请选择单位",
                      trigger: "blur",
                    },
                  ],
                  slot: true,
                  placeholder: " ",
                },
                {
                  label: "备注",
                  prop: "remark",
                  overHidden: true,
                  type: "textarea",
                  placeholder: " ",
                  minRows: 1,
                  minWidth: 120,
                },
              ],
            },
          },
        ],
      },
      data: [],
    };
  },
  computed: {
    ...mapGetters(["permission", "userInfo", "colorName"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.niFgRestocking_add, false),
        viewBtn: this.vaildData(this.permission.niFgRestocking_view, false),
        delBtn: this.vaildData(this.permission.niFgRestocking_delete, false),
        editBtn: this.vaildData(this.permission.niFgRestocking_edit, false),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
    items() {
      return this.form.items || [];
    },
  },
  methods: {
    rowRed(row, index) {
      this.$prompt("请输入冲红原因", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
      }).then(({ value }) => {
        if (!value) {
          this.$message({
            type: "error",
            message: "请输入冲红原因",
          });
          return;
        }
        red(row.id, value).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
      });
    },
    rowAudit(row, index) {
      if (row.auditStatus === 1) {
        this.$message({
          type: "warning",
          message: "该单据已审核通过，不能再进行审核!",
        });
        return;
      }
      this.$confirm("确认要审核此数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        audit(row.id).then(() => {
          this.data[index].status = 2;
          this.$message({
            type: "success",
            message: "审核成功",
          });
        });
      });
    },
    rowSkuSelect(row, index) {
      this._index = index;
      this.$refs.skuSelectDialogRef.onShow();
    },
    handleSkuSelectChange(selectionList) {
      if (selectionList) {
        this.items[this._index].toSkuId = selectionList[0].id;
        this.items[this._index].toSkuText = selectionList[0].sku;
        this.items[this._index].toSpecText = selectionList[0].specText;
        this.items[this._index].toPackageText = selectionList[0].packageText;
        this.items[this._index].toInnerPackageText =
          selectionList[0].innerPackageText;
        this.items[this._index].toUnit = selectionList[0].unit;
      }
    },
    handleInventorySelect() {
      if (!this.form.depotId) {
        this.$message({
          type: "warning",
          message: "请选择仓库!",
        });
        return;
      }
      this.$refs.inventorySelectDialog.onShow();
    },
    handleInventorySelectConfirm(selectionList) {
      selectionList.forEach((item) => {
        this.items.push({
          fromLocation: item.location,
          fromSkuId: item.skuId,
          fromSkuText: item.skuText,
          fromSpecText: item.specText,
          fromPackageText: item.packageText,
          fromInnerPackageText: item.innerPackageText,
          fromMaterialId: item.materialId,
          fromMaterialCode: item.materialCode,
          currentStock: item.num,
          fromUnit: item.unit,
          fromBatchNo: item.batchNo,
          toBatchNo: item.batchNo,
          toUnit: item.unit,
        });
      });
    },
    handleRestockingConfirm() {
      this.onLoad(this.page);
    },
    rowSave(row, done, loading) {
      add(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          window.console.log(error);
        }
      );
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          console.log(error);
        }
      );
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    handleAdd() {
      this.$refs.restockingFormDialog.onAdd();
    },
    rowEdit(row) {
      this.$refs.restockingFormDialog.onEdit(row.id);
    },
    rowView(row) {
      this.$refs.restockingFormDialog.onShow(row.id);
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen(done, type) {
      if ("add" === type) {
        this.form.opUserId = this.userInfo.user_id;
        this.form.opDate = dateFormat(new Date(), "yyyy-MM-dd");
      }
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then((res) => {
          this.form = res.data.data;
        });
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
    cellStyle({ row, column }) {
      if ("status" === column.columnKey && row.status === 1) {
        return {
          backgroundColor: "#E6A23C",
          color: "#fff",
        };
      } else if ("status" === column.columnKey && row.status === 2) {
        return {
          backgroundColor: "#67C23A",
          color: "#fff",
        };
      } else if ("status" === column.columnKey && row.status === 3) {
        return {
          backgroundColor: "#F56C6C",
          color: "#fff",
        };
      }
      if ("serialNo" === column.columnKey) {
        return {
          backgroundColor: row.red ? "#F56C6C" : "",
          color: row.red ? "#fff" : this.colorName,
          textDecoration: "underline",
          cursor: "pointer",
        };
      }
    },
  },
};
</script>

<style></style>

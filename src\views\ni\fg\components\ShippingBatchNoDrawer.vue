<template>
  <el-dialog :visible.sync="visible" fullscreen append-to-body :title="title">
    <div class="container">
      <el-alert
        v-if="items && items.length > 0"
        title="发货明细"
        type="info"
        :closable="false"
        style="margin-bottom: 5px"
      >
        <div v-for="(item, index) in items" :key="index">
          <span>
            {{ item.materialCode }}|{{ item.materialName }}|{{
              item.specText
            }}|{{ item.packageText }}|{{ item.innerPackageText }}|
          </span>
          <span style="color: #167c46; font-weight: bold">
            {{ item.num }}件|{{ item.weight }}kg
          </span>
          |<span v-if="item.remark" style="color: #f56c6c; font-weight: bold">
            {{ item.remark }}
          </span>
        </div>
      </el-alert>
      <avue-crud
        v-if="visible"
        :option="option"
        :table-loading="loading"
        :data="data"
        :page.sync="page"
        :before-open="beforeOpen"
        v-model="form"
        ref="crud"
        @row-update="rowUpdate"
        @row-save="rowSave"
        @row-del="rowDel"
        @search-change="searchChange"
        @search-reset="searchReset"
        @selection-change="selectionChange"
        @current-change="currentChange"
        @size-change="sizeChange"
        @refresh-change="refreshChange"
        @on-load="onLoad"
      >
        <template #menuLeft>
          <el-button
            size="mini"
            type="warning"
            v-if="option.addBtn"
            @click="handleChangeDepot"
            >调整仓库
          </el-button>
        </template>
        <template #materialCodeForm="{ row, size, disabled }">
          <div style="display: flex">
            <el-select
              :size="size"
              :disabled="disabled"
              v-model="form.materialCode"
              filterable
              remote
              allow-create
              placeholder=" "
              @change="handleMaterialCodeChange"
            >
              <el-option
                v-for="(item, index) in materialCodes"
                :key="index"
                :label="item.materialCode"
                :value="item.materialCode"
              >
                <span style="float: left">{{ item.materialCode }}</span>
                <span
                  style="
                    position: absolute;
                    right: 18px;
                    font-size: 12px;
                    color: #8492a6;
                  "
                  >{{ item.specText + "/" + item.materialName }}</span
                >
              </el-option>
            </el-select>
            <el-button
              type="info"
              @click="handleInventorySelect"
              icon="el-icon-plus"
            ></el-button>
          </div>
        </template>
        <template #batchNoForm="{ row, size, disabled, index }">
          <el-select
            :size="size"
            :disabled="disabled"
            v-model="form.batchNo"
            filterable
            remote
            allow-create
            placeholder=" "
            :remote-method="
              (query) => {
                batchNoRemoteMethod(query, form);
              }
            "
            :loading="batchNoLoading"
            @change="rowBatchNoChange($event, form)"
          >
            <el-option
              v-for="item in batchNoOptions"
              :key="item.id"
              :label="item.batchNo"
              :value="item.batchNo"
            >
              <span style="float: left">{{
                `${item.batchNo}(${item.num})`
              }}</span>
              <span
                style="
                  position: absolute;
                  right: 18px;
                  font-size: 12px;
                  color: #8492a6;
                "
                >{{ item.depotName }}</span
              >
            </el-option>
          </el-select>
        </template>
        <template #numForm="{ row, size, disabled }">
          <el-input-number
            v-model="form.num"
            :min="1"
            :size="size"
            style="width: 100%"
            :step="1"
            :controls="false"
            :disabled="disabled"
            @change="rowNumChange($event, form)"
          />
        </template>
        <template #menu="{ row, index }">
          <el-button
            type="text"
            size="mini"
            icon="el-icon-copy-document"
            @click="rowCopy(row, index)"
            >复制
          </el-button>
        </template>
        <template #menuForm="{ row, index }">
          <el-button
            type="primary"
            icon="el-icon-check"
            size="mini"
            plain
            v-if="type === 'add'"
            @click="handleNext"
            >继续添加
          </el-button>
        </template>
      </avue-crud>
    </div>
    <inventory-summary-select-dialog
      ref="inventorySelectDialog"
      :params="{ depotId: form.depotId, status: 1 }"
      @confirm="handleInventorySelectConfirm"
    />
    <el-dialog
      title="调整仓库"
      append-to-body
      :visible.sync="changeDepot.visible"
      width="555px"
    >
      <avue-form
        :option="changeDepot.option"
        v-model="changeDepot.form"
        @submit="handleChangeDepotSubmit"
      >
      </avue-form>
    </el-dialog>
  </el-dialog>
</template>
<script>
import {
  add,
  getDetail,
  getList,
  remove,
  update,
  changeDepotId,
} from "@/api/ni/fg/shippingItem";
import { loadItemMaterial } from "@/api/ni/fg/shipping";
import { getDetailByMaterialCode } from "@/api/ni/product/sku";
import { getList as getBatchNos } from "@/api/ni/fg/fgInventory";
import InventorySummarySelectDialog from "@/views/ni/fg/components/InventorySummarySelectDialog.vue";
import { mapGetters } from "vuex";

export default {
  components: { InventorySummarySelectDialog },
  props: {},
  data() {
    return {
      row: null,
      visible: false,
      title: "",
      type: "",
      flag: false,
      loading: false,
      selectionList: [],
      option: {
        menu: true,
        showSummary: true,
        sumColumnList: [
          {
            name: "num",
            type: "sum",
          },
          {
            name: "weight",
            type: "sum",
          },
        ],
        addBtn: true,
        editBtn: false,
        cellBtn: true,
        dialogDrag: true,
        size: "mini",
        searchSize: "mini",
        align: "center",
        searchIndex: 3,
        searchIcon: true,
        searchEnter: true,
        height: "auto",
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        span: 12,
        border: true,
        selection: true,
        menuWidth: 150,
        column: [
          {
            label: "仓库",
            prop: "depotId",
            type: "select",
            dicUrl: "/api/ni/base/depot/info/list?status=2&type=1",
            props: {
              label: "name",
              value: "id",
              desc: "code",
            },
            filters: true,
            placeholder: " ",
            rules: [
              {
                required: true,
                message: "请选择仓库",
                trigger: "blur",
              },
            ],
          },
          {
            label: "存货编码",
            prop: "materialCode",
            type: "select",
            dicData: [],
            filters: true,
            props: {
              label: "materialCode",
              value: "materialCode",
              desc: "materialName",
            },
            placeholder: " ",
            width: 150,
            change: (data) => {},
            rules: [
              {
                required: true,
                message: "请选择存货编码",
                trigger: "blur",
              },
            ],
          },
          {
            label: "规格",
            prop: "specText",
            placeholder: " ",
            overHidden: true,
            disabled: true,
            cell: false,
            filters: true,
            width: 120,
          },
          {
            label: "外包装",
            prop: "packageText",
            placeholder: " ",
            overHidden: true,
            disabled: true,
            cell: false,
            width: 115,
            filters: true,
          },
          {
            label: "内包装",
            prop: "innerPackageText",
            placeholder: " ",
            overHidden: true,
            disabled: true,
            cell: false,
            width: 115,
            filters: true,
          },
          {
            label: "质量",
            prop: "qualityLevel",
            type: "select",
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_product_sku_quality_level",
            props: {
              label: "dictValue",
              value: "dictKey",
              desc: "dictKey",
            },
            disabled: true,
            placeholder: " ",
            width: 100,
            filters: true,
            cell: false,
          },
          {
            label: "批号",
            prop: "batchNo",
            placeholder: " ",
            minWidth: 150,
            cell: true,
            filters: true,
            rules: [
              {
                required: true,
                message: "请输入批号",
                trigger: "blur",
              },
            ],
          },
          {
            label: "数量",
            prop: "num",
            placeholder: " ",
            type: "number",
            cell: true,
            minWidth: 80,
            rules: [
              {
                required: true,
                message: "请输入数量",
                trigger: "blur",
              },
            ],
          },
          {
            label: "重量",
            prop: "weight",
            placeholder: " ",
            disabled: true,
            minWidth: 80,
          },
          {
            label: "备注",
            prop: "remark",
            type: "textarea",
            placeholder: " ",
            filters: true,
            span: 24,
            minRows: 2,
            overHidden: true,
            cell: true,
            minWidth: 120,
          },
        ],
      },
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      data: [],
      form: {},
      batchNoLoading: false,
      batchNoOptions: [],
      materialCodes: [],
      items: [],
      changeDepot: {
        visible: false,
        form: {},
        option: {
          emptyBtn: false,
          size: "mini",
          span: 24,
          labelWidth: 110,
          column: [
            {
              label: "仓库",
              prop: "depotId",
              type: "select",
              dicUrl: "/api/ni/base/depot/info/list?status=2&type=1",
              props: {
                label: "name",
                value: "id",
                desc: "code",
              },
              rules: [
                {
                  required: true,
                  message: "请选择仓库",
                  trigger: "blur",
                },
              ],
            },
          ],
        },
      },
    };
  },
  computed: {
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  methods: {
    onEdit(row) {
      this.option.addBtn = true;
      this.option.menu = true;
      this.row = row;
      this.title = `[${row.serialNo}]登记批号`;
      if (["1", "3"].includes(row.type)) {
        loadItemMaterial(row.id).then((res) => {
          this.items = res.data.data;
          this.materialCodes = res.data.data;
        });
      }
      this.visible = true;
    },
    onShow(row) {
      this.option.addBtn = false;
      this.option.menu = false;
      this.row = row;
      this.title = `[${row.serialNo}]登记批号`;
      if (["1", "3"].includes(row.type)) {
        loadItemMaterial(row.id).then((res) => {
          this.items = res.data.data;
          this.materialCodes = res.data.data;
        });
      }
      this.visible = true;
    },
    handleNext() {
      this.flag = true;
      this.$refs.crud.rowSave();
    },
    handleChangeDepot() {
      if (!this.option.addBtn) {
        return;
      }
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.changeDepot.visible = true;
    },
    handleChangeDepotSubmit(form, done) {
      changeDepotId(this.ids, form.depotId)
        .then(() => {
          this.changeDepot.visible = false;
          this.page.currentPage = 1;
          this.onLoad(this.page);
        })
        .finally(() => {
          done();
        });
    },
    rowCopy(row) {
      this.form = {
        ...row,
        id: null,
        batchNo: null,
        num: null,
        weight: null,
        remark: null,
      };
      this.$refs.crud.rowAdd();
    },
    handleInventorySelect() {
      if (!this.form.depotId) {
        this.$message({
          type: "warning",
          message: "请选择仓库!",
        });
        return;
      }
      this.$refs.inventorySelectDialog.onShow();
    },
    handleInventorySelectConfirm(selectionList) {
      const item = selectionList[0];
      this.form.skuId = item.skuId;
      this.form.specText = item.specText;
      this.form.packageText = item.packageText;
      this.form.innerPackageText = item.innerPackageText;
      this.form.materialCode = item.materialCode;
      this.form.capacity = item.capacity;
      this.form.qualityLevel = item.qualityLevel;
    },
    handleMaterialCodeChange(value) {
      if (value) {
        getDetailByMaterialCode(value).then((res) => {
          this.form.skuId = res.data.data.id;
          this.form.specText = res.data.data.specText;
          this.form.packageText = res.data.data.packageText;
          this.form.innerPackageText = res.data.data.innerPackageText;
          this.form.qualityLevel = res.data.data.qualityLevel;
          this.form.capacity = res.data.data.capacity;
          this.form.batchNo = null;
        });
      } else {
        this.form.skuId = null;
        this.form.specText = null;
        this.form.packageText = null;
        this.form.innerPackageText = null;
        this.form.capacity = null;
        this.form.batchNo = null;
      }
    },
    rowSave(row, done, loading) {
      add(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          if (this.flag) {
            this.flag = false;
            loading();
            this.form.batchNo = "";
            this.form.num = null;
            return;
          }
          done();
        },
        (error) => {
          loading();
          window.console.log(error);
        }
      );
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          console.log(error);
        }
      );
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    beforeOpen(done, type) {
      this.type = type;
      if ("add" === type) {
        this.form.shippingId = this.row.id;
      }
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then((res) => {
          this.form = res.data.data;
        });
      }
      done();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      params.descs = "id";
      const q = {
        shippingId: this.row.id,
        ...params,
        ...this.query,
      };
      getList(page.currentPage, page.pageSize, q).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        data.records.forEach((item) => {
          item.batchNoOptions = [];
          item.batchNoLoading = false;
        });
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
    batchNoRemoteMethod(query, row) {
      if (query !== "") {
        this.batchNoLoading = true;
        getBatchNos(1, 20, {
          batchNo: query,
          skuId: row.skuId,
        }).then((res) => {
          this.batchNoLoading = false;
          const data = res.data.data;
          data.records.forEach((item) => {
            item.label = `${item.batchNo}(${item.depotName})`;
          });
          this.batchNoOptions = data.records.filter((item) => {
            return item.batchNo.toLowerCase().indexOf(query.toLowerCase()) > -1;
          });
        });
      } else {
        this.batchNoOptions = [];
      }
    },
    async rowBatchNoChange(val, row) {
      if (this.batchNoOptions && this.batchNoOptions.length > 0) {
        const selectedItem = this.batchNoOptions.find(
          (item) => item.batchNo === val
        );
        if (selectedItem) {
          row.num = selectedItem.num;
          this.rowNumChange(row.num);
        }
      }
    },
    rowNumChange(value) {
      this.form.weight = Number(value) * Number(this.form.capacity);
    },
  },
};
</script>

<style scoped>
.container {
  margin: 0 15px;
}
</style>

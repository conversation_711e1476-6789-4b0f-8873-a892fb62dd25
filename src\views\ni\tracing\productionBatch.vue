<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :search.sync="query"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
      :cell-style="cellStyle"
    >
      <template #batchCode="{ row, index }">
        <span>{{ row.displayBatchCode }}</span>
        <span
          v-if="row.batchCode && row.displayBatchCode !== row.batchCode"
          style="color: #f56c6c"
        >
          ({{ row.batchCode }})
        </span>
      </template>
      <template #menuLeft>
        <el-dropdown @command="generateQrcode">
            <el-button size="mini" icon="el-icon-s-check" type="warning">
              生成二维码<i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="79">79号桶二维码</el-dropdown-item>
              <el-dropdown-item command="86">86号桶二维码</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        <!--设置二维码样式-->
        <el-button
          size="mini"
          icon="el-icon-setting"
          type="primary"
          plain
          v-if="
            permission.productionBatch_update_qr_code_style && area === 'OS'
          "
          @click="handleQrCodeStyleSetting"
          >设置二维码样式
        </el-button>
        <el-button
          size="mini"
          icon="el-icon-link"
          type="warning"
          plain
          v-if="area === 'OS'"
          @click="handleLinkBhjh"
          >关联备货计划
        </el-button>
        <el-divider direction="vertical"></el-divider>
        <el-radio-group
          v-model="area"
          size="mini"
          @input="onLoad(page)"
          style="margin-right: 20px"
        >
          <el-radio-button label="all">全部</el-radio-button>
          <el-radio-button label="CN">国内</el-radio-button>
          <el-radio-button label="OS">国外</el-radio-button>
        </el-radio-group>

        <el-checkbox v-model="allQrcode">导出全部</el-checkbox>

      </template>

      <template #menu="{ row, index, size }">
        <el-button
          type="text"
          icon="el-icon-setting"
          size="mini"
          plain
          style="border: 0; background-color: transparent !important"
          @click.stop="handleBox(row)"
        >
          箱体数据
        </el-button>
        <el-button
          type="text"
          size="mini"
          icon="el-icon-edit"
          plain
          v-if="permission.update_encode"
          style="border: 0; background-color: transparent !important"
          @click="updateEncode(row)"
          >编码调整
        </el-button>
      </template>
    </avue-crud>

    <el-drawer
      :title="`[${scopePropertyName}] 箱体`"
      :visible.sync="boxDrawerVisible"
      :direction="direction"
      append-to-body
      :before-close="handleBoxDrawerClose"
      size="70%"
    >
      <div>
        <box-info v-if="boxDrawerVisible" :batch-id="batchId" />
      </div>
    </el-drawer>
    <el-dialog
      title="选择模板类型"
      :visible.sync="printDialogVisible"
      @close="cancelPrint"
      class="print-type-dialog"
      append-to-body
    >
      <div>
        <el-radio-group v-model="selectedPrintType">
          <el-radio
            v-for="item in printTypes"
            :key="item.value"
            :label="item.value"
          >
            {{ item.label }}
          </el-radio>
        </el-radio-group>
      </div>
      <template #footer>
        <el-button @click="cancelPrint">取消</el-button>
        <el-button type="primary" @click="confirmPrint">确定</el-button>
      </template>
    </el-dialog>
    <el-dialog
      title="样式选择"
      :visible.sync="qrCodeStyle.visible"
      append-to-body
      width="350px"
    >
      <avue-form
        v-model="qrCodeStyle.form"
        :option="qrCodeStyle.option"
        @submit="handleQrCodeStyleSubmit"
      >
      </avue-form>
    </el-dialog>
    <sku-select-dialog
      ref="skuSelectDialogRef"
      multiple
      @confirm="handleSkuSelectChange"
      :params="{ status: 1 }"
    />
    <guo-wai-bei-huo-ji-hua-select-dialog
      ref="guoWaiBeiHuoJiHuaSelectRef"
      @confirm="handleLinkGwBhConfirm"
    />
  </basic-container>
</template>

<script>
import {
  addBatch,
  getDetail,
  getList,
  getReportDetail,
  linkGuoWaiBeiHuoJiHua,
  remove,
  update,
  updateEncode,
  updateQrCodeStyle,
} from "@/api/ni/tracing/productionBatch";
import option from "@/const/ni/tracing/productionBatch";
import BoxInfo from "./boxInfo.vue";
import { mapGetters } from "vuex";
import { dateFormat } from "@/util/date";
import { loadPrintTemplate } from "@/api/system/printTemplate";
import SkuSelectDialog from "@/views/ni/product/components/SkuSelectDialog.vue";
import { downloadXls } from "@/util/util";
import { exportBlob } from "@/api/common";
import { getToken } from "@/util/auth";
import GuoWaiBeiHuoJiHuaSelectDialog from "@/views/ni/old/components/GuoWaiBeiHuoJiHuaSelectDialog.vue";

export default {
  components: {
    GuoWaiBeiHuoJiHuaSelectDialog,
    BoxInfo,
    SkuSelectDialog,
  },
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 100,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: option,
      data: [],
      scopePropertyName: "批次",
      boxDrawerVisible: false,
      direction: "rtl",
      printTemplate: null,
      printTypes: [
        { label: "泰山品质", value: "TS" },
        { label: "非泰山品质", value: "FTS" },
        { label: "非泰山品质至简", value: "ZJ" },
      ],
      printDialogVisible: false,
      selectedPrintType: "TS",
      currentPrintRow: null,
      batchId: null,
      area: "all",
      qrCodeStyle: {
        visible: false,
        form: {},
        option: {
          span: 24,
          size: "mini",
          emptyBtn: false,
          column: [
            {
              label: "样式",
              prop: "qrCodeStyle",
              type: "select",
              dicUrl:
                "/api/blade-system/dict-biz/dictionary?code=ni_tracing_qr_code_style",
              props: {
                label: "dictValue",
                value: "dictKey",
              },
              filterable: true,
              placeholder: " ",
              rules: [
                {
                  required: true,
                  message: "请选择样式",
                  trigger: "blur",
                },
              ],
            },
          ],
        },
      },
      allQrcode: false,
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.productionBatch_add, false),
        viewBtn: this.vaildData(this.permission.productionBatch_view, false),
        delBtn: this.vaildData(this.permission.productionBatch_delete, false),
        editBtn: this.vaildData(this.permission.productionBatch_edit, false),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  created() {
    loadPrintTemplate("ni_tracing_production_batch").then((res) => {
      this.printTemplate = JSON.parse(res.data.data.content);
    });
  },
  methods: {
    handleLinkBhjh() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$refs.guoWaiBeiHuoJiHuaSelectRef.onSelect();
    },
    handleLinkGwBhConfirm(selectionList) {
      linkGuoWaiBeiHuoJiHua(this.ids, selectionList[0].id).then(() => {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!",
        });
        this.$refs.crud.toggleSelection();
      });
    },
    handleQrCodeStyleSetting() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.qrCodeStyle.visible = true;
    },
    handleQrCodeStyleSubmit(form, done) {
      updateQrCodeStyle(this.ids, form.qrCodeStyle)
        .then((res) => {
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.onLoad(this.page);
          this.qrCodeStyle.visible = false;
        })
        .finally(() => {
          done();
        });
    },
    handleBox(row) {
      this.scopePropertyName = row.batchCode;
      this.batchId = row.id;
      this.boxDrawerVisible = true;
    },
    handleBoxDrawerClose(hide) {
      hide();
    },
    //“铁桶”批量新增批次
    rowSave(row, done, loading) {
      if (row.date) {
        row.date = dateFormat(new Date(row.date), "yyyy-MM-dd");
      }
      if (row.batchEndTime) {
        row.batchEndTime = dateFormat(new Date(row.batchEndTime), "yyyy-MM-dd hh:mm:ss");
      }
      addBatch(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          window.console.log(error);
        }
      );
    },
    //生成二维码
    generateQrcode(command) {
      if (!this.allQrcode && this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      const invalidItems = this.selectionList.filter(
        (row) => row.inspectionId === null
      );
      if (invalidItems.length > 0) {
        this.$confirm("当前数据存在未关联质检报告的数据，是否继续生成?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(() => {
          exportBlob(
            `/api/ni/tracing/production-batch/generateQrcode?${
              this.website.tokenHeader
            }=${getToken()}`,
            { ids: this.ids,command: command,allQrcode: this.allQrcode}
          ).then((res) => {
            if(command == '86'){
              downloadXls(res.data, "86号铁桶二维码.xlsx");
            }else if (command == '79'){
              downloadXls(res.data, "79号铁桶二维码.xlsx");
            }

          });
        });
      } else {
        exportBlob(
          `/api/ni/tracing/production-batch/generateQrcode?${
            this.website.tokenHeader
          }=${getToken()}`,
          { ids: this.ids,command: command,allQrcode: this.allQrcode}
        ).then((res) => {
          if(command == '86'){
              downloadXls(res.data, "86号铁桶二维码.xlsx");
            }else if (command == '79'){
              downloadXls(res.data, "79号铁桶二维码.xlsx");
            }
        });
      }
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          console.log(error);
        }
      );
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen(done, type) {
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then((res) => {
          this.form = res.data.data;
        });
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.area = "all";
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      if (params.code) {
        params.inspectionId = 1;
      }
      this.loading = true;
      let para = Object.assign(params, this.query);
      para.area = this.area === "all" ? null : this.area;
      getList(page.currentPage, page.pageSize, para).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        data.records.forEach((item) => {
          if (item.inspectionId == null) {
            item.inspectionCode = "未关联";
          } else {
            item.inspectionCode = item.inspectionCode
              ? item.inspectionCode
              : "无编号";
          }
        });
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
    cellStyle({ row, column }) {
      if ("batchCode" === column.columnKey && row.isInout) {
        return {
          backgroundColor: "#909399",
          color: "#fff",
        };
      }
      if ("inspectionCode" === column.columnKey && row.publishStatus === "0") {
        return {
          backgroundColor: "#F56C6C",
          color: "#fff",
        };
      } else if ("inspectionCode" === column.columnKey) {
        return {
          backgroundColor: "#67C23A",
          color: "#fff",
        };
      }
    },
    //打印接口
    async handlePrint(row) {
      if (!this.printTemplate) {
        this.$message.error("打印模板加载失败，请联系管理员");
        return;
      }
      console.log(row);
      if (row.inspectionId == null) {
        this.$message.error("当前批次未关联质检报告");
        return;
      }
      this.currentPrintRow = row;
      this.printDialogVisible = true;
    },
    //确认打印
    async confirmPrint() {
      try {
        const res = await getReportDetail(
          this.currentPrintRow.id,
          this.selectedPrintType
        );
        const printData = res.data.data;
        if (!printData) {
          this.$message.error("获取打印数据失败");
          return;
        }
        const hiprintTemplate = new hiprint.PrintTemplate({
          template: this.printTemplate,
        });
        hiprintTemplate.print(printData);
        this.printDialogVisible = false;
      } catch (error) {
        console.error("打印失败:", error);
        this.$message.error("获取报告数据失败，请重试");
        this.printDialogVisible = false;
      }
    },

    //取消打印
    cancelPrint() {
      this.printDialogVisible = false;
    },

    updateEncode(row) {
      this.handleSkuSelect();
      this.batchId = row.id;
    },

    handleSkuSelect() {
      this.$refs.skuSelectDialogRef.onShow();
    },

    handleSkuSelectChange(selectionList) {
      if (selectionList.length !== 1) {
        this.$message.error("选择数据失败，请重试");
      } else {
        const selectedSku = selectionList[0];
        const productionBatch = {
          batchId: this.batchId,
          innerPackagingId: selectedSku.innerPackageId,
          innerPackagingName: selectedSku.innerPackageText,
          outerPackagingId: selectedSku.packageId,
          outerPackagingName: selectedSku.packageText,
          specificationCode: selectedSku.specification,
          specificationName: selectedSku.specText,
          qualityCode: selectedSku.qualityLevel,
        };
        updateEncode(productionBatch).then(
          () => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!",
            });
          },
          (error) => {
            loading();
            console.log(error);
          }
        );
        this.$refs.skuSelectDialogRef.hide();
      }
    },
  },
};
</script>

<style>
.print-type-dialog .el-dialog {
  width: 600px !important;
}

.print-type-dialog .el-dialog__body {
  max-width: 100%;
  padding: 20px;
}
</style>

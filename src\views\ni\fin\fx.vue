<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
      :cell-style="cellStyle"
    >
      <template #serialNo="{ row, index }">
        <flow-timeline-popover
          v-if="row.processInsId"
          :process-ins-id="row.processInsId"
          :process-def-key="processDefKey"
          v-model="row.serialNo"
          :flow.sync="row.flow"
          trigger="click"
          lazy
        />
        <span v-else>{{ row.serialNo }}</span>
      </template>
      <template #sourceLedger="{ row, index }">
        <span>{{ row.sourceLedger }}/{{ row.sourceCurrencyText }}</span>
      </template>
      <template #targetLedger="{ row, index }">
        <span>{{ row.targetLedger }}/{{ row.targetCurrencyText }}</span>
      </template>
      <template #sourceAmount="{ row, index }">
        <span>
          {{
            Number(row.sourceAmount).toLocaleString("zh-CN", {
              minimumFractionDigits: 2,
            })
          }}
        </span>
      </template>
      <template #targetAmount="{ row, index }">
        <span>
          {{
            Number(row.targetAmount).toLocaleString("zh-CN", {
              minimumFractionDigits: 2,
            })
          }}
        </span>
      </template>
      <template #bookRate="{ row, index }">
        <span>
          {{
            Number(row.bookRate).toLocaleString("zh-CN", {
              minimumFractionDigits: 2,
            })
          }}
        </span>
      </template>
      <template #bankFee="{ row, index }">
        <span>
          {{
            Number(row.bankFee).toLocaleString("zh-CN", {
              minimumFractionDigits: 2,
            })
          }}
        </span>
      </template>
      <template #fxGainLoss="{ row, index }">
        <span>
          {{
            Number(row.fxGainLoss).toLocaleString("zh-CN", {
              minimumFractionDigits: 2,
            })
          }}
        </span>
      </template>
      <template #menuLeft>
        <el-button
          type="primary"
          size="mini"
          icon="el-icon-s-promotion"
          v-if="permission.finFx_add"
          @click="handleApply"
          >发起申请
        </el-button>
        <el-button
          type="info"
          size="mini"
          icon="el-icon-download"
          plain
          v-if="permission.finFx_export"
          @click="handleExport"
          >导出
        </el-button>
        <el-button
          type="warning"
          size="mini"
          icon="el-icon-setting"
          @click="handleSetting"
          >会计科目设置
        </el-button>
      </template>
      <template #menu="{ row, index }">
        <el-button
          type="text"
          size="mini"
          icon="el-icon-s-check"
          v-if="
            permission.finFx_post &&
            row.status === 9 &&
            !row.red &&
            row.postStatus === 0
          "
          @click="rowPost(row)"
          >确认执行
        </el-button>
        <el-button
          type="text"
          size="mini"
          icon="el-icon-delete"
          style="color: #f56c6c"
          v-if="permission.finFx_delete && [0, 4, 6].includes(row.status)"
          @click="rowDel(row)"
          >删除
        </el-button>
        <el-button
          type="text"
          size="mini"
          icon="el-icon-s-release"
          style="color: #f56c6c"
          v-if="
            permission.finFx_void &&
            row.status === 9 &&
            !row.red &&
            row.postStatus !== 9 &&
            row.postStatus !== 2
          "
          @click="rowVoid(row)"
          >作废
        </el-button>
        <el-button
          type="text"
          size="mini"
          icon="el-icon-refresh-left"
          style="color: #e6a23c"
          v-if="
            permission.finFx_red &&
            row.status === 9 &&
            !row.red &&
            row.postStatus === 1
          "
          @click="rowRed(row)"
          >冲销
        </el-button>
      </template>
    </avue-crud>
    <el-dialog
      title="会计科目设置"
      append-to-body
      :visible.sync="setting.visible"
      width="555px"
    >
      <el-tabs v-model="setting.brand" @tab-click="handleSettingBrandClick">
        <el-tab-pane
          v-for="(item, index) in brandDict"
          :key="index"
          :label="item.dictValue"
          :name="item.dictKey"
        ></el-tab-pane>
      </el-tabs>
      <avue-form
        :loading="setting.loading"
        :option="setting.option"
        v-model="setting.form"
        @submit="handleSettingSubmit"
      >
      </avue-form>
    </el-dialog>
  </basic-container>
</template>

<script>
import {
  getAccountingSettings,
  getDetail,
  getList,
  post,
  red,
  remove,
  saveAccountingSettings,
  toVoid,
} from "@/api/ni/fin/finFx";
import { mapGetters } from "vuex";
import exForm from "@/views/plugin/workflow/mixins/ex-form";
import FlowTimelinePopover from "@/components/wf-flow-timeline/popover.vue";
import request from "@/router/axios";

export default {
  components: { FlowTimelinePopover },
  mixins: [exForm],
  data() {
    return {
      processDefKey: "process_fin_fx_apply",
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        menuWidth: 150,
        editBtn: false,
        delBtn: false,
        searchEnter: true,
        addBtn: false,
        labelWidth: 110,
        searchLabelWidth: 110,
        align: "center",
        span: 8,
        searchIndex: 3,
        searchIcon: true,
        size: "mini",
        searchSize: "mini",
        height: "auto",
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: false,
        selection: true,
        viewBtn: false,
        column: [
          {
            label: "申请日期",
            prop: "opDate",
            type: "date",
            search: true,
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            searchRange: true,
            placeholder: " ",
            clearable: false,
            minWidth: 120,
          },
          {
            label: "流水号",
            prop: "serialNo",
            minWidth: 150,
            overHidden: true,
          },
          {
            label: "摘要",
            prop: "title",
            minWidth: 100,
            overHidden: true,
          },
          {
            label: "账套",
            prop: "brandText",
            width: 80,
          },
          {
            label: "账套",
            prop: "brand",
            type: "radio",
            dicUrl: "/api/blade-system/dict-biz/dictionary?code=ni_brand",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            search: true,
            hide: true,
            showColumn: false,
            rules: [
              {
                required: true,
                message: "请选择账套",
                trigger: "blur",
              },
            ],
          },
          {
            label: "执行状态",
            prop: "postStatusText",
            width: 80,
          },
          {
            label: "审批状态",
            prop: "status",
            dicUrl: "/api/blade-system/dict/dictionary?code=data_status",
            search: true,
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            dataType: "number",
            type: "select",
            hide: true,
            showColumn: false,
            width: 80,
          },
          {
            label: "执行状态",
            prop: "postStatus",
            dicUrl: "/api/blade-system/dict-biz/dictionary?code=ni_post_status",
            search: true,
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            dataType: "number",
            type: "select",
            hide: true,
            showColumn: false,
            width: 80,
          },
          {
            label: "转出账户",
            prop: "sourceLedger",
            overHidden: true,
            width: 150,
          },
          {
            label: "转出账户",
            prop: "sourceLedgerId",
            placeholder: " ",
            type: "select",
            dicUrl: "/api/ni/fin/ledger/list?status=2",
            overHidden: true,
            props: {
              label: "dictLabel",
              value: "id",
              desc: "currencyName",
            },
            hide: true,
            showColumn: false,
            search: true,
            rules: [
              {
                required: true,
                message: "请选择转出账户",
                trigger: "blur",
              },
            ],
          },
          {
            label: "转出金额",
            prop: "sourceAmount",
            type: "input",
            overHidden: true,
            width: 100,
          },
          {
            label: "转入账户",
            prop: "targetLedger",
            type: "input",
            overHidden: true,
            width: 150,
          },
          {
            label: "转入账户",
            prop: "targetLedgerId",
            placeholder: " ",
            type: "select",
            dicUrl: "/api/ni/fin/ledger/list?status=2",
            overHidden: true,
            props: {
              label: "dictLabel",
              value: "id",
              desc: "currencyName",
            },
            hide: true,
            showColumn: false,
            search: true,
            rules: [
              {
                required: true,
                message: "请选择转出账户",
                trigger: "blur",
              },
            ],
          },
          {
            label: "转入金额",
            prop: "targetAmount",
            type: "number",
            precision: 2,
            overHidden: true,
            width: 100,
          },
          {
            label: "执行汇率",
            prop: "execRate",
            type: "number",
            overHidden: true,
            width: 80,
          },
          {
            label: "账面汇率",
            prop: "bookRate",
            type: "input",
            hide: true,
          },
          {
            label: "手续费",
            prop: "bankFee",
            type: "input",
            hide: true,
            width: 80,
          },
          {
            label: "手续费扣减账户",
            prop: "feeLedgerId",
            placeholder: " ",
            type: "select",
            dicUrl: "/api/ni/fin/ledger/list?status=2&brand={{key}}",
            overHidden: true,
            props: {
              label: "dictLabel",
              value: "id",
              desc: "currencyName",
            },
            hide: true,
          },
          {
            label: "手续费科目",
            prop: "feeAccountingId",
            type: "tree",
            parent: false,
            clearable: true,
            dicUrl: "/api/ni/fin/accounting/submitTree",
            props: {
              label: "name",
              value: "id",
              desc: "code",
            },
            hide: true,
            placeholder: " ",
            typeformat(item) {
              return `${item.code}-${item.name}`;
            },
          },
          {
            label: "汇兑损益",
            prop: "fxGainLoss",
            type: "number",
            precision: 2,
            overHidden: true,
            width: 80,
          },
          {
            label: "申请人",
            prop: "createUserName",
            width: 80,
            overHidden: true,
          },
          {
            label: "审批状态",
            prop: "statusText",
            width: 80,
          },
        ],
      },
      data: [],
      exportColumn: [
        {
          label: "申请日期",
          prop: "opDate",
        },
        {
          label: "流水号",
          prop: "serialNo",
        },
        {
          label: "摘要",
          prop: "title",
        },
        {
          label: "账套",
          prop: "brandText",
        },
        {
          label: "执行状态",
          prop: "postStatusText",
        },
        {
          label: "转出账户",
          prop: "sourceLedger",
          overHidden: true,
          width: 150,
        },
        {
          label: "转出金额",
          prop: "sourceAmount",
          type: "input",
          overHidden: true,
          width: 100,
        },
        {
          label: "转入账户",
          prop: "targetLedger",
          type: "input",
          overHidden: true,
          width: 150,
        },
        {
          label: "转入金额",
          prop: "targetAmount",
          type: "number",
          precision: 2,
          overHidden: true,
          width: 100,
        },
        {
          label: "执行汇率",
          prop: "execRate",
        },
        {
          label: "账面汇率",
          prop: "bookRate",
        },
        {
          label: "手续费",
          prop: "bankFee",
        },
        {
          label: "汇兑损益",
          prop: "fxGainLoss",
        },
        {
          label: "申请人",
          prop: "createUserName",
        },
        {
          label: "审批状态",
          prop: "statusText",
        },
      ],
      brandDict: [],
      setting: {
        loading: false,
        visible: false,
        brand: "1",
        option: {
          span: 24,
          labelWidth: 150,
          size: "mini",
          searchSize: "mini",
          emptyBtn: false,
          column: [
            {
              label: "汇兑损益编码",
              prop: "gainLossAccountingId",
              type: "tree",
              remote: true,
              dicData: [],
              props: {
                label: "title",
                value: "id",
                desc: "code",
              },
              parent: false,
              placeholder: " ",
              rules: [
                {
                  required: true,
                  message: "请选择汇兑损益编码",
                  trigger: "blur",
                },
              ],
            },
            {
              label: "手续费",
              prop: "fee",
              type: "dynamic",
              labelPosition: "top",
              span: 24,
              children: {
                align: "center",
                headerAlign: "center",
                column: [
                  {
                    label: "币种",
                    prop: "currency",
                    type: "select",
                    dicUrl: "/api/blade-system/dict/dictionary?code=currency",
                    props: {
                      label: "dictValue",
                      value: "dictKey",
                    },
                    parent: false,
                    placeholder: " ",
                    rules: [
                      {
                        required: true,
                        message: "请选择币种",
                        trigger: "blur",
                      },
                    ],
                  },
                  {
                    label: "会计科目",
                    prop: "accountingId",
                    type: "tree",
                    remote: true,
                    dicData: [],
                    props: {
                      label: "title",
                      value: "id",
                      desc: "code",
                    },
                    placeholder: " ",
                    rules: [
                      {
                        required: true,
                        message: "请选择会计科目",
                        trigger: "blur",
                      },
                    ],
                  },
                ],
              },
            },
          ],
        },
        form: {},
      },
    };
  },
  created() {
    request({
      url: "/api/blade-system/dict-biz/dictionary?code=ni_brand",
      method: "get",
    }).then((res) => {
      this.brandDict = res.data.data;
    });
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.finFx_add, false),
        viewBtn: this.vaildData(this.permission.finFx_view, false),
        delBtn: this.vaildData(this.permission.finFx_delete, false),
        editBtn: this.vaildData(this.permission.finFx_edit, false),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  methods: {
    handleSetting() {
      this.setting.visible = true;
      this.handleSettingBrandClick({ name: "1" });
    },
    handleSettingBrandClick(tab) {
      this.$http
        .get("/api/ni/fin/accounting/submitTree", {
          params: {
            brand: tab.name,
          },
        })
        .then((res) => {
          const gainLossAccountingId = this.findObject(
            this.setting.option.column,
            "gainLossAccountingId"
          );
          gainLossAccountingId.dicData = res.data.data;
          const fee = this.findObject(this.setting.option.column, "fee");
          if (fee) {
            const accountingIdColumn = fee.children.column.find(
              (item) => item.prop === "accountingId"
            );
            if (accountingIdColumn) accountingIdColumn.dicData = res.data.data;
          }
        });
      this.setting.form = {
        gainLossAccountingId: "",
        fee: [],
      };

      getAccountingSettings(tab.name).then((res) => {
        if (
          res.data &&
          res.data.code === 200 &&
          res.data.data &&
          typeof res.data.data === "object"
        ) {
          this.setting.form = Object.assign(
            {},
            this.setting.form,
            res.data.data
          );
          if (res.data.data.fee && Array.isArray(res.data.data.fee)) {
            this.setting.form.fee = [...res.data.data.fee];
          }
        }
      });
    },
    handleSettingSubmit(form, done) {
      saveAccountingSettings(this.setting.brand, form)
        .then(() => {
          this.$message({
            type: "success",
            message: "保存成功!",
          });
          this.setting.visible = false;
        })
        .finally(() => {
          done();
        });
    },
    rowPost(row) {
      this.$confirm(
        `您确定要将该单据<span style="color: #0d84ff"> ${row.serialNo} </span>的执行状态改为已执行？此操作将不可撤销。`,
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
          dangerouslyUseHTMLString: true,
        }
      )
        .then(() => {
          return post(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    rowVoid(row, index) {
      this.$confirm(
        `您确定要作废单据<span style="color: #0d84ff"> ${row.serialNo} </span>吗？此操作将不可撤销。`,
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
          dangerouslyUseHTMLString: true,
        }
      )
        .then(() => {
          return toVoid(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    rowRed(row, index) {
      this.$confirm(
        `您确定要冲销单据<span style="color: #0d84ff"> ${row.serialNo} </span>吗？此操作将生成一张方向相反的红字凭证，且不可撤销。`,
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
          dangerouslyUseHTMLString: true,
        }
      )
        .then(() => {
          return red(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    async handleExport() {
      let msg =
        "确定将<span style='font-weight: bolder;color: #F56C6C'>选择数据</span>导出?";
      if (this.selectionList.length === 0) {
        msg =
          "确定要将<span style='font-weight: bolder;color: #F56C6C'>全部数据</span>导出?";
      }
      try {
        await this.$alert(msg, {
          dangerouslyUseHTMLString: true,
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        });
        const data = [];
        if (this.selectionList.length > 0) {
          this.selectionList.forEach((item) => {
            data.push({
              ...item,
            });
          });
        } else {
          const q = {
            ...this.query,
          };
          if (q.opDate != null && q.opDate.length === 2) {
            q.startOpDate = q.opDate[0];
            q.endOpDate = q.opDate[1];
            q.opDate = null;
          }
          const res = await getList(1, 10000, q);
          const data1 = res.data.data.records;
          data1.forEach((item) => {
            data.push({
              ...item,
            });
          });
        }
        await this.$Export.excel({
          title: "资金结汇单",
          columns: this.exportColumn,
          data,
        });
      } catch (e) {
        console.log(e);
      }
    },
    handleApply() {
      this.dynamicRoute(
        {
          processDefKey: this.processDefKey,
        },
        "start"
      );
    },
    rowDel(row) {
      this.$confirm(
        `您确定要删除单据<span style="color: #0d84ff"> ${row.serialNo} </span>吗？此操作将不可撤销。`,
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
          dangerouslyUseHTMLString: true,
        }
      )
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    beforeOpen(done, type) {
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then((res) => {
          this.form = res.data.data;
        });
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      const q = Object.assign(params, this.query);
      if (q.opDate != null && q.opDate.length === 2) {
        q.startOpDate = q.opDate[0];
        q.endOpDate = q.opDate[1];
        q.opDate = null;
      }
      getList(page.currentPage, page.pageSize, q).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        data.records.forEach((item) => {
          item.postStatus = item.postStatus != null ? item.postStatus : 0;
        });
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
    cellStyle({ row, column }) {
      if ("postStatusText" === column.columnKey) {
        return this.postStatusStyle(row.postStatus);
      }
      if ("statusText" === column.columnKey) {
        return this.statusStyle(row.status);
      }
      if ("brandText" === column.columnKey) {
        return this.brandStyle(row.brand);
      }
      if ("fxGainLoss" === column.columnKey && row.fxGainLoss < 0) {
        return {
          color: "#F56C6C",
          fontWeight: "bold",
        };
      }
    },
    postStatusStyle(postStatus) {
      if (postStatus === 0) {
        return {
          backgroundColor: "#909399",
          color: "#fff",
        };
      } else if (postStatus === 1) {
        return {
          backgroundColor: "#67C23A",
          color: "#fff",
        };
      } else if (postStatus === 2) {
        return {
          backgroundColor: "#F56C6C",
          color: "#fff",
        };
      } else if (postStatus === 9) {
        return {
          backgroundColor: "#E6A23C",
          color: "#fff",
        };
      }
    },
    brandStyle(brand) {
      if (brand === "1") {
        return {
          background: "#167C46",
          color: "#fff",
        };
      } else if (brand === "2") {
        return {
          background: "#06c15c",
          color: "#fff",
        };
      } else if (brand === "4") {
        return {
          background: "#056487",
          color: "#fff",
        };
      } else if (brand === "5") {
        return {
          background: "#ae800c",
          color: "#fff",
        };
      }
      return {
        background: "#167C46",
        color: "#fff",
      };
    },
    statusStyle(status) {
      if (status === 0) {
        return {
          backgroundColor: "#909399",
          color: "#fff",
        };
      } else if (status === 1) {
        return {
          backgroundColor: "#409EFF",
        };
      } else if (status === 2) {
        return {
          backgroundColor: "#E6A23C",
          color: "#fff",
        };
      } else if (status === 3) {
        return {
          backgroundColor: "#F56C6C",
        };
      } else if (status === 6) {
        return {
          backgroundColor: " #F56C6C",
          color: "#fff",
        };
      } else if ([4, 5].includes(status)) {
        return {
          backgroundColor: "#E6A23C",
        };
      } else if (status === 9) {
        return {
          backgroundColor: "#67C23A",
          color: "#fff",
        };
      } else {
        return {
          backgroundColor: "#E6A23C",
        };
      }
    },
  },
};
</script>

<style></style>

<template>
  <basic-container>
    <el-container>
      <el-aside class="aside_main" :class="{ aside_main_show: !asideStatus }">
        <div class="box">
          <el-scrollbar style="height: 100%">
            <basic-container>
              <!-- <el-divider></el-divider> -->
              <avue-tree
                ref="typeTree"
                :option="treeOption"
                :data="treeData"
                @node-click="nodeClick"
                @getNode="getNode"
              />
            </basic-container>
          </el-scrollbar>
        </div>
        <!-- 左侧按钮 -->
        <div
          class="aside_open_close"
          @click="asidechange"
          :style="{ left: asideStatus ? '350px' : '0px' }"
        >
          <i class="el-icon-arrow-left" v-if="aside_open_close"></i>
          <i class="el-icon-arrow-right" v-else></i>
        </div>
      </el-aside>

      <el-main style="margin-left: 10px" class="main_cont">
        <!-- 左侧按钮 -->
        <!-- <div class="aside_open_close" @click="asidechange">
          <i class="el-icon-arrow-left" v-if="aside_open_close"></i>
          <i class="el-icon-arrow-right" v-else></i>
        </div> -->
        <template>
          <avue-crud
            ref="crud"
            :option="option"
            :table-loading="loading"
            :data="data"
            :page.sync="page"
            :permission="permissionList"
            v-model="form"
            @row-update="rowUpdate"
            @row-save="rowSave"
            @row-del="rowDel"
            @search-change="searchChange"
            @search-reset="searchReset"
            @selection-change="selectionChange"
            @current-change="currentChange"
            @size-change="sizeChange"
            @on-load="onLoad"
            @refresh-change="onLoad(page, query)"
            :row-style="rowStyle"
            @row-dblclick="handleRowDBLClick"
            :before-open="beforeOpen"
          >
            <!-- 访问控制 -->
            <template #fileAccess="{ row }">
              <el-button
                size="mini"
                @click="$refs.crud.rowView(row, index)"
                type="text"
              >
                {{ row.$fileAccess }}
              </el-button>
            </template>
            <!-- 显示文件大小 -->
            <template #fileSize="{ row, index }">
              <span v-if="row.isFile">{{ fileSizeFormat(row.fileSize) }}</span>
              <span v-else></span>
            </template>
            <!-- 根据文件类型显示图标 -->
            <template #fileName="{ row, index }">
              <span v-if="!row.isFile">
                <i
                  class="el-icon-folder"
                  style="color: darkorange; font-size: 24px"
                ></i>
                {{ row.fileName }}
              </span>
              <span v-else-if="fileTypeFormat(row.fileType) == 'document'">
                <i
                  class="el-icon-document"
                  style="color: #a9a9a9; font-size: 18px"
                ></i>
                {{ row.fileName }}
              </span>
              <span v-else-if="fileTypeFormat(row.fileType) == 'img'">
                <i
                  class="el-icon-picture-outline"
                  style="color: #a9a9a9; font-size: 18px"
                ></i>
                {{ row.fileName }}
              </span>
              <span v-else-if="fileTypeFormat(row.fileType) == 'video'">
                <i
                  class="el-icon-video-playr"
                  style="color: #a9a9a9; font-size: 18px"
                ></i>
                {{ row.fileName }}
              </span>
              <span v-else-if="fileTypeFormat(row.fileType) == 'music'">
                <i
                  class="el-icon-headset"
                  style="color: #a9a9a9; font-size: 18px"
                ></i>
                {{ row.fileName }}
              </span>
              <span v-else-if="fileTypeFormat(row.fileType) == 'con'">
                <i
                  class="el-icon-collection"
                  style="color: #a9a9a9; font-size: 18px"
                ></i>
                {{ row.fileName }}
              </span>
              <span v-else>{{ row.fileName }}</span>
            </template>
            <template #fileType="{ row }">
              <span v-if="!row.isFile">文件夹</span>
              <span v-else>{{ row.fileType }}</span>
            </template>
            <template slot="fileUploadForm" slot-scope="{ row, size }">
              <el-upload
                class="upload-demo"
                drag
                action="/api/blade-resource/oss/endpoint/put-file?code=public"
                :headers="header"
                multiple
                :disabled="disabled"
                :limit="5"
                :file-list="fileList"
                :on-change="handleOnChangeFile"
                :on-exceed="handleExceedFile"
                :on-error="handleErrorFile"
                :before-upload="handleBeforeUpload"
              >
                <i class="el-icon-upload"></i>
                <div class="el-upload__text">
                  将文件拖到此处，或<em>点击上传</em>
                </div>
                <div class="el-upload__tip" slot="tip">
                  单次最多上传五个文件，且文件最大不超过1GB
                </div>
              </el-upload>
            </template>
            <template #menuLeft>
              <el-button
                type="warning"
                size="mini"
                icon="el-icon-folder-add"
                plain
                v-if="permission.file_build"
                @click="handleBuildFile"
                >创建文件夹</el-button
              >

              <!-- 面包屑（动态添加和删除） -->
              <div class="breadcrumbClass">
                <el-breadcrumb separator-class="el-icon-arrow-right">
                  <el-breadcrumb-item>
                    <span
                      class="breadcrumbItemStyle"
                      v-for="(item, index) in stack"
                      :key="index"
                      @click="handleBreadcrumbClick(item)"
                    >
                      {{ item.text }}
                    </span></el-breadcrumb-item
                  >
                </el-breadcrumb>
                <el-button class="breadcrumbBtn" @click="returnBack" size="mini"
                  >返回上一级</el-button
                >
              </div>
            </template>
            <template slot="menu" slot-scope="{ row }">
              <el-button
                type="text"
                icon="el-icon-edit"
                :size="size"
                v-if="
                  permissionList.editBtn ||
                  userInfo.user_id === row.createUser ||
                  userInfo.role_name.includes('admin')
                "
                @click="$refs.crud.rowEdit(row)"
                >编辑
              </el-button>
              <el-button
                type="text"
                icon="el-icon-delete"
                size="mini"
                plain
                v-if="
                  permissionList.delBtn ||
                  userInfo.user_id === row.createUser ||
                  userInfo.role_name.includes('admin')
                "
                @click="$refs.crud.rowDel(row, index)"
                >删除
              </el-button>
              <el-button
                v-if="row.isFile"
                type="text"
                size="small"
                icon="el-icon-view"
                @click="handlePreview(row)"
                >预览</el-button
              >
              <el-button
                v-if="row.isFile"
                type="text"
                size="small"
                icon="el-icon-download"
                @click="handleDownload(row.fileUrl, row.fileName)"
                >下载</el-button
              >
              <!-- <el-button
                v-if="!row.isFile"
                type="text"
                size="small"
                icon="el-icon-upload"
                @click="handleAdd"
              >
                上传</el-button
              >
              <el-button
                v-if="!row.isFile"
                type="text"
                size="small"
                icon="el-icon-upload"
                @click="handleAddChildFile"
              >
                创建文件夹</el-button
              > -->
            </template>
          </avue-crud>
        </template>
      </el-main>
    </el-container>
    <build-file-dialog
      ref="buildFileDialog"
      :parentId="this.parentId"
      @submit="handleBuildFileSubmit"
    />
    <file-scope-dialog
      ref="fileScopeDialog"
      :user-option="userOption"
      all
      @submit="handleScopeSubmit"
    />
  </basic-container>
</template>

<script>
import {
  getDetail,
  getList,
  add,
  getParentIdTree,
  getParentId,
  remove,
  update,
} from "@/api/resource/fileManagement";
import { mapGetters } from "vuex";
import BuildFileDialog from "@/views/resource/components/buildFileDialog";
import { getAuthorizationHeader } from "@/api/resource/fileManagement";
import FileScopeDialog from "@/views/resource/components/fileScopeDialog";
import UserOption from "@/views/plugin/workflow/process/components/user-option.vue";
import { Base64 } from "js-base64";

export default {
  components: {
    BuildFileDialog,
    FileScopeDialog,
    UserOption,
  },
  data() {
    return {
      asideStatus: true, //控制左侧菜单栏显隐
      aside_open_close: true, //控制左侧菜单栏显隐
 //面包屑的堆栈
      stack: [
        { text: "首页", to: { path: "/" } },
        { text: "文件管理", to: { path: "/resource/file" }, id: 0 },
      ], //用于存放父id的堆栈
      stackCopy: [
        { text: "首页", to: { path: "/" } },
        { text: "文件管理", to: { path: "/resource/file" }, id: 0 },
      ],
      treeOption: {
        filter: false,
        size: "mini",
        searchSize: "mini",
        addBtn: false,
        editBtn: false,
        delBtn: false,
        lazy: true,
        treeLoad: function (node, resolve) {
          const parentId = node.level === 0 ? 0 : node.data.id;
          getParentIdTree(parentId).then((res) => {
            resolve(
              res.data.data.map((item) => {
                return {
                  ...item,
                  label: `${item.fileName}`,
                  value: item.id,
                  leaf: !item.hasChildren,
                };
              })
            );
          });
        },
      },
      addFileName: "",
      fileId: null, //文件id
      isFileUpload: false, //判断文件是否上传
      isFile: false, //判断是文件还是文件夹
      scopeVisible: false,
      fileList: [],
      header: getAuthorizationHeader(),
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 50,
        currentPage: 1,
        total: 0,
      },
      parentId: 0,
      selectionList: [],
      option: {
        rowKey: "id",
        rowParentKey: "parentId",
        size: "mini",
        height: "auto",
        calcHeight: 30,
        tip: false,
        border: false,
        selection: false,
        addBtnText: "文件上传",
        addBtnIcon: "el-icon-upload",
        addBtn: true,
        editBtn: false,
        delBtn: false,
        align: "center",
        searchMenuSpan: 6,
        searchSize: "mini",
        searchIcon: true,
        searchIndex: 3,
        searchShow: true,
        searchSpan: 6,
        column: [
          {
            label: "上级文件夹",
            prop: "parentId",
            dicData: [],
            type: "select",
            hide: true,
            span: 24,
            viewdisplay: false,
            addDisplay: true,
            editDisplay: true,
            props: {
              label: "fileName",
              value: "id",
            },
          },
          {
            label: "文件名",
            prop: "fileName",
            search: true,
            overHidden: true,
            viewdisplay: true,
            addDisplay: false,
            editDisplay: true,
            sortable: true,
          },
          {
            label: "文件类型",
            prop: "fileType",
            display: false,
            viewDisplay: true,
            search: true,
            sortable: true,
          },
          {
            label: "文件摘要",
            prop: "fileAbstract",
            type: "textarea",
            minRows: 3,
            maxRows: 5,
            maxlength: 100,
            span: 24,
            showWordLimit: true,
            search: true,
            overHidden: true,
          },
          {
            label: "文件大小",
            prop: "fileSize",
            viewdisplay: true,
            addDisplay: false,
            editDisplay: false,
            sortable: true,
          },
          {
            label: "创建者",
            prop: "createUser",
            display: false,
            dicUrl: `/api/blade-user/user-list`,
            search: true,
            sortable: true,
            type: "tree",
            props: {
              label: "name",
              value: "id",
            },
          },
          {
            label: "部门",
            prop: "createDept",
            display: false,
            dicUrl: `/api/blade-system/dept/list`,
            search: true,
            sortable: true,
            type: "tree",
            props: {
              label: "deptName",
              value: "id",
            },
          },
          {
            label: "创建日期",
            prop: "createTimeRange",
            type: "datetimerange",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
            startPlaceholder: "日期开始范围",
            endPlaceholder: "日期结束范围",
            search: true,
            labelWidth: 110,
            searchRange: true,
            hide: true,
            display: false,
          },
          {
            label: "创建日期",
            prop: "createTime",
            display: false,
            sortable: true,
            width: 130,
            overHidden: true,
          },
          {
            label: "文件上传",
            prop: "fileUpload",
            viewDisplay: false,
            editDisplay: false,
            hide: true,
            span: 24,
            rules: [
              {
                required: false,
                message: "请上传文件",
                trigger: "blur",
              },
            ],
          },
          {
            label: "访问控制",
            prop: "fileAccess",
            type: "radio",
            viewDisplay: false,
            span: 24,
            dicData: [
              {
                label: "公开",
                value: 0,
              },
              {
                label: "私人",
                value: 1,
              },
              {
                label: "权限",
                value: 2,
              },
            ],
            change: ({ value, column }) => {
              this.handleFileScope(value, column.boxType);
            },
            rules: [
              {
                required: true,
                message: "请选择访问权限",
                trigger: "blur",
              },
            ],
          },
          {
            label: "访问权限",
            prop: "fileScope",
            display: false,
            hide: true,
          },
          {
            label: "文件信息",
            prop: "fileInfoList",
            display: false,
            hide: true,
          },
          {
            label: "用户",
            prop: "scopeUserName",
            span: 24,
            hide: true,
            viewdisplay: true,
            addDisplay: false,
            editDisplay: false,
          },
          {
            label: "部门",
            prop: "scopeDeptName",
            span: 24,
            hide: true,
            viewdisplay: true,
            addDisplay: false,
            editDisplay: false,
          },
          {
            label: "角色",
            prop: "scopeRoleName",
            span: 24,
            hide: true,
            viewdisplay: true,
            addDisplay: false,
            editDisplay: false,
          },
        ],
      },
      data: [],
      userOption: {
        userUrl: "/api/blade-user/search/user?status=1",
        roleUrl: "/api/blade-system/search/role",
        deptUrl: "/api/blade-system/search/dept",
        postUrl: "/api/blade-system/search/post",
      },
    };
  },
  computed: {
    ...mapGetters(["permission", "userInfo"]),
    permissionList(row) {
      console.log(row);
      return {
        delBtn: this.vaildData(this.permission.file_delete, false),
        editBtn: this.vaildData(this.permission.file_edit, false),
        addBtn: this.vaildData(
          this.permission.file_add && this.parentId != 0,
          false
        ),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
    // isDev() {
    //   return process.env.NODE_ENV === "development"
    // }
  },
  //表格错位问题
  activated() {
    this.$nextTick(() => {
      this.$refs.crud.doLayout();
    });
  },
  created() {
    this.initDict();
  },
  methods: {
    // 侧边栏收缩与展开
    asidechange() {
      this.asideStatus = !this.asideStatus;

      if (this.asideStatus) {
        setTimeout(() => {
          this.aside_open_close = true;
        }, 500);
      } else {
        setTimeout(() => {
          this.aside_open_close = false;
        }, 500);
      }
    },
    //新建文件夹提交按钮回调
    handleBuildFileSubmit() {
      this.onLoad(this.page);
    },
    //初始化
    initDict() {
      this.$http.get("/api/ni/resource/file/getParentIdList").then((res) => {
        const parentId = this.findObject(this.option.column, "parentId");
        const data = res.data.data;
        parentId.dicData = data;
        const buildFileParentId = this.findObject(
          this.$refs.buildFileDialog.option.column,
          "parentId"
        );
        buildFileParentId.dicData = data;
      });
    },
    //文件预览
    handlePreview(row) {
      const url = "/kkf/onlinePreview?url=";
      window.open(url + encodeURIComponent(Base64.encode(row.fileUrl)));
    },
    //文件下载
    async handleDownload(fileUrl, fileName) {
      try {
        const response = await fetch(fileUrl);
        if (!response.ok) {
          throw new Error("链接请求失败");
        }
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);

        // 创建一个<a>标签
        const link = document.createElement("a");
        link.href = url;
        link.target = "_blank"; // 在新窗口中打开文件
        link.download = fileName; // 使用文件名称进行下载
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // 释放URL对象
        window.URL.revokeObjectURL(url);
      } catch (error) {
        console.error(error);
      }
    },

    //文件类型判断
    fileTypeFormat(fileType) {
      if (
        [
          "txt",
          "doc",
          "docx",
          "xls",
          "xlsx",
          "pdf",
          "wps",
          "ppt",
          "pptx",
        ].includes(fileType)
      ) {
        return "document";
      } else if (
        ["jpg", "jpeg", "png", "gif", "psd", "bmp"].includes(fileType)
      ) {
        return "img";
      } else if (
        ["wmv", "asf", "rm", "rmvb", "mov", "mp4", "avi"].includes(fileType)
      ) {
        return "video";
      } else if (["wav", "mp3"].includes(fileType)) {
        return "music";
      } else if (["rar", "zip"].includes(fileType)) {
        return "con";
      }
    },
    //文件大小格式化
    fileSizeFormat(size) {
      if (size == null) {
        return "未知";
      }
      if (size < 1024) {
        return size + " B";
      } else if (size < 1024 * 1024) {
        return (size / 1024).toFixed(2) + " K";
      } else if (size < 1024 * 1024 * 1024) {
        return (size / (1024 * 1024)).toFixed(2) + " M";
      } else {
        return (size / (1024 * 1024 * 1024)).toFixed(2) + " G";
      }
    },
    beforeOpen(done, type) {
      if (["add"].includes(type)) {
        const parentId = this.findObject(this.option.column, "parentId");
        parentId.disabled = true;
        this.$set(this.form, "parentId", this.parentId.toString());
      }
      if (["view"].includes(type)) {
        getDetail(this.form.id).then((res) => {
          this.form = res.data.data;
        });
      }
      if (["edit"].includes(type)) {
        getDetail(this.form.id).then((res) => {
          const data = res.data.data.fileScope;
          if (res.data.data.fileAccess == 2) {
            this.$set(
              this.userOption,
              "data",
              data.map((d) => {
                return {
                  value: d.value,
                  text: d.text,
                  type: d.type,
                };
              })
            );
          }
        });
        this.scopeVisible = false;
        this.$set(this.form, "parentId", this.parentId.toString());
        const parentId = this.findObject(this.option.column, "parentId");
        parentId.disabled = false;
      }
      done();
    },
    //双击打开文件夹
    handleRowDBLClick(row) {
      if (!row.isFile) {
        this.parentId = row.id;
        this.addBread(row.fileName, row.id);
        this.searchChange();
      }
    },
    //分别控制文件和文件夹样式
    rowStyle({ row }) {
      if (!row.isFile) {
        return {
          backgroundColor: "#ffffcc",
        };
      }
    },
    //面包屑接口跳转
    handleBreadcrumbClick(item) {
      //出栈
      // 找到当前点击的面包屑在 stack 中的索引
      const index = this.stack.findIndex(
        (stackItem) => stackItem.id === item.id
      );

      if (index !== -1) {
        // 只删除从第三个面包屑开始的后续面包屑
        this.stack.splice(Math.max(index + 1, 2));
      }
      this.parentId = item.id;
      this.searchChange();
    },
    addBread(name, id) {
      this.stack = JSON.parse(JSON.stringify(this.stackCopy));
      getParentId(id).then((res) => {
        const parentIdList = res.data.data;
        // 当前文件是否有父id
        if (parentIdList.length > 0) {
          parentIdList.forEach((item) => {
            // 检查栈中是否已存在相同的id
            const exists = this.stack.some(
              (stackItem) => stackItem.id === item.id
            );
            if (!exists) {
              // 如果不存在相同的id，则将其添加到栈中
              this.stack.push({ text: item.text, id: item.id });
            }
          });
        }
      });
    },

    returnBack() {
      if (!this.parentId) {
        this.$message.warning("无上级文件夹");
        return;
      }
      //返回上一级
      // this.breadcrumbItems.pop();
      this.stack.pop();
      //获取上一级面包屑的id作为父id
      // const lastItem = this.breadcrumbItems[this.breadcrumbItems.length - 1];
      const lastItem = this.stack[this.stack.length - 1];
      this.parentId = lastItem.id;
      this.searchChange();
    },

    //创建子文件夹
    handleAddChildFile() {
      this.handleBuildFile();
    },
    //文件上传
    handleAdd() {
      this.$refs.crud.rowAdd();
    },
    //访问权限
    handleFileScope(value, type) {
      if (type == "add" && value == 2) {
        this.$refs.fileScopeDialog.visible = true;
      } else if (type == "edit" && value == 2) {
        if (this.scopeVisible) {
          this.$refs.fileScopeDialog.visible = true;
        } else {
          this.$refs.fileScopeDialog.visible = false;
        }
        this.scopeVisible = true;
      }
    },
    handleScopeSubmit(list) {
      this.form.fileScope = list;
    },
    //新建文件夹
    handleBuildFile() {
      this.$refs.buildFileDialog.visible = true;
    },
    //判断命令牌是否过期
    // isTokenExpired(){

    // },
    //上传文件校验
    handleBeforeUpload(file) {
      this.header = getAuthorizationHeader(); // 更新命令牌
      if (file.size > 1024 * 1024 * 1024) {
        this.$message.warning("当前文件大于1G，请上传小于1G的文件");
        return false;
      }
    },
    //上传文件
    handleOnChangeFile(file, fileList) {
      // console.log(fileList);
      if (fileList.length > 0) {
        this.isFileUpload = true;
        this.isFile = true;
        this.form.fileInfoList = fileList;
      }
    },
    //超出限制
    handleExceedFile() {
      this.$message.warning(`单次限制上传 5 个文件`);
    },
    //上传失败
    handleErrorFile(err) {
      this.$message.error(err);
    },
    //删除
    rowDel(row, done) {
      if (row.isFile) {
        this.$confirm("是否要删除此文件?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(() => {
          remove(row.id).then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!",
            });
            done();
          });
        });
      } else {
        this.$confirm("是否要删除此文件夹及其包含的所有文件?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(() => {
          remove(row.id).then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!",
            });
            done();
          });
        });
      }
    },
    //提交
    rowSave(row, done, loading) {
      if (!this.isFileUpload) {
        this.$message.warning("请先上传文件");
        return;
      }
      row.isFile = this.isFile;
      add(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          window.console.log(error);
        }
      );
    },
    rowUpdate(row, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          window.console.log(error);
        }
      );
    },
    nodeClick(data) {
      console.log(data);
      if (!data.isFile) {
        this.parentId = data.id;
        //对文件夹添加面包屑
        //1.已经存在不再添加。2.切换目录要清空面包屑。3.切换子目录添加父目录面包屑
        this.addBread(data.fileName, data.id);
      } else {
        this.parentId = data.parentId;
        this.fileId = data.id;
        this.addBread(data.fileName, data.parentId);
      }
      // if (data.parentId != 0) {
      //   getDetail(data.parentId).then((res) => {
      //     const resData = res.data.data;
      //     this.addBread(resData.fileName, data.parentId);
      //   });
      // }
      this.searchChange();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
      this.onLoad(this.page, this.query);
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      //回到文件管理主界面时清空面包屑栈
      if (this.parentId == 0 && this.stack.length > 2) {
        this.stack = JSON.parse(JSON.stringify(this.stackCopy));
      }
      //若文件名为空，则执行局部检索；若文件名不为空，则执行全局搜索
      if (params.fileName == null && params.fileType == null
      && params.fileAbstract == null && params.createUser == null) {
        params.pageId = this.parentId;
        if (this.fileId) {
          params.fileId = this.fileId;
        }
        this.fileId = null;
      }
      // this.initDict();
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
      });
    },
  },
};
</script>

<style lang="scss" scoped>
/deep/ .avue-crud__img {
  img {
    width: 32px;
    height: 32px;
  }
}
.breadcrumbClass {
  display: flex;
  flex-direction: row;
  // background-color: red;
  margin: 15px auto 10px auto;
  align-items: center;
  justify-content: space-between;
}
.breadcrumbBtn {
  margin-left: 10px;
}

/deep/ .el-table__cell:hover {
  cursor: pointer;
}

/deep/ .el-table__cell > .cell {
  text-align: left !important;
}

/* 侧边栏样式 */
.aside_main {
  width: 350px !important;
  transition: width 0.5s;
}

.aside_main_show {
  width: 0px !important;
}

/* 侧边栏按钮样式 */
.aside_open_close {
  position: absolute;
  // left: 350px;
  top: 30%;
  width: 16px;
  height: 60px;
  line-height: 60px;
  color: #fff;
  background-color: #167c464b;
  // border-radius: 0 6px 6px 0;
  border-radius: 6px 0 0 6px;
  font-size: 20px;
  z-index: 1000;
  cursor: pointer;
  // transform: translateX(-30px);
}

.aside_open_close:hover {
  background-color: #45966b;
  color: #fff;
}

.main_cont {
  position: relative;
  margin: 0;
  padding: 0;
  background-color: #e9eef3;
}

// /deep/ .el-scrollbar__view {
//   background-color: red;
//   height: calc(100vh-114px);
// }
.breadcrumbItemStyle {
  float: left;
  width: auto;
  margin-right: 10px;
  text-align: center;
  position: relative;
  z-index: 2;
  font-weight: bold;
  font-size: 14px;
}

.breadcrumbItemStyle::after {
  content: ">";
  margin: 0 10px;
  color: #333;
}

.breadcrumbItemStyle:last-child::after {
  content: "";
}
</style>

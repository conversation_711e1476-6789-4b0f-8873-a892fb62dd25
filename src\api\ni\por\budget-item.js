import request from "@/router/axios";

export const getPage = (current, size, params, deptId) => {
  return request({
    url: "/api/ni/por/budget/item/page",
    method: "get",
    params: {
      ...params,
      deptId,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/ni/por/budget/item/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const remove = (ids) => {
  return request({
    url: "/api/ni/por/budget/item/remove",
    method: "post",
    params: {
      ids,
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/ni/por/budget/item/submit",
    method: "post",
    data: row,
  });
};

export const update = (row) => {
  return request({
    url: "/api/ni/por/budget/item/submit",
    method: "post",
    data: row,
  });
};

export const getList = (params) => {
  return request({
    url: "/api/ni/por/budget/item/list",
    method: "get",
    params,
  });
};

export const getListWithRepair = (budgetId, cost) => {
  return request({
    url: "/api/ni/por/budget/item/listWithRepair",
    method: "get",
    params: {
      budgetId,
      cost,
    },
  });
};
export const getPageWithRepair = (current, size, params) => {
  return request({
    url: "/api/ni/por/budget/item/pageWithRepair",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};
export const getOverPage = (current, size, params) => {
  return request({
    url: "/api/ni/por/budget/item/overPage",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};
export const getOverCount = (params) => {
  return request({
    url: "/api/ni/por/budget/item/overCount",
    method: "get",
    params,
  });
};
export const overRemark = (id, overRemark) => {
  return request({
    url: "/api/ni/por/budget/item/overRemark",
    method: "post",
    data: {
      id,
      overRemark,
    },
  });
};
export const overAudit = (id) => {
  return request({
    url: "/api/ni/por/budget/item/overAudit",
    method: "post",
    params: {
      id,
    },
  });
};
export const changeSubCode = (ids, subCode) => {
  return request({
    url: "/api/ni/por/budget/item/changeSubCode",
    method: "post",
    params: {
      ids,
      subCode,
    },
  });
};

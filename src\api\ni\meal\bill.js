import request from "@/router/axios";

export const getDetail = () => {
  return request({
    url: "/api/ni/meal/order/bill/list",
    method: "get",
  });
};

export const search = (params) => {
  return request({
    url: "/api/ni/meal/order/bill/search",
    method: "post",
    data: params,
  });
};

export const exportBlob = (url, query) => {
  return request({
    url: url,
    method: "post",
    responseType: "blob",
    data: query,
  });
};
export const monthPage = (current, size, params) => {
  return request({
    url: "/api/ni/meal/order/bill/month/page",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};
export const getMonthDetail = (personId, date) => {
  return request({
    url: "/api/ni/meal/order/bill/month/detail",
    method: "get",
    params: {
      personId,
      date,
    },
  });
};
export const monthSum = (current, size, params) => {
  return request({
    url: "/api/ni/meal/order/bill/month/sumRow",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};
export const monthWithRetailPage = (current, size, params) => {
  return request({
    url: "/api/ni/meal/order/bill/month/withRetailPage",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

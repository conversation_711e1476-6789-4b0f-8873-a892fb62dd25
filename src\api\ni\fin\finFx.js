import request from "@/router/axios";

export const getList = (current, size, params) => {
  return request({
    url: "/api/ni/fin/fx/list",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/ni/fin/fx/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const remove = (ids) => {
  return request({
    url: "/api/ni/fin/fx/remove",
    method: "post",
    params: {
      ids,
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/ni/fin/fx/submit",
    method: "post",
    data: row,
  });
};

export const update = (row) => {
  return request({
    url: "/api/ni/fin/fx/submit",
    method: "post",
    data: row,
  });
};
export const red = (id) => {
  return request({
    url: "/api/ni/fin/fx/red",
    method: "post",
    params: {
      id,
    },
  });
};
export const toVoid = (id) => {
  return request({
    url: "/api/ni/fin/fx/toVoid",
    method: "post",
    params: {
      id,
    },
  });
};
export const post = (id) => {
  return request({
    url: "/api/ni/fin/fx/post",
    method: "post",
    params: {
      id,
    },
  });
};

export const getAccountingSettings = (brand) => {
  return request({
    url: "/api/ni/fin/fx/getAccountingSettings",
    method: "get",
    params: {
      brand,
    },
  });
};
export const saveAccountingSettings = (brand, data) => {
  return request({
    url: "/api/ni/fin/fx/saveAccountingSettings",
    method: "post",
    params: {
      brand,
    },
    data,
  });
};

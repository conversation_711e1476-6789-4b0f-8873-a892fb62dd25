<script>
import { getList } from "@/api/ni/old/guoWaiBeiHuoJiHua";

export default {
  props: {
    multiple: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      zhuangTai: "all",
      detail: false,
      title: "国外备货计划",
      params: {},
      visible: false,
      form: {},
      query: {},
      loading: false,
      selectionList: [],
      data: [],
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      option: {
        searchEnter: true,
        searchIndex: 3,
        searchIcon: true,
        size: "mini",
        searchSize: "mini",
        align: "center",
        menu: false,
        addBtn: false,
        header: false,
        border: true,
        tip: false,
        reserveSelection: true,
        highlightCurrentRow: true,
        searchMenuSpan: 6,
        selection: true,
        column: [
          {
            label: "",
            prop: "radio",
            type: "radio",
            width: 55,
            hide: true,
          },
          {
            label: "Id",
            prop: "id",
            search: true,
            width: 60,
          },
          {
            label: "状态",
            prop: "zhuangTai",
            width: 70,
            overHidden: true,
          },
          {
            label: "发货编号",
            prop: "faHuoBianHao",
            width: 90,
            search: true,
            overHidden: true,
          },
          {
            label: "要求备货时间",
            prop: "yaoQiuBeiHuoShiJian",
            width: 100,
            search: true,
            overHidden: true,
          },
          {
            label: "质量级别",
            prop: "zhiLiangJiBie",
            overHidden: true,
            width: 110,
          },
          {
            label: "特殊指标",
            prop: "teShuZhiBiaoYaoQiu",
            placeholder: " ",
            overHidden: true,
            search: true,
            minWidth: 110,
          },
          {
            label: "用友存货编码",
            prop: "yongYouCunHuoBianMa",
            placeholder: " ",
            overHidden: true,
            search: true,
            width: 100,
          },
          {
            label: "规格mm",
            prop: "guiGeMm",
            placeholder: " ",
            overHidden: true,
            search: true,
            minWidth: 80,
          },
          {
            label: "内包装",
            prop: "neiBaoZhuang",
            placeholder: " ",
            overHidden: true,
            width: 90,
          },
          {
            label: "外包装",
            prop: "waiBaoZhuang",
            placeholder: " ",
            overHidden: true,
            minWidth: 110,
          },
          {
            label: "标签",
            prop: "biaoQian",
            placeholder: " ",
            overHidden: true,
            minWidth: 120,
          },
          {
            label: "件数",
            prop: "jianShu",
            placeholder: " ",
            overHidden: true,
            minWidth: 70,
            fixed: "right",
          },
          {
            label: "备注",
            prop: "beiZhu",
            placeholder: " ",
            overHidden: true,
            minWidth: 100,
          },
          {
            label: "单位",
            prop: "danWei",
            placeholder: " ",
            overHidden: true,
            minWidth: 70,
          },
          {
            label: "数量kg",
            prop: "shuLiangKg",
            placeholder: " ",
            overHidden: true,
            minWidth: 90,
            fixed: "right",
          },
          {
            label: "打垛方式",
            prop: "daDuoFangShi",
            placeholder: " ",
            overHidden: true,
            minWidth: 120,
          },
          {
            label: "毛重",
            prop: "maoZhongKgs",
            placeholder: " ",
            overHidden: true,
            minWidth: 90,
          },
          {
            label: "体积",
            prop: "tiJi",
            placeholder: " ",
            overHidden: true,
            minWidth: 120,
          },
          {
            label: "预计批号",
            prop: "yuJiPiHao",
            placeholder: " ",
            overHidden: true,
            minWidth: 90,
          },
          {
            label: "实际批号",
            prop: "shiJiPiHao",
            placeholder: " ",
            overHidden: true,
            minWidth: 120,
          },
          {
            label: "批号打印位置",
            prop: "piHaoDaYinWeiZhi",
            placeholder: " ",
            overHidden: true,
            minWidth: 120,
          },
        ],
      },
    };
  },
  methods: {
    onShow(params) {
      if (!this.multiple) {
        this.$set(this.option, "selection", false);
        this.findObject(this.option.column, "radio").hide = false;
      } else {
        this.$set(this.option, "selection", true);
        this.findObject(this.option.column, "radio").hide = true;
      }
      this.detail = true;
      this.page.currentPage = 1;
      this.query = {};
      this.params = params;
      this.visible = true;
    },
    onSelect(params) {
      if (!this.multiple) {
        this.$set(this.option, "selection", false);
        this.findObject(this.option.column, "radio").hide = false;
      } else {
        this.$set(this.option, "selection", true);
        this.findObject(this.option.column, "radio").hide = true;
      }
      this.detail = false;
      this.page.currentPage = 1;
      this.query = {};
      this.params = params;
      this.visible = true;
    },
    handleConfirm() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$emit("confirm", this.selectionList);
      this.handleClose();
    },
    handleClose(done) {
      this.selectionClear();
      this.visible = false;
      if (done && typeof done == "function") done();
    },
    searchReset() {
      this.query = {};
      this.zhuangTai = "all";
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.onLoad(this.page, params);
      done();
    },
    selectionClear() {
      this.selectionList = [];
      if (this.$refs.crud) this.$refs.crud.toggleSelection();
    },
    rowClick(row) {
      if (!this.multiple) {
        this.selectionList = [row];
        this.$set(this.form, "radio", row.id);
      } else this.$refs.crud.toggleSelection([row]);
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      const qu = { ...params, ...this.query, ...this.params };
      if (this.zhuangTai !== "all") {
        qu.zhuangTai = this.zhuangTai;
      } else {
        qu.zhuangTai = null;
      }
      qu.descs = "id";
      getList(page.currentPage, page.pageSize, qu).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records || [];
        this.loading = false;
      });
    },
  },
};
</script>

<template>
  <el-dialog
    ref="ai-dialog"
    custom-class="ai-dialog"
    :visible.sync="visible"
    :title="title"
    width="60%"
    append-to-body
    :before-close="handleClose"
  >
    <template #title>
      {{ title }}
      <el-divider direction="vertical"></el-divider>
      <el-radio-group
        v-model="zhuangTai"
        size="mini"
        @input="onLoad(page)"
        style="margin-right: 20px"
      >
        <el-radio-button label="all">全部</el-radio-button>
        <el-radio-button label="主管审核">待备货</el-radio-button>
        <el-radio-button label="已备货">已备货</el-radio-button>
      </el-radio-group>
    </template>
    <avue-crud
      v-if="visible"
      :option="option"
      :table-loading="loading"
      :data="data"
      v-model="form"
      :search.sync="query"
      :page.sync="page"
      ref="crud"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionList = $event"
      @row-click="rowClick"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template v-if="!multiple" #radio="{ row }">
        <el-radio v-model="form.radio" :label="row.id"><i></i></el-radio>
      </template>
    </avue-crud>
    <span slot="footer" class="dialog-footer" v-if="!detail">
      <el-button @click="handleClose" size="mini">取 消</el-button>
      <el-button type="primary" @click="handleConfirm" size="mini"
        >确 定</el-button
      >
    </span>
  </el-dialog>
</template>

<style scoped></style>

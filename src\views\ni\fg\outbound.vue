<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      :cell-style="cellStyle"
      @on-load="onLoad"
    >
      <template #serialNo="{ row, index }">
        <span
          :style="{
            textDecoration: 'underline',
            cursor: 'pointer',
          }"
          @click="rowDetail(row, index)"
          >{{ row.serialNo }}</span
        >
      </template>
      <template #type="{ row, index }">
        <span>{{ row.$type }}</span>
        <span v-if="row.type === '0207'"
          >({{ backReasonKeyValue[row.backReason] }})</span
        >
      </template>
      <template #relatedOrderText="{ row }">
        <span
          style="text-decoration: underline; cursor: pointer"
          @click="rowRelatedOrder(row)"
        >
          {{ row.relatedOrderText }}
        </span>
      </template>
      <template #numForm="{ row, index }">
        <el-input-number
          size="mini"
          :min="1"
          :step="1"
          v-model="row.num"
          :max="row.currentStock"
          style="width: 100%"
          controls-position="right"
          @change="rowNumChange($event, row)"
        />
      </template>
      <template #menuLeft>
        <el-button
          type="primary"
          size="mini"
          icon="el-icon-plus"
          v-if="permission.fgOutbound_add"
          @click="handleAdd"
          >新 增
        </el-button>
        <el-button
          type="danger"
          size="mini"
          icon="el-icon-delete"
          plain
          v-if="permission.fgOutbound_delete"
          @click="handleDelete"
          >删 除
        </el-button>
        <el-dropdown v-if="permission.fgOutbound_sync" @command="handleSync">
          <el-button type="warning" size="mini" icon="el-icon-refresh"
            >同步旧NI出库<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="0201">销售订单</el-dropdown-item>
            <el-dropdown-item command="0205" disabled
              >国外发货
            </el-dropdown-item>
            <el-dropdown-item command="0206">销售外库补货</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-button
          type="success"
          size="mini"
          plain
          icon="el-icon-s-data"
          @click="handleCount"
          >统计
        </el-button>
      </template>
      <template #menu="{ row, index, size }">
        <el-button
          type="text"
          icon="el-icon-edit"
          size="mini"
          v-if="row.status === 0 && permission.fgOutbound_edit"
          @click.stop="$refs.outboundFormDialogRef.onEdit(row.id)"
          >编辑
        </el-button>
        <el-button
          type="text"
          icon="el-icon-delete"
          size="mini"
          v-if="row.status === 0 && permission.fgOutbound_delete"
          @click.stop="$refs.crud.rowDel(row, index)"
          >删除
        </el-button>
        <el-button
          type="text"
          icon="el-icon-back"
          size="mini"
          v-if="row.status === 1 && !row.red && permission.fgOutbound_red"
          @click.stop="rowRed(row)"
          >冲红
        </el-button>
        <el-button
          type="text"
          icon="el-icon-reading"
          size="mini"
          @click.stop="handleDataSub(row)"
          >明细
        </el-button>
        <el-button
          type="text"
          icon="el-icon-document-copy"
          size="mini"
          style="color: #e6a23c"
          v-if="row.status === 2 && !row.red && permission.fgOutbound_add"
          @click.stop="rowCopy(row)"
          >复制新增
        </el-button>
        <el-button
          type="text"
          icon="el-icon-download"
          :size="size"
          v-if="!['0201', '0205', '0206'].includes(row.type)"
          @click="rowAttach(row)"
          >附件
        </el-button>
        <el-button
          type="text"
          icon="el-icon-download"
          :size="size"
          v-if="
            ['0201', '0205', '0206'].includes(row.type) && row.relatedOrderText
          "
          @click="rowFhAttach(row)"
          >发货附件
        </el-button>
        <el-button
          type="text"
          icon="el-icon-download"
          :size="size"
          v-if="
            ['0201', '0205', '0206'].includes(row.type) && row.relatedOrderText
          "
          @click="rowZjAttach(row)"
          >质检附件
        </el-button>
        <el-button
          v-if="row.type === '0205' && row.relatedOrderText"
          type="text"
          icon="el-icon-download"
          :size="size"
          @click="rowLhdAttach(row)"
          >理货单附件
        </el-button>
        <el-button
          type="text"
          v-if="row.type === '0201' && !row.relatedOrderId"
          icon="el-icon-connection"
          size="mini"
          @click="rowLinkOldCNOrder(row, index)"
          >关联销售订单
        </el-button>
        <el-button
          type="text"
          v-if="row.type === '0206' && !row.relatedOrderId"
          icon="el-icon-connection"
          size="mini"
          @click="rowLinkXiaoShouWaiKuBuHuo(row, index)"
          >关联外库补货
        </el-button>
        <!--        <el-button-->
        <!--          type="text"-->
        <!--          v-if="-->
        <!--            row.type === '0201' &&-->
        <!--            row.relatedOrderId &&-->
        <!--            (row.syncOld == null || row.syncOld === 0)-->
        <!--          "-->
        <!--          icon="el-icon-upload2"-->
        <!--          size="mini"-->
        <!--          @click="rowSyncOldCNOrder(row, index)"-->
        <!--          >同步发货-->
        <!--        </el-button>-->
        <!--        <el-button-->
        <!--          type="text"-->
        <!--          v-if="-->
        <!--            row.type === '0205' &&-->
        <!--            row.relatedOrderId &&-->
        <!--            (row.syncOld == null || row.syncOld === 0)-->
        <!--          "-->
        <!--          icon="el-icon-upload2"-->
        <!--          size="mini"-->
        <!--          @click="rowSyncOldOSOrder(row, index)"-->
        <!--          >同步发货-->
        <!--        </el-button>-->
        <!--        <el-button-->
        <!--          type="text"-->
        <!--          v-if="-->
        <!--            row.type === '0206' &&-->
        <!--            row.relatedOrderId &&-->
        <!--            (row.syncOld == null || row.syncOld === 0)-->
        <!--          "-->
        <!--          icon="el-icon-upload2"-->
        <!--          size="mini"-->
        <!--          @click="rowSyncXiaoShouWaiKuBuHuo(row, index)"-->
        <!--          >同步补货-->
        <!--        </el-button>-->
        <el-button
          type="text"
          v-if="row.type === '0205' && !row.relatedOrderId"
          icon="el-icon-connection"
          size="mini"
          @click="rowLinkOldOSOrder(row, index)"
          >关联国外发货
        </el-button>
      </template>
    </avue-crud>
    <el-drawer
      :title="fgTransactionName"
      :visible.sync="subVisible"
      :direction="direction"
      append-to-body
      :before-close="handleSubClose"
      size="1300px"
    >
      <basic-container>
        <avue-crud
          :option="optionSub"
          :data="dataSub"
          :page.sync="pageSub"
          v-model="formSub"
          :table-loading="loadingSub"
          ref="crudSub"
          @row-del="rowDelSub"
          @row-update="rowUpdateSub"
          @row-save="rowSaveSub"
          :before-open="beforeOpenSub"
          @search-change="searchChangeSub"
          @search-reset="searchResetSub"
          @selection-change="selectionChangeSub"
          @current-change="currentChangeSub"
          @size-change="sizeChangeSub"
          @on-load="onLoadSub"
          :cell-style="cellStyleSub"
        >
          <template #menu="{ row, index }">
            <el-button
              type="text"
              icon="el-icon-upload"
              size="mini"
              @click.stop="rowQualitySub(row)"
              >质检单
            </el-button>
            <el-button
              v-if="!row.batchNo"
              type="text"
              icon="el-icon-s-grid"
              size="mini"
              @click.stop="rowBatchNoSub(row)"
              >登记批号
            </el-button>
          </template>
        </avue-crud>
      </basic-container>
    </el-drawer>
    <outbound-form-dialog ref="outboundFormDialogRef" @confirm="onLoad(page)" />
    <xiao-shou-ding-dan-select-dialog
      ref="xiaoShouDingDanSelectDialogRef"
      @confirm="handleXiaoShouDingDanSelectConfirm"
    />
    <xiao-shou-wai-ku-bu-huo-select-dialog
      ref="xiaoShouWaiKuBuHuoSelectDialogRef"
      @confirm="handleXiaoShouWaiKuBuHuoSelectConfirm"
    />
    <attach-dialog ref="attachDialogRef" code="private" />
    <guo-wai-fa-huo-select-dialog
      ref="guoWaiFaHuoSelectDialogRef"
      @confirm="rowLinkOldOSOrderSubmit"
    />
    <batch-no-check-in-dialog
      ref="batchNoCheckInDialogRef"
      @confirm="onLoadSub(pageSub)"
    />
    <outbound-count-dialog ref="outboundCountDialogRef" />
  </basic-container>
</template>

<script>
import {
  add,
  getDetail,
  getList,
  linkGuoWaiFaHuo,
  linkXiaoShouDingDan,
  linkXiaoShouWaiKuBuHuo,
  red,
  remove,
  syncFromXiaoShouDingDan,
  syncFromXiaoShouWaiKuBuHuo,
  syncOldCN,
  syncOldOS,
  syncXiaoShouWaiKuBuHuo,
  update,
} from "@/api/ni/fg/fgOutbound";
import {
  add as addSub,
  getAttachLinks,
  getDetail as getDetailSub,
  getList as getListSub,
  remove as removeSub,
  update as updateSub,
} from "@/api/ni/fg/fgTransactionItem";
import { mapGetters } from "vuex";
import { dateFormat } from "@/util/date";
import OutboundFormDialog from "@/views/ni/fg/components/OutboundForm1Dialog.vue";
import XiaoShouDingDanSelectDialog from "@/views/ni/old/components/XiaoShouDingDanSelectDialog.vue";
import AttachDialog from "@/components/attach-dialog/index.vue";
import GuoWaiFaHuoSelectDialog from "@/views/ni/old/components/GuoWaiFaHuoSelectDialog.vue";
import BatchNoCheckInDialog from "@/views/ni/fg/components/BatchNoCheckInDialog.vue";
import XiaoShouWaiKuBuHuoSelectDialog from "@/views/ni/old/components/XiaoShouWaiKuBuHuoSelectDialog.vue";
import OutboundCountDialog from "@/views/ni/fg/components/OutboundCountDialog.vue";

export default {
  components: {
    XiaoShouWaiKuBuHuoSelectDialog,
    GuoWaiFaHuoSelectDialog,
    AttachDialog,
    XiaoShouDingDanSelectDialog,
    OutboundFormDialog,
    BatchNoCheckInDialog,
    OutboundCountDialog,
  },
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      data: [],
      selectionList: [],
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      option: {
        editBtn: false,
        delBtn: false,
        addBtn: false,
        dialogFullscreen: true,
        dialogDrag: true,
        size: "mini",
        searchSize: "mini",
        align: "center",
        searchIndex: 3,
        searchIcon: true,
        searchEnter: true,
        height: "auto",
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        span: 8,
        border: true,
        index: true,
        selection: true,
        menuWidth: 300,
        column: [
          {
            label: "出库编号",
            prop: "serialNo",
            placeholder: "系统自动生成",
            overHeight: true,
            search: true,
          },
          {
            label: "出库主题",
            prop: "title",
            overHidden: true,
            search: true,
            rules: [
              {
                required: true,
                message: "请输入出库主题",
                trigger: "blur",
              },
            ],
          },
          {
            label: "出库类型",
            prop: "type",
            type: "select",
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_fg_outbound_type",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            multiple: true,
            search: true,
            rules: [
              {
                required: true,
                message: "请选择出库类型",
                trigger: "blur",
              },
            ],
          },
          {
            label: "仓库",
            prop: "depotId",
            type: "select",
            dicUrl: "/api/ni/base/depot/info/list?status=2&type=1",
            props: {
              label: "name",
              value: "id",
              desc: "code",
            },
            search: true,
            rules: [
              {
                required: true,
                message: "请选择仓库",
                trigger: "blur",
              },
            ],
          },
          {
            label: "操作人",
            prop: "opUserId",
            type: "select",
            remote: true,
            dicUrl: `/api/blade-user/search/otj/user?name={{key}}`,
            props: {
              label: "realName",
              value: "id",
            },
            dicFormatter: (data) => {
              return data.data.records;
            },
            placeholder: " ",
            filterable: true,
            overHidden: true,
            rules: [
              {
                required: true,
                message: "请选择操作人",
                trigger: "blur",
              },
            ],
            search: true,
            hide: true,
            showColumn: false,
          },
          {
            label: "操作人",
            prop: "opUserName",
            display: false,
            minWidth: 80,
          },
          {
            label: "出库时间",
            prop: "opDate",
            search: true,
            type: "date",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            searchRange: true,
            placeholder: " ",
            minWidth: 120,
            rules: [
              {
                required: true,
                message: "请选择操作时间",
                trigger: "blur",
              },
            ],
          },
          {
            label: "关联单据",
            prop: "relatedOrderId",
            type: "input",
            hide: true,
            showColumn: false,
          },
          {
            label: "关联单据",
            prop: "relatedOrderText",
            type: "input",
            overHidden: true,
            display: false,
            search: true,
          },
          {
            label: "退生产原因",
            prop: "backReason",
            type: "select",
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_fg_outbound_back_reason",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            hide: true,
            showColumn: false,
            placeholder: " ",
            rules: [
              {
                required: true,
                message: "请选择退生产原因",
                trigger: "blur",
              },
            ],
          },
          {
            label: "出库数量",
            prop: "total",
            overHidden: true,
            minWidth: 100,
            display: false,
          },
          {
            label: "备注",
            prop: "remark",
            type: "textarea",
            span: 24,
            minRows: 2,
            search: true,
            overHidden: true,
          },
          {
            label: "创建人",
            prop: "createUserName",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            hide: true,
          },
          {
            label: "创建时间",
            prop: "createTime",
            type: "date",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            addDisplay: false,
            editDisplay: false,
            hide: true,
          },
          {
            label: "状态",
            prop: "status",
            type: "select",
            dicData: [
              {
                label: "正常",
                value: 1,
              },
              {
                label: "已冲红",
                value: 2,
              },
            ],
            addDisplay: false,
            editDisplay: false,
          },
        ],
      },
      subVisible: false,
      direction: "rtl",
      transactionId: 0,
      fgTransactionName: "出库明细",
      formSub: {},
      querySub: {},
      loadingSub: true,
      dataSub: [],
      selectionListSub: [],
      pageSub: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      optionSub: {
        showSummary: true,
        sumColumnList: [
          {
            name: "num",
            type: "sum",
            decimals: 0,
          },
          {
            name: "weight",
            type: "sum",
          },
        ],
        menu: false,
        menuWidth: 170,
        size: "mini",
        searchSize: "mini",
        align: "center",
        searchIndex: 3,
        searchIcon: true,
        searchEnter: true,
        addBtn: false,
        delBtn: false,
        editBtn: false,
        height: "auto",
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        selection: true,
        dialogClickModal: false,
        column: [
          {
            label: "存货编码",
            prop: "materialCode",
            placeholder: " ",
            width: 110,
            cell: false,
            display: false,
          },
          {
            label: "规格",
            prop: "specText",
            placeholder: " ",
            display: false,
            overHidden: true,
            cell: false,
            width: 120,
          },
          {
            label: "外包装",
            prop: "packageText",
            placeholder: " ",
            display: false,
            overHidden: true,
            cell: false,
            width: 115,
          },
          {
            label: "内包装",
            prop: "innerPackageText",
            placeholder: " ",
            display: false,
            overHidden: true,
            cell: false,
            width: 115,
          },
          {
            label: "质量",
            prop: "qualityLevel",
            type: "select",
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_product_sku_quality_level",
            props: {
              label: "dictValue",
              value: "dictKey",
              desc: "dictKey",
            },
            placeholder: " ",
            width: 100,
          },
          {
            label: "批号",
            prop: "batchNo",
            placeholder: " ",
            minWidth: 110,
          },
          {
            label: "数量",
            prop: "num",
            placeholder: " ",
            type: "number",
            minWidth: 80,
          },
          {
            label: "重量",
            prop: "weight",
            placeholder: " ",
            minWidth: 80,
          },
          {
            label: "单位",
            prop: "unit",
            type: "select",
            filterable: true,
            width: 80,
            cell: false,
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_material_unit",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            rules: [
              {
                required: true,
                message: "请选择单位",
                trigger: "blur",
              },
            ],
            slot: true,
            placeholder: " ",
          },
          {
            label: "备注",
            prop: "remark",
            type: "textarea",
            placeholder: " ",
            minRows: 1,
            overHidden: true,
            minWidth: 120,
          },
        ],
      },
      backReasonKeyValue: {},
      selectType: "",
      row: {},
      qualityLevelColorMap: {
        A: "#67C23A", // 高吸附 - 绿色
        P: "#409EFF", // 优等品 - 蓝色
        Q: "#E6A23C", // 合格品 - 橙色
      },
    };
  },
  created() {
    this.$http
      .get(
        "/api/blade-system/dict-biz/dictionary?code=ni_fg_outbound_back_reason"
      )
      .then((res) => {
        this.backReasonKeyValue = res.data.data.reduce((acc, cur) => {
          acc[cur.dictKey] = cur.dictValue;
          return acc;
        }, {});
      });
  },
  computed: {
    ...mapGetters(["permission", "userInfo", "colorName"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.fgOutbound_add, false),
        viewBtn: this.vaildData(this.permission.fgOutbound_view, false),
        delBtn: this.vaildData(this.permission.fgOutbound_delete, false),
        editBtn: this.vaildData(this.permission.fgOutbound_edit, false),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
    subIds() {
      let ids = [];
      this.selectionListSub.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
    items() {
      return this.form.items || [];
    },
  },
  methods: {
    handleXiaoShouWaiKuBuHuoSelectConfirm(selectionList) {
      if (this.selectType === "linkXiaoShouWaiKuBuHuo")
        this.rowLinkXiaoShouWaiKuBuHuoSubmit(selectionList);
      else if (this.selectType === "syncXiaoShouWaiKuBuHuo") {
        this.synWaiKuBuHuo(selectionList);
      }
    },
    handleXiaoShouDingDanSelectConfirm(selectionList) {
      if (this.selectType === "linkOldCNOlder")
        this.rowLinkOldCNOrderSubmit(selectionList);
      else if (this.selectType === "syncXiaoShouDingDan") {
        this.syncXiaoShouDingDan(selectionList);
      }
    },
    synWaiKuBuHuo(selectionList) {
      if (selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$alert(
        `确定将选择的<span style="color: #F56C6C;font-weight: bold">${selectionList.length}条</span>销售外库补货数据同步?`,
        {
          dangerouslyUseHTMLString: true,
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(() => {
          const ids = selectionList.map((ele) => ele.id);
          return syncFromXiaoShouWaiKuBuHuo(ids.join(","), 1);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    syncXiaoShouDingDan(selectionList) {
      if (selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$alert(
        `确定将选择的<span style="color: #F56C6C;font-weight: bold">${selectionList.length}条</span>数据同步?`,
        {
          dangerouslyUseHTMLString: true,
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(() => {
          const ids = selectionList.map((ele) => ele.id);
          return syncFromXiaoShouDingDan(ids.join(","), 1);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    rowRelatedOrder(row) {
      if (row.type === "0201") {
        this.$refs.xiaoShouDingDanSelectDialogRef.onShow({
          id: row.relatedOrderId,
          descs: "id",
        });
      } else if (row.type === "0205") {
        this.$refs.guoWaiFaHuoSelectDialogRef.onShow({
          id: row.relatedOrderId,
          descs: "id",
        });
      } else if (row.type === "0206") {
        this.$refs.xiaoShouWaiKuBuHuoSelectDialogRef.onShow({
          id: row.relatedOrderId,
          descs: "id",
        });
      }
    },
    rowLinkXiaoShouWaiKuBuHuo(row) {
      this.selectType = "linkXiaoShouWaiKuBuHuo";
      this.row = row;
      this.$refs.xiaoShouWaiKuBuHuoSelectDialogRef.onSelect(
        { descs: "id" },
        "/api/ni/old/xiaoShouWaiKuBuHuo/shippingList"
      );
    },
    rowLinkOldCNOrder(row) {
      this.selectType = "linkOldCNOlder";
      this.row = row;
      this.$refs.xiaoShouDingDanSelectDialogRef.onSelect(
        { descs: "id" },
        "/api/ni/old/xiaoShouDingDan/shippingList"
      );
    },
    rowSyncXiaoShouWaiKuBuHuo(row) {
      this.$confirm("确定同步该数据?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.loading = true;
          return syncXiaoShouWaiKuBuHuo(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    rowSyncOldOSOrder(row) {
      this.$confirm("确定同步该数据?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.loading = true;
          return syncOldOS(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    rowSyncOldCNOrder(row) {
      this.$confirm("确定同步该数据?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.loading = true;
          return syncOldCN(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    rowLinkXiaoShouWaiKuBuHuoSubmit(selectionList) {
      if (selectionList.length > 0) {
        linkXiaoShouWaiKuBuHuo(this.row.id, selectionList[0].id).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
      }
    },
    rowLinkOldCNOrderSubmit(selectionList) {
      if (selectionList.length > 0) {
        linkXiaoShouDingDan(this.row.id, selectionList[0].id).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
      }
    },
    rowLinkOldOSOrder(row) {
      this.row = row;
      this.$refs.guoWaiFaHuoSelectDialogRef.onSelect({ descs: "id" });
    },
    rowLinkOldOSOrderSubmit(selectionList) {
      if (selectionList.length > 0) {
        linkGuoWaiFaHuo(this.row.id, selectionList[0].id).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
      }
    },
    rowNumChange(value, row) {
      row.weight = Number(value) * Number(row.capacity);
    },
    rowAttach(row) {
      this.rowAttach1(row.id, "ni_fg_outbound");
    },
    rowFhAttach(row) {
      this.rowAttach1(row.relatedOrderText, "ni_fg_shipping_fh");
    },
    rowZjAttach(row) {
      this.rowAttach1(row.relatedOrderText, "ni_fg_shipping_zj");
    },
    rowLhdAttach(row) {
      this.rowAttach1(row.relatedOrderText, "ni_fg_shipping_lhd");
    },
    rowAttach1(businessKey, businessName) {
      return this.$refs.attachDialogRef.init(businessKey, businessName);
    },
    async rowCopy(row) {
      const res = await getDetail(row.id);
      const form = res.data.data;
      form.id = null;
      form.serialNo = null;
      form.opUserId = this.userInfo.user_id;
      form.opDate = dateFormat(new Date(), "yyyy-MM-dd");
      form.status = null;
      form.items.forEach((item) => {
        item.id = null;
        item.transactionId = null;
      });
      this.$refs.outboundFormDialogRef.onAdd(form.depotId, [], form);
    },
    rowRed(row) {
      this.$prompt("请输入冲红原因", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
      }).then(({ value }) => {
        if (!value) {
          this.$message({
            type: "error",
            message: "请输入冲红原因",
          });
          return;
        }
        red(row.id, value).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
      });
    },
    rowDetail(row) {
      this.$refs.outboundFormDialogRef.onShow(row.id);
    },
    // 主表模块
    rowSave(row, done, loading) {
      add(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          window.console.log(error);
          loading();
        }
      );
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          window.console.log(error);
          loading();
        }
      );
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    handleAdd() {
      this.$refs.outboundFormDialogRef.onAdd(null, []);
    },
    handleSync(type) {
      if (type === "0201") {
        this.selectType = "syncXiaoShouDingDan";
        this.$refs.xiaoShouDingDanSelectDialogRef.onSelect(
          {
            fhd: "成品库,三厂仓库",
            zt: "已发货,已到货",
            descs: "id",
          },
          null,
          true
        );
      } else if (type === "0206") {
        this.selectType = "syncXiaoShouWaiKuBuHuo";
        this.$refs.xiaoShouWaiKuBuHuoSelectDialogRef.onSelect(
          { zhuangTai: "已发货,已到货", descs: "id" },
          null,
          true
        );
      }
    },
    handleCount() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$refs.outboundCountDialogRef.onShow(this.ids);
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      // 检查是否存在状态非0的项
      const hasNonZeroStatus = this.selectionList.some(
        (ele) => ele.status !== 0 && ele.status !== undefined
      );
      if (hasNonZeroStatus) {
        this.$message.error("选择的数据包含已提交审核的条目，请重新选择");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen(done, type) {
      const items = this.findObject(this.option.column, "items");
      const currentStock = this.findObject(
        items.children.column,
        "currentStock"
      );
      if ("add" === type) {
        currentStock.hide = false;
        this.form.opUserId = this.userInfo.user_id;
        this.form.opDate = dateFormat(new Date(), "yyyy-MM-dd");
      }
      if (["edit", "view"].includes(type)) {
        currentStock.hide = true;
        getDetail(this.form.id).then((res) => {
          this.form = res.data.data;
        });
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },

    onLoad(page, params = {}) {
      this.loading = true;
      const q = {
        ...params,
        ...this.query,
      };
      if (q.opDate != null && q.opDate.length === 2) {
        q.startOpDate = q.opDate[0];
        q.endOpDate = q.opDate[1];
        q.opDate = null;
      }
      getList(page.currentPage, page.pageSize, q).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
    cellStyle({ row, column }) {
      if ("status" === column.columnKey) {
        return {
          backgroundColor: row.status === 2 ? "#E6A23C" : this.colorName,
          color: "#fff",
        };
      }
      if ("relatedOrderText" === column.columnKey && row.syncOld === 1) {
        return {
          color: "#333", // 文字颜色
          backgroundColor: "#2080F0", // 默认浅灰
        };
      }
      if ("serialNo" === column.columnKey && row.red) {
        return {
          backgroundColor: "#F56C6C",
          color: "#fff",
        };
      }
      if ("type" === column.columnKey) {
        const typeColorMap = {
          "0201": "#409EFF", // 国内发货 - 更明亮的蓝色
          "0205": "#67C23A", // 国外发货 - 保持绿色，清晰易辨
          "0202": "#E6A23C", // 调拨出库 - 保持橙色，柔和但醒目
          "0203": "#F56C6C", // 盘亏出库 - 保持红色，表示警告或异常
          "0204": "#FFB980", // 样品出库 - 更柔和的橙粉色，避免过于刺眼
          "0206": "#FF6F61", // 销售外库补货 - 更深一些的红色，与盘亏区分开
          "0207": "#FF8F80", // 退生产 - 柔和的浅红色，与其他类型形成对比
          "0208": "#FFB6C1", //倒箱出库 - 更柔和的橙色，与盘亏区分开
          "0299": "#FEE08C", // 其他出库 - 保持黄色，表示中性状态
        };
        return {
          color: "#333", // 文字颜色
          backgroundColor: typeColorMap[row.type] || "#f5f7fa", // 默认浅灰
        };
      }
    },
    // 子表模块
    rowQualitySub(row) {
      this.$refs.attachDialogRef.init(row.id, "ni_fg_transaction_item");
    },
    rowBatchNoSub(row) {
      this.$refs.batchNoCheckInDialogRef.onCheckIn(row);
    },
    handleDataSub(row) {
      this.subVisible = true;
      this.transactionId = row.id;
      this.onLoadSub(this.pageSub);
    },
    handleSubClose(hide) {
      hide();
    },
    rowSaveSub(row, loading, done) {
      row = {
        ...row,
        transactionId: this.transactionId,
      };
      addSub(row).then(
        () => {
          loading();
          this.onLoadSub(this.pageSub);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        },
        (error) => {
          done();
          window.console.log(error);
        }
      );
    },
    rowUpdateSub(row, index, loading, done) {
      row = {
        ...row,
        transactionId: this.transactionId,
      };
      updateSub(row).then(
        () => {
          loading();
          this.onLoadSub(this.pageSub);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        },
        (error) => {
          done();
          window.console.log(error);
        }
      );
    },
    rowDelSub(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return removeSub(row.id);
        })
        .then(() => {
          this.onLoadSub(this.pageSub);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    handleDeleteSub() {
      if (this.selectionListSub.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return removeSub(this.subIds);
        })
        .then(() => {
          this.onLoadSub(this.pageSub);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crudSub.toggleSelection();
        });
    },
    beforeOpenSub(done, type) {
      if (["edit", "view"].includes(type)) {
        getDetailSub(this.formSub.id).then((res) => {
          this.formSub = res.data.data;
        });
      }
      done();
    },
    searchResetSub() {
      this.querySub = {};
      this.onLoadSub(this.pageSub);
    },
    searchChangeSub(params) {
      this.querySub = params;
      this.onLoadSub(this.pageSub, params);
    },
    selectionChangeSub(list) {
      this.selectionListSub = list;
    },
    currentChangeSub(currentPage) {
      this.pageSub.currentPage = currentPage;
    },
    sizeChangeSub(pageSize) {
      this.pageSub.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoadSub(page, params = {}) {
      this.loadingSub = true;
      const values = {
        ...params,
        transactionId: this.transactionId,
      };
      getListSub(
        page.currentPage,
        page.pageSize,
        Object.assign(values, this.querySub)
      ).then((res) => {
        const data = res.data.data;
        this.pageSub.total = data.total;
        this.dataSub = data.records.map((item) => {
          item.quality = "";
          return item;
        });
        this.dataSub.forEach((item) => {
          getAttachLinks(item.id).then((res) => {
            item.quality = res.data.data.join(",");
          });
        });
        this.selectionListSub = [];
        this.loadingSub = false;
      });
    },
    cellStyleSub({ row, column }) {
      if ("qualityLevel" === column.columnKey) {
        const color = this.qualityLevelColorMap[row.qualityLevel];
        return {
          backgroundColor: color || "#909399", // 默认颜色为灰色
          color: "#fff",
        };
      }
    },
  },
};
</script>

<style></style>

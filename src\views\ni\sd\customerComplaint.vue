<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page.sync="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @refresh-change="refreshChange"
               @on-load="onLoad">
      <template #complaintNumber="{ row, size, index }">
        <flow-timeline-popover
          v-if="row.processInsId"
          :process-ins-id="row.processInsId"
          :form-key="formKey"
          :process-def-key="processDefKey"
          v-model="row.complaintNumber"
          :flow.sync="row.flow"
          trigger="click"
          lazy
        />
        <span v-else>{{ row.complaintNumber }}</span>
      </template>
      <template slot="menuLeft">
        <el-button
          type="warning"
          size="mini"
          icon="el-icon-position"
          plain
          v-if="permission.customerComplaint_apply"
          @click="handleApply"
        >投诉申请
        </el-button>
        <el-button type="danger"
                   size="mini"
                   icon="el-icon-delete"
                   plain
                   v-if="permission.customerComplaint_delete"
                   @click="handleDelete">删 除
        </el-button>
        <el-button
          type="warning"
          size="mini"
          icon="el-icon-printer"
          plain
          v-if="permission.customerComplaint_print"
          @click="handlePrintConfirm"
        >打印
        </el-button>
        <el-button
          size="mini"
          icon="el-icon-download"
          type="success"
          v-if="permission.customerComplaint_export"
          @click="handleExport"
        >导出
        </el-button>
<!--        <el-button-->
<!--          size="mini"-->
<!--          icon="el-icon-download"-->
<!--          type="success"-->
<!--          v-if="permission.customerComplaint_export"-->
<!--          @click="handleExport1"-->
<!--        >生成-->
<!--        </el-button>-->
      </template>
      <template #menu="{ row, index, size }">
        <el-button
          type="text"
          icon="el-icon-tickets"
          :size="size"
          @click="handleDataSub(row)"
        >详情
        </el-button>
        <el-divider direction="vertical" />
        <el-dropdown>
          <el-button type="text" :size="size">
            更多<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">

            <el-dropdown-item type="text"
                              icon="el-icon-download"
                              :size="size"
                              v-if="row.feedbackReport!=='有' && row.$complaintType==='产品质量'"
                              @click.native="handleExport1(row)">
              生成报告
            </el-dropdown-item>
            <el-dropdown-item type="text"
                              icon="el-icon-download"
                              :size="size"
                              @click.native="rowAttach(row)">
              附件
            </el-dropdown-item>
            <el-dropdown-item type="text"
                              icon="el-icon-download"
                              :size="size"
                              v-if="row.feedbackReport==='有'"
                              @click.native="handleDownload(row.reportAttachLink, row.reportAttachName)">
              下载报告
            </el-dropdown-item>

          </el-dropdown-menu>
          </el-dropdown>
<!--        <el-button-->
<!--          type="text"-->
<!--          icon="el-icon-download"-->
<!--          :size="size"-->
<!--          @click="rowAttach(row)"-->
<!--        >附件-->
<!--        </el-button>-->
      </template>
    </avue-crud>
    <attach-dialog ref="attachDialogRef" :detail="attachDetail"/>
    <el-drawer
      title="投诉处理详情"
      size="60%"
      :visible.sync="drawer"
      :direction="direction"
      append-to-body
      :before-close="handleSubClose">
      <basic-container>
      <avue-crud :data="data1"
                 :page.sync="page1"
                 ref="crud"
                 @current-change="currentChange1"
                 @size-change="sizeChange1"
                 :option="option1">
        <template #menu="{ row, index, size }">
          <el-button
            type="text"
            icon="el-icon-download"
            :size="size"
            @click="rowAttach(row)"
          >附 件
          </el-button>
        </template>
      </avue-crud>
      </basic-container>
    </el-drawer>
  </basic-container>

</template>

<script>
import {
  getList,
  getDetail,
  add,
  update,
  remove,
  getListDetail,
  getListMasterDetail, qualityFeedbackForm, getCustomerList
} from "@/api/ni/sd/customerComplaint";
  import {mapGetters} from "vuex";
  import AttachDialog from "@/components/attach-dialog/index.vue";
  import FlowTimelinePopover from "@/components/wf-flow-timeline/popover.vue";
  import exForm from "@/views/plugin/workflow/mixins/ex-form";
  import {dateNow1} from "@/util/date";
  import {hiprint} from "vue-plugin-hiprint";
  import {loadPrintTemplate} from "@/api/system/printTemplate";
import Userinfo from "@/views/system/userinfo.vue";
import NProgress from "nprogress";

  export default {
    mixins: [exForm],
    components: {FlowTimelinePopover, AttachDialog},
    data() {
      return {
        currenRow: null,
        direction: "rtl",
        drawer: false,
        processDefKey: "process_sd_customer_complaint2",
        formKey: "wf_ex_sd/CustomerComplaint",
        form: {},
        form1: {},
        query: {},
        query1: {},
        loading: true,
        loading1: true,
        page1: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },

        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },

        complaintTypeDict:[],
        complaintTypeDictKeyValue: {},

        classificationDict:[],
        classificationDictKeyValue: {},

        businessManagerDict:[],
        businessManagerDictKeyValue: {},

        userinfoDict:[],
        userinfoDictKeyValue: {},

        selectionList: [],
        attachDetail: false,
        option1: {
          // align: 'center',
          search: false,
          searchShow: false,
          addBtn: false,
          menuRight: false,
          refreshBtn: false,
          columnBtn: false,
          border: true,
          stripe: true,
          editBtn: false,
          delBtn: false,
          viewBtn: true,
          menuWidth: 80,
          index: true,
          column: [
            // {
            //   label: "投诉编号",
            //   prop: "complaintNumber",
            //   type: "input",
            //   search: true,
            //   disabled: true,
            //   minWidth: 30,
            // },
            {
              label: "解决方案",
              prop: "solution",
              type: "textarea",
              search: false,
              disabled: true,
              span: 24,
              minWidth: 180,
            },
            {
              label: "处理时间",
              prop: "processTime",
              type: "input",
              search: false,
              disabled: true,
              overHidden: true,
              minWidth: 30,
            },
            {
              label: "负责人",
              prop: "inChargePerson",
              dicUrl: `/api/blade-user/user-list`,
              minWidth: 30,
              props: {
                label: "name",
                value: "id",
              },
              search: false,
            },
            {
              label: "负责部门",
              prop: "chargeDept",
              type: "tree",
              disabled: true,
              dicUrl: `/api/blade-system/dept/list`,
              minWidth: 30,
              props: {
                label: 'deptName',
                value: 'id',
              },
              search: false,
            },
            ],
        },
        option: {
          dialogFullscreen: false,
          searchEnter: true,
          align: "center",
          height:'auto',
          calcHeight: 30,
          tip: false,
          searchShow: true,
          searchMenuSpan: 6,
          border: true,
          index: false,
          viewBtn: true,
          selection: true,
          dialogClickModal: false,
          menuWidth: 200,
          labelWidth: 110,
          size: 'mini',
          searchSize: 'mini',
          span: 6,
          searchIndex: 3,
          searchIcon: true,
          addBtn: false,
          delBtn: false,
          editBtn: false,
          column: [
            {
              label: "状态",
              prop: "status",
              type: "input",
              dicUrl: "/api/blade-system/dict-biz/dictionary?code=ni_sd_customer_complaint_status",
              dataType: "number",
              display:false,
              props: {
                label: "dictValue",
                value: "dictKey"
              },
            },
            {
              label: "投诉编号",
              prop: "complaintNumber",
              type: "input",
              search: true,
              disabled: true,
              minWidth: 100,
              placeholder: "投诉编号自动生成",
            },
            {
              label: "销售区域",
              prop: "salesArea",
              type: "select",
              dicData: [
                { label: '国内', value: '1' },
                { label: '国外', value: '2' }
              ],
              search: true,
              minWidth: 100,
            },
            {
              label: "投诉类型",
              prop: "complaintType",
              type: "select",
              dicUrl: "/api/blade-system/dict-biz/dictionary?code=ni_sd_customer_complaint_type",
              dataType: "number",
              props: {
                label: "dictValue",
                value: "dictKey"
              },
              rules: [{
                required: true,
                message: "请输入投诉类型",
                trigger: "blur"
              }],
            },
            {
              label: "分类",
              prop: "classification",
              type: "select",
              dicUrl: "/api/blade-system/dict-biz/dictionary?code=ni_sd_customer_complaint_classification",
              dataType: "number",
              props: {
                label: "dictValue",
                value: "dictKey"
              },
              rules: [{
                required: true,
                message: "请输入分类",
                trigger: "blur"
              }],
            },
            {
              label: "产品批号",
              prop: "prodBatchNum",
              type: "input",
              search: true,
              rules: [{
                required: true,
                message: "请输入产品批号",
                trigger: "blur"
              }],
            },
            {
              label: "投诉时间",
              prop: "complaintTime",
              type: "date",
              format: "yyyy-MM-dd hh:mm:ss",
              valueFormat: "yyyy-MM-dd hh:mm:ss",
              search: true,
              overHidden: true,
              minWidth: 100,
              rules: [{
                required: true,
                message: "请输入投诉时间",
                trigger: "blur"
              }],
            },
            {
              label: "业务经理",
              prop: "businessManager",
              type: "select",
              dicData: [],
              // props: {
              //   label: "dictValue",
              //   value: "dictKey",
              // },
              // dicUrl: "/api/blade-user/user-list?deptIds=1558250198611173378",//1648593234170556437
              props: {
                label: "account",
                value: "id",
              },
              rules: [{
                required: true,
                message: "请输入业务经理",
                trigger: "blur"
              }],
            },
            {
              label: "客户编码",
              prop: "customerCode",
              type: "input",
              search: true,
              // rules: [{
              //   required: true,
              //   message: "请输入客户编码",
              //   trigger: "blur"
              // }],
            },
            {
              label: "客户名称",
              prop: "customerName",
              type: "input",
              search: true,
              minWidth: 150,
              overHidden: true,
              rules: [{
                required: true,
                message: "请输入客户名称",
                trigger: "blur"
              }],
            },
            {
              label: "反馈报告",
              prop: "feedbackReport",
              type: "input",
              search: false,
            },
            {
              label: "投诉内容",
              prop: "complaintContent",
              type: "textarea",
              search: true,
              minWidth: 180,
              overHidden: true,
              span: 24,
              rules: [{
                required: true,
                message: "请输入投诉内容",
                trigger: "blur"
              }],
            },

            // {
            //   label: "负责人",
            //   prop: "inChargePerson",
            //   type: "input",
            //   search: true,
            //   minWidth: 60,
            //   rules: [{
            //     required: true,
            //     message: "请输入分管负责人",
            //     trigger: "blur"
            //   }],
            // },
            // {
            //   label: "处理意见",
            //   prop: "handlingOpinion",
            //   type: "textarea",
            //   rules: [{
            //     required: true,
            //     message: "请输入处理意见",
            //     trigger: "blur"
            //   }],
            // },
            {
              label: "制单人",
              prop: "createUser",
              type: "input",
              display: false,
              dicUrl:`/api/blade-user/user-list`,
              props: {
                label: "name",
                value: "id",
              },
            },
            {
              label: "制单部门",
              prop: "createDept",
              type: "input",
              display: false,
              dicUrl: `/api/blade-system/dept/list`,
              props: {
                label: 'deptName',
                value: 'id',
              },
            },
            {
              label: "创建时间",
              prop: "createTime",
              type: "input",
              display: false,
              overHidden: true,
              minWidth: 100,
            },
          ]
        },
        data: [],
        data1: [],
        itemPrintTemplate: null,
      };
    },
    computed: {
      ...mapGetters(["permission"]),
      permissionList() {
        return {
          addBtn: this.vaildData(this.permission.customerComplaint_add, false),
          viewBtn: this.vaildData(this.permission.customerComplaint_view, false),
          delBtn: this.vaildData(this.permission.customerComplaint_delete, false),
          editBtn: this.vaildData(this.permission.customerComplaint_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    created() {
      loadPrintTemplate("ni_sd_customer_complaint").then((res) => {
        this.itemPrintTemplate = JSON.parse(res.data.data.content);
      });
    },
    mounted() {
      this.dictInit();
    },
    methods: {
      //数据打印
      async handlePrint() {
        let printData;
        let hiprintTemplate;
        if (!this.itemPrintTemplate) {
          this.$message.error("打印模板加载失败，请联系管理员");
          return;
        }
        //获取打印数据
        let data = await getListMasterDetail(this.ids).then(res => {
          return res.data.data
        });

        //字典赋值
        let data1 = data.map((item) => {
          var tmp = [{solution:'空',inChargePerson:'空',processTime:'空',}]
          return {
            ...item,
            complaintType: this.complaintTypeDictKeyValue[item.complaintType],
            classification: this.classificationDictKeyValue[item.classification],
            businessManager: this.userinfoDictKeyValue[item.businessManager],
            salesArea: item.businessManager === '1'?'国内':'国外',
            items: item.items.length===0?tmp:item.items,
          };
        });
        // printData = {
        //   ...data1,
        //   // printDate: dateNow1(),
        //   // total: total,
        // }
        hiprintTemplate = new hiprint.PrintTemplate({
          template: this.itemPrintTemplate,
        });
        hiprintTemplate.print(data1);
      },

      handlePrintConfirm() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择要打印的数据");
          return;
        }
        this.$confirm("是否打印当前选择的数据？", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            this.handlePrint();
          })
      },

      handleExport1(row) {
        this.$confirm("你确定要生成 投诉编号是 " + row.complaintNumber +  " 的《产业质量问题反馈报告》吗？", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(() => {
          //获取数据
          // let data='{"投诉类型": "产品质量",\n' +
          //   '"分类": "漏气",\n' +
          //   '"投诉编号": "**********",\n' +
          //   '"产品批号":"20250288",\n' +
          //   '"投诉时间": "2025/3/25",\n' +
          //   '"业务经理": "房文强",\n' +
          //   '"客户名称": "山东艺玻璃科技有限公司",\n' +
          //   '"投诉内容":"投诉漏气"}'
          let data1={}
          data1["投诉类型"] = row.$complaintType
          data1["分类"] = row.$classification
          data1["投诉编号"] = row.complaintNumber
          data1["产品批号"] = row.prodBatchNum
          data1["投诉时间"] = row.complaintTime
          data1["业务经理"] = row.$businessManager
          data1["客户名称"] = row.customerName
          data1["投诉内容"] = row.complaintContent
          this.loading = true
          let data2 = JSON.stringify(data1);
          let data3 = qualityFeedbackForm(row.id, data2).then((res) => {
            let link = res.data.data.link;
            let fileName = res.data.data.originalName;
            this.handleDownload(link, fileName)
            this.onLoad(this.page);
            this.loading = false
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          }, error => {
            this.loading = false
            loading();
            console.log(error);
          });
        });
        this.loading = false
      },
      //文件下载
      async handleDownload(fileUrl, fileName) {
        try {
          const response = await fetch(fileUrl);
          if (!response.ok) {
            throw new Error("链接请求失败");
          }
          const blob = await response.blob();
          const url = window.URL.createObjectURL(blob);

          // 创建一个<a>标签
          const link = document.createElement("a");
          link.href = url;
          link.target = "_blank"; // 在新窗口中打开文件
          link.download = fileName; // 使用文件名称进行下载
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);

          // 释放URL对象
          window.URL.revokeObjectURL(url);
        } catch (error) {
          console.error(error);
        }
      },

      handleExport() {
        this.$confirm("是否导出当前筛选的所有数据？", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(() => {
          this.handleExportData();
        });
      },
      // 数据导出
      async handleExportData() {
        let opt = {
          column: [
            // {
            //   label: "状态",
            //   prop: "statusName",
            // },
            {
              label: "投诉编号",
              prop: "complaintNumber",
            },
            {
              label: "销售区域",
              prop: "salesArea",
            },
            {
              label: "投诉类型",
              prop: "complaintType",
            },
            {
              label: "分类",
              prop: "classification",
            },
            {
              label: "产品批号",
              prop: "prodBatchNum",
            },
            {
              label: "投诉时间",
              prop: "complaintTime",
            },
            {
              label: "业务经理",
              prop: "businessManager",
            },
            {
              label: "客户编码",
              prop: "customerCode",
            },
            {
              label: "客户名称",
              prop: "customerName",
            },
            {
              label: "投诉内容",
              prop: "complaintContent",
            },
          ],
        };
        await this.getExportData();
        this.$Export.excel({
          title: "客户投诉记录",
          columns: opt.column,
          data: this.exportData.map((item) => {
            return {
              ...item,
              // statusName: this.statusDictKeyValue[item.status],
              // inspectorName: this.expressTypeDictKeyValue[item.inspector],
              // expressCompanyName: this.expressCompanyDictKeyValue[item.expressCompany],
              // paymentTypeDictName: this.paymentTypeDictKeyValue[item.paymentType],
              // expressTypeName: this.expressTypeDictKeyValue[item.expressType],
            };
          }),
        });
        this.exportData = [];
      },
      //获取搜索的打印数据
      async getExportData() {
        const promises = [];
        this.exportData = [];
        for (var i = 1; i <= this.page.total / this.page.pageSize + 1; i++) {
          const promise = getList(i, this.page.pageSize, {
            ...this.params,
            ...this.query,
          }).then((res) => {
            const data = res.data.data.records;
            this.exportData = this.exportData.concat(data);
          });

          promises.push(promise);
        }
        // 等待所有异步请求完成
        await Promise.all(promises);
        // console.log(this.exportData)
        return this.exportData;
      },
      handleDataSub(row) {
        this.currenRow = row;
        let params = {"parentId":row.id};
        this.query1 = params;
        this.page1.currentPage = 1;
        this.onLoadDetail(this.page1);
        this.drawer = true;
      },
      handleSubClose(hide) {
        this.drawer = false;
        hide();
      },
      handleApply() {
        this.dynamicRoute(
          {
            processDefKey: this.processDefKey,
            formKey: this.formKey,
          },
          "start"
        );
      },
      rowAttach(row) {
        if (row.status > 0) {
          this.attachDetail = true;
        } else {
          this.attachDetail = false;
        }
        this.$refs.attachDialogRef.init(row.id, this.module);
      },
      dictInit() {
         this.$http
           //国内销售部和国外销售部
          .get("/api/blade-user/user-list?deptIds=1558250198611173378,1558250278609133570")
          .then((res) => {
            const column = this.findObject(this.option.column, "businessManager");
            let dt1 = res.data.data;
            let dt2=[];
            for (let i = 0; i < dt1.length; i++) {
                //岗位是国内销售经理和国外销售经理
                if (dt1[i].postId === '1648593234170556437' || dt1[i].postId === '1648593234170556441') {
                  dt2.push(dt1[i])
                }
            }
            column.dicData = dt2
          });


          this.$http
            .get("/api/blade-system/dict-biz/dictionary?code=ni_sd_customer_complaint_type")
            .then((res) => {
              const column = this.findObject(this.option.column, "complaintType");
              column.dicData = res.data.data;
              this.complaintTypeDict = res.data.data;
              this.complaintTypeDictKeyValue = this.complaintTypeDict.reduce((acc, cur) => {
                acc[cur.dictKey] = cur.dictValue;
                return acc;
              }, {});
              console.log('complaintType:',this.classificationDictKeyValue)
            });
        this.$http
          .get("/api/blade-system/dict-biz/dictionary?code=ni_sd_customer_complaint_classification")
          .then((res) => {
            const column = this.findObject(this.option.column, "classification");
            column.dicData = res.data.data;
            this.classificationDict = res.data.data;
            this.classificationDictKeyValue = this.classificationDict.reduce((acc, cur) => {
              acc[cur.dictKey] = cur.dictValue;
              return acc;
            }, {});
            console.log('classification:',this.classificationDictKeyValue)
          });

        this.$http//api/blade-user/user-list?deptIds=1558250198611173378,1558250278609133570"
          .get("/api/blade-user/user-list")
          .then((res) => {
            console.log(res)
            // const column = this.findObject(this.option.column, "classification");
            // column.dicData = res.data.data;
            this.userInfoDict = res.data.data;
            this.userinfoDictKeyValue = this.userInfoDict.reduce((acc, cur) => {
              acc[cur.id] = cur.account;
              return acc;
            }, {});
            console.log('userinfoDict',this.userinfoDictKeyValue)
          });
      },

      rowSave(row, done, loading) {
        add(row).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          loading();
          window.console.log(error);
        });
      },
      rowUpdate(row, index, done, loading) {
        update(row).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          loading();
          console.log(error);
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1;
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange1(currentPage){
        this.page1.currentPage = currentPage;
        this.onLoadDetail(this.page1);
      },
      sizeChange1(pageSize){
        this.page1.pageSize = pageSize;
        this.onLoadDetail(this.page1);
      },

      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      refreshChange() {
        this.onLoad(this.page, this.query);
      },
      onLoad(page, params = {}) {
        this.loading = true;
        getList(page.currentPage, page.pageSize, Object.assign(params, this.query, {descs:'id'})).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      },

      onLoadDetail(page, params = {}) {
        params = {'parentId':this.currenRow.id};
        this.loading1 = true;
        getListDetail(page.currentPage, page.pageSize, Object.assign(params, this.query, {desc:'id'})).then(res => {
          const data = res.data.data;
          this.page1.total = data.total;
          this.data1 = data.records;
          this.loading1 = false;
        });
      },
    }
  };
</script>

<style>

</style>

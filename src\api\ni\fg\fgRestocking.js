import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/ni/fg/restocking/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/ni/fg/restocking/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/ni/fg/restocking/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/ni/fg/restocking/save',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/ni/fg/restocking/update',
    method: 'post',
    data: row
  })
}

export const audit = (id) => {
  return request({
    url: '/api/ni/fg/restocking/audit/' + id,
    method: 'post',
  })
}

export const red = (id, reason) => {
  return request({
    url: '/api/ni/fg/restocking/red/' + id,
    method: 'post',
    params: {
      reason,
    }
  })
}


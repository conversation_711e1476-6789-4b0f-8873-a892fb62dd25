<template>
  <basic-container>
    <avue-skeleton :loading="waiting" avatar :rows="8">
      <div style="display: flex">
        <avue-title
          style="margin-bottom: 20px"
          :styles="{ fontSize: '20px' }"
          :value="process.name"
        ></avue-title>
        <el-badge
          v-if="permission.wf_process_draft && draftCount > 0"
          :value="draftCount"
          style="margin-top: 5px; margin-right: 40px"
          type="warning"
        >
          <el-button size="mini" v-loading="loading" @click="handleDraftBox"
            >草稿箱
          </el-button>
        </el-badge>
      </div>
      <el-card shadow="never" style="margin-top: 20px">
        <!-- 自定义表单区域 -->
        <avue-form
          v-if="
            option &&
            ((option.column && option.column.length > 0) ||
              (option.group && option.group.length > 0))
          "
          v-model="form"
          ref="form"
          :option="option"
          :defaults.sync="defaults"
          @submit="handleSubmit"
          @error="loading = false"
          :upload-preview="handleUploadPreview"
        >
          <template #eaPersonId="{ size, disabled, index }">
            <user-select1
              v-model="form.eaPersonId"
              :size="size"
              :disabled="disabled"
              userUrl="/api/blade-user/search/user"
            />
          </template>
          <template #financeAccount="{ size, disabled }">
            <bank-card-input
              v-model="form.financeAccount"
              :size="size"
              :disabled="disabled"
            />
          </template>
          <template #budgetId="{ size, disabled }">
            <un-finish-budget-select
              v-model="form.budgetId"
              :size="size"
              :params="{ brand: form.brand }"
              placeholder=" "
              @clear="handleBudgetClear"
              @confirm="handleBudgetConfirm"
            />
          </template>
          <template #loanId="{ size, disabled, index }">
            <fin-loan-select
              v-model="form.loanId"
              :size="size"
              :disabled="disabled"
              @confirm="handleLoanSelect"
            />
          </template>
          <!-- 结算差额 -->
          <template #differenceAmount>
            <div
              style="
                margin: 20px 0;
                color: red;
                font-weight: bold;
                font-size: 16px;
              "
            >
              结算差额: {{ form.differenceAmount }}
            </div>
          </template>
          <template #items="{ index, size }">
            <cost-ea-item
              v-model="form.items"
              :budget-id="form.budgetId"
              :cost-apply-id="form.costApplyId"
              @amountChange="handleSumAmount"
              @numChange="handleSumNum"
              @attachClick="rowAttach"
            />
          </template>
        </avue-form>
        <!-- 自定义表单区域 -->
      </el-card>

      <el-card shadow="never" style="margin-top: 20px" v-if="showExamForm">
        <wf-examine-form
          ref="examineForm"
          :process="process"
          @user-select="handleUserSelect"
        ></wf-examine-form>
      </el-card>
      <div style="height: 120px"></div>
      <el-row
        class="foot-item avue-affix"
        :style="{
          width: isCollapse ? 'calc(100% - 80px)' : 'calc(100% - 260px)',
        }"
        id="avue-view"
      >
        <el-button
          v-no-more-click
          type="primary"
          size="mini"
          v-loading="loading"
          @click="handleSubmit"
          >发起
        </el-button>
        <el-button
          v-if="permission.wf_process_draft"
          type="success"
          size="mini"
          v-loading="loading"
          @click="
            handleDraftNotClose(process.id, process.formKey, form, process.key)
          "
          >存为草稿
        </el-button>
        <el-button
          v-if="permission.wf_process_draft"
          type="success"
          size="mini"
          v-loading="loading"
          @click="handleDraft(process.id, process.formKey, form, process.key)"
          >存为草稿并关闭
        </el-button>
      </el-row>
    </avue-skeleton>

    <!-- 人员选择弹窗 -->
    <wf-user-select
      ref="user-select"
      :check-type="checkType"
      :default-checked="defaultChecked"
      @onConfirm="handleUserSelectConfirm"
    ></wf-user-select>
    <!-- 草稿弹窗 -->
    <draft-popup
      :visible.sync="isDraftPopupVisible"
      :draftList="draftList"
      @select="handleDraftSelect"
      @delete="handleDraftDelete"
    ></draft-popup>
    <attach-dialog
      ref="attachRef"
      code="public"
      :delBtn="false"
      @close="handleRefreshAttach"
    />
  </basic-container>
</template>

<script>
import WfExamineForm from "@/views/plugin/workflow/process/components/examForm.vue";
import WfUserSelect from "@/views/plugin/workflow/process/components/user-select";
import UserSelect1 from "@/components/user-select";
import UserSelect from "@/components/user-select";

import exForm from "@/views/plugin/workflow/mixins/ex-form";
import draft from "@/views/plugin/workflow/mixins/draft";
import FinLoanSelect from "@/views/ni/fin/components/FinLoanSelect";
import UnFinishBudgetSelect from "@/views/ni/por/components/UnFinishBudgetSelect";
import CostEaItem from "@/views/ni/fin/components/CostEaItemAdd";
import AttachDialog from "@/components/attach-dialog";
import { getList as getAttachList } from "@/api/resource/attach";
import BankCardInput from "@/components/bank-card-input";
import CostApplySelect from "@/views/ni/fin/components/CostApplySelect";
import DraftPopup from "@/views/plugin/workflow/process/components/draftPopup.vue";
import debounce from "@/util/debounce";

export default {
  components: {
    WfUserSelect,
    WfExamineForm,
    UserSelect,
    FinLoanSelect,
    UnFinishBudgetSelect,
    CostEaItem,
    AttachDialog,
    BankCardInput,
    CostApplySelect,
    UserSelect1,
    DraftPopup,
  },
  mixins: [exForm, draft],
  activated() {
    let val = this.$route.query.p;
    if (val) {
      const param = JSON.parse(Buffer.from(val, "base64").toString());
      const { processId, processDefKey, form } = param;
      if (processId) {
        this.getForm(processId);
      } else if (processDefKey) {
        this.getFormByProcessDefKey(processDefKey);
      }
      if (form) {
        Object.assign(this.form, form);
      }
    }
  },
  computed: {
    showExamForm() {
      const { hideComment, hideCopy, hideExamine } = this.process;
      return !hideComment || !hideCopy || !hideExamine;
    },
  },
  watch: {
    //监听借款金额和报销总金额（这俩金额变动的时候结算差额也跟着变化）
    "form.loanAmount": {
      handler(newVal) {
        const loanAmount = parseFloat(newVal) || 0;
        const reimbursementAmount = parseFloat(this.form.amount) || 0;
        this.form.differenceAmount = (loanAmount - reimbursementAmount).toFixed(
          2
        );
      },
      immediate: true,
    },
    "form.amount": {
      handler(newVal) {
        const loanAmount = parseFloat(this.form.loanAmount) || 0;
        const reimbursementAmount = parseFloat(newVal) || 0;
        this.form.differenceAmount = (loanAmount - reimbursementAmount).toFixed(
          2
        );
      },
      immediate: true,
    },
    //监听冲抵借款按钮（不是借款不显示结算差额）
    "form.loan": {
      handler(newVal) {
        console.log(newVal);
        if (newVal) {
          const differenceAmount = this.findObject(
            this.option.column,
            "differenceAmount"
          );
          differenceAmount.display = true;
        } else {
          const differenceAmount = this.findObject(
            this.option.column,
            "differenceAmount"
          );
          differenceAmount.display = false;
        }
      },
      immediate: true,
    },
  },
  data() {
    return {
      defaults: {},
      form: {
        eaPersonId: "",
        items: [],
      },
      option: {
        span: 8,
        labelWidth: 120,
        calcHeight: 30,
        size: "mini",
        menuBtn: false,
        column: [
          {
            type: "input",
            label: "申请人",
            display: true,
            prop: "createUserName",
            value: "${this.$store.getters.userInfo.nick_name}",
            readonly: true,
            span: 8,
          },
          {
            type: "input",
            label: "申请部门",
            display: true,
            row: true,
            prop: "createDeptName",
            value: "${this.$store.getters.userInfo.dept_name}",
            readonly: true,
            span: 8,
          },
          {
            label: "报销人",
            prop: "eaPersonId",
            placeholder: " ",
            filterable: true,
            span: 8,
            rules: [
              {
                required: true,
                message: "请选择报销人",
                trigger: "blur",
              },
            ],
          },
          {
            label: "报销类型",
            prop: "type",
            type: "select",
            placeholder: " ",
            span: 8,
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_fin_loan_type",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            rules: [
              {
                required: true,
                message: "请选择报销类型",
                trigger: "blur",
              },
            ],
            control: (val) => {
              if (val === "4") {
                return {
                  porOrderIdList: {
                    display: true,
                  },
                };
              } else {
                return {
                  porOrderIdList: {
                    display: false,
                  },
                };
              }
            },
          },
          {
            label: "账套",
            prop: "brand",
            type: "radio",
            dicUrl: "/api/blade-system/dict-biz/dictionary?code=ni_brand",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            value: "1",
            span: 8,
            rules: [
              {
                required: true,
                message: "请选择账套",
                trigger: "blur",
              },
            ],
          },
          {
            label: "关联预算",
            prop: "budgetId",
            span: 8,
            rules: [
              {
                required: true,
                message: "请选择关联预算",
                trigger: "blur",
              },
            ],
          },
          {
            label: "冲抵借款",
            prop: "loan",
            type: "switch",
            value: false,
            span: 8,
            dicData: [
              {
                label: "否",
                value: false,
              },
              {
                label: "是",
                value: true,
              },
            ],
            // disabled: true,
            rules: [
              {
                required: true,
                message: "请选择是否冲抵借款",
                trigger: "blur",
              },
            ],
            control: (val) => {
              if (val) {
                return {
                  loanId: {
                    display: true,
                  },
                  // loanType: {
                  //   display: true,
                  // },
                  loanAmount: {
                    display: true,
                  },
                };
              } else {
                return {
                  loanId: {
                    display: false,
                  },
                  // loanType: {
                  //   display: false,
                  // },
                  loanAmount: {
                    display: false,
                  },
                };
              }
            },
          },
          {
            label: "关联借款单",
            prop: "loanId",
            type: "input",
            display: false,
            span: 8,
            rules: [
              {
                required: true,
                message: "请选择关联借款单",
                trigger: "blur",
              },
            ],
          },
          // {
          //   label: "借款类型",
          //   prop: "loanType",
          //   type: "select",
          //   placeholder: " ",
          //   dicUrl:
          //     "/api/blade-system/dict-biz/dictionary?code=ni_fin_loan_type",
          //   props: {
          //     label: "dictValue",
          //     value: "dictKey",
          //   },
          //   display: false,
          //   span: 8,
          //   disabled: true,
          // },
          {
            label: "借款金额",
            prop: "loanAmount",
            type: "number",
            placeholder: " ",
            precision: 2,
            span: 8,
            display: false,
            disabled: true,
          },
          {
            label: "报销金额",
            prop: "amount",
            placeholder: " ",
            type: "number",
            precision: 2,
            span: 8,
            rules: [
              {
                required: true,
                message: "请输入报销金额",
                trigger: "blur",
              },
            ],
          },
          {
            label: "付款方式",
            prop: "paymentType",
            type: "select",
            minWidth: 85,
            span: 8,
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_fin_payment_type",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            placeholder: " ",
            rules: [
              {
                required: true,
                message: "请选择付款方式",
                trigger: "blur",
              },
            ],
            control: (val) => {
              if (["1", "5"].includes(val)) {
                return {
                  financeBank: {
                    display: true,
                  },
                  financeAccount: {
                    display: true,
                  },
                };
              } else {
                return {
                  financeBank: {
                    display: false,
                  },
                  financeAccount: {
                    display: false,
                  },
                };
              }
            },
          },
          {
            label: "票据张数",
            prop: "invoiceNum",
            placeholder: " ",
            type: "number",
            precision: 0,
            span: 8,
            rules: [
              {
                required: true,
                message: "请输入票据张数",
                trigger: "blur",
              },
            ],
          },
          {
            label: "报销人银行",
            prop: "financeBank",
            display: false,
            span: 8,
            rules: [
              {
                required: true,
                message: "请输入报销人银行",
                trigger: "blur",
              },
            ],
          },
          {
            label: "报销人银行账号",
            prop: "financeAccount",
            placeholder: " ",
            span: 8,
            display: false,
            rules: [
              {
                required: true,
                message: "请输入银行账号",
                trigger: "blur",
              },
            ],
          },
          {
            label: "用途",
            prop: "remark",
            placeholder: " ",
            hide: true,
            type: "textarea",
            span: 24,
            minRows: 2,
            rules: [
              {
                required: true,
                message: "请输入用途",
                trigger: "blur",
              },
            ],
          },
          {
            label: "附件",
            type: "upload",
            propsHttp: {
              res: "data",
              url: "attachId",
              name: "originalName",
            },
            action: "/api/blade-resource/oss/endpoint/put-file-attach",
            display: true,
            hide: true,
            showColumn: false,
            span: 24,
            showFileList: true,
            multiple: true,
            prop: "attachment",
          },
          {
            label: "",
            prop: "differenceAmount",
            display: false,
            labelWidth: 50,
          },
          {
            label: "报销明细",
            prop: "items",
            labelPosition: "top",
            span: 24,
          },
        ],
      },
      process: {},
      loading: false,
      payment: 0,
      fromPath: "",
      actionItem: null,
      actionItemIndex: null,
      isDraftPopupVisible: false,
      draftList: [],
      draftCount: 0,
      draftId: null,
    };
  },
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      //  这里的vm指的就是vue实例，可以用来当做this使用
      vm.fromPath = from.path; //获取上一级路由的路径
    });
  },
  methods: {
    rowAttach(row, index) {
      this.actionItem = row;
      this.actionItemIndex = index;
      this.$refs.attachRef.init(row.id, "ni_fin_cost_ea");
    },
    handleRefreshAttach() {
      getAttachList({
        businessName: "ni_fin_cost_ea",
        businessKey: this.actionItem.id,
      }).then((res) => {
        const data = res.data.data;
        this.actionItem.attach = data.map((item) => {
          return {
            label: item.originalName,
            value: item.id,
          };
        });
        this.form.items.splice(this.actionItemIndex, 1, { ...this.actionItem });
      });
    },
    handleSumAmount(amount) {
      this.form.amount = amount;
    },
    handleSumNum(num) {
      this.form.invoiceNum = num;
    },
    handleBudgetConfirm(selectionList) {
      if (selectionList) {
        this.form.budgetSerialNo = selectionList[0].serialNo;
        this.form.budgetType = selectionList[0].type;
      }
    },
    handleBudgetClear() {
      this.form.budgetId = null;
      this.form.items = [];
    },
    handleLoanSelect(selectList, totalAmount) {
      if (selectList && selectList.length > 0) {
        this.form.loanAmount = totalAmount;
        this.form.loanDetails = selectList.map((item) => ({
          id: item.id,
          reason: item.reason,
          amount: item.amount,
        }));
        this.form.differenceAmount = (
          parseFloat(totalAmount) - parseFloat(this.form.amount || 0)
        ).toFixed(2);
      } else {
        this.form.loanAmount = 0;
        this.form.loanDetails = [];
        this.form.differenceAmount = 0 - parseFloat(this.form.amount || 0);
      }
    },
    getForm(processId) {
      this.getStartForm(processId).then((res) => {
        const _this = this;
        let { process } = res;
        this.form.processId = process.id;
        const option = _this.option;
        const { column, group } = option;
        column.forEach((col) => {
          if (col.value) col.value = _this.getDefaultValues(col.value);
        });
        if (group && group.length > 0) {
          // 处理group
          group.forEach((gro) => {
            gro.column.forEach((col) => {
              if (col.value) col.value = _this.getDefaultValues(col.value);
            });
          });
        }
        if (_this.permission.wf_process_draft) {
          // 查询草稿箱
          this.initDraft(process.id, process.key).then((data) => {
            this.draftCount = data.length;
            this.draftList = data;
            if (data && Array.isArray(data) && data.length > 0) {
              _this
                .$confirm("是否引用之前保存的草稿？", "提示", {})
                .then(() => {
                  this.isDraftPopupVisible = true; // 打开草稿弹窗
                });
            }
          });
        }
        _this.waiting = false;
      });
    },
    getFormByProcessDefKey(processDefKey) {
      this.getStartFormByProcessDefKey(processDefKey).then((res) => {
        const _this = this;
        let { process } = res;
        _this.form.processId = process.id;
        const option = _this.option;
        const { column, group } = option;
        column.forEach((col) => {
          if (col.value) col.value = _this.getDefaultValues(col.value);
        });
        if (group && group.length > 0) {
          // 处理group
          group.forEach((gro) => {
            gro.column.forEach((col) => {
              if (col.value) col.value = _this.getDefaultValues(col.value);
            });
          });
        }
        if (_this.permission.wf_process_draft) {
          // 查询草稿箱
          this.initDraft(process.id, process.key).then((data) => {
            this.draftCount = data.length;
            this.draftList = data;
            if (data && Array.isArray(data) && data.length > 0) {
              _this
                .$confirm("是否引用之前保存的草稿？", "提示", {})
                .then(() => {
                  this.isDraftPopupVisible = true; // 打开草稿弹窗
                });
            }
          });
        }
        _this.waiting = false;
      });
    },
    handleSubmit: debounce(function () {
      //保存再提交
      if (this.form.inquiry) {
        this.form.inquiryState = 1;
      }
      this.form.porState = 1;
      if (this.form.items && this.form.items.length > 0) {
        //校验编码
        const unSetNumAmount = this.form.items.some(
          (item) =>
            !item.num || item.num <= 0 || !item.amount || item.amount <= 0
        );
        if (unSetNumAmount) {
          this.$message.warning("有未设置票数/金额的费用，请设置后再提交");
          this.loading = false;
          return;
        }
      }
      if (!this.form.loan) {
        this.form.differenceAmount = null;
      }
      this.form.draftId = this.draftId;
      this.handleStartProcess(true)
        .then((done) => {
          this.$message.success("发起成功");
          if (this.draftId != null) {
            this.draftCount = this.draftCount - 1;
            this.draftList = this.draftList.filter(
              (item) => item.id !== this.draftId
            );
          }
          if (this.fromPath) {
            this.handleCloseTag(this.fromPath);
          } else this.handleCloseTag("/plugin/workflow/process/send");
          done();
        })
        .catch(() => {
          this.loading = false;
        });
    }, 1000),

    //选择草稿
    handleDraftSelect(selectedDraft) {
      //草稿版本与流程版本不一致
      if (!selectedDraft.sameVersion) {
        this.$confirm(
          "选中的草稿与当前流程版本不一致，是否继续引用？",
          "提示",
          {}
        ).then(() => {
          this.draftId = selectedDraft.id;
          this.form = JSON.parse(selectedDraft.variables);
          this.isDraftPopupVisible = false;
        });
      } else {
        this.draftId = selectedDraft.id;
        this.form = JSON.parse(selectedDraft.variables);
      }
    },
    //删除草稿
    handleDraftDelete(draftId) {
      this.$confirm("是否删除选中的草稿箱数据？", "提示", {}).then(() => {
        this.$axios
          .post(`/api/blade-workflow/process/draft/delete/${draftId}`)
          .then((response) => {
            this.$message.success("草稿删除成功");
            this.draftCount = this.draftCount - 1;
            this.draftList = this.draftList.filter(
              (item) => item.id !== draftId
            );
          })
          .catch((error) => {
            this.$message.error("草稿删除失败，请重试");
          });
      });
    },
    handleDraftBox() {
      if (this.draftList.length > 0) {
        this.isDraftPopupVisible = true;
      } else {
        // 重新获取草稿数据
        this.initDraft(this.form.processId).then((data) => {
          this.draftCount = data.length;
          this.draftList = data;
          if (data && Array.isArray(data) && data.length > 0) {
            this.isDraftPopupVisible = true;
          }
        });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.foot-item {
  position: fixed;
  bottom: 0;
  margin-left: -20px;
  // right: 0;
  z-index: 101;
  height: 66px;
  background-color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  -webkit-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
</style>

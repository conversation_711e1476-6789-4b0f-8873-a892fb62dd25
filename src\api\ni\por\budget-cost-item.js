import request from "@/router/axios";

export const getPage = (current, size, params, deptId) => {
  return request({
    url: "/api/ni/por/budget/cost-item/page",
    method: "get",
    params: {
      ...params,
      deptId,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/ni/por/budget/cost-item/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const remove = (ids) => {
  return request({
    url: "/api/ni/por/budget/cost-item/remove",
    method: "post",
    params: {
      ids,
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/ni/por/budget/cost-item/submit",
    method: "post",
    data: row,
  });
};

export const update = (row) => {
  return request({
    url: "/api/ni/por/budget/cost-item/submit",
    method: "post",
    data: row,
  });
};
export const getList = (params) => {
  return request({
    url: "/api/ni/por/budget/cost-item/list",
    method: "get",
    params,
  });
};

export const getListWithRepair = (budgetId) => {
  return request({
    url: "/api/ni/por/budget/cost-item/listWithRepair",
    method: "get",
    params: {
      budgetId,
    },
  });
};

export const getOverPage = (current, size, params) => {
  return request({
    url: "/api/ni/por/budget/cost-item/overPage",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};
export const getOverCount = (params) => {
  return request({
    url: "/api/ni/por/budget/cost-item/overCount",
    method: "get",
    params,
  });
};

export const overRemark = (id, overRemark) => {
  return request({
    url: "/api/ni/por/budget/cost-item/overRemark",
    method: "post",
    data: {
      id,
      overRemark,
    },
  });
};
export const overAudit = (id) => {
  return request({
    url: "/api/ni/por/budget/cost-item/overAudit",
    method: "post",
    params: {
      id,
    },
  });
};

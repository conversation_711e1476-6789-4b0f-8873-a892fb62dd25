import request from '@/router/axios';

export const getList = (current, size, params) => {
    return request({
        url: '/api/ni/fg/inbound/list',
        method: 'get',
        params: {
            ...params,
            current,
            size,
        }
    })
}

export const getDetail = (id) => {
    return request({
        url: '/api/ni/fg/inbound/detail',
        method: 'get',
        params: {
            id
        }
    })
}

export const remove = (ids) => {
    return request({
        url: '/api/ni/fg/inbound/remove',
        method: 'post',
        params: {
            ids,
        }
    })
}
export const audit = (ids) => {
  return request({
    url: '/api/ni/fg/inbound/audit',
    method: 'post',
    params: {
      ids,
    }
  })
}
export const changeItemRemark = (ids, remark) => {
  return request({
    url: '/api/ni/fg/inbound/changeItemRemark',
    method: 'post',
    params: {
      ids,
      remark,
    }
  })
}
export const add = (row) => {
    return request({
        url: '/api/ni/fg/inbound/save',
        method: 'post',
        data: row
    })
}

export const update = (row) => {
    return request({
        url: '/api/ni/fg/inbound/update',
        method: 'post',
        data: row
    })
}

export const red = (id, reason) => {
    return request({
        url: '/api/ni/fg/inbound/red/' + id,
        method: 'post',
        params: {
            reason,
        }
    })
}
export const penmaSync = (startDate, endDate) => {
    return request({
        url: '/api/ni/fg/inbound/penmaSync',
        method: 'post',
        params: {
            startDate,endDate
        }
    })
}
export const productionBatchSync = (startDate, endDate,skuIds) => {
  return request({
    url: "/api/ni/fg/inbound/productionBatchSync",
    method: "post",
    params: {
      startDate,
      endDate,
      skuIds
    },
  });
};
export const productionBatchSync1 = (startDate, endDate,skuIds) => {
  return request({
    url: "/api/ni/fg/inbound/v1/productionBatchSync",
    method: "post",
    params: {
      startDate,
      endDate,
      skuIds
    },
  });
};

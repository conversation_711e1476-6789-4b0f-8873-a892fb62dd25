```java
// 定义包路径，属于问题反馈模块的服务实现层
package com.natergy.ni.feedback.service.impl;

// 导入FastJSON相关类，用于JSON序列化和反序列化
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.PropertyNamingStrategy;
import com.alibaba.fastjson.serializer.SerializeConfig;
// 导入MyBatis-Plus的查询包装类
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
// 导入当前模块的DTO、实体、参数和VO类
import com.natergy.ni.feedback.dto.*;
import com.natergy.ni.feedback.entity.FeedbackEntity;
import com.natergy.ni.feedback.entity.FeedbackSolvingRecordEntity;
import com.natergy.ni.feedback.params.FeedbackParams;
import com.natergy.ni.feedback.vo.FeedbackVO;
// 导入当前模块的Mapper接口
import com.natergy.ni.feedback.mapper.FeedbackSolvingRecordMapper;
import com.natergy.ni.feedback.mapper.FeedbackMapper;
import com.natergy.ni.feedback.service.IFeedbackService;
// 导入Apache的字符串工具类
import org.apache.commons.lang3.StringUtils;
// 导入BladeX框架的REST模板工具类，用于发送HTTP请求
import org.springblade.common.utils.RestTemplateUtil;
// 导入BladeX框架的基础服务实现类
import org.springblade.core.mp.base.BaseServiceImpl;
// 导入Spring的注解
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Service;
// 导入MyBatis-Plus的分页接口
import com.baomidou.mybatisplus.core.metadata.IPage;

// 导入Java工具类和并发相关类
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * 问题反馈 服务实现类
 *
 * <AUTHOR>
 * @since 2025-02-26
 */
// @Service：Spring注解，标识该类为服务层组件，由Spring管理
@Service
// 继承BaseServiceImpl，实现IFeedbackService接口，泛型为Mapper接口和实体类
public class FeedbackServiceImpl extends BaseServiceImpl<FeedbackMapper, FeedbackEntity> implements IFeedbackService {

	// 从配置文件中注入Dify服务的同步URL（用于同步数据到向量数据库）
	@Value("${dify.feedback.synchronous-url}")
	private String synchronousUrl;

	// 从配置文件中注入Dify服务的文本搜索URL（用于搜索相似问题）
	@Value("${dify.feedback.search-text-url}")
	private String searchTextUrl;

	// 定义字符串分隔符（用于多值字段的拆分和拼接）
	private final static String SEPARATOR = ",";

	/**
	 * 搜索相似问题的集合名称（向量数据库中的集合名）
	 */
	private final static String COLLECTION_NAME = "ten_not_pass_issues";

	/**
	 * 搜索相似问题的相似度阈值（低于该值的结果将被过滤）
	 */
	private final static Float SIMILARITY_THRESHOLD = 0.65f;

	// 注入问题解决记录的Mapper接口，用于查询解决记录
	private final FeedbackSolvingRecordMapper feedbackSolvingRecordMapper;

	// 构造函数注入，Spring自动注入FeedbackSolvingRecordMapper
	public FeedbackServiceImpl(FeedbackSolvingRecordMapper feedbackSolvingRecordMapper) {
		this.feedbackSolvingRecordMapper = feedbackSolvingRecordMapper;
	}

	/**
	 * 条件分页查询问题反馈列表
	 */
	@Override
	public IPage<FeedbackVO> selectFeedbackPage(IPage<FeedbackVO> page, FeedbackParams feedbackParams, String description, String userIdsStr) {
		// 调用Mapper层的自定义分页查询方法，设置查询结果并返回分页对象
		return page.setRecords(baseMapper.selectFeedbackPage(page, feedbackParams, description, userIdsStr));
	}

	/**
	 * 异步同步数据到Qdrant向量数据库
	 */
	@Override
	public CompletableFuture<Void> asyncQdrant(FeedbackEntity feedbackEntity) {
		// 使用CompletableFuture.runAsync创建异步任务，执行同步数据方法
		return CompletableFuture.runAsync(() -> synchronizeData(feedbackEntity));
	}

	/**
	 * 同步数据到Qdrant向量数据库（核心逻辑）
	 */
	@Override
	public void synchronizeData(FeedbackEntity feedbackEntity) {
		// 查询当前问题反馈对应的所有解决记录
		List<FeedbackSolvingRecordEntity> feedbackSolvingRecordList = feedbackSolvingRecordMapper.selectList(
			new LambdaQueryWrapper<FeedbackSolvingRecordEntity>()
				.eq(FeedbackSolvingRecordEntity::getFeedbackId, feedbackEntity.getId())
		);

		// 合并所有解决记录的"问题原因"（非空值），用分号分隔
		String problemCause = feedbackSolvingRecordList.stream()
			.map(FeedbackSolvingRecordEntity::getProblemCause)
			.filter(StringUtils::isNotBlank)
			.collect(Collectors.joining(";"));

		// 合并所有解决记录的"解决方案"（非空值），用分号分隔
		String solution = feedbackSolvingRecordList.stream()
			.map(FeedbackSolvingRecordEntity::getSolution)
			.filter(StringUtils::isNotBlank)
			.collect(Collectors.joining(";"));

		// 配置FastJSON的序列化策略：将Java的小驼峰命名转换为JSON的蛇形命名（如userId → user_id）
		SerializeConfig config = new SerializeConfig();
		config.propertyNamingStrategy = PropertyNamingStrategy.SnakeCase;

		// 将负责人姓名字符串拆分为列表
		List<String> responsibilityNameList = Arrays.stream(feedbackEntity.getResponsibilityName().split(SEPARATOR))
			.collect(Collectors.toList());

		// 将部门名称字符串拆分为列表
		List<String> deptNames = Arrays.asList(feedbackEntity.getDeptNames().split(SEPARATOR));

		// 构建Qdrant向量数据库的传输对象（QdrantPostDTO）
		QdrantPostDTO qdrantPostDTO = new QdrantPostDTO(
			feedbackEntity.getFinalResolutionTime().toString(),  // 问题解决日期
			deptNames,                                         // 责任部门列表
			feedbackEntity.getDescription(),                   // 问题描述
			responsibilityNameList,                            // 负责人列表
			feedbackEntity.getIsTenNonNeglect() ? "十不放过" : StringUtils.EMPTY,  // 问题类别（是否属于"十不放过"）
			problemCause,                                      // 合并的问题原因
			solution                                           // 合并的解决方案
		);

		// 将QdrantPostDTO序列化为JSON字符串（使用蛇形命名）
		String dataJson = JSON.toJSONString(qdrantPostDTO, config);

		// 调用REST工具类发送POST请求，同步数据到Qdrant向量数据库
		ResponseEntity<QdrantResponseDTO> responseEntity = RestTemplateUtil.post(
			synchronousUrl,          // 目标URL（从配置文件注入）
			MediaType.APPLICATION_JSON,  // 请求类型为JSON
			QdrantResponseDTO.class,     // 响应数据类型
			dataJson                     // 请求体（JSON字符串）
		);

		// 获取响应结果，并校验非空
		QdrantResponseDTO qdrantResponseDTO = responseEntity.getBody();
		Objects.requireNonNull(qdrantResponseDTO, "智能体响应异常");  // 若为空则抛出异常

		// 更新问题反馈实体的向量数据库点ID（pointId），并保存
		feedbackEntity.setPointId(qdrantResponseDTO.getId());
		this.updateById(feedbackEntity);
	}

	/**
	 * 根据文本搜索相似问题
	 */
	@Override
	public List<SearchResponseDTO> searchText(String queryText) {
		// 配置FastJSON的序列化策略（小驼峰转蛇形命名）
		SerializeConfig config = new SerializeConfig();
		config.propertyNamingStrategy = PropertyNamingStrategy.SnakeCase;

		// 构建搜索请求对象（包含查询文本、集合名称、相似度阈值）
		SearchPostDTO searchPostDTO = new SearchPostDTO(
			queryText,          // 用户输入的查询文本
			COLLECTION_NAME,    // 向量数据库中的集合名
			SIMILARITY_THRESHOLD  // 相似度阈值
		);

		// 将搜索请求对象序列化为JSON字符串
		String dataJson = JSON.toJSONString(searchPostDTO, config);

		// 定义响应数据类型（泛型为List<SearchResponseDTO>）
		ParameterizedTypeReference<List<SearchResponseDTO>> responseType = new ParameterizedTypeReference<List<SearchResponseDTO>>() {};

		// 发送POST请求，调用文本搜索接口
		ResponseEntity<List<SearchResponseDTO>> responseEntity = RestTemplateUtil.post(
			searchTextUrl,       // 搜索接口URL（从配置文件注入）
			MediaType.APPLICATION_JSON,  // 请求类型为JSON
			responseType,                // 响应数据类型
			dataJson                     // 请求体（JSON字符串）
		);

		// 获取响应结果，并校验非空
		List<SearchResponseDTO> body = responseEntity.getBody();
		Objects.requireNonNull(body, "智能体响应异常");  // 若为空则抛出异常

		// 返回搜索结果列表
		return body;
	}

}
```

### 类功能说明

该类是`IFeedbackService`接口的实现类，基于 BladeX 框架的`BaseServiceImpl`扩展，实现了问题反馈的核心业务逻辑，主要功能包括：

1. **条件分页查询**：通过调用 Mapper 层的自定义方法，实现多条件组合的分页查询，支持数据权限控制（`userIdsStr`）。
2. **向量数据库同步**：
   - `asyncQdrant`：创建异步任务，非阻塞地执行数据同步逻辑。
   - `synchronizeData`：核心同步方法，从解决记录中合并问题原因和方案，构建符合 Qdrant 要求的数据格式，通过 HTTP 请求同步到向量数据库，并更新问题反馈的`pointId`（向量数据库中的唯一标识）。
3. **文本搜索**：`searchText`方法接收用户输入的查询文本，构建搜索请求并调用外部接口，从向量数据库中查询相似问题，返回符合相似度阈值的结果。

实现细节上，通过 FastJSON 处理 JSON 序列化（小驼峰转蛇形命名）以适配外部接口，使用`RestTemplateUtil`发送 HTTP 请求，结合 Java 8 Stream API 处理集合数据，确保代码简洁高效。同时，通过构造函数注入依赖，遵循 Spring 的依赖注入规范。
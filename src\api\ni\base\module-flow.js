import request from '@/router/axios';


export const getByModule = (module) => {
  return request({
    url: '/api/ni/base/moduleFlow/getByModule',
    method: 'get',
    params: {
      module
    }
  })
}

export const saveOrUpdate = (row) => {
  return request({
    url: '/api/ni/base/moduleFlow/saveOrUpdate',
    method: 'post',
    data: row
  })
}

export const remove = (module) => {
  return request({
    url: '/api/ni/base/moduleFlow/remove',
    method: 'post',
    params: {
      module,
    }
  })
}




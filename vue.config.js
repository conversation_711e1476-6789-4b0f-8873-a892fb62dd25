const MonacoWebpackPlugin = require('monaco-editor-webpack-plugin')
const path = require('path');
module.exports = {
  //路径前缀
  publicPath: "/",
  lintOnSave: true,
  productionSourceMap: false,
  configureWebpack: {
    plugins: [
      new MonacoWebpackPlugin()
    ],
  },
  chainWebpack: (config) => {
    //忽略的打包文件
    config.externals({
      vue: "Vue",
      "vue-router": "VueRouter",
      vuex: "Vuex",
      axios: "axios",
      // "element-ui": "ELEMENT",
    });
    const entry = config.entry("app");
    entry.add("babel-polyfill").end();
    entry.add("classlist-polyfill").end();
    // entry.add("@/mock").end();
  },
  //开发模式反向代理配置，生产模式请使用Nginx部署并配置反向代理
  devServer: {
    port: 1888,
    proxy: {
      "/api": {
        //本地服务接口地址
        target: "http://localhost:10081",
        ws: true,
        pathRewrite: {
          "^/api": "/",
        },
      },
      "/kkf": {
        //本地服务接口地址
        target: "http://localhost:8012",
        ws: true,
      },
      // "/websocket": {
      //   //本地服务接口地址
      //   target: "ws://localhost:10081",
      //   ws: true,
      //   pathRewrite: {
      //     "^/websocket": "/",
      //   },
      // }
    },


  },
};

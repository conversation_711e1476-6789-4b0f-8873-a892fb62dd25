<template>
  <basic-container>
    <avue-skeleton :loading="waiting" avatar :rows="8">
      <avue-title
        style="margin-bottom: 20px"
        :styles="{ fontSize: '20px' }"
        :value="process.name"
      ></avue-title>
      <el-card shadow="never" style="margin-top: 20px">
        <!-- 自定义表单区域 -->
        <avue-form
          v-if="option && option.column && option.column.length > 0"
          v-model="form"
          ref="form"
          :option="option"
          :defaults.sync="defaults"
          @submit="handleSubmit"
          @error="loading = false"
          :upload-preview="handleUploadPreview"
        >
          <template #type="{ disabled, size, index, row }">
            <el-select
              v-model="form.type"
              filterable
              size="mini"
              placeholder=" "
            >
              <el-option
                v-for="item in typeDict"
                :key="item.dictKey"
                :label="item.dictValue"
                :value="item.dictKey"
                v-if="permission[`porapply_apply_${item.dictKey}`]"
              />
            </el-select>
          </template>
          <template #projectId="{ disabled, size, index, row }">
            <project-select
              v-model="form.projectId"
              :size="size"
              :params="{ status: 9 }"
              :disabled="disabled"
              @confirm="projectConfirm"
              @clear="projectClear"
            />
          </template>
          <template #budgetId="{ row, disabled, size, index }">
            <un-finish-budget-select
              v-model="form.budgetId"
              :size="size"
              @confirm="handleBudgetConfirm($event, form)"
              @clear="handleBudgetClear"
            />
          </template>
          <template #amount="{ disabled, size }">
            <span
              style="color: #f56c6c; font-weight: bolder; font-size: 16px"
              >{{ form.amount ? form.amount : "0.00" }}</span
            >
          </template>
          <template #itemsLabel>
            <span style="font-size: 16px; font-weight: 500">采购明细</span>
            <el-divider direction="vertical"></el-divider>
            <el-button-group>
              <el-button
                type="primary"
                size="mini"
                icon="el-icon-plus"
                plain
                :disabled="!form.projectId && !form.budgetId"
                @click="itemAdd"
                >添加
              </el-button>
              <el-button
                type="danger"
                size="mini"
                icon="el-icon-delete"
                plain
                :disabled="item.selectionList.length <= 0"
                @click="itemDelete"
                >删 除
              </el-button>
              <el-button
                type="warning"
                size="mini"
                icon="el-icon-c-scale-to-original"
                plain
                :disabled="item.selectionList.length <= 0"
                @click="handleItemInquiry"
                >批量比价
              </el-button>
            </el-button-group>
          </template>
          <template #items="{ row, disabled }">
            <div style="overflow: hidden">
              <avue-crud
                style="margin-left: 1px"
                :option="item.option"
                :data="form.items"
                @selection-change="itemSelectionChange"
                ref="itemCrud"
                @cell-click="itemCellClickChange"
                :upload-preview="handleUploadPreview"
              >
                <template #expand="{ row }">
                  <el-form label-position="left" inline size="mini">
                    <el-row>
                      <el-col :span="12">
                        采购原因:
                        <span>{{ row.reason }}</span>
                      </el-col>
                      <el-col :span="12">
                        采购风险:
                        <span>{{ row.risk }}</span>
                      </el-col>
                      <el-col :span="24">
                        采购带来的收益及效果:
                        <span>{{ row.profit }}</span>
                      </el-col>
                      <el-col :span="24">
                        项目安全性分析及风险控制:
                        <span>{{ row.security }}</span>
                      </el-col>
                      <el-col :span="24">
                        项目环保因素分析及风险控制:
                        <span>{{ row.ep }}</span>
                      </el-col>
                      <el-col :span="24">
                        供应商比价:
                        <el-table
                          :data="row.inquiries"
                          size="mini"
                          border
                          style="width: 700px"
                        >
                          <el-table-column
                            prop="supplier"
                            label="供应商名称"
                            show-overflow-tooltip
                          >
                          </el-table-column>
                          <el-table-column
                            prop="supplierLinkman"
                            label="联系方式"
                            show-overflow-tooltip
                          >
                          </el-table-column>
                          <el-table-column
                            prop="remark"
                            label="比价详情"
                            show-overflow-tooltip
                          >
                          </el-table-column>
                          <el-table-column
                            prop="recommend"
                            label="建议供应商"
                            show-overflow-tooltip
                          >
                            <template slot-scope="scope">
                              <el-tag
                                size="mini"
                                v-if="scope.row.recommend"
                                type="danger"
                                effect="dark"
                                >是
                              </el-tag>
                            </template>
                          </el-table-column>
                        </el-table>
                      </el-col>
                    </el-row>
                  </el-form>
                </template>
                <template #inquiries="{ row, size, index }">
                  <a
                    href="#"
                    style="
                      text-decoration: underline;
                      font-weight: bold;
                      color: #f56c6c;
                    "
                    @click.p.prevent="rowInquiry(row, index)"
                  >
                    比价
                    <span v-if="row.inquiries">
                      [{{ row.inquiries.length }}]
                    </span>
                  </a>
                </template>
                <template #cost="{ row, index }">
                  <el-button
                    type="text"
                    size="mini"
                    :icon="
                      row.inquiry && !row.inquiryExpand
                        ? 'el-icon-arrow-right'
                        : 'el-icon-arrow-down'
                    "
                    v-if="row.inquiry"
                    @click="itemRowExpand(row)"
                  >
                  </el-button>
                  <el-tag
                    size="mini"
                    type="danger"
                    effect="dark"
                    v-if="row.cost"
                  >
                    费用
                  </el-tag>
                  <el-tag size="mini" type="info" effect="plain" v-else>
                    实物
                  </el-tag>
                </template>
                <template #pv="{ row, index }">
                  <el-tag size="mini" type="danger" effect="dark" v-if="row.pv">
                    是
                  </el-tag>
                  <el-tag size="mini" type="info" effect="plain" v-else>
                    否
                  </el-tag>
                </template>
                <template #materialCode="{ row, size, disabled, index }">
                  <span v-if="row.materialCode">
                    [{{ row.materialCode }}]{{ row.materialName }}
                  </span>
                </template>
                <template #materialCodeForm="{ row, size, disabled, index }">
                  <span v-if="row.materialId">
                    [{{ row.materialCode }}]{{ row.materialName }}
                  </span>
                  <material-select
                    v-else
                    v-model="row.materialId"
                    :size="size"
                    :disabled="disabled"
                    @submit="handleMaterialSubmit($event, row)"
                  />
                </template>
                <template #numForm="{ row, disabled, size }">
                  <el-input-number
                    :size="size"
                    v-model="row.num"
                    :disabled="disabled"
                    :min="0"
                    :controls="false"
                    style="width: 100%"
                    @change="handleNumChange($event, row)"
                  />
                </template>
                <template #amountForm="{ row, disabled, size }">
                  <el-input-number
                    :size="size"
                    v-model="row.amount"
                    :disabled="disabled"
                    :min="0"
                    :controls="false"
                    style="width: 100%"
                    @change="handleAmountChange($event, row)"
                  />
                </template>
                <template #attach="{ row, index }">
                  <el-link v-if="row.attach" type="primary" target="_blank">
                    <i class="el-icon-circle-plus-outline" />
                    附件({{ row.attach ? row.attach.length : 0 }})
                  </el-link>
                </template>
              </avue-crud>
            </div>
          </template>
        </avue-form>

        <!-- 自定义表单区域 -->
      </el-card>

      <el-card shadow="never" style="margin-top: 20px" v-if="showExamForm">
        <wf-examine-form
          ref="examineForm"
          :process="process"
          @user-select="handleUserSelect"
        ></wf-examine-form>
      </el-card>
      <div style="height: 120px"></div>
      <el-row
        class="foot-item avue-affix"
        :style="{
          width: isCollapse ? 'calc(100% - 80px)' : 'calc(100% - 260px)',
        }"
        id="avue-view"
      >
        <el-button
          v-no-more-click
          type="primary"
          size="mini"
          :loading="loading"
          @click="handleSubmit"
          >发起
        </el-button>
        <el-button
          v-if="permission.wf_process_draft"
          type="success"
          size="mini"
          :loading="loading"
          @click="handleDraftNotClose(process.id, process.formKey, form)"
          >存为草稿
        </el-button>
        <el-button
          v-if="permission.wf_process_draft"
          type="success"
          size="mini"
          :loading="loading"
          @click="handleDraft(process.id, process.formKey, form)"
          >存为草稿并关闭
        </el-button>
      </el-row>
    </avue-skeleton>

    <!-- 人员选择弹窗 -->
    <wf-user-select
      ref="user-select"
      :check-type="checkType"
      :default-checked="defaultChecked"
      @onConfirm="handleUserSelectConfirm"
    ></wf-user-select>
    <budget-item-dialog
      ref="porBudgetItemRef"
      multiple
      :params="{ used: false }"
      @confirm="handleItemSelect"
    />
    <material-select-dialog
      ref="materialSelectDialogRef"
      multiple
      @submit="handleItemAddSubmit"
    />
    <el-dialog
      title="比价"
      append-to-body
      :visible.sync="item.inquiryVisible"
      width="80%"
    >
      <div slot="title" class="clearfix">
        <span>比价</span>
        <el-divider direction="vertical"></el-divider>
        <el-button
          v-if="itemSelectMaterialIds"
          type="primary"
          size="mini"
          @click="handleInquiryHistory"
        >
          历史比价
        </el-button>
      </div>
      <avue-form
        v-if="item.inquiryVisible"
        :option="item.inquiryOpt"
        v-model="item.inquiryForm"
        @submit="handleInquirySubmit"
      >
      </avue-form>
    </el-dialog>
    <apply-history-inquiry-dialog
      ref="historyInquiryRef"
      :material-id="itemSelectMaterialIds"
      :show.sync="item.inquiryHistoryShow"
      @submit="handleHistoryInquirySubmit"
    />
  </basic-container>
</template>

<script>
import WfExamineForm from "@/views/plugin/workflow/process/components/examForm.vue";
import WfUserSelect from "@/views/plugin/workflow/process/components/user-select";
import MaterialSelect from "@/views/ni/base/components/MaterialSelect1";
import ProjectSelect from "@/views/ni/project/components/MyProjectSelect";

import exForm from "@/views/plugin/workflow/mixins/ex-form";
import draft from "@/views/plugin/workflow/mixins/draft";
import BudgetItemDialog from "@/views/ni/por/components/BudgetItemDialog";
import MaterialSelectDialog from "@/views/ni/base/components/MaterialSelectDialog";
import UnFinishBudgetSelect from "@/views/ni/por/components/UnFinishBudgetSelect";
import { detail } from "@/api/system/param";
import ApplyHistoryInquiryDialog from "@/views/ni/por/components/ApplyHistoryInquiryDialog";
import { getList as getAttachList } from "@/api/resource/attach";
import debounce from "@/util/debounce";

export default {
  components: {
    ApplyHistoryInquiryDialog,
    WfUserSelect,
    WfExamineForm,
    MaterialSelect,
    ProjectSelect,
    BudgetItemDialog,
    MaterialSelectDialog,
    UnFinishBudgetSelect,
  },
  mixins: [exForm, draft],
  activated() {
    let val=this.$route.query.p
    if (val) {
      this.submitLoading = false;
      let text = Buffer.from(val, "base64").toString();
      text = text.replace(/[\r|\n|\t]/g, "");
      const param = JSON.parse(text);
      const { processId, processDefKey, form } = param;
      detail({ paramKey: "ni.por.apply.inquiries.amount" }).then((res) => {
        this.inquiriesAmount = Number(res.data.data.paramValue);
        this.form.tip = `采购金额大于 ${this.inquiriesAmount}元，需比价!`;
      });
      if (processId) {
        this.getForm(processId);
      } else if (processDefKey) {
        this.getFormByProcessDefKey(processDefKey);
      }
      if (form) {
        const f = JSON.parse(
            new Buffer(decodeURIComponent(form), "utf8").toString("utf8")
        );
        Object.assign(this.form, f);
      }
    }
  },
  computed: {
    showExamForm() {
      const { hideComment, hideCopy, hideExamine } = this.process;
      return !hideComment || !hideCopy || !hideExamine;
    },
    itemIds() {
      let items = new Set();
      this.form.items.forEach((ele) => {
        items.add(ele.budgetItemId);
      });
      return Array.from(items).join(",");
    },
    itemSelectMaterialIds() {
      let items = new Set();
      this.item.selectionList.forEach((ele) => {
        items.add(ele.materialId);
      });
      return Array.from(items).join(",");
    },
  },
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      //  这里的vm指的就是vue实例，可以用来当做this使用
      vm.fromPath = from.path; //获取上一级路由的路径
    });
  },
  data() {
    return {
      p: "",
      inquiriesAmount: 0,
      defaults: {},
      form: {
        budgetId: null,
        items: [],
      },
      option: {
        labelWidth: 120,
        calcHeight: 30,
        size: "mini",
        span: 8,
        menuBtn: false,
        column: [
          {
            type: "input",
            label: "驳回人",
            display: true,
            prop: "createUserName",
            value: "${this.$store.getters.userInfo.nick_name}",
            readonly: true,
          },
          {
            type: "input",
            label: "驳回人部门",
            display: true,
            row: true,
            prop: "createDeptName",
            value: "${this.$store.getters.userInfo.dept_name}",
            readonly: true,
          },
          {
            label: "采购编号",
            prop: "serialNo",
            minWidth: 135,
            placeholder: " ",
            disabled: true,
          },
          {
            label: "采购主题",
            prop: "title",
            placeholder: " ",
            minWidth: 135,
            rules: [
              {
                required: true,
                message: "请输入采购主题",
                trigger: "blur",
              },
            ],
          },
          {
            label: "采购类型",
            prop: "type",
            placeholder: " ",
            type: "select",
            dicData: [],
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            rules: [
              {
                required: true,
                message: "请选择采购类型",
                trigger: "blur",
              },
            ],
            change: ({ value }) => {
              if (value) {
                this.buildTitle(value, this.form.projectTitle);
              }
            },
          },

          {
            label: "关联项目",
            prop: "projectId",
            placeholder: " ",
            filterable: true,
            display: false,
            rules: [
              {
                required: true,
                message: "请选择关联项目",
                trigger: "blur",
              },
            ],
          },
          {
            label: "关联项目",
            prop: "projectTitle",
            placeholder: " ",
            filterable: true,
            disabled: true,
          },
          {
            label: "年度预算",
            prop: "year",
            type: "radio",
            value: false,
            dicData: [
              {
                label: "是",
                value: true,
              },
              {
                label: "否",
                value: false,
              },
            ],
            display: false,
            placeholder: " ",
            rules: [
              {
                required: true,
                message: "请选择是否年度预算",
                trigger: "blur",
              },
            ],
            change: ({ value }) => {
              const budgetIdColumn = this.findObject(
                this.option.column,
                "budgetId"
              );
              const subTypeColumn = this.findObject(
                this.option.column,
                "subType"
              );
              const projectIdColumn = this.findObject(
                this.option.column,
                "projectId"
              );
              const projectTitleColumn = this.findObject(
                this.option.column,
                "projectTitle"
              );
              if (value) {
                budgetIdColumn.rules = [
                  {
                    required: true,
                    message: "请选择预算",
                    trigger: "blur",
                  },
                ];
                subTypeColumn.display = false;
                projectIdColumn.display = false;
                projectTitleColumn.display = true;
              } else {
                budgetIdColumn.rules = [
                  {
                    required: false,
                    message: "请选择预算",
                    trigger: "blur",
                  },
                ];
                subTypeColumn.display = true;
                projectIdColumn.display = true;
                projectTitleColumn.display = false;
              }
            },
          },
          {
            label: "关联预算",
            labelTip: "只关联已审核的预算",
            prop: "budgetId",
            placeholder: " ",
            // dicUrl: "/api/ni/por/budget/unFinishList",
            dicData: [],
            type: "tree",
            props: {
              label: "title",
              value: "id",
            },
            filterable: true,
            rules: [
              {
                required: true,
                message: "请选择关联预算",
                trigger: "blur",
              },
            ],
          },
          {
            label: "预算类型",
            prop: "subType",
            placeholder: " ",
            type: "select",
            dicUrl: "/api/ni/por/type/listWithPermission",
            display: false,
            props: {
              label: "name",
              value: "code",
              desc: "code",
            },
            rules: [
              {
                required: true,
                message: "请选择预算类型",
                trigger: "blur",
              },
            ],
          },
          {
            label: "紧急申购",
            prop: "crash",
            type: "radio",
            placeholder: " ",
            value: false,
            dicData: [
              {
                label: "是",
                value: true,
              },
              {
                label: "否",
                value: false,
              },
            ],
            rules: [
              {
                required: true,
                message: "请选择是否紧急申购",
                trigger: "blur",
              },
            ],
            change: ({ value }) => {
              const yearColumn = this.findObject(this.option.column, "year");
              const budgetIdColumn = this.findObject(
                this.option.column,
                "budgetId"
              );
              const subTypeColumn = this.findObject(
                this.option.column,
                "subType"
              );
              const projectIdColumn = this.findObject(
                this.option.column,
                "projectId"
              );
              const projectTitleColumn = this.findObject(
                this.option.column,
                "projectTitle"
              );
              if (value) {
                yearColumn.display = true;
                budgetIdColumn.rules = [
                  {
                    required: false,
                    message: "请选择预算",
                    trigger: "blur",
                  },
                ];
                subTypeColumn.display = true;
                projectIdColumn.display = true;
                projectTitleColumn.display = false;
              } else {
                yearColumn.display = false;
                budgetIdColumn.rules = [
                  {
                    required: true,
                    message: "请选择预算",
                    trigger: "blur",
                  },
                ];
                subTypeColumn.display = false;
                projectIdColumn.display = false;
                projectTitleColumn.display = true;
              }
            },
          },
          {
            label: "需用日期",
            prop: "needDate",
            placeholder: " ",
            minWidth: 90,
            type: "date",
            rules: [
              {
                required: true,
                message: "请选择需用日期",
                trigger: "blur",
              },
            ],
          },
          {
            label: "采购方式",
            prop: "buyer",
            type: "radio",
            value: "1",
            dicUrl: "/api/blade-system/dict-biz/dictionary?code=ni_por_buyer",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            dataType: "number",
            rules: [
              {
                required: true,
                message: "请选择采购方式",
                trigger: "blur",
              },
            ],
          },
          {
            label: "账套",
            prop: "brand",
            type: "radio",
            minWidth: 90,
            value: "1",
            dicUrl: "/api/blade-system/dict-biz/dictionary?code=ni_brand",
            disabled: true,
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            search: true,
          },
          {
            label: "是否自动申购",
            prop: "auto",
            type: "radio",
            dicData: [
              {
                label: "是",
                value: true,
              },
              {
                label: "否",
                value: false,
              },
            ],
            display: false,
            value: false,
          },
          {
            label: "采购金额",
            prop: "amount",
            type: "number",
            disabled: true,
            placeholder: " ",
          },
          {
            label: "备注",
            placeholder: " ",
            prop: "remark",
            type: "textarea",
            display: false,
            span: 24,
            minRows: 3,
            overHidden: true,
          },
          {
            label: "附件",
            type: "upload",
            propsHttp: {
              res: "data",
              url: "attachId",
              name: "originalName",
            },
            action:
              "/api/blade-resource/oss/endpoint/put-file-attach?code=public",
            display: true,
            span: 24,
            showFileList: true,
            multiple: true,
            limit: 10,
            prop: "attach",
          },
          {
            label: "",
            labelWidth: 20,
            type: "title",
            prop: "tip",
            display: true,
            hide: true,
            showColumn: false,
            span: 24,
            styles: {
              color: "red",
              fontSize: "18px",
            },
          },
          {
            label: "采购明细",
            prop: "items",
            labelPosition: "top",
            span: 24,
          },
        ],
      },
      item: {
        selectionList: [],
        option: {
          header: false,
          rowKey: "budgetItemId",
          expand: true,
          expandWidth: 1,
          expandRowKeys: [],
          cellBtn: false,
          addBtn: false,
          refreshBtn: false,
          columnBtn: false,
          menu: false,
          saveBtn: false,
          updateBtn: false,
          cancelBtn: false,
          editBtn: false,
          delBtn: false,
          dialogFullscreen: true,
          size: "mini",
          align: "center",
          calcHeight: 30,
          tip: false,
          border: true,
          viewBtn: false,
          dialogClickModal: false,
          selection: true,
          showSummary: true,
          sumColumnList: [
            {
              name: "num",
              type: "sum",
              decimals: 1,
            },
            {
              name: "amount",
              type: "sum",
            },
          ],
          column: [
            {
              label: "类型",
              prop: "cost",
              placeholder: " ",
              width: 80,
              disabled: true,
            },
            {
              label: "品名",
              prop: "materialName",
              type: "textarea",
              minRows: 1,
              placeholder: " ",
              overHidden: true,
              cell: true,
              minWidth: 110,
              rules: [
                {
                  required: true,
                  message: "请输入品名",
                  trigger: "blur",
                },
              ],
            },
            {
              label: "用途",
              prop: "purpose",
              type: "textarea",
              minWidth: 110,
              minRows: 1,
              placeholder: " ",
              overHidden: true,
              cell: true,
            },
            {
              label: "编码",
              minWidth: 100,
              placeholder: " ",
              prop: "materialCode",
              overHidden: true,
              clearable: false,
              cell: true,
              rules: [
                {
                  required: false,
                  message: "请选择编码",
                  trigger: "blur",
                },
              ],
            },
            {
              label: "规格",
              prop: "specification",
              placeholder: " ",
              overHidden: true,
              disabled: true,
              minWidth: 110,
            },
            {
              label: "材质",
              prop: "quality",
              placeholder: " ",
              disabled: true,
              overHidden: true,
              minWidth: 110,
            },
            {
              label: "单位",
              prop: "unit",
              type: "select",
              dicUrl:
                "/api/blade-system/dict-biz/dictionary?code=ni_material_unit",
              props: {
                label: "dictValue",
                value: "dictKey",
              },
              minWidth: 80,
              placeholder: " ",
              slot: true,
              rules: [
                {
                  required: true,
                  message: "请选择单位",
                  trigger: "blur",
                },
              ],
            },
            {
              label: "数量",
              prop: "num",
              type: "number",
              precision: 0,
              placeholder: " ",
              minWidth: 110,
              controls: false,
              cell: true,
              rules: [
                {
                  required: true,
                  message: "请输入数量",
                  trigger: "blur",
                },
              ],
            },
            {
              label: "单价",
              prop: "price",
              type: "number",
              controls: false,
              precision: 2,
              placeholder: " ",
              minWidth: 110,
              cell: true,
              change: ({ value, row }) => {
                if (row.num) {
                  row.amount = (Number(value) * Number(row.num)).toFixed(2);
                } else {
                  row.amount = 0;
                }
                this.sumAmount();
              },
            },
            {
              label: "金额",
              prop: "amount",
              overHidden: true,
              type: "number",
              cell: true,
              controls: false,
              minWidth: 110,
              precision: 2,
              placeholder: " ",
              rules: [
                {
                  required: true,
                  message: "请输入金额",
                  trigger: "blur",
                },
              ],
            },
            {
              label: "压力容器",
              prop: "pv",
              cell: true,
              type: "radio",
              width: 110,
              dicData: [
                {
                  label: "是",
                  value: 1,
                },
                {
                  label: "否",
                  value: 0,
                },
              ],
            },
            {
              label: "比价",
              prop: "inquiries",
              minWidth: 110,
            },
            {
              label: "备注",
              prop: "remark",
              type: "textarea",
              minWidth: 130,
              minRows: 1,
              placeholder: " ",
              cell: true,
              overHidden: true,
            },
            {
              label: "附件",
              type: "upload",
              width: 94,
              propsHttp: {
                res: "data",
                url: "attachId",
                name: "originalName",
              },
              cell: true,
              action:
                "/api/blade-resource/oss/endpoint/put-file-attach?code=public",
              display: true,
              showFileList: true,
              multiple: true,
              limit: 10,
              prop: "attach",
            },
          ],
        },
        inquiryVisible: false,
        inquiryOpt: {
          size: "mini",
          emptyBtn: false,
          labelWidth: 120,
          column: [
            {
              label: "采购原因",
              prop: "reason",
              type: "textarea",
              minRows: 1,
              placeholder: " ",
              rules: [
                {
                  required: true,
                  message: "请输入采购原因",
                  trigger: "blur",
                },
              ],
            },
            {
              label: "采购风险",
              prop: "risk",
              type: "textarea",
              minRows: 1,
              placeholder: " ",
              rules: [
                {
                  required: true,
                  message: "请输入采购风险",
                  trigger: "blur",
                },
              ],
            },
            {
              label: "采购带来的收益及效果",
              prop: "profit",
              span: 24,
              type: "textarea",
              minRows: 2,
              placeholder: " ",
              rules: [
                {
                  required: true,
                  message: "请输入采购带来的收益及效果",
                  trigger: "blur",
                },
              ],
            },
            {
              label: "项目安全性分析及风险控制",
              prop: "security",
              span: 24,
              minRows: 2,
              type: "textarea",
              placeholder: " ",
              rules: [
                {
                  required: true,
                  message: "请输入项目安全性分析及风险控制",
                  trigger: "blur",
                },
              ],
            },
            {
              label: "项目环保因素分析及风险控制",
              prop: "ep",
              span: 24,
              minRows: 2,
              type: "textarea",
              placeholder: " ",
              rules: [
                {
                  required: true,
                  message: "请输入项目环保因素分析及风险控制",
                  trigger: "blur",
                },
              ],
            },
            {
              label: "验收标准",
              prop: "ac",
              span: 12,
              minRows: 1,
              type: "textarea",
              placeholder: " ",
            },
            {
              label: "技术参数",
              prop: "technology",
              span: 12,
              minRows: 1,
              type: "textarea",
              placeholder: " ",
            },
            {
              label: "三方比价",
              prop: "inquiries",
              span: 24,
              type: "dynamic",
              children: {
                span: 8,
                align: "center",
                headerAlign: "center",
                addBtn: false,
                delBtn: false,
                column: [
                  {
                    label: "供应商名称",
                    prop: "supplier",
                    placeholder: " ",
                    type: "textarea",
                    overHidden: true,
                    minRows: 1,
                    minWidth: 200,
                    rules: [
                      {
                        required: true,
                        message: "请填写供应商名称",
                        trigger: "blur",
                      },
                    ],
                  },
                  {
                    label: "联系方式",
                    prop: "supplierLinkman",
                    placeholder: " ",
                    type: "textarea",
                    minRows: 1,
                    overHidden: true,
                    minWidth: 200,
                    rules: [
                      {
                        required: true,
                        message: "请填写供应商联系人",
                        trigger: "blur",
                      },
                    ],
                  },

                  {
                    label: "比价详情",
                    prop: "remark",
                    type: "textarea",
                    overHidden: true,
                    minWidth: 250,
                    minRows: 1,
                    placeholder: " ",
                    rules: [
                      {
                        required: true,
                        message: "请填写比价详情",
                        trigger: "blur",
                      },
                    ],
                  },
                  {
                    label: "建议供应商",
                    prop: "recommend",
                    type: "radio",
                    width: 150,
                    value: false,
                    dicData: [
                      {
                        label: "是",
                        value: true,
                      },
                      {
                        label: "否",
                        value: false,
                      },
                    ],
                    placeholder: " ",
                    rules: [
                      {
                        required: true,
                        message: "请选择建议供应商",
                        trigger: "blur",
                      },
                    ],
                    change: ({ value, index }) => {
                      if (value)
                        this.$nextTick(() => {
                          this.item.inquiryForm.inquiries.forEach((item, i) => {
                            if (i === index) {
                              return;
                            }
                            item.recommend = false;
                          });
                        });
                    },
                  },
                ],
              },
            },
          ],
        },
        inquiryForm: {},
        inquiryHistoryShow: false,
      },
      process: {},
      loading: false,
      payment: 0,
      typeDict: [],
      typeDictKeyValue: {},
      fromPath: "",
    };
  },
  created() {
    this.dictInit();
  },
  methods: {
    handleHistoryInquirySubmit(row) {
      const form = {
        ...row,
        id: null,
      };
      form.inquiries.forEach((inquiry) => (inquiry.id = null));
      this.item.inquiryForm = form;
    },
    handleMaterialSubmit(selectList, row1) {
      if (selectList.length > 0) {
        const row = selectList[0];
        this.$nextTick(() => {
          this.$set(row1, "materialName", row.name);
          this.$set(row1, "materialCode", row.code);
          this.$set(row1, "specification", row.specification);
          this.$set(row1, "quality", row.quality);
          this.$set(row1, "gb", row.gb);
          this.$set(row1, "unit", row.unit);
          this.$set(row1, "cost", row.cost);
        });
      }
    },
    handleBudgetClear() {
      this.form.budgetId = null;
      this.form.items = [];
    },
    handleBudgetConfirm(selectList, row) {
      if (selectList && selectList.length > 0) {
        this.$nextTick(() => this.$refs.form.clearValidate());
        row.projectId = selectList[0].projectId;
        row.projectTitle = selectList[0].projectTitle;
        row.projectSerialNo = selectList[0].projectSerialNo;
        row.brand = selectList[0].brand;
        row.subType = selectList[0].type;
        const projectId = this.findObject(this.option.column, "projectId");

        if (selectList[0].year === "2" && this.form.crash) {
          //年度预算
          projectId.rules = [
            {
              required: false,
              message: "请输入",
              trigger: "blur",
            },
          ];
        } else {
          projectId.rules = [
            {
              required: true,
              message: "请选择项目",
              trigger: "blur",
            },
          ];
        }
      }
    },

    handleInquirySubmit(form, done) {
      if (form.inquiries.every((item) => !item.recommend)) {
        this.$message({
          type: "warning",
          message: "请选择推荐供应商!",
        });
        done();
        return;
      }
      if (form.inquiries.filter((item) => item.recommend).length > 1) {
        this.$message({
          type: "warning",
          message: "推荐供应商只能选择一家!",
        });
        done();
        return;
      }
      this.form.items.forEach((item, index) => {
        if (form.index && form.index.includes(index)) {
          item.reason = form.reason;
          item.risk = form.risk;
          item.profit = form.profit;
          item.security = form.security;
          item.ep = form.ep;
          item.ac = form.ac;
          item.technology = form.technology;
          item.inquiries = form.inquiries;
          this.$set(item, "inquiry", true);
          this.$set(item, "inquiryExpand", false);
        }
      });
      this.item.inquiryVisible = false;
      done();
    },
    handleInquiryHistory() {
      this.item.inquiryHistoryShow = true;
    },
    itemRowExpand(row) {
      row.inquiryExpand = !row.inquiryExpand;
      this.$refs.itemCrud.toggleRowExpansion(row);
    },
    itemCellClickChange(row) {
      this.form.items.forEach((item) => (item.$cellEdit = false));
      row.$cellEdit = true;
      const materialCodeColumn = this.findObject(
        this.item.option.column,
        "materialCode"
      );
      if (!row.cost) {
        materialCodeColumn.rules = [
          {
            required: true,
            message: "请选择编码",
            trigger: "blur",
          },
        ];
      } else {
        materialCodeColumn.rules = [
          {
            required: false,
            message: "请选择编码",
            trigger: "blur",
          },
        ];
      }
    },
    handleNumChange(num, row) {
      if (row.price) {
        row.amount = Number(row.price) * Number(row.num);
      }
      this.sumAmount();
    },
    handleAmountChange(amount, row) {
      if (row.num) {
        row.price = (Number(amount) / Number(row.num)).toFixed(2);
      }
      if (Number(amount) >= Number(this.inquiriesAmount)) {
        console.log("总价超过" + this.inquiriesAmount);
      }
      this.sumAmount();
    },
    sumAmount() {
      this.$nextTick(() => {
        const itemAmount = this.form.items.reduce((acc, cur) => {
          return Number(acc) + Number(cur.amount);
        }, 0);
        this.form.amount = Number(itemAmount.toFixed(2));
      });
    },
    itemSelectionChange(list) {
      this.item.selectionList = list;
    },
    itemSelectionClear() {
      this.item.selectionList = [];
      this.$refs.itemCrud.toggleSelection();
    },
    handleItemAddSubmit(selectList) {
      if (selectList) {
        selectList.forEach((item) => {
          const row = {
            typeId: item.typeId,
            materialCode: item.code,
            materialName: item.name,
            materialTypeId: item.typeId,
            materialId: item.id,
            specification: item.specification,
            quality: item.quality,
            unit: item.unit,
            gb: item.gb,
            cost: item.cost,
            pv: 0,
          };
          this.form.items.push(row);
        });
      }
    },
    async handleItemSelect(selectionList) {
      for (const item of selectionList) {
        const res = await getAttachList({
          businessName: "ni_por_budget_item",
          businessKey: item.id,
        });
        const data = res.data.data;
        const i = {
          budgetItemId: item.id,
          purpose: item.purpose,
          materialCode: item.materialCode,
          materialName: item.materialName,
          materialId: item.materialId,
          specification: item.specification,
          quality: item.quality,
          gb: item.gb,
          unit: item.unit,
          num: item.num,
          budgetNum: item.num,
          usedNum: item.applyNum ? item.applyNum : 0,
          amount: item.amount,
          price: item.price,
          remark: item.remark,
          cost: item.cost,
          pv: item.pv ? item.pv : 0,
          attach: data.map((item) => {
            return {
              label: item.originalName,
              value: item.id,
            };
          }),
        };
        this.form.items.push(i);
      }
      this.sumAmount();
    },
    rowInquiry(row, index) {
      Object.keys(this.item.inquiryForm).forEach(
        (key) => (this.item.inquiryForm[key] = "")
      );
      this.$nextTick(() => {
        this.item.inquiryForm = {
          ...row,
          index: [index],
        };
        if (
          !this.item.inquiryForm.inquiries ||
          this.item.inquiryForm.inquiries.length === 0
        ) {
          this.item.inquiryForm.inquiries = [
            {
              applyId: this.form.id,
              recommend: false,
            },
            { applyId: this.form.id, recommend: false },
            { applyId: this.form.id, recommend: false },
          ];
        }
        this.item.inquiryVisible = true;
      });
    },
    handleItemInquiry() {
      if (this.item.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      const indexList = this.item.selectionList.map((item) => item.$index);
      Object.keys(this.item.inquiryForm).forEach(
        (key) => (this.form[key] = "")
      );
      if (this.item.selectionList.length === 1) {
        this.item.inquiryForm = {
          ...this.item.selectionList[0],
          index: indexList,
        };
      } else {
        this.item.inquiryForm = {
          index: indexList,
        };
      }
      if (
        !this.item.inquiryForm.inquiries ||
        this.item.inquiryForm.inquiries.length === 0
      ) {
        this.item.inquiryForm.inquiries = [
          { recommend: false },
          { recommend: false },
          { recommend: false },
        ];
      }
      this.item.inquiryVisible = true;
    },
    itemDelete() {
      if (this.item.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      const indexList = this.item.selectionList.map((item) => item.$index);
      const [...items] = this.form.items.filter(
        (item, index) => !indexList.includes(index)
      );
      this.form.items = items;
      this.sumAmount();
    },
    itemAdd() {
      if (!this.form.budgetId && !this.form.crash) {
        this.$message.warning("请选择预算");
        return;
      }
      if (this.form.crash && !this.form.budgetId) {
        this.$refs.materialSelectDialogRef.visible = true;
      } else {
        this.$refs.porBudgetItemRef.init(this.form.budgetId);
      }
    },
    dictInit() {
      this.$http
        .get("/api/blade-system/dict-biz/dictionary?code=ni_por_type")
        .then((res) => {
          const column = this.findObject(this.option.column, "type");
          column.dicData = res.data.data;
          this.typeDict = res.data.data;
          this.typeDictKeyValue = this.typeDict.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });
    },
    projectClear() {
      const column = this.findObject(this.option.column, "budgetId");
      column.dicData = [];
      this.form.budgetId = null;
    },
    projectConfirm(selectionList) {
      if (selectionList) {
        this.$nextTick(() => this.$refs.form.clearValidate());
        this.form.projectTitle = selectionList[0].title;
        this.form.budgetId = null;
        this.form.items = [];
        this.buildTitle(this.form.type, this.form.projectTitle);
        const budgetId = this.findObject(this.option.column, "budgetId");
        if (this.form.crash) {
          budgetId.rules = [
            {
              required: false,
              message: "请输入",
              trigger: "blur",
            },
          ];
        } else {
          budgetId.rules = [
            {
              required: true,
              message: "请选择项目",
              trigger: "blur",
            },
          ];
        }
      }
    },
    buildTitle(type, projectTitle) {
      const typeLabel = this.typeDictKeyValue[type]
        ? this.typeDictKeyValue[type]
        : "";
      const projectTitle1 = projectTitle ? projectTitle : "";
      this.form.title = `[${typeLabel}]${projectTitle1}项目的采购申请`;
    },
    getForm(processId) {
      this.getStartForm(processId).then((res) => {
        const _this = this;
        let { process } = res;
        this.form.processId = process.id;
        const option = _this.option;
        const { column } = option;
        column.forEach((col) => {
          if (col.value) col.value = _this.getDefaultValues(col.value);
        });
        // if (group && group.length > 0) {
        //   // 处理group
        //   group.forEach((gro) => {
        //     gro.column.forEach((col) => {
        //       if (col.value) col.value = _this.getDefaultValues(col.value);
        //     });
        //   });
        // }
        //
        // const groupArr = []
        // const columnArr = this.filterAvueColumn(column, startForm, true).column
        // if (group && group.length > 0) { // 处理group
        //   group.forEach(gro => {
        //     gro.column = this.filterAvueColumn(gro.column, startForm, true).column
        //     if (gro.column.length > 0) groupArr.push(gro)
        //   })
        // }
        //
        // option.column = columnArr
        // option.group = groupArr
        // this.option = option

        if (_this.permission.wf_process_draft) {
          // 查询是否有草稿箱
          _this.initDraft(process.id).then((data) => {
            _this
              .$confirm("是否恢复之前保存的草稿？", "提示", {})
              .then(() => {
                _this.form = JSON.parse(data);
              })
              .catch(() => {});
          });
        }
        _this.waiting = false;
      });
    },
    getFormByProcessDefKey(processDefKey) {
      this.getStartFormByProcessDefKey(processDefKey).then((res) => {
        const _this = this;
        let { process } = res;
        _this.form.processId = process.id;
        const option = _this.option;
        const { column } = option;
        column.forEach((col) => {
          if (col.value) col.value = _this.getDefaultValues(col.value);
        });
        // if (group && group.length > 0) {
        //   // 处理group
        //   group.forEach((gro) => {
        //     gro.column.forEach((col) => {
        //       if (col.value) col.value = _this.getDefaultValues(col.value);
        //     });
        //   });
        // }
        if (_this.permission.wf_process_draft) {
          // 查询是否有草稿箱
          _this.initDraft(process.id).then((data) => {
            _this
              .$confirm("是否恢复之前保存的草稿？", "提示", {})
              .then(() => {
                _this.form = JSON.parse(data);
              })
              .catch(() => {});
          });
        }
        _this.waiting = false;
      });
    },
    validateItems(items) {
      return (
        !items ||
        !items.some(
          (item) =>
            !item.num ||
            !item.amount ||
            item.num === 0 ||
            (!item.cost && !item.materialId)
        )
      );
    },
    handleSubmit:debounce(function () {
      this.loading = true;
      //强制给year赋值，避免后台转对象时报错
      if (!this.form.crash) {
        this.form.year = false;
      }
      //保存再提交
      if (this.form.inquiry) {
        this.form.inquiryState = 1;
      }
      this.form.porState = 1;
      const items = this.validateItems(this.form.items);
      if (items) {
        if (!this.form.amount) {
          this.sumAmount();
        }
        let msg = "";
        this.form.items.forEach((item) => {
          if (
            !this.form.crash &&
            Number(item.amount) >= Number(this.inquiriesAmount) &&
            (item.inquiries == null || item.inquiries.length < 1)
          ) {
            msg += `<span style="color: #F56C6C;font-weight: bolder">${
              item.materialName
            }(￥${Number(item.amount).toLocaleString("zh-CN", {
              minimumFractionDigits: 2,
            })})</span>未比价;<br/>`;
          }
        });
        //技术上强制必填工作表，除钢材，外修，外协的不做
        //技术上包含技术一部到七部，还有工艺
        //基建的必填工作表
        if (msg && "18" !== this.form.type) {
          if (
            !["18", "15"].includes(this.form.type) &&
            (this.form.createDeptName.startsWith("技术") ||
              this.form.createDeptName === "工艺组")
          ) {
            this.$alert(msg, "警告", {
              dangerouslyUseHTMLString: true,
              type: "warning",
              center: true,
              confirmButtonText: "确定",
            }).then(() => {
              this.loading = false;
            });
            return;
          }
          this.$confirm(msg, "警告", {
            dangerouslyUseHTMLString: true,
            confirmButtonText: "重新比价",
            cancelButtonText: "无比价",
            type: "warning",
            center: true,
            distinguishCancelAndClose: true,
          })
            .then(() => {
              this.loading = false;

            })
            .catch((action) => {
              if (action === "close") {
                this.loading = false;
                return;
              }
              this.handleStartProcess(true)
                .then((done) => {
                  this.$message.success("发起成功");
                  if (this.fromPath) {
                    this.handleCloseTag(this.fromPath);
                  } else this.handleCloseTag("/ni/por/apply");
                  done();
                })
                .catch(() => {
                  this.loading = false;
                });
            });
        } else
          this.handleStartProcess(true)
            .then((done) => {
              this.$message.success("发起成功");
              if (this.fromPath) {
                this.handleCloseTag(this.fromPath);
              } else this.handleCloseTag("/ni/por/apply");
              done();
            })
            .catch(() => {
              this.loading = false;
            });
      } else {
        this.$message.warning("明细中存在未填写的数量/金额");
        this.loading = false;
      }
    },1000),
  },
};
</script>

<style lang="scss" scoped>
/deep/ .el-table__expand-icon {
  visibility: hidden;
}

/deep/ .el-link--inner {
  font-size: 12px;
}

/deep/ .el-upload-list__item-name {
  font-size: 12px;
}

.foot-item {
  position: fixed;
  bottom: 0;
  margin-left: -20px;
  // right: 0;
  z-index: 101;
  height: 66px;
  background-color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  -webkit-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
</style>

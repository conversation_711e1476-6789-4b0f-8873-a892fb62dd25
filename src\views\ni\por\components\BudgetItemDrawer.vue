<template>
  <el-drawer :visible.sync="visible" :title="title" size="100%" append-to-body>
    <basic-container>
      <avue-crud
        v-if="visible"
        :option="option"
        :table-loading="loading"
        :data="data"
        :page.sync="page"
        :before-open="beforeOpen"
        :before-close="beforeClose"
        v-model="form"
        ref="crud"
        @row-update="rowUpdate"
        @row-save="rowSave"
        @row-del="rowDel"
        @search-change="searchChange"
        @search-reset="searchReset"
        @selection-change="selectionChange"
        @current-change="currentChange"
        @size-change="sizeChange"
        @refresh-change="refreshChange"
        @on-load="onLoad"
      >
        <template #used="{ row }">
          <el-tag v-if="row.used" type="danger" size="mini" effect="dark">
            是
          </el-tag>
          <el-tag v-else type="info" size="mini" effect="plain">否</el-tag>
        </template>
        <template #menuLeft>
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-plus"
            v-if="option.addBtn"
            plain
            @click="handleAdd"
          >新 增
          </el-button>
          <el-button
            type="danger"
            size="mini"
            icon="el-icon-delete"
            v-if="option.delBtn"
            plain
            @click="handleDelete"
          >删 除
          </el-button>
          <el-button
            type="warning"
            size="mini"
            icon="el-icon-share"
            @click="handleSub"
          >分配小项
          </el-button>
          <el-divider direction="vertical"></el-divider>
          <el-radio-group v-model="cost" size="mini" @input="handleCostChange">
            <el-radio-button label="all">全部</el-radio-button>
            <el-radio-button :label="true">费用</el-radio-button>
            <el-radio-button :label="false">实物</el-radio-button>
          </el-radio-group>
        </template>
        <template #cost="{ row }">
          <el-tag size="mini" type="danger" effect="dark" v-if="row.cost">
            费用
          </el-tag>
          <el-tag size="mini" type="info" effect="plain" v-else> 实物</el-tag>
        </template>
        <template #materialName="{ row, size }">
          <el-tag v-if="row.repair" :size="size" effect="plain" type="danger"
          >补
          </el-tag>
          <span>{{ row.materialName }}</span>
        </template>
        <template #menu="{ row, size }">
          <el-button
            type="text"
            icon="el-icon-download"
            :size="size"
            @click="rowAttach(row)"
          >附件
          </el-button>
        </template>
      </avue-crud>
    </basic-container>
    <material-select-dialog
      multiple
      ref="materialSelectRef"
      @submit="handleMaterialAddSubmit"
    />
    <attach-dialog
      ref="attachDialogRef"
      code="public"
      :del-btn="action.status === 0"
    />
    <el-dialog
      title="修改小项"
      append-to-body
      :visible.sync="sub.visible"
      width="500px"
    >
      <avue-form
        v-if="sub.visible"
        :option="sub.option"
        v-model="sub.form"
        @submit="handleSubSubmit"
      >
      </avue-form>
    </el-dialog>
  </el-drawer>
</template>

<script>
import {add, changeSubCode, getDetail, getPage as getItemList, remove, update,} from "@/api/ni/por/budget-item";

import MaterialSelectDialog from "@/views/ni/base/components/MaterialSelectDialog";
import AttachDialog from "@/components/attach-dialog";

export default {
  name: "BudgetItemDrawer",
  components: {
    MaterialSelectDialog,
    AttachDialog,
  },
  data() {
    return {
      budgetId: null,
      withRepair: false,
      title: "",
      visible: false,
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        showSummary: true,
        sumColumnList: [
          {
            name: "num",
            type: "sum",
            decimals: 2,
          },
          {
            name: "amount",
            type: "sum",
            decimals: 2,
          },
          {
            name: "orderNum",
            type: "sum",
            decimals: 2,
          },
          {
            name: "orderAmount",
            type: "sum",
            decimals: 2,
          },
        ],
        menu: true,
        searchEnter: true,
        cellBtn: true,
        addBtn: false,
        selection: true,
        searchLabel: 110,
        span: 8,
        labelWidth: 130,
        menuWidth: 80,
        size: "mini",
        searchSize: "mini",
        align: "center",
        searchIndex: 3,
        searchIcon: true,
        height: "auto",
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: false,
        viewBtn: false,
        dialogClickModal: false,
        column: [
          {
            label: "审批状态",
            prop: "budgetStatus",
            type: "select",
            dicUrl: "/api/blade-system/dict/dictionary?code=data_status",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            searchPlaceholder: " ",
            dataType: "number",
            display: false,
            search: true,
          },
          {
            label: "小项",
            prop: "subCode",
            type: "select",
            dicData: [],
            props: {
              label: "name",
              value: "code",
            },
            placeholder: " ",
            width: 100,
            cell: true,
          },
          {
            label: "类型",
            prop: "cost",
            type: "select",
            dicData: [
              {
                label: "费用",
                value: true,
              },
              {
                label: "实物",
                value: false,
              },
            ],
            placeholder: " ",
            width: 70,
            disabled: true,
          },
          {
            label: "品名",
            prop: "materialName",
            disabled: true,
            minWith: 110,
            overHidden: true,
            placeholder: " ",
            search: true,
            searchOrder: 99
          },
          {
            label: "用途",
            prop: "purpose",
            type: "textarea",
            minRows: 1,
            placeholder: " ",
            overHidden: true,
            minWidth: 90,
            search: true,
            searchOrder: 98
          },
          {
            label: "编码",
            minWidth: 110,
            placeholder: " ",
            prop: "materialCode",
            clearable: false,
            search: true,
            searchOrder: 95,
            rules: [
              {
                required: true,
                message: "请输入",
                trigger: "blur",
              },
            ],
          },
          {
            label: "规格",
            prop: "specification",
            disabled: true,
            minWith: 120,
            overHidden: true,
            placeholder: " ",
            search: true,
            searchOrder: 97
          },
          {
            label: "材质",
            prop: "quality",
            minWidth: 90,
            overHidden: true,
            searchOrder: 96
          },
          {
            label: "国标",
            prop: "gb",
            minWith: 100,
            overHidden: true,
            disabled: true,
            placeholder: " ",
            hide: true,
          },
          {
            label: "单位",
            prop: "unit",
            overHidden: true,
            type: "select",
            placeholder: " ",
            value: "1",
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_material_unit",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            minWith: 90,
            slot: true,
            rules: [
              {
                required: true,
                message: "请选择",
                trigger: "blur",
              },
            ],
          },
          {
            label: "数量",
            prop: "num",
            type: "number",
            placeholder: " ",
            minWidth: 100,
            cell: true,
            rules: [
              {
                required: true,
                message: "请输入数量",
                trigger: "blur",
              },
            ],
            change: (data) => {
              const that = this;
              const {row, value} = data;
              if (row.amount) {
                row.price = row.amount / value;
              } else {
                row.price = 0;
              }

              let total = 0;
              that.form.items.forEach((item) => {
                total += item.amount;
              });
              that.form.amount = total;
            },
          },
          {
            label: "单价",
            prop: "price",
            type: "number",
            minWidth: 100,
            disabled: true,
            controls: false,
            precision: 2,
            placeholder: " ",
          },
          {
            label: "金额",
            prop: "amount",
            overHidden: true,
            controls: false,
            type: "number",
            cell: true,
            change: ({row, value}) => {
              const that = this;
              if (row.num) {
                row.price = value / row.num;
              } else {
                row.price = 0;
              }
              let total = 0;
              that.form.items.forEach((item) => {
                total += item.amount;
              });
              that.form.amount = total;
            },
            minWidth: 100,
            precision: 2,
            placeholder: " ",
            rules: [
              {
                required: true,
                message: "请输入金额",
                trigger: "blur",
              },
            ],
          },
          {
            label: "已采购数量",
            prop: "orderNum",
            type: "number",
            placeholder: " ",
            minWidth: 90,
          },
          {
            label: "已采购金额",
            prop: "orderAmount",
            overHidden: true,
            controls: false,
            type: "number",
            minWidth: 95,
            precision: 2,
            placeholder: " ",
          },
          {
            label: "备注",
            prop: "remark",
            minWidth: 110,
            cell: true,
            type: "textarea",
            minRows: 1,
            placeholder: " ",
            overHidden: true,
          },
          {
            label: "申请人",
            prop: "createUserName",
            minWidth: 90,
            overHidden: true,
            placeholder: " ",
          },
          {
            label: "申请时间",
            prop: "createTime",
            minWidth: 90,
            overHidden: true,
            placeholder: " ",
          },
          {
            label: "已用",
            prop: "used",
            minWidth: 90,
            overHidden: true,
            placeholder: " ",
          },
        ],
      },
      data: [],
      form: {},
      action: {},
      sub: {
        visible: false,
        option: {
          span: 24,
          size: "mini",
          emptyBtn: false,
          column: [
            {
              label: "小项",
              prop: "subCode",
              overHidden: true,
              type: "select",
              dicData: [],
              props: {
                label: "name",
                value: "code",
                desc: "code",
              },
              placeholder: " ",
              rules: [
                {
                  required: true,
                  message: "请选择小项",
                  trigger: "blur",
                },
              ],
            },
          ],
        },
        form: {},
      },
      cost: 'all'
    };
  },
  computed: {
    ids() {
      let ids = new Set();
      this.selectionList.forEach((ele) => {
        ids.add(ele.id);
      });
      return Array.from(ids).join(",");
    },
  },
  methods: {
    handleCostChange(value) {
      if (value === 'all') {
        this.query.cost = null
      } else {
        this.query.cost = value
      }
      this.page.currentPage = 1;
      this.onLoad(this.page, this.query)
    },
    handleSubSubmit(form, done) {
      const ids = this.selectionList.map((item) => item.id);
      changeSubCode(this.ids, form.subCode)
        .then(() => {
          this.$message({
            type: "warning",
            message: "操作成功!",
          });
          this.data.forEach((item) => {
            if (ids.includes(item.id)) {
              item.subCode = form.subCode;
            }
          });
          this.sub.visible = false;
        })
        .finally(() => {
          done();
        });
    },
    rowAttach(row) {
      this.action = row;
      this.$refs.attachDialogRef.init(row.id, this.module);
    },
    async init(row, detail, withRepair) {
      if (row.year === "2") {
        this.title = `${row.yearDate}年${row.title}-明细`;
      } else {
        this.title = `${row.title}-明细`;
      }
      const subCode = this.findObject(this.option.column, "subCode");
      const subCode1 = this.findObject(this.sub.option.column, "subCode");
      let projectId;
      if (row.year === "2") {
        projectId = row.repair ? row.parentId : row.id;
      } else {
        projectId = row.projectId;
      }
      const res = await this.$http.get(
        "/api/ni/project/sub/select?projectId=" + projectId
      );
      subCode.dicData = res.data.data;
      subCode1.dicData = res.data.data;
      if (detail) {
        this.option.addBtn = false;
        this.option.editBtn = false;
        this.option.delBtn = false;
      } else {
        this.option.addBtn = true;
        this.option.editBtn = true;
        this.option.delBtn = true;
      }
      this.budgetId = row.id;
      this.withRepair = withRepair;
      this.visible = true;
    },
    handleMaterialAddSubmit(selectionList) {
      if (selectionList) {
        selectionList.forEach((item) => {
          this.$refs.crud.rowCellAdd({
            budgetId: this.budgetId,
            materialId: item.id,
            materialCode: item.code,
            materialName: item.name,
            specification: item.specification,
            gb: item.gb,
            unit: item.unit,
          });
        });
      }
    },
    handleAdd() {
      this.$refs.materialSelectRef.visible = true;
    },
    rowSave(row, done, loading) {
      const form = {...row};
      let ad;
      ad = add(form);
      ad.then(
        () => {
          // this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
        }
      );
    },
    rowUpdate(row, index, done, loading) {
      let ad;
      ad = update(row);
      ad.then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
        }
      );
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return update(row);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    handleSub() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.sub.visible = true;
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeClose(done) {
      done();
    },
    beforeOpen(done, type) {
      if ("add" == type) {
        this.form.status = 0;
        this.form.amount = 0;
      } else if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then((res) => {
          this.form = res.data.data;
          if (
            this.form.type == 9 &&
            this.form.startDate != null &&
            this.form.endDate != null
          ) {
            this.form.date = [this.form.startDate, this.form.endDate];
          }
        });
      }
      done();
    },
    searchReset() {
      this.cost = "all"
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.query.cost = this.cost === 'all' ? null : this.cost
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      const query = Object.assign(params, this.query);
      query.budgetId = this.budgetId;
      getItemList(page.currentPage, page.pageSize, query).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
  },
};
</script>

<style scoped></style>

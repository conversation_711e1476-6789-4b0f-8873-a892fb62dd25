import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/ni/meal/takeout/user-groups/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/ni/meal/takeout/user-groups/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/ni/meal/takeout/user-groups/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/ni/meal/takeout/user-groups/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/ni/meal/takeout/user-groups/submit',
    method: 'post',
    data: row
  })
}

export const play = (ids) => {
  return request({
    url: '/api/ni/meal/takeout/user-groups/play',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const pause = (ids) => {
  return request({
    url: '/api/ni/meal/takeout/user-groups/pause',
    method: 'post',
    params: {
      ids,
    }
  })
}

<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      :cell-style="cellStyle"
      @cell-click="cellClickChange"
      @on-load="onLoad"
    >
      <template #materialCode="{ row, index }">
        <span v-if="row.materialCode">{{ row.materialCode }}</span>
        <span v-else>未设置</span>
      </template>
      <template #capacity="{ row, index }">
        <span>{{ Number(row.capacity) + unitDictKv[row.unit] }}</span>
      </template>
      <template #taishan="{ row, index }">
        <span v-if="row.taishan">是</span>
        <span v-else>否</span>
      </template>
      <template #skuForm="{ size, disabled }">
        <el-input
          placeholder=" "
          v-model="form.sku"
          :size="size"
          :disabled="disabled"
        >
          <el-button slot="append" icon="el-icon-magic-stick" @click="handleSku"
            >生成
          </el-button>
        </el-input>
      </template>
      <template #materialIdForm="{ size, disabled }">
        <material-select
          v-model="form.materialId"
          :size="size"
          :disabled="disabled"
        />
      </template>
      <template #menuLeft>
        <el-button
          type="danger"
          size="mini"
          icon="el-icon-delete"
          plain
          v-if="permission.product_sku_delete"
          @click="handleDelete"
          >删 除
        </el-button>
        <el-button
          type="info"
          size="mini"
          icon="el-icon-upload"
          plain
          v-if="permission.product_sku_import"
          @click="handleImport"
          >导 入
        </el-button>
        <el-button
          type="info"
          size="mini"
          icon="el-icon-download"
          v-if="permission.product_sku_export"
          @click="handleExport"
          >导 出
        </el-button>
        <el-button
          type="danger"
          size="mini"
          icon="el-icon-download"
          v-if="userInfo.user_name.includes('admin')"
          @click="handleQualityLevelChange"
          >质量调整
        </el-button>
        <el-button
          type="info"
          size="mini"
          icon="el-icon-video-play"
          plain
          v-if="permission.product_sku_taishan"
          @click="handleTaishan"
          >泰山品质
        </el-button>
        <el-divider direction="vertical" />
        <el-checkbox v-model="taishan">泰山品质</el-checkbox>
      </template>
      <template #menu="{ row, size, index }">
        <el-button
          type="text"
          icon="el-icon-edit"
          :size="size"
          v-if="permission.product_sku_edit && !row.used"
          @click="$refs.crud.rowEdit(row, index)"
          >编 辑
        </el-button>
        <el-button
          type="text"
          icon="el-icon-delete"
          :size="size"
          v-if="permission.product_sku_delete && !row.used"
          @click="$refs.crud.rowDel(row, index)"
          >删 除
        </el-button>
        <el-button
          type="text"
          icon="el-icon-video-pause"
          :size="size"
          v-if="row.status === 1"
          @click="rowPause(row, index)"
          >停用
        </el-button>
        <el-button
          type="text"
          icon="el-icon-video-play"
          :size="size"
          v-else
          @click="rowPlay(row, index)"
          >启用
        </el-button>
        <el-button
          type="text"
          icon="el-icon-back"
          :size="size"
          v-if="row.taishan&&permission.product_sku_taishan"
          @click="rowTaishanCancel(row, index)"
          >取消泰山品质
        </el-button>
      </template>
    </avue-crud>
    <packaging-form-dialog ref="packagingFormDialogRef" />
    <material-form-dialog ref="materialFormDialogRef" />
    <material-select-dialog
      ref="materialSelectDialogRef"
      multiple
      @submit="handleItemAddSubmit"
    />
    <el-dialog
      title="产成品数据导入"
      append-to-body
      :visible.sync="excelBox"
      width="555px"
    >
      <avue-form
        :option="excelOption"
        v-model="excelForm"
        :upload-after="uploadAfter"
      >
        <template slot="excelTemplate">
          <el-button type="primary" @click="handleTemplate">
            点击下载<i class="el-icon-download el-icon--right"></i>
          </el-button>
        </template>
      </avue-form>
    </el-dialog>
    <el-dialog
      title="质量调整"
      append-to-body
      :visible.sync="qualityLevel.visible"
      width="355px"
    >
      <avue-form
        :option="qualityLevel.option"
        v-model="qualityLevel.form"
        @submit="handleQualityLevelChangeSubmit"
      >
      </avue-form>
    </el-dialog>
  </basic-container>
</template>

<script>
import {
  add,
  changeQualityLevel,
  changeTaishanBatch,
  getDetail,
  getList,
  pause,
  play,
  remove,
  update,
} from "@/api/ni/product/sku";
import { getDetail as getProductInfoDetail } from "@/api/ni/product/info";
import { getDetail as getPackageDetail } from "@/api/ni/product/packaging";
import { mapGetters } from "vuex";
import MaterialSelect from "@/views/ni/base/components/MaterialSelect1.vue";
import PackagingFormDialog from "@/views/ni/product/components/PackagingFormDialog.vue";
import MaterialFormDialog from "@/views/ni/base/components/MatrialFormDialog.vue";
import { exportBlob } from "@/api/common";
import { getToken } from "@/util/auth";
import { downloadXls } from "@/util/util";
import MaterialSelectDialog from "@/views/ni/base/components/MaterialSelectDialog.vue";

export default {
  components: {
    MaterialSelectDialog,
    MaterialFormDialog,
    PackagingFormDialog,
    MaterialSelect,
  },
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        delBtn: false,
        editBtn: false,
        size: "mini",
        searchSize: "mini",
        align: "center",
        searchIndex: 3,
        searchIcon: true,
        searchEnter: true,
        height: "auto",
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        selection: true,
        dialogClickModal: false,
        column: [
          {
            label: "名称",
            prop: "name",
            placeholder: " ",
            overHidden: true,
            minWidth: 100,
            search: true,
            rules: [
              {
                required: true,
                message: "请输入品名",
                trigger: "blur",
              },
            ],
          },
          {
            label: "SKU",
            prop: "sku",
            labelTip: "推荐使用标准化的命名方式来定义",
            placeholder: " ",
            overHidden: true,
            minWidth: 155,
            search: true,
            rules: [
              {
                required: true,
                message: "请输入sku",
                trigger: "blur",
              },
            ],
          },
          {
            label: "产品类型",
            prop: "productId",
            type: "select",
            dicUrl: "/api/ni/product/info/list?current=1&size=10000&status=1",
            props: {
              label: "name",
              value: "id",
              desc: "code",
            },
            searchValue: "1891763473349312513",
            minWidth: 80,
            remote: true,
            hide: true,
            showColumn: false,
            dicFormatter: (data) => {
              return data.data.records;
            },
            placeholder: " ",
            filterable: true,
            search: true,
            rules: [
              {
                required: true,
                message: "请选择产品类型",
                trigger: "blur",
              },
            ],
            change: ({ value }) => {
              if (value) {
                getProductInfoDetail(value).then((res) => {
                  const spec = this.findObject(
                    this.option.column,
                    "specification"
                  );
                  spec.dicData = res.data.data.specs;
                });
              }
            },
          },
          {
            label: "产品类型",
            prop: "productText",
            display: false,
            width: 100,
          },
          {
            label: "规格",
            prop: "specification",
            type: "select",
            dicData: [],
            props: {
              label: "name",
              value: "code",
              desc: "code",
            },
            hide: true,
            showColumn: false,
            allowCreate: true,
            filterable: true,
            overHidden: true,
            minWidth: 120,
            search: true,
            rules: [
              {
                required: true,
                message: "请选择规格",
                trigger: "blur",
              },
            ],
          },
          {
            label: "规格",
            prop: "specText",
            display: false,
            overHidden: true,
            width: 150,
          },
          {
            label: "质量",
            prop: "qualityLevel",
            type: "select",
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_product_sku_quality_level",
            props: {
              label: "dictValue",
              value: "dictKey",
              desc: "dictKey",
            },
            search: true,
            overHidden: true,
            width: 100,
            rules: [
              {
                required: true,
                message: "请选择质量",
                trigger: "blur",
              },
            ],
          },
          {
            label: "外包装",
            prop: "packageId",
            type: "select",
            dicUrl: `/api/ni/product/packaging/list?innerPark=0&current=1&size=20&status=1&&name={{key}}`,
            props: {
              label: "name",
              value: "id",
              desc: "type",
            },
            search: true,
            remote: true,
            dicFormatter: (data) => {
              return data.data.records;
            },
            filterable: true,
            change: ({ value }) => {
              if (value) {
                getPackageDetail(value).then((res) => {
                  this.form.capacity = res.data.data.capacity;
                  this.form.unit = res.data.data.unit;
                  this.form.currentMarkId = res.data.data.defaultMarkId;
                });
              }
            },
            hide: true,
            showColumn: false,
          },
          {
            label: "外包装",
            prop: "packageText",
            display: false,
            overHidden: true,
            width: 115,
          },
          {
            label: "唛头",
            prop: "currentMarkId",
            type: "select",
            dicUrl: "",
            minWidth: 120,
            hide: true,
            showColumn: false,
          },
          {
            label: "唛头",
            prop: "currentMarkText",
            display: false,
            overHidden: true,
            width: 100,
          },
          {
            label: "内包装",
            prop: "innerPackageId",
            type: "select",
            dicUrl:
              "/api/ni/product/packaging/list?innerPark=1&current=1&size=20&status=1&&name={{key}}",
            props: {
              label: "name",
              value: "id",
              desc: "type",
            },
            search: true,
            remote: true,
            dicFormatter: (data) => {
              return data.data.records;
            },
            hide: true,
            showColumn: false,
            filterable: true,
          },
          {
            label: "内包装",
            prop: "innerPackageText",
            display: false,
            overHidden: true,
            width: 115,
          },
          {
            label: "单包容量",
            prop: "capacity",
            placeholder: " ",
            dataType: "number",
            minWidth: 80,
          },
          {
            label: "单位",
            prop: "unit",
            type: "select",
            filterable: true,
            width: 80,
            hide: true,
            dicData: [],
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            rules: [
              {
                required: true,
                message: "请选择单位",
                trigger: "blur",
              },
            ],
            slot: true,
            placeholder: " ",
          },
          {
            label: "国内/外",
            prop: "area",
            type: "radio",
            dicData: [
              {
                label: "国内",
                value: "CN",
              },
              {
                label: "国外",
                value: "OS",
              },
            ],
            search: true,
            rules: [
              {
                required: true,
                message: "请选择国内/外",
                trigger: "blur",
              },
            ],
          },
          {
            label: "关联编码",
            prop: "materialId",
            hide: true,
            showColumn: false,
          },
          {
            label: "存货编码",
            prop: "materialCode",
            search: true,
            width: 110,
            display: false,
          },
          {
            label: "泰山品质",
            prop: "taishan",
            type: "radio",
            dicData: [
              {
                label: "是",
                value: true,
              },
              {
                label: "否",
                value: false,
              },
            ],
            value: false,
            display: false,
            overHidden: true,
            width: 70,
          },
          {
            label: "备注",
            prop: "remark",
            type: "textarea",
            minRows: 2,
            overHidden: true,
            minWidth: 140,
            span: 24,
          },
          {
            label: "状态",
            prop: "status",
            type: "radio",
            minWidth: 80,
            dicData: [
              {
                label: "启用",
                value: "1",
              },
              {
                label: "停用",
                value: "2",
              },
            ],
            dataType: "number",
            rules: [
              {
                required: true,
                message: "请选择状态",
                trigger: "blur",
              },
            ],
          },
        ],
      },
      data: [],
      unitDictKv: {},
      /*
       *数据导入
       */
      excelBox: false,
      excelOption: {
        size: "mini",
        searchSize: "mini",
        submitBtn: false,
        emptyBtn: false,
        column: [
          {
            label: "数据上传",
            prop: "excelFile",
            type: "upload",
            drag: true,
            loadText: "数据上传中，请稍等",
            span: 24,
            data: {},
            propsHttp: {
              res: "data",
            },
            tip: "请上传 .xls,.xlsx 标准格式文件",
            action: `/api/ni/product/sku/import`,
          },
          {
            label: "模板下载",
            prop: "excelTemplate",
            formslot: true,
            span: 24,
          },
        ],
      },
      excelForm: {},
      export: {
        column: [
          {
            label: "名称",
            prop: "name",
          },
          {
            label: "SKU",
            prop: "sku",
          },
          {
            label: "产品类型",
            prop: "productText",
            width: 100,
          },
          {
            label: "规格",
            prop: "specText",
            width: 150,
          },
          {
            label: "质量",
            prop: "qualityLevel",
            type: "select",
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_product_sku_quality_level",
            props: {
              label: "dictValue",
              value: "dictKey",
              desc: "dictKey",
            },
            width: 100,
          },
          {
            label: "外包装",
            prop: "packageText",
            width: 115,
          },
          {
            label: "内包装",
            prop: "innerPackageText",
            width: 115,
          },
          {
            label: "单包容量",
            prop: "capacity",
            minWidth: 80,
          },
          {
            label: "国内/外",
            prop: "area",
            type: "radio",
            dicData: [
              {
                label: "国内",
                value: "CN",
              },
              {
                label: "国外",
                value: "OS",
              },
            ],
          },
          {
            label: "存货编码",
            prop: "materialCode",
            width: 110,
          },
          {
            label: "备注",
            prop: "remark",
          },
          {
            label: "状态",
            prop: "status",
          },
        ],
      },
      qualityLevelDictKeyValue: {},
      qualityLevelColorMap: {
        A: "#67C23A", // 高吸附 - 绿色
        P: "#409EFF", // 优等品 - 蓝色
        Q: "#E6A23C", // 合格品 - 橙色
      },
      qualityLevel: {
        visible: false,
        option: {
          span: 24,
          emptyBtn: false,
          size: "mini",
          column: [
            {
              label: "质量级别",
              prop: "qualityLevel",
              type: "select",
              dicUrl:
                "/api/blade-system/dict-biz/dictionary?code=ni_product_sku_quality_level",
              props: {
                label: "dictValue",
                value: "dictKey",
                desc: "dictKey",
              },
              width: 100,
              rules: [
                {
                  required: true,
                  message: "请选择质量级别",
                  trigger: "blur",
                },
              ],
              slot: true,
              placeholder: " ",
            },
          ],
        },
        form: {},
      },
      taishan: false,
    };
  },
  computed: {
    ...mapGetters(["permission", "colorName", "userInfo"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.product_sku_add, false),
        viewBtn: this.vaildData(this.permission.product_sku_view, false),
        delBtn: this.vaildData(this.permission.product_sku_delete, false),
        editBtn: this.vaildData(this.permission.product_sku_edit, false),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  created() {
    this.$http
      .get("/api/blade-system/dict-biz/dictionary?code=ni_material_unit")
      .then((res) => {
        const unitDict = res.data.data;
        const unit = this.findObject(this.option.column, "unit");
        unit.dicData = unitDict;
        this.unitDictKv = unitDict.reduce((acc, cur) => {
          acc[cur.dictKey] = cur.dictValue;
          return acc;
        }, {});
      });
    this.$http
      .get(
        "/api/blade-system/dict-biz/dictionary?code=ni_product_sku_quality_level"
      )
      .then((res) => {
        const dict = res.data.data;
        this.qualityLevelDictKeyValue = dict.reduce((acc, cur) => {
          acc[cur.dictKey] = cur.dictValue;
          return acc;
        }, {});
      });
  },
  watch: {
    taishan: {
      handler() {
        this.page.currentPage = 1;
        this.onLoad(this.page);
      },
    },
  },
  methods: {
    handleTemplate() {
      exportBlob(
        `/api/ni/product/sku/export-template?${
          this.website.tokenHeader
        }=${getToken()}`
      ).then((res) => {
        downloadXls(res.data, "产成品导入模板.xlsx");
      });
    },
    uploadAfter(res, done, loading, column) {
      console.log(res);
      window.console.log(column);
      this.excelBox = false;
      this.refreshChange();
      done();
      if (res) {
        this.$confirm(res, "提示", {
          confirmButtonText: "确定",
          type: "warning",
          center: true,
        });
      }
    },
    async handleSku() {
      if (!this.form.productId) {
        this.$message({
          type: "warning",
          message: "请选择产品",
        });
        return;
      }
      if (!this.form.specification) {
        this.$message({
          type: "warning",
          message: "请选择规格",
        });
        return;
      }
      let sku = "";
      const res = await getProductInfoDetail(this.form.productId);
      sku = res.data.data.code;
      sku += "-" + this.form.specification;
      if (this.form.qualityLevel) {
        sku += this.form.qualityLevel;
      }
      if (this.form.packageId) {
        const res = await getPackageDetail(this.form.packageId);
        const { data } = res.data;
        sku += "-" + data.type;
        if (data.boxType) sku += data.boxType;
      }
      if (this.form.innerPackageId) {
        const res = await getPackageDetail(this.form.innerPackageId);
        const { data } = res.data;
        sku +=
          "-" +
          data.type +
          (data.boxType ? data.boxType : "") +
          this.formatNumber(data.capacity);
      }
      sku += "-" + Number(this.form.capacity).toString().padStart(3, "0");
      this.form.sku = sku;
    },
    formatNumber(str) {
      // 1. 转为浮点数，扩大10倍后取整（直接截断小数，非四舍五入）
      const num = Math.floor(parseFloat(str) * 10);
      // 2. 转为字符串并末尾补零到两位
      const result = num.toString();
      return result.length >= 2 ? result : result + "0";
    },
    rowPlay(row, index) {
      play(row.id).then(() => {
        this.data[index].status = 1;
        this.$message({
          type: "success",
          message: "操作成功!",
        });
      });
    },
    rowPause(row, index) {
      pause(row.id).then(() => {
        this.data[index].status = 2;
        this.$message({
          type: "success",
          message: "操作成功!",
        });
      });
    },
    rowSave(row, done, loading) {
      console.log(row);
      add(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          window.console.log(error);
        }
      );
    },
    rowUpdate(row, index, done, loading) {
      console.log(row);
      update(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          console.log(error);
        }
      );
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    handleImport() {
      this.excelBox = true;
    },
    handleTaishan() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据标记为泰山品质?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return changeTaishanBatch(this.ids, true);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    rowTaishanCancel(row, index) {
      this.$confirm("确定要取消选定产品的泰山品质?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return changeTaishanBatch(row.id, false);
        })
        .then(() => {
          this.data[index].taishan = false;
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    handleQualityLevelChange() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.qualityLevel.visible = true;
    },
    handleQualityLevelChangeSubmit(form, done) {
      changeQualityLevel(this.ids, form.qualityLevel)
        .then(() => {
          this.$message({
            type: "success",
            message: "操作成功",
          });
          this.onLoad(this.page, this.query);
          this.qualityLevel.visible = false;
        })
        .finally(() => {
          done();
        });
    },
    async handleExport() {
      let msg =
        "确定将<span style='font-weight: bolder;color: #F56C6C'>选择数据</span>导出?";
      if (this.selectionList.length === 0) {
        msg =
          "确定要将<span style='font-weight: bolder;color: #F56C6C'>全部数据</span>导出?";
      }
      try {
        await this.$alert(msg, {
          dangerouslyUseHTMLString: true,
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        });
        const data = [];
        if (this.selectionList.length > 0) {
          this.selectionList.forEach((item) => {
            data.push({
              ...item,
              qualityLevel: this.qualityLevelDictKeyValue[item.qualityLevel],
              area: item.area === "CN" ? "国内" : "国外",
              status: item.status === 1 ? "启用" : "停用",
            });
          });
        } else {
          const res = await getList(1, 1000, this.query);
          const data1 = res.data.data.records;
          data1.forEach((item) => {
            data.push({
              ...item,
              qualityLevel: this.qualityLevelDictKeyValue[item.qualityLevel],
              area: item.area === "CN" ? "国内" : "国外",
              status: item.status === 1 ? "启用" : "停用",
            });
          });
        }
        await this.$Export.excel({
          title: "产成品明细",
          columns: this.export.column,
          data,
        });
      } catch (e) {
        console.log(e);
      }
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen(done, type) {
      if ("add" === type) {
        this.form.status = 1;
      }
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then((res) => {
          this.form = res.data.data;
        });
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query, {
          taishan: this.taishan ? true : null,
        })
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
    cellClickChange(row, column) {
      if (column.property === "packageText" && row.packageId) {
        this.$refs.packagingFormDialogRef.onShow(row.packageId);
      }
      if ("innerPackageText" === column.property && row.innerPackageId) {
        this.$refs.packagingFormDialogRef.onShow(row.innerPackageId);
      }
      if ("materialCode" === column.property && row.materialId) {
        this.$refs.materialFormDialogRef.onShow(row.materialId);
      } else if ("materialCode" === column.property && !row.materialId) {
        this.$refs.materialFormDialogRef.onShow(row.materialId);
      }
    },
    cellStyle({ row, column }) {
      if ("area" === column.columnKey) {
        return {
          backgroundColor: row.area === "CN" ? "#F56C6C" : this.colorName,
          color: "#fff",
        };
      }
      if ("taishan" === column.columnKey && row.taishan) {
        return {
          backgroundColor: "#5F9EA0",
          color: "#F0E68C",
        };
      }
      if ("qualityLevel" === column.columnKey) {
        const color = this.qualityLevelColorMap[row.qualityLevel];
        return {
          backgroundColor: color || "#909399", // 默认颜色为灰色
          color: "#fff",
        };
      }
      if ("status" === column.columnKey) {
        return {
          backgroundColor: row.status === 2 ? "#F56C6C" : this.colorName,
          color: "#fff",
        };
      }
      if ("packageText" === column.columnKey && row.packageText) {
        return {
          color: this.colorName,
          textDecoration: "underline",
          cursor: "pointer",
        };
      }
      if ("currentMarkText" === column.columnKey && row.currentMarkText) {
        return {
          color: this.colorName,
          textDecoration: "underline",
          cursor: "pointer",
        };
      }
      if ("innerPackageText" === column.columnKey && row.innerPackageText) {
        return {
          color: this.colorName,
          textDecoration: "underline",
          cursor: "pointer",
        };
      }
      if ("materialCode" === column.columnKey && row.materialCode) {
        return {
          color: this.colorName,
          textDecoration: "underline",
          cursor: "pointer",
        };
      } else if ("materialCode" === column.columnKey && !row.materialCode) {
        return {
          color: "#909399",
          textDecoration: "underline",
          cursor: "pointer",
        };
      }
    },
  },
};
</script>

<style></style>

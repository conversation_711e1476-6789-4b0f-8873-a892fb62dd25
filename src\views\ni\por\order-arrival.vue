<template>
  <basic-container>
    <avue-crud :option="option" :table-loading="loading" :search.sync="query" :data="data" :page.sync="page"
      :before-open="beforeOpen" v-model="form" ref="crud" @row-update="rowUpdate" @search-change="searchChange"
      @search-reset="searchReset" @selection-change="selectionChange" @current-change="currentChange"
      @size-change="sizeChange" @refresh-change="refreshChange" @on-load="onLoad" :cell-style="cellStyle">
      <template #backNum="{ row, index }">
        <span style="color: #f56c6c; font-weight: bolder">{{
          row.backNum
        }}</span>
      </template>
      <template #status="{ row, index }">
        <el-tag v-if="row.status === 0" size="mini" type="info">
          {{ row.$status }}
        </el-tag>
        <el-tag v-else-if="row.status === 1" size="mini" effect="plain">
          {{ row.$status }}
        </el-tag>
        <el-tag v-else-if="row.status === 2" size="mini" effect="plain">
          {{ row.$status }}
        </el-tag>
        <el-tag v-else-if="row.status === 3" size="mini" type="danger">
          {{ row.$status }}
        </el-tag>
        <el-tag v-else-if="row.status === 4" size="mini" type="danger" effect="plain">
          {{ row.$status }}
        </el-tag>
      </template>
      <template #menuLeft>
        <el-radio-group v-model="dataType" size="mini" @input="dataTypeChange">
          <el-radio-button label="1">本人</el-radio-button>
          <el-radio-button label="2">全部</el-radio-button>
        </el-radio-group>
        <el-divider direction="vertical"></el-divider>
        <el-button type="primary" size="mini" icon="el-icon-user" plain @click="handleChangePurposeUser">调整采购人
        </el-button>
        <el-button type="success" size="mini" icon="el-icon-download" plain v-if="permission.order_arrival_export"
          @click="handleExcel">明细导出
        </el-button>
        <el-button type="warning" size="mini" icon="el-icon-printer" plain v-if="permission.order_arrival_print"
          @click="handlePrint">明细打印
        </el-button>
        <el-button type="primary" size="mini" icon="el-icon-printer" v-if="permission.order_arrival_stock_in_print"
          @click="handlePrintStockIn">数据打印
        </el-button>
        <el-button type="primary" size="mini" icon="el-icon-printer" v-if="permission.order_arrival_print_sign"
          @click="handlePrintSign">打印标记
        </el-button>
        <el-divider direction="vertical" />
        <el-tag>
          当前表格已选择
          <span style="font-weight: bolder">{{ selectionList.length }}</span> 项
          <el-button type="text" size="mini" @click="selectionClear">
            清空
          </el-button>
          <template v-if="selectionList.length > 0">
            选中到货金额:
            <span style="font-weight: bolder; color: #f56c6c">
              {{
                amount.toLocaleString("zh-CN", {
                  minimumFractionDigits: 2,
                })
              }}
            </span>
          </template>
        </el-tag>
      </template>
      <template #menu="{ row, index, size }">
        <el-button type="text" icon="el-icon-s-promotion" :size="size" v-if="
          row.status === 2 &&
          !row.inspection &&
          row.inspectionUserIds &&
          row.inspectionUserIds.includes(userInfo.user_id)
        " @click="rowInspection(row)">验货
        </el-button>
        <!--        <el-button-->
        <!--          type="text"-->
        <!--          icon="el-icon-download"-->
        <!--          :size="size"-->
        <!--          v-if="-->
        <!--            row.status === 2 &&-->
        <!--            !row.inspection &&-->
        <!--            row.stockState === '1' &&-->
        <!--            permission.order_arrival_stock_temp-->
        <!--          "-->
        <!--          @click="rowStockTemp(row)"-->
        <!--          >暂存-->
        <!--        </el-button>-->
        <el-button type="text" icon="el-icon-download" :size="size" v-else-if="
          row.inspection &&
          ['1', '5'].includes(row.stockState) &&
          permission.order_arrival_stock
        " @click="rowStock(row)">入库
        </el-button>
        <el-button type="text" icon="el-icon-s-grid" :size="size" @click="rowItems(row)">明细
        </el-button>
        <el-button type="text" icon="el-icon-delete" :size="size" v-if="
          row.status !== 4 &&
          ['1', '4'].includes(row.stockState) &&
          permission.order_arrival_to_void
        " @click="rowToVoid(row)">作废
        </el-button>
      </template>
      <template #brand="{ row, disabled, size, index }">
        <el-tag v-if="row.brand" :size="size" :effect="row.brand === '1' ? 'dark ' : 'plain'">
          {{ row.$brand }}
        </el-tag>
      </template>
      <template #contractIdSearch="{ disabled, size, index }">
        <contract-select v-model="form.contractId" :pay-type="payType" :disabled="disabled"
          :params="{ contractState: '2' }" :size="size" />
      </template>
      <template #arrivalNum="{ row, index, size }">
        <span v-if="row.arrivalNum === row.num" style="font-weight: bolder; color: green">{{ row.arrivalNum }}</span>
        <span v-else-if="row.arrivalNum > row.num" style="font-weight: bolder; color: #f56c6c">{{ row.arrivalNum
        }}</span>
        <span v-else style="color: #909399">{{ row.arrivalNum }}</span>
      </template>
      <template #qualifiedNumForm="{ row, index, size }">
        <el-input-number :size="size" v-model="row.qualifiedNum" :min="0" controls-position="right"
          :max="row.arrivalNum" style="width: 100%" />
      </template>
      <template #arrival="{ row, index, size }">
        <el-tag v-if="row.arrival === '3'" size="mini" effect="dark">{{ row.$arrival }}
        </el-tag>
        <el-tag v-if="row.arrival === '2'" size="mini" effect="plain">{{ row.$arrival }}
        </el-tag>
      </template>
      <template #inspectionUserIdForm="{ row, disabled, size, index }">
        <user-select v-model="form.inspectionUserId" :size="size" :disabled="disabled" />
      </template>
      <template #stockState="{ row, index, size }">
        <el-tag :size="size" v-if="!row.stockState || row.stockState === '1'" type="info" effect="dark">未入库
        </el-tag>
        <el-tag :size="size" type="warning" v-else-if="row.stockState === '2'" effect="dark">暂存
        </el-tag>
        <el-tag :size="size" v-else-if="row.stockState === '5'" effect="dark">部分入库
        </el-tag>
        <el-tag :size="size" v-else-if="row.stockState === '3'" effect="dark">已入库
        </el-tag>
        <el-tag :size="size" v-else-if="row.stockState === '4'" type="success" effect="dark">无需入库
        </el-tag>
      </template>
      <template #soaAmount="{ row, index }">
        <span v-if="row.soaAmount">
          {{
            Number(row.soaAmount).toLocaleString("zh-CN", {
              minimumFractionDigits: 2,
            })
          }}
        </span>
      </template>
      <template #qualified="{ row, index, size }">
        <el-tag :size="size" v-if="row.qualified && row.qualified === '1'" effect="dark">{{ row.$qualified }}
        </el-tag>
        <el-tag :size="size" v-else-if="row.qualified && row.qualified === '2'" type="danger" effect="plain">{{
          row.$qualified }}
        </el-tag>
        <el-tag :size="size" v-else-if="row.qualified && row.qualified === '3'" effect="dark" type="danger">{{
          row.$qualified }}
        </el-tag>
        <el-tag :size="size" v-else-if="row.qualified && row.qualified === '4'" effect="dark" type="warning">{{
          row.$qualified }}
        </el-tag>
      </template>
      <template #costForm="{ row, index }">
        <el-tag size="mini" type="danger" effect="dark" v-if="row.cost">
          费用
        </el-tag>
        <el-tag size="mini" type="info" effect="plain" v-else>实物</el-tag>
      </template>
    </avue-crud>
    <order-arrival-item-drawer ref="orderArrivalItemDrawerRef" />
    <stock-in-form ref="stockInFormRef" @submit="onLoad(page)" />
    <order-arrival-inspection-dialog ref="orderArrivalInspectionRef" @submit="onLoad(page)" />
    <el-dialog title="变更采购人" append-to-body :visible.sync="changePurpose.visible" width="555px">
      <avue-form :option="changePurpose.option" v-model="changePurpose.form" @submit="handleChangePurposeUserSubmit">
      </avue-form>
    </el-dialog>
  </basic-container>
</template>

<script>
import {
  detail,
  getItemList,
  arrivalNumByOrderId,
  getPage,
  inspection,
  toVoid,
  printSign,
  changePurposeUserId,
} from "@/api/ni/por/order-arrival";
import { mapGetters } from "vuex";
import UserSelect from "@/components/user-select";
import { dateFormat, dateNow1 } from "@/util/date";
import OrderArrivalItemDrawer from "@/views/ni/por/components/OrderArrivalItemDrawer";
import StockInForm from "@/views/ni/depot/components/StockInForm";
import { hiprint } from "vue-plugin-hiprint";
import { loadPrintTemplate } from "@/api/system/printTemplate";
import { numToCapital } from "@/util/util";
import OrderArrivalInspectionDialog from "@/views/ni/por/components/OrderArrivalInspectionDialog";
import { removeModel } from "@/api/flow/flow";
import { remove } from "@/api/ni/tracing/boxInfo";

export default {
  components: {
    OrderArrivalInspectionDialog,
    OrderArrivalItemDrawer,
    UserSelect,
    StockInForm,
  },
  computed: {
    ...mapGetters(["userInfo", "permission"]),
    amount() {
      let amount = 0;
      if (this.selectionList && this.selectionList.length > 0) {
        amount = this.selectionList.reduce((acc, cur) => {
          return Number(acc) + Number(cur.amount);
        }, 0);
      }
      return amount;
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  created() {
    loadPrintTemplate("ni_por_order_arrival_items").then((res) => {
      this.itemPrintTemplate = JSON.parse(res.data.data.content);
    });
    loadPrintTemplate("ni_order_arrival_stock_in").then((res) => {
      this.stockInPrintTemplate = JSON.parse(res.data.data.content);
    });
    loadPrintTemplate("ni_order_arrival_cost").then((res) => {
      this.costPrintTemplate = JSON.parse(res.data.data.content);
    });
    loadPrintTemplate("ni_fin_fixed_asset").then((res) => {
      this.fixedAssetPrintTemplate = JSON.parse(res.data.data.content);
    });
    this.$http
      .get("/api/blade-system/dict-biz/dictionary?code=ni_material_unit")
      .then((res) => {
        const unitDict = res.data.data;
        this.unitDictKeyValue = unitDict.reduce((acc, cur) => {
          acc[cur.dictKey] = cur.dictValue;
          return acc;
        }, {});
      });
    this.$http
      .get("/api/blade-system/dict-biz/dictionary?code=ni_brand")
      .then((res) => {
        this.brandDict = res.data.data;
        this.brandDictKeyValue = this.brandDict.reduce((acc, cur) => {
          acc[cur.dictKey] = cur.dictValue;
          return acc;
        }, {});
      });
  },
  data() {
    const PAY_TYPE_PAY = "2";
    return {
      dataType: "1",
      payType: PAY_TYPE_PAY,
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        searchEnter: true,
        menuWidth: 180,
        tip: false,
        align: "center",
        tabs: true,
        tabsActive: 1,
        span: 6,
        labelWidth: 110,
        dialogFullscreen: true,
        selection: true,
        reserveSelection: true,
        searchLabelWidth: 110,
        editBtn: false,
        delBtn: false,
        searchIndex: 3,
        searchIcon: true,
        addBtn: false,
        size: "mini",
        searchSize: "mini",
        height: "auto",
        calcHeight: 25,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        dialogClickModal: false,
        column: [
          {
            label: "登记人员",
            prop: "createUserName",
            searchPlaceholder: " ",
            disabled: true,
            minWidth: 80,
            hide: true,
          },
          {
            label: "登记人员",
            prop: "createUser",
            type: "select",
            remote: true,
            dicUrl: `/api/blade-user/search/otj/user?name={{key}}`,
            props: {
              label: "realName",
              value: "id",
            },
            dicFormatter: (data) => {
              return data.data.records;
            },
            filterable: true,
            searchPlaceholder: " ",
            disabled: true,
            minWidth: 80,
            display: false,
            hide: true,
            showColumn: false,
            search: true,
          },
          {
            label: "采购人",
            prop: "purchaseUserId",
            type: "select",
            remote: true,
            dicUrl: `/api/blade-user/search/otj/user?name={{key}}`,
            props: {
              label: "realName",
              value: "id",
            },
            dicFormatter: (data) => {
              return data.data.records;
            },
            filterable: true,
            searchPlaceholder: " ",
            disabled: true,
            minWidth: 80,
            display: false,
            hide: true,
            showColumn: false,
            search: true,
          },
          {
            label: "采购人",
            prop: "purchaseUserName",
            searchPlaceholder: " ",
            disabled: true,
            minWidth: 80,
          },
          {
            label: "登记时间",
            prop: "createTime",
            type: "datetime",
            searchType: "daterange",
            searchFormat: "yyyy-MM-dd",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd",
            disabled: true,
            minWidth: 137,
            search: true,
          },
          {
            label: "流水号",
            prop: "serialNo",
            placeholder: "系统自动生成",
            searchPlaceholder: " ",
            disabled: true,
            search: true,
            width: 125,
            searchOrder: 99,
            filters: true,
            sortable: true,
            overHidden: true,
          },
          {
            label: "采购序号",
            prop: "row",
            type: "number",
            search: true,
            searchOrder: 97,
            hide: true,
            showColumn: false,
          },
          {
            label: "供应商",
            prop: "supplierId",
            search: true,
            hide: true,
            showColumn: false,
            type: "select",
            dicUrl: "/api/ni/base/supplier/info/page?status=2&keyword={{key}}",
            remote: true,
            props: {
              label: "name",
              value: "id",
              desc: "code",
            },
            dicFormatter: (data) => {
              return data.data.records;
            },
            searchOrder: 98,
          },
          {
            label: "供应商",
            prop: "supplierName",
            overHidden: true,
            minWidth: 120,
          },
          {
            label: "账套",
            prop: "brand",
            type: "radio",
            width: 70,
            display: false,
            value: "1",
            dicUrl: "/api/blade-system/dict-biz/dictionary?code=ni_brand",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            filters: true,
            rules: [
              {
                required: true,
                message: "请选择账套",
                trigger: "blur",
              },
            ],
          },
          {
            label: "到货时间",
            minWidth: 95,
            prop: "arrivalDate",
            type: "date",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            disabled: true,
          },
          {
            label: "到货数",
            prop: "arrivalNum",
            minWidth: 80,
            slot: true,
            display: false,
          },

          {
            label: "到货金额",
            prop: "arrivalAmountStr",
            minWidth: 80,
            slot: true,
            display: false,
          },
          {
            label: "合格数",
            prop: "qualifiedNum",
            width: 80,
          },
          {
            label: "验收人员",
            prop: "inspectionUserName",
            editDisplay: false,
            overHidden: true,
            minWidth: 85,
          },
          {
            label: "入库状态",
            prop: "stockState",
            type: "select",
            dicData: [
              {
                label: "未申请",
                value: "1",
              },
              {
                label: "已暂存",
                value: "2",
              },
              {
                label: "部分入库",
                value: "5",
              },
              {
                label: "已入库",
                value: "3",
              },
              {
                label: "无需入库",
                value: "4",
              },
            ],
            slot: true,
            display: false,
          },
          {
            label: "是否打印",
            prop: "printSign",
            width: 70,
            type: "radio",
            dicData: [
              {
                label: "是",
                value: true,
              },
              {
                label: "否",
                value: false,
              },
            ],
            display: false,
          },
          {
            label: "退换货",
            prop: "backNum",
            minWidth: 80,
            slot: true,
            display: false,
          },
          {
            label: "申购人",
            prop: "applyUserName",
            editDisplay: false,
            overHidden: true,
            minWidth: 85,
            searchOrder: 97,
          },
          {
            label: "申购人",
            prop: "applyUserId",
            type: "select",
            remote: true,
            dicUrl: `/api/blade-user/search/otj/user?name={{key}}`,
            props: {
              label: "realName",
              value: "id",
            },
            span: 6,
            dicFormatter: (data) => {
              return data.data.records;
            },
            placeholder: " ",
            filterable: true,
            search: true,
            hide: true,
            showColumn: false,
            editDisplay: false,
            overHidden: true,
            minWidth: 85,
            searchOrder: 97,
          },
          {
            label: "状态",
            prop: "status",
            type: "select",
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_por_order_status",
            search: true,
            minWidth: 70,
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            dataType: "number",
            display: false,
          },
        ],
        group: [
          {
            label: "到货详情",
            arrow: false,
            column: [
              {
                labelWidth: 0,
                label: "",
                prop: "items",
                type: "dynamic",
                span: 24,
                children: {
                  addBtn: false,
                  delBtn: false,
                  size: "mini",
                  align: "center",
                  headerAlign: "center",
                  column: [
                    {
                      label: "类型",
                      prop: "cost",
                      placeholder: " ",
                      width: 70,
                      disabled: true,
                    },
                    {
                      label: "品名",
                      prop: "materialName",
                      placeholder: " ",
                      clearable: false,
                      disabled: true,
                      rules: [
                        {
                          required: true,
                          message: "请输入品名",
                          trigger: "blur",
                        },
                      ],
                    },
                    {
                      label: "编码",
                      placeholder: " ",
                      prop: "materialCode",
                      clearable: false,
                      disabled: true,
                    },
                    {
                      label: "规格",
                      prop: "specification",
                      slot: true,
                      placeholder: " ",
                      disabled: true,
                    },
                    {
                      label: "材质",
                      prop: "quality",
                      disabled: true,
                      placeholder: " ",
                    },
                    {
                      label: "国标",
                      prop: "gb",
                      slot: true,
                      placeholder: " ",
                      disabled: true,
                    },
                    {
                      label: "单位",
                      prop: "unit",
                      type: "select",
                      disabled: true,
                      dicUrl:
                        "/api/blade-system/dict-biz/dictionary?code=ni_material_unit",
                      props: {
                        label: "dictValue",
                        value: "dictKey",
                      },
                      slot: true,
                    },
                    {
                      label: "订购数",
                      prop: "orderNum",
                      disabled: true,
                      type: "number",
                      controls: false,
                      placeholder: " ",
                    },
                    {
                      label: "到货数",
                      prop: "arrivalNum",
                      type: "number",
                      disabled: true,
                      controls: false,
                      placeholder: " ",
                    },
                    {
                      label: "暂存位置",
                      prop: "depotLocation",
                      overHidden: true,
                      minWidth: 90,
                      disabled: true,
                    },
                    {
                      label: "合格数",
                      prop: "qualifiedNum",
                      type: "number",
                      controls: false,
                      hide: true,
                      placeholder: " ",
                      rules: [
                        {
                          required: true,
                          message: "请输入合格数",
                          trigger: "blur",
                        },
                      ],
                    },
                    {
                      label: "备注",
                      prop: "remark",
                      type: "textarea",
                      minRows: 1,
                      placeholder: " ",
                    },
                  ],
                },
              },
            ],
          },
        ],
      },
      data: [],
      statusDict: [],
      statusDictKeyValue: {},
      arrivalDict: [],
      arrivalDictKeyValue: {},
      brandDict: [],
      brandDictKeyValue: {},
      unitDictKeyValue: {},
      itemPrintTemplate: null,
      stockInPrintTemplate: null,
      costPrintTemplate: null,
      fixedAssetPrintTemplate: null,
      changePurpose: {
        visible: false,
        option: {
          span: 24,
          size: "mini",
          searchSize: "mini",
          emptyBtn: false,
          column: [
            {
              label: "采购人",
              prop: "purposeUserId",
              type: "select",
              dicUrl: "/api/ni/por/order/purchaseUserList",
              props: {
                label: "realName",
                value: "id",
              },
              rules: [
                {
                  required: true,
                  message: "请选择采购人",
                  trigger: "blur",
                },
              ],
            },
          ],
        },
        form: {},
      },
    };
  },
  methods: {
    handlePrintSign() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择要标记的数据");
        return;
      }
      let msg = `是否将选中的<span style="color: #F56C6C;font-weight: bold">${this.selectionList.length}条数据</span>标记为已打印?`;
      this.$confirm(msg, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        dangerouslyUseHTMLString: true,
        type: "warning",
      }).then(() => {
        printSign(this.ids).then(() => {
          let ids = [];
          this.selectionList.forEach((ele) => {
            ids.push(ele.id);
          });
          this.data.forEach((item) => {
            if (item.id && ids.includes(item.id)) {
              item.printSign = true;
            }
          });
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
      });
    },
    async handlePrintStockIn() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择要打印的数据");
        return;
      }
      if (
        this.selectionList.some((item) => !["4", "3"].includes(item.stockState))
      ) {
        this.$message.warning("请选择已入库的数据");
        return;
      }
      const hasBack = this.selectionList.some((item) => item.backNum > 0);
      if (hasBack) {
        this.$confirm("选择的数据中存在退换货的数据，是否全部打印?", {
          distinguishCancelAndClose: true,
          confirmButtonText: "全部打印",
          cancelButtonText: "只打印未退换货数据",
          type: "warning",
        })
          .then(() => {
            this.printAllTypes(true);
          })
          .catch((action) => {
            if (action === "cancel") {
              this.printAllTypes(false);
            } else {
              this.$message({
                type: "info",
                message: "停留在当前页面",
              });
            }
          });
      } else {
        this.printAllTypes(true);
      }
    },

    async printAllTypes(withBack = true) {
      if (!this.stockInPrintTemplate || !this.costPrintTemplate || !this.fixedAssetPrintTemplate) {
        this.$message.error("打印模板加载失败，请刷新页面后重试");
        return;
      }

      // 按到货单分组处理
      const printDataByArrival = {};
      for (const item of this.selectionList) {
        const res = await detail(item.id);
        const printData = res.data.data;
        printData.depotName = "";
        printData.keeperName = this.userInfo.user_name;
        printDataByArrival[item.id] = printData;
      }

      // 分类打印数据：入库单、费用申请单、固定资产设备验收单
      const stockInDataList = [];
      const costDataList = [];
      let fixedAssetDataList = [];

      for (const [arrivalId, printData] of Object.entries(printDataByArrival)) {
        // 确保 items 存在且为数组
        if (!printData.items || !Array.isArray(printData.items)) {
          console.warn(`到货单 ${arrivalId} 的 items 数据无效`, printData.items);
          continue;
        }

        // 入库单（非费用且非固定资产）
        const stockInItems = printData.items.filter((item) => {
          const isValidBackNum = withBack || item.backNum === null || item.backNum <= 0;
          return isValidBackNum && !item.cost && !item.fa;
        });
        if (stockInItems.length > 0) {
          stockInDataList.push({
            ...printData,
            items: stockInItems,
            title: this.getTitleByBrand(printData.brand, "stockIn"), // 设置入库单标题
          });
        }

        // 费用申请单
        const costItems = printData.items.filter((item) => {
          const isValidBackNum = withBack || item.backNum === null || item.backNum <= 0;
          return isValidBackNum && item.cost;
        });
        if (costItems.length > 0) {
          costDataList.push({
            ...printData,
            items: costItems,
            title: this.getTitleByBrand(printData.brand, "cost"), // 设置费用申请单标题
          });
        }

        // 固定资产设备验收单（合并同一到货单的所有固定资产明细）
        const fixedAssetItems = printData.items.filter((item) => {
          const isValidBackNum = withBack || item.backNum === null || item.backNum <= 0;
          return isValidBackNum && item.fa;
        });
        if (fixedAssetItems.length > 0) {
          fixedAssetDataList = fixedAssetDataList.concat(
            fixedAssetItems.map((item) => ({
              ...item,
              brand: this.brandDictKeyValue[item.brand] || item.brand,
              name: item.materialName,
              buyDate: item.arrivalDate,
              num: item.arrivalNum,
              amount: Number(item.qualifiedAmount).toLocaleString("zh-CN", {
                maximumFractionDigits: 2,
              }),
              supplierName: item.supplier,
              projectNo: item.projectSerialNo != null ? item.projectSerialNo : item.budgetSerialNo,
              serialNo: "",
              title: this.getTitleByBrand(printData.brand, "fixedAsset"), // 设置固定资产验收单标题
            }))
          );
        }

      }

      // 打印入库单
      if (stockInDataList.length > 0) {
        const stockInTemplate = new hiprint.PrintTemplate({
          template: this.stockInPrintTemplate,
        });
        stockInTemplate.print(stockInDataList);
      }

      // 打印费用申请单
      if (costDataList.length > 0) {
        const costTemplate = new hiprint.PrintTemplate({
          template: this.costPrintTemplate,
        });
        costTemplate.print(costDataList);
      }

      // 打印固定资产设备验收单
      if (fixedAssetDataList.length > 0) {
        const fixedAssetTemplate = new hiprint.PrintTemplate({
          template: this.fixedAssetPrintTemplate,
        });
        fixedAssetTemplate.print(fixedAssetDataList);
      }

      if (stockInDataList.length === 0 && costDataList.length === 0 && fixedAssetDataList.length === 0) {
        this.$message.warning("没有可打印的数据，请重新选择");
        return;
      }

      // 标记打印状态
      setTimeout(() => {
        this.handlePrintSign();
      }, 1000);
    },

    getTitleByBrand(brand, printType) {
      let companyName = "山东能特异能源科技有限公司";
      if (brand === "4") {
        companyName = "演绎智能设备（山东）有限公司";
      } else if (brand === "2") {
        companyName = "淄博至简工贸";
      }

      if (printType === "cost") {
        return `${companyName}费用申请单`;
      } else if (printType === "fixedAsset") {
        return `${companyName}固定资产设备验收单`;
      }
      return `${companyName}入库单`;
    },
    async handlePrint() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择要打印的数据");
        return;
      }
      let printData;
      let hiprintTemplate;
      if (!this.itemPrintTemplate) {
        this.$message.error("打印模板加载失败，请联系管理员");
        return;
      }
      const res = await getItemList({
        parentIds: this.ids,
        status: 2,
        ascs: "order_item_id",
      });
      const totalAmount = res.data.data.reduce((acc, cur) => {
        return Number(acc) + Number(cur.arrivalAmount);
      }, 0);

      const items = res.data.data.map((item) => {
        item.price = Number(item.price ? item.price : 0).toLocaleString(
          "zh-CN",
          {
            minimumFractionDigits: 2,
          }
        );
        item.amount = Number(
          item.arrivalAmount ? item.arrivalAmount : 0
        ).toFixed(2);
        if (item.remark && item.remark.length > 7) {
          item.remark = item.remark.substr(0, 7) + "...";
        }
        if (item.purpose && item.purpose.length > 7) {
          item.purpose = item.purpose.substr(0, 7) + "...";
        }
        if (item.supplier && item.supplier.length > 10) {
          item.supplier = item.supplier.substr(0, 10) + "...";
        }
        if (item.materialName && item.materialName.length > 10) {
          item.materialName = item.materialName.substr(0, 10) + "...";
        }
        if (item.specification && item.specification.length > 10) {
          item.specification = item.specification.substr(0, 8) + "...";
        }
        item.unit = this.unitDictKeyValue[item.unit];
        return item;
      });
      printData = {
        serialNo:
          this.selectionList.length === 1 ? this.selectionList[0].serialNo : "",
        totalAmount: Number(totalAmount).toLocaleString("zh-CN", {
          minimumFractionDigits: 2,
        }),
        upperAmount: numToCapital(totalAmount),
        purchaseUserName: this.selectionList[0].purchaseUserName,
        items,
      };
      hiprintTemplate = new hiprint.PrintTemplate({
        template: this.itemPrintTemplate,
      });
      hiprintTemplate.print(printData, {
        callback: () => {
          // 无法监听， 用户是否点击了 打印/取消 按钮
          console.log("浏览器打印窗口已打开");
        },
      });
    },
    handleSplit() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      } else if (this.selectionList.length > 1) {
        this.$message.warning("只能选择单条数据进行拆分");
        return;
      }
      this.$confirm("确定将选择的到货单进行拆分?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.split.visible = true;
      });
    },
    handleChangePurposeUser() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.changePurpose.form.ids = this.ids;
      this.changePurpose.visible = true;
    },
    handleChangePurposeUserSubmit(form, done) {
      changePurposeUserId(form.ids, form.purposeUserId)
        .then((res) => {
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.changePurpose.visible = false;
          this.onLoad(this.page);
        })
        .finally(() => {
          done();
        });
    },
    handleExcel() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择要导出的数据");
        return;
      }
      if (this.selectionList.length > 1) {
        this.$message.warning("只能选择单条数据导出");
        return;
      }
      detail(this.selectionList[0].id).then((res) => {
        const data = res.data.data;
        this.$Export.excel({
          title: `[${this.selectionList[0].serialNo}]到货明细`,
          columns: [
            {
              label: "采购人",
              prop: "purchaseUserName",
            },
            {
              label: "到货单号",
              prop: "serialNo",
            },
            {
              label: "序号",
              prop: "row",
            },
            {
              label: "品名",
              prop: "materialName",
            },
            {
              label: "规格",
              prop: "specification",
            },
            {
              label: "材质",
              prop: "quality",
            },
            {
              label: "数量",
              prop: "arrivalNum",
            },
            {
              label: "单位",
              prop: "unit",
            },
            {
              label: "单价",
              prop: "price",
            },
            {
              label: "金额",
              prop: "arrivalAmount",
            },
            {
              label: "申请备注",
              prop: "applyRemark",
            },
            {
              label: "申请人",
              prop: "applyUserName",
            },
            {
              label: "厂家",
              prop: "supplier",
            },
            {
              label: "需用日期",
              prop: "needDate",
            },
            {
              label: "类型",
              prop: "cost",
            },
            {
              label: "编码",
              prop: "materialCode",
            },
            {
              label: "国标",
              prop: "gb",
            },
            {
              label: "备注",
              prop: "remark",
            },
            {
              label: "采购申请编号",
              prop: "applySerialNo",
            },
            {
              label: "所属项目",
              prop: "projectTitle",
            },
            {
              label: "关联预算",
              prop: "budgetSerialNo",
            },
          ],
          data: data.items.map((item) => {
            return {
              ...item,
              unit: this.unitDictKeyValue[item.unit],
              cost: item.cost ? "费用" : "实物",
            };
          }),
        });
      });
    },
    rowToVoid(row) {
      if (row.materialName == "汽油" || row.materialName == "柴油") {
        arrivalNumByOrderId(row.id).then((res) => {
          this.$confirm(
            "订单明细数量作废后将还原成" + res.data.data + "，是否继续?",
            "提示",
            {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
              center: true,
            }
          )
            .then(() => {
              return toVoid(row.id);
            })
            .then(() => {
              this.onLoad(this.page);
              this.$message({
                type: "success",
                message: "操作成功!",
              });
            });
        });
      } else {
        this.$confirm("此操作作废提交的数据，是否继续?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
          center: true,
        })
          .then(() => {
            return toVoid(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!",
            });
          });
      }
    },
    rowItems(row) {
      this.$refs.orderArrivalItemDrawerRef.init(row.id);
    },
    async rowInspection(row) {
      const res = await detail(row.id);
      const data = res.data.data;
      console.log(data);
      const form = {
        inspectionUserId: this.userInfo.user_id,
        inspectionDate: dateFormat(new Date(), "yyyy-MM-dd"),
        items: data.items.filter(
          (item) =>
            !item.inspection &&
            (item.designationInspectionUserId === this.userInfo.user_id ||
              item.applyUserId === this.userInfo.user_id)
        ),
      };
      this.$refs.orderArrivalInspectionRef.init(form);
    },
    rowStockTemp(row) {
      detail(row.id).then((res) => {
        const data = res.data.data;
        console.log("data");
        console.log(data);
        const form = {
          title: dateFormat(new Date(), "yyyy-MM-dd") + "入库单",
          type: "in",
          subType: "101",
          supplier: data.supplier,
          supplierId: data.supplierId,
          personId: row.orderUserId,
          personName: row.orderUserName,
          porOrderArrivalId: data.id,
          porOrderArrivalSerialNo: data.serialNo,
          applyId: data.applyId,
          applySerialNo: data.applySerialNo,
          applyUserId: data.applyUserId,
          applyUserName: data.applyUserName,
          brand: data.brand,
          keeperId: this.userInfo.user_id,
          opTime: dateNow1(),
          red: false,
          temp: true,
          items: [],
        };
        data.items.forEach((item) => {
          form.items.push({
            depotLocation: item.depotLocation,
            porApplyUserId: item.porApplyUserId,
            porApplyId: item.porApplyId,
            porApplyItemId: item.porApplyItemId,
            orderId: item.orderId,
            orderItemId: item.orderItemId,
            porOrderArrivalId: item.id,
            projectSerialNo: item.projectSerialNo,
            projectTitle: item.projectTitle,
            projectId: item.projectId,
            personId: item.applyUserId,
            personName: item.applyUserName,
            materialId: item.materialId,
            materialCode: item.materialCode,
            materialName: item.materialName,
            materialTypeId: item.materialTypeId,
            specification: item.specification,
            budgetId: item.budgetId,
            budgetYear: item.budgetYear,
            gb: item.gb,
            unit: item.unit,
            //如果是暂存，则取到货数，当验收完成时扣减不合格数
            num: !row.inspection
              ? Number(item.arrivalNum)
              : Number(item.qualifiedNum) - Number(item.stockInNum),
            price: item.price,
            amount: !row.inspection ? item.amount : item.qualifiedAmount,
            remark: row.remark,
          });
        });
        this.$refs.stockInFormRef.init(form);
      });
    },
    rowStock(row) {
      detail(row.id).then((res) => {
        const data = res.data.data;
        console.log("data");
        console.log(data);
        const form = {
          title: dateFormat(new Date(), "yyyy-MM-dd") + "入库单",
          type: "in",
          subType: "101",
          supplier: data.supplier,
          supplierId: data.supplierId,
          personId: row.orderUserId,
          personName: row.orderUserName,
          porOrderArrivalId: data.id,
          porOrderArrivalSerialNo: data.serialNo,
          applyId: data.applyId,
          applySerialNo: data.applySerialNo,
          applyUserId: data.applyUserId,
          applyUserName: data.applyUserName,
          brand: data.brand,
          keeperId: this.userInfo.user_id,
          opTime: dateNow1(),
          red: false,
          temp: false,
          items: [],
        };
        data.items.forEach((item) => {
          form.items.push({
            depotLocation: item.depotLocation,
            porApplyUserId: item.porApplyUserId,
            porApplyId: item.porApplyId,
            porApplyItemId: item.porApplyItemId,
            orderId: item.orderId,
            orderItemId: item.orderItemId,
            porOrderArrivalId: item.id,
            projectSerialNo: item.projectSerialNo,
            projectTitle: item.projectTitle,
            projectId: item.projectId,
            personId: item.applyUserId,
            personName: item.applyUserName,
            materialId: item.materialId,
            materialCode: item.materialCode,
            materialName: item.materialName,
            materialTypeId: item.materialTypeId,
            specification: item.specification,
            budgetId: item.budgetId,
            budgetYear: item.budgetYear,
            gb: item.gb,
            unit: item.unit,
            //如果是暂存，则取到货数，当验收完成时扣减不合格数
            num: !row.inspection
              ? Number(item.arrivalNum)
              : Number(item.qualifiedNum) - Number(item.stockInNum),
            price: item.price,
            amount: !row.inspection ? item.amount : item.qualifiedAmount,
            remark: row.remark,
          });
        });
        console.log(form);
        this.$refs.stockInFormRef.init(form);
      });
    },
    beforeOpen(done, type) {
      if (["edit", "view"].includes(type)) {
        detail(this.form.id).then((res) => {
          this.form = res.data.data;
          this.form.inspection = true;
          this.form.inspectionUserId = this.userInfo.user_id;
          this.form.inspectionDate = dateFormat(new Date(), "yyyy-MM-dd");
        });
      }
      done();
    },
    rowUpdate(row, index, done, loading) {
      inspection(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          console.log(error);
        }
      );
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    dataTypeChange() {
      this.onLoad(this.page);
    },
    cellStyle({ row, column }) {
      if ("printSign" === column.columnKey && row.printSign) {
        return {
          backgroundColor: "#F56C6C",
          color: "#fff",
        };
      }
    },
    onLoad(page, params = {}) {
      this.loading = true;
      const q = {
        ...params,
        ...this.query,
        descs: "id",
      };
      if (q.createTime != null && q.createTime.length === 2) {
        q.startCreateTime = q.createTime[0] + " 00:00:00";
        q.endCreateTime = q.createTime[1] + " 23:59:59";
        q.createTime = null;
      }
      if (this.dataType === "1") {
        q.purchaseUserId = this.userInfo.user_id;
      } else {
        q.purchaseUserId = null;
      }
      getPage(page.currentPage, page.pageSize, q).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.data.forEach((item) => {
          if (item.arrivalNum == null || item.arrivalNum <= 0) {
            item.arrivalState = "3";
          } else if (item.arrivalNum && item.arrivalNum >= item.num) {
            item.arrivalState = "1";
          } else if (item.arrivalNum && item.arrivalNum < item.num) {
            item.arrivalState = "2";
          }
          if (item.arrivalAmount) {
            item.arrivalAmountStr = Number(item.arrivalAmount).toLocaleString(
              "zh-CN",
              {
                minimumFractionDigits: 2,
              }
            );
          }
        });
        this.loading = false;
        this.selectionClear();
      });
    },
  },
};
</script>

<style></style>

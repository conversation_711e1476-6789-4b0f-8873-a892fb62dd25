<script>
import { mapGetters } from "vuex";
import { count } from "@/api/ni/fg/fgTransactionItem";

export default {
  name: "OutboundFormDialog",
  data() {
    return {
      visible: false,
      loading: false,
      option: {
        showSummary: true,
        sumColumnList: [
          {
            name: "num",
            type: "sum",
            decimals: 1,
          },
          {
            name: "weight",
            type: "sum",
            decimals: 1,
          },
        ],
        header: false,
        menu: false,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        span: 12,
        dialogDrag: true,
        size: "mini",
        searchSize: "mini",
        align: "center",
        searchIndex: 2,
        searchIcon: true,
        searchEnter: true,
        calcHeight: 30,
        tip: false,
        searchShow: false,
        border: true,
        selection: false,
        dialogClickModal: false,
        column: [
          {
            label: "存货编码",
            prop: "materialCode",
            overHidden: true,
            sortable: true,
            filters: true,
            minWidth: 110,
          },
          {
            label: "规格",
            prop: "specText",
            overHidden: true,
            sortable: true,
            filters: true,
            minWidth: 90,
          },
          {
            label: "外包装",
            prop: "packageText",
            overHidden: true,
            sortable: true,
            filters: true,
            minWidth: 110,
          },
          {
            label: "内包装",
            prop: "innerPackageText",
            overHidden: true,
            sortable: true,
            filters: true,
            minWidth: 90,
          },
          {
            label: "质量",
            prop: "qualityLevel",
            type: "select",
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_product_sku_quality_level",
            props: {
              label: "dictValue",
              value: "dictKey",
              desc: "dictKey",
            },
            placeholder: " ",
            sortable: true,
            filters: true,
            minWidth: 75,
          },
          {
            label: "数量",
            prop: "num",
            type: "number",
            minWidth: 110,
          },
          {
            label: "重量",
            prop: "weight",
            type: "number",
            overHidden: true,
            minWidth: 110,
          },
          {
            label: "当前库存",
            prop: "currentStock",
            type: "number",
            overHidden: true,
            minWidth: 110,
          },
        ],
      },
      data: [],
      form: {},
      ids: null,
    };
  },
  computed: {
    ...mapGetters(["userInfo"]),
  },
  methods: {
    onShow(ids) {
      this.ids = ids;
      this.data = [];
      this.visible = true;
    },
    onLoad() {
      this.loading = true;
      count(this.ids).then((res) => {
        this.data = res.data.data;
        this.loading = false;
      });
    },
  },
};
</script>

<template>
  <el-dialog :visible.sync="visible" append-to-body title="出库统计">
    <avue-crud
      ref="crud"
      v-if="visible"
      :table-loading="loading"
      :option="option"
      :data="data"
      v-model="form"
      @on-load="onLoad"
    >
    </avue-crud>
  </el-dialog>
</template>

<style scoped></style>

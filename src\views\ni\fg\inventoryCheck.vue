<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page.sync="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               :cell-style="cellStyle"
               @cell-click="cellClick"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @refresh-change="refreshChange"
               @on-load="onLoad">
      <template #menuLeft>
        <el-button type="danger"
                   size="mini"
                   icon="el-icon-delete"
                   plain
                   v-if="permission.niFgInventoryCheck_delete"
                   @click="handleDelete">删 除
        </el-button>
      </template>
      <template #menu="{row,index}">
        <el-button type="text" icon="el-icon-edit" size="mini"
                   v-if="row.status === 1&&permission.niFgInventoryCheck_edit"
                   @click.stop="$refs.crud.rowEdit(row, index)">编辑
        </el-button>
        <el-button type="text" icon="el-icon-delete" size="mini"
                   v-if="row.status === 1&&permission.niFgInventoryCheck_delete"
                   @click.stop="$refs.crud.rowDel(row, index)">删除
        </el-button>
        <el-button type="text" icon="el-icon-delete" size="mini"
                   v-if="[2,3].includes(row.status)"
                   @click.stop="rowCancel(row, index)">取消
        </el-button>
        <!--盘点-->
        <el-button type="text"
                   size="mini"
                   icon="el-icon-s-claim"
                   plain
                   @click="rowCheck(row,index)">盘点
        </el-button>
        <el-button
          type="text"
          icon="el-icon-s-check"
          size="mini"
          v-if="[2,3].includes(row.status)"
          @click="rowConfirm(row,index)"
        >审 核
        </el-button>
      </template>
    </avue-crud>
    <inventory-check-snapshot-dialog ref="inventoryCheckSnapshotDialogRef"/>
    <inventory-check-drawer ref="inventoryCheckDrawerRef"/>
  </basic-container>
</template>

<script>
import {add, getDetail, getList, remove, update, cancel, confirm} from "@/api/ni/fg/fgInventoryCheck";
import {mapGetters} from "vuex";
import {dateNow1} from "@/util/date";
import {build as buildShapshot} from "@/api/ni/fg/fgInventoryCheckSnapshot";

export default {
  components: {
    InventoryCheckSnapshotDialog: () => import("./components/InventoryCheckSnapshotDialog.vue"),
    InventoryCheckDrawer: () => import("./components/InventoryCheckDrawer.vue"),
  },
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      selectionList: [],
      option: {
        editBtn: false,
        delBtn: false,
        span: 8,
        size: "mini",
        searchSize: "mini",
        align: "center",
        searchIndex: 3,
        searchIcon: true,
        searchEnter: true,
        height: 'auto',
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        selection: true,
        dialogClickModal: false,
        column: [
          {
            label: "单据编号",
            prop: "serialNo",
            minWidth: 120,
            placeholder: "系统自动生成",
            overHidden: true,
            search: true
          },
          {
            label: "主题",
            prop: "title",
            type: "input",
            minWidth: 130,
            overHidden: true,
            search: true
          },
          {
            label: '仓库',
            prop: 'depotId',
            type: 'select',
            dicUrl: "/api/ni/base/depot/info/list?status=2&type=1",
            props: {
              label: "name",
              value: "id",
              desc: "code",
            },
            minWidth: 90,
            overHidden: true,
            search: true,
          },
          {
            label: "操作人",
            prop: "opUserId",
            type: "select",
            remote: true,
            dicUrl: `/api/blade-user/search/otj/user?name={{key}}`,
            props: {
              label: "realName",
              value: "id",
            },
            width: 80,
            dicFormatter: (data) => {
              return data.data.records;
            },
            placeholder: " ",
            filterable: true,
            overHidden: true,
            rules: [
              {
                required: true,
                message: "请选择操作人",
                trigger: "blur",
              },
            ],
            search: true,
            hide: true,
            showColumn: false,
          },
          {
            label: "操作人",
            prop: "opUserName",
            display: false,
            minWidth: 80
          },
          {
            label: "登记时间",
            prop: "startDate",
            type: "datetime",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
            minWidth: 150
          },
          {
            label: "快照时间",
            prop: "snapshotTime",
            type: "datetime",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
            minWidth: 150,
            display: false
          },
          {
            label: "完成时间",
            prop: "completeTime",
            type: "datetime",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
            display: false,
            minWidth: 150
          },
          {
            label: "盘盈",
            prop: "overage",
            display: false,
            minWidth: 80,
          },
          {
            label: "盘亏",
            prop: "shortage",
            display: false,
            minWidth: 80
          },
          {
            label: "备注",
            prop: "remark",
            type: "textarea",
            span: 24,
            overHidden: true,
            minWidth: 150
          },
          {
            label: "状态",
            prop: "status",
            type: "select",
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_fg_inventory_check_status",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            dataType: "number",
            search: true,
            display: false,
            width: 120
          },
        ]
      },
      data: []
    };
  },
  computed: {
    ...mapGetters(["permission", "userInfo"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.niFgInventoryCheck_add, false),
        viewBtn: this.vaildData(this.permission.niFgInventoryCheck_view, false),
        delBtn: this.vaildData(this.permission.niFgInventoryCheck_delete, false),
        editBtn: this.vaildData(this.permission.niFgInventoryCheck_edit, false)
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach(ele => {
        ids.push(ele.id);
      });
      return ids.join(",");
    }
  },
  methods: {
    rowConfirm(row, index) {
      this.$confirm("审核后将生成盘盈盘亏数据，并更新库存，确定要继续？", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        return confirm(row.id);
      })
        .then(() => {
          this.data[index].status = 4
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        });
    },
    rowSnapshot(row) {
      getDetail(row.id).then((res) => {
        const form = res.data.data
        if (!form.snapshotTime && form.status !== 9) {
          this.$confirm("还未生成库存快照,是否立即生成?", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning"
          })
            .then(() => {
              return buildShapshot(form.id);
            })
            .then(() => {
              this.onLoad(this.page);
              this.$message({
                type: "success",
                message: "操作成功!"
              });
            });
          return
        }
        this.$refs.inventoryCheckSnapshotDialogRef.onShow(form)
      })
    },
    rowCancel(row) {
      this.$confirm("确定要取消该盘点?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return cancel(row.id);
        }).then(() => {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
      });
    },
    rowCheck(row, index) {
      getDetail(row.id).then((res) => {
        const form = res.data.data
        if (!form.snapshotTime && form.status !== 9) {
          this.$confirm("还未生成库存快照,是否立即生成?", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning"
          })
            .then(() => {
              return buildShapshot(form.id);
            })
            .then(() => {
              setTimeout(() => {
                this.onLoad(this.page);
              }, 500)
              this.$message({
                type: "success",
                message: "操作成功!"
              });
            });
          return
        }
        this.$refs.inventoryCheckDrawerRef.onCheck(form)
      })
    },
    rowSave(row, done, loading) {
      add(row).then((res) => {
        done();
        if (res.data.data.id && res.data.data.depotId)
          this.$confirm("添加成功,是否立即生成库存快照?", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning"
          })
            .then(() => {
              return buildShapshot(res.data.data.id);
            })
            .then(() => {
              this.onLoad(this.page)
              this.$message({
                type: "success",
                message: "操作成功!"
              });
            });
        else this.onLoad(this.page);
      }, error => {
        loading();
        window.console.log(error);
      });
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(() => {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
        done();
      }, error => {
        loading();
        console.log(error);
      });
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen(done, type) {
      if ("add" === type) {
        this.form.opUserId = this.userInfo.user_id;
        this.form.startDate = dateNow1()
      }
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then(res => {
          this.form = res.data.data;
        });
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
    cellClick(row, column,) {
      if (column.property === "snapshotTime") {
        this.rowSnapshot(row)
      }
    },
    cellStyle({row, column}) {
      if ("status" === column.columnKey) {
        if (row.status === 1) {
          return {
            backgroundColor: '#eef1f6',
            color: "##909399",
          }
        } else if (row.status === 2) {
          return {
            backgroundColor: '#409EFF',
            color: "#fff",
          }
        } else if (row.status === 3) {
          return {
            backgroundColor: '#67C23A',
            color: "#fff",
          }
        } else if (row.status === 4) {
          return {
            backgroundColor: '#9966CC',
            color: "#fff",
          }
        } else if (row.status === 9) {
          return {
            backgroundColor: '#F56C6C',
            color: "#fff",
          }
        }
      }
      if ('snapshotTime' === column.columnKey && row.snapshotTime) {
        return {
          textDecoration: 'underline',
          cursor: 'pointer'
        }
      }
      if ("serialNo" === column.columnKey && row.red) {
        return {
          backgroundColor: '#F56C6C',
          color: "#fff",
        }
      }
      if ("overage" === column.columnKey && row.overage){
        return {
          backgroundColor: '#4A90E2',
          color: "#fff",
        }
      }
      if ("shortage" === column.columnKey && row.shortage){
        return {
          backgroundColor: '#F56C6C',
          color: "#fff",
        }
      }
    },
  }
};
</script>

<style>
</style>

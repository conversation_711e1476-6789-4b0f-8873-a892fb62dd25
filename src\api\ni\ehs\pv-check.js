import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/ni/ehs/pvCheck/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/ni/ehs/pvCheck/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/ni/ehs/pvCheck/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/ni/ehs/pvCheck/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/ni/ehs/pvCheck/update',
    method: 'post',
    data: row
  })
}

export const save = (row) => {
  return request({
    url: '/api/ni/ehs/pvCheck/safetyUser/save',
    method: 'post',
    data: row
  })
}

export const getSafetyUserDetail = () => {
  return request({
    url: '/api/ni/ehs/pvCheck/safetyUser/detail',
    method: 'get',
  })
}

export const getSafetyUserType = () => {
  return request({
    url: '/api/ni/ehs/pvCheck/getSafetyUserType',
    method: 'get',
  })
}

export const startWeek = (row) => {
  return request({
    url: '/api/ni/ehs/pvCheck/startWeek',
    method: 'post',
    data: row,
  })
}

export const startMonth = (row) => {
  return request({
    url: '/api/ni/ehs/pvCheck/startMonth',
    method: 'post',
    data: row,
  })
 }

 export const getStartMeetingData = (isWeek) => {
  return request({
    url: '/api/ni/ehs/pvCheck/getStartMeetingData',
    method: 'get',
    params: {
      isWeek
    }
  })
 }
 
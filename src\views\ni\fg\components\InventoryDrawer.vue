<script>
import {
  add,
  changeRemark,
  freezeCancel,
  getList,
  remove,
  sumNum,
  update,
} from "@/api/ni/fg/fgInventory";
import { mapGetters } from "vuex";
import { freeze } from "@/api/ni/fg/fgFreeze";

export default {
  name: "InventoryDrawer",
  data() {
    return {
      title: "",
      depotNum: 0,
      batchNoNum: 0,
      visible: false,
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        cellBtn: true,
        showSummary: true,
        sumColumnList: [
          {
            name: "num",
            type: "sum",
          },
          {
            name: "weight",
            type: "sum",
          },
        ],
        addBtn: false,
        height: "auto",
        size: "mini",
        searchSize: "mini",
        align: "center",
        searchIndex: 3,
        searchIcon: true,
        searchEnter: true,
        calcHeight: 30,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        selection: true,
        dialogClickModal: false,
        column: [
          {
            label: "品名",
            prop: "skuText",
            placeholder: " ",
            overHidden: true,
            minWidth: 120,
            hide: true,
          },
          {
            label: "存货编码",
            prop: "materialCode",
            placeholder: " ",
            width: 100,
            overHidden: true,
          },
          {
            label: "规格",
            prop: "specText",
            placeholder: " ",
            display: false,
            overHidden: true,
            width: 100,
          },
          {
            label: "外包装",
            prop: "packageText",
            placeholder: " ",
            display: false,
            overHidden: true,
            width: 115,
          },
          {
            label: "内包装",
            prop: "innerPackageText",
            placeholder: " ",
            display: false,
            overHidden: true,
            width: 115,
          },
          {
            label: "唛头",
            prop: "currentMarkText",
            placeholder: " ",
            overHidden: true,
            hide: true,
            width: 110,
          },
          {
            label: "批号",
            prop: "batchNo",
            placeholder: " ",
            minWidth: 110,
            overHidden: true,
            search: true,
            cell: true,
            rules: [
              {
                required: true,
                message: "请输入批号",
                trigger: "blur",
              },
            ],
          },
          {
            label: "数量",
            prop: "num",
            placeholder: " ",
            type: "number",
            minWidth: 100,
            cell: true,
            change: ({ value, row }) => {
              row.weight = Number(value) * Number(row.capacity);
              console.log(row);
            },
            rules: [
              {
                required: true,
                message: "请输入数量",
                trigger: "blur",
              },
            ],
          },
          {
            label: "重量",
            prop: "weight",
            placeholder: " ",
            minWidth: 80,
          },
          {
            label: "单位",
            prop: "unit",
            type: "select",
            filterable: true,
            width: 60,
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_material_unit",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            slot: true,
            placeholder: " ",
          },
          {
            label: "生产日期",
            prop: "productionDate",
            type: "datetime",
            placeholder: " ",
            minWidth: 120,
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
            search: true,
            searchRange: true,
            cell: true,
            sortable: true,
            rules: [
              {
                required: true,
                message: "请选择生产日期",
                trigger: "blur",
              },
            ],
          },
          {
            label: "备注",
            prop: "remark",
            type: "textarea",
            minRows: 1,
            minWidth: 100,
            span: 24,
            search: true,
            cell: true,
          },
          {
            label: "状态",
            prop: "status",
            type: "select",
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_fg_inventory_status",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            dataType: "number",
            minWidth: 100,
            search: true,
          },
        ],
      },
      data: [],
      query: {},
      form: {},
      row: {},
      status: "all",
      freeze: {
        visible: false,
        form: {},
        option: {
          size: "mini",
          span: 24,
          column: [
            {
              label: "冻结原因",
              prop: "reason",
              placeholder: " ",
            },
            {
              label: "冻结类型",
              prop: "type",
              type: "select",
              dicUrl:
                "/api/blade-system/dict-biz/dictionary?code=ni_fg_inventory_freeze_type",
              props: {
                label: "dictValue",
                value: "dictKey",
              },
              placeholder: " ",

              rules: [
                {
                  required: true,
                  message: "请选择冻结类型",
                  trigger: "blur",
                },
              ],
            },
            {
              label: "备注",
              prop: "remark",
              placeholder: " ",
              type: "textarea",
              minRows: 2,
              search: true,
            },
          ],
        },
      },
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        delBtn: this.vaildData(
          this.permission.ni_fg_inventory_summary_batch_no_delete,
          false
        ),
        editBtn: this.vaildData(
          this.permission.ni_fg_inventory_summary_batch_no_edit,
          false
        ),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  methods: {
    onShow(row) {
      this.row = row;
      this.title = `[${row.$depotId}]${row.skuText}`;
      this.depotNum = row.num;
      this.handleSumBatchNo();
      this.visible = true;
    },
    handleSumBatchNo() {
      sumNum(this.row.depotId, this.row.skuId).then((res) => {
        this.batchNoNum = res.data.data;
      });
    },
    handleAdd() {
      this.data.splice(0, 0, {
        depotId: this.row.depotId,
        skuId: this.row.skuId,
        materialCode: this.row.materialCode,
        specText: this.row.specText,
        skuText: this.row.skuText,
        packageText: this.row.packageText,
        innerPackageText: this.row.innerPackageText,
        unit: this.row.unit,
        capacity: this.row.capacity,
        status: 1,
        $cellEdit: true,
      });
    },
    sortChange(val) {
      if (val.prop === "productionDate" && val.order === "descending") {
        this.query.descs = "production_date";
        this.query.ascs = null;
      } else if (val.prop === "productionDate" && val.order === "ascending") {
        this.query.ascs = "production_date";
        this.query.descs = null;
      }
      this.page.currentPage = 1;
      this.onLoad(this.page);
    },
    handleFreeze() {
      const freeze = this.selectionList.some((item) => item.status === 2);
      if (freeze) {
        this.$message({
          type: "warning",
          message: "请选择非冻结的数据进行冻结!",
        });
        return;
      }
      const depotId = this.selectionList[0].depotId;
      this.freeze.form = {
        depotId,
        ids: this.ids,
      };
      this.freeze.visible = true;
    },
    handleFreezeSubmit(form, done) {
      freeze(form)
        .then(() => {
          this.freeze.visible = false;
          this.$message({
            type: "success",
            message: "冻结成功!",
          });
          this.onLoad(this.page);
        })
        .finally(() => {
          done();
        });
    },
    handleFreezeCancel() {
      this.$confirm("确定取消选择数据的冻结状态?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return freezeCancel(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    handleClose(done) {
      this.visible = false;
      if (done && typeof done == "function") done();
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    searchReset() {
      this.query = {
        depotId: this.row.depotId,
        skuId: this.row.skuId,
      };
      this.page.currentPage = 1;
      this.onLoad(this.page);
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      const q = {
        ...params,
        ...this.query,
        ...this.params,
      };
      if (q.productionDate && q.productionDate.length === 2) {
        q.startProductionDate = q.productionDate[0];
        q.endProductionDate = q.productionDate[1];
        q.productionDate = null;
      }
      q.depotId = this.row.depotId;
      q.skuId = this.row.skuId;
      if (this.status !== "all") {
        q.status = this.status;
      }
      if (!q.descs && !q.ascs) q.descs = "id";
      getList(page.currentPage, page.pageSize, q).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.handleSumBatchNo();
        this.selectionClear();
        this.loading = false;
      });
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    cellStyle({ row, column }) {
      if ("status" === column.columnKey && row.status === 2) {
        return {
          backgroundColor: "#409EFF",
          color: "#fff",
        };
      }
    },
    cellClick(row, column) {
      if (
        column.property === "remark" &&
        this.permission.fgInventory_change_remark
      ) {
        this.handleChangeRemark(row);
      }
    },
    rowSave(form, done, loading) {
      add(form)
        .then(
          () => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!",
            });
            done();
          },
          (error) => {
            console.log(error);
          }
        )
        .finally(() => {
          loading();
        });
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    rowUpdate(form, index, done, loading) {
      update(form).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          console.log(error);
        }
      );
    },
    handleChangeRemark(row) {
      this.$prompt("请输入备注", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        inputValue: row.remark,
        inputPlaceholder: "请输入备注",
        inputPattern: /^.{0,100}$/,
        inputErrorMessage: "备注不能超过100个字符",
      }).then(({ value }) => {
        changeRemark(row.id, value).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
      });
    },
  },
};
</script>

<template>
  <el-drawer
    :title="title"
    :visible.sync="visible"
    direction="rtl"
    size="80%"
    append-to-body
    destroy-on-close
  >
    <template slot="title">
      <div>
        <span>{{ title }}</span>
        <el-divider direction="vertical" />
        总库存：{{ depotNum }}，批号库存：<span
          :style="
            batchNoNum > depotNum
              ? { color: '#F56C6C', fontWeight: 'bolder' }
              : {}
          "
          >{{ batchNoNum }}</span
        >
      </div>
    </template>
    <div style="margin: 20px 20px">
      <avue-crud
        ref="crud"
        v-if="visible"
        :option="option"
        :table-loading="loading"
        :data="data"
        :page.sync="page"
        v-model="form"
        :permission="permissionList"
        @row-update="rowUpdate"
        @row-save="rowSave"
        @row-del="rowDel"
        @search-change="searchChange"
        @search-reset="searchReset"
        @selection-change="selectionList = $event"
        @current-change="page.currentPage = $event"
        @size-change="page.pageSize = $event"
        @refresh-change="refreshChange"
        @on-load="onLoad"
        :cell-style="cellStyle"
        @cell-click="cellClick"
        @sort-change="sortChange"
      >
        <template #status="{ row, index }">
          <span>{{ row.$status }}</span>
          <span v-if="row.freezeTypeText">({{ row.freezeTypeText }})</span>
        </template>
        <template #menuLeft>
          <!--登记-->
          <el-button
            type="primary"
            icon="el-icon-plus"
            size="mini"
            v-if="permission.ni_fg_inventory_summary_batchNo_add"
            @click="handleAdd"
            >登记
          </el-button>

          <el-button
            type="danger"
            icon="el-icon-turn-off-microphone"
            size="mini"
            v-if="permission.ni_fg_inventory_summary_isolation"
            @click="handleFreeze"
            >标记冻结
          </el-button>
          <el-button
            type="info"
            icon="el-icon-turn-off-microphone"
            size="mini"
            v-if="permission.ni_fg_inventory_summary_isolation"
            @click="handleFreezeCancel"
            >取消冻结
          </el-button>
          <el-divider direction="vertical"></el-divider>
          <el-radio-group v-model="status" size="mini" @input="onLoad(page)">
            <el-radio-button label="all">全部</el-radio-button>
            <el-radio-button label="1">正常</el-radio-button>
            <el-radio-button label="2">冻结</el-radio-button>
          </el-radio-group>
        </template>
      </avue-crud>
    </div>
    <el-dialog
      title="冻结库存"
      :visible.sync="freeze.visible"
      width="400px"
      append-to-body
    >
      <avue-form
        :option="freeze.option"
        v-model="freeze.form"
        @submit="handleFreezeSubmit"
      >
      </avue-form>
    </el-dialog>
  </el-drawer>
</template>

<style scoped></style>

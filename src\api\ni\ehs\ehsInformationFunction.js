import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/ni/ehs/ehsInformationFunction/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/ni/ehs/ehsInformationFunction/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/ni/ehs/ehsInformationFunction/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/ni/ehs/ehsInformationFunction/save',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/ni/ehs/ehsInformationFunction/update',
    method: 'post',
    data: row
  })
}

export const batchUpdateNotifyUser=  (row) => {
  return request({
    url: '/api/ni/ehs/ehsInformationFunction/batchUpdateNotifyUser',
    method: 'post',
    data: row
  })
}


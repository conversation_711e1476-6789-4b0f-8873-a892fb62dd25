<template>
  <el-dialog
    ref="project-dialog"
    v-dialogdrag
    custom-class="project-dialog"
    :visible.sync="visible"
    title="项目选择"
    width="60%"
    :before-close="handleClose"
    append-to-body
  >
    <avue-crud
      v-if="isInit && visible"
      :option="option"
      :table-loading="loading"
      :page.sync="page"
      :data="data"
      v-model="form"
      ref="crud"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionList = $event"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @row-click="rowClick"
      @on-load="onLoad"
    >
      <template v-if="!multiple" #radio="{ row }">
        <el-radio v-model="form.radio" :label="row.id"><i></i></el-radio>
      </template>
    </avue-crud>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose" size="mini">取 消</el-button>
      <el-button type="primary" @click="handleConfirm" size="mini"
      >确 定</el-button
      >
    </span>
  </el-dialog>
</template>
<script>
import {getDetail, getList} from "@/api/ni/project/index";

export default {
  props: {
    defaultChecked: String,
    customOption: Object,
    multiple: {
      type: Boolean,
      default: false,
    },
  },
  watch: {
    multiple: {
      handler(val) {
        if (!val) {
          this.$set(this.option, "selection", false);
          this.findObject(this.option.column, "radio").hide = false;
        } else {
          this.$set(this.option, "selection", true);
          this.findObject(this.option.column, "radio").hide = true;
        }
      },
      immediate: true,
    },
  },
  computed: {
    ids() {
      let ids = new Set();
      this.selectionList.forEach((ele) => {
        ids.add(ele.id);
      });
      return Array.from(ids).join(",");
    },
    names() {
      let names = new Set();
      this.selectionList.forEach((ele) => {
        names.add(ele.title);
      });
      return Array.from(names).join(",");
    },
  },
  data() {
    return {
      isInit: false,
      visible: false,
      form: {},
      query: {},
      loading: false,
      selectionList: [],
      data: [],
      props: {
        id: "id",
        name: "title",
        records: "data.data.records",
        total: "data.data.total",
      },
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      option: {
        searchEnter: true,
        size: "mini",
        searchSize: "mini",
        align: "center",
        menu: false,
        addBtn: false,
        header: false,
        border: true,
        tip: false,
        reserveSelection: true,
        highlightCurrentRow: true,
        gutter: 5,
        searchMenuSpan: 6,
        selection: true,
        column: [
          {
            label: "",
            prop: "radio",
            type: "radio",
            width: 55,
            hide: true,
          },
          {
            label: "申请人",
            prop: "createUserName",
            display: false,
            minWidth: 100,
          },
          {
            label: "申请部门",
            prop: "createDeptName",
            minWidth: 100,
            display: false,
          },
          {
            label: "项目名称",
            prop: "title",
            placeholder: " ",
            search: true,
            span: 8,
            overHidden: true,
            minWidth: 150,
          },
          {
            label: "项目编号",
            prop: "serialNo",
            search: true,
            placeholder: "系统自动生成",
            addDisabled: false,
            editDisabled: true,
            overHidden: true,
            minWidth: 120,
            span: 8,
          },
          {
            label: "类型",
            prop: "type",
            type: "select",
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_project_type",
            props: {
              label: "dictValue",
              value: "dictKey",
              desc: "dictKey",
            },
            placeholder: " ",
            span: 8,
          },
          {
            label: "账套",
            prop: "brand",
            type: "radio",
            value: "1",
            dicUrl: "/api/blade-system/dict-biz/dictionary?code=ni_brand",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
          },
          {
            label: "项目集",
            prop: "catalogName",
            placeholder: " ",
            display: false,
            overHidden: true,
            minWidth: 120,
          },
          {
            label: "负责人",
            prop: "leader",
            display: false,
            minWidth: 100,
            overHidden: true,
          },
          {
            label: "成员数",
            prop: "personNum",
            display: false,
            minWidth: 90,
          },
          {
            label: "总预算(W)",
            prop: "budget",
            type: "number",
            minWidth: 100,
            placeholder: " ",
          },
          {
            label: "已申请(W)",
            prop: "applyBudget",
            type: "number",
            minWidth: 100,
            placeholder: " ",
            display: false,
          },
          {
            label: "计划开始",
            prop: "startDate",
            minWidth: 100,
            overHidden: true,
            display: false,
          },
          {
            label: "计划完成",
            prop: "endDate",
            minWidth: 100,
            overHidden: true,
            display: false,
          },
          {
            label: "实际开始时间",
            prop: "actualStartDate",
            minWidth: 100,
            overHidden: true,
            display: false,
          },
          {
            label: "访问控制",
            prop: "acl",
            search: true,
            type: "radio",
            overHidden: true,
            minWidth: 100,
            value: "1",
            dicData: [
              {
                label: "私有",
                value: "1",
              },
              {
                label: "公开",
                value: "2",
              },
            ],
          },
        ],
      },
    };
  },
  mounted() {
    this.init();
  },
  methods: {
    init() {
      if (!this.isInit) {
        if (this.customOption) {
          const {column, props} = this.customOption;
          if (column) this.$set(this.option, "column", column);
          if (props) this.$set(this, "props", props);
        }
        this.isInit = true;
      }
    },
    handleConfirm() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$emit("onConfirm", this.ids, this.names);
      this.handleClose();
    },
    handleClose(done) {
      // this.selectionClear()
      this.visible = false;
      if (done && typeof done == "function") done();
    },
    searchReset() {
      this.query = {};
      this.page.currentPage = 1;
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    selectionClear() {
      this.selectionList = [];
      if (this.$refs.crud) this.$refs.crud.toggleSelection();
    },
    rowClick(row) {
      if (!this.multiple) {
        this.selectionList = [row];
        this.$set(this.form, "radio", row.id);
      } else this.$refs.crud.toggleSelection([row]);
    },
    async changeDefaultChecked() {
      if (!this.defaultChecked) return;
      let defaultChecked = this.defaultChecked;

      if (this.multiple) {
        // this.selectionClear()
        const checks = defaultChecked.split(",");
        if (checks.length > 0) {
          setTimeout(() => {
            checks.forEach(async (c) => {
              let row = this.data.find((d) => d.id == c); // 当前页查找
              if (!row) {
                row = this.selectionList.find((d) => d.id == c); // 勾选列表查找
                if (!row) {
                  let res = await getDetail(c); // 接口查找
                  if (res.data.data) row = res.data.data;
                }
              }
              if (row && this.$refs.crud)
                this.$refs.crud.toggleRowSelection(row, true);
            });
          }, 500);
        }
      } else {
        let row = this.data.find((d) => d.id == defaultChecked);
        if (!row) {
          let res = await getDetail(defaultChecked);
          if (res.data.data) row = res.data.data;
        }

        if (row) {
          this.selectionList = [row];
          this.$set(this.form, "radio", defaultChecked);
        } else {
          this.selectionList = [];
          this.$set(this.form, "radio", "");
        }
      }
    },
    onLoad(page, params = {}) {
      this.loading = true;
      const query = {
        ...params,
        ...this.query,
      };
      getList(page.currentPage, page.pageSize, query).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.changeDefaultChecked();
      });
    },
  },
};
</script>
<style lang="scss">
.project-dialog {
  display: flex;
  flex-direction: column;
  margin: 0 !important;
  position: absolute;
  top: 40%;
  left: 50%;
  transform: translate(-50%, -40%);
  max-height: calc(100% - 30px);
  max-width: calc(100% - 30px);

  .el-dialog__body {
    flex: 1;
    overflow: auto;
  }
}
</style>

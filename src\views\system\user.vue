<template>
  <el-row>
    <el-col :span="5">
      <div class="box">
        <el-scrollbar>
          <basic-container>
            <avue-tree
              :option="treeOption"
              :data="treeData"
              @node-click="nodeClick"
            />
          </basic-container>
        </el-scrollbar>
      </div>
    </el-col>
    <el-col :span="19">
      <basic-container>
        <avue-crud
          :option="option"
          :table-loading="loading"
          :data="data"
          ref="crud"
          v-model="form"
          :permission="permissionList"
          @row-del="rowDel"
          @row-update="rowUpdate"
          @row-save="rowSave"
          :before-open="beforeOpen"
          :page.sync="page"
          @search-change="searchChange"
          @search-reset="searchReset"
          @selection-change="selectionChange"
          @current-change="currentChange"
          @size-change="sizeChange"
          @refresh-change="refreshChange"
          @on-load="onLoad"
        >
          <template #householdRegistrationForm>
            <el-cascader
              v-model="form.regionCode"
              :options="householdRegistrationOptions"
              filterable
              clearable
              separator=""
            ></el-cascader>
          </template>
          <template #avatar="{ row }">
            <el-image
              style="min-width: 50px; height: 50px"
              :src="`/api/blade-resource/attach/download?${website.tokenHeader}=${token}&id=${row.avatar}`"
              :preview-src-list="[
                `/api/blade-resource/attach/download?${website.tokenHeader}=${token}&id=${row.avatar}`,
              ]"
            />
          </template>
          <template #avatarForm="{ row }">
            <el-image
              v-if="row.avatar"
              style="min-width: 50px; height: 50px"
              :src="`/api/blade-resource/attach/download?${website.tokenHeader}=${token}&id=${row.avatar}`"
              :preview-src-list="[
                `/api/blade-resource/attach/download?${website.tokenHeader}=${token}&id=${row.avatar}`,
              ]"
            />
          </template>
          <template #menuLeft>
            <el-dropdown @command="handleBatchCommand">
              <el-button icon="el-icon-setting" type="info" size="mini">
                批量操作<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item
                  icon="el-icon-delete"
                  command="delete" v-if="permission.user_delete"
                >
                  删 除
                </el-dropdown-item>
                <el-dropdown-item icon="el-icon-user" command="grant" v-if="permission.user_role">
                  角色配置
                </el-dropdown-item>
                <el-dropdown-item icon="el-icon-refresh" command="reset" v-if="permission.user_reset">
                  密码重置
                </el-dropdown-item>
                <el-dropdown-item
                  command="lock"
                  v-if="userInfo.role_name.includes('admin')"
                  icon="el-icon-coordinate"
                >账号解封
                </el-dropdown-item>
                <el-dropdown-item
                  command="tag"
                  icon="el-icon-discount"
                >标记省内/外
                </el-dropdown-item>
                <el-dropdown-item
                  command="tagYY"
                  v-if="userInfo.role_name.includes('admin')"
                  icon="el-icon-success"
                >标记演绎
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
            <el-button
              type="success"
              size="mini"
              plain
              v-if="userInfo.role_name.includes('admin')"
              icon="el-icon-upload2"
              @click="handleImport"
            >导入
            </el-button>
            <el-button
              type="warning"
              size="mini"
              plain
              v-if="permission.user_export"
              icon="el-icon-download"
              @click="handleExport"
            >导出
            </el-button>
            <el-button
              type="info"
              size="mini"
              plain
              v-if="permission.user_export"
              icon="el-icon-download"
              @click="handleExportQr"
            >导出二维码
            </el-button>
            <el-divider direction="vertical"/>
            <el-radio-group v-model="status" size="mini">
              <el-radio-button label="all">全部</el-radio-button>
              <el-radio-button label="1">在职</el-radio-button>
              <el-radio-button label="2">离职</el-radio-button>
            </el-radio-group>
            <el-divider direction="vertical"/>
            <el-checkbox v-model="yy">演绎</el-checkbox>
          </template>
          <template #realName="{ row }">
            <i
              class="el-icon-male"
              v-if="row.sex === 1"
              style="color: blue"
            ></i>
            <i
              class="el-icon-female"
              v-else-if="row.sex === 2"
              style="color: red"
            ></i>
            <span>{{ row.realName }}</span>
            <el-tag v-if="row.yy" size="mini" type="warning" effect="dark"
            >演绎
            </el-tag>
          </template>
          <template #status="{ row }">
            <el-tag v-if="row.status === 1" size="mini" effect="dark"
            >{{ row.$status }}
            </el-tag>
            <el-tag
              v-if="row.status === 2"
              size="mini"
              type="info"
              effect="dark"
            >{{ row.$status }}
            </el-tag>
            <el-tag
              v-if="row.status === 3"
              size="mini"
              type="danger"
              effect="dark"
            >{{ row.$status }}
            </el-tag>
          </template>
          <template #roleName="{ row }">
            <el-tag size="mini">{{ row.roleName }}</el-tag>
          </template>
          <template #deptName="{ row }">
            <el-tag size="mini">{{ row.deptName }}</el-tag>
          </template>
          <template #postName="{ row }">
            <el-tag v-if="row.postName" size="mini" effect="dark"
            >{{ row.postName }}
            </el-tag>
          </template>
          <template #signedContract="{ row }">
            <el-tooltip
              v-if="row.$signedContract"
              effect="dark"
              :content="`${row.contractStart} - ${row.contractEnd}`"
              placement="top"
            >
              <el-button size="mini" type="text">
                {{ `${row.$signedContract}` }}
              </el-button>
            </el-tooltip>
          </template>
          <template #phoneForm="{ value, type }">
            <div v-if="['add', 'edit'].includes(type)">
              <el-input
                size="mini"
                v-model="form.phone"
                placeholder="请输入 主手机号"
              >
                <template slot="append">
                  <i
                    class="el-input__icon el-icon-circle-plus"
                    @click="addSecondaryPs()"
                  ></i>
                </template>
              </el-input>
              <el-input
                size="mini"
                v-for="(item, index) in secondaryPs"
                v-model="item.phoneNum"
                :key="index"
                placeholder="请输入 副手机号"
              >
                <template slot="append">
                  <i
                    class="el-input__icon el-icon-delete"
                    @click="deleteSecondaryPs(item)"
                  ></i>
                </template>
              </el-input>
            </div>
            <el-input
              v-else
              size="mini"
              v-model="form.phone"
              :disabled="true"
            ></el-input>
          </template>
          <template #licensePlatesForm="{ value, type }">
            <div v-if="['add', 'edit'].includes(type)">
              <el-input
                size="mini"
                v-model="form.licensePlate"
                placeholder="请输入 车牌号"
              >
                <template slot="append">
                  <i
                    class="el-input__icon el-icon-circle-plus"
                    @click="addLicensePlates()"
                  ></i>
                </template>
              </el-input>
              <el-input
                size="mini"
                v-for="(item, index) in licensePlates"
                v-model="item.licensePlate"
                :key="index"
                placeholder="请输入 车牌号"
              >
                <template slot="append">
                  <i
                    class="el-input__icon el-icon-delete"
                    @click="deleteLicensePlates(item)"
                  ></i>
                </template>
              </el-input>
            </div>
            <el-input
              v-else
              size="mini"
              v-model="form.licensePlate"
              :disabled="true"
            ></el-input>
          </template>
          <template #menu="{ row, size, index }">
            <el-divider direction="vertical"/>
            <el-dropdown>
              <el-button type="text" :size="size">
                更多<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item
                  @click.native="$refs.crud.rowEdit(row, index)"
                >
                  <i class="el-icon-edit"></i>编 辑
                </el-dropdown-item>
                <el-dropdown-item @click.native="$refs.crud.rowDel(row, index)">
                  <i class="el-icon-delete"></i>删 除
                </el-dropdown-item>
                <el-dropdown-item @click.native="rowAvatar(row)">
                  <i class="el-icon-user"></i>上传头像
                </el-dropdown-item>
                <el-dropdown-item @click.native="rowAttach(row)">
                  <i class="el-icon-download"></i>附件管理
                </el-dropdown-item>
                <el-dropdown-item @click.native="rowHealthRecords(row)">
                  <i class="el-icon-suitcase"></i>健康记录
                </el-dropdown-item>
                <el-dropdown-item @click.native="rowLog(row)">
                  <i class="el-icon-time"></i>操作日志
                </el-dropdown-item>

              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </avue-crud>
        <el-dialog
          title="用户角色配置"
          append-to-body
          :visible.sync="roleBox"
          width="345px"
        >
          <el-tree
            :data="roleGrantList"
            show-checkbox
            check-strictly
            default-expand-all
            node-key="id"
            size="mini"
            ref="treeRole"
            :default-checked-keys="roleTreeObj"
            :props="props"
          >
          </el-tree>

          <span slot="footer" class="dialog-footer">
            <el-button size="mini" @click="roleBox = false">取 消</el-button>
            <el-button size="mini" type="primary" @click="submitRole"
            >确 定</el-button
            >
          </span>
        </el-dialog>
        <el-dialog
          title="用户数据导入"
          append-to-body
          :visible.sync="excelBox"
          width="555px"
        >
          <avue-form
            :option="excelOption"
            v-model="excelForm"
            :upload-after="uploadAfter"
          >
            <template slot="excelTemplate">
              <el-button type="primary" @click="handleTemplate">
                点击下载<i class="el-icon-download el-icon--right"></i>
              </el-button>
            </template>
          </avue-form>
        </el-dialog>
        <el-dialog
          title="头像上传"
          append-to-body
          :visible.sync="avatarBox"
          width="555px"
        >
          <avue-form
            :option="avatarOption"
            v-model="avatarForm"
            :upload-after="uploadAfter"
          >
          </avue-form>
        </el-dialog>
        <log-opt-dialog ref="logOptDialogRef" :module="module"/>

        <user-health ref="healthRecordsRef"></user-health>

        <user-attachment
          ref="attachDialogRef"
          :attach-upload="permissionList.attachUploadBtn"
          :attach-edit="permissionList.attachEditBtn"
          :attach-download="permissionList.attachDownloadBtn"
          :attach-delete="permissionList.attachDeleteBtn"
        />
        <el-dialog
          title="标记省内/外"
          append-to-body
          :visible.sync="tagProvince.visible"
          width="345px"
        >
          <avue-form
            :option="tagProvince.option"
            v-model="tagProvince.form"
            @submit="handleTagProvinceSubmit"
          >
          </avue-form>
        </el-dialog>
        <el-dialog
          title="标记是否演绎"
          append-to-body
          :visible.sync="tagYY.visible"
          width="345px"
        >
          <avue-form
            :option="tagYY.option"
            v-model="tagYY.form"
            @submit="handleTagYYSubmit"
          >
          </avue-form>
        </el-dialog>
      </basic-container>
    </el-col>
  </el-row>
</template>

<script>
import {
  add,
  changeYY,
  getList,
  getUser,
  getUserPlatform,
  grant, grantPlus,
  markInProvince,
  remove,
  resetPassword,
  searchSamePhone,
  unlock,
  update,
  updateAvatar,
  updatePlatform,
} from "@/api/system/user";
import {exportBlob} from "@/api/common";
import {getDeptLazyTree, getDeptTree} from "@/api/system/dept";
import {getRoleTree} from "@/api/system/role";
import {getPostList} from "@/api/system/post";
import {mapGetters} from "vuex";
import website from "@/config/website";
import {getToken} from "@/util/auth";
import {downloadXls} from "@/util/util";
import {dateNow} from "@/util/date";
import NProgress from "nprogress";
import "nprogress/nprogress.css";
import func from "@/util/func";
import UserAttachment from "@/views/system/components/user-attachment";
import UserHealth from "@/views/system/components/user-health";
import LogOptDialog from "@/components/log-opt-dialog";
import {getDetail, getRegionLazyTree} from "@/api/base/region";

export default {
  components: {
    UserAttachment,
    LogOptDialog,
    UserHealth,
  },
  data() {
    const validatePhone = (rule, value, callback) => {
      if (!value || value === "") {
        callback(new Error("请输入手机号"));
      } else {
        if (!/^1[3|4|5|6|7|8|9][0-9]\d{8}$/.test(value)) {
          callback(new Error("手机号格式有误"));
        } else {
          searchSamePhone({id: this.form.id, phone: value}).then((res) => {
            if (res.data.data) {
              if (res.data.data === "1") {
                callback();
              } else {
                callback(new Error(res.data.data));
              }
            } else {
              callback();
            }
          });
        }
      }
    };
    return {
      status: '1',
      householdRegistrationOptions: [],
      token: getToken(),
      module: "blade_user",
      normalBusinessName: "blade_user_normal",
      form: {},
      roleBox: false,
      avatarBox: false,
      excelBox: false,
      platformBox: false,
      initFlag: true,
      selectionList: [],
      loading: true,
      platformLoading: false,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      platformPage: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      init: {
        roleTree: [],
        deptTree: [],
      },
      props: {
        label: "title",
        value: "key",
      },
      roleGrantList: [],
      roleTreeObj: [],
      treeDeptId: "",
      treeData: [],
      treeOption: {
        size: "mini",
        searchSize: "mini",
        nodeKey: "id",
        lazy: true,
        treeLoad: function (node, resolve) {
          const parentId = node.level === 0 ? 0 : node.data.id;
          getDeptLazyTree(parentId).then((res) => {
            resolve(
              res.data.data.map((item) => {
                return {
                  ...item,
                  leaf: !item.hasChildren,
                };
              })
            );
          });
        },
        addBtn: false,
        menu: false,
        props: {
          labelText: "标题",
          label: "title",
          value: "value",
          children: "children",
        },
      },
      option: {
        searchEnter: true,
        align: "center",
        size: "mini",
        searchSize: "mini",
        height: "auto",
        calcHeight: 80,
        tip: false,
        searchIcon: true, // 搜索栏能否收缩
        searchIndex: 2, // 搜索按钮索引,超出的搜索栏收缩
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        selection: true,
        menuWidth: 150,
        addBtn: true,
        editBtn: false,
        viewBtn: true,
        delBtn: false,
        dialogType: "drawer",
        dialogClickModal: false,
        column: [
          {
            label: "登录账号",
            prop: "account",
            search: true,
            display: false,
          },
          {
            label: "状态",
            prop: "status",
            slot: true,
            display: false,
            type: "select",
            disabled: true,
            dataType: "number",
            placeholder: " ",
            dicUrl: "/api/blade-system/dict/dictionary?code=user_status",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            width: "60",
            align: "left",
            fixed: "left",
          },
          {
            label: "用户姓名",
            prop: "realName",
            display: false,
          },
          {
            label: "头像",
            prop: "avatar",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
          },
          {
            label: "用户编号",
            prop: "code",
            hide: true,
            display: false,
          },
          {
            label: "角色",
            prop: "roleId",
            type: "tree",
            multiple: true,
            dicUrl: "/api/blade-system/role/tree",
            props: {
              label: "title",
              value: "key",
            },
            hide: true,
            display: false,
            search: true,
            showColumn: false,
          },
          {
            label: "所属角色",
            prop: "roleName",
            slot: true,
            display: false,
          },
          {
            label: "所属部门",
            prop: "deptName",
            filters: true,
            slot: true,
            display: false,
          },
          {
            label: "岗位",
            prop: "postId",
            type: "tree",
            multiple: true,
            dicUrl: "/api/blade-system/post/select-all",
            props: {
              label: "postName",
              value: "id",
            },
            hide: true,
            display: false,
            search: true,
            showColumn: false,
          },
          {
            label: "岗位",
            prop: "postName",
            slot: true,
            display: false,
          },
          {
            label: "考勤编号",
            prop: "zkecoBadgeNumber",
            search: true,
            hide: true,
            overHidden: true,
            display: false,
          },
          {
            label: "性别",
            prop: "sex",
            type: "select",
            minWidth: 60,
            dicData: [
              {
                label: "男",
                value: 1,
              },
              {
                label: "女",
                value: 2,
              },
              {
                label: "未知",
                value: 3,
              },
            ],
            search: true,
            overHidden: true,
            display: false,
            hide: true,
          },
          {
            label: "婚否",
            prop: "isMarried",
            type: "select",
            hide: true,
            minWidth: 60,
            dataType: "number",
            dicData: [
              {
                label: "否",
                value: 0,
              },
              {
                label: "是",
                value: 1,
              },
            ],
            search: true,
            display: false,
          },

          {
            label: "人员分布",
            display: false,
            type: "select",
            prop: "userProvince",
            hide: true,
            showColumn: false,
            span: 6,
            search: true,
            dicData: [
              {
                label: "省内",
                value: "1",
              },
              {
                label: "省外",
                value: "0",
              },
            ],
          },
          {
            label: "家庭住址",
            prop: "HomeAddress",
            type: "input",
            hide: true,
            overHidden: true,
            display: false,
          },
          {
            label: "入职时间",
            prop: "entryTime",
            type: "date",
            minWidth: 90,
            searchRange: true,
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            search: true,
            overHidden: true,
            display: false,
          },
          {
            label: "离职时间",
            prop: "leaveTime",
            type: "date",
            minWidth: 90,
            searchRange: true,
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            search: false,
            hide: true,
            overHidden: true,
            display: false,
          },
          {
            label: "劳动合同",
            prop: "signedContract",
            type: "select",
            minWidth: 80,
            dataType: "number",
            dicData: [
              {
                label: "未签订",
                value: null,
              },
              {
                label: "已签订",
                value: 1,
              },
            ],
            display: false,
          },
          {
            label: "生日",
            type: "date",
            prop: "birthday",
            minWidth: 90,
            searchRange: true,
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            search: true,
            overHidden: true,
            display: false,
            hide: true,
          },
          {
            label: "身份证号",
            prop: "idCard",
            type: "input",
            minWidth: 140,
            overHidden: true,
            display: false,
            hide: true,
          },
          {
            label: "手机号码",
            prop: "phone",
            minWidth: 100,
            search: true,
            overHidden: true,
            display: false,
            hide: true,
          },
        ],
        group: [
          {
            label: "基础信息",
            prop: "baseInfo",
            icon: "el-icon-user-solid",
            column: [
              {
                label: "所属租户",
                prop: "tenantId",
                type: "tree",
                dicUrl: "/api/blade-system/tenant/select",
                props: {
                  label: "tenantName",
                  value: "tenantId",
                },
                hide: !website.tenantMode,
                addDisplay: website.tenantMode,
                editDisplay: website.tenantMode,
                viewDisplay: website.tenantMode,
                rules: [
                  {
                    required: true,
                    message: "请输入所属租户",
                    trigger: "click",
                  },
                ],
              },
              {
                label: "登录账号",
                prop: "account",
                rules: [
                  {
                    required: true,
                    message: "请输入登录账号",
                    trigger: "blur",
                  },
                ],
              },
              {
                label: "用户平台",
                type: "select",
                dicUrl: "/api/blade-system/dict/dictionary?code=user_type",
                props: {
                  label: "dictValue",
                  value: "dictKey",
                },
                dataType: "number",
                slot: true,
                prop: "userType",
                rules: [
                  {
                    required: true,
                    message: "请选择用户平台",
                    trigger: "blur",
                  },
                ],
              },
              {
                label: "用户编号",
                prop: "code",
                display: true,
              },
              {
                label: "考勤编号",
                prop: "zkecoUserId",
                type: "select",
                filterable: true,
                dicUrl: "/api/ni/pa/paAttUserInfo/badge-number-list",
                dataType: "number",
                props: {
                  label: "badgenumber",
                  value: "userid",
                },
              },
              {
                label: "状态",
                prop: "status",
                slot: true,
                type: "select",
                dataType: "number",
                placeholder: " ",
                value: "1",
                dicUrl: "/api/blade-system/dict/dictionary?code=user_status",
                props: {
                  label: "dictValue",
                  value: "dictKey",
                },
                width: "60",
                align: "left",
                fixed: "left",
                rules: [
                  {
                    required: true,
                    message: "请选择状态",
                    trigger: "blur",
                  },
                ],
              },
              {
                label: "离职时间",
                prop: "leaveTime",
                hide: true,
                display: false,
                type: "datetime",
                format: "yyyy-MM-dd HH:mm:ss",
                valueFormat: "yyyy-MM-dd HH:mm:ss",
                rules: [
                  {
                    required: true,
                    message: "请选择 离职时间",
                    trigger: "blur",
                  },
                ],
              },
            ],
          },
          {
            label: "详细信息",
            prop: "detailInfo",
            icon: "el-icon-s-order",
            column: [
              {
                label: "用户昵称",
                prop: "name",
                hide: true,
                rules: [
                  {
                    required: true,
                    message: "请输入用户昵称",
                    trigger: "blur",
                  },
                ],
              },
              {
                label: "用户姓名",
                prop: "realName",
                rules: [
                  {
                    required: true,
                    message: "请输入用户姓名",
                    trigger: "blur",
                  },
                  {
                    min: 2,
                    max: 5,
                    message: "姓名长度在2到5个字符",
                  },
                ],
              },
              {
                label: "身份证号",
                prop: "idCard",
                type: "input",
                rules: [
                  {
                    required: true,
                    message: "请选择身份证号",
                    // validator: validateIdCard,
                    trigger: "blur",
                  },
                ],
              },
              {
                label: "手机号码",
                prop: "phone",
                overHidden: true,
                rules: [
                  {required: true, validator: validatePhone, trigger: "blur"},
                ],
                formslot: true,
              },
              {
                label: "电子邮箱",
                prop: "email",
                hide: true,
                overHidden: true,
              },
              {
                label: "用户性别",
                prop: "sex",
                type: "select",
                dicData: [
                  {
                    label: "男",
                    value: 1,
                  },
                  {
                    label: "女",
                    value: 2,
                  },
                  {
                    label: "未知",
                    value: 3,
                  },
                ],
                hide: true,
              },
              {
                label: "用户生日",
                type: "date",
                prop: "birthday",
                format: "yyyy-MM-dd",
                valueFormat: "yyyy-MM-dd",
                hide: true,
              },
              {
                label: "入职时间",
                prop: "entryTime",
                type: "date",
                format: "yyyy-MM-dd",
                valueFormat: "yyyy-MM-dd",
                rules: [
                  {
                    required: false,
                    message: "请选择 入职日期",
                    trigger: "blur",
                  },
                ],
              },
              {
                label: "户籍",
                prop: "householdRegistration",
                type: "input",
                overHidden: true,
                span: 6,
                // rules: [
                //   {
                //     required: true,
                //     message: "请输入户籍",
                //     trigger: "blur",
                //   },
                // ],
              },
              {
                label: "",
                labelWidth: 0,
                prop: "regionDetail",
                type: "input",
                overHidden: true,
                rules: [
                  {
                    required: true,
                    message: "请输入户籍",
                    trigger: "blur",
                  },
                ],
                span: 6,
              },
              {
                label: "户籍编号",
                display: false,
                prop: "regionCode",
                hide: true,
                showColumn: false,
                span: 6,
              },
              {
                label: "家庭住址",
                prop: "homeAddress",
                type: "input",
                overHidden: true,
              },
              {
                label: "是否已婚",
                prop: "isMarried",
                type: "select",
                rules: [
                  {
                    required: false,
                    message: "请选择 是否已婚",
                    trigger: "blur",
                  },
                ],
                dataType: "number",
                dicData: [
                  {
                    label: "否",
                    value: 0,
                  },
                  {
                    label: "是",
                    value: 1,
                  },
                ],
                // value: 0,
              },
              {
                label: "政治面貌",
                prop: "politicalStatus",
                type: "select",
                filterable: true,
                dicData: [],
                dataType: "number",
                props: {
                  label: "dictValue",
                  value: "dictKey",
                },
                // value: 1,
              },
              {
                label: "文化程度",
                prop: "eduLevel",
                type: "tree",
                rules: [
                  {
                    required: false,
                    message: "请选择 文化程度",
                    trigger: "blur",
                  },
                ],
                props: {
                  label: "dictValue",
                  value: "dictKey",
                },
                dataType: "number",
                dicData: [],
              },
              {
                label: "毕业院校",
                prop: "eduSchool",
                type: "input",
                overHidden: true,
              },
              {
                label: "毕业专业",
                prop: "eduMajor",
                type: "input",
                overHidden: true,
              },
              {
                label: "账号状态",
                prop: "statusName",
                hide: true,
                display: false,
              },
              {
                label: "副手机号",
                prop: "secondaryPhones",
                addDisplay: false,
                editDisplay: false,
                viewDisplay: true,
                readonly: true,
                placeholder: " ",
              },
              {
                label: "车牌号",
                prop: "licensePlate",
                addDisplay: false,
                editDisplay: false,
                viewDisplay: true,
                readonly: true,
                placeholder: " ",
              },
              {
                label: "车牌号",
                prop: "licensePlates",
                addDisplay: true,
                editDisplay: true,
                viewDisplay: false,
              },
              {
                label: "合同开始",
                prop: "contractStart",
                type: "date",
                format: "yyyy-MM-dd",
                valueFormat: "yyyy-MM-dd",
                hide: true,
              },
              {
                label: "合同结束",
                prop: "contractEnd",
                type: "date",
                format: "yyyy-MM-dd",
                valueFormat: "yyyy-MM-dd",
                hide: true,
              },
              {
                label: "合同附件",
                prop: "attachContract",
                type: "upload",
                drag: true,
                loadText: "模板上传中，请稍等",
                span: 24,
                propsHttp: {
                  res: "data",
                },
                limit: 1,
                hide: true,
                viewDisplay: false,
                addDisplay: true,
                editDisplay: true,
                action:
                  "/api/blade-resource/oss/endpoint/put-file-attach-business",
              },
            ],
          },
          {
            label: "职责信息",
            prop: "dutyInfo",
            icon: "el-icon-s-custom",
            column: [
              {
                label: "所属角色",
                prop: "roleId",
                multiple: true,
                type: "tree",
                dicData: [],
                props: {
                  label: "title",
                },
                checkStrictly: true,
                slot: true,
                rules: [
                  {
                    required: true,
                    message: "请选择所属角色",
                    trigger: "click",
                  },
                ],
              },
              {
                label: "所属部门",
                prop: "deptId",
                type: "tree",
                multiple: true,
                dicData: [],
                props: {
                  label: "title",
                },
                checkStrictly: true,
                slot: true,
                rules: [
                  {
                    required: true,
                    message: "请选择所属部门",
                    trigger: "click",
                  },
                ],
              },
              {
                label: "所属岗位",
                prop: "postId",
                type: "tree",
                multiple: true,
                dicData: [],
                props: {
                  label: "postName",
                  value: "id",
                },
                rules: [
                  {
                    required: true,
                    message: "请选择所属岗位",
                    trigger: "click",
                  },
                ],
              },
              {
                label: "归属公司",
                prop: "belongTo",
                type: "select",
                dicData: [],
                dataType: "number",
                props: {
                  label: "dictValue",
                  value: "dictKey",
                },
                rules: [
                  {
                    required: true,
                    message: "请选择 归属公司",
                    trigger: "blur",
                  },
                ],
              },
              {
                label: "焊工号",
                prop: "welderSn",
              },
            ],
          },
        ],
      },
      data: [],
      platformQuery: {},
      platformSelectionList: [],
      platformData: [],
      platformForm: {},
      platformOption: {
        size: "mini",
        searchSize: "mini",
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        selection: true,
        viewBtn: true,
        dialogClickModal: false,
        menuWidth: 120,
        editBtnText: "配置",
        column: [
          {
            label: "登录账号",
            prop: "account",
            search: true,
            display: false,
          },
          {
            label: "所属租户",
            prop: "tenantName",
            slot: true,
            display: false,
          },
          {
            label: "用户姓名",
            prop: "realName",
            search: true,
            display: false,
          },
          {
            label: "用户平台",
            prop: "userTypeName",
            slot: true,
            display: false,
          },
          {
            label: "用户平台",
            type: "select",
            dicUrl: "/api/blade-system/dict/dictionary?code=user_type",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            dataType: "number",
            search: true,
            hide: true,
            display: false,
            prop: "userType",
            rules: [
              {
                required: true,
                message: "请选择用户平台",
                trigger: "blur",
              },
            ],
          },
          {
            label: "用户拓展",
            prop: "userExt",
            type: "textarea",
            minRows: 8,
            span: 24,
            overHidden: true,
            row: true,
            hide: true,
          },
        ],
      },
      excelForm: {},
      excelOption: {
        size: "mini",
        searchSize: "mini",
        submitBtn: false,
        emptyBtn: false,
        column: [
          {
            label: "模板上传",
            prop: "excelFile",
            type: "upload",
            drag: true,
            loadText: "模板上传中，请稍等",
            span: 24,
            propsHttp: {
              res: "data",
            },
            tip: "请上传 .xls,.xlsx 标准格式文件",
            action: "/api/blade-user/import-user",
          },
          {
            label: "数据覆盖",
            prop: "isCovered",
            type: "switch",
            align: "center",
            width: 80,
            dicData: [
              {
                label: "否",
                value: 0,
              },
              {
                label: "是",
                value: 1,
              },
            ],
            value: 0,
            slot: true,
            rules: [
              {
                required: true,
                message: "请选择是否覆盖",
                trigger: "blur",
              },
            ],
          },
          {
            label: "模板下载",
            prop: "excelTemplate",
            formslot: true,
            span: 24,
          },
        ],
      },
      avatarOption: {
        size: "mini",
        submitBtn: false,
        emptyBtn: false,
        column: [
          {
            label: "头像",
            type: "upload",
            dataType: "string",
            listType: "picture-img",
            propsHttp: {
              res: "data",
              url: "link",
            },
            action: "/api/blade-resource/oss/endpoint/put-file-attach",
            tip: "只能上传jpg/png，且不超过500kb",
            span: 24,
            prop: "avatarUrl",
            rules: [
              {
                required: true,
                message: "请选择头像",
                trigger: "blur",
              },
            ],
          },
        ],
      },
      avatarForm: {},
      secondaryPs: [],
      licensePlates: [],
      yy: false,
      tagProvince: {
        visible: false,
        option: {
          span: 24,
          size: "mini",
          emptyBtn: false,
          column: [
            {
              label: "省内/外",
              prop: "inProvince",
              type: "radio",
              align: "center",
              width: 80,
              dicData: [
                {
                  label: "省内",
                  value: 0,
                },
                {
                  label: "省外",
                  value: 1,
                },
              ],
              value: 0,
              slot: true,
              rules: [
                {
                  required: true,
                  message: "请标记省内/外",
                  trigger: "blur",
                },
              ],
            },
          ],
        },
        form: {}
      },
      tagYY: {
        visible: false,
        option: {
          span: 24,
          size: "mini",
          emptyBtn: false,
          column: [
            {
              label: "是否演绎",
              prop: "yy",
              type: "radio",
              align: "center",
              width: 80,
              dicData: [
                {
                  label: "是",
                  value: true,
                },
                {
                  label: "否",
                  value: false,
                },
              ],
              value: true,
              slot: true,
              rules: [
                {
                  required: true,
                  message: "请选择是否演绎",
                  trigger: "blur",
                },
              ],
            },
          ],
        },
        form: {}
      }

    };
  },
  watch: {
    "form.tenantId"() {
      if (this.form.tenantId !== "" && this.initFlag) {
        this.initData(this.form.tenantId);
      }
    },
    "excelForm.isCovered"() {
      if (this.excelForm.isCovered !== "") {
        const column = this.findObject(this.excelOption.column, "excelFile");
        column.action = `/api/blade-user/import-user?isCovered=${this.excelForm.isCovered}`;
      }
    },
    "form.status"() {
      const column = this.findObject(this.option.group, "leaveTime");
      column.display = this.form.status === 2;
    },
    status: {
      handler(val) {
        const column = this.findObject(this.option.column, "leaveTime");
        column.search = val === '2';
        this.page.currentPage = 1
        this.onLoad(this.page)
      }
    }
  },
  computed: {
    ...mapGetters(["userInfo", "permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.user_add, false),
        viewBtn: this.vaildData(this.permission.user_view, false),
        delBtn: this.vaildData(this.permission.user_delete, false),
        editBtn: this.vaildData(this.permission.user_edit, false),

        attachUploadBtn: this.vaildData(
          this.permission.user_attach_upload,
          false
        ),
        attachEditBtn: this.vaildData(this.permission.user_attach_edit, false),
        attachDownloadBtn: this.vaildData(
          this.permission.user_attach_download,
          false
        ),
        attachDeleteBtn: this.vaildData(
          this.permission.user_attach_delete,
          false
        ),
      };
    },
    platformPermissionList() {
      return {
        addBtn: false,
        viewBtn: false,
        delBtn: false,
        editBtn: this.vaildData(this.permission.user_edit, false),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  mounted() {
    // 非租户模式默认加载管理组数据
    if (!website.tenantMode) {
      this.initData(website.tenantId);
    }
    this.initDic();
  },
  methods: {
    nodeClick(data) {
      this.treeDeptId = data.id;
      this.page.currentPage = 1;
      this.onLoad(this.page);
    },
    initData(tenantId) {
      getRoleTree(tenantId).then((res) => {
        const column = this.findObject(this.option.group, "roleId");
        column.dicData = res.data.data;
      });
      getDeptTree(tenantId).then((res) => {
        const column = this.findObject(this.option.group, "deptId");
        column.dicData = res.data.data;
      });
      getPostList(tenantId).then((res) => {
        const column = this.findObject(this.option.group, "postId");
        column.dicData = res.data.data;
      });
    },
    initDic() {
      this.$http
        .get(
          "/api/blade-system/dict-biz/dictionary?code=ni_pa_political_status"
        )
        .then((res) => {
          const column = this.findObject(this.option.group, "politicalStatus");
          column.dicData = res.data.data;
        });
      this.$http
        .get("/api/blade-system/dict-biz/dictionary-tree?code=ni_pa_eduLevel")
        .then((res) => {
          const column = this.findObject(this.option.group, "eduLevel");
          column.dicData = res.data.data;
        });
      this.$http
        .get("/api/blade-system/dict/dictionary?code=natergy_company")
        .then((res) => {
          const column = this.findObject(this.option.group, "belongTo");
          column.dicData = res.data.data;
        });
      getRegionLazyTree().then((res) => {
        this.householdRegistrationOptions = res.data.data;
      });
    },
    submitRole() {
      const roleList = this.$refs.treeRole.getCheckedKeys().join(",");
      this.$confirm('请选择角色配置操作！', '提示', {
        distinguishCancelAndClose: true,
        confirmButtonText: '累加',
        cancelButtonText: '替换',
        type: 'warning'
      }).then(() => {
        grantPlus(this.ids, roleList).then(() => {
          this.roleBox = false;
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.onLoad(this.page);
        });
      }) .catch(action => {
        if(action==='cancel'){
          grant(this.ids, roleList).then(() => {
            this.roleBox = false;
            this.$message({
              type: "success",
              message: "操作成功!",
            });
            this.onLoad(this.page);
          });
        }
      });
    },
    processRow(row) {
      return new Promise((resolve) => {
        row.deptId = row.deptId.join(",");
        row.roleId = row.roleId.join(",");
        row.postId = row.postId.join(",");
        if (this.form.zkecoUserId) {
          row.zkecoBadgeNumber = this.form.$zkecoUserId;
        } else {
          row.zkecoBadgeNumber = "111111";
        }

        if (this.secondaryPs) {
          let phones = [];
          this.secondaryPs.forEach((item) => {
            if (item.phoneNum && item.phoneNum.trim()) {
              phones.push(item.phoneNum.trim());
            }
          });
          row.secondaryPhones = phones.join(",");
        } else {
          row.secondaryPhones = "clear";
        }

        let licensePs = [];
        if (row.licensePlate && row.licensePlate.trim()) {
          licensePs.push(row.licensePlate.trim());
        }
        if (this.licensePlates) {
          this.licensePlates.forEach((item) => {
            if (item.licensePlate && item.licensePlate.trim()) {
              licensePs.push(item.licensePlate.trim());
            }
          });
        }
        if (licensePs.length === 0) {
          row.licensePlate = "clear";
        } else {
          row.licensePlate = licensePs.join(",");
        }

        row.contractSN = this.form.contractSN;
        if (
          row.attachContract &&
          row.attachContract[0] &&
          row.attachContract[0].label
        ) {
          row.contractName = row.attachContract[0].label;
        }
        if (this.form.regionCode && this.form.regionCode.length === 3) {
          // 户籍拼接
          getDetail(this.form.regionCode[2]).then((res) => {
            const province = res.data.data.provinceName;
            const city = res.data.data.cityName;
            const district = res.data.data.districtName;
            row.householdRegistration = `${province}${city}${district}${this.form.regionDetail}`;
            resolve(row);
          });
        }
      });
    },
    rowSave(row, done, loading) {
      this.processRow(row)
        .then((processedRow) => {
          // 等待processRow完成
          add(processedRow).then(
            () => {
              this.initFlag = false;
              this.onLoad(this.page);
              this.$message({
                type: "success",
                message: "操作成功!",
              });
              done();
            },
            (error) => {
              window.console.log(error);
              loading();
            }
          );
        })
        .catch((error) => {
          window.console.log(error);
          loading();
        });
    },

    rowUpdate(row, index, done, loading) {
      this.processRow(row)
        .then((processedRow) => {
          // 等待processRow完成
          update(processedRow).then(
            () => {
              this.initFlag = false;
              this.onLoad(this.page);
              this.$message({
                type: "success",
                message: "操作成功!",
              });
              done();
            },
            (error) => {
              window.console.log(error);
              loading();
            }
          );
        })
        .catch((error) => {
          window.console.log(error);
          loading();
        });
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    searchReset() {
      this.query = {};
      this.treeDeptId = "";
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    handleBatchCommand(command) {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      if (command === 'delete') {
        this.handleDelete()
      } else if (command === 'grant') {
        this.handleGrant()
      } else if (command === 'reset') {
        this.handleReset()
      } else if (command === 'lock') {
        this.handleLock()
      } else if (command === 'tag') {
        this.handleTagProvince()
      } else if (command === 'tagYY') {
        this.handleTagYY()
      }
    },
    handleTagYY() {
      this.tagYY.visible = true
    },
    handleTagYYSubmit(form, done) {
      changeYY(this.ids, form.yy)
        .then(() => {
          this.tagYY.visible = false;
          this.onLoad(this.page, this.query);
          this.$message.success("操作成功！");
        }).finally(() => {
        done();
      });
    },
    handleTagProvince() {
      this.tagProvince.visible = true
    },
    handleTagProvinceSubmit(form, done) {
      markInProvince(this.ids, form.inProvince)
        .then(() => {
          this.tagProvince.visible = false;
          this.onLoad(this.page, this.query);
          this.$message.success("操作成功！");
        }).finally(() => {
        done();
      });
    },
    handleDelete() {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    handleReset() {
      this.$confirm("确定将选择账号密码重置为123456?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return resetPassword(this.ids);
        })
        .then(() => {
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    handleGrant() {
      this.roleTreeObj = [];
      if (this.selectionList.length === 1) {
        this.roleTreeObj = this.selectionList[0].roleId.split(",");
      }
      getRoleTree().then((res) => {
        this.roleGrantList = res.data.data;
        this.roleBox = true;
      });
    },
    handlePlatform() {
      this.platformBox = true;
    },
    handleLock() {
      this.$confirm("确定将选择账号解封？", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return unlock(this.ids);
        })
        .then(() => {
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    handleImport() {
      this.excelBox = true;
    },
    uploadAfter(res, done) {
      this.avatarForm.avatar = res.attachId;
      updateAvatar(this.avatarForm).then(() => {
        this.$message({
          type: "success",
          message: "操作成功!",
        });
        this.avatarBox = false;
        this.onLoad(this.page);
      });
      done();
    },
    handleExportQr() {
      const account = func.toStr(this.query.account);
      const realName = func.toStr(this.query.realName);
      let ids = "";
      if (this.selectionList && this.selectionList.length > 0)
        ids = this.selectionList
          .map((item) => {
            return item.id;
          })
          .join(",");
      this.$confirm("是否导出二维码?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        NProgress.start();
        exportBlob(
          `/api/blade-user/export-qr?${
            this.website.tokenHeader
          }=${getToken()}&account=${account}&realName=${realName}&ids=${ids}`
        ).then((res) => {
          let blob = new Blob([res.data], {type: "application/zip"});
          let url = window.URL.createObjectURL(blob);
          const link = document.createElement("a"); // 创建a标签
          link.href = url;
          link.download = "二维码.zip"; // 重命名文件
          link.click();
          URL.revokeObjectURL(url); // 释放内存
          NProgress.done();
        });
      });
    },
    handleExport() {
      const account = func.toStr(this.query.account);
      const realName = func.toStr(this.query.realName);
      this.$confirm("是否导出用户数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        NProgress.start();
        exportBlob(
          `/api/blade-user/export-user?${
            this.website.tokenHeader
          }=${getToken()}&account=${account}&realName=${realName}`
        ).then((res) => {
          downloadXls(res.data, `用户数据表${dateNow()}.xlsx`);
          NProgress.done();
        });
      });
    },
    handleTemplate() {
      exportBlob(
        `/api/blade-user/export-template?${
          this.website.tokenHeader
        }=${getToken()}`
      ).then((res) => {
        downloadXls(res.data, "用户数据模板.xlsx");
      });
    },
    beforeOpen(done, type) {
      this.secondaryPs = [];
      this.licensePlates = [];
      if (["edit", "view"].includes(type)) {
        getUser(this.form.id).then((res) => {
          this.form = res.data.data;
          if (this.form.hasOwnProperty("deptId")) {
            this.form.deptId = this.form.deptId.split(",");
          }
          if (this.form.hasOwnProperty("roleId")) {
            this.form.roleId = this.form.roleId.split(",");
          }
          if (this.form.hasOwnProperty("postId")) {
            this.form.postId = this.form.postId.split(",");
          }
          if ("edit" === type) {
            this.form.contractSN = res.data.data.contractSN;
            this.form.attachContract = null;
            this.initialSecondaryPs();
            this.initialLicensePlate();
          }
        });
      }
      if (["add", "edit"].includes(type)) {
        const column = this.findObject(this.option.group, "attachContract");
        column.data = {
          businessKey: this.form.id,
          businessName: "blade_user_contract",
        };
      }
      this.initFlag = true;
      done();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      // entryTime/birthday 范围查询
      const query = Object.assign(params, this.query);
      if (query.entryTime && query.entryTime.length === 2) {
        query.entryTimeStart = query.entryTime[0];
        query.entryTimeEnd = query.entryTime[1];
        query.entryTime = null;
      }
      if (query.birthday && query.birthday.length === 2) {
        query.birthdayStart = query.birthday[0];
        query.birthdayEnd = query.birthday[1];
        query.birthday = null;
      }
      if(query.leaveTime&&query.leaveTime.length===2){
        query.leaveTimeStart = query.leaveTime[0];
        query.leaveTimeEnd = query.leaveTime[1];
        query.leaveTime = null
      }
      query.status = this.status !== 'all' ? this.status : null
      getList(page.currentPage, page.pageSize, query, this.treeDeptId).then(
        (res) => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        }
      );
    },
    platformRowUpdate(row, index, done, loading) {
      updatePlatform(row.id, row.userType, row.userExt).then(
        () => {
          this.platformOnLoad(this.platformPage);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          window.console.log(error);
          loading();
        }
      );
    },
    platformBeforeOpen(done, type) {
      if (["edit", "view"].includes(type)) {
        getUserPlatform(this.platformForm.id).then((res) => {
          this.platformForm = res.data.data;
        });
      }
      done();
    },
    platformSearchReset() {
      this.platformQuery = {};
      this.platformOnLoad(this.platformPage);
    },
    platformSearchChange(params, done) {
      this.platformQuery = params;
      this.platformPage.currentPage = 1;
      this.platformOnLoad(this.platformPage, params);
      done();
    },
    platformSelectionChange(list) {
      this.platformSelectionList = list;
    },
    platformSelectionClear() {
      this.platformSelectionList = [];
      this.$refs.platformCrud.toggleSelection();
    },
    platformCurrentChange(currentPage) {
      this.platformPage.currentPage = currentPage;
    },
    platformSizeChange(pageSize) {
      this.platformPage.pageSize = pageSize;
    },
    platformRefreshChange() {
      this.platformOnLoad(this.platformPage, this.platformQuery);
    },
    platformOnLoad(page, params = {}) {
      this.platformLoading = true;
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query),
        this.treeDeptId
      ).then((res) => {
        const data = res.data.data;
        this.platformPage.total = data.total;
        this.platformData = data.records;
        this.platformLoading = false;
        this.selectionClear();
      });
    },
    idCardChange(value) {
      let date = value.substring(6, 14);
      let birthday =
        date.substring(0, 4) +
        "-" +
        date.substring(4, 6) +
        "-" +
        date.substring(6, 8) +
        " 00:00:00";
      let sex = value.substring(16, 17) % 2 == 1 ? 1 : 2;
      this.$set(this.form, "birthday", birthday);
      this.$set(this.form, "sex", sex);
    },
    rowAvatar(row) {
      this.avatarForm.id = row.id;
      this.avatarBox = true;
    },
    rowAttach(row) {
      this.$refs.attachDialogRef.init(row.id, this.normalBusinessName);
    },
    rowHealthRecords(row) {
      this.$refs.healthRecordsRef.init(row.id);
    },
    rowLog(row) {
      this.$refs.logOptDialogRef.init(row.id);
    },
    initialSecondaryPs() {
      if (this.form.secondaryPhones) {
        let phones = this.form.secondaryPhones.split(",");
        let phoneKV = [];
        phones.forEach((phone) => {
          if (phone && phone.trim()) {
            phoneKV.push({phoneNum: phone});
          }
        });
        this.secondaryPs = phoneKV;
      } else {
        this.secondaryPs = [];
      }
    },
    addSecondaryPs() {
      if (!this.form.phone || !this.form.phone.trim()) {
        this.$message({
          type: "error",
          message: "请先填写主手机号!",
        });
        return;
      }
      for (let index = 0; index < this.secondaryPs.length; index++) {
        if (
          !this.secondaryPs[index].phoneNum ||
          !this.secondaryPs[index].phoneNum.trim()
        ) {
          this.$message({
            type: "error",
            message: "请先补充空的输入框!",
          });
          return;
        }
      }
      this.secondaryPs.push({phoneNum: null});
    },
    deleteSecondaryPs(item) {
      this.secondaryPs = this.secondaryPs.filter((i) => i !== item);
    },
    initialLicensePlate() {
      if (this.form.licensePlate) {
        let licensePlates = this.form.licensePlate.split(",");
        this.form.licensePlate = licensePlates.shift();
        let licensePs = [];
        licensePlates.forEach((licensePlate) => {
          if (licensePlate && licensePlate.trim()) {
            licensePs.push({licensePlate});
          }
        });
        this.licensePlates = licensePs;
      } else {
        this.licensePlates = [];
      }
    },
    addLicensePlates() {
      if (!this.form.licensePlate || !this.form.licensePlate.trim()) {
        this.$message({
          type: "error",
          message: "请先补充空的输入框!",
        });
        return;
      }
      for (let index = 0; index < this.licensePlates.length; index++) {
        if (
          !this.licensePlates[index].licensePlate ||
          !this.licensePlates[index].licensePlate.trim()
        ) {
          this.$message({
            type: "error",
            message: "请先补充空的输入框!",
          });
          return;
        }
      }
      this.licensePlates.push({licensePlate: null});
    },
    deleteLicensePlates(item) {
      this.licensePlates = this.licensePlates.filter((i) => i !== item);
    },
    uploadPreview(file, column, done) {
      done(); //默认执行打开方法
    },
  },
};
</script>

<style></style>

import request from '@/router/axios';

export const getList = (current, size, params, createDept) => {
  return request({
    url: '/api/ni/pa/overtime/apply/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
      createDept,
    }
  })
}

export const getPage = (current, size, params) => {
    return request({
      url: '/api/ni/pa/overtime/apply/page',
      method: 'get',
      params: {
        ...params,
        current,
        size,
      }
    })
  }

export const getDetail = (id) => {
  return request({
    url: '/api/ni/pa/overtime/apply/getDetail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/ni/pa/overtime/apply/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

// 加班申请数据新增
export const getOvertimeProcessIdByProcessKey = (processKey) => {
	return request({
		url: `/api/blade-workflow/process/getProcessIdByProcessKey`,
		method: 'GET',
		params:{
			processKey
		}
	})
}

export const add = (row) => {
  return request({
    url: '/api/ni/pa/overtime/apply/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/ni/pa/overtime/apply/submit',
    method: 'post',
    data: row
  })
}

export const getDetailByProcessInsId = (processInsId) => {
  return request({
    url: '/api/ni/pa/overtime/apply/detail',
    method: 'get',
    params: {
      processInsId
    }
  })
}

export const start = (row) => {
  return request({
    url: '/api/ni/pa/overtime/apply/start',
    method: 'post',
    data: row
  })
}

export const saveAndStart = (processDefKey, row) => {
  return request({
    url: '/api/ni/pa/overtime/apply/saveAndStart',
    method: 'post',
    params: {
      processDefKey
    },
    data: row
  })
}

export const getListAnnual = (current, size, params, createDept) => {
  return request({
    url: '/api/ni/pa/overtime/apply/list-annual',
    method: 'get',
    params: {
      ...params,
      current,
      size,
      createDept,
    }
  })
}


export const getDetailListByIds = (current, size, params) => {
  return request({
    url: '/api/ni/pa/overtime/apply/getDetailListByIds',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}



<template>
  <el-dialog :visible.sync="formDialogVisible" fullscreen append-to-body>
    <template slot="title">
      <div class="dialog-header">
        <div class="dialog-title">导入账单</div>
      </div>
    </template>
    <div class="dialog-steps">
      <el-steps :active="active" finish-status="success">
        <el-step title="上传文件"></el-step>
        <el-step title="核对结果"></el-step>
        <el-step title="确认打款"></el-step>
        <el-step title="操作完成"></el-step>
      </el-steps>
    </div>

    <!-- 步骤1: 上传文件 -->
    <div v-show="active === 0" class="upload-container">
      <div class="upload-section">
        <el-upload
          ref="upload"
          class="upload-demo"
          drag
          action="#"
          :before-upload="beforeUpload"
          :on-change="handleFileChange"
          :on-remove="handleFileRemove"
          :auto-upload="false"
          accept=".xlsx"
          :limit="1"
          :file-list="fileList"
        >
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将Excel文件拖到此处，或<em>点击上传</em></div>
          <div class="el-upload__tip" slot="tip">
            只能上传Excel文件(.xlsx)，且不超过10MB
          </div>
        </el-upload>
      </div>

      <div class="upload-tips">
        <h4>Excel文件格式要求：</h4>
        <ul>
          <li>收方账号（必填）</li>
          <li>收款账户名称（必填）</li>
          <li>金额（必填，必须大于0）</li>
          <li>币种（必填，必须为"人民币"）</li>
          <li>收款方开户行名称（必填）</li>
          <li>支付联行号（可选）</li>
          <li>附言（可选）</li>
          <li>开户网点名称（可选）</li>
          <li>备注（可选）</li>
        </ul>
      </div>

      <div class="btns-container">
        <div class="next-step-btns">
          <el-button @click="handleClose">取消</el-button>
          <el-button
            type="primary"
            @click="submitUpload"
            :loading="uploading"
            :disabled="!hasFile"
          >
            {{ uploading ? '导入中...' : '开始导入' }}
          </el-button>
        </div>
      </div>
    </div>

    <!-- 步骤2: 核对结果 -->
    <div v-if="active === 1" class="result-container">
      <!-- 统计信息卡片 -->
      <div class="summary-cards" v-if="importResult">
        <el-row :gutter="20" style="margin-bottom: 30px;">
          <el-col :span="4">
            <div class="summary-card">
              <div class="card-header">
                <i class="el-icon-document"></i>
                <span>总记录数</span>
              </div>
              <div class="card-value">{{ importResult.totalCount || 0 }}</div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="summary-card success">
              <div class="card-header">
                <i class="el-icon-success"></i>
                <span>匹配成功</span>
              </div>
              <div class="card-value">{{ importResult.matchedCount || 0 }}</div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="summary-card error">
              <div class="card-header">
                <i class="el-icon-error"></i>
                <span>匹配失败</span>
              </div>
              <div class="card-value">{{ importResult.unmatchedCount || 0 }}</div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="summary-card">
              <div class="card-header">
                <i class="el-icon-money"></i>
                <span>总金额</span>
              </div>
              <div class="card-value">{{ formatAmount(importResult.totalAmount) }}</div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="summary-card success">
              <div class="card-header">
                <i class="el-icon-check"></i>
                <span>匹配金额</span>
              </div>
              <div class="card-value">{{ formatAmount(importResult.matchedAmount) }}</div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="summary-card error">
              <div class="card-header">
                <i class="el-icon-close"></i>
                <span>未匹配金额</span>
              </div>
              <div class="card-value">{{ formatAmount(importResult.unmatchedAmount) }}</div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 匹配成功的记录 -->
      <div class="matched-section" style="margin-bottom: 30px;">
        <div class="section-header">
          <h3>
            <i class="el-icon-success" style="color: #67C23A;"></i>
            匹配成功记录 ({{ (importResult && importResult.matchedRecords) ? importResult.matchedRecords.length : 0 }})
          </h3>
        </div>
        <div v-if="importResult && importResult.matchedRecords && importResult.matchedRecords.length > 0">
          <el-table
            :data="importResult.matchedRecords"
            style="width: 100%;"
            size="small"
            stripe
            border
          >
            <el-table-column type="index" width="60" label="序号" align="center"></el-table-column>
            <el-table-column prop="excelRecord" label="Excel记录" min-width="300" show-overflow-tooltip></el-table-column>
            <el-table-column prop="transactionInfo" label="匹配的转出申请" min-width="300" show-overflow-tooltip></el-table-column>
            <el-table-column label="状态" width="100" align="center">
              <template slot-scope="scope">
                <el-tag type="success" size="small">匹配成功</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120" align="center">
              <template slot-scope="scope">
                <el-button
                  v-if="scope.row.paymentStatus !== 1"
                  type="text"
                  icon="el-icon-money"
                  size="small"
                  @click="confirmSinglePayment(scope.row)"
                  style="color: #67C23A;"
                >
                  确认打款
                </el-button>
                <el-tag v-else type="success" size="small">已打款</el-tag>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div v-else class="empty-data">
          <el-empty description="暂无匹配成功的记录" :image-size="80"></el-empty>
        </div>
      </div>

      <!-- 匹配失败的记录 -->
      <div class="unmatched-section">
        <div class="section-header">
          <h3>
            <i class="el-icon-warning" style="color: #E6A23C;"></i>
            匹配失败记录 ({{ (importResult && importResult.unmatchedRecords) ? importResult.unmatchedRecords.length : 0 }})
          </h3>
        </div>
        <div v-if="importResult && importResult.unmatchedRecords && importResult.unmatchedRecords.length > 0">
          <el-table
            :data="importResult.unmatchedRecords"
            style="width: 100%;"
            size="small"
            stripe
            border
          >
            <el-table-column type="index" width="60" label="序号" align="center"></el-table-column>
            <el-table-column prop="excelRecord" label="Excel记录" min-width="400" show-overflow-tooltip></el-table-column>
            <el-table-column prop="reason" label="失败原因" min-width="250" show-overflow-tooltip></el-table-column>
            <el-table-column label="状态" width="100" align="center">
              <template slot-scope="scope">
                <el-tag type="danger" size="small">匹配失败</el-tag>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div v-else class="empty-data">
          <el-empty description="暂无匹配失败的记录" :image-size="80"></el-empty>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="btns-container">
        <div class="next-step-btns">
          <el-button @click="nextStep(-1)" size="small">上一步</el-button>
          <el-button type="primary" size="small" @click="nextStep(1)">下一步</el-button>
        </div>
      </div>
    </div>

    <!-- 步骤3: 确认打款 -->
    <div v-if="active === 2" class="payment-confirmation-container">
      <div class="confirmation-header">
        <h3><i class="el-icon-money" style="color: #67C23A;"></i> 确认打款</h3>
        <p>请确认以下匹配成功的记录是否已完成打款操作</p>
      </div>

      <!-- 匹配成功记录的确认打款列表 -->
      <div v-if="importResult && importResult.matchedRecords && importResult.matchedRecords.length > 0">
        <el-table
          :data="importResult.matchedRecords"
          style="width: 100%;"
          size="small"
          stripe
          border
        >
          <el-table-column type="index" width="60" label="序号" align="center"></el-table-column>
          <el-table-column prop="excelRecord" label="Excel记录" min-width="300" show-overflow-tooltip></el-table-column>
          <el-table-column prop="transactionInfo" label="匹配的转出申请" min-width="300" show-overflow-tooltip></el-table-column>
          <el-table-column label="打款状态" width="120" align="center">
            <template slot-scope="scope">
              <el-tag v-if="scope.row.paymentStatus === 1" type="success" size="small">已打款</el-tag>
              <el-tag v-else type="warning" size="small">未打款</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120" align="center">
            <template slot-scope="scope">
              <el-button
                v-if="scope.row.paymentStatus !== 1"
                type="text"
                icon="el-icon-money"
                size="small"
                @click="confirmSinglePayment(scope.row)"
                style="color: #67C23A;"
              >
                确认打款
              </el-button>
              <span v-else style="color: #67C23A;">✓ 已确认</span>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div v-else class="empty-data">
        <el-empty description="暂无需要确认打款的记录" :image-size="80"></el-empty>
      </div>

      <!-- 操作按钮 -->
      <div class="btns-container">
        <div class="next-step-btns">
          <el-button @click="nextStep(-1)" size="small">上一步</el-button>
          <el-button
            type="primary"
            size="small"
            icon="el-icon-money"
            @click="confirmAllPayments"
            :disabled="allPaymentsConfirmed"
          >
            {{ allPaymentsConfirmed ? '所有记录已确认打款' : '批量确认打款' }}
          </el-button>
        </div>
      </div>
    </div>

    <!-- 步骤4: 完成 -->
    <div v-if="active === 3">
      <el-result icon="success" title="账单导入完成" subTitle="账单核对和打款确认已完成">
        <template slot="extra">
          <el-button type="primary" size="medium" @click="handleClose">返回转出管理列表</el-button>
        </template>
      </el-result>
    </div>
  </el-dialog>
</template>

<script>
import { importBill, update, getDetail } from "@/api/ni/fund/fundTransactionRecords";

export default {
  data() {
    return {
      formDialogVisible: false,
      active: 0,
      uploading: false,
      fileList: [],
      currentFile: null,
      importResult: null, // 存储FundTransferBillImportResultVO数据
    };
  },

  computed: {
    hasFile() {
      return this.currentFile !== null;
    },

    // 检查是否所有匹配成功的记录都已确认打款
    allPaymentsConfirmed() {
      if (!this.importResult || !this.importResult.matchedRecords || this.importResult.matchedRecords.length === 0) {
        return true;
      }
      return this.importResult.matchedRecords.every(record => record.paymentStatus === 1);
    },
  },

  methods: {
    init() {
      this.formDialogVisible = true;
      this.active = 0;
      this.uploading = false;
      this.fileList = [];
      this.currentFile = null;
      this.importResult = null;
    },

    handleClose() {
      this.formDialogVisible = false;
      this.$emit("refresh");
    },

    // 文件上传前的钩子
    beforeUpload(file) {
      // 检查文件类型
      const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      if (!isExcel) {
        this.$message.error('只能上传Excel文件(.xlsx)！');
        return false;
      }

      // 检查文件大小
      const isLt10M = file.size / 1024 / 1024 < 10;
      if (!isLt10M) {
        this.$message.error('上传文件大小不能超过10MB！');
        return false;
      }

      return false; // 阻止自动上传
    },

    // 文件状态改变时的钩子
    handleFileChange(file, fileList) {
      this.fileList = fileList;
      this.currentFile = file.raw;
    },

    // 文件移除时的钩子
    handleFileRemove(file, fileList) {
      this.fileList = fileList;
      this.currentFile = null;
    },

    // 提交上传
    async submitUpload() {
      if (!this.currentFile) {
        this.$message.warning('请先选择要上传的文件！');
        return;
      }

      this.uploading = true;
      try {
        const response = await importBill(this.currentFile);

        // 输出原始响应数据
        console.log('=== 原始响应数据 ===');
        console.log('response:', response);
        console.log('response.data:', response.data);
        console.log('response.data类型:', typeof response.data);

        // 正确获取后端返回的FundTransferBillImportResultVO数据
        // 后端返回格式: {code: 200, success: true, data: {...}}
        // 所以需要使用 response.data.data 获取真正的业务数据
        this.importResult = response.data.data;

        // 检查数据结构
        this.checkDataStructure(this.importResult);

        // 数据验证和修复
        if (!this.importResult) {
          console.error('importResult为空，使用默认值');
          this.importResult = {
            totalCount: 0,
            matchedCount: 0,
            unmatchedCount: 0,
            totalAmount: 0,
            matchedAmount: 0,
            unmatchedAmount: 0,
            matchedRecords: [],
            unmatchedRecords: []
          };
        }

        // 确保数组字段存在
        if (!this.importResult.matchedRecords) {
          this.importResult.matchedRecords = [];
        }
        if (!this.importResult.unmatchedRecords) {
          this.importResult.unmatchedRecords = [];
        }

        // 输出详细调试信息
        console.log('=== 导入结果详情 ===');
        console.log('总记录数:', this.importResult.totalCount);
        console.log('匹配成功:', this.importResult.matchedCount);
        console.log('匹配失败:', this.importResult.unmatchedCount);
        console.log('总金额:', this.importResult.totalAmount);
        console.log('匹配金额:', this.importResult.matchedAmount);
        console.log('未匹配金额:', this.importResult.unmatchedAmount);
        console.log('=== 匹配成功记录详情 ===');
        if (this.importResult.matchedRecords && this.importResult.matchedRecords.length > 0) {
          this.importResult.matchedRecords.forEach((record, index) => {
            console.log(`匹配成功记录 ${index + 1}:`, record);
            console.log(`记录 ${index + 1} 的所有字段:`, Object.keys(record));
            console.log(`记录 ${index + 1} 是否有transactionId:`, !!record.transactionId);
            console.log(`记录 ${index + 1} 是否有transactionRecord:`, !!record.transactionRecord);
            console.log(`记录 ${index + 1} 的paymentStatus:`, record.paymentStatus);
          });
        } else {
          console.log('无匹配成功记录');
        }
        console.log('=== 匹配失败记录详情 ===');
        if (this.importResult.unmatchedRecords && this.importResult.unmatchedRecords.length > 0) {
          this.importResult.unmatchedRecords.forEach((record, index) => {
            console.log(`匹配失败记录 ${index + 1}:`, {
              Excel记录: record.excelRecord,
              失败原因: record.reason
            });
          });
        } else {
          console.log('无匹配失败记录');
        }
        console.log('=== 完整返回数据 ===');
        console.log(JSON.stringify(this.importResult, null, 2));

        this.$message.success('导入完成！');
        this.nextStep(1);
      } catch (error) {
        console.error('导入失败:', error);
        const errorMsg = error.response && error.response.data && error.response.data.msg
          ? error.response.data.msg
          : error.message;
        this.$message.error('导入失败：' + errorMsg);
      } finally {
        this.uploading = false;
      }
    },

    // 下一步/上一步
    nextStep(flag) {
      this.active += flag;
    },

    // 格式化金额显示
    formatAmount(amount) {
      if (!amount) return '¥0.00';
      return `¥${Number(amount).toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      })}`;
    },

    // 检查数据结构完整性
    checkDataStructure(data) {
      console.log('=== 数据结构检查 ===');
      console.log('data是否存在:', !!data);
      if (data) {
        console.log('totalCount:', data.totalCount, typeof data.totalCount);
        console.log('matchedCount:', data.matchedCount, typeof data.matchedCount);
        console.log('unmatchedCount:', data.unmatchedCount, typeof data.unmatchedCount);
        console.log('totalAmount:', data.totalAmount, typeof data.totalAmount);
        console.log('matchedAmount:', data.matchedAmount, typeof data.matchedAmount);
        console.log('unmatchedAmount:', data.unmatchedAmount, typeof data.unmatchedAmount);
        console.log('matchedRecords是否为数组:', Array.isArray(data.matchedRecords));
        console.log('unmatchedRecords是否为数组:', Array.isArray(data.unmatchedRecords));
        if (data.matchedRecords) {
          console.log('matchedRecords长度:', data.matchedRecords.length);
        }
        if (data.unmatchedRecords) {
          console.log('unmatchedRecords长度:', data.unmatchedRecords.length);
        }
      }
    },

    /**
     * 确认单条记录打款
     * @param {Object} record - 匹配成功的记录
     */
    async confirmSinglePayment(record) {
      console.log('=== 确认打款调试信息 ===');
      console.log('record:', record);
      console.log('record的所有字段:', Object.keys(record));

      try {
        await this.$confirm(
          "请确认已完成了打款?",
          "确认打款提示",
          {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }
        );

        // 方案1: 检查是否有完整的交易记录对象
        if (record.transactionRecord) {
          console.log('使用transactionRecord进行更新');
          const transactionRecord = record.transactionRecord;
          transactionRecord.paymentStatus = 1;  // 设置为已打款
          transactionRecord.status = 1;         // 设置交易状态为交易完成

          await update(transactionRecord);
          // 使用Vue.set确保响应式更新
          this.$set(record, 'paymentStatus', 1);
          this.$alert('确认打款成功！', '操作成功', {
            confirmButtonText: '确定',
            type: 'success'
          });
        }
        // 方案2: 检查是否有transactionId字段，先获取完整记录再更新
        else if (record.transactionId || record.id) {
          console.log('使用transactionId获取完整记录并更新');
          const id = record.transactionId || record.id;

          // 先获取完整的交易记录
          const detailResponse = await getDetail(id);
          const transactionRecord = detailResponse.data.data;

          // 更新状态
          transactionRecord.paymentStatus = 1;  // 设置为已打款
          transactionRecord.status = 1;         // 设置交易状态为交易完成

          // 保存更新
          await update(transactionRecord);
          // 使用Vue.set确保响应式更新
          this.$set(record, 'paymentStatus', 1);
          this.$alert('确认打款成功！', '操作成功', {
            confirmButtonText: '确定',
            type: 'success'
          });
        }
        // 方案3: 尝试从transactionInfo中解析ID
        else if (record.transactionInfo && typeof record.transactionInfo === 'string') {
          console.log('尝试从transactionInfo中解析ID');
          // 尝试从transactionInfo字符串中提取ID（如果后端在字符串中包含了ID信息）
          const idMatch = record.transactionInfo.match(/ID[：:]\s*(\d+)/);
          if (idMatch) {
            const transactionId = idMatch[1];

            // 先获取完整的交易记录
            const detailResponse = await getDetail(transactionId);
            const transactionRecord = detailResponse.data.data;

            // 更新状态
            transactionRecord.paymentStatus = 1;  // 设置为已打款
            transactionRecord.status = 1;         // 设置交易状态为交易完成

            // 保存更新
            await update(transactionRecord);
            // 使用Vue.set确保响应式更新
            this.$set(record, 'paymentStatus', 1);
            this.$alert('确认打款成功！', '操作成功', {
              confirmButtonText: '确定',
              type: 'success'
            });
          } else {
            this.$message.error('无法从交易信息中提取ID，请联系管理员');
          }
        }
        // 方案4: 如果都没有，只更新本地状态（临时方案）
        else {
          console.log('没有找到交易ID，只更新本地状态');
          // 使用Vue.set确保响应式更新
          this.$set(record, 'paymentStatus', 1);
          this.$alert('本地状态已更新为已打款！\n注意：仅更新了本地显示状态，请确保后端数据同步', '操作成功', {
            confirmButtonText: '确定',
            type: 'success'
          });
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('确认打款失败:', error);
          this.$message.error('确认打款失败，请重试');
        }
      }
    },

    /**
     * 批量确认所有匹配成功记录的打款
     */
    async confirmAllPayments() {
      const unpaidRecords = this.importResult.matchedRecords.filter(record => record.paymentStatus !== 1);

      // 如果所有记录都已确认打款，直接跳转到完成步骤
      if (unpaidRecords.length === 0) {
        this.$alert('所有记录都已确认打款，导入完成！', '操作成功', {
          confirmButtonText: '确定',
          type: 'success'
        }).then(() => {
          // 跳转到完成步骤
          this.nextStep(1);
        });
        return;
      }

      try {
        await this.$alert(
          "确定将匹配成功的账单设置为已打款吗？",
          "批量确认打款提示",
          {
            confirmButtonText: "确定",
            type: "warning",
          }
        );

        // 批量处理确认打款
        const promises = unpaidRecords.map(async (record) => {
          // 方案1: 使用完整的交易记录进行更新
          if (record.transactionRecord) {
            const transactionRecord = record.transactionRecord;
            transactionRecord.paymentStatus = 1;  // 设置为已打款
            transactionRecord.status = 1;         // 设置交易状态为交易完成

            await update(transactionRecord);
            this.$set(record, 'paymentStatus', 1);
          }
          // 方案2: 使用transactionId获取完整记录并更新
          else if (record.transactionId || record.id) {
            const id = record.transactionId || record.id;

            // 先获取完整的交易记录
            const detailResponse = await getDetail(id);
            const transactionRecord = detailResponse.data.data;

            // 更新状态
            transactionRecord.paymentStatus = 1;  // 设置为已打款
            transactionRecord.status = 1;         // 设置交易状态为交易完成

            // 保存更新
            await update(transactionRecord);
            this.$set(record, 'paymentStatus', 1);
          }
          // 方案3: 尝试从transactionInfo中解析ID
          else if (record.transactionInfo && typeof record.transactionInfo === 'string') {
            const idMatch = record.transactionInfo.match(/ID[：:]\s*(\d+)/);
            if (idMatch) {
              const transactionId = idMatch[1];

              // 先获取完整的交易记录
              const detailResponse = await getDetail(transactionId);
              const transactionRecord = detailResponse.data.data;

              // 更新状态
              transactionRecord.paymentStatus = 1;  // 设置为已打款
              transactionRecord.status = 1;         // 设置交易状态为交易完成

              // 保存更新
              await update(transactionRecord);
              this.$set(record, 'paymentStatus', 1);
            } else {
              this.$set(record, 'paymentStatus', 1);
            }
          }
          // 方案4: 只更新本地状态
          else {
            this.$set(record, 'paymentStatus', 1);
          }
        });

        await Promise.all(promises);
        this.$alert('批量确认打款成功！', '操作成功', {
          confirmButtonText: '确定',
          type: 'success'
        }).then(() => {
          // 批量确认完成后跳转到下一步
          this.nextStep(1);
        });
      } catch (error) {
        if (error !== 'cancel') {
          console.error('批量确认打款失败:', error);
          this.$message.error('部分记录确认打款失败，请重试');
        }
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.dialog-header {
  display: flex;
  padding: 0 20px 20px 20px;
  .dialog-title {
    font-size: 20px;
    font-weight: 500;
    color: #303133;
  }
}

.dialog-steps {
  padding: 0 35px 35px 35px;
}

::v-deep .el-dialog__body {
  padding: 0 45px !important;
}

.upload-container {
  padding: 0 50px;
  height: calc(100vh - 293px);
  overflow-y: auto;
}

.upload-section {
  margin-bottom: 30px;

  .upload-demo {
    width: 100%;

    ::v-deep .el-upload {
      width: 100%;

      .el-upload-dragger {
        width: 100%;
        height: 180px;
        border: 2px dashed #d9d9d9;
        border-radius: 6px;
        cursor: pointer;
        position: relative;
        overflow: hidden;
        transition: border-color 0.2s;

        &:hover {
          border-color: #409EFF;
        }

        .el-icon-upload {
          font-size: 67px;
          color: #c0c4cc;
          margin: 40px 0 16px;
          line-height: 50px;
        }

        .el-upload__text {
          color: #606266;
          font-size: 14px;
          text-align: center;

          em {
            color: #409EFF;
            font-style: normal;
          }
        }
      }

      .el-upload__tip {
        font-size: 12px;
        color: #606266;
        margin-top: 7px;
      }
    }
  }
}

.upload-tips {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 20px;
  margin-bottom: 30px;

  h4 {
    margin: 0 0 15px 0;
    font-size: 16px;
    color: #495057;
  }

  ul {
    margin: 0;
    padding-left: 20px;

    li {
      margin-bottom: 8px;
      color: #6c757d;
      font-size: 14px;
    }
  }
}

.result-container {
  padding: 0 50px;
  height: calc(100vh - 293px);
  overflow-y: auto;
}

.summary-cards {
  .summary-card {
    background: #fff;
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    transition: all 0.3s;

    &:hover {
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    }

    .card-header {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 12px;

      i {
        font-size: 18px;
        margin-right: 8px;
        color: #909399;
      }

      span {
        font-size: 14px;
        color: #606266;
      }
    }

    .card-value {
      font-size: 24px;
      font-weight: bold;
      color: #303133;
    }

    &.success {
      border-color: #67C23A;
      background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%);

      .card-header i {
        color: #67C23A;
      }

      .card-value {
        color: #67C23A;
      }
    }

    &.error {
      border-color: #F56C6C;
      background: linear-gradient(135deg, #fff5f5 0%, #fef0f0 100%);

      .card-header i {
        color: #F56C6C;
      }

      .card-value {
        color: #F56C6C;
      }
    }
  }
}

.section-header {
  margin-bottom: 15px;

  h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
    color: #303133;

    i {
      margin-right: 8px;
    }
  }
}

.btns-container {
  display: flex;
  justify-content: center;
  margin-top: 30px;

  .next-step-btns {
    display: flex;
    gap: 15px;
  }
}

// 表格样式优化
::v-deep .el-table {
  border-radius: 6px;
  overflow: hidden;

  .el-table__header {
    th {
      background: #f8f9fa !important;
      color: #495057;
      font-weight: 500;
      border-bottom: 1px solid #dee2e6;
    }
  }

  .el-table__body {
    tr {
      &:hover {
        background: #f8f9fa;
      }
    }

    td {
      border-bottom: 1px solid #f1f3f4;
    }
  }
}

// 标签样式优化
::v-deep .el-tag {
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;

  &.el-tag--success {
    background: #f0f9ff;
    border-color: #b3d8ff;
    color: #0d84ff;
  }

  &.el-tag--danger {
    background: #fff2f0;
    border-color: #ffb3b3;
    color: #ff4d4f;
  }
}

.empty-data {
  padding: 40px 0;
  text-align: center;
  background: #fafafa;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  color: #999;
}

.payment-confirmation-container {
  padding: 20px;
}

.confirmation-header {
  margin-bottom: 20px;
  text-align: center;
}

.confirmation-header h3 {
  margin: 0 0 10px 0;
  font-size: 18px;
  color: #303133;
}

.confirmation-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.batch-payment-section {
  text-align: center;
  padding: 20px 0;
  border-top: 1px solid #ebeef5;
}
</style>

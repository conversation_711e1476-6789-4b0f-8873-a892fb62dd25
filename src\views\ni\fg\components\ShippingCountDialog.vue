<script>
import { mapGetters } from "vuex";
import { count } from "@/api/ni/fg/shipping";

export default {
  name: "ShippingCountDialog",
  data() {
    return {
      visible: false,
      loading: false,
      option: {
        showSummary: true,
        sumColumnList: [
          {
            name: "num",
            type: "sum",
          },
          {
            name: "weight",
            type: "sum",
          },
          {
            name: "outboundNum",
            type: "sum",
          },
        ],
        header: false,
        menu: false,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        span: 12,
        dialogDrag: true,
        size: "mini",
        searchSize: "mini",
        align: "center",
        searchIndex: 2,
        searchIcon: true,
        searchEnter: true,
        calcHeight: 30,
        tip: false,
        searchShow: false,
        border: true,
        selection: false,
        dialogClickModal: false,
        column: [],
      },
      column: [
        {
          label: "存货编码",
          prop: "materialCode",
          overHidden: true,
          sortable: true,
          filters: true,
          minWidth: 110,
        },
        {
          label: "规格",
          prop: "specText",
          overHidden: true,
          sortable: true,
          filters: true,
          minWidth: 90,
        },
        {
          label: "外包装",
          prop: "packageText",
          overHidden: true,
          sortable: true,
          filters: true,
          minWidth: 110,
        },
        {
          label: "内包装",
          prop: "innerPackageText",
          overHidden: true,
          sortable: true,
          filters: true,
          minWidth: 90,
        },
        {
          label: "质量",
          prop: "qualityLevel",
          type: "select",
          dicUrl:
            "/api/blade-system/dict-biz/dictionary?code=ni_product_sku_quality_level",
          props: {
            label: "dictValue",
            value: "dictKey",
            desc: "dictKey",
          },
          placeholder: " ",
          sortable: true,
          filters: true,
          minWidth: 75,
        },
        {
          label: "数量",
          prop: "num",
          type: "number",
          width: 90,
        },
        {
          label: "重量",
          prop: "weight",
          type: "number",
          overHidden: true,
          width: 100,
        },
        {
          label: "出库数量",
          prop: "outboundNum",
          type: "number",
          minWidth: 110,
        },
        {
          label: "库存数量",
          prop: "currentStock",
          type: "number",
          overHidden: true,
          hide: false,
          minWidth: 110,
        },
      ],
      data: [],
      form: {},
      params: {},
      depotId: "all",
      depotDict: [],
    };
  },
  created() {
    this.loadDepotDict();
  },
  computed: {
    ...mapGetters(["userInfo"]),
  },
  methods: {
    loadDepotDict() {
      this.$http
        .get("/api/ni/base/depot/info/list?status=2&type=1")
        .then((res) => {
          this.depotDict = [
            {
              id: "all",
              name: "全部",
            },
          ];
          res.data.data.forEach((item) => {
            this.depotDict.push(item);
          });
        });
    },
    async onShow(params) {
      const res = await this.$http.get(
        "/api/ni/base/depot/info/list?status=2&type=1"
      );
      this.depotDict = [];
      this.option.column = [...this.column];
      res.data.data.forEach((item) => {
        this.depotDict.push(item);
        this.option.column.push({
          label: item.name,
          prop: item.id,
          type: "number",
          minWidth: 90,
        });
      });
      console.log(this.option.column);
      this.params = params;
      this.data = [];
      this.visible = true;
    },
    cellStyle({ row, column }) {
      if (
        "outboundNum" === column.columnKey &&
        Number(row.outboundNum) > Number(row.num)
      ) {
        return {
          background: "#F56C6C",
          color: "#fff",
        };
      } else if (
        "outboundNum" === column.columnKey &&
        Number(row.outboundNum) === Number(row.num)
      ) {
        return {
          background: "#409EFF",
          color: "#fff",
        };
      }
    },
    onLoad() {
      this.loading = true;
      count(this.params).then((res) => {
        const data = res.data.data;
        data.forEach((row) => {
          if (row.outboundList && row.outboundList.length > 0) {
            row.outboundList.forEach((outbound) => {
              row[outbound.depotId] = outbound.num;
            });
          }
        });
        this.data = data;
        this.loading = false;
      });
    },
  },
};
</script>

<template>
  <el-dialog
    :visible.sync="visible"
    append-to-body
    title="发货统计"
    width="1100px"
  >
    <avue-crud
      ref="crud"
      v-if="visible"
      :table-loading="loading"
      :option="option"
      :data="data"
      v-model="form"
      @on-load="onLoad"
      :cell-style="cellStyle"
    >
    </avue-crud>
  </el-dialog>
</template>

<style scoped></style>

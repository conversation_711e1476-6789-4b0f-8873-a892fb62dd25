import request from "@/router/axios";

export const getList = (current, size, params) => {
  return request({
    url: "/api/ni/pa/group/page",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/ni/pa/group/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const remove = (ids) => {
  return request({
    url: "/api/ni/pa/group/remove",
    method: "post",
    params: {
      ids,
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/ni/pa/group/submit",
    method: "post",
    data: row,
  });
};

export const update = (row) => {
  return request({
    url: "/api/ni/pa/group/submit",
    method: "post",
    data: row,
  });
};

export const linkPerson = (params) => {
  return request({
    url: "/api/ni/pa/group/linkPerson",
    method: "post",
    params,
  });
};

export const personList = (groupId) => {
  return request({
    url: "/api/ni/pa/group/personList",
    method: "get",
    params: {
      groupId,
    },
  });
};

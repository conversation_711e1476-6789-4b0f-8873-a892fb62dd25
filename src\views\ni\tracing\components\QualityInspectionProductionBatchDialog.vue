<template>
  <el-dialog
    title="关联批次"
    v-dialogdrag
    :visible.sync="visible"
    append-to-body
    width="1200px"
  >
    <div>
      <avue-crud
        v-if="visible"
        :option="option"
        :table-loading="loading"
        :data="data"
        :page.sync="page"
        :search.sync="query"
        :before-open="beforeOpen"
        v-model="form"
        ref="crud"
        @row-del="rowDel"
        @search-change="searchChange"
        @search-reset="searchReset"
        @selection-change="selectionChange"
        @current-change="currentChange"
        @size-change="sizeChange"
        @refresh-change="refreshChange"
        @on-load="onLoad"
        :cell-style="cellStyle"
      >
      <template #batchCode="{ row, index }">
        <span>{{ row.displayBatchCode }}</span>
        <span
          v-if="row.batchCode && row.displayBatchCode !== row.batchCode"
          style="color: #f56c6c"
        >
          ({{ row.batchCode }})
        </span>
      </template>
        <template #menuLeft>
          <el-button type="primary" size="mini" @click="handleAdd" v-if="row"
            >关联批次
          </el-button>
          <el-button type="warning" size="mini" @click="handleRemoveBatch"
            >移除批次
          </el-button>
        </template>
        <template #menu="{ row, index }">
          <el-button type="text" size="mini" @click="rowRemove(row)"
            >移除
          </el-button>
        </template>
      </avue-crud>
    </div>
    <product-batch-select-dialog
      ref="productionBatchSelectDialog"
      @confirm="handleLinkBatchConfirm"
    />
  </el-dialog>
</template>
<script>
import {
  getDetail,
  getList,
  remove,
  unLinkInspectionId,
  linkInspectionId,
  update,
} from "@/api/ni/tracing/productionBatch";
import ProductBatchSelectDialog from "@/views/ni/tracing/components/ProductBatchSelectDialog.vue";
import { mapGetters } from "vuex";

export default {
  components: { ProductBatchSelectDialog },
  data() {
    return {
      row: null,
      visible: false,
      loading: false,
      data: [],
      page: {
        current: 1,
        size: 10,
        total: 0,
      },
      option: {
        addBtn: false,
        menuWidth: 150,
        align: "center",
        size: "mini",
        searchSize: "mini",
        calcHeight: 30,
        tip: false,
        searchIcon: true,
        searchShow: true,
        indexLabel: "序号",
        searchMenuSpan: 6,
        searchIndex: 3,
        border: true,
        index: false,
        editBtn: false,
        delBtn: false,
        selection: true,
        dialogClickModal: false,
        showSummary: true,
        sumColumnList: [
          {
            name: "planBoxQuantity",
            type: "sum",
            decimals: 0,
          },
          {
            name: "actualBoxQuantity",
            type: "sum",
            decimals: 0,
          },
          {
            name: "actualTotalCapacity",
            type: "sum",
            decimals: 2,
          },
        ],
        column: [
          {
            label: "批次时间",
            prop: "batchEndTime",
            type: "datetime",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
            search: true,
            searchRange: true,
            searchOrder: 40,
            width: 140,
            rules: [
              {
                required: true,
                message: "批次时间为必填项",
                trigger: ["blur", "change"],
              },
            ],
            html: true,
          },
          {
            label: "批次号",
            prop: "batchCode",
            type: "input",
            width: 120,
            overHidden: true,
            addDisplay: false,
          },
          {
            label: "起始批次",
            prop: "startBatchCode",
            type: "input",
            hide: true,
            disabled: true,
            width: 60,
            search: true,
            viewDisplay: false,
          },
          {
            label: "终止批次",
            prop: "endBatchCode",
            type: "input",
            hide: true,
            disabled: true,
            width: 60,
            search: true,
            viewDisplay: false,
          },
          {
            label: "发货编号",
            prop: "shipCode",
            type: "input",
            search: true,
            width: 180,
            hide: true,
            editDisplay: false,
          },
          {
            label: "备注",
            prop: "remark",
            type: "input",
            width: 180,
            hide: true,
            viewDisplay: false,
            editDisplay: false,
          },
          {
            label: "质检报告",
            prop: "inspectionCode",
            width: 110,
            overHidden: true,
            display: false,
          },
          {
            label: "国内/外",
            prop: "area",
            type: "select",
            width: 70,
            addDisplay: false,
            dicData: [
              {
                label: "国内",
                value: "CN",
              },
              {
                label: "国外",
                value: "OS",
              },
            ],
          },
          {
            label: "码垛号",
            prop: "palletNumber",
            type: "input",
            hide: true,
            width: 60,
            rules: [
              {
                required: true,
                message: "码垛号为必填项",
                trigger: ["blur", "change"],
              },
            ],
            html: true,
            addDisplay: false,
          },
          {
            label: "品牌",
            prop: "brandId",
            type: "select",
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_product_brand",
            props: {
              label: "dictValue",
              value: "id",
            },
            hide: true,
            showColumn: false,
            viewDisplay: false,
            allowCreate: true,
            filterable: true,
            overHidden: true,
            minWidth: 70,
            search: true,
            rules: [
              {
                required: true,
                message: "品牌为必填项",
                trigger: ["blur", "change"],
              },
            ],
            html: true,
          },
          {
            label: "品牌",
            prop: "brandName",
            type: "input",
            width: 70,
            disabled: true,
            editDisplay: false,
            addDisplay: false,
          },
          {
            label: "外包装",
            prop: "outerPackagingId",
            type: "select",
            dicUrl: `/api/ni/product/packaging/list?innerPark=0&current=1&size=20&status=1&&name={{key}}`,
            props: {
              label: "name",
              value: "id",
              desc: "type",
            },
            search: true,
            remote: true,
            viewDisplay: false,
            addDisplay: false,
            dicFormatter: (data) => {
              return data.data.records;
            },
            filterable: true,
            hide: true,
            showColumn: false,
          },
          {
            label: "外包装",
            prop: "outerPackagingId",
            type: "select",
            dicUrl: `/api/ni/product/packaging/list?innerPark=0&current=1&size=20&status=1&&name={{key}}&&type=D`,
            props: {
              label: "name",
              value: "id",
              desc: "type",
            },
            remote: true,
            viewDisplay: false,
            dicFormatter: (data) => {
              return data.data.records;
            },
            filterable: true,
            hide: true,
            showColumn: false,
            rules: [
              {
                required: true,
                message: "外包装为必填项",
                trigger: ["blur", "change"],
              },
            ],
            html: true,
          },
          {
            label: "外包装",
            prop: "outerPackagingName",
            type: "input",
            width: 110,
            overHidden: true,
            disabled: true,
            editDisplay: false,
            addDisplay: false,
            html: true,
            formatter: function (row, column, cellValue, index) {
              const displayValue = cellValue || column.outerPackagingName || "";
              if (row.sku) {
                return `<span style="color:green">${displayValue}</span>`;
              } else {
                return `<span style="color:red">${displayValue}</span>`;
              }
            },
          },
          {
            label: "内包装",
            prop: "innerPackagingId",
            type: "select",
            dicUrl:
              "/api/ni/product/packaging/list?innerPark=1&current=1&size=20&status=1&&name={{key}}",
            props: {
              label: "name",
              value: "id",
              desc: "type",
            },
            search: true,
            viewDisplay: false,
            dicFormatter: (data) => {
              return data.data.records;
            },
            filterable: true,
            change: ({ value }) => {
              if (value) {
                console.log("选择");
              }
            },
            hide: true,
            showColumn: false,
            addDisplay: false,
          },
          {
            label: "内包装",
            prop: "innerPackagingName",
            type: "input",
            width: 110,
            overHidden: true,
            disabled: true,
            editDisplay: false,
            addDisplay: false,
          },

          {
            label: "规格",
            prop: "specificationId",
            type: "select",
            dicUrl: "/api/ni/product/spec/list",
            props: {
              label: "name",
              value: "id",
              desc: "type",
            },
            hide: true,
            showColumn: false,
            viewDisplay: false,
            allowCreate: true,
            filterable: true,
            overHidden: true,
            minWidth: 120,
            search: true,
            rules: [
              {
                required: true,
                message: "规格为必填项",
                trigger: ["blur", "change"],
              },
            ],
            html: true,
          },
          {
            label: "规格",
            prop: "specificationName",
            type: "input",
            width: 90,
            overHidden: true,
            disabled: true,
            editDisplay: false,
            addDisplay: false,
          },
          {
            label: "质量",
            prop: "qualityId",
            type: "select",
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_product_sku_quality_level",
            props: {
              label: "dictValue",
              value: "id",
              desc: "type",
            },
            hide: true,
            showColumn: false,
            viewDisplay: false,
            allowCreate: true,
            filterable: true,
            overHidden: true,
            minWidth: 100,
            search: true,
            rules: [
              {
                required: true,
                message: "质量为必填项",
                trigger: ["blur", "change"],
              },
            ],
            html: true,
          },
          {
            label: "质量",
            prop: "qualityName",
            type: "input",
            width: 80,
            overHidden: true,
            disabled: true,
            editDisplay: false,
            addDisplay: false,
          },

          {
            label: "计划箱数",
            prop: "planBoxQuantity",
            type: "input",
            width: 90,
            addDisplay: false,
          },
          {
            label: "实际箱数",
            prop: "actualBoxQuantity",
            type: "input",
            width: 90,
            addDisplay: false,
          },
          {
            label: "实际总重量",
            prop: "actualTotalCapacity",
            type: "input",
            width: 90,
            addDisplay: false,
          },
          {
            label: "允许发货",
            prop: "allowDelivery",
            type: "select",
            value: true,
            width: 80,
            dicData: [
              {
                label: "允许",
                value: true,
              },
              {
                label: "不允许",
                value: false,
              },
            ],
            search: true,
            rules: [
              {
                required: true,
                message: "允许发货为必填项",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "二维码样式",
            prop: "qrCodeStyle",
            type: "select",
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_tracing_qr_code_style",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            width: 90,
            display: false,
          },
        ],
      },
      query: {},
      form: {},
      selectionList: [],
    };
  },
  computed: {
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  methods: {
    onShow(row) {
      this.row = row;
      this.visible = true;
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          console.log(error);
        }
      );
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen(done, type) {
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then((res) => {
          this.form = res.data.data;
        });
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.area = "all";
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      let para = Object.assign(params, this.query);
      para.inspectionId = this.row.id;
      getList(page.currentPage, page.pageSize, para).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        data.records.forEach((item) => {
          if (item.inspectionId == null) {
            item.inspectionCode = "未关联";
          } else {
            item.inspectionCode = item.inspectionCode
              ? item.inspectionCode
              : "无编号";
          }
        });
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
    handleAdd() {
      this.$refs.productionBatchSelectDialog.onSelect({ inspectionStatus: 0 });
    },
    handleRemoveBatch() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据移除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return unLinkInspectionId(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    handleLinkBatchConfirm(selectionList) {
      linkInspectionId(
        selectionList.map((item) => item.id).join(","),
        this.row.id
      ).then(() => {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!",
        });
      });
    },
    rowRemove(row) {
      this.$confirm("确定将选择数据移除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return unLinkInspectionId(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    cellStyle({ row, column }) {
      if ("batchCode" === column.columnKey && row.isInout) {
        return {
          backgroundColor: "#909399",
          color: "#fff",
        };
      }
      if ("inspectionCode" === column.columnKey && row.publishStatus === "0") {
        return {
          backgroundColor: "#F56C6C",
          color: "#fff",
        };
      } else if ("inspectionCode" === column.columnKey) {
        return {
          backgroundColor: "#67C23A",
          color: "#fff",
        };
      }
    },
  },
};
</script>

<style scoped></style>

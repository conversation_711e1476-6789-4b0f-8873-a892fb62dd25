import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/ni/ehs/ehsSpecialEquipment/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/ni/ehs/ehsSpecialEquipment/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/ni/ehs/ehsSpecialEquipment/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/ni/ehs/ehsSpecialEquipment/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/ni/ehs/ehsSpecialEquipment/update',
    method: 'post',
    data: row
  })
}

export const save = (row) => {
  return request({
    url: '/api/ni/ehs/ehsSpecialEquipment/safetyUser/save',
    method: 'post',
    data: row
  })
}

export const getSafetyUserDetail = () => {
  return request({
    url: '/api/ni/ehs/ehsSpecialEquipment/safetyUser/detail',
    method: 'get',
  })
}

export const getSafetyUserType = () => {
  return request({
    url: '/api/ni/ehs/ehsSpecialEquipment/getSafetyUserType',
    method: 'get',
  })
}

export const startWeek = (row) => {
  return request({
    url: '/api/ni/ehs/ehsSpecialEquipment/startWeek',
    method: 'post',
    data: row,
  })
}

export const startMonth = (row) => {
  return request({
    url: '/api/ni/ehs/ehsSpecialEquipment/startMonth',
    method: 'post',
    data: row,
  })
 }

 export const getStartMeetingData = (isWeek) => {
  return request({
    url: '/api/ni/ehs/ehsSpecialEquipment/getStartMeetingData',
    method: 'get',
    params: {
      isWeek
    }
  })
 }
 
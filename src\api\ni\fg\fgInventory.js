import request from '@/router/axios';


export const getSummaryByDepotSkuList = (current, size, params) => {
  return request({
    url: '/api/ni/fg/inventory/summaryByDepotSkuList',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getList = (current, size, params) => {
  return request({
    url: '/api/ni/fg/inventory/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/ni/fg/inventory/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const changeRemark = (ids, remark) => {
  return request({
    url: '/api/ni/fg/inventory/changeRemark',
    method: 'post',
    params: {
      ids,
      remark
    }
  })
}
export const getBeforeProductionDate = (depotId, skuId, productionDate) => {
  return request({
    url: '/api/ni/fg/inventory/getBeforeProductionDate',
    method: 'post',
    params: {
      depotId, skuId, productionDate
    }
  })
}

export const getBeforeBatchNo = (depotId, ids) => {
  return request({
    url: '/api/ni/fg/inventory/getBeforeBatchNo',
    method: 'post',
    params: {
      depotId,
      ids
    }
  })
}

export const freezeCancel = (ids) => {
  return request({
    url: '/api/ni/fg/inventory/freezeCancel',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const sumNum = (depotId, skuId) => {
  return request({
    url: '/api/ni/fg/inventory/sumNum',
    method: 'get',
    params: {
      depotId,
      skuId
    }
  })
}
export const remove = (ids) => {
  return request({
    url: "/api/ni/fg/inventory/remove",
    method: "post",
    params: {
      ids,
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/ni/fg/inventory/save",
    method: "post",
    data: row,
  });
};

export const update = (row) => {
  return request({
    url: "/api/ni/fg/inventory/update",
    method: "post",
    data: row,
  });
};

<template>
  <avue-crud
    :option="option"
    :page.sync="page"
    :data="data"
    v-model="form"
    ref="crud"
    @selection-change="selectionChange"
    @cell-click="itemCellClickChange"
    @current-change="currentChange"
    @size-change="sizeChange"
    @cell-mouse-enter="(row) => (row.cell = true)"
    @cell-mouse-leave="(row) => (row.cell = false)"
    :upload-preview="handleUploadPreview"
    @on-load="onLoad"
  >
    <template #index="{ row, column, index }">
      <el-button
        v-if="row.cell"
        type="danger"
        size="mini"
        icon="el-icon-delete"
        circle
        @click="handleDel(row, index)"
      ></el-button>
      <span v-else>{{ index + 1 }}</span>
    </template>
    <template #cost="{ row, index }">
      <el-tag size="mini" type="danger" effect="dark" v-if="row.cost">
        费用
      </el-tag>
      <el-tag size="mini" type="info" effect="plain" v-else> 实物</el-tag>
    </template>
    <template #rowNo="{ row, index }">
      <span v-if="row.rowNo">{{ row.rowNo }}</span>
      <span v-else>
        {{ row.row }}
        <template v-if="row.no">-{{ row.no }}</template>
      </span>
    </template>
    <template #materialCode="{ row, size, index }">
      <span v-if="row.materialCode">
        {{ row.materialCode }}]{{ row.materialName1 }}
      </span>
    </template>
    <template #num="{ row, index }">
      <span
        :style="{
          color: row.num < 0 ? 'red' : '',
          fontWeight: row.num < 0 ? 'bold' : '',
        }"
      >
        {{
          Number(row.num).toLocaleString("zh-CN", {
            minimumFractionDigits: 2,
          })
        }}
      </span>
    </template>
    <template #amount="{ row, index }">
      <span
        :style="{
          color: row.amount < 0 ? 'red' : '',
          fontWeight: row.amount < 0 ? 'bold' : '',
        }"
      >
        {{
          Number(row.amount).toLocaleString("zh-CN", {
            minimumFractionDigits: 2,
          })
        }}
      </span>
    </template>
    <template #materialCodeForm="{ row, size, index }">
      <material-select
        v-model="row.materialId"
        :size="size"
        :disabled="option.detail"
        cost
        @submit="handleMaterialSubmit($event, row, index)"
      />
    </template>
    <template #numForm="{ row, disabled, size }">
      <el-input-number
        :size="size"
        v-model="row.num"
        :disabled="disabled"
        :min="0"
        :controls="false"
        controls-position="right"
        style="width: 100%"
        @change="handleNumChange($event, row)"
      />
    </template>
    <template #amountForm="{ row, disabled, size }">
      <el-input-number
        :size="size"
        v-model="row.amount"
        :disabled="disabled"
        :min="0"
        :controls="false"
        controls-position="right"
        style="width: 100%"
        @change="handleAmountChange($event, row)"
      />
    </template>
    <template #priceForm="{ row, disabled, size }">
      <el-input-number
        :size="size"
        v-model="row.price"
        :disabled="disabled"
        :min="0"
        :controls="false"
        style="width: 100%"
        @change="handlePriceChange($event, row)"
      />
    </template>
    <template #type="{ row, index }">
      <el-tag size="mini" v-if="row.type === '1'" effect="plain">{{
          row.$type
        }}
      </el-tag>
      <el-tag
        size="mini"
        v-else-if="row.type === '2'"
        type="warning"
        effect="plain"
      >{{ row.$type }}
      </el-tag
      >
      <el-tag size="mini" v-else type="danger" effect="dark">{{
          row.$type
        }}
      </el-tag>
    </template>
    <template #attach="{ row, index }">
      <el-link
        v-if="row.attach"
        type="primary"
        target="_blank"
        @click="rowAttach(row, index)"
      >
        <i class="el-icon-circle-plus-outline"/>
        附件({{ row.attach ? row.attach.length : 0 }})
      </el-link>
    </template>
  </avue-crud>
</template>

<script>
import MaterialSelect from "@/views/ni/base/components/MaterialSelect1";
import {handleUploadPreview} from "@/util/util";

export default {
  name: "PayableApplyOrderItem",
  components: {MaterialSelect},
  props: {
    value: {
      type: Array,
      default: () => [],
    },
    cost: {
      type: Boolean,
      default: false,
    },
    detail: {
      type: Boolean,
      default: false,
    },
    selectionList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      data: [],
      form: {},
      page: {
        pageSize: 20,
        currentPage: 1,
        total: 0,
      },
      option: {
        header: false,
        detail: false,
        tip: false,
        menu: false,
        labelWidth: 110,
        calcHeight: 30,
        align: "center",
        refreshBtn: false,
        columnBtn: false,
        searchBtn: false,
        highlightCurrentRow: true,
        border: true,
        stripe: true,
        menuRight: false,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        submitBtn: false,
        emptyBtn: false,
        size: "mini",
        showSummary: true,
        selection: true,
        sumColumnList: [
          {
            name: "amount",
            type: "sum",
          },
          {
            name: "payApplyAmount",
            type: "sum",
          },
          {
            name: "applyAmount",
            type: "sum",
          },
        ],
        headerAlign: "center",
        column: [
          {
            label: "费用",
            prop: "cost",
            type: "select",
            dicData: [
              {
                label: "费用",
                value: true,
              },
              {
                label: "实物",
                value: false,
              },
            ],
            placeholder: " ",
            width: 70,
            disabled: true,
          },
          {
            label: "序号",
            prop: "rowNo",
            overHidden: true,
            width: 55,
          },
          {
            label: "品名",
            prop: "materialName",
            type: "textarea",
            minRows: 1,
            overHidden: true,
            placeholder: " ",
            cell: true,
          },
          {
            label: "用途",
            prop: "purpose",
            type: "textarea",
            minRows: 1,
            overHidden: true,
            placeholder: " ",
            cell: true,
          },
          {
            label: "编码",
            prop: "materialCode",
            overHidden: true,
            placeholder: " ",
            clearable: false,
            cell: true,
          },

          {
            label: "规格",
            prop: "specification",
            overHidden: true,
            placeholder: " ",
            disabled: true,
            clearable: false,
          },
          {
            label: "材质",
            prop: "quality",
            overHidden: true,
          },
          {
            label: "单位",
            prop: "unit",
            overHidden: true,
            type: "select",
            placeholder: " ",
            value: "1",
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_material_unit",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            slot: true,
            width: 75,
          },
          {
            label: "数量",
            prop: "num",
            type: "number",
            overHidden: true,
            placeholder: " ",
            cell: true,
            minWidth: 110,
            rules: [
              {
                required: true,
                message: "请输入",
                trigger: "blur",
              },
            ],
          },
          {
            label: "单价",
            prop: "price",
            type: "number",
            minWidth: 110,
            disabled: true,
            controls: false,
            precision: 2,
            cell: true,
            placeholder: " ",
          },
          {
            label: "金额",
            prop: "amount",
            type: "number",
            minWidth: 120,
            precision: 2,
            placeholder: " ",
            display: false,
            disabled: true,
            cell: true,
          },
          {
            label: "类型",
            prop: "type",
            type: "select",
            dicData: [
              {
                label: "订单",
                value: "1",
              },
              {
                label: "退货",
                value: "2",
              },
              {
                label: "调账",
                value: "3",
              },
              {
                label: "其他应付",
                value: "9",
              },
            ],
          },
          {
            label: "调账类型",
            prop: "adjustment",
            type: "select",
            width: 81,
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_fin_adjustment",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
          },
          {
            label: "调账业务",
            prop: "adjustmentType",
            type: "select",
            width: 81,
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_fin_adjustment_type",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
          },
          {
            label: "项目号",
            minWidth: 110,
            placeholder: " ",
            overHidden: true,
            prop: "projectSerialNo",
            hide: false,
          },
          {
            label: "预算号",
            minWidth: 110,
            placeholder: " ",
            overHidden: true,
            prop: "budgetSerialNo",
          },
          {
            label: "申购人",
            minWidth: 110,
            placeholder: " ",
            overHidden: true,
            prop: "applyUserName",
            hide: false,
          },
          {
            label: "备注",
            minWidth: 110,
            placeholder: " ",
            overHidden: true,
            prop: "remark",
            type: "textarea",
            minRows: 1,
            cell: true,
          },
          {
            label: "附件",
            type: "upload",
            width: 94,
            propsHttp: {
              res: "data",
              url: "attachId",
              name: "originalName",
            },
            cell: true,
            action:
              "/api/blade-resource/oss/endpoint/put-file-attach?code=public",
            display: true,
            showFileList: true,
            multiple: true,
            limit: 10,
            prop: "attach",
          },
        ],
      },
    };
  },
  watch: {
    value: {
      handler(val) {
        if (val) {
          this.onLoad(this.page);
        }
      },
      immediate: true,
    },
    detail: {
      handler(val) {
        this.option.detail = val;
        if (val && this.data) {
          this.data.forEach((item) => (item.$cellEdit = false));
        }
      },
      immediate: true,
    },
    cost: {
      handler(val) {
        const applyUserName = this.findObject(
          this.option.column,
          "applyUserName"
        );
        const projectSerialNo = this.findObject(
          this.option.column,
          "projectSerialNo"
        );
        if (val) {
          applyUserName.hide = true;
          projectSerialNo.hide = true;
        } else {
          applyUserName.hide = false;
          projectSerialNo.hide = false;
        }
      },
      immediate: true,
    },
  },
  methods: {
    onLoad(page) {
      this.loading = true;
      page.total = this.value.length;
      this.data = this.value.slice(
        (page.currentPage - 1) * page.pageSize,
        page.currentPage * page.pageSize
      );
      this.loading = false;
    },
    rowAttach(row, index) {
      this.$emit("attachClick", row, index);
    },
    handleUploadPreview(file, column, done) {
      handleUploadPreview(file, column, done);
    },
    handlePriceChange(price, row) {
      if (row.num) {
        row.amount = (Number(row.num) * Number(price)).toFixed(2);
      }
      this.sumAmount();
    },
    handleMaterialSubmit(selectList, row1, index) {
      if (selectList.length > 0) {
        const row = selectList[0];
        this.$nextTick(() => {
          this.$set(row1, "materialName", row.name);
          this.$set(row1, "materialCode", row.code);
          this.$set(row1, "specification", row.specification);
          this.$set(row1, "quality", row.quality);
          this.$set(row1, "gb", row.gb);
          this.$set(row1, "unit", row.unit);
          this.$set(row1, "cost", row.cost);
          const ii = (this.page.currentPage - 1) * this.page.pageSize + index;
          const data = this.value.map((item, i) => {
            if (i === ii) {
              item = row1;
            }
            return item;
          });
          this.$emit("update:value", data);
        });
      }
    },
    itemCellClickChange(row) {
      if (!this.detail && row.cost) {
        this.data.forEach((item) => (item.$cellEdit = false));
        row.$cellEdit = true;
      }
    },
    handleDel(row, index) {
      if (this.cost && !this.detail) {
        const i = (this.page.currentPage - 1) * this.page.pageSize + index;
        this.$emit("update:value", this.value.splice(i, 1));
      }
    },
    selectionChange(list) {
      this.$emit("update:selectionList", list);
    },
    handleNumChange(num, row) {
      if (row.price) {
        row.amount = Number(row.price) * Number(row.num);
      }
      this.sumAmount();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    sumAmount() {
      this.$nextTick(() => {
        const itemAmount = this.value.reduce((acc, cur) => {
          return Number(acc) + (cur.amount ? Number(cur.amount) : 0);
        }, 0);
        this.$emit("sumAmount", Number(itemAmount.toFixed(2)));
      });
    },
    handleAmountChange(amount, row) {
      if (row.num) {
        row.price = (Number(amount) / Number(row.num)).toFixed(2);
      }
      this.sumAmount();
    },
  },
};
</script>

<style lang="scss" scoped>
/deep/ .el-link--inner {
  font-size: 12px;
}

/deep/ .el-upload-list__item-name {
  font-size: 12px;
}
</style>

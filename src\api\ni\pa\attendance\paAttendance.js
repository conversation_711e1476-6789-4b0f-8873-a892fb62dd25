import request from "@/router/axios";

export const submit = (row) => {
  return request({
    url: "/api/ni/pa/attendance/submit",
    method: "post",
    data: row,
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/ni/pa/attendance/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const getList = (current, size, params) => {
  return request({
    url: "/api/ni/pa/attendance/page",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const submitRange = (dates, key, row) => {
  return request({
    url: `/api/ni/pa/attendance/range/${key}`,
    method: "post",
    params: {
      dates,
    },
    data: row,
  });
};

export const multipleModify = (params, month) => {
  return request({
    url: `/api/ni/pa/attendance/multiple-modify/${month}`,
    method: "post",
    data: params,
  });
};

export const multipleCheck = (params, month) => {
  return request({
    url: `/api/ni/pa/attendance/multiple-check/${month}`,
    method: "post",
    data: params,
  });
};

export const store = (params) => {
  return request({
    url: `/api/ni/pa/attendance/store`,
    method: "post",
    params: {
      ...params,
    },
  });
};

export const personCalendarPage = (params) => {
  return request({
    url: "/api/ni/pa/attendance/person-calendar-page",
    method: "get",
    params: {
      ...params,
    },
  });
};

export const reportPage = (current, size, params) => {
  return request({
    url: "/api/ni/pa/attendance/report-page",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const multiComment = (userIds, month, comment) => {
  return request({
    url: "/api/ni/pa/attendance/multi-comment",
    method: "post",
    params: {
      userIds,
      month,
      comment,
    },
  });
};

import request from '@/router/axios';

export const getList = (params) => {
  return request({
    url: '/api/ni/base/supplier/type/list',
    method: 'get',
    params: {
      ...params,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/ni/base/supplier/type/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/ni/base/supplier/type/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/ni/base/supplier/type/save',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/ni/base/supplier/type/update',
    method: 'post',
    data: row
  })
}

export const getTree = (params) => {
  return request({
    url: '/api/ni/base/supplier/type/tree',
    method: 'get',
    params: {
      ...params,
    }
  })
}
export const sync = (id, isCovered = false) => {
  return request({
    url: '/api/ni/base/supplier/type/sync',
    method: 'post',
    params: {
      id,
      isCovered
    }
  })
}

import { setStore, getStore, removeStore } from "@/util/store";
import website from "@/config/website";
import { currentVersion } from "@/api/system/version";

const common = {
  state: {
    language: getStore({ name: "language" }) || "zh",
    isCollapse: false,
    isFullScren: false,
    isMenu: true,
    isShade: false,
    screen: -1,
    isLock: getStore({ name: "isLock" }) || false,
    showTag: true,
    showDebug: false,
    showCollapse: true,
    showSearch: true,
    showLock: true,
    showDoc: true,
    showFullScren: true,
    showTheme: process.env.VUE_APP_SHOW_THEME||true,
    showMenu: true,
    showColor: true,
    colorName: getStore({ name: "colorName" }) || process.env.VUE_APP_COLOR||"#167C46",
    themeName: getStore({ name: "themeName" }) || process.env.VUE_APP_THEME || "theme-lte",
    lockPasswd: getStore({ name: "lockPasswd" }) || "",
    website: website,
    tenant: getStore({ name: "tenantCode" }) || website.tenantId,
    version: getStore({ name: "version" }) || {},
  },
  mutations: {
    SET_VERSION: (state, version) => {
      state.version = version;
      if(version.id){
        setStore({
          name: "version",
          content: state.version,
          type: "session",
        });
      }else {
        removeStore({
          name: "version",
          type: "session",
        })
      }
    },
    SET_LANGUAGE: (state, language) => {
      state.language = language;
      setStore({
        name: "language",
        content: state.language,
      });
    },
    SET_SHADE: (state, active) => {
      state.isShade = active;
    },
    SET_COLLAPSE: (state) => {
      state.isCollapse = !state.isCollapse;
    },
    SET_FULLSCREN: (state) => {
      state.isFullScren = !state.isFullScren;
    },
    SET_IS_MENU: (state, menu) => {
      state.isMenu = menu;
    },
    SET_LOCK: (state) => {
      state.isLock = true;
      setStore({
        name: "isLock",
        content: state.isLock,
        type: "session",
      });
    },
    SET_SCREEN: (state, screen) => {
      state.screen = screen;
    },
    SET_COLOR_NAME: (state, colorName) => {
      state.colorName = colorName;
      setStore({
        name: "colorName",
        content: state.colorName,
      });
    },
    SET_THEME_NAME: (state, themeName) => {
      state.themeName = themeName;
      setStore({
        name: "themeName",
        content: state.themeName,
      });
    },
    SET_LOCK_PASSWD: (state, lockPasswd) => {
      state.lockPasswd = lockPasswd;
      setStore({
        name: "lockPasswd",
        content: state.lockPasswd,
        type: "session",
      });
    },
    CLEAR_LOCK: (state) => {
      state.isLock = false;
      state.lockPasswd = "";
      removeStore({
        name: "lockPasswd",
        type: "session",
      });
      removeStore({
        name: "isLock",
        type: "session",
      });
    },
    SET_TENANT: (state, tenantId) => {
      state.tenant = tenantId;
      setStore({
        name: "tenantCode",
        content: state.tenant,
        type: "session",
      });
    },
  },
  actions: {
    GetCurrentVersion({ commit }) {
      return new Promise((resolve) => {
        currentVersion().then((res) => {
          const data = res.data;
          commit("SET_VERSION", data.data);
          resolve(data.data);
        });
      });
    },
  },
};
export default common;

<template>
  <el-drawer :visible.sync="visible" :title="title" size="1100px" append-to-body>
    <basic-container>
      <avue-crud v-if="visible" :option="option" :table-loading="loading" :data="data" :page.sync="page" v-model="form"
        ref="crud" @search-change="searchChange" @search-reset="searchReset" @selection-change="selectionChange"
        @current-change="currentChange" @size-change="sizeChange" @refresh-change="refreshChange" @on-load="onLoad">
        <template #menuLeft="{ row }">
          <el-button type="primary" size="mini" @click="rowPrint(row)">打印
          </el-button>
        </template>
        <template #cost="{ row }">
          <el-tag size="mini" type="danger" effect="dark" v-if="row.cost">
            费用
          </el-tag>
          <el-tag size="mini" type="info" effect="plain" v-else> 实物</el-tag>
        </template>
      </avue-crud>
    </basic-container>
  </el-drawer>
</template>

<script>
import { getItemPage,getItemList } from "@/api/ni/por/order-arrival";
import { hiprint } from "vue-plugin-hiprint";
import { loadPrintTemplate } from "@/api/system/printTemplate";
import { numToCapital } from "@/util/util";

export default {
  name: "OrderArrivalItemDrawer",
  components: {},
  data() {
    return {
      arrivalId: null,
      title: "",
      visible: false,
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        menu: false,
        searchEnter: true,
        cellBtn: true,
        addBtn: false,
        selection: true,
        searchLabel: 110,
        span: 8,
        labelWidth: 130,
        menuWidth: 180,
        size: "mini",
        searchSize: "mini",
        align: "center",
        searchIndex: 3,
        searchIcon: true,
        height: "auto",
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: false,
        viewBtn: false,
        dialogClickModal: false,
        column: [
          {
            label: "类型",
            prop: "cost",
            type: "select",
            dicData: [
              {
                label: "费用",
                value: true,
              },
              {
                label: "实物",
                value: false,
              },
            ],
            placeholder: " ",
            width: 70,
            disabled: true,
          },
          {
            label: "序号",
            prop: "row",
            width: 65,
            overHidden: true,
          },
          {
            label: "品名",
            prop: "materialName",
            filters: true,
            minWidth: 95,
            display: false,
            overHidden: true,
          },
          {
            label: "编码",
            placeholder: " ",
            prop: "materialCode",
            minWidth: 120,
            clearable: false,
            disabled: true,
          },
          {
            label: "规格",
            prop: "specification",
            filters: true,
            minWidth: 95,
            display: false,
            overHidden: true,
          },
          {
            label: "材质",
            prop: "quality",
            filters: true,
            minWidth: 95,
            overHidden: true,
            display: false,
          },
          {
            label: "国标",
            prop: "gb",
            hide: true,
            filters: true,
            minWidth: 95,
            overHidden: true,
            display: false,
          },
          {
            label: "单位",
            prop: "unit",
            overHidden: true,
            type: "select",
            placeholder: " ",
            value: "1",
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_material_unit",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            slot: true,
            filters: true,
            sortable: true,
            minWidth: 90,
            display: false,
          },
          {
            label: "数量",
            prop: "arrivalNum",
            type: "number",
            controls: false,
            placeholder: " ",
            sortable: true,
            minWidth: 90,
            display: false,
          },
          {
            label: "单价",
            prop: "price",
            type: "number",
            controls: false,
            precision: 2,
            placeholder: " ",
            disabled: true,
            cell: true,
            sortable: true,
            display: false,
          },
          {
            label: "金额",
            prop: "arrivalAmountStr",
            type: "number",
            precision: 2,
            controls: false,
            placeholder: " ",
            sortable: true,
            minWidth: 90,
            display: false,
          },
          {
            label: "申请备注",
            prop: "applyRemark",
            type: "textarea",
            span: 24,
            minRows: 3,
            overHidden: true,
            minWidth: 110,
            display: false,
          },
          {
            label: "订单备注",
            prop: "orderRemark",
            type: "textarea",
            span: 24,
            minRows: 3,
            overHidden: true,
            minWidth: 110,
            display: false,
          },

          {
            label: "备注",
            prop: "remark",
          },
          {
            label: "合格数",
            prop: "qualifiedNum",
            type: "number",
            controls: false,
            placeholder: " ",
            sortable: true,
            minWidth: 90,
            display: false,
            fixed: "right",
          },
          {
            label: "暂存",
            prop: "staging",
            type: "radio",
            fixed: "right",
            minWidth: 70,
            dicData: [
              {
                label: "是",
                value: true,
              },
              {
                label: "否",
                value: false,
              },
            ],
          },
          {
            label: "暂存位置",
            prop: "depotLocation",
            overHidden: true,
            minWidth: 110,
          },
          {
            label: "入库状态",
            prop: "stockState",
            fixed: "right",
            type: "select",
            dicData: [
              {
                label: "未入库",
                value: "1",
              },
              {
                label: "部分入库",
                value: "5",
              },
              {
                label: "已入库",
                value: "3",
              },
              {
                label: "无需入库",
                value: "4",
              },
            ],
            minWidth: 90,
            slot: true,
            display: false,
            filters: true,
          },
        ],
      },
      data: [],
      form: {},
      action: {},
      brandDict: [],
      brandDictKeyValue: {},
      unitDict:[],
      unitDictKeyValue: {},
      stockInPrintTemplate: null,
      costPrintTemplate: null,
      fixedAssetPrintTemplate: null,
    };
  },
  methods: {
    init(arrivalId) {
      loadPrintTemplate("ni_order_arrival_stock_in").then((res) => {
        this.stockInPrintTemplate = JSON.parse(res.data.data.content);
      });
      loadPrintTemplate("ni_order_arrival_cost").then((res) => {
        this.costPrintTemplate = JSON.parse(res.data.data.content);
      });
      loadPrintTemplate("ni_fin_fixed_asset").then((res) => {
        this.fixedAssetPrintTemplate = JSON.parse(res.data.data.content);
      });
      this.$http
      .get("/api/blade-system/dict-biz/dictionary?code=ni_material_unit")
      .then((res) => {
        const unitDict = res.data.data;
        this.unitDictKeyValue = unitDict.reduce((acc, cur) => {
          acc[cur.dictKey] = cur.dictValue;
          return acc;
        }, {});
      });
    this.$http
      .get("/api/blade-system/dict-biz/dictionary?code=ni_brand")
      .then((res) => {
        this.brandDict = res.data.data;
        this.brandDictKeyValue = this.brandDict.reduce((acc, cur) => {
          acc[cur.dictKey] = cur.dictValue;
          return acc;
        }, {});
      });

      this.arrivalId = arrivalId;
      this.visible = true;
    },
    async rowPrint() {
  //模板是否加载
  if (!this.stockInPrintTemplate || !this.costPrintTemplate || !this.fixedAssetPrintTemplate) {
    this.$message.error("打印模板加载失败，请刷新页面后重试");
    return;
  }

  //是否选择数据
  if (this.selectionList.length === 0) {
    this.$message.warning("请选择要打印的数据");
    return;
  }

  //打印类型
  const firstItem = this.selectionList[0];
  const isCost = firstItem.cost;
  const isStock = !firstItem.cost && firstItem.stockState === "3";
  const isFixedAsset = !firstItem.cost && (firstItem.fa === true || firstItem.faStatus === 2);

  let printType;
  if (isCost) {
    printType = "cost";
  } else if (isStock) {
    printType = "stock";
  } else if (isFixedAsset) {
    printType = "fixedAsset";
  } else {
    this.$message.warning("选中的数据不符合任何打印模板条件");
    return;
  }

  //选中项是否属于同一类型
  const isValid = this.selectionList.every((item) => {
    if (printType === "cost") {
      return item.cost === true;
    } else if (printType === "stock") {
      return !item.cost && item.stockState === "3";
    } else if (printType === "fixedAsset") {
      return !item.cost && (item.fa === true || item.faStatus === 2);
    }
    return false;
  });

  if (!isValid) {
    this.$message.error("请选择相同类型的记录进行打印（费用、入库或固定资产）");
    return;
  }

  //到货单信息
  let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
  ids.join(",");
  const res = await getItemList({
    ids: ids,
  });
  const arrivalData = res.data.data;

  //打印数据
  let printData = [];
  let template;

  if (printType === "cost") {
    //费用申请单
    const costItems = this.selectionList.map((item) => ({
      ...item,
      unit: this.unitDictKeyValue[item.unit] || item.unit,
      materialName: item.materialName && typeof item.materialName === 'string'
        ? item.materialName.substr(0, 10) + (item.materialName.length > 10 ? "..." : "")
        : "",
      specification: item.specification && typeof item.specification === 'string'
        ? item.specification.substr(0, 8) + (item.specification.length > 8 ? "..." : "")
        : "",
      remark: item.remark && typeof item.remark === 'string'
        ? item.remark.substr(0, 7) + (item.remark.length > 7 ? "..." : "")
        : "",
      price: Number(item.price || 0).toLocaleString("zh-CN", { minimumFractionDigits: 2 }),
      amount: Number(item.arrivalAmount || 0).toLocaleString("zh-CN", { minimumFractionDigits: 2 }),
    }));

    printData.push({
      ...arrivalData,
      serialNo: arrivalData[0].serialNo,
      purchaseUserName: arrivalData[0].purchaseUserName,
      supplierName: arrivalData[0].supplier,
      arrivalDate: arrivalData[0].arrivalDate,
      items: costItems,
      title: this.getTitleByBrand(arrivalData.brand, printType),
      totalAmount: Number(
        this.selectionList.reduce((acc, cur) => acc + Number(cur.arrivalAmount || 0), 0)
      ).toLocaleString("zh-CN", { minimumFractionDigits: 2 }),
      upperAmount: numToCapital(
        this.selectionList.reduce((acc, cur) => acc + Number(cur.arrivalAmount || 0), 0)
      ),
      depotName: "",
      keeperName: this.$store.getters.userInfo.user_name,
    });

    template = this.costPrintTemplate;
  } else if (printType === "stock") {
    //入库单
    const stockItems = this.selectionList.map((item) => ({
      ...item,
      unit: this.unitDictKeyValue[item.unit] || item.unit,
      materialName: item.materialName != null ? item.materialName.substr(0, 10) + (item.materialName.length > 10 ? "..." : "")
        : "",
      specification: item.specification != null ? item.specification.substr(0, 8) + (item.specification.length > 8 ? "..." : "")
        : "",
      remark: item.remark!=null ? item.remark.substr(0, 7) + (item.remark.length > 7 ? "..." : "")
        : "",
      price: Number(item.price || 0).toLocaleString("zh-CN", { minimumFractionDigits: 2 }),
      amount: Number(item.arrivalAmount || 0).toLocaleString("zh-CN", { minimumFractionDigits: 2 }),
    }));

    printData.push({
      ...arrivalData,
      serialNo: arrivalData[0].serialNo,
      purchaseUserName: arrivalData[0].purchaseUserName,
      supplierName: arrivalData[0].supplier,
      arrivalDate: arrivalData[0].arrivalDate,

      items: stockItems,
      title: this.getTitleByBrand(arrivalData.brand, printType),
      totalAmount: Number(
        this.selectionList.reduce((acc, cur) => acc + Number(cur.arrivalAmount || 0), 0)
      ).toLocaleString("zh-CN", { minimumFractionDigits: 2 }),
      upperAmount: numToCapital(
        this.selectionList.reduce((acc, cur) => acc + Number(cur.arrivalAmount || 0), 0)
      ),
      depotName: "",
      keeperName: this.$store.getters.userInfo.user_name,
    });

    template = this.stockInPrintTemplate;
  } else if (printType === "fixedAsset") {
    //固定资产设备验收单
    printData = this.selectionList.map((item) => ({
      ...item,
      brand: this.brandDictKeyValue[item.brand] || item.brand,
      name: item.materialName || "",
      buyDate: item.arrivalDate,
      num: item.arrivalNum,
      amount: Number(item.qualifiedAmount || item.arrivalAmount || 0).toLocaleString("zh-CN", {
        maximumFractionDigits: 2,
      }),
      supplierName: item.supplier || "",
      projectNo: item.projectSerialNo != null ? item.projectSerialNo : item.budgetSerialNo || "",
      serialNo: "",
      title: this.getTitleByBrand(arrivalData.brand, printType),
    }));

    template = this.fixedAssetPrintTemplate;
  }

  if (printData.length === 0) {
    this.$message.warning("没有可打印的数据");
    return;
  }

  //打印
  const hiprintTemplate = new hiprint.PrintTemplate({
    template: template,
  });
  hiprintTemplate.print(printData, {
    callback: () => {
      console.log("打印窗口已打开");
      this.$emit("print-success");// todo: 添加打印成功事件
    },
  });
},
getTitleByBrand(brand, printType) {
      let companyName = "山东能特异能源科技有限公司";
      if (brand === "4") {
        companyName = "演绎智能设备（山东）有限公司";
      } else if (brand === "2") {
        companyName = "淄博至简工贸";
      }

      if (printType === "cost") {
        return `${companyName}费用申请单`;
      } else if (printType === "fixedAsset") {
        return `${companyName}固定资产设备验收单`;
      }
      return `${companyName}入库单`;
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      const query = Object.assign(params, this.query);
      query.parentIds = this.arrivalId;
      query.status = 2;
      query.descs = "id";
      getItemPage(page.currentPage, page.pageSize, query).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        data.records.forEach((item) => {
          if (item.arrivalAmount) {
            item.arrivalAmountStr = Number(item.arrivalAmount).toLocaleString(
              "zh-CN",
              {
                minimumFractionDigits: 2,
              }
            );
          }
        });
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
  },
};
</script>

<style scoped></style>

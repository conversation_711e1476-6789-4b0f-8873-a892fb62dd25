import request from '@/router/axios';


export const getDetail = (id) => {
  return request({
    url: '/api/ni/old/guoWaiFaHuo/detail',
    method: 'get',
    params: {
      id
    }
  })
}
export const getShippingList = (current, size, params) => {
  return request({
    url: '/api/ni/old/guoWaiFaHuo/shippingList',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getList = (current, size, params) => {
  return request({
    url: '/api/ni/old/guoWaiFaHuo/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}


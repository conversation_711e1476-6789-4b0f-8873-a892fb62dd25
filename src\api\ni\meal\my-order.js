import request from "@/router/axios";

export const getMyPage = (current, size, params) => {
  return request({
    url: "/api/ni/meal/order/myPage",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const gePage = (current, size, params) => {
  return request({
    url: "/api/ni/meal/order/page",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (date, type, personId) => {
  return request({
    url: "/api/ni/meal/order/detail",
    method: "get",
    params: {
      date,
      type,
      personId,
    },
  });
};
export const add = (row) => {
  return request({
    url: "/api/ni/meal/order/day/submit",
    method: "post",
    data: row,
  });
};
export const update = (row) => {
  return request({
    url: "/api/ni/meal/order/day/submit",
    method: "post",
    data: row,
  });
};
export const cancel = (params) => {
  return request({
    url: "/api/ni/meal/order/cancel",
    method: "post",
    params,
  });
};

<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      show-overflow-tooltip
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template #status="{ row }">
        <el-tag :type="feedbackStatus[row.status - 1].style"
          >{{ feedbackStatus[row.status - 1].text }}
        </el-tag>
      </template>
      <template #isTenNonNeglect="{ row }">
        <el-tag :type="row.isTenNonNeglect ? 'danger' : 'warning'"
          >{{ row.isTenNonNeglect ? "是" : "不是" }}
        </el-tag>
      </template>

      <template #pointId="{ row }">
        <el-tag :type="row.pointId ? 'success' : 'info'"
          >{{ row.pointId ? "已同步" : "未同步" }}
        </el-tag>
      </template>

      <template slot="menu" slot-scope="{ row, size, index }">
        <el-button
          type="text"
          icon="el-icon-cloudy"
          :size="size"
          v-if="
            permission.feedback_synchronous && row.status === 3 && !row.pointId
          "
          @click="() => synchronous(row, index)"
          >同步
        </el-button>
      </template>

      <template slot="menuLeft">
        <el-button
          type="danger"
          size="small"
          icon="el-icon-delete"
          plain
          v-if="permission.feedback_delete"
          @click="handleDelete"
          >删 除
        </el-button>
        <el-button
          type="primary"
          size="small"
          icon="el-icon-cloudy"
          plain
          v-if="permission.feedback_synchronous"
          @click="() => synchronous()"
          >同步
        </el-button>
        <el-button
          type="primary"
          size="small"
          icon="el-icon-download"
          v-if="permission.feedback_export"
          @click="handleExport"
          >导出
        </el-button>
      </template>

      <template slot-scope="{ value }" slot="feedbackCollectionCountForm">
        {{ value }}次
      </template>
      <template slot-scope="{ value }" slot="statusForm">
        <el-tag :type="feedbackStatus[value - 1].style"
          >{{ feedbackStatus[value - 1].text }}
        </el-tag>
      </template>
      <template slot-scope="{ value }" slot="isTenNonNeglectForm">
        <el-tag :type="value ? 'danger' : 'warning'"
          >{{ value ? "是" : "不是" }}
        </el-tag>
      </template>

      <template slot-scope="{ value }" slot="pointIdForm">
        <el-tag :type="value ? 'success' : 'info'"
          >{{ value ? "已同步" : "未同步" }}
        </el-tag>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import {
  getList,
  getDetail,
  add,
  update,
  remove,
  asyncQdrant,
} from "@/api/ni/feedback/feedback";
import option from "@/const/ni/feedback/feedback";
import { mapGetters } from "vuex";

export default {
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: option,
      data: [],
      feedbackStatus: [
        { text: "等待负责人认领", style: "warning" },
        { text: "认领处理中", style: "warning" },
        { text: "待发起人确认", style: "warning" },
        { text: "人工分配中", style: "warning" },
        { text: "无需处理", style: "success" },
        { text: "人工驳回", style: "danger" },
        { text: "发起人驳回", style: "danger" },
        { text: "问题已处理", style: "success" },
      ],
      exportColumn: [
        {
          label: "发起人",
          prop: "applyUserName",
          type: "input",
        },
        {
          label: "问题负责人",
          prop: "responsibilityName",
          type: "input",
        },
        {
          label: "问题描述",
          prop: "description",
          type: "textarea",
          search: true,
          span: 24,
        },
        {
          label: "AI指导意见",
          prop: "aiGuidance",
          type: "textarea",
          span: 24,
        },
        {
          label: "最终解决日期",
          prop: "finalResolutionTime",
          type: "input",
        },
        {
          label: "状态",
          prop: "status",
          type: "select",
        },
        {
          label: "提出时间",
          prop: "createTime",
          type: "input",
        },
        {
          label: "十不放过",
          prop: "isTenNonNeglect",
          type: "select",
        },
      ],
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.feedback_add, false),
        viewBtn: this.vaildData(this.permission.feedback_view, false),
        delBtn: this.vaildData(this.permission.feedback_delete, false),
        editBtn: this.vaildData(this.permission.feedback_edit, false),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  methods: {
    rowSave(row, done, loading) {
      add(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          window.console.log(error);
        }
      );
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          console.log(error);
        }
      );
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    async handleExport() {
      let msg =
        "确定将<span style='font-weight: bolder;color: #F56C6C'>选择数据</span>导出?";
      if (this.selectionList.length === 0) {
        msg =
          "确定要将<span style='font-weight: bolder;color: #F56C6C'>全部数据</span>导出?";
      }
      try {
        await this.$alert(msg, {
          dangerouslyUseHTMLString: true,
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        });
        const data = [];
        if (this.selectionList.length > 0) {
          this.selectionList.forEach((item) => {
            data.push({
              ...item,
              status: this.feedbackStatus[item.status - 1].text,
              isTenNonNeglect: item.isTenNonNeglect ? "是" : "否",
              pointId: item.pointId ? "已同步" : "未同步",
            });
          });
        } else {
          const res = await getList(1, 10000, {
            ...this.params,
            ...this.query,
          });
          const data1 = res.data.data.records;
          data1.forEach((item) => {
            data.push({
              ...item,
              status: this.feedbackStatus[item.status - 1].text,
              isTenNonNeglect: item.isTenNonNeglect ? "是" : "否",
              pointId: item.pointId ? "已同步" : "未同步",
            });
          });
        }
        await this.$Export.excel({
          title: "问题反馈导出",
          columns: this.exportColumn,
          data,
        });
      } catch (e) {
        console.log(e);
      }
    },
    synchronous(row, index) {
      console.log(row);
      let selectData = [];
      let feedbackIds;
      if (row) {
        selectData[0] = row;
        feedbackIds = row.id;
      } else {
        selectData = this.selectionList;
        feedbackIds = this.ids;
      }
      if (selectData.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }

      const nonStatus = selectData.filter(
        (item) => item.status != 8 && !item.pointId
      ).length;
      if (nonStatus) {
        this.$message.warning("请不要选择未完成或者已经同步的消息");
        return;
      }

      this.$confirm("确定将选择数据同步到Qdrant?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return asyncQdrant(feedbackIds);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen(done, type) {
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then((res) => {
          const data = res.data.data;

          data.fileUrls = data.fileUrls.map((item) => item.value);

          this.form = data;
        });
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
  },
};
</script>

<style lang="scss" scoped>
/deep/ .el-table .cell {
  display: -webkit-box !important;
  -webkit-box-orient: vertical !important;
  -webkit-line-clamp: 2 !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}
</style>

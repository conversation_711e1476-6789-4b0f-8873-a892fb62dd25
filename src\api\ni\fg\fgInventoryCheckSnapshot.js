import request from '@/router/axios';


export const getDetail=(id)=>{
  return request({
    url: '/api/ni/fg/inventory-check-snapshot/detail',
    method: 'get',
    params: {
      id
    }
  })
}
export const build = (checkId) => {
  return request({
    url: '/api/ni/fg/inventory-check-snapshot/build/' + checkId,
    method: 'post',
  })
}

export const getList = (current, size, params) =>
  request({
    url: '/api/ni/fg/inventory-check-snapshot/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })

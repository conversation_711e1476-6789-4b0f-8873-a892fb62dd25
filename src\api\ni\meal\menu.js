import request from "@/router/axios";

export const getPage = (current, size, params) => {
  return request({
    url: "/api/ni/meal/menu/page",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getList = (params) => {
  return request({
    url: "/api/ni/meal/menu/list",
    method: "get",
    params,
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/ni/meal/menu/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const remove = (ids, syncOrder = false) => {
  return request({
    url: "/api/ni/meal/menu/remove",
    method: "post",
    params: {
      ids,
      syncOrder
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/ni/meal/menu/save",
    method: "post",
    data: row,
  });
};

export const update = (row, syncOrder = false) => {
  return request({
    url: "/api/ni/meal/menu/update",
    method: "post",
    data: row,
    params: {
      syncOrder,
    },
  });
};
export const submit = (ids) => {
  return request({
    url: "/api/ni/meal/menu/submit",
    method: "post",
    params: {
      ids,
    },
  });
};

export const back = (ids) => {
  return request({
    url: "/api/ni/meal/menu/back",
    method: "post",
    params: {
      ids,
    },
  });
};

export const toVoid = (ids) => {
  return request({
    url: "/api/ni/meal/menu/toVoid",
    method: "post",
    params: {
      ids,
    },
  });
};

export const dayAdd = (row) => {
  return request({
    url: "/api/ni/meal/menu/daySave",
    method: "post",
    data: row,
  });
};

export const dayUpdate = (row) => {
  return request({
    url: "/api/ni/meal/menu/dayUpdate",
    method: "post",
    data: row,
  });
};

export const dayPage = (current, size, params) => {
  return request({
    url: "/api/ni/meal/menu/dayPage",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};
export const getDayDetail = (date, type) => {
  return request({
    url: "/api/ni/meal/menu/dayDetail",
    method: "get",
    params: {
      date,
      type,
    },
  });
};

export const dayRemove = (date) => {
  return request({
    url: "/api/ni/meal/menu/dayRemove",
    method: "post",
    params: {
      date,
    },
  });
};

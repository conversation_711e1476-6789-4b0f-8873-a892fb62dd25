export default {
  height:'auto',
  calcHeight: 30,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  searchIndex: 3,
  searchIcon: true,
  border: true,
  index: false,
  viewBtn: true,
  selection: true,
  dialogClickModal: false,
  menuWidth: 180,
  addBtn: false, // 禁用自动生成的新增按钮，使用模板中手动添加的按钮
  column: [
    {
      label: "状态",
      prop: "status",
      dicUrl: "/api/blade-system/dict/dictionary?code=data_status",
      props: {
        label: "dictValue",
        value: "dictKey",
      },
      dataType: "number",
      type: "select",
    },
    {
      label: "申请人",
      prop: "userName",
      type: "input",
      search: true,
      searchSpan: 6,
      rules: [{
        required: true,
        message: "请输入申请人",
        trigger: "blur"
      }],
      slot: true,
      formslot: true,
    },
    {
      label: "部门",
      prop: "department",
      type: "input",
      search: true,
      rules: [{
        required: true,
        message: "请输入部门",
        trigger: "blur"
      }],
    },
    {
      label: "创建人",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建部门",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改人",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改时间",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "是否已删除",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "租户id",
      prop: "tenantId",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "用餐日期",
      prop: "mealData",
      type: "date",
      format: "yyyy-MM-dd",
      valueFormat: "yyyy-MM-dd",
      search: true,
      searchSpan: 6,
      placeholder: "选择日期",
      rules: [{
        required: true,
        message: "请选择用餐日期",
        trigger: "blur"
      }],
    },
    {
      label: "用餐时段",
      prop: "mealTime",
      type: "select",
      search: true,
      searchSpan: 6,
      dicData: [
        {
          label: "早餐",
          value: "早餐"
        },
        {
          label: "午餐",
          value: "午餐"
        },
        {
          label: "晚餐",
          value: "晚餐"
        },
        {
          label: "自定义时间",
          value: "自定义时间"
        }
      ],
      rules: [{
        required: true,
        message: "请选择用餐时段",
        trigger: "blur"
      }],
      control: (val) => {
        if (val === '自定义时间') {
          return {
            customTime: { display: true, required: true }
          };
        } else {
          return {
            customTime: { display: false, required: false }
          };
        }
      }
    },
    {
      label: "用餐类型",
      prop: "mealType",
      type: "select",
      search: true,
      dicUrl: "/api/blade-system/dict-biz/dictionary?code=ni_meal_add_type",
      props: {
        label: "dictValue",
        value: "dictKey"
      },
      rules: [{
        required: true,
        message: "请选择用餐类型",
        trigger: "blur"
      }],
      slot: true
    },
    {
      label: "用餐时间",
      prop: "customTime",
      type: "time",
      format: "HH:mm",
      valueFormat: "HH:mm",
      placeholder: "选择时间",
      rules: [{
        required: false,
        message: "请选择用餐时间",
        trigger: "blur"
      }],
    },
    {
      label: "用餐人数",
      prop: "amount",
      type: "input",
      rules: [{
        required: true,
        message: "请输入用餐人数",
        trigger: "blur"
      }],
    },
    {
      label: "用餐标准",
      prop: "standard",
      type: "select",
      dicUrl: "/api/blade-system/dict-biz/dictionary?code=ni_meal_add_stand",
      props: {
        label: "dictValue",
        value: "dictKey"
      },
      rules: [{
        required: true,
        message: "请选择用餐标准",
        trigger: "blur"
      }],
      slot: true
    },
    {
      label: "用餐地点",
      prop: "place",
      type: "select",
      dicUrl: "/api/blade-system/dict-biz/dictionary?code=ni_meal_add_addr",
      props: {
        label: "dictValue",
        value: "dictKey"
      },
      search: true,
      rules: [{
        required: true,
        message: "请选择用餐地点",
        trigger: "blur"
      }],
      slot: true
    },
    {
      label: "酒水标准",
      prop: "drink",
      type: "select",
      dicUrl: "/api/blade-system/dict-biz/dictionary?code=ni_meal_add_estand",
      props: {
        label: "dictValue",
        value: "dictKey"
      },
      rules: [{
        required: true,
        message: "请选择酒水标准",
        trigger: "blur"
      }],
      slot: true,
      control: (val) => {
        if (val) {
          return {
            kind: {
              display: true,
              rules: [{
                required: true,
                message: "请输入酒水种类",
                trigger: "blur"
              }]
            }
          };
        } else {
          return {
            kind: {
              display: false,
              rules: []
            }
          };
        }
      }
    },
    {
      label: "酒水种类",
      prop: "kind",
      type: "input",
      display: false,
      rules: []
    },
    {
      label: "预计花费",
      prop: "targetBill",
      type: "number",
      controls: false,
      precision: 2,
      placeholder: " ",
      rules: [{
        required: true,
        message: "请输入预计花费",
        trigger: "blur"
      }],
    },
    {
      label: "实际花费",
      prop: "actualBill",
      type: "number",
      controls: false,
      precision: 2,
      placeholder: " ",
      rules: [{
        required: true,
        message: "请输入实际花费",
        trigger: "blur"
      }],
    },
    {
      label: "用餐事由",
      prop: "reason",
      type: "input",
      rules: [{
        required: true,
        message: "请输入用餐事由",
        trigger: "blur"
      }],
    },
    {
      label: "申请人id",
      prop: "userId",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
  ]
}

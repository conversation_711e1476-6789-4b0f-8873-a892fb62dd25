import request from "@/router/axios";

export const getList = (current, size, params) => {
  return request({
    url: "/api/ni/por/purchaseBack/item/page",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/ni/por/purchaseBack/item/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const remove = (ids) => {
  return request({
    url: "/api/ni/por/purchaseBack/item/remove",
    method: "post",
    params: {
      ids,
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/ni/por/purchaseBack/item/submit",
    method: "post",
    data: row,
  });
};

export const update = (row) => {
  return request({
    url: "/api/ni/por/purchaseBack/item/submit",
    method: "post",
    data: row,
  });
};

export const toVoid = (ids) => {
  return request({
    url: "/api/ni/por/purchaseBack/item/toVoid",
    method: "post",
    params: {
      ids,
    },
  });
};
export const refund = (ids) => {
  return request({
    url: "/api/ni/por/purchaseBack/item/refund",
    method: "post",
    params: {
      ids,
    },
  });
};
export const changeAlipayBusinessNo = (ids, alipayBusinessNo) => {
  return request({
    url: "/api/ni/por/purchaseBack/item/changeAlipayBusinessNo",
    method: "post",
    params: {
      ids,
      alipayBusinessNo,
    },
  });
};

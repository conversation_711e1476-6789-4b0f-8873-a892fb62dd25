import request from '@/router/axios';

export const getPage = (current, size, params) => {
  return request({
    url: '/api/ni/fin/receivablesPlan/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/ni/fin/receivablesPlan/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/ni/fin/receivablesPlan/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/ni/fin/receivablesPlan/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/ni/fin/receivablesPlan/submit',
    method: 'post',
    data: row
  })
}
export const submit = (ids) => {
  return request({
    url: '/api/ni/fin/receivablesPlan/submit',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const back = (ids) => {
  return request({
    url: '/api/ni/fin/receivablesPlan/back',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const toVoid = (ids) => {
  return request({
    url: '/api/ni/fin/receivablesPlan/toVoid',
    method: 'post',
    params: {
      ids,
    }
  })
}


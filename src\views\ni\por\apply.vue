<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :search.sync="query"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      :upload-preview="uploadPreview"
      :upload-after="uploadAfter"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
      :cell-style="cellStyle"
    >
      <template #amountSearch="data">
        <el-col :span="11">
          <el-input-number
            size="mini"
            v-model="query.amount1"
            label=" "
            :controls="false"
          ></el-input-number>
        </el-col>
        <el-col class="line" :span="2">-</el-col>
        <el-col :span="11">
          <el-input-number
            size="mini"
            v-model="query.amount2"
            label=" "
            :controls="false"
          ></el-input-number>
        </el-col>
      </template>
      <template #pv="{ row, index }">
        <el-tag size="mini" type="danger" effect="dark" v-if="row.pv">
          是
        </el-tag>
        <el-tag size="mini" type="info" effect="plain" v-else> 否</el-tag>
      </template>
      <template #materialCodeForm="{ row, size, disabled, index }">
        <span v-if="!row.cost && row.materialId">
          {{ row.materialCode }}
        </span>
        <material-select
          v-else
          v-model="row.materialId"
          :size="size"
          :disabled="disabled"
          @submit="handleMaterialSubmit($event, row)"
        />
      </template>
      <template #serialNo="{ row, index }">
        <flow-timeline-popover
          v-if="row.processInsId"
          :process-ins-id="row.processInsId"
          :form-key="formKey"
          :process-def-key="processDefKey"
          v-model="row.serialNo"
          :flow.sync="row.flow"
          trigger="click"
          lazy
        />
        <span v-else>{{ row.serialNo }}</span>
      </template>
      <template #buyer="{ row, index }">
        <span>{{ column }}</span>
        <el-dropdown trigger="click" @command="rowBuyerChange($event, row)">
          <el-button
            class="el-dropdown-link"
            size="mini"
            type="text"
          >
            {{ row.$buyer }}
          </el-button>
          <i class="el-icon-arrow-down el-icon--right"></i>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
              v-for="item in buyerDict"
              :key="item.dictKey"
              :disabled="item.dictKey === row.buyer"
              :command="item.dictKey"
            >{{ item.dictValue }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </template>
      <template #costForm="{ row }">
        <el-tag size="mini" type="danger" effect="dark" v-if="row.cost">
          费用
        </el-tag>
        <el-tag size="mini" type="info" effect="plain" v-else> 实物</el-tag>
      </template>
      <template #brand="{ row, size }">
        <el-tag
          v-if="row.brand"
          :size="size"
          :effect="row.brand === '1' ? 'dark ' : 'plain'"
        >
          {{ row.$brand }}
        </el-tag>
      </template>
      <template #status="{ row }">
        <el-tag v-if="row.status === 0" size="mini" type="info">
          {{ row.$status }}
        </el-tag>
        <el-tag v-else-if="row.status === 1" size="mini" effect="plain">
          {{ row.$status }}
        </el-tag>
        <el-tag v-else-if="row.status === 2" size="mini" effect="plain">
          {{ row.$status }}
        </el-tag>
        <el-tag v-else-if="row.status === 3" size="mini" type="danger">
          {{ row.$status }}
        </el-tag>
        <el-tag
          v-else-if="row.status === 4"
          size="mini"
          type="warning"
          effect="plain"
        >
          {{ row.$status }}
        </el-tag>
        <el-tag
          v-else-if="row.status === 5"
          size="mini"
          type="warning"
          effect="plain"
        >
          {{ row.$status }}
        </el-tag>
        <el-tag
          v-else-if="row.status === 6"
          size="mini"
          type="danger"
          effect="plain"
        >
          {{ row.$status }}
        </el-tag>
        <el-tag
          v-else-if="row.status === 9"
          size="mini"
          type="success"
          effect="plain"
        >
          {{ row.$status }}
        </el-tag>
        <el-tag
          v-else-if="row.status === 7"
          size="mini"
          type="warning"
          effect="plain"
        >
          {{ row.$status }}
        </el-tag>
      </template>
      <template #projectTitle="{ row }">
        <span v-if="row.projectId != null">{{
            `${row.projectSerialNo}(${row.projectTitle})`
          }}</span>
      </template>
      <template #crash="{ row, size }">
        <el-tag v-if="row.crash" :size="size" type="error" effect="dark"
        >是
        </el-tag>
        <el-tag v-else :size="size" type="info" effect="plain">否</el-tag>
      </template>
      <template #amount="{ row }">
        <span style="color: green; font-weight: bolder">{{ row.amount }}</span>
      </template>
      <template #orderAmount="{ row }">
        <span
          style="color: red; font-weight: bolder"
          v-if="row.orderAmount && Number(row.orderAmount) > Number(row.amount)"
        >{{ row.orderAmount }}</span
        >
        <span style="font-weight: bolder" v-else>{{
            row.orderAmount ? row.orderAmount : 0
          }}</span>
      </template>
      <template #porState="{ row, size }">
        <el-tag
          v-if="row.porState === 1"
          :size="size"
          type="info"
          effect="dark"
        >
          {{ row.$porState }}
        </el-tag>
        <el-tag
          v-else-if="row.porState === 2"
          :size="size"
          type="warning"
          effect="dark"
        >
          {{ row.$porState }}
        </el-tag>
        <el-tag v-else-if="row.porState === 3" :size="size" effect="plan">
          {{ row.$porState }}
        </el-tag>
        <el-tag v-else :size="size" effect="dark">
          {{ row.$porState }}
        </el-tag>
      </template>
      <template #arrival="{ row, size }">
        <el-tag
          v-if="row.arrival === '2'"
          :size="size"
          type="warning"
          effect="dark"
          @click="rowInspection(row)"
        >
          {{ row.$arrival }}
        </el-tag>
        <el-tag
          v-else-if="row.arrival === '3'"
          :size="size"
          effect="dark"
          @click="rowInspection(row)"
        >
          {{ row.$arrival }}
        </el-tag>
        <el-tag
          v-else-if="row.arrival === '1'"
          :size="size"
          type="info"
          effect="dark"
        >
          {{ row.$arrival }}
        </el-tag>
      </template>
      <template #projectIdForm="{ size }">
        <project-select
          v-model="form.projectId"
          :size="size"
          :params="{ status: 9 }"
        />
      </template>
      <template #budgetIdForm="{ size }">
        <un-finish-budget-select
          v-model="form.budgetId"
          :size="size"
          @confirm="handleBudgetConfirm($event, form)"
          @clear="handleBudgetClear"
        />
      </template>
      <template #numForm="{ row, disabled, size }">
        <el-input-number
          v-if="form.crash && !form.budgetId"
          :size="size"
          v-model="row.num"
          :disabled="disabled"
          :min="0"
          controls-position="right"
          style="width: 100%"
          @change="handleNumChange($event, row)"
        />
        <el-input-number
          v-else
          :size="size"
          v-model="row.num"
          :disabled="disabled"
          :min="0"
          controls-position="right"
          :max="row.budgetNum"
          style="width: 100%"
          @change="handleNumChange($event, row)"
        />
      </template>
      <template #menuLeft>
        <el-dropdown @command="handleApply">
          <el-button
            v-if="permission.porapply_apply"
            type="primary"
            size="mini"
            icon="el-icon-plus"
            plain
          >采购申请<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
              v-for="(type, index) in typeDict"
              :key="index"
              :command="type.dictKey"
              v-if="permission[`porapply_apply_${type.dictKey}`]"
            >
              {{ type.dictValue }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-button
          type="danger"
          size="mini"
          icon="el-icon-delete"
          plain
          v-if="permission.porapply_delete"
          @click="handleDelete"
        >删 除
        </el-button>
        <el-button
          size="mini"
          plain
          icon="el-icon-download"
          type="success"
          v-if="permission.porapply_export"
          @click="handleExport"
        >导出
        </el-button>
        <el-dropdown @command="handlePrint">
          <el-button type="warning" size="mini" icon="el-icon-printer" plain
          >打 印<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="work">打印工作表</el-dropdown-item>
            <el-dropdown-item command="danger">易制毒、易制爆化学品申购表</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <!-- <el-button
          type="warning"
          size="mini"
          icon="el-icon-printer"
          plain
          @click="handlePrint"
          >打印工作表
        </el-button> -->
        <el-button
          type="info"
          size="mini"
          icon="el-icon-setting"
          plain
          v-if="userInfo.role_name.includes('admin')"
          @click="handlePvSetting"
        >压力容器设置
        </el-button>
        <!-- 默认收货地 -->
        <el-button
          type="info"
          size="mini"
          icon="el-icon-setting"
          plain
          v-if="userInfo.role_name.includes('admin')"
          @click="handleDefaultAddress"
        >默认收货地
        </el-button>
        <el-divider direction="vertical"></el-divider>
        <el-radio-group v-model="brand" size="mini" @input="onLoad(page)">
          <el-radio-button label="natergy">能特异</el-radio-button>
          <el-radio-button label="yy">演绎</el-radio-button>
        </el-radio-group>
        <el-divider direction="vertical"></el-divider>
        <el-checkbox v-model="auto">自动申购</el-checkbox>
        <el-checkbox v-model="crash">紧急申购</el-checkbox>
      </template>
      <template #menuRight="{ size }">
        <el-button
          icon="el-icon-time"
          circle
          :size="size"
          @click="handleLog"
        ></el-button>
      </template>
      <template #menu="{ row, index, size }">
        <el-button
          type="text"
          icon="el-icon-s-promotion"
          :size="size"
          v-if="[0].includes(row.status) && permission.porapply_apply"
          @click="rowApply(row)"
        >提交
        </el-button>
        <el-button
          type="text"
          icon="el-icon-download"
          :size="size"
          @click="rowAttach(row)"
        >附件
        </el-button>
      </template>
    </avue-crud>
    <inquiry-dialog
      v-if="inquiryShow"
      :show.sync="inquiryShow"
      :title="inquiryTitle"
      :data="form"
      @submit="searchReset"
    />
    <el-drawer
      :visible.sync="detailVisible"
      :title="form.title"
      custom-class="wf-drawer"
      size="100%"
      append-to-body
    >
      <apply-detail
        v-if="detailVisible"
        :taskId="form.taskId"
        :businessKey="form.id"
        :processInstanceId="form.processInsId"
      />
    </el-drawer>
    <attach-dialog ref="attachDialogRef" :detail="attachDetail"/>
    <log-opt-dialog ref="logOptDialogRef" :module="module"/>
    <flow-set-dialog ref="flowSetDialogRef" @confirm="loadDefaultFlowKey"/>
    <order-arrival-list-dialog
      ref="orderArrivalListDialogRef"
      :depot-in="false"
      @afterArrival="onLoad(page)"
    />
    <budget-item-dialog
      ref="porBudgetItemRef"
      multiple
      :params="{ used: false }"
      @confirm="handleItemSelect"
    />
    <material-select-dialog
      ref="materialSelectDialogRef"
      multiple
      @submit="handleItemAddSubmit"
    />
    <el-dialog
      title="压力容器设置"
      append-to-body
      :visible.sync="pvSetting.visible"
      width="555px"
    >
      <avue-form
        :option="pvSetting.option"
        v-model="pvSetting.form"
        @submit="handlePvSettingSubmit"
      >
      </avue-form>
    </el-dialog>
    <dept-default-address-setting-dialog
      ref="deptDefaultAddressSettingDialogRef"
      @confirm="onLoad(page)"
    />
  </basic-container>
</template>

<script>
import {
  add,
  apply,
  changeBuyer,
  getDetail,
  getPage,
  getPvSetting,
  remove,
  submit,
  update,
  updatePvSetting,
} from "@/api/ni/por/apply";
import {mapGetters} from "vuex";
import ApplyItemForm from "@/views/ni/por/components/ApplyItemForm";
import InquiryDialog from "@/views/ni/por/components/InquiryDialog";
import exForm from "@/views/plugin/workflow/mixins/ex-form";
import TaskDetail from "@/views/plugin/workflow/ops/detail";
import AttachDialog from "@/components/attach-dialog";
import BudgetSelect from "@/views/ni/por/components/BudgetSelect";
import ProjectSelect from "@/views/ni/project/components/MyProjectSelect";
import ApplyDetail from "@/views/ni/por/apply-detail";
import {getByModule} from "@/api/ni/base/module-flow";
import FlowSetDialog from "@/components/flow-set-dialog";
import {detail as getParamDetail} from "@/api/system/param";
import LogOptDialog from "@/components/log-opt-dialog";
import MaterialSelect from "@/views/ni/base/components/MaterialSelect";
import OrderArrivalListDialog from "@/views/ni/por/components/OrderArrivalListDialog";
import UnFinishBudgetSelect from "@/views/ni/por/components/UnFinishBudgetSelect";
import BudgetItemDialog from "@/views/ni/por/components/BudgetItemDialog";
import MaterialSelectDialog from "@/views/ni/base/components/MaterialSelectDialog";
import {download} from "@/api/resource/attach";
import {downloadFileBlob} from "@/util/util";
import {hiprint} from "vue-plugin-hiprint";
import {loadPrintTemplate} from "@/api/system/printTemplate";
import FlowTimelinePopover from "@/components/wf-flow-timeline/popover";
import DeptDefaultAddressSettingDialog from "@/views/ni/por/components/DeptDefaultAddressSettingDialog.vue";

export default {
  mixins: [exForm],
  components: {
    BudgetItemDialog,
    UnFinishBudgetSelect,
    ApplyItemForm,
    InquiryDialog,
    TaskDetail,
    AttachDialog,
    MaterialSelect,
    BudgetSelect,
    ProjectSelect,
    ApplyDetail,
    FlowSetDialog,
    LogOptDialog,
    OrderArrivalListDialog,
    MaterialSelectDialog,
    FlowTimelinePopover,
    DeptDefaultAddressSettingDialog
  },
  data() {
    return {
      itemDialogShow: false,
      activeName: "items",
      detailVisible: false,
      module: "ni_por_apply",
      processDefKey: "process_por_apply",
      formKey: "wf_ex_por/Apply",
      inquiryTitle: "",
      inquiryShow: false,
      inquiriesAmount: -1,
      form: {
        items: [],
      },
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      needInquiry: true,
      selectionList: [],
      option: {
        editBtn: false,
        addBtn: false,
        searchEnter: true,
        span: 6,
        dialogFullscreen: true,
        searchLabelWidth: 110,
        labelWidth: 110,
        size: "mini",
        searchSize: "mini",
        align: "center",
        searchIndex: 3,
        searchIcon: true,
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: false,
        viewBtn: false,
        selection: true,
        dialogClickModal: false,
        column: [
          {
            label: "状态",
            prop: "status",
            dicData: [],
            search: true,
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            dataType: "number",
            display: false,
            type: "select",
          },
          {
            label: "申请人",
            prop: "createUserName",
            addDisplay: false,
            editDisplay: false,
            overHidden: true,
            search: true,
            searchOrder: 99,
          },
          {
            label: "申请部门",
            prop: "createDeptName",
            addDisplay: false,
            editDisplay: false,
            overHidden: true,
            search: true,
            searchOrder: 98,
          },
          {
            label: "申请时间",
            prop: "createTime",
            type: "date",
            minWidth: 127,
            overHidden: true,
            display: false,
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            search: true,
            searchRange: true,
          },
          {
            label: "采购编号",
            prop: "serialNo",
            overHidden: true,
            minWidth: 135,
            search: true,
            searchOrder: 97,
            disabled: true,
            placeholder: "系统自动生成",
          },
          {
            label: "采购主题",
            prop: "title",
            placeholder: " ",
            overHidden: true,
            minWidth: 135,
            search: true,
            rules: [
              {
                required: true,
                message: "请输入",
                trigger: "blur",
              },
            ],
          },
          {
            label: "采购分类",
            prop: "type",
            type: "select",
            dicData: [],
            overHidden: true,
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            placeholder: " ",
            search: true,
          },
          {
            label: "关联项目",
            prop: "projectId",
            placeholder: " ",
            hide: true,
            showColumn: false,
            display: false,
            rules: [
              {
                required: true,
                message: "请输入",
                trigger: "blur",
              },
            ],
          },
          {
            label: "关联项目",
            prop: "projectTitle",
            overHidden: true,
            placeholder: " ",
            disabled: true,
          },
          {
            label: "关联预算",
            prop: "budgetSerialNo",
            display: false,
            minWidth: 117,
            overHidden: true,
          },
          {
            label: "账套",
            prop: "brand",
            type: "radio",
            minWidth: 90,
            value: "1",
            dicData: [],
            disabled: true,
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            search: true,
          },
          {
            label: "关联预算",
            prop: "budgetId",
            type: "select",
            dicUrl: "/api/ni/por/budget/page?status=9&keyword={{key}}",
            remote: true,
            props: {
              label: "serialNo",
              value: "id",
              desc: "title",
            },
            dicFormatter: (data) => {
              return data.data.records;
            },
            placeholder: " ",
            filterable: true,
            overHidden: true,
            search: true,
            hide: true,
            showColumn: false,
            rules: [
              {
                required: true,
                message: "请输入",
                trigger: "blur",
              },
            ],
            change: ({value}) => {
              const amountCoulmn = this.findObject(
                this.option.group[0].column[0].children.column,
                "amount"
              );
              if (value || !this.form.crash) {
                amountCoulmn.disabled = true;
              } else {
                amountCoulmn.disabled = false;
              }
            },
          },
          {
            label: "紧急申购",
            prop: "crash",
            labelTip: "紧急申购无需比价",
            type: "radio",
            value: false,
            dicData: [
              {
                label: "是",
                value: true,
              },
              {
                label: "否",
                value: false,
              },
            ],
            rules: [
              {
                required: true,
                message: "请输入",
                trigger: "blur",
              },
            ],
            change: ({value}) => {
              const tipColumn = this.findObject(this.option.column, "tip");
              const budgetIdColumn = this.findObject(
                this.option.column,
                "budgetId"
              );
              const projectIdColumn = this.findObject(
                this.option.column,
                "projectId"
              );
              const projectTitleColumn = this.findObject(
                this.option.column,
                "projectTitle"
              );
              const amountCoulmn = this.findObject(
                this.option.group[0].column[0].children.column,
                "amount"
              );
              if (value) {
                tipColumn.display = false;
                budgetIdColumn.rules = [
                  {
                    required: false,
                    message: "请输入",
                    trigger: "blur",
                  },
                ];
                projectIdColumn.display = true;
                projectTitleColumn.display = false;
                if (!this.form.budgetId) {
                  amountCoulmn.disabled = false;
                } else {
                  amountCoulmn.disabled = true;
                }
              } else {
                tipColumn.display = true;
                budgetIdColumn.rules = [
                  {
                    required: true,
                    message: "请输入",
                    trigger: "blur",
                  },
                ];
                projectIdColumn.display = false;
                projectTitleColumn.display = true;
                amountCoulmn.disabled = true;
              }
            },
          },
          {
            label: "压力容器",
            prop: "pv",
            type: "radio",
            placeholder: " ",
            value: 0,
            disabled: true,
            width: 70,
            dicData: [
              {
                label: "是",
                value: 1,
              },
              {
                label: "否",
                value: 0,
              },
            ],
            rules: [
              {
                required: true,
                message: "请选择是否压力容器",
                trigger: "blur",
              },
            ],
            change: ({value}) => {
              this.form.items.forEach((item) => {
                item.pv = value;
              });
            },
          },
          {
            label: "需用日期",
            prop: "needDate",
            overHidden: true,
            minWidth: 90,
            type: "date",
            rules: [
              {
                required: true,
                message: "请输入",
                trigger: "blur",
              },
            ],
            pickerOptions: {
              disabledDate(time) {
                return time.getTime() <= Date.now();
              },
            },
          },
          {
            label: "采购方式",
            prop: "buyer",
            type: "radio",
            value: "1",
            dicData: [],
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            dataType: "number",
            search: true,
            minWidth: 90,
            rules: [
              {
                required: true,
                message: "请选择采购方式",
                trigger: "blur",
              },
            ],
          },
          {
            label: "金额",
            prop: "amount",
            type: "number",
            minWidth: 90,
            fixed: "right",
            placeholder: " ",
            addDisplay: false,
            disabled: true,
            overHidden: true,
            controls: false,
            search: true,
          },
          {
            label: "自动申购",
            prop: "auto",
            type: "select",
            dicData: [
              {
                label: "是",
                value: true,
              },
              {
                label: "否",
                value: false,
              },
            ],
            display: false,
            minWidth: 80,
            placeholder: " ",
            overHidden: true,
          },
          {
            label:'收货地',
            prop: "receivingAddress",
            type: "radio",
            dicUrl: "/api/blade-system/dict-biz/dictionary?code=ni_por_receiving_address",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            clearable: false,
          },
          {
            label: "备注",
            overHidden: true,
            hide: true,
            prop: "remark",
            type: "textarea",
            span: 24,
            minRows: 3,
          },
          {
            label: "附件上传",
            prop: "attach",
            type: "upload",
            drag: true,
            loadText: "模板上传中，请稍等",
            span: 24,
            propsHttp: {
              id: "attachId",
              res: "data",
              url: "attachId",
              name: "originalName",
            },
            limit: 10,
            hide: true,
            viewDisplay: false,
            addDisplay: true,
            editDisplay: true,
            action: "/api/blade-resource/oss/endpoint/put-file-attach",
          },
          {
            label: "采购金额",
            prop: "orderAmount",
            type: "number",
            minWidth: 100,
            overHidden: true,
            fixed: "right",
            display: false,
          },
          {
            label: "采购状态",
            prop: "porState",
            type: "select",
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_por_apply_por_state",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            dataType: "number",
            display: false,
            minWidth: 82,
            fixed: "right",
            search: true,
            rules: [
              {
                required: true,
                message: "请输入",
                trigger: "blur",
              },
            ],
            change: ({value}) => {
              this.needInquiry = value;
            },
          },
          {
            label: "采购人",
            prop: "porUserNames",
            display: false,
            minWidth: 82,
          },
          {
            label: "到货情况",
            prop: "arrival",
            type: "select",
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_por_apply_arrival",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            display: false,
            minWidth: 82,
            fixed: "right",
            search: true,
          },
          {
            label: "",
            labelWidth: 20,
            type: "title",
            prop: "tip",
            display: true,
            hide: true,
            showColumn: false,
            span: 24,
            styles: {
              color: "red",
              fontSize: "18px",
            },
          },
        ],
        group: [
          {
            label: "采购明细",
            column: [
              {
                labelWidth: 0,
                label: "",
                prop: "items",
                span: 24,
                type: "dynamic",
                children: {
                  size: "mini",
                  addBtn: true,
                  showSummary: true,
                  sumColumnList: [
                    {
                      name: "num",
                      type: "sum",
                      decimals: 1,
                    },
                    {
                      name: "amount",
                      type: "sum",
                    },
                  ],
                  rowAdd: () => {
                    if (!this.form.budgetId && !this.form.crash) {
                      this.$message.warning("请选择预算");
                      return;
                    }
                    if (this.form.crash && !this.form.budgetId) {
                      this.$refs.materialSelectDialogRef.visible = true;
                    } else {
                      this.$refs.porBudgetItemRef.init(this.form.budgetId);
                    }
                  },
                  align: "center",
                  headerAlign: "center",
                  column: [
                    {
                      label: "类型",
                      prop: "cost",
                      placeholder: " ",
                      width: 70,
                      disabled: true,
                    },
                    {
                      label: "品名",
                      placeholder: " ",
                      prop: "materialName",
                      rules: [
                        {
                          required: true,
                          message: "请输入品名",
                          trigger: "blur",
                        },
                      ],
                    },
                    {
                      label: "产品编码",
                      prop: "materialCode",
                      placeholder: " ",
                    },
                    {
                      label: "规格型号",
                      placeholder: " ",
                      prop: "specification",
                      disabled: true,
                    },
                    {
                      label: "材质",
                      prop: "quality",
                      overHidden: true,
                      disabled: true,
                    },
                    {
                      label: "国标",
                      prop: "gb",
                      placeholder: " ",
                      disabled: true,
                    },
                    {
                      label: "单位",
                      prop: "unit",
                      overHidden: true,
                      type: "select",
                      placeholder: " ",
                      value: "1",
                      disabled: true,
                      dicUrl:
                        "/api/blade-system/dict-biz/dictionary?code=ni_material_unit",
                      props: {
                        label: "dictValue",
                        value: "dictKey",
                      },
                      slot: true,
                    },
                    {
                      label: "预算数量",
                      prop: "budgetNum",
                      disabled: true,
                      type: "number",
                      placeholder: " ",
                      minWidth: 100,
                    },
                    {
                      label: "已申请数量",
                      prop: "usedNum",
                      type: "number",
                      disabled: true,
                      placeholder: " ",
                      minWidth: 100,
                    },
                    {
                      label: "数量",
                      prop: "num",
                      type: "number",
                      placeholder: " ",
                      minWidth: 100,
                      cell: true,
                      rules: [
                        {
                          required: true,
                          message: "请输入数量",
                          trigger: "blur",
                        },
                      ],
                    },
                    {
                      label: "单价",
                      prop: "price",
                      type: "number",
                      disabled: true,
                      controls: false,
                      precision: 2,
                      placeholder: " ",
                      cell: true,
                    },
                    {
                      label: "金额",
                      prop: "amount",
                      overHidden: true,
                      controls: false,
                      type: "number",
                      minWidth: 100,
                      precision: 2,
                      placeholder: " ",
                      rules: [
                        {
                          required: true,
                          message: "请输入金额",
                          trigger: "blur",
                        },
                      ],
                      change: ({row, value}) => {
                        const that = this;
                        if (row.num) {
                          row.price = value / row.num;
                        } else {
                          row.price = 0;
                        }
                        let amount = 0;
                        that.form.items.forEach((item) => {
                          amount += item.amount;
                        });
                        that.form.amount = amount;
                      },
                    },
                    {
                      label: "备注",
                      prop: "remark",
                      type: "textarea",
                      minRows: 1,
                      placeholder: " ",
                    },
                  ],
                },
              },
            ],
          },
          {
            label: "比价",
            column: [
              {
                label: "采购原因",
                prop: "reason",
                type: "textarea",
                minRows: 1,
                placeholder: " ",
              },
              {
                label: "采购风险",
                prop: "risk",
                type: "textarea",
                minRows: 1,
                placeholder: " ",
              },
              {
                label: "采购带来的收益及效果",
                prop: "profit",
                span: 24,
                type: "textarea",
                minRows: 2,
                placeholder: " ",
              },
              {
                label: "项目安全性分析及风险控制",
                prop: "security",
                span: 24,
                minRows: 2,
                type: "textarea",
                placeholder: " ",
              },
              {
                label: "项目环保因素分析及风险控制",
                prop: "ep",
                span: 24,
                minRows: 2,
                type: "textarea",
                placeholder: " ",
              },
              {
                label: "供应商比价",
                prop: "inquiries",
                span: 24,
                type: "dynamic",
                children: {
                  span: 8,
                  align: "center",
                  headerAlign: "center",
                  rowAdd: (done) => {
                    const row = {
                      applyId: this.form.id,
                    };
                    done(row);
                  },
                  column: [
                    {
                      label: "供应商名称",
                      prop: "supplier",
                      placeholder: " ",
                      type: "textarea",
                      minRows: 1,
                      rules: [
                        {
                          required: true,
                          message: "请填写供应商名称",
                          trigger: "blur",
                        },
                      ],
                    },
                    {
                      label: "联系方式",
                      prop: "supplierLinkman",
                      placeholder: " ",
                      type: "textarea",
                      minRows: 1,
                      rules: [
                        {
                          required: true,
                          message: "请填写供应商联系人",
                          trigger: "blur",
                        },
                      ],
                    },

                    {
                      label: "比价详情",
                      prop: "remark",
                      type: "textarea",
                      minRows: 1,
                      placeholder: " ",
                      rules: [
                        {
                          required: true,
                          message: "请填写比价详情",
                          trigger: "blur",
                        },
                      ],
                    },
                    {
                      label: "建议供应商",
                      prop: "recommend",
                      type: "radio",
                      dicData: [
                        {
                          label: "是",
                          value: true,
                        },
                        {
                          label: "否",
                          value: false,
                        },
                      ],
                      placeholder: " ",
                      rules: [
                        {
                          required: true,
                          message: "请选择建议供应商",
                          trigger: "blur",
                        },
                      ],
                      change: ({value, index}) => {
                        if (value)
                          this.form.inquiries.forEach((item, i) => {
                            if (i === index) {
                              return;
                            }
                            item.recommend = false;
                          });
                      },
                    },
                  ],
                },
              },
            ],
          },
        ],
      },
      data: [],
      typeDict: [],
      typeDictKeyValue: {},
      buyerDict: [],
      buyerDictKeyValue: {},
      unitDictKeyValue: {},
      brandDictKeyValue: {},
      statusDictKeyValue: {},
      attachDetail: false,
      itemPrintTemplate: null,
      dangerPrintTemplate: null,
      auto: false,
      crash: false,
      brand: "natergy",
      pvSetting: {
        visible: false,
        option: {
          span: 24,
          labelWidth: 150,
          size: "mini",
          searchSize: "mini",
          emptyBtn: false,
          column: [
            {
              label: "发起人",
              labelTip: "只有选中的人可以发起",
              prop: "applyUser",
              type: "select",
              remote: true,
              dicUrl: `/api/blade-user/search/otj/user?name={{key}}`,
              props: {
                label: "realName",
                value: "account",
              },
              multiple: true,
              dicFormatter: (data) => {
                return data.data.records;
              },
              placeholder: " ",
              rules: [
                {
                  required: true,
                  message: "请选择发起人",
                  trigger: "blur",
                },
              ],
            },
            {
              label: "到货验收人",
              labelTip: "压力容器默认的审核人",
              prop: "arrivalUser",
              type: "select",
              remote: true,
              dicUrl: `/api/blade-user/search/otj/user?name={{key}}`,
              props: {
                label: "realName",
                value: "account",
              },
              dicFormatter: (data) => {
                return data.data.records;
              },
              placeholder: " ",
              rules: [
                {
                  required: true,
                  message: "请选择到货验收人",
                  trigger: "blur",
                },
              ],
            },
          ],
        },
        form: {},
      },
      export: {
        column: [
          {
            label: "状态",
            prop: "status",
            width: 80,
          },
          {
            label: "申请人",
            prop: "createUserName",
            width: 95,
          },
          {
            label: "申请部门",
            prop: "createDeptName",
            width: 80,
          },
          {
            label: "申请时间",
            prop: "createTime",
            width: 70,
          },
          {
            label: "申请编号",
            prop: "serialNo",
            width: 90,
          },
          {
            label: "申请主题",
            prop: "title",
            width: 90,
          },
          {
            label: "采购分类",
            prop: "type",
            width: 90,
          },
          {
            label: "关联项目",
            prop: "projectSerialNo",
            width: 90,
          },
          {
            label: "关联预算",
            prop: "budgetSerialNo",
            width: 90,
          },
          {
            label: "账套",
            prop: "brand",
            width: 70,
          },

          {
            label: "紧急申购",
            width: 85,
            prop: "crash",
          },
          {
            label: "压力容器",
            prop: "pv",
            width: 90,
          },
          {
            label: "需用日期",
            prop: "needDate",
            width: 90,
          },
          {
            label: "采购方式",
            prop: "buyer",
            width: 90,
          },
          {
            label: "自动申购",
            prop: "auto",
            width: 90,
          },
          {
            label: "采购人",
            prop: "porUserNames",
            width: 90,
          },
          {
            label: "申请金额",
            prop: "amount",
            width: 90,
          },
        ],
      },
    };
  },
  created() {
    loadPrintTemplate("ni_por_apply").then((res) => {
      this.itemPrintTemplate = JSON.parse(res.data.data.content);
    });
    loadPrintTemplate("ni_por_apply_danger").then((res) => {
      this.dangerPrintTemplate = JSON.parse(res.data.data.content);
    });
    getParamDetail({paramKey: "por.pv.apply.user"}).then((res) => {
      const applyUser = res.data.data.paramValue;
      const pv = this.findObject(this.option.column, "pv");
      pv.disabled = !applyUser.includes(this.userInfo.user_name);
    });
    this.onLoad(this.page);
  },
  beforeRouteEnter(to, from, next) {
    if (!to.meta.keepAlive) to.meta.keepAlive = true;
    if (from.path === "/workflow/process/external/por/Apply/start") {
      console.log(from);
      next((vm) => {
        vm.page.currentPage = 1;
        vm.onLoad(vm.page);
      });
      return;
    }
    next();
  },
  computed: {
    ...mapGetters(["permission", "userInfo"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.porapply_add, false),
        viewBtn: this.vaildData(this.permission.porapply_view, false),
        delBtn: this.vaildData(this.permission.porapply_delete, false),
        editBtn: this.vaildData(this.permission.porapply_edit, false),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
    itemData() {
      return this.form.items || [];
    },
  },
  mounted() {
    this.dictInit();
  },
  watch: {
    auto: {
      handler(val) {
        if (val) {
          this.query.auto = 1;
        } else {
          this.query.auto = null;
        }
        this.page.currentPage = 1;
        this.onLoad(this.page);
      },
      immediate: false,
    },
    crash: {
      handler(val) {
        if (val) {
          this.query.crash = true;
        } else {
          this.query.crash = null;
        }
        this.page.currentPage = 1;
        this.onLoad(this.page);
      },
      immediate: false,
    },
  },
  methods: {
    handleDefaultAddress() {
      this.$refs.deptDefaultAddressSettingDialogRef.onSetting()
    },
    handleAutoChange() {
      this.page.currentPage = 1;
      this.onLoad(this.page);
    },
    handlePvSetting() {
      this.pvSetting.visible = true;
      getPvSetting().then((res) => {
        const {data} = res.data;
        this.pvSetting.form = data;
      });
    },
    handlePvSettingSubmit(form, done) {
      updatePvSetting(form)
        .then(() => {
          this.pvSetting.visible = false;
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        })
        .finally(() => {
          done();
        });
    },
    async handlePrint(type) {
      if (type == "work") {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择要打印的数据");
          return;
        }
        if (!this.itemPrintTemplate) {
          this.$message.error("打印模板加载失败，请联系管理员");
          return;
        }

        const res = await getDetail(this.selectionList[0].id);
        const printData = res.data.data;
        const inquiries = printData.items.filter(
          (item) => item.inquiries && item.inquiries.length > 0
        );
        if (!inquiries || inquiries.length <= 0) {
          this.$message.warning("该申请无比价信息");
          return;
        }
        let printDataList = [];
        inquiries.forEach((item) => {
          const items = [
            {
              ...item,
              cost: item.cost ? "费用" : "实物",
              unit: this.unitDictKeyValue[item.unit],
              remark:
                item.remark && item.remark.length > 50
                  ? item.remark.substr(0, 50) + "..."
                  : item.remark,
            },
          ];
          const data = {
            ...printData,
            type: this.typeDictKeyValue[printData.type],
            amountType: printData.amount < 100000 ? "十万元以下" : "十万元以上",
            security: item.security,
            environment: item.ep ? item.ep : "",
            reason: item.reason ? item.reason : "",
            profit: item.profit ? item.profit : "",
            risk: item.risk ? item.risk : "",
            ZGSuggestion: item.comments1 ? item.comments1 : "",
            GYSuggestion: item.comments2 ? item.comments2 : "",
            MaSuggestion: item.comments3 ? item.comments3 : "",
            ZJBSuggestion: item.comments4 ? item.comments4 : "",
            items,
          };
          item.inquiries.forEach((inquiry, index) => {
            data[`inquiries${index + 1}`] =
              inquiry.supplier +
              "<br>" +
              inquiry.supplierLinkman +
              "<br>" +
              inquiry.remark;
            if (inquiry.recommend) {
              data.recommend +=
                inquiry.supplier + "(" + inquiry.supplierLinkman + ")" + "<br>";
            }
          });
          printDataList.push(data);
        });
        const hiprintTemplate = new hiprint.PrintTemplate({
          template: this.itemPrintTemplate,
        });
        hiprintTemplate.print(printDataList);
      } else if (type == "danger") {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择要打印的数据");
          return;
        } else if (this.selectionList.length > 1) {
          this.$message.warning("只能选择一条数据");
          return;
        }

        let hiprintTemplate;
        const data = await getDetail(this.selectionList[0].id);
        console.log(data);
        const printData = {
          createTime: this.selectionList[0].createTime.split(' ')[0],
          title: this.selectionList[0].title,
          type: this.typeDictKeyValue[this.selectionList[0].type],
          brand: this.brandDictKeyValue[this.selectionList[0].brand],
          pv: this.selectionList[0].pv ? "是" : "否",
          createUserName: this.selectionList[0].createUserName,
          createDeptName: this.selectionList[0].createDeptName,
          porUserNames: this.selectionList[0].porUserNames,
          buyer: this.buyerDictKeyValue[this.selectionList[0].buyer],
          budgetSerialNo: this.selectionList[0].budgetSerialNo,
          projectSerialNo: this.selectionList[0].projectSerialNo,
          serialNo: this.selectionList[0].serialNo,
          items: data.data.data.items,
        };
        printData.items.forEach((item) => {
          if (item.remark && item.remark.length > 7) {
            item.remark = item.remark.substr(0, 7) + "...";
          }
          if (item.purpose && item.purpose.length > 15) {
            item.purpose = item.purpose.substr(0, 15) + "...";
          }
          if (item.unit != null) {
            item.unit = this.unitDictKeyValue[item.unit];
          }
        });
        hiprintTemplate = new hiprint.PrintTemplate({
          template: this.dangerPrintTemplate,
        });
        hiprintTemplate.print(printData);
      }
    },

    uploadAfter(res, done) {
      window.console.log(res);
      done();
    },
    uploadPreview(file) {
      console.log(file);
      download(file.url).then((res) => {
        let fileName = decodeURI(
          res.headers["content-disposition"].split("=")[1]
        );
        console.log(res.headers["content-disposition"]);
        console.log(fileName);
        downloadFileBlob(res.data, fileName);
      });
    },
    handleItemAddSubmit(selectList) {
      if (selectList) {
        selectList.forEach((item) => {
          const row = {
            typeId: item.typeId,
            materialCode: item.code,
            materialName: item.name,
            materialTypeId: item.typeId,
            materialId: item.id,
            specification: item.specification,
            unit: item.unit,
          };
          this.form.items.push(row);
        });
      }
    },
    handleItemSelect(selectionList) {
      this.form.items = selectionList.map((item) => {
        return {
          budgetItemId: item.id,
          materialCode: item.materialCode,
          materialName: item.materialName,
          materialId: item.materialId,
          specification: item.specification,
          gb: item.gb,
          unit: item.unit,
          num: item.num,
          budgetNum: item.num,
          usedNum: item.applyNum ? item.applyNum : 0,
          amount: item.amount,
          price: item.price,
          unitDicData: item.unitDicData,
          remark: item.remark,
        };
      });
    },
    handleNumChange(num, row) {
      const that = this;
      if (row.price) {
        row.amount = Number(row.price) * Number(row.num);
      }
      let amount = 0;
      that.form.items.forEach((item) => {
        amount += Number(item.amount ? item.amount : 0);
      });
      that.form.amount = amount;
    },
    rowInspection(row) {
      this.$refs.orderArrivalListDialogRef.initByApplyId(row.id);
    },
    handleLog() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择要查看的数据");
        return;
      } else if (this.selectionList.length > 1) {
        this.$message.warning("只能选择一条数据");
        return;
      }
      this.$refs.logOptDialogRef.init(this.selectionList[0].id);
    },
    loadDefaultFlowKey() {
      getByModule(this.module).then((res) => {
        const moduleFlow = res.data.data;
        if (moduleFlow) {
          this.processDefKey = moduleFlow.flowKey;
        }
      });
    },
    handleBudgetClear() {
      this.form.budgetId = null;
      this.form.items = [];
      const amountColumn = this.findObject(
        this.option.group[0].column[0].children.column,
        "amount"
      );
      if (this.form.crash) {
        amountColumn.disabled = false;
      } else {
        amountColumn.disabled = true;
      }
    },
    handleBudgetConfirm(selectList, row) {
      if (selectList && selectList.length > 0) {
        row.projectId = selectList[0].projectId;
        row.projectTitle = selectList[0].projectTitle;
        row.projectSerialNo = selectList[0].projectSerialNo;
        row.brand = selectList[0].brand;
      }
    },
    handleMaterialSubmit(row, row1) {
      row1.materialCode = row.code;
      row1.materialName = row.name;
      row1.materialId = row.id;
      row1.specification = row.specification;
      row1.gb = row.gb;
      row1.unit = row.unit;
    },
    rowAttach(row) {
      if (row.status > 0) {
        this.attachDetail = true;
      } else {
        this.attachDetail = false;
      }
      this.$refs.attachDialogRef.init(row.id, this.module);
    },
    rowApply(row) {
      this.$confirm("此操作将提交该数据，是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        center: true,
      }).then(() => {
        submit(row.id, this.processDefKey).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
      });
    },
    handleApply(type) {
      const form = {type};
      //易制毒、易制爆化学品
      if (type === '21') {
        this.$confirm('易制毒、易制爆化学品申购需打印纸质申购单！', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.dynamicRoute(
            {
              processDefKey: this.processDefKey,
              formKey: this.formKey,
              form: encodeURIComponent(
                Buffer.from(JSON.stringify(form)).toString("utf8")
              ),
            },
            "start"
          );
        })
        return;
      }
      this.dynamicRoute(
        {
          processDefKey: this.processDefKey,
          formKey: this.formKey,
          form: encodeURIComponent(
            Buffer.from(JSON.stringify(form)).toString("utf8")
          ),
        },
        "start"
      );
    },
    dictInit() {
      this.$http
        .get("/api/blade-system/dict/dictionary?code=data_status")
        .then((res) => {
          const column = this.findObject(this.option.column, "status");
          column.dicData = res.data.data;
          this.statusDictKeyValue = res.data.data.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });
      this.$http
        .get("/api/blade-system/dict-biz/dictionary?code=ni_material_unit")
        .then((res) => {
          const unitDict = res.data.data;
          this.unitDictKeyValue = unitDict.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });
      this.$http
        .get("/api/blade-system/dict-biz/dictionary?code=ni_por_buyer")
        .then((res) => {
          const column = this.findObject(this.option.column, "buyer");
          this.buyerDict = res.data.data;
          column.dicData = this.buyerDict;
          this.buyerDictKeyValue = res.data.data.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });
      this.$http
        .get("/api/blade-system/dict-biz/dictionary?code=ni_brand")
        .then((res) => {
          const column = this.findObject(this.option.column, "brand");
          column.dicData = res.data.data;
          this.brandDictKeyValue = res.data.data.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });
      this.$http
        .get("/api/blade-system/dict-biz/dictionary?code=ni_por_type")
        .then((res) => {
          const column = this.findObject(this.option.column, "type");
          this.typeDict = res.data.data;
          this.typeDict = res.data.data.map((item) => {
            const i = {...item};
            //禁用原材料/包装物/费用采购
            if (["1", "2", "6"].includes(i.dictKey)) {
              i.disabled = true;
            }
            return i;
          });
          column.dicData = this.typeDict;
          this.typeDictKeyValue = this.typeDict.reduce((acc, cur) => {
            acc[cur.dictKey] = cur.dictValue;
            return acc;
          }, {});
        });
    },
    itemRowUpdate(form, index, done, loading) {
      loading();
      done();
    },
    rowBuyerChange(buyer, row) {
      changeBuyer(row.id, buyer).then(() => {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!",
        });
      });
    },
    rowSave(row, done, loading) {
      //TODO 如果row的status==1 则走提交申请
      //如果采购金额超过2000，则需要比价
      let amount = 0;
      if (row.items != null) {
        row.items.forEach((item) => {
          amount += Number(item.amount);
        });
      }
      row.amount = amount;
      let res;
      if (
        !this.form.crash &&
        this.inquiriesAmount > 0 &&
        amount >= this.inquiriesAmount &&
        (this.form.inquiries == null || this.form.inquiries.length < 1)
      ) {
        this.$confirm(
          `采购金额超过${this.inquiriesAmount},还未比价，是否提交!?`,
          {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }
        )
          .then(() => {
            if (row.status === 1) {
              res = apply(row, this.processDefKey);
            } else {
              res = add(row);
            }
          })
          .catch(() => {
            loading();
            return;
          });
        return;
      }
      if (row.status === 1) {
        res = apply(row, this.processDefKey);
      } else {
        res = add(row);
      }
      res.then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          window.console.log(error);
        }
      );
    },
    rowUpdate(row, index, done, loading) {
      let amount = 0;
      if (row.items != null) {
        row.items.forEach((item) => {
          amount += Number(item.amount);
        });
      }
      row.amount = amount;
      if (
        !this.form.crash &&
        this.inquiriesAmount > 0 &&
        amount >= this.inquiriesAmount &&
        (this.form.inquiries == null || this.form.inquiries.length < 1)
      ) {
        this.$confirm(
          `采购金额超过${this.inquiriesAmount},还未比价，是否提交!?`,
          {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }
        )
          .then(() => {
            if (row.status === 1) {
              apply(row, this.processDefKey).then(
                () => {
                  this.onLoad(this.page);
                  this.$message({
                    type: "success",
                    message: "操作成功!",
                  });
                  done();
                },
                (error) => {
                  loading();
                  window.console.log(error);
                }
              );
            } else {
              update(row).then(
                () => {
                  this.onLoad(this.page);
                  this.$message({
                    type: "success",
                    message: "操作成功!",
                  });
                  done();
                },
                (error) => {
                  loading();
                  console.log(error);
                }
              );
            }
          })
          .catch(() => {
            loading();
            return;
          });
        return;
      }
      // 如果row的status==1 则走提交申请
      if (row.status === 1) {
        apply(row, this.processDefKey).then(
          () => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!",
            });
            done();
          },
          (error) => {
            loading();
            window.console.log(error);
          }
        );
      } else {
        update(row).then(
          () => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!",
            });
            done();
          },
          (error) => {
            loading();
            console.log(error);
          }
        );
      }
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    handleInquiry(row) {
      if (row.inquiry)
        getDetail(row.id).then((res) => {
          this.form = res.data.data;
          this.inquiryTitle = row.title + "-询价";
          this.inquiryShow = true;
        });
    },
    async handleExport() {
      let msg =
        '是否导出<span style="color: #F56C6C;font-weight: bold">所有数据</span>?';
      if (this.selectionList.length > 0) {
        msg =
          '是否要导出<span style="color: #F56C6C;font-weight: bold">当前选中的数据</span>？';
      }
      let data = [];
      this.$confirm(msg, {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        dangerouslyUseHTMLString: true,
        type: "warning",
      }).then(async () => {
        if (this.selectionList.length > 0) {
          data = this.selectionList;
        } else {
          const query = {...this.query};
          query.descs = "id";
          if (this.brand === "natergy") {
            query.brand = "1,2";
          } else if (this.brand === "yy") {
            query.brand = "4";
          }
          if (query.createTime && query.createTime.length === 2) {
            query.startCreateDate = query.createTime[0];
            query.endCreateDate = query.createTime[1];
            query.createTime = null;
          }
          const res = await getPage(1, 1000000, query);
          data = res.data.data.records.map((item) => ({...item, flow: []}));
        }
        this.$Export.excel({
          title: "采购申请",
          columns: this.export.column,
          data: data.map((item) => {
            return {
              ...item,
              type: this.typeDictKeyValue[item.type],
              brand: this.brandDictKeyValue[item.brand],
              status: this.statusDictKeyValue[item.status],
              crash: item.crash ? "是" : "否",
              pv: item.pv && item.pv === 1 ? "是" : "否",
              auto: item.auto ? "是" : "否",
              buyer: this.buyerDictKeyValue[item.buyer],
              amount: Number(item.amount).toFixed(2),
            };
          }),
        });
      });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen(done, type) {
      if ("add" === type) {
        this.form.crash = false;
        this.form.inquiry = true;
        this.form.status = 9;
        this.form.porState = 1;
        this.form.auto = false;
        detail({paramKey: "ni.por.apply.inquiries.amount"}).then((res) => {
          this.inquiriesAmount = Number(res.data.data.paramValue);
          this.form.tip = `采购金额大于 ${this.inquiriesAmount}元，需比价!`;
        });
      } else if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then((res) => {
          this.form = res.data.data;
        });
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.auto = false;
      this.crash = false;
      this.page.currentPage = 1;
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      const query = Object.assign(params, this.query);
      query.descs = "id";
      if (this.brand === "natergy") {
        query.brand = "1,2";
      } else if (this.brand === "yy") {
        query.brand = "4";
      }
      if (query.createTime && query.createTime.length === 2) {
        query.startCreateDate = query.createTime[0];
        query.endCreateDate = query.createTime[1];
        query.createTime = null;
      }
      getPage(page.currentPage, page.pageSize, query).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records.map((item) => ({...item, flow: []}));
        this.loading = false;
        this.selectionClear();
      });
    },
    cellStyle({row, column}) {
      if ("createUserName" === column.columnKey && row.crash) {
        return {
          backgroundColor: '#F56C6C',
          color: '#fff'
        };
      }
      if ("type" === column.columnKey && row.type === '21') {
        return {
          background: 'url(/img/yzdyzb.png) no-repeat left center / contain',
          backgroundColor: '#F56C6C',
          color: '#fff'
        };
      }
    },
  },
};
</script>

<style scoped>
.inquiry-badge {
  margin-top: 10px;
  /*margin-right: 20px;*/
}
</style>

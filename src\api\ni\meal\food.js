import request from "@/router/axios";

export const getList = (params) => {
  return request({
    url: "/api/ni/meal/food/list",
    method: "get",
    params,
  });
};

export const getPage = (current, size, params) => {
  return request({
    url: "/api/ni/meal/food/page",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/ni/meal/food/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const remove = (ids) => {
  return request({
    url: "/api/ni/meal/food/remove",
    method: "post",
    params: {
      ids,
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/ni/meal/food/save",
    method: "post",
    data: row,
  });
};

export const update = (row) => {
  return request({
    url: "/api/ni/meal/food/update",
    method: "post",
    data: row,
  });
};

export const submit = (ids) => {
  return request({
    url: "/api/ni/meal/food/submit",
    method: "post",
    params: {
      ids,
    },
  });
};

export const back = (ids) => {
  return request({
    url: "/api/ni/meal/food/back",
    method: "post",
    params: {
      ids,
    },
  });
};

export const toVoid = (ids) => {
  return request({
    url: "/api/ni/meal/food/toVoid",
    method: "post",
    params: {
      ids,
    },
  });
};

import request from "@/router/axios";


export const getList = (current, size, params) => {
  return request({
    url: "/api/ni/fin/fixedAsset/getPage",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/ni/fin/fixedAsset/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/ni/fin/fixedAsset/add",
    method: "post",
    data: row,
  });
};

export const remove = (ids) => {
  return request({
    url: "/api/ni/fin/fixedAsset/remove",
    method: "post",
    params: {
      ids,
    },
  });
};

export const update = (row) => {
  return request({
    url: "/api/ni/fin/fixedAsset/update",
    method: "post",
    data: row,
  });
};

export const submit = (ids) => {
  return request({
    url: '/api/ni/fin/fixedAsset/submit',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const back = (ids) => {
  return request({
    url: '/api/ni/fin/fixedAsset/back',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const save = (row) => {
  return request({
    url: "/api/ni/fin/fixedAsset/save",
    method: "post",
    data: row,
  });
};

export const changeCategory = (id, category) => {
  return request({
    url: "/api/ni/fin/fixedAsset/changeCategory",
    method: "post",
    params: {
      id,
      category,
    },
  });
};

export const changeAssetStatus = (id, assetStatus) => {
  return request({
    url: "/api/ni/fin/fixedAsset/changeAssetStatus",
    method: "post",
    params: {
      id,
      assetStatus,
    },
  });
};

export const changeUseStatus = (id, useStatus) => {
  return request({
    url: "/api/ni/fin/fixedAsset/changeUseStatus",
    method: "post",
    params: {
      id,
      useStatus,
    },
  });
};

export const changeDeposit = (id, deposit) => {
  return request({
    url: "/api/ni/fin/fixedAsset/changeDeposit",
    method: "post",
    params: {
      id,
      deposit,
    },
  });
};

export const changeWarranty = (id, warranty) => {
  return request({
    url: "/api/ni/fin/fixedAsset/changeWarranty",
    method: "post",
    params: {
      id,
      warranty,
    },
  });
};

export const assetRegister = (row) => {
  return request({
    url: "/api/ni/fin/fixedAsset/assetRegister",
    method: "post",
    data: row,
  });
};


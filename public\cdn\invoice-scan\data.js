export var networkScanParam={
	isNetScan:false,
	IpAddr:''
};
export var scanForm={
	device:'',
	autofeeder:true,
	pixel:1,
	white:0,
	single:false,
	format:"jpg",
	resolution:100,
	isActual:true,
	isUI:false,
	cropAdjustTop:0,
	cropAdjustLeft:0,
	cropAdjustBottom:0,
	cropAdjustRight:0,
	compress:0,
	mode:0,
	//边框
	cheek:0,
	cheekWidth:0,
	upload:{
		uploadMode:2,
		httpUrl:'',
		fileName:'',
		httpMethod:'',
		header:'',
		param:'',
		ftpUrl:'',
		ftpPath:'/invoice-scan',
		ftpUser:'',
		ftpPassword:'',
		ftpPort:21,
		ftpMode:2
	},
	scanSystem:{
		licence:'',
		imagePath:'',
		imagePreName:'IMAGE',
		isDate:false,
		isTime:false,
		random:1,
		randomLength:4,
		randomCover:true,
		datePattern:'yyyyMMdd'
	},
	selectedModel:null,
	sort:2,
	userId:'',
	netWorkScanParam:networkScanParam
};
export var uploadForm={
	uploadMode:0,
	httpUrl:'',
	fileName:'',
	httpMethod:'',
	header:'',
	param:'',
	ftpUrl:'',
	ftpPath:'/invoice-scan',
	ftpUser:'',
	ftpPassword:'',
	ftpPort:21,
	ftpMode:2,
	format:2
};
export var extractDoc={
	part_number:'',
	classification:'',
	urgency:'',
	sign_authority:'',
	issue_num:'',
	title:'',
	main_send:'',
	publish_date:'',
	duplicate_send:'',
	print_unit:'',
	main_text:'',
	Issuer:'',
	classification_date:''
};
export var extractData={
	doc:extractDoc,
	contextMenuTarget:null,
	contextMenuVisible:false,
	drawing:false,
	Rect:null,
	selectStartPointer:{  x:0,y:0,},
	selectEndPointer:{  x:0,y:0,},
	uploadUrl:'',
	isBeforeUpload:false,
	isBeforeSaveOfd:false,
	ofdPath:'',
	ofdList:null,
	isOfdList:false
};

export const data = {
		urls:[],
		canvas:null,
		image:{
			width:0,
			height:0,
		},
		WebScan:null,
		isSetup:false,
		isDoubleOfd:0,
		isSelectDoubleOfd:false,
		isSelectDoublePdf:false,
		isSelectSortMode:false,
		sortMode:0,
		isDoublePdf:0,
		isProcess:false,
		process:0,
		form:scanForm,
		treeOptions:[],
		treePlaceHolder:'请选择图像路径',
		isDisableOpt:false,
		treeProps: {
			multiple: false,
			lazy: true,
			emitPath:false,
			checkStrictly:true,
			// lazyLoad: function(node, resolve){
				// if (node.level === 0) {
				// 	App.WebScan.getFileExplore("",true,function(result){
				// 		if(result.code==200){
				// 			App.treeOptions=result.data;
				// 		}
				// 	});
				// }else{
				// 	App.WebScan.getFileExplore(node.value,true,function(result){
				// 		if(result.code==200){
				// 			resolve(result.data);
				// 		}
				// 	});
				// }
			// },
			value:'id',
			label:'label',
			leaf:'isEnd'
		},
		devices:[],
		pid:"",
		imageName:'',
		loading:null,
		loadingPageShow:false,
		rectificationDialog:false,
		repSliderValue:0,
		isWindows:false,
		assistLine:null,
		assistText:null,
		rules:{
			device:[
				{ required: true, message: '请选择扫描仪', trigger: 'blur' }
			],
			// 'upload.httpUrl':[{
			// 	validator: this.validateHttpSet, trigger: 'blur'
			// }],
			// 'upload.httpMethod':[{
			// 	validator: this.validateHttpSet, trigger: 'blur'
			// }],
			// 'upload.fileName':[{
			// 	validator: this.validateHttpSet, trigger: 'blur'
			// }],
			// 'upload.ftpUrl':[{
			// 	validator: this.validateFtpSet, trigger: 'blur'
			// }],
			// 'upload.ftpPath':[{
			// 	validator: this.validateFtpSet, trigger: 'blur'
			// }]
		},
		validateHttpSet:function (rule, value, callback){
			if (this.form.upload.uploadMode == '0' && value== '') {
				callback(new Error('请输入必填项'));
			}
		},
		validateFtpSet:function (rule, value, callback){
			if (this.form.upload.uploadMode == '1' && value== '') {
				callback(new Error('请输入必填项'));
			}
		},
		activeName:'1',
		isInsertScan:false,
		selectImageObj:{
			index:-1
		},
		selectManyImages:[],
		oprationImageObj:{},
		redoStack:[],
		undoStack:[],
		startP:null,
		isUploadAllImage:false,
		uploadAllForm:uploadForm,
		uploadAllRules:{
			// 'httpUrl':[{
			// 	validator: this.validateUploadAllHttpSet, trigger: 'blur'
			// }],
			// 'httpMethod':[{
			// 	validator: this.validateUploadAllHttpSet, trigger: 'blur'
			// }],
			// 'fileName':[{
			// 	validator: this.validateUploadAllHttpSet, trigger: 'blur'
			// }],
			// 'ftpUrl':[{
			// 	validator: this.validateUploadAllFtpSet, trigger: 'blur'
			// }],
			// 'ftpPath':[{
			// 	validator: this.validateUploadAllFtpSet, trigger: 'blur'
			// }]
		},
		validateUploadAllHttpSet:function (rule, value, callback){
			if (this.uploadAllForm.uploadMode == '0' && value== '') {
				callback(new Error('请输入必填项'));
			}
		},
		validateUploadAllFtpSet:function (rule, value, callback){
			if (this.uploadAllForm.uploadMode == '1' && value== '') {
				callback(new Error('请输入必填项'));
			}
		},
		permissions:[],
		isDownLoad:false,
		downLoadMode:0,
		isPageCover:false,
		isReal:false,
		logLevel:'DEBUG',
		split:'0',
		autoRotate:'0',
		autoRotateLinux:'None',
		splitLinux:'None',
		splitOptionsWindows:[{
			"value":"0",
			"label":"无"
		},{
			"value":"1",
			"label":"水平"
		},{
			"value":"2",
			"label":"垂直"
		}],
		autoRotateOptionsWindows:[{
			"value":"0",
			"label":"关闭"
		},{
			"value":"1",
			"label":"打开"
		}],
		autoRotateOptionsLinux:[{
			"value":"None",
			"label":"无"
		},{
			"value":"90",
			"label":"90度"
		},{
			"value":"180",
			"label":"180度"
		},{
			"value":"270",
			"label":"270度"
		},{
			"value":"Auto Orientation",
			"label":"自动旋转"
		}],
		cheeks:[{
			"value":0,
			"label":"无"
		},{
			"value":1,
			"label":"白色"
		},{
			"value":2,
			"label":"黑色"
		}],
		sortOptions:[{
			"value":null,
			"label":"无"
		},{
			"value":0,
			"label":"首页先进"
		},{
			"value":1,
			"label":"尾页先进"
		}],
		flipSideRotationLinuxs:[{
			"value":"Book",
			"label":"左右翻页"
		},
		{
			"value":"Fanfold",
			"label":"上下翻页"
		}],
		flipSideRotationWindowses:[{
			"value":"0",
			"label":"左右翻页"
		},
		{
			"value":"1",
			"label":"上下翻页"
		}],
		canvasJson:null,
		minCheek:0,
		maxCheek:20,
		extract:extractData,
		modelList:new Array(),
		isManagerModel:false,
		isaddModel:false,
		modelAddInputValue:'',
		isEditModel:false,
		modelEditInputValue:'',
		modelEditId:'',
		flipSideRotationLinux:'Book',
		flipSideRotationWindows :'0',
		uploadLicenceUrl:'',
		preLicenceDialog:false
	}

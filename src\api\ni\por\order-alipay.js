import request from "@/router/axios";

export const getDetail = (params) => {
  return request({
    url: "/api/ni/por/order/alipay/detail",
    method: "get",
    params,
  });
};

export const remove = (ids) => {
  return request({
    url: "/api/ni/por/order/alipay/remove",
    method: "post",
    params: {
      ids,
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/ni/por/order/alipay/save",
    method: "post",
    data: row,
  });
};

export const update = (row) => {
  return request({
    url: "/api/ni/por/order/alipay/update",
    method: "post",
    data: row,
  });
};

export const submit = (ids) => {
  return request({
    url: "/api/ni/por/order/alipay/submit",
    method: "post",
    params: {
      ids,
    },
  });
};

export const toVoid = (ids) => {
  return request({
    url: "/api/ni/por/order/alipay/toVoid",
    method: "post",
    params: {
      ids,
    },
  });
};

export const back = (params) => {
  return request({
    url: "/api/ni/por/order/alipay/back",
    method: "post",
    params,
  });
};

export const getAliPayPage = (current, size, params) => {
  return request({
    url: "/api/ni/por/order/alipay/page",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getAlipayList = (params) => {
  return request({
    url: "/api/ni/por/order/alipay/list",
    method: "get",
    params,
  });
};

export const getItemList = (params) => {
  return request({
    url: "/api/ni/por/order/alipay/item/list",
    method: "get",
    params: {
      ...params,
    },
  });
};
export const getCostItemList = (params) => {
  return request({
    url: "/api/ni/por/order/alipay/cost-item/list",
    method: "get",
    params: {
      ...params,
    },
  });
};

export const changeAlipayOrder = (id, alipayOrder) => {
  return request({
    url: "/api/ni/por/order/alipay/changeAlipayOrder",
    method: "post",
    data: {
      id,
      alipayOrder,
    },
  });
};
export const changeDate = (id, date) => {
  return request({
    url: "/api/ni/por/order/alipay/changeDate",
    method: "post",
    data: {
      id,
      date,
    },
  });
};


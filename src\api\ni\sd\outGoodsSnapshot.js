import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/ni/sd/outGoodsSnapshot/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/ni/sd/outGoodsSnapshot/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/ni/sd/outGoodsSnapshot/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/ni/sd/outGoodsSnapshot/addPlan',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/ni/sd/outGoodsSnapshot/submit',
    method: 'post',
    data: row
  })
}
/**
 * 根据id查询发货快照明细表
 * @returns {*}
 */
export const getOutGoodsDetail = (id) => {
  return request({
    url: '/api/ni/sd/outGoodsSnapshot/OutGoodsDetail',
    method: 'get',
    params: {
      id
    }
  })
}

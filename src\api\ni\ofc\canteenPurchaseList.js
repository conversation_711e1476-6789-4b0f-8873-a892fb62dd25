import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/canteenPurchaseList/canteenPurchaseList/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/canteenPurchaseList/canteenPurchaseList/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/canteenPurchaseList/canteenPurchaseList/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/canteenPurchaseList/canteenPurchaseList/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/canteenPurchaseList/canteenPurchaseList/submit',
    method: 'post',
    data: row
  })
}

/**
 * 批量对账
 */
export const verify = (ids) => {
  return request({
    url:"/api/canteenPurchaseList/canteenPurchaseList/verify",
    method:"post",
    params:{
      ids,
    },
  });
};

<template>
  <el-card
    shadow="never"
    :class="{
      'board-item': true,
    }"
  >
    <div slot="header" class="clearfix">
      <span @click="handleRouter" class="board-title">每日库存快照</span>
      <el-date-picker
        style="float: right; padding: 3px 0"
        v-model="date"
        type="date"
        size="mini"
        format="yyyy-MM-dd"
        valueFormat="yyyy-MM-dd"
        placeholder="选择日期"
        :clearable="false"
        @change="handleReload"
      >
      </el-date-picker>
    </div>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      ref="crud"
      @search-change="searchChange"
      @search-reset="searchReset"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template #cost="{ row, index }">
        <el-tag size="mini" type="danger" effect="dark" v-if="row.cost">
          费用
        </el-tag>
        <el-tag size="mini" type="info" effect="plain" v-else> 实物</el-tag>
      </template>
    </avue-crud>
  </el-card>
</template>

<script>
import { dailyInOutboundSum, getList } from "@/api/ni/fg/fgDailyStockSnapshot";
import { dateFormat } from "@/util/date";

export default {
  props: {
    area: {
      type: String,
      default: "CN",
    },
  },
  data() {
    return {
      date: null,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 1,
      },
      loading: false,
      option: {
        search: false,
        border: false,
        header: false,
        menu: false,
        size: "mini",
        align: "center",
        calcHeight: 30,
        height: "482px",
        column: [
          {
            label: "仓库",
            prop: "depotId",
            type: "select",
            dicUrl: "/api/ni/base/depot/info/list?status=2&type=1",
            props: {
              label: "name",
              value: "id",
              desc: "code",
            },
            sortable: true,
            filters: true,
            minWidth: 100,
            overHidden: true,
            searchOrder: 98,
          },
          {
            label: "存货编码",
            prop: "materialCode",
            sortable: true,
            placeholder: " ",
            minWidth: 100,
            overHidden: true,
            filters: true,
          },
          {
            label: "规格",
            prop: "specText",
            placeholder: " ",
            display: false,
            overHidden: true,
            sortable: true,
            filters: true,
            minWidth: 80,
          },
          {
            label: "质量",
            prop: "qualityLevel",
            type: "select",
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_product_sku_quality_level",
            props: {
              label: "dictValue",
              value: "dictKey",
              desc: "dictKey",
            },
            placeholder: " ",
            sortable: true,
            filters: true,
            width: 100,
          },
          {
            label: "外包装",
            prop: "packageText",
            placeholder: " ",
            display: false,
            overHidden: true,
            minWidth: 115,
            sortable: true,
            filters: true,
          },
          {
            label: "内包装",
            prop: "innerPackageText",
            placeholder: " ",
            display: false,
            overHidden: true,
            sortable: true,
            filters: true,
            minWidth: 115,
          },
          {
            label: "期初件数",
            prop: "openingStock",
            minWidth: 80,
            sortable: true,
            type: "number",
          },
          {
            label: "期末件数",
            prop: "closingStock",
            minWidth: 80,
            sortable: true,
            type: "number",
          },
          {
            label: "期初重量",
            prop: "openingStockWeight",
            placeholder: " ",
            type: "number",
            sortable: true,
            minWidth: 80,
          },
          {
            label: "期末重量",
            prop: "closingStockWeight",
            placeholder: " ",
            type: "number",
            sortable: true,
            minWidth: 80,
          },
          {
            label: "入库",
            minWidth: 140,
            children: [
              {
                label: "件数",
                prop: "inboundNum",
                placeholder: " ",
                minWidth: 70,
              },
              {
                label: "重量",
                prop: "inboundWeight",
                placeholder: " ",
                minWidth: 70,
              },
            ],
          },
          {
            label: "出库",
            minWidth: 140,
            children: [
              {
                label: "件数",
                prop: "outboundNum",
                placeholder: " ",
                minWidth: 70,
              },
              {
                label: "重量",
                prop: "outboundWeight",
                placeholder: " ",
                minWidth: 70,
              },
            ],
          },
          {
            label: "国内/外",
            prop: "area",
            type: "radio",
            dicData: [
              {
                label: "国内",
                value: "CN",
              },
              {
                label: "国外",
                value: "OS",
              },
            ],
            width: 100,
            filters: true,
            sortable: true,
          },
        ],
      },
      data: [{}],
    };
  },
  created() {
    const yesterday = new Date(new Date().getTime() - 24 * 60 * 60 * 1000);
    this.date = dateFormat(yesterday, "yyyy-MM-dd");
  },
  methods: {
    handleRouter() {
      this.$router.push({ path: "/ni/fg/daily-stock-snapshot" });
    },
    handleReload() {
      this.page.currentPage = 1;
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    searchReset() {
      this.query = {};
      this.form = {};
      this.onLoad(this.page);
      this.selectionClear();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      const q = {
        ...params,
        ...this.query,
        ...this.form,
        date: this.date,
        area: this.area,
      };
      getList(page.currentPage, page.pageSize, q).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        data.records.forEach((item) => {
          item.inboundNum = "--";
          item.outboundNum = "--";
          item.inboundWeight = "--";
          item.outboundWeight = "--";
        });
        const ids = data.records.map((item) => item.id);
        if (ids.length > 0) {
          this.loadInboundSum(ids.join(","));
          this.loadOutboundSum(ids.join(","));
        }
        this.data = data.records;
        this.loading = false;
      });
    },
    loadInboundSum(ids) {
      dailyInOutboundSum(ids, "IN").then((res) => {
        const data = res.data.data;
        const dataMap = new Map(data.map((item) => [item.id + "", item]));
        this.data.forEach((item) => {
          if (dataMap.has(item.id + "")) {
            const target = dataMap.get(item.id + "");
            item.inboundNum = target.num;
            item.inboundWeight = target.weight;
          }
        });
        console.log(this.data);
      });
    },
    loadOutboundSum(ids) {
      dailyInOutboundSum(ids, "OUT").then((res) => {
        const data = res.data.data;
        const dataMap = new Map(data.map((item) => [item.id, item]));
        this.data.forEach((item) => {
          if (dataMap.has(Number(item.id))) {
            const target = dataMap.get(Number(item.id));
            item.outboundNum = target.num;
            item.outboundWeight = target.weight;
          }
        });
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.board-title {
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    color: #125b36;
    text-decoration: underline;
  }
}

.board-item {
  padding: 10px 20px;

  /deep/ .el-card__header {
    color: #303133;
    font-size: 20px;
    font-weight: 500;
    border-bottom: 0px;
    padding: 16px 18px !important;
  }
}
</style>

import request from "@/router/axios";

export const getPage = (current, size, params) => {
  return request({
    url: "/api/ni/por/type/page",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/ni/por/type/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const remove = (ids) => {
  return request({
    url: "/api/ni/por/type/remove",
    method: "post",
    params: {
      ids,
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/ni/por/type/submit",
    method: "post",
    data: row,
  });
};

export const update = (row) => {
  return request({
    url: "/api/ni/por/type/submit",
    method: "post",
    data: row,
  });
};

export const getIdsByRoleId = (roleId) => {
  return request({
    url: "/api/ni/por/type/getIdsByRoleId",
    method: "get",
    params: {
      roleId,
    },
  });
};
export const grant = (row) => {
  return request({
    url: "/api/ni/por/type/grant",
    method: "post",
    data: row,
  });
};

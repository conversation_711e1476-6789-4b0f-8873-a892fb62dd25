import request from '@/router/axios';


export const getList = (current, size, params) => {
    return request({
      url: '/api/ni/base/articleSummary/list',
      method: 'get',
      params: {
        ...params,
        current,
        size,
      }
    })
  }

export const getPage = (current, size, params) => {
    return request({
      url: '/api/ni/base/articleSummary/page',
      method: 'get',
      params: {
        ...params,
        current,
        size,
      }
    })
  }
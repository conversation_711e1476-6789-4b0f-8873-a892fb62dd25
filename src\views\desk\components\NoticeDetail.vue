<template>
  <el-dialog
    :visible.sync="read.visible"
    width="1000px"
    append-to-body
    modal-append-to-body
    :close-on-click-modal="false"
    :show-close="false"
    custom-class="read-dialog"
    v-dialogdrag
    center
  >
    <div class="read-content">
      <avue-article :props="read.props" :data="read"></avue-article>
    </div>
    <template v-if="read.attach && read.attach.length > 0">
      <div
        v-for="(item, index) in read.attach"
        :key="index"
        style="padding: 0 20px"
      >
        <span
          :style="{
            textDecoration: 'underline',
            cursor: 'pointer',
            color: colorName,
          }"
          @click="handleAttachDownload(item)"
        >
          {{ item.originalName }}
        </span>
        <span
          :style="{
            marginLeft: '15px',
            textDecoration: 'underline',
            cursor: 'pointer',
          }"
          @click="handleAttachView(item)"
          >预览</span
        >
      </div>
    </template>
    <span slot="footer" class="dialog-footer">
      <avue-form
        v-if="read.isManagerResponse === 1 && read.read !== 1"
        :option="read.comment.option"
        v-model="read.comment.form"
        @submit="handleReadWithComment"
        @reset-change="() => (read.visible = false)"
      ></avue-form>
      <el-button v-else type="primary" size="mini" @click="handleRead">
        已读
      </el-button>
    </span>
  </el-dialog>
</template>
<script>
import { mapGetters } from "vuex";
import { fileLink, getPage } from "@/api/resource/attach";
import { downloadFileBlob } from "@/util/util";
import { Base64 } from "js-base64";
import { saveComment } from "@/api/desk/userNotice";
import { getUserNotice } from "@/api/desk/notice";

export default {
  name: "NoticeDetail",
  props: {
    title: String,
  },
  computed: {
    ...mapGetters(["permission", "colorName"]),
  },
  data() {
    return {
      read: {
        attach: [],
        visible: false,
        read: 0,
        props: {
          title: "title",
          lead: "lead",
          meta: "meta",
          body: "content",
        },
        comment: {
          option: {
            size: "mini",
            span: 24,
            emptyText: "取消",
            submitText: "已读",
            column: [
              {
                label: "回复",
                prop: "comment",
                type: "textarea",
                span: 24,
                minRows: 2,
                rules: [
                  {
                    required: true,
                    message: "请输入回复",
                    trigger: "blur",
                  },
                ],
              },
            ],
          },
          form: {},
        },
        isManagerResponse: 0,
        title: "",
        meta: "",
        content: "",
        lead: "",
      },
    };
  },
  methods: {
    onView(row) {
      getUserNotice(row.id).then((res) => {
        this.read.visible = true;
        const data = res.data.data;
        getPage(1, 1000, {
          businessKey: row.id,
          businessName: "blade_desk_notice",
        }).then((res) => {
          this.read.attach = res.data.data.records;
        });
        this.read.read = data.isRead;
        if (data.isRead === 1 && data.isManagerResponse) {
          this.read.lead = `${
            data.receiveTime ? "[" + data.receiveTime + "]" : ""
          } ${data.receiveUserName} 回复了 ${data.comment}`;
        }
        this.read.isManagerResponse = data.isManagerResponse;
        this.read.comment.form.id = row.userNoticeId;
        this.read.comment.form.comment = null;
        this.read.title = data.title;
        this.read.meta = `发布人:${
          data.createUserName ? data.createUserName : "系统通知"
        }|发布时间:${data.releaseTime}`;
        this.read.content = data.content;
      });
    },
    async handleAttachDownload(row) {
      if (row.accessPolicy && row.accessPolicy === "Private") {
        //请求后端获取下载Url
        const r = await fileLink(row.id);
        downloadFileBlob(r.data.data, row.originalName);
      } else downloadFileBlob(row.link, row.originalName);
    },
    handleAttachView(row) {
      const url = "/kkf/onlinePreview?url=";
      if (row.accessPolicy && row.accessPolicy === "Private") {
        //请求后端获取下载Url
        fileLink(row.id).then((res) => {
          window.open(url + encodeURIComponent(Base64.encode(res.data.data)));
        });
      } else window.open(url + encodeURIComponent(Base64.encode(row.link)));
    },
    handleReadWithComment(form, done) {
      saveComment(form.id, form.comment)
        .then(() => {
          this.read.visible = false;
          this.$emit("read", this.read.data);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        })
        .finally(() => {
          done();
        });
    },
    handleRead() {
      this.read.visible = false;
      this.$emit("read", this.read.data);
    },
  },
};
</script>
<style scoped></style>

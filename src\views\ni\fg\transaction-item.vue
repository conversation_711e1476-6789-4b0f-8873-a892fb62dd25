<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      v-model="form"
      ref="crud"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      :cell-style="cellStyle"
      @current-row-change="handleCurrentRowChange"
      @on-load="onLoad"
    >
      <template #menuLeft>
        <!--导出-->
        <el-button
          type="primary"
          v-if="permission.transaction_item_export"
          size="mini"
          icon="el-icon-download"
          @click="handleExport"
        >
          导出
        </el-button>
        <el-divider direction="vertical"></el-divider>
        <el-radio-group v-model="type" size="mini">
          <el-radio-button label="all">全部</el-radio-button>
          <el-radio-button label="01">入库</el-radio-button>
          <el-radio-button label="02">出库</el-radio-button>
        </el-radio-group>
        <el-divider direction="vertical"></el-divider>
        <el-tag>
          当前表格已选择
          <span style="font-weight: bolder">{{ selectionList.length }}</span>
          项
          <el-button type="text" size="mini" @click="selectionClear">
            清空
          </el-button>
        </el-tag>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import { getList } from "@/api/ni/fg/fgTransactionItem";
import { mapGetters } from "vuex";

export default {
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        showSummary: true,
        sumColumnList: [
          {
            name: "num",
            type: "sum",
          },
          {
            name: "weight",
            type: "sum",
          },
        ],
        addBtn: false,
        menu: false,
        editBtn: false,
        delBtn: false,
        dialogFullscreen: true,
        dialogDrag: true,
        size: "mini",
        searchSize: "mini",
        align: "center",
        searchIndex: 3,
        searchIcon: true,
        searchEnter: true,
        height: "auto",
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        span: 8,
        border: true,
        selection: true,
        highlightCurrentRow: true,
        column: [
          {
            label: "单据主题",
            prop: "title",
            minWidth: 110,
            overHidden: true,
            search: true,
          },
          {
            label: "单据编码",
            prop: "serialNo",
            minWidth: 110,
            overHidden: true,
            search: true,
          },
          {
            label: "单据类型",
            prop: "queryType",
            type: "cascader",
            dicData: [],
            multiple: true,
            showAllLevels: false,
            placeholder: " ",
            search: true,
            hide: true,
            showColumn: false,
          },
          {
            label: "单据类型",
            prop: "typeText",
            minWidth: 90,
          },
          {
            label: "仓库",
            prop: "depotId",
            type: "select",
            dicUrl: "/api/ni/base/depot/info/list?status=2&type=1",
            props: {
              label: "name",
              value: "id",
              desc: "code",
            },
            minWidth: 100,
            overHidden: true,
            editDisabled: true,
            search: true,
          },
          {
            label: "操作人",
            prop: "opUserId",
            type: "select",
            remote: true,
            dicUrl: `/api/blade-user/search/otj/user?name={{key}}`,
            props: {
              label: "realName",
              value: "id",
            },
            dicFormatter: (data) => {
              return data.data.records;
            },
            placeholder: " ",
            filterable: true,
            overHidden: true,
            search: true,
            hide: true,
            showColumn: false,
          },
          {
            label: "操作人",
            prop: "opUserName",
            display: false,
            overHidden: true,
            width: 70,
          },
          {
            label: "操作时间",
            prop: "opDate",
            search: true,
            type: "date",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            searchRange: true,
            placeholder: " ",
            editDisabled: true,
            width: 100,
          },
          {
            label: "关联单据",
            prop: "relatedOrderText",
            type: "input",
            display: false,
            overHidden: true,
            width: 100,
            search: true,
          },
          {
            label: "状态",
            prop: "status",
            type: "select",
            dicData: [
              {
                label: "待审核",
                value: 0,
              },
              {
                label: "正常",
                value: 1,
              },
              {
                label: "已冲红",
                value: 2,
              },
            ],
            dataType: "number",
            search: true,
            addDisplay: false,
            editDisplay: false,
          },
          {
            label: "国内/外",
            prop: "area",
            type: "radio",
            search: true,
            width: 100,
            dicData: [
              {
                label: "国内",
                value: "CN",
              },
              {
                label: "国外",
                value: "OS",
              },
            ],
          },
          {
            label: "产品名称",
            prop: "skuText",
            placeholder: " ",
            overHeight: true,
            overHidden: true,
            width: 120,
            hide: true,
          },
          {
            label: "规格",
            prop: "specText",
            placeholder: " ",
            display: false,
            overHidden: true,
            cell: false,
            search: true,
            width: 90,
          },
          {
            label: "外包装",
            prop: "packageText",
            placeholder: " ",
            display: false,
            overHidden: true,
            search: true,
            width: 110,
          },
          {
            label: "内包装",
            prop: "innerPackageText",
            placeholder: " ",
            display: false,
            overHidden: true,
            width: 100,
          },
          {
            label: "内包装",
            prop: "innerPackageId",
            type: "select",
            dicUrl:
              "/api/ni/product/packaging/list?innerPark=1&current=1&size=20&status=1&&name={{key}}",
            props: {
              label: "name",
              value: "id",
              desc: "type",
            },
            search: true,
            remote: true,
            dicFormatter: (data) => {
              return data.data.records;
            },
            hide: true,
            showColumn: false,
            filterable: true,
          },
          {
            label: "批号",
            prop: "batchNo",
            placeholder: " ",
            width: 110,
            overHidden: true,
            search: true,
          },
          {
            label: "生产日期",
            prop: "productionTime",
            type: "datetime",
            format: "yyyy-MM-dd hh:mm:ss",
            valueFormat: "yyyy-MM-dd hh:mm:ss",
            width: 124,
            overHidden: true,
            search: true,
            searchRange: true,
          },
          {
            label: "箱数",
            prop: "num",
            placeholder: " ",
            type: "number",
            precision: 0,
            width: 80,
            overHidden: true,
          },
          {
            label: "重量",
            prop: "weight",
            placeholder: " ",
            type: "number",
            minWidth: 80,
            overHidden: true,
          },
          {
            label: "单位",
            prop: "unit",
            type: "select",
            filterable: true,
            width: 60,
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_material_unit",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            values: "16",
            overHidden: true,
            slot: true,
            placeholder: " ",
          },
          {
            label: "存货编码",
            prop: "materialCode",
            placeholder: " ",
            width: 90,
            overHidden: true,
            display: false,
            search: true,
          },
          {
            label: "备注",
            prop: "remark",
            type: "textarea",
            placeholder: " ",
            minRows: 1,
            overHidden: true,
            minWidth: 120,
            search: true,
          },
          {
            label: "创建时间",
            prop: "createTime",
            search: true,
            type: "datetime",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
            searchRange: true,
            placeholder: " ",
            editDisabled: true,
            hide: true,
            width: 100,
          },
        ],
      },
      data: [],
      export: {
        column: [
          {
            label: "单据主题",
            prop: "title",
          },
          {
            label: "单据编码",
            prop: "serialNo",
          },
          {
            label: "单据类型",
            prop: "typeText",
          },
          {
            label: "仓库",
            prop: "depotName",
          },

          {
            label: "操作人",
            prop: "opUserName",
            width: 70,
          },
          {
            label: "操作时间",
            prop: "opDate",
            width: 100,
          },
          {
            label: "关联单据",
            prop: "relatedOrderText",
            width: 100,
          },
          {
            label: "状态",
            prop: "status",
          },
          {
            label: "国内/外",
            prop: "area",
            width: 120,
          },
          {
            label: "规格",
            prop: "specText",
            width: 90,
          },
          {
            label: "外包装",
            prop: "packageText",
            width: 110,
          },
          {
            label: "内包装",
            prop: "innerPackageText",
            width: 100,
          },
          {
            label: "批号",
            prop: "batchNo",
            width: 110,
          },
          {
            label: "生产日期",
            prop: "productionTime",
            width: 124,
          },
          {
            label: "箱数",
            prop: "num",
            width: 80,
          },
          {
            label: "重量",
            prop: "weight",
            width: 80,
          },
          {
            label: "存货编码",
            prop: "materialCode",
            width: 90,
          },
          {
            label: "备注",
            prop: "remark",
            width: 120,
          },
        ],
      },
      statusKv: {
        0: "未审核",
        1: "正常",
        2: "已冲红",
      },
      type: "all",
    };
  },
  computed: {
    ...mapGetters(["permission", "colorName"]),
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  watch: {
    type() {
      this.onLoad(this.page, this.query);
    },
  },
  mounted() {
    this.loadTypeDict();
  },
  methods: {
    async loadTypeDict() {
      const dicData = [
        {
          label: "入库",
          value: "1",
          children: [],
        },
        {
          label: "出库",
          value: "2",
          children: [],
        },
      ];
      const res = await this.$http.get(
        "/api/blade-system/dict-biz/dictionary?code=ni_fg_inbound_type"
      );
      dicData[0].children = res.data.data.map((item) => {
        return {
          label: item.dictValue,
          value: item.dictKey,
        };
      });
      const res1 = await this.$http.get(
        "/api/blade-system/dict-biz/dictionary?code=ni_fg_outbound_type"
      );
      dicData[1].children = res1.data.data.map((item) => {
        return {
          label: item.dictValue,
          value: item.dictKey,
        };
      });
      const type = this.findObject(this.option.column, "queryType");
      type.dicData = dicData;
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    handleCurrentRowChange(row) {
      this.$refs.crud.toggleRowSelection(row, true);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      const q = Object.assign(params, this.query);
      if (q.opDate && q.opDate.length === 2) {
        q.startOpDate = q.opDate[0];
        q.endOpDate = q.opDate[1];
        q.opDate = null;
      }
      if (q.createTime && q.createTime.length === 2) {
        q.startCreateTime = q.createTime[0];
        q.endCreateTime = q.createTime[1];
        q.createTime = null;
      }
      if (q.productionTime && q.productionTime.length === 2) {
        q.startProductionTime = q.productionTime[0];
        q.endProductionTime = q.productionTime[1];
        q.productionTime = null;
      }
      if (q.type && q.type.length === 2) {
        q.type = q.type[1];
      }
      if (this.type !== "all") {
        q.typeLike = this.type;
      }
      if (q.queryType) {
        q.queryTypeStr = JSON.stringify(q.queryType);
      }
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records.map((record) => {
          if (record.type && record.type.startsWith("02")) {
            record.num = -record.num;
            record.weight = -record.weight;
          }
          return record;
        });
        this.loading = false;
        this.selectionClear();
      });
    },
    cellStyle({ row, column }) {
      if ("status" === column.columnKey) {
        if (row.status === 0) {
          return {
            backgroundColor: "#f5f7fa",
            color: "##909399",
          };
        } else if (row.status === 1) {
          return {
            backgroundColor: this.colorName,
            color: "#fff",
          };
        } else if (row.status === 2) {
          return {
            backgroundColor: "#F56C6C",
            color: "#fff",
          };
        }
      }
      if ("serialNo" === column.columnKey && row.red) {
        return {
          backgroundColor: "#F56C6C",
          color: "#fff",
        };
      }
      if ("typeText" === column.columnKey) {
        if (row.type && row.type.startsWith("01")) {
          return {
            backgroundColor: "#409EFF", // 蓝色表示入库
            color: "#fff",
          };
        } else if (row.type && row.type.startsWith("02")) {
          return {
            backgroundColor: "#E6A23C", // 橙色表示出库
            color: "#fff",
          };
        }
      }
      if ("num" === column.columnKey && row.type.startsWith("02")) {
        return {
          color: "#F56C6C",
        };
      }
    },
    async handleExport() {
      let msg =
        "确定将<span style='font-weight: bolder;color: #F56C6C'>选择数据</span>导出?";
      if (this.selectionList.length === 0) {
        msg =
          "确定要将<span style='font-weight: bolder;color: #F56C6C'>全部数据</span>导出?";
      }
      try {
        await this.$alert(msg, {
          dangerouslyUseHTMLString: true,
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        });
        const data = [];
        if (this.selectionList.length > 0) {
          this.selectionList.forEach((item) => {
            data.push({
              ...item,
              status: this.statusKv[item.status],
            });
          });
        } else {
          const res = await getList(1, 10000, {
            ...this.params,
            ...this.query,
          });
          const data1 = res.data.data.records;
          data1.forEach((item) => {
            data.push({
              ...item,
              status: this.statusKv[item.status],
              area: item.area === "CN" ? "国内" : "国外",
            });
          });
        }
        await this.$Export.excel({
          title: "成品库出入库明细",
          columns: this.export.column,
          data,
        });
      } catch (e) {
        console.log(e);
      }
    },
  },
};
</script>

<style></style>

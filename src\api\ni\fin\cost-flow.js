import request from "@/router/axios";

export const getList = (current, size, params) => {
  return request({
    url: "/api/ni/fin/costFlow/page",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/ni/fin/costFlow/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const remove = (ids) => {
  return request({
    url: "/api/ni/fin/costFlow/remove",
    method: "post",
    params: {
      ids,
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/ni/fin/costFlow/save",
    method: "post",
    data: row,
  });
};

export const update = (row) => {
  return request({
    url: "/api/ni/fin/costFlow/update",
    method: "post",
    data: row,
  });
};

export const submit = (ids) => {
  return request({
    url: '/api/ni/fin/costFlow/submit',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const back = (ids) => {
  return request({
    url: '/api/ni/fin/costFlow/back',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const toVoid = (ids) => {
  return request({
    url: '/api/ni/fin/costFlow/toVoid',
    method: 'post',
    params: {
      ids,
    }
  })
}

import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/ni/fin/loan/addReturnItems/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/ni/fin/loan/addReturnItems/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/ni/fin/loan/addReturnItems/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/ni/fin/loan/addReturnItems/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/ni/fin/loan/addReturnItems/submit',
    method: 'post',
    data: row
  })
}


<template>
  <basic-container>
    <avue-skeleton :loading="waiting" avatar :rows="8">
      <avue-affix id="avue-view" :offset-top="114">
        <div class="header">
          <avue-title :value="process.processDefinitionName"></avue-title>
          <div v-if="process.status != 'todo'">
            主题：
            <avue-select
              v-model="theme"
              size="mini"
              :clearable="false"
              :dic="themeList"
            ></avue-select>
          </div>
        </div>
      </avue-affix>
      <el-tabs v-model="activeName">
        <el-tab-pane label="申请信息" name="first">
          <el-card shadow="never">
            <div
              id="printBody"
              :class="process.status != 'todo' ? `wf-theme-${theme}` : ''"
            >
              <avue-form
                v-if="
                  option &&
                  ((option.column && option.column.length > 0) ||
                    (option.group && option.group.length > 0))
                "
                v-model="form"
                ref="form"
                :defaults.sync="defaults"
                :option="option"
                :upload-preview="handleUploadPreview"
              >
                <template #financeAccount="{ size, disabled }">
                  <bank-card-input
                    v-model="form.financeAccount"
                    :size="size"
                    :disabled="disabled"
                  />
                </template>
                <template #budgetId="{ size, disabled }">
                  <un-finish-budget-select
                    v-model="form.budgetId"
                    :size="size"
                    :disabled="disabled"
                    :params="{ brand: form.brand }"
                    placeholder=" "
                    @clear="handleBudgetClear"
                    @confirm="handleBudgetConfirm"
                  />
                </template>
                <template #eaPersonId="{ row, size, disabled, index }">
                  <user-select1
                    v-model="form.eaPersonId"
                    :size="size"
                    :disabled="disabled"
                    userUrl="/api/blade-user/search/user"
                  />
                </template>
                <template #financialStaffId="{ row, size, disabled, index }">
                  <user-select1
                    v-model="form.financialStaffId"
                    :size="size"
                    :disabled="disabled"
                  />
                </template>
                <template #loanId="{ row, size, disabled, index }">
                  <fin-loan-select
                    v-model="form.loanId"
                    :size="size"
                    :disabled="disabled"
                    @confirm="handleLoanSelect"
                  />
                </template>
                <!-- 结算差额 -->
                <template #differenceAmount>
                  <div
                    style="
                      margin: 20px 0;
                      color: red;
                      font-weight: bold;
                      font-size: 16px;
                    "
                  >
                    结算差额: {{ form.differenceAmount }}
                  </div>
                </template>
                <template #items="{ index, size }">
                  <cost-ea-item
                    :detail.sync="option.detail"
                    v-model="form.items"
                    :budget-id="form.budgetId"
                    :cost-apply-id="form.costApplyId"
                    :update-material-code="form.updateMaterialCode === '1'"
                    @amountChange="handleSumAmount"
                    @numChange="handleSumNum"
                    @attachClick="rowAttach"
                  />
                </template>
              </avue-form>
            </div>
          </el-card>
          <el-card
            shadow="never"
            style="margin-top: 20px"
            v-if="process.status == 'todo'"
          >
            <wf-examine-form
              ref="examineForm"
              :comment.sync="comment"
              :process="process"
              @user-select="handleUserSelect"
            ></wf-examine-form>
          </el-card>
        </el-tab-pane>
        <el-tab-pane label="流转信息" name="second">
          <el-card shadow="never" style="margin-top: 5px">
            <wf-flow :flow="flow"></wf-flow>
          </el-card>
        </el-tab-pane>
        <el-tab-pane label="流程跟踪" name="third">
          <template v-if="activeName == 'third'">
            <el-card shadow="never" style="margin-top: 5px">
              <wf-design
                ref="bpmn"
                style="height: 500px"
                :options="bpmnOption"
              ></wf-design>
            </el-card>
          </template>
        </el-tab-pane>
      </el-tabs>
    </avue-skeleton>

    <!-- 底部按钮 -->
    <wf-button
      :loading="submitLoading"
      :button-list="buttonList"
      :process="process"
      :comment="comment"
      @examine="handleExamine"
      @user-select="handleUserSelect"
      @print="handlePrint"
      @rollback="handleRollbackTask"
      @terminate="handleTerminateProcess"
      @withdraw="handleWithdrawTask"
    ></wf-button>
    <!-- 人员选择弹窗 -->
    <user-select
      ref="user-select"
      :check-type="checkType"
      :default-checked="defaultChecked"
      @onConfirm="handleUserSelectConfirm"
    ></user-select>
    <attach-dialog
      ref="attachRef"
      code="public"
      :delBtn="false"
      @close="handleRefreshAttach"
    />
  </basic-container>
</template>

<script>
import WfExamineForm from "@/views/plugin/workflow/process/components/examForm.vue";
import WfButton from "@/views/plugin/workflow/process/components/button.vue";
import WfFlow from "@/views/plugin/workflow/process/components/flow.vue";
import userSelect from "@/views/plugin/workflow/process/components/user-select";

import exForm from "@/views/plugin/workflow/mixins/ex-form";
import theme from "@/views/plugin/workflow/mixins/theme";
import UserSelect1 from "@/components/user-select";
import FinLoanSelect from "@/views/ni/fin/components/FinLoanSelect";
import UnFinishBudgetSelect from "@/views/ni/por/components/UnFinishBudgetSelect";
import CostEaItem from "@/views/ni/fin/components/CostEaItem";
import { dateFormat } from "@/util/date";
import { getList as getAttachList } from "@/api/resource/attach";
import AttachDialog from "@/components/attach-dialog";
import BankCardInput from "@/components/bank-card-input";
import { getDetail as getCostApplyDetail } from "@/api/ni/fin/cost-apply";
import CostApplySelect from "@/views/ni/fin/components/CostApplySelect";

export default {
  mixins: [exForm, theme],
  components: {
    userSelect,
    WfExamineForm,
    WfButton,
    WfFlow,
    UserSelect1,
    FinLoanSelect,
    UnFinishBudgetSelect,
    CostEaItem,
    AttachDialog,
    BankCardInput,
    CostApplySelect,
  },
  watch: {
    "$route.query.p": {
      immediate: true,
      handler(val) {
        if (val) {
          this.submitLoading = true;
          Object.keys(this.form).forEach((key) => (this.form[key] = ""));
          const param = JSON.parse(Buffer.from(val, "base64").toString());
          const { taskId, processInsId } = param;
          if (processInsId) {
            this.getDetail(taskId, processInsId);
          }
        }
      },
    },
    //监听冲抵借款按钮（不是借款不显示结算差额）
    "form.loan": {
      handler(newVal) {
        console.log(newVal);
        if (newVal) {
          const differenceAmount = this.findObject(
            this.option.column,
            "differenceAmount"
          );
          differenceAmount.display = true;
        } else {
          const differenceAmount = this.findObject(
            this.option.column,
            "differenceAmount"
          );
          differenceAmount.display = false;
        }
      },
      immediate: true,
    },
  },
  data() {
    return {
      activeName: "first",
      defaults: {},
      form: {
        items: [],
      },
      option: {
        detail: true,
        labelWidth: 120,
        calcHeight: 30,
        size: "mini",
        span: 8,
        menuBtn: false,
        column: [
          {
            type: "input",
            label: "申请人",
            display: true,
            prop: "createUserName",
            readonly: true,
            span: 8,
          },
          {
            type: "input",
            label: "申请部门",
            display: true,
            row: true,
            prop: "createDeptName",
            readonly: true,
            span: 8,
          },
          {
            label: "报销人",
            prop: "eaPersonId",
            span: 8,
            rules: [
              {
                required: true,
                message: "请选择报销人",
                trigger: "blur",
              },
            ],
          },
          {
            label: "流水号",
            prop: "serialNo",
            placeholder: "系统自动生成",
            disabled: true,
            span: 8,
          },
          {
            label: "报销类型",
            prop: "type",
            type: "select",
            placeholder: " ",
            span: 8,
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_fin_loan_type",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            rules: [
              {
                required: true,
                message: "请选择报销类型",
                trigger: "blur",
              },
            ],
            control: (val) => {
              if (val === "4") {
                return {
                  porOrderIdList: {
                    display: true,
                  },
                };
              } else {
                return {
                  porOrderIdList: {
                    display: false,
                  },
                };
              }
            },
          },
          {
            label: "账套",
            prop: "brand",
            type: "radio",
            dicUrl: "/api/blade-system/dict-biz/dictionary?code=ni_brand",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            cascader: ["ledgerId"],
            value: "1",
            span: 8,
            rules: [
              {
                required: true,
                message: "请选择账套",
                trigger: "blur",
              },
            ],
          },
          {
            label: "关联预算",
            prop: "budgetId",
            span: 8,
            rules: [
              {
                required: true,
                message: "请选择关联预算",
                trigger: "blur",
              },
            ],
          },
          {
            label: "冲抵借款",
            prop: "loan",
            type: "switch",
            value: false,
            span: 8,
            dicData: [
              {
                label: "否",
                value: false,
              },
              {
                label: "是",
                value: true,
              },
            ],
            // disabled: true,
            control: (val) => {
              if (val) {
                return {
                  loanId: {
                    display: true,
                  },
                  // loanType: {
                  //   display: true,
                  // },
                  loanAmount: {
                    display: true,
                  },
                };
              } else {
                return {
                  loanId: {
                    display: false,
                  },
                  // loanType: {
                  //   display: false,
                  // },
                  loanAmount: {
                    display: false,
                  },
                };
              }
            },
          },
          {
            label: "关联借款单",
            prop: "loanId",
            type: "input",
            display: false,
            span: 8,
            rules: [
              {
                required: true,
                message: "请选择关联借款单",
                trigger: "blur",
              },
            ],
          },
          // {
          //   label: "借款类型",
          //   prop: "loanType",
          //   type: "select",
          //   placeholder: " ",
          //   dicUrl:
          //     "/api/blade-system/dict-biz/dictionary?code=ni_fin_loan_type",
          //   props: {
          //     label: "dictValue",
          //     value: "dictKey",
          //   },
          //   display: false,
          //   span: 8,
          //   disabled: true,
          // },
          {
            label: "借款金额",
            prop: "loanAmount",
            type: "number",
            placeholder: " ",
            precision: 2,
            span: 8,
            display: false,
            disabled: true,
          },
          {
            label: "报销金额",
            prop: "amount",
            placeholder: " ",
            type: "number",
            precision: 2,
            disabled: true,
            span: 8,
          },
          {
            label: "付款方式",
            prop: "paymentType",
            type: "select",
            minWidth: 85,
            span: 8,
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=ni_fin_payment_type",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            placeholder: " ",
            rules: [
              {
                required: true,
                message: "请选择付款方式",
                trigger: "blur",
              },
            ],
            control: (val) => {
              if (["1", "5"].includes(val)) {
                return {
                  financeBank: {
                    display: true,
                  },
                  financeAccount: {
                    display: true,
                  },
                };
              } else {
                return {
                  financeBank: {
                    display: false,
                  },
                  financeAccount: {
                    display: false,
                  },
                };
              }
            },
          },
          {
            label: "票据张数",
            prop: "invoiceNum",
            placeholder: " ",
            type: "number",
            precision: 0,
            disabled: true,
            span: 8,
            row: true,
          },
          {
            label: "报销人银行",
            prop: "financeBank",
            display: false,
            span: 8,
            rules: [
              {
                required: true,
                message: "请输入报销人银行",
                trigger: "blur",
              },
            ],
          },
          {
            label: "报销人银行账号",
            prop: "financeAccount",
            placeholder: " ",
            span: 8,
            display: false,
            labelWidth: 100,
            rules: [
              {
                required: true,
                message: "请输入报销人银行账号",
                trigger: "blur",
              },
            ],
          },
          {
            label: "财务账户",
            prop: "ledgerId",
            placeholder: " ",
            type: "select",
            dicUrl: "/api/ni/fin/ledger/list?status=2&brand={{key}}",
            overHidden: true,
            props: {
              label: "dictLabel",
              value: "id",
              desc: "currencyName",
            },
            span: 8,
            rules: [
              {
                required: false,
                message: "请选择付款账户",
                trigger: "blur",
              },
            ],
          },
          {
            label: "付款日期",
            prop: "payTime",
            minWidth: 115,
            type: "date",
            overHidden: true,
            placeholder: " ",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            span: 8,
            rules: [
              {
                required: false,
                message: "请选择付款日期",
                trigger: "blur",
              },
            ],
          },
          {
            label: "财务人员",
            prop: "financialStaffId",
            span: 8,
            rules: [
              {
                required: false,
                message: "请选择财务人员",
                trigger: "blur",
              },
            ],
          },
          {
            label: "用途",
            prop: "remark",
            placeholder: " ",
            hide: true,
            type: "textarea",
            span: 24,
            minRows: 2,
          },
          {
            label: "附件",
            type: "upload",
            propsHttp: {
              res: "data",
              url: "attachId",
              name: "originalName",
            },
            action: "/api/blade-resource/oss/endpoint/put-file-attach",
            display: true,
            hide: true,
            showColumn: false,
            span: 24,
            showFileList: true,
            multiple: true,
            limit: 100,
            prop: "attachment",
          },
          {
            label: "",
            prop: "differenceAmount",
            display: false,
            labelWidth: 50,
          },
          {
            label: "报销明细",
            prop: "items",
            labelPosition: "top",
            span: 24,
          },
        ],
      },
      vars: [], // 需要提交的字段
      submitLoading: false, // 提交时按钮loading
      typeDict: [],
      typeDictKeyValue: {},
      inquiries: {},
      fromPath: "",
      actionItem: null,
      actionItemIndex: null,
      processIsFinished: null,
    };
  },
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      //  这里的vm指的就是vue实例，可以用来当做this使用
      vm.fromPath = from.path; //获取上一级路由的路径
    });
  },
  methods: {
    rowAttach(row, index) {
      if (!this.option.detail && this.form.updateMaterialCode !== "1") {
        return;
      }
      this.actionItem = row;
      this.actionItemIndex = index;
      this.$refs.attachRef.init(row.id, "ni_fin_cost_ea");
    },
    handleRefreshAttach() {
      getAttachList({
        businessName: "ni_fin_cost_ea",
        businessKey: this.actionItem.id,
      }).then((res) => {
        const data = res.data.data;
        this.actionItem.attach = data.map((item) => {
          return {
            label: item.originalName,
            value: item.id,
          };
        });
        this.form.items.splice(this.actionItemIndex, 1, { ...this.actionItem });
      });
    },
    handleSumAmount(amount) {
      this.form.amount = amount;
    },
    handleSumNum(num) {
      this.form.invoiceNum = num;
    },
    handleBudgetConfirm(selectionList) {
      if (selectionList) {
        this.form.budgetSerialNo = selectionList[0].serialNo;
        this.form.budgetType = selectionList[0].type;
      }
    },
    handleBudgetClear() {
      this.form.budgetId = null;
      this.form.items = [];
    },
    handleLoanSelect(selectList, totalAmount) {
      if (selectList && selectList.length > 0) {
        this.form.loanAmount = totalAmount;
        this.form.loanDetails = selectList.map((item) => ({
          id: item.id,
          reason: item.reason,
          amount: item.amount,
        }));
        this.form.differenceAmount = (
          parseFloat(totalAmount) - parseFloat(this.form.amount || 0)
        ).toFixed(2);
      } else {
        this.form.loanAmount = 0;
        this.form.loanDetails = [];
        this.form.differenceAmount = 0 - parseFloat(this.form.amount || 0);
      }
    },
    // 获取任务详情
    getDetail(taskId, processInsId) {
      this.getTaskDetail(taskId, processInsId).then((res) => {
        const { process } = res;
        const { variables, status, processIsFinished, taskName } = process;
        this.processIsFinished = processIsFinished;
        const option = this.option;
        option.menuBtn = false;
        const { column, group } = option;
        if (status !== "todo") {
          // 已办，删除字段默认值
          option.detail = true;
          if (column && column.length > 0) {
            // 处理column
            column.forEach((col) => {
              if (col.type === "dynamic")
                col.children.column.forEach((cc) => {
                  delete cc.value;
                });
              delete col.value;
            });
          }

          if (group && group.length > 0) {
            // 处理group
            group.forEach((gro) => {
              if (gro.column && gro.column.length > 0) {
                gro.column.forEach((col) => {
                  if (col.type == "dynamic")
                    col.children.column.forEach((cc) => {
                      delete cc.value;
                    });
                  delete col.value;
                });
              }
            });
          }
        } else {
          if (
            variables["updateMaterialCode"] === "1" ||
            ["recall", "reject"].includes(processIsFinished) ||
            taskName === "财务初审" ||
            variables["updateLedger"] === "1" ||
            taskName === "出纳付款"
          ) {
            option.detail = false;
          } else {
            option.detail = true;
          }
          let vars = [];
          column.forEach((col) => {
            // 只在没有后端值时使用默认值
            if (col.value && variables[col.prop] === undefined) {
              col.value = this.getDefaultValues(col.value);
            }
            vars.push(col.prop);
            if (
              (variables["updateMaterialCode"] === "1" ||
                taskName === "财务初审") &&
              !["recall", "reject"].includes(processIsFinished)
            ) {
              col.disabled = true;
            }
          });
          if (group && group.length > 0) {
            // 处理group
            group.forEach((gro) => {
              gro.column.forEach((col) => {
                if (col.value) col.value = this.getDefaultValues(col.value);
              });
              vars = vars.concat(group.vars);
            });
          }
          this.vars = vars;
        }
        const ledgerIdColumn = this.findObject(option.column, "ledgerId");
        const payTimeColumn = this.findObject(option.column, "payTime");
        const financialStaffIdColumn = this.findObject(
          option.column,
          "financialStaffId"
        );
        if (
          (variables["updateLedger"] === "1" || taskName === "出纳付款") &&
          status !== "done"
        ) {
          ledgerIdColumn.disabled = false;
          ledgerIdColumn.rules = [
            {
              required: true,
              message: "请选择付款账户",
              trigger: "blur",
            },
          ];
          payTimeColumn.disabled = false;
          payTimeColumn.rules = [
            {
              required: true,
              message: "请选择付款日期",
              trigger: "blur",
            },
          ];
          financialStaffIdColumn.disabled = false;
          financialStaffIdColumn.rules = [
            {
              required: true,
              message: "请选择付款日期",
              trigger: "blur",
            },
          ];
          variables.payTime = dateFormat(new Date(), "yyyy-MM-dd");
          variables.financialStaffId = this.userInfo.user_id;
          variables.payState = "2";
        } else {
          ledgerIdColumn.disabled = true;
          ledgerIdColumn.rules = [
            {
              required: false,
              message: "请选择付款账户",
              trigger: "blur",
            },
          ];
          payTimeColumn.disabled = true;
          payTimeColumn.rules = [
            {
              required: false,
              message: "请选择付款日期",
              trigger: "blur",
            },
          ];
          financialStaffIdColumn.disabled = true;
          financialStaffIdColumn.rules = [
            {
              required: false,
              message: "请选择付款日期",
              trigger: "blur",
            },
          ];
        }
        for (let key in variables) {
          if (!variables[key]) delete variables[key];
        }

        const serialNumber = this.findObject(
          this.option.column,
          "serialNumber"
        );
        if (
          option.column &&
          process.variables &&
          process.variables.serialNumber &&
          (!serialNumber || serialNumber === -1)
        ) {
          option.column.unshift({
            label: "流水号",
            prop: "serialNumber",
            span: 24,
            detail: true,
          });
        }

        this.option = option;
        //重新加载附件，处理附件外部更新的情况
        this.form = variables;
        this.form.loan = variables.loan;

        this.loadAttach();
        this.waiting = false;
        this.submitLoading = false;
      });
    },
    async loadAttach() {
      this.form.attachment = [];
      const res = await getAttachList({
        businessName: "ni_fin_cost_ea",
        businessKey: this.form.id,
      });
      const data = res.data.data;
      this.form.attachment = data.map((item) => {
        return {
          label: item.originalName,
          value: item.id,
        };
      });
    },
    // 审核
    handleExamine(pass) {
      this.submitLoading = true;
      this.$refs.form.validate((valid, done) => {
        if (valid && pass) {
          const variables = {};
          this.option.column.forEach((v) => {
            if (this.form[v.prop]) variables[v.prop] = this.form[v.prop];
          });
          console.log(variables);
          if (
            this.form.updateMaterialCode === "1" &&
            this.form.items &&
            this.form.items.length > 0
          ) {
            //校验编码
            const unSetMaterialCode = this.form.items.some(
              (item) => !item.materialId || item.materialId <= 0
            );
            if (
              unSetMaterialCode &&
              !["recall", "reject"].includes(this.processIsFinished)
            ) {
              this.$message.warning("有未设置编码的费用，请设置后再提交");
              done();
              this.submitLoading = false;
              return;
            }
          }
          this.handleCompleteTask(pass, variables)
            .then(() => {
              this.$message.success("处理成功");
              if (this.fromPath) {
                this.handleCloseTag(this.fromPath);
              } else this.handleCloseTag("/plugin/workflow/process/todo");
            })
            .catch(() => {
              done();
              this.submitLoading = false;
            });
        } else if (!pass) {
          this.handleCompleteTask(pass, {})
            .then(() => {
              this.$message.success("处理成功");
              if (this.fromPath) {
                this.handleCloseTag(this.fromPath);
              } else this.handleCloseTag("/plugin/workflow/process/todo");
            })
            .catch(() => {
              done();
              this.submitLoading = false;
            });
        } else {
          done();
          this.submitLoading = false;
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.header {
  width: 100%;
  height: 50px;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 10px 10px 0;
}
</style>

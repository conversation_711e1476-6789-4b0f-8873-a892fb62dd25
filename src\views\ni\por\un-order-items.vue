<template>
  <basic-container>
    <vxe-toolbar ref="toolbarRef" size="mini" custom>
      <template #buttons>
        <el-radio-group
          v-model="dataType"
          size="mini"
          @input="onLoad()"
          :disabled="loading"
        >
          <el-radio-button label="1">本人</el-radio-button>
          <el-radio-button label="2">全部</el-radio-button>
        </el-radio-group>
        <vxe-button size="mini" status="warning" content="修改采购人员">
          <template #dropdowns>
            <vxe-button
              v-for="item in userDict"
              :key="item.id"
              :content="item.realName"
              @click="handlePurchaseUserChange(item.id)"
            ></vxe-button>
          </template>
        </vxe-button>
        <vxe-button
          size="mini"
          status="primary"
          content="修改供应商"
          @click="handleSupplierChange"
        ></vxe-button>
        <vxe-button size="mini" status="primary" content="生成订单">
          <template #dropdowns>
            <vxe-button
              content="货到付款"
              @click="handleOrder('1')"
            ></vxe-button>
            <vxe-button content="预付款" @click="handleOrder('2')"></vxe-button>
            <vxe-button content="支付宝" @click="handleOrder('3')"></vxe-button>
            <vxe-button
              content="合同预付"
              @click="handleOrder('4')"
            ></vxe-button>
          </template>
        </vxe-button>
        <vxe-button
          size="mini"
          status="danger"
          content="拆分"
          @click="handleSplit"
        ></vxe-button>
        <vxe-button size="mini" content="批量操作">
          <template #dropdowns>
            <vxe-button content="修改备注" @click="handleRemark"></vxe-button>
            <vxe-button content="取 消" @click="handleCancel"></vxe-button>
            <vxe-button content="驳回" @click="handleReject"></vxe-button>
          </template>
        </vxe-button>
        <vxe-button
          size="mini"
          status="primary"
          content="导 出"
          @click="handleExcel"
        ></vxe-button>
        <vxe-button
          size="mini"
          status="primary"
          content="打 印"
          @click="handlePrint"
        ></vxe-button>
        <el-divider direction="vertical" />
        <el-tag>
          当前表格已选择
          <span style="font-weight: bolder">{{ selectionList.length }}</span> 项
          <el-button type="text" size="mini" @click="handleSelectionClear">
            清空
          </el-button>
          <template v-if="selectionList.length > 0">
            选中金额:
            <span style="font-weight: bolder; color: #f56c6c">
              {{
                amount.toLocaleString("zh-CN", {
                  minimumFractionDigits: 2,
                })
              }}
            </span>
          </template>
        </el-tag>
        <el-divider direction="vertical" />
        <el-checkbox v-model="pv">压力容器</el-checkbox>
      </template>
      <template #tools>
        <vxe-button
          type="text"
          icon="vxe-icon-refresh"
          class="tool-btn"
          @click="onLoad"
        ></vxe-button>
      </template>
    </vxe-toolbar>
    <vxe-table
      border
      stripe
      show-overflow
      row-id="id"
      ref="table"
      :height="tableHeight"
      :cell-class-name="cellStyle"
      :mouse-config="{ selected: true }"
      :row-config="{ isCurrent: true, isHover: true }"
      :sort-config="{ trigger: 'cell', multiple: true }"
      :column-config="{ resizable: true }"
      :checkbox-config="{ checkField: 'checked', trigger: 'row' }"
      :edit-config="{ trigger: 'dblclick', mode: 'cell' }"
      :keyboard-config="{
        isArrow: true,
        isDel: true,
        isEnter: true,
        isTab: true,
        isEdit: true,
      }"
      :scroll-x="{ enabled: true }"
      :scroll-y="{ enabled: true }"
      @checkbox-change="handleSelectionChange"
      @checkbox-all="handleSelectionChange"
    >
      <vxe-column type="checkbox" width="55" />
      <vxe-column
        field="row"
        title="序号"
        width="68"
        :filters="[{ data: '' }]"
        :filter-method="
          ({ option, row }) =>
            row.row && row.row.toString().indexOf(option.data) != -1
        "
      >
        <template #filter="{ $panel, column }">
          <input
            type="type"
            v-for="(option, index) in column.filters"
            :key="index"
            v-model="option.data"
            @keyup.enter="$panel.confirmFilter()"
            @input="$panel.changeOption($event, !!option.data, option)"
          />
        </template>
      </vxe-column>
      <vxe-column
        field="purchaseUserName"
        title="采购人"
        width="84"
        :filters="[{ data: '' }]"
        :filter-method="
          ({ option, row }) => {
            return (
              row.purchaseUserName &&
              row.purchaseUserName.indexOf(option.data) !== -1
            );
          }
        "
      >
        <template #filter="{ $panel, column }">
          <input
            type="type"
            v-for="(option, index) in column.filters"
            :key="index"
            v-model="option.data"
            @keyup.enter="$panel.confirmFilter()"
            @input="$panel.changeOption($event, !!option.data, option)"
          />
        </template>
      </vxe-column>
      <vxe-column
        field="materialName"
        title="品名"
        width="95"
        :filters="[{ data: '' }]"
        :filter-method="
          ({ option, row }) =>
            row.materialName && row.materialName.indexOf(option.data) !== -1
        "
      >
        <template #filter="{ $panel, column }">
          <input
            type="type"
            v-for="(option, index) in column.filters"
            :key="index"
            v-model="option.data"
            @keyup.enter="$panel.confirmFilter()"
            @input="$panel.changeOption($event, !!option.data, option)"
          />
        </template>
        <template #default="{ row, rowIndex }">
          <el-button type="text" size="mini" @click="showHistoryDialog(row)">
            {{ row.materialName }}
          </el-button>
        </template>
      </vxe-column>
      <vxe-column
        field="specification"
        title="规格"
        width="105"
        :filters="[{ data: '' }]"
        :filter-method="
          ({ option, row }) =>
            row.specification && row.specification.indexOf(option.data) !== -1
        "
      >
        <template #filter="{ $panel, column }">
          <input
            type="type"
            v-for="(option, index) in column.filters"
            :key="index"
            v-model="option.data"
            @keyup.enter="$panel.confirmFilter()"
            @input="$panel.changeOption($event, !!option.data, option)"
          />
        </template>
      </vxe-column>
      <vxe-column
        field="quality"
        title="材质"
        show-overflow
        width="95"
        :filters="[{ data: '' }]"
        :filter-method="
          ({ option, row }) =>
            row.quality && row.quality.indexOf(option.data) !== -1
        "
      >
        <template #filter="{ $panel, column }">
          <input
            type="type"
            v-for="(option, index) in column.filters"
            :key="index"
            v-model="option.data"
            @keyup.enter="$panel.confirmFilter()"
            @input="$panel.changeOption($event, !!option.data, option)"
          />
        </template>
      </vxe-column>
      <vxe-column
        field="num"
        title="数量"
        show-overflow
        width="85"
        :edit-render="{ autofocus: '.vxe-input--inner' }"
      >
        <template #edit="scope">
          <vxe-input
            v-model="scope.row.num"
            type="number"
            size="mini"
            placeholder=" "
            @blur="rowNumChangeSubmit($event, scope.row)"
          ></vxe-input>
        </template>
      </vxe-column>
      <vxe-column
        field="unit"
        title="单位"
        :formatter="({ cellValue }) => unitDictKeyValue[cellValue]"
        show-overflow
        width="65"
      ></vxe-column>

      <vxe-column
        field="applyRemark"
        title="申请备注"
        show-overflow
        width="110"
        :filters="[{ data: '' }]"
        :filter-method="
          ({ option, row }) =>
            row.applyRemark && row.applyRemark.indexOf(option.data) !== -1
        "
      >
        <template #filter="{ $panel, column }">
          <input
            type="type"
            v-for="(option, index) in column.filters"
            :key="index"
            v-model="option.data"
            @keyup.enter="$panel.confirmFilter()"
            @input="$panel.changeOption($event, !!option.data, option)"
          />
        </template>
      </vxe-column>

      <vxe-column
        field="applyAttachNum"
        title="申请附件"
        show-overflow
        width="76"
      >
        <template #default="{ row, rowIndex }">
          <el-link
            v-if="row.applyAttachNum && row.applyAttachNum > 0"
            type="primary"
            target="_blank"
            @click="rowAttach(row)"
          >
            附件({{ row.applyAttachNum }})
          </el-link>
        </template>
      </vxe-column>
      <vxe-column
        field="supplier"
        title="供应商"
        show-overflow
        sortable
        width="110"
        :filters="[{ data: '' }]"
        :filter-method="
          ({ option, row }) => {
            if (option.data === ' ') {
              return !row.supplier;
            }
            return row.supplier && row.supplier.indexOf(option.data) !== -1;
          }
        "
      >
        <template #filter="{ $panel, column }">
          <input
            type="type"
            v-for="(option, index) in column.filters"
            :key="index"
            v-model="option.data"
            @keyup.enter="$panel.confirmFilter()"
            @input="$panel.changeOption($event, !!option.data, option)"
          />
        </template>
      </vxe-column>
      <vxe-column
        field="supplierLinkman"
        title="联系人"
        show-overflow
        width="110"
      ></vxe-column>
      <vxe-column
        field="price"
        title="单价"
        show-overflow
        width="95"
        :filters="[
          { label: '不是0元', value: 1, checked: false },
          { label: '0元或未填', value: 2, checked: false },
        ]"
        :filter-method="
          ({ option, row }) => {
            if (option.value === 1) {
              return Number(row.price) !== 0;
            } else if (option.value === 2) {
              return Number(row.price) === 0 || !row.price;
            }
          }
        "
        :edit-render="{ autofocus: '.vxe-input--inner' }"
      >
        <template #edit="scope">
          <vxe-input
            v-model="scope.row.price"
            type="number"
            size="mini"
            placeholder=" "
            @blur="rowPriceChangeSubmit($event, scope.row)"
          ></vxe-input>
        </template>
      </vxe-column>
      <vxe-column
        field="amount"
        title="金额"
        show-overflow
        :filters="[{ data: '' }]"
        :edit-render="{ autofocus: '.vxe-input--inner' }"
        :filter-method="
          ({ option, row }) => Number(row.amount) === Number(option.data)
        "
        width="95"
      >
        <template #filter="{ $panel, column }">
          <input
            type="type"
            v-for="(option, index) in column.filters"
            :key="index"
            v-model="option.data"
            @keyup.enter="$panel.confirmFilter()"
            @input="$panel.changeOption($event, !!option.data, option)"
          />
        </template>
        <template #edit="scope">
          <vxe-input
            v-model="scope.row.amount"
            type="number"
            size="mini"
            className="amount-input"
            placeholder=" "
            @blur="rowAmountChangeSubmit($event, scope.row)"
          ></vxe-input>
        </template>
      </vxe-column>
      <vxe-column
        field="remark"
        title="备注"
        show-overflow
        :filters="[{ data: '' }]"
        :filter-method="
          ({ option, row }) =>
            row.remark && row.remark.indexOf(option.data) !== -1
        "
        :edit-render="{ autofocus: '.vxe-textarea--inner' }"
        width="110"
      >
        <template #filter="{ $panel, column }">
          <input
            type="type"
            v-for="(option, index) in column.filters"
            :key="index"
            v-model="option.data"
            @keyup.enter="$panel.confirmFilter()"
            @input="$panel.changeOption($event, !!option.data, option)"
          />
        </template>
        <template #edit="scope">
          <vxe-textarea
            v-model="scope.row.remark"
            placeholder=" "
            :autosize="{ minRows: 1 }"
            @blur="rowRemarkChangeSubmit($event, scope.row)"
          />
        </template>
      </vxe-column>
      <vxe-column
        field="applyUserName"
        title="申请人"
        show-overflow
        width="84"
        :filters="[{ data: '' }]"
        :filter-method="
          ({ option, row }) =>
            row.applyUserName && row.applyUserName.indexOf(option.data) !== -1
        "
      >
        <template #filter="{ $panel, column }">
          <input
            type="type"
            v-for="(option, index) in column.filters"
            :key="index"
            v-model="option.data"
            @keyup.enter="$panel.confirmFilter()"
            @input="$panel.changeOption($event, !!option.data, option)"
          />
        </template>
      </vxe-column>
      <vxe-column
        field="receivingAddress"
        title="到货地"
        min-width="105"
        :formatter="({ cellValue }) => receivingAddressDictKeyValue[cellValue]"
        :filters="receivingAddressDict"
        :edit-render="{}"
      >
        <template #edit="{ row }">
          <vxe-select
            size="mini"
            clearable
            transfer
            v-model="row.receivingAddress"
            @change="rowReceivingAddress($event, row)"
          >
            <vxe-option
              v-for="(item, index) in receivingAddressDict"
              :key="index"
              :value="item.value"
              :label="item.label"
            ></vxe-option>
          </vxe-select>
        </template>
      </vxe-column>
      <vxe-column
        field="needDate"
        title="需用日期"
        show-overflow
        sortable
        width="109"
      ></vxe-column>
      <vxe-column
        field="purpose"
        title="用途"
        width="105"
        :filters="[{ data: '' }]"
        :filter-method="
          ({ option, row }) =>
            row.purpose && row.purpose.indexOf(option.data) !== -1
        "
      >
        <template #filter="{ $panel, column }">
          <input
            type="type"
            v-for="(option, index) in column.filters"
            :key="index"
            v-model="option.data"
            @keyup.enter="$panel.confirmFilter()"
            @input="$panel.changeOption($event, !!option.data, option)"
          />
        </template>
      </vxe-column>
      <vxe-column field="inquiryLength" title="比价" show-overflow width="109">
        <template #default="{ row, rowIndex }">
          <el-button
            v-if="row.inquiries && row.inquiries.length > 0"
            :ref="'inquiryRef_' + row.id"
            type="text"
            size="mini"
            slot="reference"
            style="color: red"
            @click="rowInquiryShow(row, rowIndex)"
          >
            比价【{{ row.inquiryLength }}】
          </el-button>
          <span v-else>无比价</span>
        </template>
      </vxe-column>
      <vxe-column
        field="applyPrice"
        title="建议单价"
        show-overflow
        width="95"
      ></vxe-column>
      <vxe-column
        field="applyAmount"
        title="建议金额"
        show-overflow
        width="95"
      ></vxe-column>
      <vxe-column
        field="receive"
        title="到货"
        show-overflow
        width="70"
        :filter-multiple="false"
        :formatter="({ cellValue }) => (cellValue ? '是' : '否')"
        :filters="[
          { label: '是', value: true },
          { label: '否', value: false },
        ]"
        :filter-method="
          ({ option, row }) =>
            (row.receive ? row.receive : false) === option.value
        "
      ></vxe-column>
      <vxe-column
        field="receiveRemark"
        title="到货备注"
        show-overflow
        width="80"
      ></vxe-column>
      <vxe-column
        field="depotLocation"
        title="暂存位置"
        show-overflow
        width="80"
      ></vxe-column>
      <vxe-column field="cost" title="类型" show-overflow width="70">
        <template #default="{ row, rowIndex }">
          <el-tag size="mini" type="danger" effect="dark" v-if="row.cost">
            费用
          </el-tag>
          <el-tag size="mini" type="info" effect="plain" v-else> 实物</el-tag>
        </template>
      </vxe-column>
      <vxe-column
        field="brand"
        title="账套"
        show-overflow
        width="70"
        :filters="brandDict"
        :filter-method="
          ({ option, row }) => (row.brand ? row.brand : '1') === option.value
        "
      >
        <template #default="{ row, rowIndex }">
          <dict-tag size="mini" v-model="row.brand" :dict="brandDict" />
        </template>
      </vxe-column>
      <vxe-column
        field="materialCode"
        title="编码"
        show-overflow="title"
        width="120"
      ></vxe-column>
      <vxe-column field="gb" title="国标" show-overflow="title" width="95" />
      <vxe-column field="pv" title="压力容器" show-overflow="title" width="80">
        <template #default="{ row, rowIndex }">
          <el-tag size="mini" type="danger" effect="dark" v-if="row.pv">
            是
          </el-tag>
          <el-tag size="mini" type="info" effect="plain" v-else> 否</el-tag>
        </template>
      </vxe-column>
      <vxe-column field="fa" title="固定资产" show-overflow="title" width="80">
        <template #default="{ row, rowIndex }">
          <el-tag size="mini" type="danger" effect="dark" v-if="row.fa">
            是
          </el-tag>
          <el-tag size="mini" type="info" effect="plain" v-else> 否</el-tag>
        </template>
      </vxe-column>
      <vxe-column
        field="applySerialNo"
        title="采购申请"
        show-overflow="title"
        width="120"
        :filters="[{ data: '' }]"
        :filter-method="
          ({ option, row }) =>
            row.applySerialNo && row.applySerialNo.indexOf(option.data) !== -1
        "
      >
        <template #filter="{ $panel, column }">
          <input
            type="type"
            v-for="(option, index) in column.filters"
            :key="index"
            v-model="option.data"
            @keyup.enter="$panel.confirmFilter()"
            @input="$panel.changeOption($event, !!option.data, option)"
          />
        </template>
      </vxe-column>
      <vxe-column
        field="applyTime"
        title="申请时间"
        show-overflow
        width="100"
      ></vxe-column>
      <vxe-column
        field="applyType"
        title="采购分类"
        show-overflow
        :formatter="({ cellValue }) => typeDictKeyValue[cellValue]"
        width="95"
      ></vxe-column>
      <vxe-column
        field="projectSerialNo"
        title="所属项目"
        show-overflow="title"
        width="100"
        :filters="[{ data: '' }]"
        :filter-method="
          ({ option, row }) =>
            row.projectSerialNo &&
            row.projectSerialNo.indexOf(option.data) !== -1
        "
      >
        <template #filter="{ $panel, column }">
          <input
            type="type"
            v-for="(option, index) in column.filters"
            :key="index"
            v-model="option.data"
            @keyup.enter="$panel.confirmFilter()"
            @input="$panel.changeOption($event, !!option.data, option)"
          />
        </template>
      </vxe-column>
      <vxe-column
        field="budgetSerialNo"
        title="关联预算"
        show-overflow="title"
        width="115"
        :filters="[{ data: '' }]"
        :filter-method="
          ({ option, row }) =>
            row.budgetSerialNo && row.budgetSerialNo.indexOf(option.data) !== -1
        "
      >
        <template #filter="{ $panel, column }">
          <input
            type="type"
            v-for="(option, index) in column.filters"
            :key="index"
            v-model="option.data"
            @keyup.enter="$panel.confirmFilter()"
            @input="$panel.changeOption($event, !!option.data, option)"
          />
        </template>
      </vxe-column>
      <vxe-column
        field="buyer"
        title="采购方式"
        show-overflow
        width="100"
        :filters="[
          { label: '采购部', value: 1 },
          { label: '办公室', value: 2 },
        ]"
        :filter-method="
          ({ option, row }) => (row.buyer ? row.buyer : 1) === option.value
        "
      >
        <template #default="{ row, rowIndex }">
          <el-tag
            v-if="row.buyer === 2"
            effect="dark"
            type="danger"
            size="mini"
          >
            办公室
          </el-tag>
          <el-tag v-else size="mini" effect="plain">采购</el-tag>
        </template>
      </vxe-column>
    </vxe-table>
    <supplier-select-dialog
      ref="supplierSelectRef"
      :multiple="false"
      @submit="handleSupplierSubmit"
    />
    <vxe-modal v-model="split.visible" :mask="false">
      <template #title>
        <span style="color: red">拆分明细</span>
      </template>
      <template #default>
        <avue-form
          v-if="split.visible"
          :option="split.option"
          v-model="split.form"
          @submit="handleSplitSubmit"
        >
        </avue-form>
      </template>
    </vxe-modal>
    <inquiry-show-card ref="inquiryPopRef" />
    <order-item-history-dialog ref="orderItemHistoryRef" />
    <attach-dialog ref="attachRef" code="public" detail :delBtn="false" />
  </basic-container>
</template>

<script>
import {
  buildOrder,
  cancel,
  changeAmount,
  changeNum,
  changePurchaseUser,
  changeRemark,
  splitNum,
  unOrderItemList,
} from "@/api/ni/por/order";
import {
  changeReceivingAddress,
  changeSupplier,
  reject,
} from "@/api/ni/por/order-item";
import SupplierSelectDialog from "@/views/ni/base/components/SupplierSelectDialog";
import SupplierMultipleSelect from "@/views/ni/base/components/SupplierSelect";
import { mapGetters } from "vuex";
import OrderItemHistoryDialog from "@/views/ni/por/components/OrderItemHistoryDialog";
import NProgress from "nprogress";
import { exportBlob } from "@/api/common";
import printJS from "print-js";
import AttachDialog from "@/components/attach-dialog";
import InquiryShowCard from "@/views/ni/por/components/inquiry-show-card";

export default {
  components: {
    InquiryShowCard,
    AttachDialog,
    SupplierSelectDialog,
    SupplierMultipleSelect,
    OrderItemHistoryDialog,
  },
  computed: {
    ...mapGetters(["userInfo"]),
    amount() {
      let amount = 0;
      if (this.selectionList && this.selectionList.length > 0) {
        amount = this.selectionList.reduce((acc, cur) => {
          return Number(acc) + Number(cur.amount);
        }, 0);
      }
      return amount;
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  data() {
    const PAY_TYPE_PAY = "2";
    return {
      dataType: "1",
      rowOptions: [{ data: "" }],
      tempNum: 0,
      tempPrice: 0,
      tempAmount: 0,
      tempRemark: "",
      payType: PAY_TYPE_PAY,
      inquiry: {
        visible: false,
        form: {},
      },
      split: {
        visible: false,
        form: {},
        option: {
          span: 24,
          labelWidth: 130,
          size: "mini",
          emptyBtn: false,
          column: [
            {
              label: "拆分数量",
              prop: "num",
              type: "number",
              placeholder: " ",
              rules: [
                {
                  required: true,
                  message: "请输入拆分数量",
                  trigger: "blur",
                },
              ],
            },
          ],
        },
      },
      form: {},
      query: {},
      loading: true,
      selectionList: [],
      data: [],
      statusDict: [],
      statusDictKeyValue: {},
      arrivalDict: [],
      arrivalDictKeyValue: {},
      brandDict: [],
      brandDictKeyValue: {},
      currentStartIndex: 0,
      currentEndIndex: 20,
      userDict: [],
      userDictKeyValue: {},
      unitDict: [],
      unitDictKeyValue: {},
      typeDict: [],
      typeList: [],
      costList: [],
      payTypeDict: [],
      payTypeDictKeyValue: {},
      receivingAddressDict: [],
      receivingAddressDictKeyValue: {},
      typeDictKeyValue: {},
      originList: [],
      popover: {
        visible: false,
        type: "",
        placement: "top-start",
        width: 200,
        reference: {},
        activeId: -1,
        activeRow: {},
      },
      tableHeight: this.calculateTableHeight(), // 初始化表格高度
      pv: false,
    };
  },
  watch: {
    pv: {
      handler(val) {
        if (val) this.query.pv = 1;
        else this.query.pv = null;
        this.onLoad();
      },
      immediate: false,
    },
  },
  created() {
    this.$nextTick(() => {
      // 将表格和工具栏进行关联
      const $table = this.$refs.table;
      const $toolbar = this.$refs.toolbarRef;
      if ($table && $toolbar) {
        $table.connect($toolbar);
      }
    });
    this.costList = [
      {
        text: "费用",
        value: true,
      },
      {
        text: "实物",
        value: false,
      },
    ];
    this.$http
      .get(
        "/api/blade-system/dict-biz/dictionary?code=ni_por_receiving_address"
      )
      .then((res) => {
        this.receivingAddressDict = res.data.data.map((item) => {
          return {
            label: item.dictValue,
            value: item.dictKey,
          };
        });
        this.receivingAddressDictKeyValue = res.data.data.reduce((acc, cur) => {
          acc[cur.dictKey] = cur.dictValue;
          return acc;
        }, {});
      });
    this.$http
      .get("/api/blade-system/dict-biz/dictionary?code=ni_por_pay_type")
      .then((res) => {
        this.payTypeDict = res.data.data;
        this.payTypeDictKeyValue = this.payTypeDict.reduce((acc, cur) => {
          acc[cur.dictKey] = cur.dictValue;
          return acc;
        }, {});
      });
    this.$http
      .get("/api/blade-system/dict-biz/dictionary?code=ni_por_type")
      .then((res) => {
        this.typeDict = res.data.data;
        this.typeDictKeyValue = this.typeDict.reduce((acc, cur) => {
          acc[cur.dictKey] = cur.dictValue;
          return acc;
        }, {});
      });
    this.$http.get("/api/ni/por/order/purchaseUserList").then((res) => {
      this.userDict = res.data.data;
      this.userDictKeyValue = this.userDict.reduce((acc, cur) => {
        acc[cur.id] = cur.realName;
        return acc;
      }, {});
    });
    this.$http
      .get("/api/blade-system/dict-biz/dictionary?code=ni_material_unit")
      .then((res) => {
        this.unitDict = res.data.data;
        this.unitDictKeyValue = this.unitDict.reduce((acc, cur) => {
          acc[cur.dictKey] = cur.dictValue;
          return acc;
        }, {});
      });
    this.$http
      .get("/api/blade-system/dict-biz/dictionary?code=ni_brand")
      .then((res) => {
        this.brandDict = res.data.data.map((item) => {
          return {
            label: item.dictValue,
            value: item.dictKey,
          };
        });
        this.brandDictKeyValue =  res.data.data.reduce((acc, cur) => {
          acc[cur.dictKey] = cur.dictValue;
          return acc;
        }, {});
      });
    this.onLoad();
  },
  mounted() {
    window.addEventListener("resize", this.handleResize);
  },
  destroyed() {
    window.removeEventListener("resize", this.handleResize);
  },
  beforeRouteEnter(to, from, next) {
    to.meta.keepAlive = true;
    next();
  },
  methods: {
    rowReceivingAddress($event, row) {
      changeReceivingAddress(row.id, $event.value).then(() => {
        row.receivingAddress = $event.value;
        this.$message({
          type: "success",
          message: "操作成功!",
        });
      });
    },
    handleSelectionClear() {
      this.$refs.table.clearCheckboxRow();
      this.selectionList = [];
    },
    handleResize() {
      // 窗口大小变化时重新计算表格高度
      this.tableHeight = this.calculateTableHeight();
      console.log(this.tableHeight);
    },
    calculateTableHeight() {
      console.log(window.innerHeight - 224);
      // 根据实际需求计算表格高度
      return window.innerHeight - 224;
    },

    rowInquiryShow(row) {
      this.$refs.inquiryPopRef.onShow(row);
    },
    rowPopShow(type, row, width) {
      if (this.popover.activeRow.id === row.id && this.popover.visible) {
        this.$refs["popoverRef"] && this.$refs["popoverRef"].doClose();
      }
      this.popover.visible = false;
      this.popover.activeRow = row;
      this.popover.width = width;
      this.popover.type = type;
      this.popover.reference = this.$refs[type + "Ref_" + row.id].$el;
      this.$nextTick(() => {
        // 等待显示的 popover 销毁后再 重新渲染新的 popover
        this.popover.visible = true;
        this.$nextTick(() => {
          // 此时才能获取 refs 引用
          this.$refs["popoverRef"].doShow();
        });
      });
    },
    rowRemarkChangeSubmit($event, row) {
      changeRemark(row.id, $event.value).then(() => {
        this.$message({
          type: "success",
          message: "操作成功!",
        });
      });
    },
    rowAmountChangeSubmit($event, row) {
      changeAmount(row.id, "", $event.value).then(() => {
        row.price = ($event.value / row.num).toFixed(2);
        this.$message({
          type: "success",
          message: "操作成功!",
        });
      });
    },
    rowPriceChangeSubmit($event, row) {
      const amount = (row.num * $event.value).toFixed(2);
      changeAmount(row.id, "", amount).then(() => {
        row.amount = amount;
        this.$message({
          type: "success",
          message: "操作成功!",
        });
      });
    },
    rowNumChangeSubmit($event, row) {
      const amount = (row.price * $event.value).toFixed(2);
      changeNum(row.id, "", $event.value).then(() => {
        row.amount = amount;
        this.$message({
          type: "success",
          message: "操作成功!",
        });
      });
    },
    handleSplitSubmit(form, done) {
      splitNum(form.id, form.num)
        .then(() => {
          this.onLoad();
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.split.visible = false;
          this.split.form = {};
        })
        .finally(() => {
          done();
        });
    },
    handlePopClose() {
      this.$refs["popoverRef"] && this.$refs["popoverRef"].doClose();
    },
    handlePopShow() {
      setTimeout(() => {
        if (this.popover.type === "price") {
          // this.$refs.pricePopRef.focusInput();
        }
      }, 1000);
    },
    htmlDecode(input) {
      const doc = new DOMParser().parseFromString(input, "text/html");
      return doc.documentElement.textContent;
    },
    rowAttach(row) {
      this.$refs.attachRef.init(row.applyItemId, "ni_por_apply_item");
    },

    handleSelectionChange({ checked }) {
      this.selectionList = this.$refs.table.getCheckboxRecords();
      console.log(checked ? "勾选事件" : "取消事件", this.selectionList);
    },
    handleRemark() {
      this.selectionList = this.$refs.table.getCheckboxRecords();
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择要修改的数据");
        return;
      }
      const ids = this.selectionList.map((item) => item.id).join(",");
      this.$prompt("备注", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
      })
        .then(({ value }) => {
          return changeRemark(ids, value);
        })
        .then(() => {
          this.onLoad();
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    handleSplit() {
      this.selectionList = this.$refs.table.getCheckboxRecords();
      console.log(this.selectionList);
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择要拆分的数据");
        return;
      }
      if (this.selectionList.length > 1) {
        this.$message.warning("只能选择一条数据");
        return;
      }
      this.split.form.num = this.selectionList[0].num;
      this.split.form.id = this.selectionList[0].id;
      this.split.visible = true;
    },
    handleOrder(payType) {
      this.selectionList = this.$refs.table.getCheckboxRecords();
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择要生成的数据");
        return;
      }
      const brands = new Map();
      this.selectionList.forEach((item) => {
        if (!brands.has(item.brand)) {
          brands.set(item.brand, true);
        }
      });
      if (brands.size > 1) {
        this.$message.warning("请选择相同的账套数据");
        return;
      }
      const noSupplier = this.selectionList.some((item) => !item.supplierId);
      if (noSupplier) {
        this.$message.warning("选中的数据存在未选择供应商的数据，请重新选择");
        return;
      }
      const purchaseUsers = new Map();
      const suppliers = new Map();
      this.selectionList.forEach((item) => {
        if (!suppliers.has(item.supplierId)) {
          suppliers.set(item.supplierId, true);
        }
        if (purchaseUsers.has(item.purchaseUserId)) {
          purchaseUsers.set(item.purchaseUserId, true);
        }
      });
      if (suppliers.size > 1) {
        this.$message.warning("请选择相同的供应商数据");
        return;
      }
      if (purchaseUsers.size > 1) {
        this.$message.warning("选中的数据中存在不同采购人员的数据，请重新选择");
        return;
      }
      let payTypeLabel = "";
      if (payType) {
        payTypeLabel = this.payTypeDictKeyValue[payType];
      }
      this.$confirm(
        `确定将选择数据生成${
          payTypeLabel && payType != "1" ? "【" + payTypeLabel + "付款】" : ""
        }订单?`,
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(() => {
          const data = this.selectionList.map((item) => {
            return {
              id: item.id,
              itemType: item.itemType,
              payType,
            };
          });
          return buildOrder(data);
        })
        .then(() => {
          this.onLoad();
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    cellStyle({ row, column }) {
      if ("applyUserName" === column.field && row.crash) {
        return "col-red";
      }
      if ("row" === column.field && row.yearsAgo) {
        return "col-red";
      }
      if ("purchaseUserName" === column.property && row.receive) {
        return "col-orange";
      }
      if ("applySerialNo" === column.property && row.applyStatus !== 9) {
        return "col-red";
      }
      if ("materialName" === column.property && row.applyType === "21") {
        return "col-yzdyzb";
      }
      return null;
    },
    handleReject() {
      this.selectionList = this.$refs.table.getCheckboxRecords();
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择要驳回的数据");
        return;
      }
      this.$prompt("确定要要选择的数据驳回?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        inputPlaceholder: "请输入驳回原因",
        inputErrorMessage: "驳回原因",
        inputValidator: (value) => {
          if (!value.trim()) {
            return "驳回原因不能为空";
          }
        },
      })
        .then(({ value }) => {
          reject(this.ids, value).then(() => {
            this.onLoad();
            this.$message({
              type: "success",
              message: "操作成功!",
            });
          });
        })
        .catch(() => {});
    },
    handleCancel() {
      this.selectionList = this.$refs.table.getCheckboxRecords();
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择要取消的数据");
        return;
      }
      this.$confirm("确定将选择数据取消?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          const data = this.selectionList.map((item) => {
            return {
              id: item.id,
              itemType: item.itemType,
            };
          });
          return cancel(data);
        })
        .then(() => {
          this.onLoad();
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    handleExcel() {
      this.selectionList = this.$refs.table.getCheckboxRecords();
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择要导出的数据");
        return;
      }
      this.$Export.excel({
        title: "订单明细",
        columns: [
          {
            label: "采购人",
            prop: "purchaseUserName",
          },
          {
            label: "序号",
            prop: "row",
          },
          {
            label: "品名",
            prop: "materialName",
          },
          {
            label: "规格",
            prop: "specification",
          },
          {
            label: "材质",
            prop: "quality",
          },
          {
            label: "数量",
            prop: "num",
          },
          {
            label: "单位",
            prop: "unit",
          },
          {
            label: "单价",
            prop: "price",
          },
          {
            label: "金额",
            prop: "amount",
          },
          {
            label: "申请备注",
            prop: "applyRemark",
          },

          {
            label: "供应商",
            prop: "supplier",
          },
          {
            label: "申请人",
            prop: "applyUserName",
          },
          {
            label: "到货地",
            prop: "receivingAddress",
          },
          {
            label: "账套",
            prop: "brand",
          },
          {
            label: "需用日期",
            prop: "needDate",
          },
          {
            label: "用途",
            prop: "purpose",
          },
          {
            label: "紧急申购",
            prop: "crash",
          },
          {
            label: "类型",
            prop: "cost",
          },
          {
            label: "编码",
            prop: "materialCode",
          },
          {
            label: "国标",
            prop: "gb",
          },
          {
            label: "备注",
            prop: "remark",
          },
          {
            label: "采购申请编号",
            prop: "applySerialNo",
          },
          {
            label: "采购分类",
            prop: "applyType",
          },
          {
            label: "所属项目",
            prop: "projectTitle",
          },
          {
            label: "关联预算",
            prop: "budgetSerialNo",
          },
        ],
        data: this.selectionList.map((item) => {
          return {
            ...item,
            unit: this.unitDictKeyValue[item.unit],
            cost: item.cost ? "费用" : "实物",
            applyType: this.typeDictKeyValue[item.applyType],
            receivingAddress: this.receivingAddressDictKeyValue[
              item.receivingAddress
            ]
              ? this.receivingAddressDictKeyValue[item.receivingAddress]
              : item.receivingAddress,
            brand: this.brandDictKeyValue[item.brand ? item.brand : '1'],
          };
        }),
      });
    },
    handlePrint() {
      this.selectionList = this.$refs.table.getCheckboxRecords();
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择要打印的数据");
        return;
      }
      NProgress.start();
      let url = "/api/ni/por/budget/export2Pdf";
      exportBlob(url, { id: this.selectionList[0].id }).then((res) => {
        let data = new Blob([res.data], {
          type: "application/pdf;charset=utf-8",
        });
        let pdfUrl = window.URL.createObjectURL(data);
        printJS({ printable: pdfUrl, type: "pdf" });
        NProgress.done();
      });
    },
    showHistoryDialogByMaterialTypeId(row) {
      this.$refs.orderItemHistoryRef.initByMaterialTypeId(row);
    },
    showHistoryDialog(row) {
      this.$refs.orderItemHistoryRef.initByMaterialId(row);
    },
    handlePurchaseUserChange(val) {
      this.selectionList = this.$refs.table.getCheckboxRecords();
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择要修改的数据");
        return;
      }
      const ids = this.selectionList.map((item) => item.id).join(",");
      changePurchaseUser(ids, val).then(() => {
        this.onLoad();
        this.$message({
          type: "success",
          message: "操作成功!",
        });
      });
    },
    handleSupplierSubmit(selectList) {
      if (selectList) {
        const ids = this.selectionList.map((item) => item.id).join(",");
        changeSupplier(ids, selectList[0].id).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
      }
    },
    handleSupplierChange() {
      this.selectionList = this.$refs.table.getCheckboxRecords();
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择要修改的数据");
        return;
      }
      const isFinish = this.selectionList.some((item) =>
        ("1", "2").includes(item.arrivalState)
      );
      if (isFinish) {
        this.$message.warning("数据中存在已到货的数据，请重新选择");
        return;
      }
      this.$refs.supplierSelectRef.init();
    },
    onLoad() {
      this.loading = true;
      let userId;
      if (this.dataType === "1") {
        userId = this.userInfo.user_id;
      } else if (this.dataType === "2") {
        userId = null;
      }
      unOrderItemList({ userId, pv: this.query.pv }).then((res) => {
        const data = res.data.data;
        data.forEach((item) => {
          item.checked = false;
          if (item.no) {
            item.row += "-" + item.no;
          }
          if (item.supplierLinkman) {
            item.supplierLinkman =
              item.supplierLinkman + "-" + item.supplierLinkPhone;
          }
          if (item.price) {
            item.priceStr = Number(item.price).toLocaleString("zh-CN", {
              minimumFractionDigits: 2,
            });
          }
          if (item.inquiries) {
            item.inquiryLength = item.inquiries.length;
          }
          if (item.applyRemark)
            item.applyRemark = this.htmlDecode(item.applyRemark);
        });
        this.$refs.table.loadData(data);
        this.$refs.table.clearCheckboxRow();
        this.selectionList = [];
        this.loading = false;
      });
    },
  },
};
</script>

<style lang="scss" scoped>
/deep/ .el-link--inner {
  font-size: 12px;
}

/deep/ .vxe-table .vxe-body--column.col-red {
  background-color: #f56c6c;
  color: #fff;
}

/deep/ .vxe-table .vxe-body--column.col-orange {
  background-color: #e6a23c;
  color: #fff;
}

/deep/ .vxe-table .vxe-body--column.col-yzdyzb {
  color: #fff;
  background: #f56c6c url(/img/yzdyzb.png) no-repeat left center / contain;
  position: relative;
}
</style>

export default {
  height: "auto",
  calcHeight: 30,
  tip: false,
  searchIcon: true,
  searchShow: true,
  indexLabel: "序号",
  searchMenuSpan: 6,
  border: true,
  index: false,
  viewBtn: true,
  addBtn: false,
  editBtn: false,
  delBtn: false,
  selection: false,
  searchIndex: 3,
  dialogClickModal: false,
  menuWidth: 80,
  labelWidth: 150,
  column: [
    {
      label: "类型",
      prop: "transferType",
      type: "select",
      dicData: [
        {
          label: "倒箱",
          value: 1,
        },
        {
          label: "倒垛",
          value: 2,
        },
      ],
      html: true,
      formatter: function (val, value, label) {
        if (value === 1) {
          return `<span style="color:green">${label}</span>`;
        } else {
          return `<span style="color:blue">${label}</span>`;
        }
      },
      width: 80,
    },
    {
      label: "箱号",
      prop: "boxCode",
      type: "input",
      width: 120,
      search: true,
    },
    {
      label: "新包装箱",
      prop: "targetOuterPackagingName",
      type: "input",
    },
    {
      label: "原包装箱",
      prop: "sourceOuterPackagingName",
      type: "input",
    },
    {
      label: "新批次",
      prop: "targetBatchCode",
      type: "input",
      width: 120,
      search: true,
    },
    {
      label: "原批次",
      prop: "sourceBatchCode",
      type: "input",
      width: 120,
      search: true,
    },
    {
      label: "操作人",
      prop: "operatorName",
      type: "input",
      width: 80,
    },
    {
      label: "操作日期",
      prop: "transferDate",
      type: "input",
      width: 150,
    },
  ],
};
